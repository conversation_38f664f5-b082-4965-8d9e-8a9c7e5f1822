import '@testing-library/jest-dom'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
  }),
}))

// Mock Supabase client
vi.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    auth: {
      getSession: vi.fn(),
      getUser: vi.fn(),
      signInWithPassword: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      resetPasswordForEmail: vi.fn(),
      updateUser: vi.fn(),
      onAuthStateChange: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: null, error: null })),
          maybeSingle: vi.fn(() => Promise.resolve({ data: null, error: null })),
        })),
        order: vi.fn(() => Promise.resolve({ data: [], error: null })),
        limit: vi.fn(() => Promise.resolve({ data: [], error: null })),
        or: vi.fn(() => ({
          order: vi.fn(() => ({
            limit: vi.fn(() => Promise.resolve({ data: [], error: null })),
          })),
        })),
      })),
      insert: vi.fn(() => Promise.resolve({ data: null, error: null })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => Promise.resolve({ data: null, error: null })),
          })),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(() => Promise.resolve({ data: null, error: null })),
      })),
    })),
  }),
}))

// Mock auth helpers with actual implementations
vi.mock('@/lib/supabase/auth', () => ({
  authClient: {
    signIn: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
    resetPassword: vi.fn(),
    updatePassword: vi.fn(),
  },
  getCurrentUser: vi.fn(),
  isAdmin: vi.fn((role: string) => role === 'admin'),
  isStaff: vi.fn((role: string) => ['admin', 'cs', 'account'].includes(role)),
  canManageUsers: vi.fn((role: string) => role === 'admin'),
  canViewAllData: vi.fn((role: string) => ['admin', 'cs', 'account'].includes(role)),
  validateRoleCompanyAssociation: vi.fn((role: string, companyType?: string) => {
    const staffRoles = ['admin', 'cs', 'account']
    if (staffRoles.includes(role)) return true
    if (!companyType) return false
    
    const roleCompanyMap: Record<string, string> = {
      customer: 'customer',
      carrier: 'carrier', 
      driver: 'carrier',
      factory: 'factory',
      shipper: 'shipper',
      consignee: 'consignee',
      notify_party: 'notify_party',
      forwarder_agent: 'forwarder_agent'
    }
    
    return roleCompanyMap[role] === companyType
  }),
  UserRole: {},
  AuthError: {},
  UserProfile: {},
}))
