'use client'

import { useState, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Upload,
  Download,
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw,
  Package,
  DollarSign,
  Weight,
} from 'lucide-react'
import {
  useCustomerProductBulkImport,
  useCustomerProductValidation,
} from '@/hooks/use-customer-products'
import type {
  CustomerProductBulkData,
  CustomerProductBulkResult,
} from '@/stores/customer-product-store'
import {
  formatCurrency,
  CURRENCY_OPTIONS,
} from '@/lib/validations/customer-products'

interface CustomerProductBulkImportFormProps {
  onClose: () => void
  onSuccess?: (result: CustomerProductBulkResult) => void
}

interface ParsedRow extends CustomerProductBulkData {
  rowIndex: number
  isValid: boolean
  errors: string[]
}

export function CustomerProductBulkImportForm({
  onClose,
  onSuccess,
}: CustomerProductBulkImportFormProps) {
  const { importFromCSV, generateCSVTemplate, isBulkImporting } =
    useCustomerProductBulkImport()
  const { validateBulkData } = useCustomerProductValidation()

  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState<string>('')
  const [parsedRows, setParsedRows] = useState<ParsedRow[]>([])
  const [importResult, setImportResult] =
    useState<CustomerProductBulkResult | null>(null)
  const [step, setStep] = useState<'upload' | 'preview' | 'result'>('upload')
  const [uploadError, setUploadError] = useState<string>('')

  const fileInputRef = useRef<HTMLInputElement>(null)

  // Download CSV template
  const handleDownloadTemplate = () => {
    const template = generateCSVTemplate()
    const blob = new Blob([template], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'customer-product-relationships-template.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      setUploadError('Please select a valid CSV file')
      return
    }

    if (file.size > 5 * 1024 * 1024) {
      // 5MB limit
      setUploadError('File size must be less than 5MB')
      return
    }

    setCsvFile(file)
    setUploadError('')

    // Read file content
    const reader = new FileReader()
    reader.onload = e => {
      const content = e.target?.result as string
      setCsvData(content)
      parseCsvData(content)
    }
    reader.onerror = () => {
      setUploadError('Error reading file')
    }
    reader.readAsText(file)
  }

  // Parse CSV line with proper handling of quoted fields
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    let i = 0

    while (i < line.length) {
      const char = line[i]
      const nextChar = line[i + 1]

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote inside quoted field
          current += '"'
          i += 2
          continue
        } else {
          // Start or end of quoted field
          inQuotes = !inQuotes
          i++
          continue
        }
      }

      if (char === ',' && !inQuotes) {
        // Field separator outside quotes
        result.push(current.trim())
        current = ''
        i++
        continue
      }

      // Regular character
      current += char
      i++
    }

    // Add the last field
    result.push(current.trim())
    return result
  }

  // Parse CSV data and validate
  const parseCsvData = useCallback(
    (content: string) => {
      try {
        const lines = content.trim().split('\n')
        if (lines.length < 2) {
          setUploadError(
            'CSV file must contain at least a header row and one data row'
          )
          return
        }

        const headers = parseCSVLine(lines[0]).map(h =>
          h.toLowerCase().replace(/^["']|["']$/g, '')
        )

        // Validate headers
        const requiredHeaders = [
          'customer_name',
          'product_name',
          'packaging_type',
        ]
        const hasRequiredHeaders = requiredHeaders.every(required =>
          headers.includes(required)
        )

        if (!hasRequiredHeaders) {
          setUploadError(
            'CSV must contain columns: customer_name, product_name, packaging_type'
          )
          return
        }

        // Parse rows
        const parsed: CustomerProductBulkData[] = []
        for (let i = 1; i < lines.length; i++) {
          const values = parseCSVLine(lines[i]).map(v =>
            v.replace(/^["']|["']$/g, '')
          )
          if (values.length < 3 || !values.some(v => v)) continue // Skip empty rows

          const row: CustomerProductBulkData = {
            customer_name: values[headers.indexOf('customer_name')] || '',
            product_name: values[headers.indexOf('product_name')] || '',
            packaging_type: (values[headers.indexOf('packaging_type')] ||
              'Bag') as 'Bag' | 'Plastic Basket' | 'Carton',
            customer_product_code: null,
            unit_price_cif: null,
            unit_price_fob: null,
            currency_code: 'USD',
            standard_quantity: null,
            gross_weight_per_package: null,
            net_weight_per_package: null,
            quality_grade: null,
            handling_instructions: null,
            temperature_require: null,
            vent_require: null,
            shelf_life_days: null,
            is_default: false,
            notes: null,
          }

          // Parse optional columns
          const customerCodeIndex = headers.indexOf('customer_product_code')
          if (customerCodeIndex >= 0 && values[customerCodeIndex]) {
            row.customer_product_code = values[customerCodeIndex]
          }

          const cifIndex = headers.indexOf('unit_price_cif')
          if (cifIndex >= 0 && values[cifIndex]) {
            const cifPrice = parseFloat(values[cifIndex])
            if (!isNaN(cifPrice)) row.unit_price_cif = cifPrice
          }

          const fobIndex = headers.indexOf('unit_price_fob')
          if (fobIndex >= 0 && values[fobIndex]) {
            const fobPrice = parseFloat(values[fobIndex])
            if (!isNaN(fobPrice)) row.unit_price_fob = fobPrice
          }

          const currencyIndex = headers.indexOf('currency_code')
          if (currencyIndex >= 0 && values[currencyIndex]) {
            const currency = values[currencyIndex].toUpperCase()
            if (['THB', 'CNY', 'USD', 'EUR'].includes(currency)) {
              row.currency_code = currency as 'THB' | 'CNY' | 'USD' | 'EUR'
            }
          }

          const quantityIndex = headers.indexOf('standard_quantity')
          if (quantityIndex >= 0 && values[quantityIndex]) {
            const quantity = parseFloat(values[quantityIndex])
            if (!isNaN(quantity)) row.standard_quantity = quantity
          }

          const grossWeightIndex = headers.indexOf('gross_weight_per_package')
          if (grossWeightIndex >= 0 && values[grossWeightIndex]) {
            const weight = parseFloat(values[grossWeightIndex])
            if (!isNaN(weight)) row.gross_weight_per_package = weight
          }

          const netWeightIndex = headers.indexOf('net_weight_per_package')
          if (netWeightIndex >= 0 && values[netWeightIndex]) {
            const weight = parseFloat(values[netWeightIndex])
            if (!isNaN(weight)) row.net_weight_per_package = weight
          }

          const qualityIndex = headers.indexOf('quality_grade')
          if (qualityIndex >= 0 && values[qualityIndex]) {
            row.quality_grade = values[qualityIndex]
          }

          const handlingIndex = headers.indexOf('handling_instructions')
          if (handlingIndex >= 0 && values[handlingIndex]) {
            row.handling_instructions = values[handlingIndex]
          }

          const tempIndex = headers.indexOf('temperature_require')
          if (tempIndex >= 0 && values[tempIndex]) {
            row.temperature_require = values[tempIndex]
          }

          const ventIndex = headers.indexOf('vent_require')
          if (ventIndex >= 0 && values[ventIndex]) {
            row.vent_require = values[ventIndex]
          }

          const shelfLifeIndex = headers.indexOf('shelf_life_days')
          if (shelfLifeIndex >= 0 && values[shelfLifeIndex]) {
            const days = parseInt(values[shelfLifeIndex])
            if (!isNaN(days)) row.shelf_life_days = days
          }

          const isDefaultIndex = headers.indexOf('is_default')
          if (isDefaultIndex >= 0 && values[isDefaultIndex]) {
            const defaultValue = values[isDefaultIndex].toLowerCase()
            row.is_default = ['true', '1', 'yes', 'y'].includes(defaultValue)
          }

          const notesIndex = headers.indexOf('notes')
          if (notesIndex >= 0 && values[notesIndex]) {
            row.notes = values[notesIndex]
          }

          parsed.push(row)
        }

        // Validate parsed data
        const validationResults = validateBulkData(parsed)
        const validationMap = new Map(
          validationResults.map(result => [result.row - 1, result.errors])
        )

        const parsedWithValidation: ParsedRow[] = parsed.map((row, index) => ({
          ...row,
          rowIndex: index + 2, // +2 because index starts at 0 and we skip header row
          isValid: !validationMap.has(index),
          errors: validationMap.get(index) || [],
        }))

        setParsedRows(parsedWithValidation)
        setStep('preview')
        setUploadError('')
      } catch (error) {
        setUploadError('Error parsing CSV file. Please check the format.')
      }
    },
    [validateBulkData]
  )

  // Handle import
  const handleImport = async () => {
    if (!csvData) return

    try {
      const result = await importFromCSV(csvData)
      setImportResult(result)
      setStep('result')
      onSuccess?.(result)
    } catch (error) {
      setUploadError(error instanceof Error ? error.message : 'Import failed')
    }
  }

  // Reset form
  const handleReset = () => {
    setCsvFile(null)
    setCsvData('')
    setParsedRows([])
    setImportResult(null)
    setStep('upload')
    setUploadError('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Format price display
  const formatPrice = (price: number | null, currency: string | null) => {
    if (!price || !currency) return 'N/A'
    return formatCurrency(price, currency as any)
  }

  // Valid rows for import
  const validRows = parsedRows.filter(row => row.isValid)
  const invalidRows = parsedRows.filter(row => !row.isValid)

  return (
    <div className="space-y-6">
      {/* Step: Upload */}
      {step === 'upload' && (
        <>
          {/* Template Download */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Download className="h-5 w-5 text-orange-500" />
                CSV Template
              </CardTitle>
              <CardDescription className="text-slate-400">
                Download the CSV template to ensure proper formatting for
                customer-product relationships. Existing relationships will be
                updated with new data from the CSV.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={handleDownloadTemplate}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Template
              </Button>
              <div className="mt-4 text-sm text-slate-400">
                <p className="mb-2">Required columns:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <strong>customer_name</strong> - Name of the customer
                    company
                  </li>
                  <li>
                    <strong>product_name</strong> - Name of the product
                  </li>
                  <li>
                    <strong>packaging_type</strong> - Bag, Plastic Basket, or
                    Carton
                  </li>
                </ul>
                <p className="mt-2 mb-2">
                  Important columns (at least one required):
                </p>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <strong>unit_price_cif</strong> - CIF unit price
                  </li>
                  <li>
                    <strong>unit_price_fob</strong> - FOB unit price
                  </li>
                </ul>
                <p className="mt-2 mb-2">Optional columns:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <strong>customer_product_code</strong> - Customer's internal
                    product code
                  </li>
                  <li>
                    <strong>currency_code</strong> - THB, CNY, USD, or EUR
                    (default: USD)
                  </li>
                  <li>
                    <strong>standard_quantity</strong> - Standard order quantity
                  </li>
                  <li>
                    <strong>gross_weight_per_package</strong> - Gross weight in
                    KG
                  </li>
                  <li>
                    <strong>net_weight_per_package</strong> - Net weight in KG
                  </li>
                  <li>
                    <strong>quality_grade</strong> - Product quality grade
                  </li>
                  <li>
                    <strong>handling_instructions</strong> - Special handling
                    requirements
                  </li>
                  <li>
                    <strong>temperature_require</strong> - Temperature
                    requirements
                  </li>
                  <li>
                    <strong>vent_require</strong> - Ventilation requirements
                  </li>
                  <li>
                    <strong>shelf_life_days</strong> - Product shelf life in
                    days
                  </li>
                  <li>
                    <strong>is_default</strong> - true/false for default product
                    preference
                  </li>
                  <li>
                    <strong>notes</strong> - Additional notes
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Upload className="h-5 w-5 text-orange-500" />
                Upload CSV File
              </CardTitle>
              <CardDescription className="text-slate-400">
                Select a CSV file containing customer-product relationships with
                pricing information. New relationships will be created, existing
                ones will be updated.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  className="bg-slate-800 border-slate-600 text-white file:bg-orange-500 file:text-white file:border-0 file:rounded-md file:px-3 file:py-1"
                />

                {uploadError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{uploadError}</AlertDescription>
                  </Alert>
                )}

                {csvFile && (
                  <div className="flex items-center space-x-2 p-3 bg-slate-800 rounded border border-slate-600">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <span className="text-slate-200">{csvFile.name}</span>
                    <span className="text-slate-400 text-sm">
                      ({(csvFile.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Step: Preview */}
      {step === 'preview' && (
        <>
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <FileText className="h-5 w-5 text-orange-500" />
                Import Preview
              </CardTitle>
              <CardDescription className="text-slate-400">
                Review the customer-product relationships before importing.{' '}
                {validRows.length} valid rows, {invalidRows.length} invalid
                rows. Existing relationships will be updated, new ones will be
                created.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Summary */}
                <div className="flex space-x-4">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-green-400">
                      {validRows.length} Valid
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-red-400">
                      {invalidRows.length} Invalid
                    </span>
                  </div>
                </div>

                {/* Preview Table */}
                <div className="max-h-96 overflow-auto border border-slate-600 rounded">
                  <Table>
                    <TableHeader className="bg-slate-800 sticky top-0">
                      <TableRow className="border-slate-600">
                        <TableHead className="text-slate-200">Row</TableHead>
                        <TableHead className="text-slate-200">Status</TableHead>
                        <TableHead className="text-slate-200">
                          Customer
                        </TableHead>
                        <TableHead className="text-slate-200">
                          Product
                        </TableHead>
                        <TableHead className="text-slate-200">
                          Pricing
                        </TableHead>
                        <TableHead className="text-slate-200">
                          Packaging
                        </TableHead>
                        <TableHead className="text-slate-200">
                          Default
                        </TableHead>
                        <TableHead className="text-slate-200">Errors</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {parsedRows.map((row, index) => (
                        <TableRow key={index} className="border-slate-600">
                          <TableCell className="text-slate-300">
                            {row.rowIndex}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={row.isValid ? 'default' : 'destructive'}
                              className={
                                row.isValid
                                  ? 'bg-green-600 text-white'
                                  : 'bg-red-600 text-white'
                              }
                            >
                              {row.isValid ? 'Valid' : 'Invalid'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-slate-300 max-w-32 truncate">
                            {row.customer_name}
                          </TableCell>
                          <TableCell className="text-slate-300 max-w-32 truncate">
                            {row.product_name}
                          </TableCell>
                          <TableCell className="text-slate-300">
                            <div className="space-y-1">
                              {row.unit_price_cif && (
                                <div className="text-xs flex items-center">
                                  <DollarSign className="h-3 w-3 mr-1" />
                                  CIF:{' '}
                                  {formatPrice(
                                    row.unit_price_cif,
                                    row.currency_code
                                  )}
                                </div>
                              )}
                              {row.unit_price_fob && (
                                <div className="text-xs flex items-center">
                                  <DollarSign className="h-3 w-3 mr-1" />
                                  FOB:{' '}
                                  {formatPrice(
                                    row.unit_price_fob,
                                    row.currency_code
                                  )}
                                </div>
                              )}
                              {!row.unit_price_cif && !row.unit_price_fob && (
                                <span className="text-red-400 text-xs">
                                  No price
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-slate-300">
                            <div className="space-y-1">
                              <Badge
                                variant="outline"
                                className="border-slate-500 text-slate-300 text-xs"
                              >
                                {row.packaging_type}
                              </Badge>
                              {(row.gross_weight_per_package ||
                                row.net_weight_per_package) && (
                                <div className="text-xs text-slate-400 flex items-center">
                                  <Weight className="h-3 w-3 mr-1" />
                                  {row.gross_weight_per_package &&
                                    `${row.gross_weight_per_package}kg`}
                                  {row.gross_weight_per_package &&
                                    row.net_weight_per_package &&
                                    ' / '}
                                  {row.net_weight_per_package &&
                                    `${row.net_weight_per_package}kg net`}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-slate-300">
                            {row.is_default ? 'Yes' : 'No'}
                          </TableCell>
                          <TableCell className="text-slate-300">
                            {row.errors.length > 0 ? (
                              <div className="space-y-1">
                                {row.errors.map((error, errorIndex) => (
                                  <div
                                    key={errorIndex}
                                    className="text-red-400 text-xs"
                                  >
                                    {error}
                                  </div>
                                ))}
                              </div>
                            ) : (
                              '-'
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {invalidRows.length > 0 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      {invalidRows.length} rows contain errors and will be
                      skipped during import. Only {validRows.length} valid rows
                      will be processed.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleReset}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Start Over
            </Button>
            <Button
              onClick={handleImport}
              disabled={validRows.length === 0 || isBulkImporting}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              {isBulkImporting && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Import {validRows.length} Relationships
            </Button>
          </div>
        </>
      )}

      {/* Step: Result */}
      {step === 'result' && importResult && (
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Import Complete
            </CardTitle>
            <CardDescription className="text-slate-400">
              Customer-Product relationship import process has finished
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Results Summary */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="bg-slate-800 p-4 rounded border border-slate-600">
                  <div className="text-2xl font-bold text-green-400">
                    {importResult.summary.created}
                  </div>
                  <div className="text-sm text-slate-400">Created</div>
                </div>
                <div className="bg-slate-800 p-4 rounded border border-slate-600">
                  <div className="text-2xl font-bold text-blue-400">
                    {importResult.summary.updated}
                  </div>
                  <div className="text-sm text-slate-400">Updated</div>
                </div>
                <div className="bg-slate-800 p-4 rounded border border-slate-600">
                  <div className="text-2xl font-bold text-green-400">
                    {importResult.summary.successful}
                  </div>
                  <div className="text-sm text-slate-400">Successful</div>
                </div>
                <div className="bg-slate-800 p-4 rounded border border-slate-600">
                  <div className="text-2xl font-bold text-red-400">
                    {importResult.summary.failed}
                  </div>
                  <div className="text-sm text-slate-400">Failed</div>
                </div>
                <div className="bg-slate-800 p-4 rounded border border-slate-600">
                  <div className="text-2xl font-bold text-slate-300">
                    {importResult.summary.total}
                  </div>
                  <div className="text-sm text-slate-400">Total Processed</div>
                </div>
              </div>

              {/* Success Details */}
              {(importResult.created_relationships.length > 0 ||
                importResult.updated_relationships.length > 0) && (
                <div className="space-y-4">
                  {/* Created Relationships */}
                  {importResult.created_relationships.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-white font-medium flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Created Relationships (
                        {importResult.created_relationships.length})
                      </h4>
                      <div className="max-h-32 overflow-auto bg-slate-800 border border-slate-600 rounded p-3">
                        {importResult.created_relationships.map(
                          (relationship, index) => (
                            <div
                              key={index}
                              className="text-green-400 text-sm mb-1"
                            >
                              {relationship.customer?.name} ↔{' '}
                              {relationship.product?.name}
                              {relationship.customer_product_code &&
                                ` (${relationship.customer_product_code})`}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}

                  {/* Updated Relationships */}
                  {importResult.updated_relationships.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-white font-medium flex items-center gap-2">
                        <RefreshCw className="h-4 w-4 text-blue-500" />
                        Updated Relationships (
                        {importResult.updated_relationships.length})
                      </h4>
                      <div className="max-h-32 overflow-auto bg-slate-800 border border-slate-600 rounded p-3">
                        {importResult.updated_relationships.map(
                          (relationship, index) => (
                            <div
                              key={index}
                              className="text-blue-400 text-sm mb-1"
                            >
                              {relationship.customer?.name} ↔{' '}
                              {relationship.product?.name}
                              {relationship.customer_product_code &&
                                ` (${relationship.customer_product_code})`}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Error Details */}
              {importResult.errors && importResult.errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-white font-medium flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    Import Errors ({importResult.errors.length})
                  </h4>
                  <div className="max-h-32 overflow-auto bg-slate-800 border border-slate-600 rounded p-3">
                    {importResult.errors.map((errorItem, index) => (
                      <div key={index} className="text-red-400 text-sm mb-1">
                        Row {errorItem.row}: {errorItem.error}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex space-x-2">
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Import Another File
                </Button>
                <Button
                  onClick={onClose}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Close
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
