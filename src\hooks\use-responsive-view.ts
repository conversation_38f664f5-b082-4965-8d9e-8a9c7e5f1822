'use client'

import { useEffect } from 'react'
import { useShipmentStore } from '@/stores/shipment-store'

/**
 * Custom hook to automatically switch between table and card view based on screen size
 * Following the mobile-first specification where card view is preferred on mobile/tablet
 */
export function useResponsiveView() {
  const { viewMode, setViewMode } = useShipmentStore()

  useEffect(() => {
    const checkScreenSize = () => {
      // Mobile breakpoint: 0-767px -> Force card view
      // Tablet breakpoint: 768-1023px -> Allow both, default to card
      // Desktop breakpoint: 1024px+ -> Allow both, default to table
      
      const width = window.innerWidth
      
      if (width < 768) {
        // Mobile: Force card view for better UX
        if (viewMode !== 'card') {
          setViewMode('card')
        }
      }
      // For tablet and desktop, let user preference persist
      // Only set initial preference if not already set
      else if (width >= 1024 && viewMode === 'card') {
        // Desktop users might prefer table view initially
        // But respect their choice if they've actively selected card view
        const hasUserPreference = localStorage.getItem('shipment-store')
        if (!hasUserPreference) {
          setViewMode('table')
        }
      }
    }

    // Check on mount
    checkScreenSize()

    // Listen for resize events
    window.addEventListener('resize', checkScreenSize)
    
    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [viewMode, setViewMode])

  // Return current view mode and screen size info
  const getScreenInfo = () => {
    if (typeof window === 'undefined') return { size: 'desktop', isMobile: false, isTablet: false }
    
    const width = window.innerWidth
    if (width < 768) return { size: 'mobile', isMobile: true, isTablet: false }
    if (width < 1024) return { size: 'tablet', isMobile: false, isTablet: true }
    return { size: 'desktop', isMobile: false, isTablet: false }
  }

  return {
    viewMode,
    screenInfo: getScreenInfo(),
    setViewMode,
  }
}

/**
 * Hook for getting responsive breakpoint information
 */
export function useBreakpoint() {
  useEffect(() => {
    const updateBreakpoint = () => {
      // This could be expanded to provide more detailed breakpoint info
      // For now, we'll keep it simple
    }

    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])

  const getCurrentBreakpoint = () => {
    if (typeof window === 'undefined') return 'desktop'
    
    const width = window.innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet' 
    if (width < 1440) return 'desktop'
    return 'wide'
  }

  return {
    breakpoint: getCurrentBreakpoint(),
    isMobile: getCurrentBreakpoint() === 'mobile',
    isTablet: getCurrentBreakpoint() === 'tablet',
    isDesktop: getCurrentBreakpoint() === 'desktop',
    isWide: getCurrentBreakpoint() === 'wide',
  }
}