import type { OfflineStatusUpdate } from '@/types/status-update'

interface OfflineStorageDB extends IDBDatabase {
  // Extends IDBDatabase for type safety
}

export class OfflineStorageService {
  private static readonly DB_NAME = 'DYYOfflineStorage'
  private static readonly DB_VERSION = 1
  private static readonly STORE_NAME = 'statusUpdates'
  private static readonly PHOTO_STORE_NAME = 'photos'
  
  private static dbInstance: OfflineStorageDB | null = null

  /**
   * Initialize IndexedDB database
   */
  private static async initDB(): Promise<OfflineStorageDB> {
    if (this.dbInstance) {
      return this.dbInstance
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION)

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB database'))
      }

      request.onsuccess = () => {
        this.dbInstance = request.result as OfflineStorageDB
        resolve(this.dbInstance)
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result

        // Create status updates store
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          const store = db.createObjectStore(this.STORE_NAME, { keyPath: 'id' })
          store.createIndex('shipment_id', 'shipment_id', { unique: false })
          store.createIndex('sync_status', 'sync_status', { unique: false })
          store.createIndex('timestamp', 'timestamp', { unique: false })
        }

        // Create photos store
        if (!db.objectStoreNames.contains(this.PHOTO_STORE_NAME)) {
          const photoStore = db.createObjectStore(this.PHOTO_STORE_NAME, { keyPath: 'id' })
          photoStore.createIndex('update_id', 'update_id', { unique: false })
        }
      }
    })
  }

  /**
   * Store status update offline
   */
  static async storeStatusUpdate(update: OfflineStatusUpdate): Promise<void> {
    const db = await this.initDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.STORE_NAME, this.PHOTO_STORE_NAME], 'readwrite')
      const store = transaction.objectStore(this.STORE_NAME)
      const photoStore = transaction.objectStore(this.PHOTO_STORE_NAME)

      // Store the status update
      const updateRequest = store.put(update)

      // Store photos separately as blobs
      const photoPromises = update.photos.map((photo, index) => {
        return new Promise<void>((resolvePhoto, rejectPhoto) => {
          const photoRecord = {
            id: `${update.id}_photo_${index}`,
            update_id: update.id,
            photo_blob: photo,
            photo_name: photo.name,
            photo_type: photo.type,
            photo_size: photo.size,
            index
          }

          const photoRequest = photoStore.put(photoRecord)
          photoRequest.onsuccess = () => resolvePhoto()
          photoRequest.onerror = () => rejectPhoto(photoRequest.error)
        })
      })

      transaction.oncomplete = async () => {
        try {
          await Promise.all(photoPromises)
          resolve()
        } catch (error) {
          reject(error)
        }
      }

      transaction.onerror = () => {
        reject(transaction.error)
      }

      updateRequest.onerror = () => {
        reject(updateRequest.error)
      }
    })
  }

  /**
   * Get all pending status updates
   */
  static async getPendingUpdates(): Promise<OfflineStatusUpdate[]> {
    const db = await this.initDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.STORE_NAME, this.PHOTO_STORE_NAME], 'readonly')
      const store = transaction.objectStore(this.STORE_NAME)
      const photoStore = transaction.objectStore(this.PHOTO_STORE_NAME)
      const index = store.index('sync_status')

      const request = index.getAll('pending')

      request.onsuccess = async () => {
        try {
          const updates = request.result as OfflineStatusUpdate[]
          
          // Fetch associated photos for each update
          const updatesWithPhotos = await Promise.all(
            updates.map(async (update) => {
              const photoIndex = photoStore.index('update_id')
              const photosRequest = photoIndex.getAll(update.id)
              
              return new Promise<OfflineStatusUpdate>((resolveUpdate) => {
                photosRequest.onsuccess = () => {
                  const photoRecords = photosRequest.result
                  const photos = photoRecords
                    .sort((a, b) => a.index - b.index)
                    .map(record => record.photo_blob)
                  
                  resolveUpdate({
                    ...update,
                    photos
                  })
                }
                photosRequest.onerror = () => {
                  // If photos can't be retrieved, return update without photos
                  resolveUpdate(update)
                }
              })
            })
          )

          resolve(updatesWithPhotos)
        } catch (error) {
          reject(error)
        }
      }

      request.onerror = () => {
        reject(request.error)
      }
    })
  }

  /**
   * Update sync status of a status update
   */
  static async updateSyncStatus(
    updateId: string, 
    status: 'pending' | 'syncing' | 'synced' | 'failed',
    retryCount?: number
  ): Promise<void> {
    const db = await this.initDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.STORE_NAME, 'readwrite')
      const store = transaction.objectStore(this.STORE_NAME)

      const getRequest = store.get(updateId)

      getRequest.onsuccess = () => {
        const update = getRequest.result
        if (update) {
          update.sync_status = status
          if (retryCount !== undefined) {
            update.retry_count = retryCount
          }

          const putRequest = store.put(update)
          putRequest.onsuccess = () => resolve()
          putRequest.onerror = () => reject(putRequest.error)
        } else {
          reject(new Error('Update not found'))
        }
      }

      getRequest.onerror = () => {
        reject(getRequest.error)
      }
    })
  }

  /**
   * Remove synced status updates and their photos
   */
  static async clearSyncedUpdates(): Promise<void> {
    const db = await this.initDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.STORE_NAME, this.PHOTO_STORE_NAME], 'readwrite')
      const store = transaction.objectStore(this.STORE_NAME)
      const photoStore = transaction.objectStore(this.PHOTO_STORE_NAME)
      const index = store.index('sync_status')

      const request = index.getAll('synced')

      request.onsuccess = async () => {
        try {
          const syncedUpdates = request.result
          
          // Delete all synced updates and their photos
          await Promise.all(
            syncedUpdates.map(update => {
              return new Promise<void>((resolveDelete) => {
                // Delete photos first
                const photoIndex = photoStore.index('update_id')
                const photosRequest = photoIndex.getAll(update.id)
                
                photosRequest.onsuccess = () => {
                  const photoRecords = photosRequest.result
                  const deletePhotoPromises = photoRecords.map(photoRecord => {
                    return new Promise<void>((resolvePhotoDelete) => {
                      const deletePhotoRequest = photoStore.delete(photoRecord.id)
                      deletePhotoRequest.onsuccess = () => resolvePhotoDelete()
                      deletePhotoRequest.onerror = () => resolvePhotoDelete() // Continue even if photo delete fails
                    })
                  })

                  Promise.all(deletePhotoPromises).then(() => {
                    // Delete the update
                    const deleteUpdateRequest = store.delete(update.id)
                    deleteUpdateRequest.onsuccess = () => resolveDelete()
                    deleteUpdateRequest.onerror = () => resolveDelete() // Continue even if update delete fails
                  })
                }
                
                photosRequest.onerror = () => {
                  // If we can't get photos, still try to delete the update
                  const deleteUpdateRequest = store.delete(update.id)
                  deleteUpdateRequest.onsuccess = () => resolveDelete()
                  deleteUpdateRequest.onerror = () => resolveDelete()
                }
              })
            })
          )

          resolve()
        } catch (error) {
          reject(error)
        }
      }

      request.onerror = () => {
        reject(request.error)
      }
    })
  }

  /**
   * Get total storage usage
   */
  static async getStorageUsage(): Promise<{
    updates: number
    photos: number
    totalSizeMB: number
  }> {
    const db = await this.initDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.STORE_NAME, this.PHOTO_STORE_NAME], 'readonly')
      const store = transaction.objectStore(this.STORE_NAME)
      const photoStore = transaction.objectStore(this.PHOTO_STORE_NAME)

      const updateCountRequest = store.count()
      const photoCountRequest = photoStore.count()

      let updateCount = 0
      let photoCount = 0
      let totalSize = 0

      updateCountRequest.onsuccess = () => {
        updateCount = updateCountRequest.result
      }

      photoCountRequest.onsuccess = () => {
        photoCount = photoCountRequest.result
        
        // Get all photos to calculate total size
        const getAllPhotosRequest = photoStore.getAll()
        getAllPhotosRequest.onsuccess = () => {
          const photos = getAllPhotosRequest.result
          totalSize = photos.reduce((sum, photo) => sum + photo.photo_size, 0)
          
          resolve({
            updates: updateCount,
            photos: photoCount,
            totalSizeMB: totalSize / (1024 * 1024)
          })
        }
      }

      transaction.onerror = () => {
        reject(transaction.error)
      }
    })
  }

  /**
   * Clear all offline data
   */
  static async clearAllData(): Promise<void> {
    const db = await this.initDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.STORE_NAME, this.PHOTO_STORE_NAME], 'readwrite')
      const store = transaction.objectStore(this.STORE_NAME)
      const photoStore = transaction.objectStore(this.PHOTO_STORE_NAME)

      const clearUpdatesRequest = store.clear()
      const clearPhotosRequest = photoStore.clear()

      let completedOperations = 0
      const totalOperations = 2

      const checkComplete = () => {
        completedOperations++
        if (completedOperations === totalOperations) {
          resolve()
        }
      }

      clearUpdatesRequest.onsuccess = checkComplete
      clearPhotosRequest.onsuccess = checkComplete

      transaction.onerror = () => {
        reject(transaction.error)
      }
    })
  }

  /**
   * Check if IndexedDB is supported
   */
  static isSupported(): boolean {
    return 'indexedDB' in window && indexedDB !== null
  }
}