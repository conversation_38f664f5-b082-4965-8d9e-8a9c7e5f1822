-- Document Management and Notification System Tables
-- Story 1.2: Database Schema & RLS Foundation
-- This migration creates document management and notification system tables

-- ============================================================================
-- DOCUMENTS TABLE
-- ============================================================================

-- Shipment document storage with file paths and metadata
CREATE TABLE documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    shipment_id UUID NOT NULL REFERENCES shipments(id) ON DELETE CASCADE,
    
    -- Document identification
    document_type document_type_enum NOT NULL,
    document_name TEXT NOT NULL,
    document_number TEXT, -- Official document number if applicable
    
    -- File information
    file_path TEXT NOT NULL, -- Path to stored file in Supabase Storage
    file_name TEXT NOT NULL,
    file_size_bytes INTEGER,
    file_type TEXT, -- MIME type (application/pdf, image/jpeg, etc.)
    file_hash TEXT, -- For duplicate detection and integrity
    
    -- Document metadata
    description TEXT,
    version INTEGER DEFAULT 1,
    is_original BOOLEAN DEFAULT true, -- True for original, false for copies/amendments
    language TEXT DEFAULT 'en', -- ISO language code
    
    -- Business information
    issued_date DATE,
    valid_until DATE,
    issued_by TEXT, -- Company or authority that issued the document
    
    -- Access and sharing
    is_public BOOLEAN DEFAULT false, -- Can be shared with external parties
    access_level TEXT DEFAULT 'shipment', -- shipment, company, internal
    shared_with_customer BOOLEAN DEFAULT false,
    shared_with_carrier BOOLEAN DEFAULT false,
    
    -- Processing status
    is_verified BOOLEAN DEFAULT false,
    verification_notes TEXT,
    verified_by UUID REFERENCES profiles(id),
    verified_at TIMESTAMPTZ,
    
    -- Upload information
    uploaded_by UUID NOT NULL REFERENCES profiles(id),
    upload_source TEXT, -- web, mobile, api, system
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT positive_file_size CHECK (file_size_bytes IS NULL OR file_size_bytes > 0),
    CONSTRAINT positive_version CHECK (version > 0),
    CONSTRAINT valid_date_range CHECK (valid_until IS NULL OR issued_date IS NULL OR valid_until >= issued_date),
    CONSTRAINT valid_language CHECK (language ~* '^[a-z]{2}(-[A-Z]{2})?$'),
    CONSTRAINT valid_access_level CHECK (access_level IN ('shipment', 'company', 'internal', 'public'))
);

-- ============================================================================
-- DOCUMENT TEMPLATES TABLE
-- ============================================================================

-- PDF generation templates with JSONB data
CREATE TABLE document_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Template identification
    template_name TEXT NOT NULL UNIQUE,
    document_type document_type_enum NOT NULL,
    version TEXT DEFAULT '1.0',
    
    -- Template content
    template_content TEXT NOT NULL, -- HTML template with placeholders
    template_data JSONB, -- Default data and field mappings
    template_styles TEXT, -- CSS styles for PDF generation
    
    -- Configuration
    page_size TEXT DEFAULT 'A4', -- A4, Letter, Legal, etc.
    page_orientation TEXT DEFAULT 'portrait', -- portrait, landscape
    margin_top INTEGER DEFAULT 20, -- Margins in mm
    margin_bottom INTEGER DEFAULT 20,
    margin_left INTEGER DEFAULT 20,
    margin_right INTEGER DEFAULT 20,
    
    -- Localization support
    language TEXT DEFAULT 'en',
    currency_format TEXT DEFAULT 'USD',
    date_format TEXT DEFAULT 'YYYY-MM-DD',
    number_format TEXT DEFAULT 'en-US',
    
    -- Metadata
    description TEXT,
    usage_notes TEXT,
    required_fields TEXT[], -- Array of required field names
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false, -- Default template for this document type
    
    -- Audit
    created_by UUID NOT NULL REFERENCES profiles(id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT positive_margins CHECK (
        margin_top >= 0 AND margin_bottom >= 0 AND 
        margin_left >= 0 AND margin_right >= 0
    ),
    CONSTRAINT valid_page_size CHECK (page_size IN ('A4', 'A3', 'Letter', 'Legal', 'A5')),
    CONSTRAINT valid_orientation CHECK (page_orientation IN ('portrait', 'landscape')),
    CONSTRAINT valid_language CHECK (language ~* '^[a-z]{2}(-[A-Z]{2})?$')
);

-- ============================================================================
-- NOTIFICATIONS TABLE
-- ============================================================================

-- Notification history for the communication system
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Notification content
    notification_type notification_type_enum NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    
    -- Recipients
    recipient_id UUID NOT NULL REFERENCES profiles(id),
    sender_id UUID REFERENCES profiles(id), -- NULL for system notifications
    
    -- Related entities
    shipment_id UUID REFERENCES shipments(id),
    document_id UUID REFERENCES documents(id),
    status_history_id UUID REFERENCES status_history(id),
    
    -- Delivery channels and status
    channels notification_channel_enum[] NOT NULL, -- Array of channels to send to
    email_sent BOOLEAN DEFAULT false,
    email_sent_at TIMESTAMPTZ,
    sms_sent BOOLEAN DEFAULT false,
    sms_sent_at TIMESTAMPTZ,
    line_sent BOOLEAN DEFAULT false,
    line_sent_at TIMESTAMPTZ,
    wechat_sent BOOLEAN DEFAULT false,
    wechat_sent_at TIMESTAMPTZ,
    in_app_read BOOLEAN DEFAULT false,
    in_app_read_at TIMESTAMPTZ,
    
    -- Notification data
    notification_data JSONB, -- Additional data for the notification
    language TEXT DEFAULT 'en',
    priority INTEGER DEFAULT 1, -- 1=low, 2=normal, 3=high, 4=urgent
    
    -- Status and tracking
    is_sent BOOLEAN DEFAULT false,
    send_attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    last_attempt_at TIMESTAMPTZ,
    error_message TEXT,
    
    -- Scheduling
    scheduled_for TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT valid_priority CHECK (priority BETWEEN 1 AND 4),
    CONSTRAINT positive_attempts CHECK (send_attempts >= 0 AND max_attempts > 0),
    CONSTRAINT valid_language CHECK (language ~* '^[a-z]{2}(-[A-Z]{2})?$'),
    CONSTRAINT valid_schedule CHECK (expires_at IS NULL OR expires_at >= scheduled_for)
);

-- ============================================================================
-- NOTIFICATION PREFERENCES TABLE
-- ============================================================================

-- User-specific notification channel preferences
CREATE TABLE notification_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Channel preferences
    email_enabled BOOLEAN DEFAULT true,
    sms_enabled BOOLEAN DEFAULT false,
    line_enabled BOOLEAN DEFAULT false,
    wechat_enabled BOOLEAN DEFAULT false,
    in_app_enabled BOOLEAN DEFAULT true,
    
    -- Notification type preferences
    status_updates_enabled BOOLEAN DEFAULT true,
    assignment_notifications_enabled BOOLEAN DEFAULT true,
    document_notifications_enabled BOOLEAN DEFAULT true,
    delay_alerts_enabled BOOLEAN DEFAULT true,
    system_notifications_enabled BOOLEAN DEFAULT true,
    
    -- Timing preferences
    quiet_hours_start TIME, -- e.g., '22:00'
    quiet_hours_end TIME, -- e.g., '08:00'
    timezone TEXT DEFAULT 'UTC', -- IANA timezone
    
    -- Language and format preferences
    language TEXT DEFAULT 'en',
    date_format TEXT DEFAULT 'YYYY-MM-DD',
    time_format TEXT DEFAULT '24h', -- 12h or 24h
    
    -- Frequency controls
    email_digest_frequency TEXT DEFAULT 'immediate', -- immediate, hourly, daily, weekly
    max_sms_per_day INTEGER DEFAULT 10,
    max_line_per_day INTEGER DEFAULT 20,
    
    -- Business rules
    emergency_override BOOLEAN DEFAULT true, -- Override quiet hours for urgent notifications
    weekend_delivery BOOLEAN DEFAULT false, -- Deliver non-urgent notifications on weekends
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT valid_quiet_hours CHECK (
        (quiet_hours_start IS NULL AND quiet_hours_end IS NULL) OR
        (quiet_hours_start IS NOT NULL AND quiet_hours_end IS NOT NULL)
    ),
    CONSTRAINT valid_frequency CHECK (email_digest_frequency IN ('immediate', 'hourly', 'daily', 'weekly')),
    CONSTRAINT positive_limits CHECK (max_sms_per_day > 0 AND max_line_per_day > 0),
    CONSTRAINT valid_time_format CHECK (time_format IN ('12h', '24h')),
    CONSTRAINT valid_language CHECK (language ~* '^[a-z]{2}(-[A-Z]{2})?$'),
    UNIQUE(user_id) -- One preference record per user
);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_documents_updated_at 
    BEFORE UPDATE ON documents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_document_templates_updated_at 
    BEFORE UPDATE ON document_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at 
    BEFORE UPDATE ON notifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at 
    BEFORE UPDATE ON notification_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- NOTIFICATION BUSINESS LOGIC
-- ============================================================================

-- Function to create automatic notifications
CREATE OR REPLACE FUNCTION create_automatic_notification(
    p_type notification_type_enum,
    p_title TEXT,
    p_message TEXT,
    p_recipient_id UUID,
    p_shipment_id UUID DEFAULT NULL,
    p_priority INTEGER DEFAULT 2
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
    user_preferences RECORD;
    notification_channels notification_channel_enum[];
BEGIN
    -- Get user preferences
    SELECT * INTO user_preferences 
    FROM notification_preferences 
    WHERE user_id = p_recipient_id;
    
    -- Build channels array based on preferences and notification type
    notification_channels := ARRAY[]::notification_channel_enum[];
    
    IF user_preferences.in_app_enabled AND 
       CASE p_type
           WHEN 'status_update' THEN user_preferences.status_updates_enabled
           WHEN 'assignment' THEN user_preferences.assignment_notifications_enabled
           WHEN 'document_ready' THEN user_preferences.document_notifications_enabled
           WHEN 'delay_alert' THEN user_preferences.delay_alerts_enabled
           WHEN 'system' THEN user_preferences.system_notifications_enabled
           ELSE true
       END THEN
        notification_channels := array_append(notification_channels, 'in_app');
    END IF;
    
    IF user_preferences.email_enabled THEN
        notification_channels := array_append(notification_channels, 'email');
    END IF;
    
    IF user_preferences.sms_enabled AND p_priority >= 3 THEN
        notification_channels := array_append(notification_channels, 'sms');
    END IF;
    
    -- Create notification
    INSERT INTO notifications (
        notification_type,
        title,
        message,
        recipient_id,
        shipment_id,
        channels,
        priority,
        language
    ) VALUES (
        p_type,
        p_title,
        p_message,
        p_recipient_id,
        p_shipment_id,
        notification_channels,
        p_priority,
        COALESCE(user_preferences.language, 'en')
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically create status change notifications
CREATE OR REPLACE FUNCTION notify_status_change()
RETURNS TRIGGER AS $$
DECLARE
    notification_title TEXT;
    notification_message TEXT;
    stakeholder_record RECORD;
BEGIN
    -- Build notification content
    notification_title := 'Shipment Status Update: ' || NEW.shipment_number;
    notification_message := 'Shipment status changed from ' || 
                           COALESCE(OLD.status::TEXT, 'none') || 
                           ' to ' || NEW.status::TEXT;
    
    -- Notify relevant stakeholders
    FOR stakeholder_record IN 
        SELECT DISTINCT p.id as user_id
        FROM profiles p
        JOIN companies c ON c.id = p.company_id
        WHERE c.id IN (
            NEW.customer_id, 
            NEW.carrier_id, 
            NEW.factory_id
        )
        AND p.is_active = true
    LOOP
        PERFORM create_automatic_notification(
            'status_update',
            notification_title,
            notification_message,
            stakeholder_record.user_id,
            NEW.id,
            2 -- Normal priority
        );
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply status change notification trigger
CREATE TRIGGER notify_shipment_status_change 
    AFTER UPDATE ON shipments 
    FOR EACH ROW 
    WHEN (OLD.status IS DISTINCT FROM NEW.status)
    EXECUTE FUNCTION notify_status_change();

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Documents table indexes
CREATE INDEX idx_documents_shipment ON documents(shipment_id);
CREATE INDEX idx_documents_type ON documents(document_type);
CREATE INDEX idx_documents_uploaded_by ON documents(uploaded_by);
CREATE INDEX idx_documents_public ON documents(is_public);
CREATE INDEX idx_documents_verified ON documents(is_verified);
CREATE INDEX idx_documents_created ON documents(created_at);

-- Document templates indexes
CREATE INDEX idx_document_templates_type ON document_templates(document_type);
CREATE INDEX idx_document_templates_active ON document_templates(is_active);
CREATE INDEX idx_document_templates_default ON document_templates(is_default);

-- Notifications indexes
CREATE INDEX idx_notifications_recipient ON notifications(recipient_id);
CREATE INDEX idx_notifications_shipment ON notifications(shipment_id);
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_priority ON notifications(priority);
CREATE INDEX idx_notifications_scheduled ON notifications(scheduled_for);
CREATE INDEX idx_notifications_sent ON notifications(is_sent);
CREATE INDEX idx_notifications_read ON notifications(in_app_read);

-- Notification preferences indexes
CREATE INDEX idx_notification_preferences_user ON notification_preferences(user_id);

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE documents IS 'Shipment document storage with file paths, metadata, and access controls';
COMMENT ON TABLE document_templates IS 'PDF generation templates with JSONB configuration data';
COMMENT ON TABLE notifications IS 'Notification history supporting multiple channels including Asian messaging apps';
COMMENT ON TABLE notification_preferences IS 'User-specific notification channel and timing preferences';

COMMENT ON COLUMN documents.file_path IS 'Secure file path in Supabase Storage with access controls';
COMMENT ON COLUMN documents.access_level IS 'Access control level: shipment, company, internal, public';
COMMENT ON COLUMN document_templates.template_content IS 'HTML template with placeholder variables for PDF generation';
COMMENT ON COLUMN notifications.channels IS 'Array of notification channels to send this notification to';
COMMENT ON COLUMN notification_preferences.quiet_hours_start IS 'Start time for quiet hours (no non-urgent notifications)';
COMMENT ON COLUMN notification_preferences.emergency_override IS 'Override quiet hours for urgent notifications';

COMMENT ON FUNCTION create_automatic_notification(notification_type_enum, TEXT, TEXT, UUID, UUID, INTEGER) IS 'Creates automatic notifications based on user preferences';
COMMENT ON FUNCTION notify_status_change() IS 'Trigger function to automatically notify stakeholders of shipment status changes';