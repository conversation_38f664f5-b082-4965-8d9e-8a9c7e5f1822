import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { AuthErrorDisplay } from '@/components/auth/auth-error-display'
import { PasswordStrengthIndicator } from '@/components/auth/password-strength-indicator'
import type { AuthError } from '@/lib/supabase/auth'

describe('AuthErrorDisplay Component', () => {
  describe('Null/Empty States', () => {
    it('should not render when error is null', () => {
      const { container } = render(<AuthErrorDisplay error={null} />)
      expect(container.firstChild).toBeNull()
    })

    it('should not render when error is empty string', () => {
      const { container } = render(<AuthErrorDisplay error="" />)
      expect(container.firstChild).toBeNull()
    })
  })

  describe('String Error Handling', () => {
    it('should render simple string error with alert icon', () => {
      render(<AuthErrorDisplay error="Something went wrong" />)
      
      expect(screen.getByText('Something went wrong')).toBeInTheDocument()
      expect(screen.getByRole('alert')).toHaveClass('bg-red-500/10', 'border-red-500/20')
    })

    it('should apply custom className for string errors', () => {
      const { container } = render(
        <AuthErrorDisplay error="Test error" className="custom-class" />
      )
      
      expect(container.firstChild).toHaveClass('custom-class')
    })
  })

  describe('AuthError Object Handling', () => {
    const mockAuthError: AuthError = {
      code: 'INVALID_CREDENTIALS',
      message: 'Invalid email or password',
      timestamp: new Date(),
      details: { attemptCount: 1 }
    }

    it('should render AuthError with correct message and help text', () => {
      render(<AuthErrorDisplay error={mockAuthError} />)
      
      expect(screen.getByText('Invalid email or password')).toBeInTheDocument()
      expect(screen.getByText('Double-check your email and password. Make sure Caps Lock is off.')).toBeInTheDocument()
    })

    it('should use correct icon for different error codes', () => {
      const testCases = [
        { code: 'RATE_LIMITED', expectedIcon: 'Clock' },
        { code: 'EMAIL_NOT_CONFIRMED', expectedIcon: 'Mail' },
        { code: 'ACCOUNT_DEACTIVATED', expectedIcon: 'Shield' },
        { code: 'NETWORK_ERROR', expectedIcon: 'RefreshCw' },
        { code: 'UNKNOWN_ERROR', expectedIcon: 'AlertCircle' }
      ]

      testCases.forEach(({ code, expectedIcon }) => {
        const error: AuthError = { ...mockAuthError, code: code as any }
        const { container, unmount } = render(<AuthErrorDisplay error={error} />)
        
        // Check if the correct icon component is rendered (by checking SVG presence)
        const svg = container.querySelector('svg')
        expect(svg).toBeInTheDocument()
        
        unmount()
      })
    })

    it('should apply correct colors for different error codes', () => {
      const testCases = [
        { code: 'RATE_LIMITED', expectedColors: ['bg-yellow-500/10', 'border-yellow-500/20', 'text-yellow-300'] },
        { code: 'EMAIL_NOT_CONFIRMED', expectedColors: ['bg-blue-500/10', 'border-blue-500/20', 'text-blue-300'] },
        { code: 'ACCOUNT_DEACTIVATED', expectedColors: ['bg-orange-500/10', 'border-orange-500/20', 'text-orange-300'] },
        { code: 'NETWORK_ERROR', expectedColors: ['bg-purple-500/10', 'border-purple-500/20', 'text-purple-300'] },
        { code: 'INVALID_CREDENTIALS', expectedColors: ['bg-red-500/10', 'border-red-500/20', 'text-red-300'] }
      ]

      testCases.forEach(({ code, expectedColors }) => {
        const error: AuthError = { ...mockAuthError, code: code as any }
        const { container, unmount } = render(<AuthErrorDisplay error={error} />)
        
        const errorDiv = container.firstChild as HTMLElement
        expectedColors.forEach(colorClass => {
          expect(errorDiv).toHaveClass(colorClass)
        })
        
        unmount()
      })
    })
  })

  describe('Retry Functionality', () => {
    it('should show retry button for NETWORK_ERROR with onRetry handler', () => {
      const mockRetry = vi.fn()
      const error: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed',
        timestamp: new Date()
      }

      render(<AuthErrorDisplay error={error} onRetry={mockRetry} />)
      
      const retryButton = screen.getByRole('button', { name: /try again/i })
      expect(retryButton).toBeInTheDocument()
      
      fireEvent.click(retryButton)
      expect(mockRetry).toHaveBeenCalledTimes(1)
    })

    it('should show retry button for UNKNOWN_ERROR with onRetry handler', () => {
      const mockRetry = vi.fn()
      const error: AuthError = {
        code: 'UNKNOWN_ERROR',
        message: 'Something went wrong',
        timestamp: new Date()
      }

      render(<AuthErrorDisplay error={error} onRetry={mockRetry} />)
      
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
    })

    it('should not show retry button for non-retryable errors', () => {
      const mockRetry = vi.fn()
      const error: AuthError = {
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid credentials',
        timestamp: new Date()
      }

      render(<AuthErrorDisplay error={error} onRetry={mockRetry} />)
      
      expect(screen.queryByRole('button', { name: /try again/i })).not.toBeInTheDocument()
    })

    it('should not show retry button without onRetry handler', () => {
      const error: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network error',
        timestamp: new Date()
      }

      render(<AuthErrorDisplay error={error} />)
      
      expect(screen.queryByRole('button', { name: /try again/i })).not.toBeInTheDocument()
    })
  })

  describe('Development Mode Details', () => {
    const originalEnv = process.env.NODE_ENV

    afterEach(() => {
      process.env.NODE_ENV = originalEnv
    })

    it('should show technical details in development mode', () => {
      process.env.NODE_ENV = 'development'
      
      const error: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network error',
        timestamp: new Date(),
        details: { statusCode: 500, endpoint: '/api/auth' }
      }

      render(<AuthErrorDisplay error={error} />)
      
      expect(screen.getByText('Technical Details (Dev Mode)')).toBeInTheDocument()
      expect(screen.getByText(/"statusCode": 500/)).toBeInTheDocument()
      expect(screen.getByText(/"endpoint": "\/api\/auth"/)).toBeInTheDocument()
    })

    it('should not show technical details in production mode', () => {
      process.env.NODE_ENV = 'production'
      
      const error: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network error',
        timestamp: new Date(),
        details: { statusCode: 500 }
      }

      render(<AuthErrorDisplay error={error} />)
      
      expect(screen.queryByText('Technical Details (Dev Mode)')).not.toBeInTheDocument()
    })

    it('should not show details section when no details provided', () => {
      process.env.NODE_ENV = 'development'
      
      const error: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network error',
        timestamp: new Date()
      }

      render(<AuthErrorDisplay error={error} />)
      
      expect(screen.queryByText('Technical Details (Dev Mode)')).not.toBeInTheDocument()
    })
  })

  describe('Help Text Messages', () => {
    const helpTextCases = [
      {
        code: 'INVALID_CREDENTIALS',
        expectedText: 'Double-check your email and password. Make sure Caps Lock is off.'
      },
      {
        code: 'EMAIL_NOT_CONFIRMED',
        expectedText: 'Check your spam folder if you don\'t see the confirmation email.'
      },
      {
        code: 'RATE_LIMITED',
        expectedText: 'This helps protect your account from unauthorized access attempts.'
      },
      {
        code: 'USER_NOT_FOUND',
        expectedText: 'Try signing up for a new account instead.'
      },
      {
        code: 'EMAIL_EXISTS',
        expectedText: 'Use the "Forgot Password" link if you can\'t remember your password.'
      },
      {
        code: 'ACCOUNT_DEACTIVATED',
        expectedText: 'Contact your administrator or support team for assistance.'
      },
      {
        code: 'NETWORK_ERROR',
        expectedText: 'Check your internet connection and try again.'
      },
      {
        code: 'UNKNOWN_ERROR',
        expectedText: 'If this problem persists, please contact support.'
      }
    ]

    helpTextCases.forEach(({ code, expectedText }) => {
      it(`should show correct help text for ${code}`, () => {
        const error: AuthError = {
          code: code as any,
          message: 'Error message',
          timestamp: new Date()
        }

        render(<AuthErrorDisplay error={error} />)
        expect(screen.getByText(expectedText)).toBeInTheDocument()
      })
    })
  })
})

describe('PasswordStrengthIndicator Component', () => {
  describe('Empty Password State', () => {
    it('should not render when password is empty', () => {
      const { container } = render(<PasswordStrengthIndicator password="" />)
      expect(container.firstChild).toBeNull()
    })

    it('should not render when password is null/undefined', () => {
      const { container } = render(<PasswordStrengthIndicator password={null as any} />)
      expect(container.firstChild).toBeNull()
    })
  })

  describe('Password Requirements Validation', () => {
    const requirements = [
      { test: 'weak', description: 'weak password (too short)', expectedClass: 'text-slate-400' },
      { test: 'weakPassword1!', description: 'missing uppercase', expectedClass: 'text-slate-400' },
      { test: 'WEAKPASSWORD1!', description: 'missing lowercase', expectedClass: 'text-slate-400' },
      { test: 'WeakPassword!', description: 'missing number', expectedClass: 'text-slate-400' },
      { test: 'WeakPassword1', description: 'missing special character', expectedClass: 'text-slate-400' },
      { test: 'WeakPasssssword1!', description: 'repeated characters', expectedClass: 'text-slate-400' },
      { test: 'StrongPassword1!', description: 'strong password', expectedClass: 'text-green-400' }
    ]

    requirements.forEach(({ test, description, expectedClass }) => {
      it(`should validate ${description}`, () => {
        render(<PasswordStrengthIndicator password={test} />)
        
        // Check if requirements are rendered
        expect(screen.getByText('At least 8 characters')).toBeInTheDocument()
        expect(screen.getByText('One lowercase letter')).toBeInTheDocument()
        expect(screen.getByText('One uppercase letter')).toBeInTheDocument()
        expect(screen.getByText('One number')).toBeInTheDocument()
        expect(screen.getByText('One special character')).toBeInTheDocument()
        expect(screen.getByText('No repeated characters')).toBeInTheDocument()
      })
    })
  })

  describe('Strength Calculation', () => {
    const strengthTests = [
      { password: 'weak', expectedText: 'Weak', expectedColor: 'bg-red-500' },
      { password: 'Weak1', expectedText: 'Weak', expectedColor: 'bg-red-500' },
      { password: 'WeakPass1', expectedText: 'Fair', expectedColor: 'bg-orange-500' },
      { password: 'WeakPass1!', expectedText: 'Good', expectedColor: 'bg-yellow-500' },
      { password: 'StrongPass1!', expectedText: 'Strong', expectedColor: 'bg-green-500' }
    ]

    strengthTests.forEach(({ password, expectedText, expectedColor }) => {
      it(`should show ${expectedText} strength for password: ${password}`, () => {
        render(<PasswordStrengthIndicator password={password} />)
        
        expect(screen.getByText(expectedText)).toBeInTheDocument()
        
        // Check strength bar color
        const strengthBar = document.querySelector('.h-full.transition-all')
        expect(strengthBar).toHaveClass(expectedColor)
      })
    })
  })

  describe('Visual Requirements Indicators', () => {
    it('should show check marks for met requirements', () => {
      render(<PasswordStrengthIndicator password="StrongPassword1!" />)
      
      // All requirements should be met, so all should have green check marks
      const checkIcons = document.querySelectorAll('.text-green-400')
      expect(checkIcons.length).toBeGreaterThan(0)
    })

    it('should show X marks for unmet requirements', () => {
      render(<PasswordStrengthIndicator password="weak" />)
      
      // Most requirements should be unmet, so there should be X marks
      const xIcons = document.querySelectorAll('.text-slate-500')
      expect(xIcons.length).toBeGreaterThan(0)
    })

    it('should update indicators when password changes', () => {
      const { rerender } = render(<PasswordStrengthIndicator password="weak" />)
      
      // Initially, should have X marks for unmet requirements
      expect(document.querySelectorAll('.text-slate-500')).toHaveLength(6) // All 6 requirements unmet
      
      rerender(<PasswordStrengthIndicator password="StrongPassword1!" />)
      
      // After strong password, should have check marks
      expect(document.querySelectorAll('.text-green-400')).toHaveLength(6) // All 6 requirements met
    })
  })

  describe('Strength Bar Progress', () => {
    it('should show correct progress percentage', () => {
      render(<PasswordStrengthIndicator password="WeakPass1!" />)
      
      // This password meets 5 out of 6 requirements = ~83%
      const strengthBar = document.querySelector('.h-full.transition-all') as HTMLElement
      expect(strengthBar?.style.width).toBe('83.33333333333334%')
    })

    it('should show 100% for fully strong password', () => {
      render(<PasswordStrengthIndicator password="StrongPassword1!" />)
      
      const strengthBar = document.querySelector('.h-full.transition-all') as HTMLElement
      expect(strengthBar?.style.width).toBe('100%')
    })

    it('should show low percentage for weak password', () => {
      render(<PasswordStrengthIndicator password="weak" />)
      
      const strengthBar = document.querySelector('.h-full.transition-all') as HTMLElement
      expect(strengthBar?.style.width).toBe('0%')
    })
  })

  describe('Additional Validation Errors', () => {
    it('should show validation errors for longer passwords that fail validation', () => {
      // Test with a password that's long enough but fails other validation
      // Note: This depends on the validatePassword function implementation
      render(<PasswordStrengthIndicator password="password123" />)
      
      // Should show password strength text
      expect(screen.getByText('Password strength')).toBeInTheDocument()
    })

    it('should apply custom className', () => {
      const { container } = render(
        <PasswordStrengthIndicator password="test123" className="custom-class" />
      )
      
      expect(container.firstChild).toHaveClass('custom-class')
    })
  })

  describe('Requirements Text Content', () => {
    it('should display all requirement labels correctly', () => {
      render(<PasswordStrengthIndicator password="TestPassword123!" />)
      
      const expectedLabels = [
        'At least 8 characters',
        'One lowercase letter',
        'One uppercase letter',
        'One number',
        'One special character',
        'No repeated characters'
      ]

      expectedLabels.forEach(label => {
        expect(screen.getByText(label)).toBeInTheDocument()
      })
    })
  })
})