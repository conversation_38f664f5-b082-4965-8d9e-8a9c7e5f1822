-- Container & Seal Number Confirmation System
-- Migration: Add confirmation tracking to containers table and audit support
-- Date: 2025-09-01

-- ============================================================================
-- ADD CONFIRMATION FIELDS TO CONTAINERS TABLE
-- ============================================================================

-- Add confirmation fields for container numbers
ALTER TABLE containers ADD COLUMN IF NOT EXISTS 
  container_number_confirmed BOOLEAN DEFAULT false NOT NULL;

ALTER TABLE containers ADD COLUMN IF NOT EXISTS 
  container_number_confirmed_by UUID REFERENCES profiles(user_id);

ALTER TABLE containers ADD COLUMN IF NOT EXISTS 
  container_number_confirmed_at TIMESTAMPTZ;

-- Add confirmation fields for seal numbers  
ALTER TABLE containers ADD COLUMN IF NOT EXISTS 
  seal_number_confirmed BOOLEAN DEFAULT false NOT NULL;

ALTER TABLE containers ADD COLUMN IF NOT EXISTS 
  seal_number_confirmed_by UUID REFERENCES profiles(user_id);

ALTER TABLE containers ADD COLUMN IF NOT EXISTS 
  seal_number_confirmed_at TIMESTAMPTZ;

-- Add updated_at column if not exists (for tracking changes)
ALTER TABLE containers ADD COLUMN IF NOT EXISTS 
  updated_at TIMESTAMPTZ DEFAULT now();

-- ============================================================================
-- ENHANCE STATUS HISTORY FOR CONFIRMATION AUDIT TRAIL
-- ============================================================================

-- Add action_type column to track different types of actions
ALTER TABLE status_history ADD COLUMN IF NOT EXISTS 
  action_type TEXT DEFAULT 'status_change' NOT NULL;

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Index for confirmation status queries
CREATE INDEX IF NOT EXISTS idx_containers_confirmation_status 
ON containers(container_number_confirmed, seal_number_confirmed);

-- Index for confirmed by user queries
CREATE INDEX IF NOT EXISTS idx_containers_confirmed_by 
ON containers(container_number_confirmed_by, seal_number_confirmed_by);

-- Index for confirmation timestamps
CREATE INDEX IF NOT EXISTS idx_containers_confirmation_dates 
ON containers(container_number_confirmed_at, seal_number_confirmed_at);

-- Index for status history action type
CREATE INDEX IF NOT EXISTS idx_status_history_action_type 
ON status_history(action_type);

-- Index for status history with container context
CREATE INDEX IF NOT EXISTS idx_status_history_container_actions 
ON status_history(shipment_id, action_type, created_at DESC);

-- ============================================================================
-- UPDATE TRIGGERS FOR CONFIRMATION TRACKING
-- ============================================================================

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_container_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to containers table
DROP TRIGGER IF EXISTS update_containers_updated_at_trigger ON containers;
CREATE TRIGGER update_containers_updated_at_trigger
    BEFORE UPDATE ON containers
    FOR EACH ROW EXECUTE FUNCTION update_container_updated_at();

-- ============================================================================
-- AUDIT TRAIL FUNCTION FOR CONFIRMATION ACTIONS
-- ============================================================================

-- Function to log confirmation actions to status_history
CREATE OR REPLACE FUNCTION log_container_confirmation_action()
RETURNS TRIGGER AS $$
DECLARE
    shipment_uuid UUID;
    action_message TEXT;
BEGIN
    -- Get shipment_id for the container
    SELECT shipment_id INTO shipment_uuid FROM containers WHERE id = NEW.id;
    
    -- Check if container number confirmation changed
    IF (OLD.container_number_confirmed != NEW.container_number_confirmed) THEN
        IF NEW.container_number_confirmed = true THEN
            action_message := 'Container number confirmed: ' || COALESCE(NEW.container_number, 'N/A');
            INSERT INTO status_history (
                shipment_id,
                status_from, 
                status_to,
                notes,
                updated_by,
                action_type,
                created_at
            ) VALUES (
                shipment_uuid,
                NULL, -- No status change
                NULL,
                action_message,
                NEW.container_number_confirmed_by,
                'container_confirmed',
                NEW.container_number_confirmed_at
            );
        ELSE
            action_message := 'Container number confirmation removed: ' || COALESCE(NEW.container_number, 'N/A');
            INSERT INTO status_history (
                shipment_id,
                status_from,
                status_to, 
                notes,
                updated_by,
                action_type,
                created_at
            ) VALUES (
                shipment_uuid,
                NULL,
                NULL,
                action_message,
                NEW.container_number_confirmed_by,
                'container_unconfirmed',
                CURRENT_TIMESTAMP
            );
        END IF;
    END IF;
    
    -- Check if seal number confirmation changed
    IF (OLD.seal_number_confirmed != NEW.seal_number_confirmed) THEN
        IF NEW.seal_number_confirmed = true THEN
            action_message := 'Seal number confirmed: ' || COALESCE(NEW.seal_number, 'N/A');
            INSERT INTO status_history (
                shipment_id,
                status_from,
                status_to,
                notes,
                updated_by,
                action_type,
                created_at
            ) VALUES (
                shipment_uuid,
                NULL,
                NULL,
                action_message,
                NEW.seal_number_confirmed_by,
                'seal_confirmed', 
                NEW.seal_number_confirmed_at
            );
        ELSE
            action_message := 'Seal number confirmation removed: ' || COALESCE(NEW.seal_number, 'N/A');
            INSERT INTO status_history (
                shipment_id,
                status_from,
                status_to,
                notes,
                updated_by,
                action_type,
                created_at
            ) VALUES (
                shipment_uuid,
                NULL,
                NULL,
                action_message,
                NEW.seal_number_confirmed_by,
                'seal_unconfirmed',
                CURRENT_TIMESTAMP
            );
        END IF;
    END IF;
    
    -- Check if container/seal numbers changed after confirmation (override case)
    IF (OLD.container_number IS DISTINCT FROM NEW.container_number AND NEW.container_number_confirmed = true) THEN
        action_message := 'Container number changed after confirmation: ' || 
                         COALESCE(OLD.container_number, 'N/A') || ' → ' || 
                         COALESCE(NEW.container_number, 'N/A');
        INSERT INTO status_history (
            shipment_id,
            status_from,
            status_to,
            notes,
            updated_by,
            action_type,
            created_at
        ) VALUES (
            shipment_uuid,
            NULL,
            NULL,
            action_message,
            NEW.container_number_confirmed_by,
            'confirmation_override',
            CURRENT_TIMESTAMP
        );
    END IF;
    
    IF (OLD.seal_number IS DISTINCT FROM NEW.seal_number AND NEW.seal_number_confirmed = true) THEN
        action_message := 'Seal number changed after confirmation: ' || 
                         COALESCE(OLD.seal_number, 'N/A') || ' → ' || 
                         COALESCE(NEW.seal_number, 'N/A');
        INSERT INTO status_history (
            shipment_id,
            status_from,
            status_to,
            notes,
            updated_by,
            action_type,
            created_at
        ) VALUES (
            shipment_uuid,
            NULL,
            NULL,
            action_message,
            NEW.seal_number_confirmed_by,
            'confirmation_override',
            CURRENT_TIMESTAMP
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply confirmation audit trigger
DROP TRIGGER IF EXISTS log_container_confirmation_trigger ON containers;
CREATE TRIGGER log_container_confirmation_trigger
    AFTER UPDATE ON containers
    FOR EACH ROW EXECUTE FUNCTION log_container_confirmation_action();

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES FOR CONFIRMATION
-- ============================================================================

-- Policy: Only CS and Admin can confirm containers
CREATE POLICY IF NOT EXISTS container_confirmation_policy ON containers
FOR UPDATE USING (
    -- Allow updates if user is admin or cs
    (auth.jwt() ->> 'role' IN ('admin', 'cs'))
    OR
    -- Allow drivers to update non-confirmed containers
    (auth.jwt() ->> 'role' = 'driver' AND NOT (
        COALESCE(container_number_confirmed, false) OR 
        COALESCE(seal_number_confirmed, false)
    ))
);

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON COLUMN containers.container_number_confirmed IS 'Boolean flag indicating if container number has been confirmed by CS/Admin';
COMMENT ON COLUMN containers.container_number_confirmed_by IS 'User ID of CS/Admin who confirmed the container number';  
COMMENT ON COLUMN containers.container_number_confirmed_at IS 'Timestamp when container number was confirmed';
COMMENT ON COLUMN containers.seal_number_confirmed IS 'Boolean flag indicating if seal number has been confirmed by CS/Admin';
COMMENT ON COLUMN containers.seal_number_confirmed_by IS 'User ID of CS/Admin who confirmed the seal number';
COMMENT ON COLUMN containers.seal_number_confirmed_at IS 'Timestamp when seal number was confirmed';
COMMENT ON COLUMN status_history.action_type IS 'Type of action: status_change, container_confirmed, seal_confirmed, confirmation_override';

-- ============================================================================
-- VALIDATION CONSTRAINTS
-- ============================================================================

-- Ensure confirmation timestamps exist when confirmation flags are true
ALTER TABLE containers ADD CONSTRAINT check_container_confirmation_timestamp 
CHECK (
    (container_number_confirmed = false OR container_number_confirmed_at IS NOT NULL)
    AND
    (seal_number_confirmed = false OR seal_number_confirmed_at IS NOT NULL)
);

-- Ensure confirmation user exists when confirmation flags are true
ALTER TABLE containers ADD CONSTRAINT check_container_confirmation_user
CHECK (
    (container_number_confirmed = false OR container_number_confirmed_by IS NOT NULL)  
    AND
    (seal_number_confirmed = false OR seal_number_confirmed_by IS NOT NULL)
);