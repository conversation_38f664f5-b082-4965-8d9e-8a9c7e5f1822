import { z } from 'zod'

// Driver form validation schema
export const driverFormSchema = z.object({
  carrier_id: z.string().uuid('Please select a valid carrier company'),
  driver_first_name: z
    .string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .regex(
      /^[a-zA-Z\u0E00-\u0E7F\s-']+$/,
      'First name can only contain letters, spaces, hyphens, and apostrophes'
    ),
  driver_last_name: z
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .regex(
      /^[a-zA-Z\u0E00-\u0E7F\s-']+$/,
      'Last name can only contain letters, spaces, hyphens, and apostrophes'
    ),
  driver_code: z
    .string()
    .max(20, 'Driver code must be less than 20 characters')
    .regex(
      /^[A-Z0-9-]+$/,
      'Driver code can only contain uppercase letters, numbers, and hyphens'
    )
    .optional()
    .or(z.literal('')),
  phone: z
    .string()
    .max(20, 'Phone number must be less than 20 characters')
    .regex(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number')
    .optional()
    .or(z.literal('')),
  line_id: z
    .string()
    .max(50, 'Line ID must be less than 50 characters')
    .optional()
    .or(z.literal('')),
  notes: z
    .string()
    .max(500, 'Notes must be less than 500 characters')
    .optional()
    .or(z.literal('')),
  is_active: z.boolean().default(true),
  // User binding field
  user_id: z.string().uuid().nullable().optional()
    .or(z.literal(null))
    .refine((val) => val === null || val === undefined || z.string().uuid().safeParse(val).success, {
      message: 'Please select a valid user'
    }),
  // Photo upload fields
  driver_picture_path: z.string().optional().or(z.literal('')),
  driver_picture_mime_type: z.string().optional().or(z.literal('')),
  // Vehicle information fields
  vehicle_head_number: z
    .string()
    .max(50, 'Vehicle head number must be less than 50 characters')
    .regex(
      /^[A-Za-z0-9\u0E00-\u0E7F\s-]+$/,
      'Vehicle head number can only contain letters (English/Thai), numbers, spaces, and hyphens'
    )
    .optional()
    .or(z.literal('')),
  vehicle_tail_number: z
    .string()
    .max(50, 'Vehicle tail number must be less than 50 characters')
    .regex(
      /^[A-Za-z0-9\u0E00-\u0E7F\s-]+$/,
      'Vehicle tail number can only contain letters (English/Thai), numbers, spaces, and hyphens'
    )
    .optional()
    .or(z.literal('')),
})

// Driver filter schema
export const driverFilterSchema = z.object({
  carrier_id: z.string().uuid().optional(),
  is_active: z.boolean().optional(),
})

// Driver search schema
export const driverSearchSchema = z.object({
  search_term: z
    .string()
    .max(100, 'Search term must be less than 100 characters')
    .optional(),
})

// Driver sorting schema
export const driverSortSchema = z.object({
  sort_by: z
    .enum([
      'driver_first_name',
      'driver_last_name',
      'driver_code',
      'phone',
      'is_active',
      'created_at',
      'updated_at',
    ])
    .default('driver_first_name'),
  sort_order: z.enum(['asc', 'desc']).default('asc'),
})

// Combined driver query schema
export const driverQuerySchema = driverFilterSchema
  .merge(driverSearchSchema)
  .merge(driverSortSchema)
  .extend({
    page: z.number().int().min(1).default(1),
    page_size: z.number().int().min(1).max(100).default(20),
  })

// Photo upload validation schema
export const driverPhotoUploadSchema = z.object({
  file: z
    .instanceof(File, { message: 'Please select a valid file' })
    .refine(
      file => file.size <= 2 * 1024 * 1024,
      'File size must be less than 2MB'
    )
    .refine(
      file => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
      'File must be a JPEG, PNG, or WebP image'
    )
    .refine(
      file => file.name.length <= 255,
      'Filename must be less than 255 characters'
    ),
})

// Type exports
export type DriverForm = z.infer<typeof driverFormSchema>
export type DriverFilter = z.infer<typeof driverFilterSchema>
export type DriverSearch = z.infer<typeof driverSearchSchema>
export type DriverSort = z.infer<typeof driverSortSchema>
export type DriverQuery = z.infer<typeof driverQuerySchema>
export type DriverPhotoUpload = z.infer<typeof driverPhotoUploadSchema>

// Validation functions
export const validateDriverForm = (data: unknown) => {
  return driverFormSchema.safeParse(data)
}

export const validateDriverQuery = (data: unknown) => {
  return driverQuerySchema.safeParse(data)
}

export const validateDriverPhotoUpload = (data: unknown) => {
  return driverPhotoUploadSchema.safeParse(data)
}

// Carrier validation helper - ensures only carrier companies are selectable
export const validateCarrierCompany = (companyType: string): boolean => {
  return companyType === 'carrier'
}
