'use client'

import { useEffect, useCallback, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useShipmentStore } from '@/stores/shipment-store'
import { getShipmentService } from '@/lib/services/shipment-service'
import type { ShipmentQueryOptions } from '@/lib/services/shipment-service'
import { useDebounce } from './use-debounce'

/**
 * Custom hook for shipment search and filtering with optimized queries
 * This is separate from the existing use-shipments.ts which handles CRUD operations
 */
export function useShipmentSearch() {
  const supabase = createClient()
  const shipmentService = getShipmentService(supabase)
  
  // Store state
  const {
    shipments,
    isLoading,
    error,
    filters,
    sort,
    pagination,
    setShipments,
    setLoading,
    setError,
    setPagination,
    markUpdated,
  } = useShipmentStore()

  // Debounced search to reduce API calls
  const debouncedSearch = useDebounce(filters.search, 300)

  // Ref to track current request to prevent race conditions
  const currentRequestRef = useRef<number>(0)

  /**
   * Fetch shipments with error handling and loading states
   */
  const searchShipments = useCallback(async (options?: Partial<ShipmentQueryOptions>) => {
    const requestId = ++currentRequestRef.current
    
    try {
      setLoading(true)
      setError(null)

      const queryOptions: ShipmentQueryOptions = {
        filters: { ...filters, search: debouncedSearch },
        sort,
        pagination,
        ...options,
      }

      const result = await shipmentService.getShipments(queryOptions)

      // Check if this is still the latest request
      if (requestId !== currentRequestRef.current) {
        return // Discard outdated results
      }

      if (result.error) {
        setError(result.error)
      } else {
        setShipments(result.data)
        setPagination({
          totalCount: result.totalCount,
          totalPages: Math.ceil(result.totalCount / pagination.pageSize),
        })
        markUpdated()
      }
    } catch (err: any) {
      if (requestId === currentRequestRef.current) {
        console.error('Error searching shipments:', err)
        setError(err.message || 'Failed to search shipments')
      }
    } finally {
      if (requestId === currentRequestRef.current) {
        setLoading(false)
      }
    }
  }, [
    filters,
    debouncedSearch,
    sort,
    pagination.page,
    pagination.pageSize,
    shipmentService,
    setShipments,
    setLoading,
    setError,
    setPagination,
    markUpdated,
  ])

  /**
   * Refresh search results
   */
  const refreshSearch = useCallback(() => {
    searchShipments()
  }, [searchShipments])

  /**
   * Load more results for infinite scroll
   */
  const loadMoreResults = useCallback(async () => {
    if (isLoading || pagination.page >= pagination.totalPages) {
      return
    }

    try {
      setLoading(true)
      
      const nextPageOptions: ShipmentQueryOptions = {
        filters: { ...filters, search: debouncedSearch },
        sort,
        pagination: {
          ...pagination,
          page: pagination.page + 1,
        },
      }

      const result = await shipmentService.getShipments(nextPageOptions)

      if (result.error) {
        setError(result.error)
      } else {
        // Append new data to existing shipments
        setShipments([...shipments, ...result.data])
        setPagination({
          page: pagination.page + 1,
          totalCount: result.totalCount,
          totalPages: Math.ceil(result.totalCount / pagination.pageSize),
        })
        markUpdated()
      }
    } catch (err: any) {
      console.error('Error loading more results:', err)
      setError(err.message || 'Failed to load more results')
    } finally {
      setLoading(false)
    }
  }, [
    isLoading,
    pagination,
    filters,
    debouncedSearch,
    sort,
    shipments,
    shipmentService,
    setShipments,
    setLoading,
    setError,
    setPagination,
    markUpdated,
  ])

  /**
   * Navigate to a specific page (traditional pagination)
   */
  const goToPage = useCallback(async (page: number) => {
    if (isLoading || page < 1 || page > pagination.totalPages || page === pagination.page) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      const pageOptions: ShipmentQueryOptions = {
        filters: { ...filters, search: debouncedSearch },
        sort,
        pagination: {
          ...pagination,
          page,
        },
      }

      const result = await shipmentService.getShipments(pageOptions)

      if (result.error) {
        setError(result.error)
      } else {
        // Replace current shipments with new page data
        setShipments(result.data)
        setPagination({
          page,
          totalCount: result.totalCount,
          totalPages: Math.ceil(result.totalCount / pagination.pageSize),
        })
        markUpdated()
      }
    } catch (err: any) {
      console.error('Error loading page:', err)
      setError(err.message || 'Failed to load page')
    } finally {
      setLoading(false)
    }
  }, [
    isLoading,
    pagination,
    filters,
    debouncedSearch,
    sort,
    shipmentService,
    setShipments,
    setLoading,
    setError,
    setPagination,
    markUpdated,
  ])

  /**
   * Change page size and reload data
   */
  const changePageSize = useCallback(async (newPageSize: number) => {
    if (isLoading || newPageSize === pagination.pageSize) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Calculate what the new page should be to show similar data
      const currentFirstItem = (pagination.page - 1) * pagination.pageSize + 1
      const newPage = Math.ceil(currentFirstItem / newPageSize)

      const pageSizeOptions: ShipmentQueryOptions = {
        filters: { ...filters, search: debouncedSearch },
        sort,
        pagination: {
          page: newPage,
          pageSize: newPageSize,
          totalCount: pagination.totalCount,
          totalPages: Math.ceil(pagination.totalCount / newPageSize),
        },
      }

      const result = await shipmentService.getShipments(pageSizeOptions)

      if (result.error) {
        setError(result.error)
      } else {
        setShipments(result.data)
        setPagination({
          page: newPage,
          pageSize: newPageSize,
          totalCount: result.totalCount,
          totalPages: Math.ceil(result.totalCount / newPageSize),
        })
        markUpdated()
      }
    } catch (err: any) {
      console.error('Error changing page size:', err)
      setError(err.message || 'Failed to change page size')
    } finally {
      setLoading(false)
    }
  }, [
    isLoading,
    pagination,
    filters,
    debouncedSearch,
    sort,
    shipmentService,
    setShipments,
    setLoading,
    setError,
    setPagination,
    markUpdated,
  ])

  /**
   * Go to next page
   */
  const nextPage = useCallback(() => {
    goToPage(pagination.page + 1)
  }, [goToPage, pagination.page])

  /**
   * Go to previous page
   */
  const previousPage = useCallback(() => {
    goToPage(pagination.page - 1)
  }, [goToPage, pagination.page])

  /**
   * Get search statistics
   */
  const getSearchStats = useCallback(async () => {
    try {
      return await shipmentService.getShipmentStats(filters)
    } catch (err: any) {
      console.error('Error fetching search stats:', err)
      return {
        total: 0,
        byStatus: {},
        byTransportMode: {},
        recentCount: 0,
      }
    }
  }, [shipmentService, filters])

  /**
   * Set up real-time subscriptions for shipment updates
   */
  useEffect(() => {
    const subscription = shipmentService.subscribeToShipmentUpdates(
      (payload) => {
        const { eventType, new: newRecord, old: oldRecord } = payload
        
        switch (eventType) {
          case 'INSERT':
            // New shipment created - refresh search to include it if matches filters
            markUpdated()
            if (shipments.length > 0) {
              searchShipments()
            }
            break
            
          case 'UPDATE':
            if (newRecord && oldRecord) {
              // Find and update the shipment in current results
              const existingIndex = shipments.findIndex(s => s.id === newRecord.id)
              if (existingIndex >= 0) {
                // Update existing shipment in place
                const updatedShipments = [...shipments]
                updatedShipments[existingIndex] = newRecord as ShipmentWithRelations
                setShipments(updatedShipments)
              }
              markUpdated()
            }
            break
            
          case 'DELETE':
            if (oldRecord) {
              // Remove shipment from current results
              const filteredShipments = shipments.filter(s => s.id !== oldRecord.id)
              setShipments(filteredShipments)
              // Update pagination count
              setPagination({
                totalCount: pagination.totalCount - 1,
                totalPages: Math.ceil((pagination.totalCount - 1) / pagination.pageSize),
              })
              markUpdated()
            }
            break
        }
      },
      filters
    )

    return () => {
      subscription?.unsubscribe()
    }
  }, [shipmentService, filters, shipments, searchShipments, setShipments, setPagination, pagination.totalCount, pagination.pageSize, markUpdated])

  // Auto-search when dependencies change
  useEffect(() => {
    searchShipments()
  }, [searchShipments])

  return {
    // Data
    shipments,
    isLoading,
    error,
    
    // Pagination info
    pagination,
    hasMore: pagination.page < pagination.totalPages,
    
    // Search actions
    searchShipments,
    refreshSearch,
    loadMoreResults,
    getSearchStats,
    
    // Pagination actions
    goToPage,
    changePageSize,
    nextPage,
    previousPage,
    
    // Computed values
    isEmpty: !isLoading && shipments.length === 0,
    hasError: !!error,
    isFirstLoad: !isLoading && !error && shipments.length === 0,
    hasResults: shipments.length > 0,
    
    // Search context
    searchTerm: debouncedSearch,
    hasActiveFilters: Object.values(filters).some(value => 
      Array.isArray(value) ? value.length > 0 : value !== '' && value !== null
    ),
  }
}

/**
 * Hook for real-time shipment updates during search
 */
export function useShipmentSearchRealtime() {
  const supabase = createClient()
  const shipmentService = getShipmentService(supabase)
  const { updateShipment, markNewUpdates, filters } = useShipmentStore()

  const subscribeToUpdates = useCallback(() => {
    const subscription = shipmentService.subscribeToShipmentUpdates(
      (payload) => {
        const { eventType, new: newRecord, old: oldRecord } = payload

        switch (eventType) {
          case 'INSERT':
            // New shipment created - could refresh search if it matches filters
            markNewUpdates(true)
            break
            
          case 'UPDATE':
            if (newRecord && oldRecord) {
              // Update existing shipment in the list
              updateShipment(newRecord.id, newRecord)
              markNewUpdates(true)
            }
            break
            
          case 'DELETE':
            if (oldRecord) {
              // Remove shipment from the list
              markNewUpdates(true)
            }
            break
        }
      },
      filters
    )

    return subscription
  }, [shipmentService, updateShipment, markNewUpdates, filters])

  return {
    subscribeToUpdates,
  }
}

/**
 * Hook for bulk operations on search results
 */
export function useShipmentSearchBulkOps() {
  const supabase = createClient()
  const shipmentService = getShipmentService(supabase)
  const { 
    selectedShipments, 
    setError,
    deselectAllShipments 
  } = useShipmentStore()
  const { refreshSearch } = useShipmentSearch()

  /**
   * Bulk update selected shipments status
   */
  const bulkUpdateStatus = useCallback(async (newStatus: string) => {
    if (selectedShipments.size === 0) return { success: false, error: 'No shipments selected' }

    try {
      const shipmentIds = Array.from(selectedShipments)
      const result = await shipmentService.bulkUpdateStatus(shipmentIds, newStatus)
      
      if (result.error) {
        setError(result.error)
        return { success: false, error: result.error }
      }

      // Clear selection and refresh results
      deselectAllShipments()
      await refreshSearch()
      
      return { 
        success: true, 
        data: result.data,
        message: `Updated ${shipmentIds.length} shipments to ${newStatus}` 
      }
      
    } catch (err: any) {
      console.error('Error bulk updating shipments:', err)
      const error = err.message || 'Failed to update shipments'
      setError(error)
      return { success: false, error }
    }
  }, [selectedShipments, shipmentService, setError, deselectAllShipments, refreshSearch])

  /**
   * Export selected shipments data
   */
  const exportSelectedShipments = useCallback(async (format: 'csv' | 'excel') => {
    if (selectedShipments.size === 0) return { success: false, error: 'No shipments selected' }

    try {
      const shipmentIds = Array.from(selectedShipments)
      
      // Get full details for selected shipments
      const detailPromises = shipmentIds.map(id => shipmentService.getShipmentById(id))
      const shipmentDetails = await Promise.all(detailPromises)
      
      const validShipments = shipmentDetails.filter(s => s !== null)
      
      // Here you would implement the export logic based on format
      // For now, just return the data
      return {
        success: true,
        data: validShipments,
        format,
        count: validShipments.length,
      }
      
    } catch (err: any) {
      console.error('Error exporting shipments:', err)
      const error = err.message || 'Failed to export shipments'
      setError(error)
      return { success: false, error }
    }
  }, [selectedShipments, shipmentService, setError])

  return {
    // Selection info
    selectedCount: selectedShipments.size,
    hasSelection: selectedShipments.size > 0,
    
    // Bulk actions
    bulkUpdateStatus,
    exportSelectedShipments,
  }
}