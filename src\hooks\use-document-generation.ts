/**
 * Document Generation Hook
 * Story 5.2: Automated Document Generation Engine
 */

import { useState, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { DocumentGenerationService } from '@/lib/services/document-generation-service'
import type {
  DocumentGenerationRequest,
  DocumentGenerationResult,
  BulkDocumentGenerationRequest,
  BulkGenerationResult,
  GenerationProgress,
  GenerationStatus
} from '@/types/document-generation'

export interface UseDocumentGenerationOptions {
  onProgress?: (progress: GenerationProgress) => void
  onComplete?: (result: DocumentGenerationResult | BulkGenerationResult) => void
  onError?: (error: string) => void
}

export interface UseDocumentGenerationReturn {
  generateDocument: (request: DocumentGenerationRequest) => Promise<void>
  bulkGenerateDocuments: (request: BulkDocumentGenerationRequest) => Promise<void>
  isGenerating: boolean
  progress: GenerationProgress | null
  lastResult: DocumentGenerationResult | BulkGenerationResult | null
  error: string | null
  clearError: () => void
  reset: () => void
}

/**
 * Hook for document generation operations
 */
export function useDocumentGeneration(options: UseDocumentGenerationOptions = {}): UseDocumentGenerationReturn {
  const [isGenerating, setIsGenerating] = useState(false)
  const [progress, setProgress] = useState<GenerationProgress | null>(null)
  const [lastResult, setLastResult] = useState<DocumentGenerationResult | BulkGenerationResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Create service instance
  const supabase = createClient()
  const documentService = new DocumentGenerationService(supabase)

  /**
   * Update progress and notify callback
   */
  const updateProgress = useCallback((newProgress: GenerationProgress) => {
    setProgress(newProgress)
    options.onProgress?.(newProgress)
  }, [options])

  /**
   * Generate a single document
   */
  const generateDocument = useCallback(async (request: DocumentGenerationRequest) => {
    try {
      setIsGenerating(true)
      setError(null)
      setLastResult(null)

      // Update progress through generation stages
      updateProgress({
        status: 'preparing',
        progress: 0,
        message: 'Preparing document generation...',
        currentStep: 'Fetching shipment data'
      })

      // Simulate progress updates during generation
      setTimeout(() => {
        updateProgress({
          status: 'generating',
          progress: 25,
          message: 'Processing template...',
          currentStep: 'Populating template fields'
        })
      }, 500)

      setTimeout(() => {
        updateProgress({
          status: 'generating',
          progress: 50,
          message: 'Generating PDF...',
          currentStep: 'Creating PDF document'
        })
      }, 1000)

      setTimeout(() => {
        updateProgress({
          status: 'saving',
          progress: 75,
          message: 'Saving document...',
          currentStep: 'Storing in cloud storage'
        })
      }, 1500)

      // Generate the document
      const result = await documentService.generateDocument(request)

      updateProgress({
        status: 'complete',
        progress: 100,
        message: result.success ? 'Document generated successfully!' : 'Document generation failed',
        currentStep: 'Complete'
      })

      setLastResult(result)
      options.onComplete?.(result)

      if (!result.success) {
        throw new Error(result.error || 'Document generation failed')
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
      
      setError(errorMessage)
      updateProgress({
        status: 'error',
        progress: 0,
        message: 'Document generation failed',
        error: errorMessage
      })
      
      options.onError?.(errorMessage)
    } finally {
      setIsGenerating(false)
    }
  }, [documentService, updateProgress, options])

  /**
   * Generate multiple documents in bulk
   */
  const bulkGenerateDocuments = useCallback(async (request: BulkDocumentGenerationRequest) => {
    try {
      setIsGenerating(true)
      setError(null)
      setLastResult(null)

      updateProgress({
        status: 'preparing',
        progress: 0,
        message: `Preparing bulk generation for ${request.templateIds.length} documents...`,
        currentStep: 'Initializing bulk operation'
      })

      // Generate documents with progress tracking
      const result = await documentService.bulkGenerateDocuments({
        ...request,
        onProgress: (progressPercent) => {
          updateProgress({
            status: 'generating',
            progress: progressPercent,
            message: `Generating documents... (${Math.round(progressPercent)}%)`,
            currentStep: `Processing ${Math.ceil((progressPercent / 100) * request.templateIds.length)} of ${request.templateIds.length} documents`
          })
        }
      })

      updateProgress({
        status: 'complete',
        progress: 100,
        message: `Bulk generation complete! ${result.summary.successful} successful, ${result.summary.failed} failed`,
        currentStep: 'Complete'
      })

      setLastResult(result)
      options.onComplete?.(result)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
      
      setError(errorMessage)
      updateProgress({
        status: 'error',
        progress: 0,
        message: 'Bulk generation failed',
        error: errorMessage
      })
      
      options.onError?.(errorMessage)
    } finally {
      setIsGenerating(false)
    }
  }, [documentService, updateProgress, options])

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  /**
   * Reset all state
   */
  const reset = useCallback(() => {
    setIsGenerating(false)
    setProgress(null)
    setLastResult(null)
    setError(null)
  }, [])

  return {
    generateDocument,
    bulkGenerateDocuments,
    isGenerating,
    progress,
    lastResult,
    error,
    clearError,
    reset
  }
}