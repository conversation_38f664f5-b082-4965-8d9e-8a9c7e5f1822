import { z } from 'zod'
import type { UserRole } from '@/lib/supabase/auth'

// Enhanced email validation with business domain restrictions
const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address')
  .max(255, 'Email address is too long')
  .refine(
    email => {
      // Basic email format validation
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      return emailRegex.test(email)
    },
    {
      message: 'Please enter a valid email address format',
    }
  )
  .refine(
    email => {
      // Block common disposable email domains
      const disposableDomains = [
        '10minutemail.com',
        'tempmail.org',
        'guerrillamail.com',
        'mailinator.com',
        'temp-mail.org',
      ]
      const domain = email.split('@')[1]?.toLowerCase()
      return !disposableDomains.includes(domain)
    },
    {
      message: 'Disposable email addresses are not allowed',
    }
  )

// Enhanced password validation with comprehensive security rules
const passwordSchema = z
  .string()
  .min(1, 'Password is required')
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password must be less than 128 characters')
  .refine(
    password => /(?=.*[a-z])/.test(password),
    'Password must contain at least one lowercase letter'
  )
  .refine(
    password => /(?=.*[A-Z])/.test(password),
    'Password must contain at least one uppercase letter'
  )
  .refine(
    password => /(?=.*\d)/.test(password),
    'Password must contain at least one number'
  )
  .refine(
    password => /(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(password),
    'Password must contain at least one special character'
  )
  .refine(
    password => !/(.)\1{2,}/.test(password),
    'Password cannot contain more than 2 consecutive identical characters'
  )
  .refine(
    password => !/^(.{1,2})\1+$/.test(password),
    'Password cannot be a repeated pattern'
  )
  .refine(password => {
    // Common password blacklist
    const commonPasswords = [
      'password',
      '********',
      'qwerty123',
      'admin123',
      'welcome123',
      'password123',
    ]
    return !commonPasswords.includes(password.toLowerCase())
  }, 'Password is too common, please choose a stronger password')

// User roles enum schema
export const userRoleSchema = z.enum([
  'admin',
  'cs',
  'account',
  'customer',
  'carrier',
  'driver',
  'factory',
  'shipper',
  'consignee',
  'notify_party',
  'forwarder_agent',
])

// Enhanced login form validation schema
export const loginSchema = z.object({
  email: emailSchema,
  password: z
    .string()
    .min(1, 'Password is required')
    .min(8, 'Password must be at least 8 characters long'),
  rememberMe: z.boolean().optional(),
})

// Phone authentication schema
export const phoneAuthSchema = z.object({
  phone: z
    .string()
    .min(1, 'Phone number is required')
    .refine(
      val => {
        // International phone number format with country code (must start with +)
        const phoneRegex = /^\+[1-9]\d{1,14}$/
        const cleanPhone = val.replace(/[\s\-\(\)\.]/g, '')
        return phoneRegex.test(cleanPhone)
      },
      {
        message: 'Please enter a valid phone number with country code (e.g., +***********)',
      }
    )
    .refine(
      val => {
        const cleanPhone = val.replace(/[\s\-\(\)\.]/g, '')
        return cleanPhone.length >= 7 && cleanPhone.length <= 15
      },
      {
        message: 'Phone number must be between 7 and 15 digits',
      }
    ),
})

// OTP verification schema  
export const otpVerificationSchema = z.object({
  phone: phoneAuthSchema.shape.phone,
  token: z
    .string()
    .min(1, 'Verification code is required')
    .length(6, 'Verification code must be 6 digits')
    .regex(/^\d{6}$/, 'Verification code must contain only numbers'),
})

// Enhanced name validation
const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .min(2, 'Name must be at least 2 characters long')
  .max(50, 'Name must be less than 50 characters')
  .refine(
    name => /^[a-zA-Z\s\-'\.]+$/.test(name),
    'Name can only contain letters, spaces, hyphens, apostrophes, and periods'
  )
  .refine(
    name => name.trim().length > 0,
    'Name cannot be empty or only whitespace'
  )
  .refine(
    name => !/^\s|\s$/.test(name),
    'Name cannot start or end with whitespace'
  )

// Enhanced phone number validation
const phoneSchema = z
  .string()
  .optional()
  .or(z.literal(''))
  .refine(
    val => {
      if (!val || val === '') return true
      // International phone number format with country code
      const phoneRegex = /^\+?[1-9]\d{1,14}$/
      const cleanPhone = val.replace(/[\s\-\(\)\.]/g, '')
      return phoneRegex.test(cleanPhone)
    },
    {
      message: 'Please enter a valid phone number (e.g., +********90)',
    }
  )
  .refine(
    val => {
      if (!val || val === '') return true
      const cleanPhone = val.replace(/[\s\-\(\)\.]/g, '')
      return cleanPhone.length >= 7 && cleanPhone.length <= 15
    },
    {
      message: 'Phone number must be between 7 and 15 digits',
    }
  )

// Registration form validation schema
export const registerSchema = z
  .object({
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your password'),
    firstName: nameSchema.refine(
      name => name.split(' ').length <= 2,
      'First name should not contain multiple words'
    ),
    lastName: nameSchema.refine(
      name => name.split(' ').length <= 2,
      'Last name should not contain multiple words'
    ),
    phoneNumber: phoneSchema,
    role: userRoleSchema,
    companyId: z
      .string()
      .uuid('Please select a valid company')
      .optional()
      .or(z.literal('')),
    acceptTerms: z
      .boolean()
      .refine(val => val === true, 'You must accept the terms and conditions'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  })
  .refine(
    data => {
      // Validate role-company association
      const staffRoles: UserRole[] = ['admin', 'cs', 'account']

      if (staffRoles.includes(data.role)) {
        // Staff roles don't require company association
        return true
      }

      // Non-staff roles require company association
      return data.companyId && data.companyId.length > 0
    },
    {
      message: 'Company selection is required for this role',
      path: ['companyId'],
    }
  )
  .refine(
    data => {
      // Prevent name similarity to email
      const emailLocalPart = data.email.split('@')[0].toLowerCase()
      const fullName = `${data.firstName}${data.lastName}`
        .toLowerCase()
        .replace(/\s/g, '')
      return (
        !emailLocalPart.includes(fullName) && !fullName.includes(emailLocalPart)
      )
    },
    {
      message: 'Name and email should not be too similar for security reasons',
      path: ['firstName'],
    }
  )

// Password reset request schema
export const passwordResetRequestSchema = z.object({
  email: emailSchema,
})

// Password reset schema
export const passwordResetSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  })

// Change password schema (for authenticated users)
export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: passwordSchema,
    confirmNewPassword: z.string().min(1, 'Please confirm your new password'),
  })
  .refine(data => data.newPassword === data.confirmNewPassword, {
    message: "New passwords don't match",
    path: ['confirmNewPassword'],
  })
  .refine(data => data.currentPassword !== data.newPassword, {
    message: 'New password must be different from current password',
    path: ['newPassword'],
  })

// Social media ID validation
const socialIdSchema = z
  .string()
  .optional()
  .or(z.literal(''))
  .refine(
    val => {
      if (!val || val === '') return true
      return val.length >= 3 && val.length <= 50
    },
    {
      message: 'ID must be between 3 and 50 characters',
    }
  )
  .refine(
    val => {
      if (!val || val === '') return true
      // Allow alphanumeric, underscores, dots, and hyphens
      return /^[a-zA-Z0-9._-]+$/.test(val)
    },
    {
      message:
        'ID can only contain letters, numbers, dots, underscores, and hyphens',
    }
  )

// Profile update schema
export const profileUpdateSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  phoneNumber: phoneSchema,
  lineId: socialIdSchema.refine(
    val => {
      if (!val || val === '') return true
      // Line ID specific validation (usually starts with @ for usernames)
      return /^@?[a-zA-Z0-9._-]+$/.test(val)
    },
    {
      message: 'Line ID format is invalid',
    }
  ),
  wechatId: socialIdSchema.refine(
    val => {
      if (!val || val === '') return true
      // WeChat ID specific validation
      return /^[a-zA-Z][a-zA-Z0-9_-]*$/.test(val)
    },
    {
      message:
        'WeChat ID must start with a letter and contain only letters, numbers, underscores, and hyphens',
    }
  ),
})

// Admin user creation schema
export const adminUserCreateSchema = z
  .object({
    email: emailSchema,
    firstName: nameSchema,
    lastName: nameSchema,
    phoneNumber: phoneSchema,
    lineId: socialIdSchema,
    wechatId: socialIdSchema,
    role: userRoleSchema,
    companyId: z
      .string()
      .uuid('Please select a valid company')
      .optional()
      .or(z.literal('')),
    isActive: z.boolean().default(true),
    sendInviteEmail: z.boolean().default(true),
  })
  .refine(
    data => {
      // Validate role-company association
      const staffRoles: UserRole[] = ['admin', 'cs', 'account']

      if (staffRoles.includes(data.role)) {
        // Staff roles don't require company association
        return true
      }

      // Non-staff roles require company association
      return data.companyId && data.companyId.length > 0
    },
    {
      message: 'Company selection is required for this role',
      path: ['companyId'],
    }
  )

// Admin user update schema
export const adminUserUpdateSchema = z
  .object({
    firstName: nameSchema,
    lastName: nameSchema,
    phoneNumber: phoneSchema,
    lineId: socialIdSchema,
    wechatId: socialIdSchema,
    role: userRoleSchema,
    companyId: z
      .string()
      .uuid('Please select a valid company')
      .optional()
      .or(z.literal('')),
    isActive: z.boolean(),
  })
  .refine(
    data => {
      // Validate role-company association
      const staffRoles: UserRole[] = ['admin', 'cs', 'account']

      if (staffRoles.includes(data.role)) {
        // Staff roles don't require company association
        return true
      }

      // Non-staff roles require company association
      return data.companyId && data.companyId.length > 0
    },
    {
      message: 'Company selection is required for this role',
      path: ['companyId'],
    }
  )

// User activation/deactivation schema
export const userActivationSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  isActive: z.boolean(),
  reason: z
    .string()
    .min(5, 'Please provide a reason (minimum 5 characters)')
    .max(255, 'Reason must be less than 255 characters')
    .optional(),
})

// Rate limiting and security schemas
export const loginAttemptSchema = z.object({
  email: emailSchema,
  ipAddress: z.string().refine(ip => {
    // Basic IP validation (IPv4 and IPv6)
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
    const ipv6Regex = /^[0-9a-fA-F:]+$/
    return ipv4Regex.test(ip) || ipv6Regex.test(ip)
  }, 'Invalid IP address'),
  userAgent: z.string().max(500, 'User agent string too long'),
  timestamp: z.date().default(() => new Date()),
})

// Account verification schema
export const emailVerificationSchema = z.object({
  token: z.string().min(1, 'Verification token is required'),
  email: emailSchema,
})

// Type exports for form data
export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type PasswordResetRequestFormData = z.infer<
  typeof passwordResetRequestSchema
>
export type PasswordResetFormData = z.infer<typeof passwordResetSchema>
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>
export type ProfileUpdateFormData = z.infer<typeof profileUpdateSchema>
export type AdminUserCreateFormData = z.infer<typeof adminUserCreateSchema>
export type AdminUserUpdateFormData = z.infer<typeof adminUserUpdateSchema>
export type UserActivationFormData = z.infer<typeof userActivationSchema>
export type LoginAttemptData = z.infer<typeof loginAttemptSchema>
export type EmailVerificationData = z.infer<typeof emailVerificationSchema>
export type PhoneAuthFormData = z.infer<typeof phoneAuthSchema>
export type OtpVerificationFormData = z.infer<typeof otpVerificationSchema>

// Validation helper functions
export const validateEmail = (email: string) => {
  try {
    emailSchema.parse(email)
    return { isValid: true, error: null }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        error: error.issues[0]?.message || 'Invalid email',
      }
    }
    return { isValid: false, error: 'Invalid email' }
  }
}

export const validatePassword = (password: string) => {
  try {
    passwordSchema.parse(password)
    return { isValid: true, error: null }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        error: error.issues[0]?.message || 'Invalid password',
      }
    }
    return { isValid: false, error: 'Invalid password' }
  }
}

export const validatePhoneNumber = (phone: string) => {
  try {
    phoneSchema.parse(phone)
    return { isValid: true, error: null }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        error: error.issues[0]?.message || 'Invalid phone number',
      }
    }
    return { isValid: false, error: 'Invalid phone number' }
  }
}
