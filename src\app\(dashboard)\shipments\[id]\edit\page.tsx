'use client'

import { useState, useCallback, useEffect, useRef, useMemo } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Ship,
  Factory,
  Building2,
  MapPin,
  Calendar,
  FileText,
  Upload,
  AlertCircle,
  CheckCircle,
  Loader2,
  Clock,
  Package,
  Users,
  Truck,
  ArrowLeft,
  Info,
  AlertTriangle,
  Save,
  Plus,
  Container,
  Trash2,
} from 'lucide-react'

import {
  shipmentCreationSchema,
  type ShipmentCreation,
} from '@/lib/validations/shipment'
import { useCompaniesForDropdown } from '@/hooks/use-companies'
import { usePortsManagement } from '@/hooks/use-ports'
import { useShipmentDetail } from '@/hooks/use-shipment-detail'
import { useShipmentCRUD } from '@/hooks/use-shipments'
import { useContainerCRUD } from '@/hooks/use-containers'
import { useProductsData } from '@/hooks/use-products'
import { createClient } from '@/lib/supabase/client'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { format, parseISO } from 'date-fns'
import { getProductRelationship, analyzeCustomerProducts } from '@/lib/services/customer-product-selection'
import { updateShipmentTotals, calculateProductTotals } from '@/lib/utils/shipment-totals'

interface ShipmentEditPageProps {}

export default function ShipmentEditPage({}: ShipmentEditPageProps) {
  const params = useParams()
  const router = useRouter()
  const shipmentId = params.id as string
  const [error, setError] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showAddProductDialog, setShowAddProductDialog] = useState(false)
  const [selectedProductId, setSelectedProductId] = useState<string>('')
  const [selectedContainerId, setSelectedContainerId] = useState<string>('')
  const [isAddingProduct, setIsAddingProduct] = useState(false)
  const [isDeletingProduct, setIsDeletingProduct] = useState<string | null>(null)
  
  // Track if form has been initialized to prevent multiple resets
  const formInitialized = useRef(false)
  const [isFormReady, setIsFormReady] = useState(false)
  
  // Product calculation state - track values for each product
  const [productValues, setProductValues] = useState<Record<string, {
    quantity: number
    net_weight: number
    gross_weight: number
    unit_price_cif: number
    unit_price_fob: number
    total_value_cif: number
    total_value_fob: number
    total_gross_weight: number
    total_net_weight: number
    quality_grade: string
    shipping_mark: string
    lot_number: string
    mfg_date: string
    expire_date: string
    packaging_type: string
    product_description: string
  }>>({})
  
  // Container state - track values for each container
  const [containerValues, setContainerValues] = useState<Record<string, {
    container_number: string
    container_type: string
    container_size: string
    seal_number: string
    tare_weight: number
    gross_weight: number
    volume: number
    temperature: string
    vent: string
    status: string
  }>>({})
  
  // Customer products state - track which products are related to current customer
  const [customerProductIds, setCustomerProductIds] = useState<Set<string>>(new Set())
  const [isLoadingCustomerProducts, setIsLoadingCustomerProducts] = useState(false)
  
  // Calculate total values for a product using utility function
  const calculateProductTotalsLocal = useCallback((
    quantity: number, 
    net_weight: number, 
    gross_weight: number,
    unit_price_cif: number, 
    unit_price_fob: number
  ) => {
    return calculateProductTotals({
      quantity,
      unit_price_cif,
      unit_price_fob,
      gross_weight,
      net_weight
    })
  }, [])
  
  // Update product values and recalculate totals
  const updateProductValue = useCallback((
    productId: string,
    field: keyof typeof productValues[string],
    value: number | string
  ) => {
    setProductValues(prev => {
      const current = prev[productId] || {
        quantity: 0,
        net_weight: 0,
        gross_weight: 0,
        unit_price_cif: 0,
        unit_price_fob: 0,
        total_value_cif: 0,
        total_value_fob: 0,
        total_gross_weight: 0,
        total_net_weight: 0,
        quality_grade: '',
        shipping_mark: 'N/M',
        lot_number: '',
        mfg_date: '',
        expire_date: '',
        packaging_type: '',
        product_description: ''
      }
      
      const updated = { ...current, [field]: value }
      
      // Only recalculate totals if calculation-related fields changed
      const calculationFields = ['quantity', 'net_weight', 'gross_weight', 'unit_price_cif', 'unit_price_fob']
      if (calculationFields.includes(field as string)) {
        const totals = calculateProductTotalsLocal(
          updated.quantity,
          updated.net_weight,
          updated.gross_weight,
          updated.unit_price_cif,
          updated.unit_price_fob
        )
        return {
          ...prev,
          [productId]: { ...updated, ...totals }
        }
      }
      
      return {
        ...prev,
        [productId]: updated
      }
    })
  }, [calculateProductTotalsLocal])
  
  // Update container values
  const updateContainerValue = useCallback((
    containerId: string,
    field: keyof typeof containerValues[string],
    value: string | number
  ) => {
    setContainerValues(prev => {
      const current = prev[containerId] || {
        container_number: '',
        container_type: '',
        container_size: '',
        seal_number: '',
        tare_weight: 0,
        gross_weight: 0,
        volume: 0,
        temperature: '',
        vent: '',
        status: 'empty'
      }
      
      return {
        ...prev,
        [containerId]: { ...current, [field]: value }
      }
    })
  }, [])

  // Data hooks
  const { companiesByType, loading: companiesLoading, error: companiesError } = useCompaniesForDropdown()
  const { activePorts: ports, loading: portsLoading } = usePortsManagement()
  const { shipment, isLoading, error: shipmentError, refreshShipment } = useShipmentDetail(shipmentId)
  const { updateShipment, updating: isUpdatingShipment } = useShipmentCRUD()
  const { updateContainer } = useContainerCRUD()
  
  // Supabase client for direct product updates
  const supabase = createClient()

  // Product management
  const { activeProducts, loading: productsLoading } = useProductsData()

  // Memoize data arrays and lengths to prevent infinite loops
  const memoizedActiveProducts = useMemo(() => activeProducts, [activeProducts])
  const memoizedPorts = useMemo(() => ports, [ports])
  const companiesCount = useMemo(() => Object.keys(companiesByType).length, [companiesByType])
  const portsCount = useMemo(() => ports.length, [ports])
  const productsCount = useMemo(() => activeProducts.length, [activeProducts])

  // Create prioritized product list - customer products first, then others
  const prioritizedProducts = useMemo(() => {
    if (!memoizedActiveProducts.length) return []
    
    const customerProducts = memoizedActiveProducts.filter(product => 
      customerProductIds.has(product.id)
    )
    const otherProducts = memoizedActiveProducts.filter(product => 
      !customerProductIds.has(product.id)
    )
    
    // Return customer products first, then other products
    return [...customerProducts, ...otherProducts]
  }, [memoizedActiveProducts, customerProductIds])

  // Add product to shipment function with customer_products pre-population
  const addProductToShipment = useCallback(async (productId: string, containerId?: string) => {
    if (!productId || !shipmentId) return

    setIsAddingProduct(true)
    try {
      // Find the selected product
      const selectedProduct = prioritizedProducts.find(p => p.id === productId)
      if (!selectedProduct) {
        throw new Error('Selected product not found')
      }

      // Get customer ID from current shipment
      const customerId = shipment?.customer_id
      if (!customerId) {
        throw new Error('Customer information not available')
      }

      // Initialize default values
      let defaultValues = {
        quantity: 1,
        unit_price_cif: 0,
        unit_price_fob: 0,
        total_value_cif: 0,
        total_value_fob: 0,
        gross_weight: 0,
        net_weight: 0,
        packaging_type: 'Bag',
        shipping_mark: 'N/M',
        lot_number: '',
        quality_grade: '',
      }

      // Try to get customer-product relationship for pre-population
      try {
        const customerProductRelationship = await getProductRelationship(customerId, productId)
        
        if (customerProductRelationship) {
          console.log('Found customer-product relationship:', customerProductRelationship)
          
          // Pre-populate with customer_products data
          const unitPriceCif = customerProductRelationship.unit_price_cif || 0
          const unitPriceFob = customerProductRelationship.unit_price_fob || 0
          const standardQuantity = customerProductRelationship.standard_quantity || defaultValues.quantity
          const netWeightPerPackage = customerProductRelationship.net_weight_per_package || 1
          const grossWeightPerPackage = customerProductRelationship.gross_weight_per_package || netWeightPerPackage
          const packingType = customerProductRelationship.packaging_type || defaultValues.packaging_type
          const qualityGrade = customerProductRelationship.quality_grade || ''
          
          // Calculate total values based on quantity and net weight from customer_products
          defaultValues = {
            ...defaultValues,
            quantity: standardQuantity,
            net_weight: netWeightPerPackage,
            gross_weight: grossWeightPerPackage,
            unit_price_cif: unitPriceCif,
            unit_price_fob: unitPriceFob,
            quality_grade: qualityGrade,
            packaging_type: packingType,
            total_value_cif: standardQuantity * netWeightPerPackage * unitPriceCif,
            total_value_fob: standardQuantity * netWeightPerPackage * unitPriceFob,
            total_net_weight: standardQuantity * netWeightPerPackage,
            total_gross_weight: standardQuantity * grossWeightPerPackage,
          }
          
          console.log('Pre-populating product with customer relationship data:', defaultValues)
        } else {
          console.log('No customer-product relationship found, using default values')
        }
      } catch (relationshipError) {
        console.warn('Could not fetch customer-product relationship:', relationshipError)
        // Continue with default values if relationship fetch fails
      }

      // Determine container ID - use provided containerId or first container from shipment
      let finalContainerId = containerId
      if (!finalContainerId && shipment?.containers && shipment.containers.length > 0) {
        finalContainerId = shipment.containers[0].id
        console.log('No container selected, using first container:', finalContainerId)
      }

      // Create new shipment product record
      const { data: newProduct, error } = await supabase
        .from('shipment_products')
        .insert({
          shipment_id: shipmentId,
          product_id: productId,
          product_description: selectedProduct.name,
          quantity: defaultValues.quantity,
          unit_price_cif: defaultValues.unit_price_cif,
          unit_price_fob: defaultValues.unit_price_fob,
          total_value_cif: defaultValues.total_value_cif,
          total_value_fob: defaultValues.total_value_fob,
          gross_weight: defaultValues.gross_weight,
          net_weight: defaultValues.net_weight,
          total_gross_weight: defaultValues.total_gross_weight || defaultValues.quantity * defaultValues.gross_weight,
          total_net_weight: defaultValues.total_net_weight || defaultValues.quantity * defaultValues.net_weight,
          packaging_type: defaultValues.packaging_type,
          shipping_mark: defaultValues.shipping_mark,
          lot_number: defaultValues.lot_number,
          quality_grade: defaultValues.quality_grade,
          mfg_date: null,
          expire_date: null,
          unit_of_measure_id: selectedProduct.unit_of_measure_id,
          container_id: finalContainerId || null
        })
        .select('*, products(*), units_of_measure(*)')
        .single()

      if (error) {
        throw new Error(`Failed to add product: ${error.message}`)
      }

      console.log('Product added successfully:', newProduct)
      
      // Immediately update productValues state with the new product's values
      if (newProduct) {
        setProductValues(prev => ({
          ...prev,
          [newProduct.id]: {
            quantity: defaultValues.quantity,
            net_weight: defaultValues.net_weight,
            gross_weight: defaultValues.gross_weight,
            unit_price_cif: defaultValues.unit_price_cif,
            unit_price_fob: defaultValues.unit_price_fob,
            total_value_cif: defaultValues.total_value_cif,
            total_value_fob: defaultValues.total_value_fob,
            total_gross_weight: defaultValues.total_gross_weight || defaultValues.quantity * defaultValues.gross_weight,
            total_net_weight: defaultValues.total_net_weight || defaultValues.quantity * defaultValues.net_weight,
            quality_grade: defaultValues.quality_grade,
            shipping_mark: defaultValues.shipping_mark,
            lot_number: defaultValues.lot_number,
            mfg_date: defaultValues.mfg_date,
            expire_date: defaultValues.expire_date,
            packaging_type: defaultValues.packaging_type,
            product_description: newProduct.product_description || defaultValues.product_description || selectedProduct.name,
          }
        }))
      }
      
      // Refresh shipment data to show the new product
      await refreshShipment()
      
      // Update shipment totals after adding product
      await updateShipmentTotals(supabase, shipmentId)
      console.log('✓ Updated shipment totals after adding product')
      
      // Reset dialog state
      setShowAddProductDialog(false)
      setSelectedProductId('')
      setSelectedContainerId('')
      
    } catch (error) {
      console.error('Error adding product:', error)
      setError(error instanceof Error ? error.message : 'Failed to add product to shipment')
    } finally {
      setIsAddingProduct(false)
    }
  }, [shipmentId, prioritizedProducts, supabase, refreshShipment, shipment?.customer_id])

  // Delete product from shipment function
  const deleteProductFromShipment = useCallback(async (productId: string) => {
    if (!productId || !shipmentId) return

    setIsDeletingProduct(productId)
    try {
      // Delete from database
      const { error } = await supabase
        .from('shipment_products')
        .delete()
        .eq('id', productId)

      if (error) {
        throw new Error(`Failed to delete product: ${error.message}`)
      }

      console.log('Product deleted successfully')
      
      // Remove from productValues state
      setProductValues(prev => {
        const updated = { ...prev }
        delete updated[productId]
        return updated
      })
      
      // Refresh shipment data to reflect the deletion
      await refreshShipment()
      
      // Update shipment totals after deleting product
      await updateShipmentTotals(supabase, shipmentId)
      console.log('✓ Updated shipment totals after deleting product')
      
    } catch (error) {
      console.error('Error deleting product:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete product from shipment')
    } finally {
      setIsDeletingProduct(null)
    }
  }, [shipmentId, supabase, refreshShipment])

  // Filter companies by type (memoized to prevent infinite loops)
  const customers = useMemo(() => companiesByType.customer || [], [companiesByType.customer])
  const factories = useMemo(() => companiesByType.factory || [], [companiesByType.factory])
  const forwarderAgents = useMemo(() => companiesByType.forwarder_agent || [], [companiesByType.forwarder_agent])

  // Create a custom validation schema for editing that removes future date restrictions
  const formValidationSchema = shipmentCreationSchema
    .omit({ shipment_number: true })
    .extend({
      // Override the date fields to remove future validation requirements
      etd_date: z
        .string()
        .min(1, 'ETD date is required'),
      eta_date: z
        .string()
        .min(1, 'ETA date is required'), 
      closing_time: z
        .string()
        .min(1, 'Closing time is required'),
    })
    // Keep the date sequence validation but remove future date checks
    .refine(
      data => {
        // Date sequence validation: Closing Time < ETD < ETA
        if (!data.closing_time || !data.etd_date || !data.eta_date) {
          return false
        }

        const closingTime = new Date(data.closing_time)
        const etdDate = new Date(data.etd_date)
        const etaDate = new Date(data.eta_date)

        return closingTime < etdDate && etdDate < etaDate
      },
      {
        message: 'Date sequence must be: Closing Time < ETD < ETA',
        path: ['eta_date'],
      }
    )
    // Keep the origin/destination port validation
    .refine(
      data => {
        // Origin and destination ports cannot be the same
        return data.origin_port_id !== data.destination_port_id
      },
      {
        message: 'Origin and destination ports cannot be the same',
        path: ['destination_port_id'],
      }
    )

  const form = useForm<Omit<ShipmentCreation, 'shipment_number'>>({
    resolver: zodResolver(formValidationSchema),
    defaultValues: {
      transportation_mode: 'sea',
      customer_id: '',
      factory_id: '',
      forwarder_agent_id: '',
      shipper_id: '',
      consignee_id: '',
      notify_party_id: '',
      origin_port_id: '',
      destination_port_id: '',
      etd_date: '',
      eta_date: '',
      closing_time: '',
      cy_date: '',
      liner: '',
      vessel_name: '',
      voyage_number: '',
      booking_number: '',
      invoice_number: '',
      number_of_pallet: 0,
      pallet_description: '',
      total_weight: 0,
      total_volume: 0,
      total_value_cif: 0,
      total_value_fob: 0,
      ephyto_refno: '',
      currency_code: 'USD',
      currency_code_fob: 'USD',
      status: 'booking_confirmed',
      notes: '',
    },
  })

  // Format date helpers
  const formatDateForInput = (dateString: string | null, type: 'date' | 'datetime-local' = 'date') => {
    if (!dateString) return ''
    try {
      const date = parseISO(dateString)
      if (type === 'datetime-local') {
        return format(date, "yyyy-MM-dd'T'HH:mm")
      }
      return format(date, 'yyyy-MM-dd')
    } catch (error) {
      console.error('Error formatting date:', error)
      return ''
    }
  }

  // Initialize product values when shipment loads
  useEffect(() => {
    if (shipment?.shipment_products && !isLoading) {
      const initialValues: Record<string, any> = {}
      
      shipment.shipment_products.forEach(product => {
        initialValues[product.id] = {
          quantity: product.quantity || 0,
          net_weight: product.net_weight || 0,
          gross_weight: product.gross_weight || 0,
          unit_price_cif: product.unit_price_cif || 0,
          unit_price_fob: product.unit_price_fob || 0,
          total_value_cif: product.total_value_cif || 0,
          total_value_fob: product.total_value_fob || 0,
          total_gross_weight: product.total_gross_weight || (product.quantity * product.gross_weight) || 0,
          total_net_weight: product.total_net_weight || (product.quantity * product.net_weight) || 0,
          quality_grade: product.quality_grade || '',
          shipping_mark: product.shipping_mark || 'N/M',
          lot_number: product.lot_number || '',
          mfg_date: product.mfg_date || '',
          expire_date: product.expire_date || '',
          packaging_type: product.packaging_type || '',
          product_description: product.product_description || ''
        }
      })
      
      setProductValues(initialValues)
    }
  }, [shipment?.shipment_products, isLoading])
  
  // Initialize container values when shipment loads
  useEffect(() => {
    if (shipment?.containers && !isLoading) {
      const initialValues: Record<string, any> = {}
      
      shipment.containers.forEach(container => {
        initialValues[container.id] = {
          container_number: container.container_number || '',
          container_type: container.container_type || '',
          container_size: container.container_size || '',
          seal_number: container.seal_number || '',
          tare_weight: container.tare_weight || 0,
          gross_weight: container.gross_weight || 0,
          volume: container.volume || 0,
          temperature: container.temperature || '',
          vent: container.vent || '',
          status: container.status || 'empty'
        }
      })
      
      setContainerValues(initialValues)
    }
  }, [shipment?.containers, isLoading])

  // Load customer products when shipment customer is available
  useEffect(() => {
    const loadCustomerProducts = async () => {
      if (!shipment?.customer_id || isLoadingCustomerProducts) return

      setIsLoadingCustomerProducts(true)
      try {
        const result = await analyzeCustomerProducts({
          customerId: shipment.customer_id,
          autoSelectSingle: false,
          preferDefaults: false,
          includeInactive: false
        })

        // Extract product IDs that are related to this customer
        const relatedProductIds = new Set(
          result.products.map(product => product.product_id)
        )
        
        console.log('Customer related product IDs:', relatedProductIds)
        setCustomerProductIds(relatedProductIds)
      } catch (error) {
        console.error('Error loading customer products:', error)
        setCustomerProductIds(new Set()) // Clear on error
      } finally {
        setIsLoadingCustomerProducts(false)
      }
    }

    loadCustomerProducts()
  }, [shipment?.customer_id])

  // Load shipment data into form when both shipment and dropdown data are available
  useEffect(() => {
    console.log('Form loading state check:', {
      hasShipment: !!shipment,
      isLoading,
      companiesLoading,
      portsLoading,
      productsLoading,
      isAddingProduct,
      companiesCount,
      portsCount,
      productsCount,
      formInitialized: formInitialized.current,
      isFormReady
    })
    
    // Prevent form reinitialization during product addition or if already ready
    if (isAddingProduct || formInitialized.current) {
      return
    }
    
    // Wait for ALL required data to be loaded before initializing form
    const isDataReady = shipment && 
                       !isLoading && 
                       !companiesLoading && 
                       !portsLoading &&
                       !productsLoading &&
                       companiesCount > 0 && 
                       portsCount > 0 &&
                       productsCount > 0
  
    if (isDataReady) {
      console.log('All data ready - Loading shipment data into form:', shipment)
      
      const formData = {
        transportation_mode: shipment.transportation_mode as any || 'sea',
        customer_id: shipment.customer_id || '',
        factory_id: shipment.factory_id || '',
        forwarder_agent_id: shipment.forwarder_agent_id || '',
        shipper_id: shipment.shipper_id || '',
        consignee_id: shipment.consignee_id || '',
        notify_party_id: shipment.notify_party_id || '',
        origin_port_id: shipment.origin_port_id || '',
        destination_port_id: shipment.destination_port_id || '',
        etd_date: formatDateForInput(shipment.etd_date, 'date'),
        eta_date: formatDateForInput(shipment.eta_date, 'date'),
        closing_time: formatDateForInput(shipment.closing_time, 'datetime-local'),
        cy_date: formatDateForInput(shipment.cy_date, 'datetime-local'),
        liner: shipment.liner || '',
        vessel_name: shipment.vessel_name || '',
        voyage_number: shipment.voyage_number || '',
        booking_number: shipment.booking_number || '',
        invoice_number: shipment.invoice_number || '',
        number_of_pallet: shipment.number_of_pallet || 0,
        pallet_description: shipment.pallet_description || '',
        total_weight: shipment.total_weight || 0,
        total_volume: shipment.total_volume || 0,
        total_value_cif: shipment.total_value_cif || 0,
        total_value_fob: shipment.total_value_fob || 0,
        ephyto_refno: shipment.ephyto_refno || '',
        currency_code: shipment.currency_code || 'USD',
        currency_code_fob: shipment.currency_code_fob || 'USD',
        status: shipment.status as any || 'booking_confirmed',
        notes: shipment.notes || '',
      }
      
      // Validate that the selected values exist in dropdown options
      const validateAndSetFormData = () => {
        const validatedData = { ...formData }
        
        // Validate customer exists
        if (validatedData.customer_id && !customers.find(c => c.id === validatedData.customer_id)) {
          console.warn('Customer ID not found in options:', validatedData.customer_id)
          validatedData.customer_id = ''
        }
        
        // Validate factory exists
        if (validatedData.factory_id && !factories.find(f => f.id === validatedData.factory_id)) {
          console.warn('Factory ID not found in options:', validatedData.factory_id)
          validatedData.factory_id = ''
        }
        
        // Validate forwarder agent exists
        if (validatedData.forwarder_agent_id && !forwarderAgents.find(fa => fa.id === validatedData.forwarder_agent_id)) {
          console.warn('Forwarder agent ID not found in options:', validatedData.forwarder_agent_id)
          validatedData.forwarder_agent_id = ''
        }
        
        // Validate ports exist
        if (validatedData.origin_port_id && !memoizedPorts.find(p => p.id === validatedData.origin_port_id)) {
          console.warn('Origin port ID not found in options:', validatedData.origin_port_id)
          validatedData.origin_port_id = ''
        }
        
        if (validatedData.destination_port_id && !memoizedPorts.find(p => p.id === validatedData.destination_port_id)) {
          console.warn('Destination port ID not found in options:', validatedData.destination_port_id)
          validatedData.destination_port_id = ''
        }
        
        return validatedData
      }
      
      const validatedFormData = validateAndSetFormData()
      console.log('Setting validated form data:', validatedFormData)
      
      // Use setTimeout to ensure DOM is ready
      setTimeout(() => {
        form.reset(validatedFormData)
        formInitialized.current = true
        setIsFormReady(true)
        
        // Double-check form values after a short delay
        setTimeout(() => {
          const currentValues = form.getValues()
          console.log('Form values after reset:', currentValues)
          
          // Force re-render if values don't match
          if (JSON.stringify(currentValues) !== JSON.stringify(validatedFormData)) {
            console.log('Form values mismatch, forcing update')
            Object.entries(validatedFormData).forEach(([key, value]) => {
              form.setValue(key as any, value)
            })
          }
        }, 100)
      }, 50)
    }
  }, [shipment, isLoading, companiesLoading, portsLoading, productsLoading, isAddingProduct, companiesCount, portsCount, productsCount, customers, factories, forwarderAgents, memoizedPorts])

  // Reset form initialization flag when shipment ID changes
  useEffect(() => {
    formInitialized.current = false
    setIsFormReady(false)
    console.log('Shipment ID changed, resetting form initialization flags')
  }, [shipmentId])

  // Form submission
  const handleSubmit = async (
    data: Omit<ShipmentCreation, 'shipment_number'>
  ) => {
    console.log('Form submission started with data:', data)

    try {
      setIsSubmitting(true)
      setError(null)

      // Convert date formats for database
      const convertToISODateTime = (dateTimeLocal: string) => {
        if (!dateTimeLocal || dateTimeLocal.trim() === '') {
          return null
        }
        try {
          return new Date(dateTimeLocal).toISOString()
        } catch (error) {
          console.error(`Invalid datetime format: ${dateTimeLocal}`, error)
          return null
        }
      }

      const convertDateToISOWithMidnight = (dateOnly: string) => {
        if (!dateOnly || dateOnly.trim() === '') {
          return null
        }
        try {
          const dateObj = new Date(dateOnly + 'T00:00:00.000Z')
          return dateObj.toISOString()
        } catch (error) {
          console.error(`Invalid date format: ${dateOnly}`, error)
          return null
        }
      }

      // Prepare update data
      const updateData = {
        ...data,
        etd_date: convertDateToISOWithMidnight(data.etd_date),
        eta_date: convertDateToISOWithMidnight(data.eta_date),
        closing_time: convertToISODateTime(data.closing_time),
        cy_date: convertToISODateTime(data.cy_date),
        
        // Handle nullable fields
        shipper_id: data.shipper_id || null,
        consignee_id: data.consignee_id || null,
        notify_party_id: data.notify_party_id || null,
        liner: data.liner || null,
        vessel_name: data.vessel_name || null,
        voyage_number: data.voyage_number || null,
        booking_number: data.booking_number || null,
        invoice_number: data.invoice_number || null,
        ephyto_refno: data.ephyto_refno || null,
        notes: data.notes || null,
        pallet_description: data.pallet_description || null,
      }

      console.log('Updating shipment with data:', updateData)
      await updateShipment(shipmentId, updateData)

      console.log('Shipment updated successfully')
      
      // Update containers
      if (shipment?.containers) {
        console.log('Updating containers...')
        const containerUpdates = shipment.containers.map(async (container) => {
          const updatedValues = containerValues[container.id]
          if (updatedValues) {
            return updateContainer(container.id, {
              container_number: updatedValues.container_number || null,
              container_type: updatedValues.container_type as any || null,
              container_size: updatedValues.container_size as any || null,
              seal_number: updatedValues.seal_number || null,
              tare_weight: updatedValues.tare_weight || null,
              gross_weight: updatedValues.gross_weight || null,
              volume: updatedValues.volume || null,
              temperature: updatedValues.temperature || null,
              vent: updatedValues.vent || null,
              status: updatedValues.status as any || 'empty',
            })
          }
        })
        
        await Promise.all(containerUpdates.filter(Boolean))
        console.log('Containers updated successfully')
      }
      
      // Update shipment products
      if (shipment?.shipment_products) {
        console.log('Updating products...')
        const productUpdates = shipment.shipment_products.map(async (product) => {
          const updatedValues = productValues[product.id]
          if (updatedValues) {
            const { error: productError } = await supabase
              .from('shipment_products')
              .update({
                quantity: updatedValues.quantity,
                net_weight: updatedValues.net_weight,
                gross_weight: updatedValues.gross_weight,
                unit_price_cif: updatedValues.unit_price_cif,
                unit_price_fob: updatedValues.unit_price_fob,
                total_value_cif: updatedValues.total_value_cif,
                total_value_fob: updatedValues.total_value_fob,
                total_gross_weight: updatedValues.total_gross_weight,
                total_net_weight: updatedValues.total_net_weight,
                quality_grade: updatedValues.quality_grade || null,
                shipping_mark: updatedValues.shipping_mark || null,
                lot_number: updatedValues.lot_number || null,
                mfg_date: updatedValues.mfg_date || null,
                expire_date: updatedValues.expire_date || null,
                packaging_type: updatedValues.packaging_type || null,
                product_description: updatedValues.product_description || null,
                updated_at: new Date().toISOString(),
              })
              .eq('id', product.id)
              
            if (productError) {
              console.error('Error updating product:', productError)
              throw productError
            }
          }
        })
        
        await Promise.all(productUpdates.filter(Boolean))
        console.log('Products updated successfully')
        
        // Update shipment totals after updating products
        await updateShipmentTotals(supabase, shipmentId)
        console.log('✓ Updated shipment totals after product updates')
      }
      
      console.log('All updates completed successfully')
      
      // Navigate back to shipment detail page
      router.push(`/shipments/${shipmentId}`)
      
    } catch (error) {
      console.error('Form submission error:', error)
      
      let errorMessage = 'Failed to update shipment. Please try again.'
      
      if (error instanceof Error) {
        errorMessage = error.message
      }
      
      setError(errorMessage)
      
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading || companiesLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-slate-300">Loading shipment details...</p>
        </div>
      </div>
    )
  }

  if (shipmentError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/shipments')}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Shipments
          </Button>
        </div>

        <Alert className="bg-red-900/20 border-red-500">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-red-200">
            {shipmentError}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!shipment) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/shipments')}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Shipments
          </Button>
        </div>

        <Alert className="bg-yellow-900/20 border-yellow-500">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-yellow-200">
            Shipment not found
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/shipments/${shipmentId}`)}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Detail
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold text-white">Edit Shipment</h1>
            <p className="text-slate-400 mt-1">
              {shipment.shipment_number} - Update shipment information
            </p>
          </div>
        </div>
        
        <Badge
          variant="outline"
          className="bg-orange-600/20 text-orange-200 border-orange-400"
        >
          {shipment.transportation_mode?.toUpperCase()}
        </Badge>
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Form */}
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-8"
        >
          {/* Stakeholder Information */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-blue-200 flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                Stakeholder Information
              </CardTitle>
              <CardDescription className="text-slate-400">
                Update the key stakeholders for this shipment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Customer Selection */}
                <FormField
                  control={form.control}
                  name="customer_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Customer *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select customer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {customers.map(customer => (
                            <SelectItem
                              key={customer.id}
                              value={customer.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Building2 className="h-3 w-3 text-blue-500" />
                                <span>{customer.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Factory Selection */}
                <FormField
                  control={form.control}
                  name="factory_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Factory *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select factory" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {factories.map(factory => (
                            <SelectItem
                              key={factory.id}
                              value={factory.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Factory className="h-3 w-3 text-purple-500" />
                                <div>
                                  <div>{factory.name}</div>
                                  {factory.contact_phone && (
                                    <div className="text-xs text-slate-400">
                                      Phone: {factory.contact_phone}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Forwarder Agent Selection */}
                <FormField
                  control={form.control}
                  name="forwarder_agent_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Forwarder Agent *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select forwarder agent" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {forwarderAgents.map(agent => (
                            <SelectItem
                              key={agent.id}
                              value={agent.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Truck className="h-3 w-3 text-indigo-500" />
                                <div>
                                  <div>{agent.name}</div>
                                  {agent.contact_phone && (
                                    <div className="text-xs text-slate-400">
                                      Phone: {agent.contact_phone}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Additional Shipment Information */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-purple-200 flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-500" />
                Additional Information
              </CardTitle>
              <CardDescription className="text-slate-400">
                Update currency, pallet, and documentation details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="invoice_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Invoice Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter invoice number"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Currency Code */}
                <FormField
                  control={form.control}
                  name="currency_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Currency Code *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          <SelectItem value="USD" className="text-slate-300 hover:bg-slate-700">USD - US Dollar</SelectItem>
                          <SelectItem value="THB" className="text-slate-300 hover:bg-slate-700">THB - Thai Baht</SelectItem>
                          <SelectItem value="EUR" className="text-slate-300 hover:bg-slate-700">EUR - Euro</SelectItem>
                          <SelectItem value="CNY" className="text-slate-300 hover:bg-slate-700">CNY - Chinese Yuan</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Currency Code FOB */}
                <FormField
                  control={form.control}
                  name="currency_code_fob"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Currency Code FOB *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select FOB currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          <SelectItem value="USD" className="text-slate-300 hover:bg-slate-700">USD - US Dollar</SelectItem>
                          <SelectItem value="THB" className="text-slate-300 hover:bg-slate-700">THB - Thai Baht</SelectItem>
                          <SelectItem value="EUR" className="text-slate-300 hover:bg-slate-700">EUR - Euro</SelectItem>
                          <SelectItem value="CNY" className="text-slate-300 hover:bg-slate-700">CNY - Chinese Yuan</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Number of Pallets */}
                <FormField
                  control={form.control}
                  name="number_of_pallet"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Number of Pallets
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min="0"
                          placeholder="Enter number of pallets"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Pallet Description */}
                <FormField
                  control={form.control}
                  name="pallet_description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Pallet Description
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Enter pallet description and specifications"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[80px]"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />


              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Ephyto Reference Number */}
                <FormField
                  control={form.control}
                  name="ephyto_refno"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Ephyto Reference Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter ephytosanitary reference number"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        Official phytosanitary certificate reference
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Route and Schedule Information */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-green-200 flex items-center gap-2">
                <MapPin className="h-5 w-5 text-green-500" />
                Route & Schedule
              </CardTitle>
              <CardDescription className="text-slate-400">
                Update the routing and timing for this shipment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Port Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="origin_port_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Origin Port *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select origin port" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                          {ports.filter(port => port.port_type === 'origin').map(port => (
                            <SelectItem
                              key={port.id}
                              value={port.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Ship className="h-3 w-3 text-blue-500" />
                                <div>
                                  <div>
                                    {port.port_name} ({port.code})
                                  </div>
                                  <div className="text-xs text-slate-400">
                                    {port.country}
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="destination_port_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Destination Port *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select destination port" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                          {ports.filter(port => port.port_type === 'destination').map(port => (
                            <SelectItem
                              key={port.id}
                              value={port.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Ship className="h-3 w-3 text-green-500" />
                                <div>
                                  <div>
                                    {port.port_name} ({port.code})
                                  </div>
                                  <div className="text-xs text-slate-400">
                                    {port.country}
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Date/Time Fields */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="closing_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Closing Time *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="datetime-local"
                          className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400 text-xs">
                        Document submission deadline
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="etd_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        ETD (Departure) *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="date"
                          className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400 text-xs">
                        Estimated departure date
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="eta_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        ETA (Arrival) *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="date"
                          className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400 text-xs">
                        Estimated arrival date
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* CY Date */}
                <FormField
                  control={form.control}
                  name="cy_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        CY Date
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="datetime-local"
                          className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400 text-xs">
                        Container yard delivery date
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Additional Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="booking_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Booking Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter booking number"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vessel_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Vessel Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter vessel name"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Additional Shipping Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Liner */}
                <FormField
                  control={form.control}
                  name="liner"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Liner/Shipping Line
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter shipping line name"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Voyage Number */}
                <FormField
                  control={form.control}
                  name="voyage_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Voyage Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter voyage number"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

              </div>
            </CardContent>
          </Card>

          {/* Containers Section */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-purple-200 flex items-center gap-2">
                <Package className="h-5 w-5 text-purple-500" />
                Containers ({shipment?.containers?.length || 0})
              </CardTitle>
              <CardDescription className="text-slate-400">
                Edit container information and capacity details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {shipment?.containers && shipment.containers.length > 0 ? (
                <div className="space-y-4">
                  {shipment.containers.map((container, index) => (
                    <div key={container.id} className="p-4 bg-slate-800/50 rounded-lg border border-slate-600">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                          <Label className="text-slate-200">Container Number</Label>
                          <Input
                            value={containerValues[container.id]?.container_number ?? container.container_number ?? ''}
                            onChange={(e) => updateContainerValue(container.id, 'container_number', e.target.value)}
                            placeholder="Enter container number"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Container Type</Label>
                          <Select 
                            value={containerValues[container.id]?.container_type ?? container.container_type ?? ''}
                            onValueChange={(value) => updateContainerValue(container.id, 'container_type', value)}
                          >
                            <SelectTrigger className="mt-1 bg-slate-800 border-slate-600 text-white">
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent className="bg-slate-800 border-slate-700">
                              <SelectItem value="dry">Dry</SelectItem>
                              <SelectItem value="reefer">Reefer</SelectItem>
                              <SelectItem value="open_top">Open Top</SelectItem>
                              <SelectItem value="flat_rack">Flat Rack</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Container Size</Label>
                          <Select 
                            value={containerValues[container.id]?.container_size ?? container.container_size ?? ''}
                            onValueChange={(value) => updateContainerValue(container.id, 'container_size', value)}
                          >
                            <SelectTrigger className="mt-1 bg-slate-800 border-slate-600 text-white">
                              <SelectValue placeholder="Select size" />
                            </SelectTrigger>
                            <SelectContent className="bg-slate-800 border-slate-700">
                              <SelectItem value="20ft">20ft</SelectItem>
                              <SelectItem value="40ft">40ft</SelectItem>
                              <SelectItem value="40hc">40ft HC</SelectItem>
                              <SelectItem value="45ft">45ft</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Seal Number</Label>
                          <Input
                            value={containerValues[container.id]?.seal_number ?? container.seal_number ?? ''}
                            onChange={(e) => updateContainerValue(container.id, 'seal_number', e.target.value)}
                            placeholder="Enter seal number"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Tare Weight (kg)</Label>
                          <Input
                            type="number"
                            value={containerValues[container.id]?.tare_weight ?? container.tare_weight ?? ''}
                            onChange={(e) => updateContainerValue(container.id, 'tare_weight', parseFloat(e.target.value) || 0)}
                            placeholder="0"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Gross Weight (kg)</Label>
                          <Input
                            type="number"
                            value={containerValues[container.id]?.gross_weight ?? container.gross_weight ?? ''}
                            onChange={(e) => updateContainerValue(container.id, 'gross_weight', parseFloat(e.target.value) || 0)}
                            placeholder="0"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Volume (m³)</Label>
                          <Input
                            type="number"
                            step="0.1"
                            value={containerValues[container.id]?.volume ?? container.volume ?? ''}
                            onChange={(e) => updateContainerValue(container.id, 'volume', parseFloat(e.target.value) || 0)}
                            placeholder="0"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Temperature</Label>
                          <Input
                            value={containerValues[container.id]?.temperature ?? container.temperature ?? ''}
                            onChange={(e) => updateContainerValue(container.id, 'temperature', e.target.value)}
                            placeholder="e.g., -18°C to -20°C"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Vent</Label>
                          <Input
                            value={containerValues[container.id]?.vent ?? container.vent ?? ''}
                            onChange={(e) => updateContainerValue(container.id, 'vent', e.target.value)}
                            placeholder="e.g., Open 50%, Closed"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Status</Label>
                          <Select 
                            value={containerValues[container.id]?.status ?? container.status ?? 'empty'}
                            onValueChange={(value) => updateContainerValue(container.id, 'status', value)}
                          >
                            <SelectTrigger className="mt-1 bg-slate-800 border-slate-600 text-white">
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent className="bg-slate-800 border-slate-700">
                              <SelectItem value="empty">Empty</SelectItem>
                              <SelectItem value="loaded">Loaded</SelectItem>
                              <SelectItem value="dispatched">Dispatched</SelectItem>
                              <SelectItem value="delivered">Delivered</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full border-dashed border-slate-500 text-slate-100 hover:text-white hover:border-slate-400 bg-orange-400"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Container
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="h-8 w-8 text-slate-500 mx-auto mb-2" />
                  <p className="text-slate-400 mb-4">No containers found</p>
                  <Button
                    type="button"
                    variant="outline"
                    className="border-dashed border-slate-500 text-slate-400 hover:text-white hover:border-slate-400"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Container
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Shipment Products Section */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-green-200 flex items-center gap-2">
                <Package className="h-5 w-5 text-green-500" />
                Shipment Products ({shipment?.shipment_products?.length || 0})
              </CardTitle>
              <CardDescription className="text-slate-400">
                Edit product quantities, pricing, and quality information
                <div className="mt-2 p-2 bg-slate-900/50 rounded text-xs border-l-2 border-orange-500">
                  <strong className="text-orange-300">Auto-calculation:</strong>
                  <div className="mt-1 space-y-1">
                    <div>Total CIF Value = Quantity × Net Weight × Unit Price CIF</div>
                    <div>Total FOB Value = Quantity × Net Weight × Unit Price FOB</div>
                  </div>
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {shipment?.shipment_products && shipment.shipment_products.length > 0 ? (
                <div className="space-y-4">
                  {shipment.shipment_products.map((product, index) => (
                    <div key={product.id} className="p-4 bg-slate-800/50 rounded-lg border border-slate-600">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex-1">
                          <h4 className="text-white font-medium mb-2">
                            Product: {product.product?.name || 'Unknown Product'}
                          </h4>
                          {product.product?.code && (
                            <p className="text-sm text-slate-400 mb-3">Code: {product.product.code}</p>
                          )}
                          <div>
                            <Label className="text-slate-200 text-sm">Product Description</Label>
                            <Input
                              value={productValues[product.id]?.product_description ?? product.product_description ?? ''}
                              onChange={(e) => updateProductValue(product.id, 'product_description', e.target.value)}
                              placeholder="Enter product description"
                              className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                            />
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Select 
                            value={productValues[product.id]?.packaging_type ?? product.packaging_type ?? ''}
                            onValueChange={(value) => updateProductValue(product.id, 'packaging_type', value)}
                          >
                            <SelectTrigger className="w-40 bg-slate-800 border-slate-600 text-white">
                              <SelectValue placeholder="Package" />
                            </SelectTrigger>
                            <SelectContent className="bg-slate-800 border-slate-700">
                              <SelectItem value="Bag">Bag</SelectItem>
                              <SelectItem value="Plastic Basket">Plastic Basket</SelectItem>
                              <SelectItem value="Carton">Carton</SelectItem>
                            </SelectContent>
                          </Select>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (window.confirm(`Are you sure you want to delete "${product.product?.name || 'this product'}" from the shipment?`)) {
                                deleteProductFromShipment(product.id)
                              }
                            }}
                            disabled={isDeletingProduct === product.id}
                            className="border-red-500 bg-red-600/10 text-red-200 hover:bg-red-600/20 hover:border-red-400"
                          >
                            {isDeletingProduct === product.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                          <Label className="text-slate-200">Quantity</Label>
                          <Input
                            type="number"
                            value={productValues[product.id]?.quantity ?? product.quantity ?? ''}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0
                              updateProductValue(product.id, 'quantity', value)
                            }}
                            placeholder="0"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Net Weight (kg)</Label>
                          <Input
                            type="number"
                            value={productValues[product.id]?.net_weight ?? product.net_weight ?? ''}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0
                              updateProductValue(product.id, 'net_weight', value)
                            }}
                            placeholder="0"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Gross Weight (kg)</Label>
                          <Input
                            type="number"
                            value={productValues[product.id]?.gross_weight ?? product.gross_weight ?? ''}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0
                              updateProductValue(product.id, 'gross_weight', value)
                            }}
                            placeholder="0"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Quality Grade</Label>
                          <Input
                            value={productValues[product.id]?.quality_grade ?? product.quality_grade ?? ''}
                            onChange={(e) => updateProductValue(product.id, 'quality_grade', e.target.value)}
                            placeholder="e.g., Grade A"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
                        <div>
                          <Label className="text-slate-200">Unit Price CIF ({shipment?.currency_code || 'USD'})</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={productValues[product.id]?.unit_price_cif ?? product.unit_price_cif ?? ''}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0
                              updateProductValue(product.id, 'unit_price_cif', value)
                            }}
                            placeholder="0.00"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Unit Price FOB ({shipment?.currency_code_fob || 'USD'})</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={productValues[product.id]?.unit_price_fob ?? product.unit_price_fob ?? ''}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0
                              updateProductValue(product.id, 'unit_price_fob', value)
                            }}
                            placeholder="0.00"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200 flex items-center gap-2">
                            Total CIF Value ({shipment?.currency_code || 'USD'})
                            <Badge variant="outline" className="text-xs border-green-500 text-green-300">
                              Auto-calculated
                            </Badge>
                          </Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={productValues[product.id]?.total_value_cif ?? product.total_value_cif ?? ''}
                            placeholder="0.00"
                            className="mt-1 bg-slate-900 border-slate-600 text-green-300 focus:border-green-500 cursor-not-allowed"
                            readOnly
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200 flex items-center gap-2">
                            Total FOB Value ({shipment?.currency_code_fob || 'USD'})
                            <Badge variant="outline" className="text-xs border-blue-500 text-blue-300">
                              Auto-calculated
                            </Badge>
                          </Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={productValues[product.id]?.total_value_fob ?? product.total_value_fob ?? ''}
                            placeholder="0.00"
                            className="mt-1 bg-slate-900 border-slate-600 text-blue-300 focus:border-blue-500 cursor-not-allowed"
                            readOnly
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4 pt-4 border-t border-slate-600">
                        <div className="col-span-1 md:col-span-2 lg:col-span-4">
                          <Label className="text-slate-200 text-sm font-medium">Total Weights (Auto-calculated)</Label>
                          <p className="text-xs text-slate-400 mt-1">These values are automatically calculated based on quantity and unit weights</p>
                        </div>
                        
                        <div>
                          <Label className="text-slate-200 flex items-center gap-2">
                            Total Net Weight (kg)
                            <Badge variant="outline" className="text-xs border-purple-500 text-purple-300">
                              Auto-calculated
                            </Badge>
                          </Label>
                          <Input
                            type="number"
                            step="0.0001"
                            value={productValues[product.id]?.total_net_weight ?? product.total_net_weight ?? (productValues[product.id]?.quantity || product.quantity || 0) * (productValues[product.id]?.net_weight || product.net_weight || 0)}
                            placeholder="0.0000"
                            className="mt-1 bg-slate-900 border-slate-600 text-purple-300 focus:border-purple-500 cursor-not-allowed"
                            readOnly
                          />
                          <p className="text-xs text-slate-500 mt-1">
                            {(productValues[product.id]?.quantity || product.quantity || 0).toLocaleString()} × {(productValues[product.id]?.net_weight || product.net_weight || 0).toLocaleString()} kg
                          </p>
                        </div>
                        
                        <div>
                          <Label className="text-slate-200 flex items-center gap-2">
                            Total Gross Weight (kg)
                            <Badge variant="outline" className="text-xs border-yellow-500 text-yellow-300">
                              Auto-calculated
                            </Badge>
                          </Label>
                          <Input
                            type="number"
                            step="0.0001"
                            value={productValues[product.id]?.total_gross_weight ?? product.total_gross_weight ?? (productValues[product.id]?.quantity || product.quantity || 0) * (productValues[product.id]?.gross_weight || product.gross_weight || 0)}
                            placeholder="0.0000"
                            className="mt-1 bg-slate-900 border-slate-600 text-yellow-300 focus:border-yellow-500 cursor-not-allowed"
                            readOnly
                          />
                          <p className="text-xs text-slate-500 mt-1">
                            {(productValues[product.id]?.quantity || product.quantity || 0).toLocaleString()} × {(productValues[product.id]?.gross_weight || product.gross_weight || 0).toLocaleString()} kg
                          </p>
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Weight Difference (kg)</Label>
                          <Input
                            type="number"
                            step="0.0001"
                            value={
                              ((productValues[product.id]?.total_gross_weight ?? product.total_gross_weight ?? 
                                ((productValues[product.id]?.quantity || product.quantity || 0) * (productValues[product.id]?.gross_weight || product.gross_weight || 0))) - 
                               (productValues[product.id]?.total_net_weight ?? product.total_net_weight ?? 
                                ((productValues[product.id]?.quantity || product.quantity || 0) * (productValues[product.id]?.net_weight || product.net_weight || 0)))).toFixed(4)
                            }
                            placeholder="0.0000"
                            className="mt-1 bg-slate-900 border-slate-600 text-slate-300 focus:border-slate-500 cursor-not-allowed"
                            readOnly
                          />
                          <p className="text-xs text-slate-500 mt-1">Packaging + handling weight</p>
                        </div>
                        
                        {/* <div>
                          <Label className="text-slate-200">Unit vs Total Weight Ratio</Label>
                          <Input
                            type="text"
                            value={`1:${(productValues[product.id]?.quantity || product.quantity || 0).toLocaleString()}`}
                            className="mt-1 bg-slate-900 border-slate-600 text-slate-300 focus:border-slate-500 cursor-not-allowed"
                            readOnly
                          />
                          <p className="text-xs text-slate-500 mt-1">Unit weight to total weight multiplier</p>
                        </div> */}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <Label className="text-slate-200">Shipping Mark</Label>
                          <Input
                            value={productValues[product.id]?.shipping_mark ?? product.shipping_mark ?? 'N/M'}
                            onChange={(e) => updateProductValue(product.id, 'shipping_mark', e.target.value)}
                            placeholder="Enter shipping mark"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Lot Number</Label>
                          <Input
                            value={productValues[product.id]?.lot_number ?? product.lot_number ?? ''}
                            onChange={(e) => updateProductValue(product.id, 'lot_number', e.target.value)}
                            placeholder="Enter lot number"
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <Label className="text-slate-200">Manufacturing Date</Label>
                          <Input
                            type="date"
                            value={productValues[product.id]?.mfg_date ?? (product.mfg_date ? formatDateForInput(product.mfg_date, 'date') : '')}
                            onChange={(e) => updateProductValue(product.id, 'mfg_date', e.target.value)}
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-slate-200">Expiry Date</Label>
                          <Input
                            type="date"
                            value={productValues[product.id]?.expire_date ?? (product.expire_date ? formatDateForInput(product.expire_date, 'date') : '')}
                            onChange={(e) => updateProductValue(product.id, 'expire_date', e.target.value)}
                            className="mt-1 bg-slate-800 border-slate-600 text-white focus:border-orange-500"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  <Dialog open={showAddProductDialog} onOpenChange={setShowAddProductDialog}>
                    <DialogTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        className="w-full border-dashed border-slate-500 text-slate-100 hover:text-white hover:border-slate-400 bg-orange-400"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Product
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-slate-800 border-slate-700 text-white">
                      <DialogHeader>
                        <DialogTitle className="text-green-200">Add Product to Shipment</DialogTitle>
                        <DialogDescription className="text-slate-400">
                          Select a product to add to this shipment
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label className="text-slate-200">Product</Label>
                          <Select
                            value={selectedProductId}
                            onValueChange={setSelectedProductId}
                          >
                            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500">
                              <SelectValue placeholder="Select a product" />
                            </SelectTrigger>
                            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                              {prioritizedProducts.map(product => {
                                const isCustomerProduct = customerProductIds.has(product.id)
                                return (
                                  <SelectItem
                                    key={product.id}
                                    value={product.id}
                                    className="text-slate-300 hover:bg-slate-700"
                                  >
                                    <div className="flex items-center justify-between w-full">
                                      <div className="flex items-center space-x-2">
                                        <Package className={`h-3 w-3 ${isCustomerProduct ? 'text-orange-500' : 'text-green-500'}`} />
                                        <div>
                                          <div className="flex items-center space-x-2">
                                            <span>{product.name}</span>
                                            {isCustomerProduct && (
                                              <Badge 
                                                variant="secondary" 
                                                className="bg-orange-500/20 text-orange-300 border-orange-500/30 text-xs px-1 py-0"
                                              >
                                                Customer Product
                                              </Badge>
                                            )}
                                          </div>
                                          {product.code && (
                                            <div className="text-xs text-slate-400">Code: {product.code}</div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </SelectItem>
                                )
                              })}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label className="text-slate-200">Container (Optional)</Label>
                          <Select
                            value={selectedContainerId}
                            onValueChange={setSelectedContainerId}
                          >
                            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500">
                              <SelectValue placeholder="Select a container (optional)" />
                            </SelectTrigger>
                            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                              {shipment?.containers && shipment.containers.length > 0 ? (
                                shipment.containers.map(container => (
                                  <SelectItem
                                    key={container.id}
                                    value={container.id}
                                    className="text-slate-300 hover:bg-slate-700"
                                  >
                                    <div className="flex items-center space-x-2">
                                      <Container className="h-3 w-3 text-blue-500" />
                                      <div>
                                        <div>
                                          {container.container_number || 'No Number'} - {container.container_type} ({container.container_size})
                                        </div>
                                        <div className="text-xs text-slate-400">
                                          Status: {container.status}
                                        </div>
                                      </div>
                                    </div>
                                  </SelectItem>
                                ))
                              ) : (
                                <SelectItem value="" disabled className="text-slate-500">
                                  No containers available
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex justify-end space-x-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                              setShowAddProductDialog(false)
                              setSelectedProductId('')
                              setSelectedContainerId('')
                            }}
                            className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600"
                          >
                            Cancel
                          </Button>
                          <Button
                            type="button"
                            onClick={() => addProductToShipment(selectedProductId, selectedContainerId)}
                            disabled={!selectedProductId || isAddingProduct}
                            className="bg-orange-500 hover:bg-orange-600 text-white"
                          >
                            {isAddingProduct && (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            )}
                            <Plus className="h-4 w-4 mr-2" />
                            Add Product
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="h-8 w-8 text-slate-500 mx-auto mb-2" />
                  <p className="text-slate-400 mb-4">No products found</p>
                  <Dialog open={showAddProductDialog} onOpenChange={setShowAddProductDialog}>
                    <DialogTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        className="border-dashed border-slate-500 text-slate-400 hover:text-white hover:border-slate-400"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add First Product
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-slate-800 border-slate-700 text-white">
                      <DialogHeader>
                        <DialogTitle className="text-green-200">Add Product to Shipment</DialogTitle>
                        <DialogDescription className="text-slate-400">
                          Select a product to add to this shipment
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label className="text-slate-200">Product</Label>
                          <Select
                            value={selectedProductId}
                            onValueChange={setSelectedProductId}
                          >
                            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500">
                              <SelectValue placeholder="Select a product" />
                            </SelectTrigger>
                            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                              {prioritizedProducts.map(product => {
                                const isCustomerProduct = customerProductIds.has(product.id)
                                return (
                                  <SelectItem
                                    key={product.id}
                                    value={product.id}
                                    className="text-slate-300 hover:bg-slate-700"
                                  >
                                    <div className="flex items-center justify-between w-full">
                                      <div className="flex items-center space-x-2">
                                        <Package className={`h-3 w-3 ${isCustomerProduct ? 'text-orange-500' : 'text-green-500'}`} />
                                        <div>
                                          <div className="flex items-center space-x-2">
                                            <span>{product.name}</span>
                                            {isCustomerProduct && (
                                              <Badge 
                                                variant="secondary" 
                                                className="bg-orange-500/20 text-orange-300 border-orange-500/30 text-xs px-1 py-0"
                                              >
                                                Customer Product
                                              </Badge>
                                            )}
                                          </div>
                                          {product.code && (
                                            <div className="text-xs text-slate-400">Code: {product.code}</div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </SelectItem>
                                )
                              })}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label className="text-slate-200">Container (Optional)</Label>
                          <Select
                            value={selectedContainerId}
                            onValueChange={setSelectedContainerId}
                          >
                            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500">
                              <SelectValue placeholder="Select a container (optional)" />
                            </SelectTrigger>
                            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                              {shipment?.containers && shipment.containers.length > 0 ? (
                                shipment.containers.map(container => (
                                  <SelectItem
                                    key={container.id}
                                    value={container.id}
                                    className="text-slate-300 hover:bg-slate-700"
                                  >
                                    <div className="flex items-center space-x-2">
                                      <Container className="h-3 w-3 text-blue-500" />
                                      <div>
                                        <div>
                                          {container.container_number || 'No Number'} - {container.container_type} ({container.container_size})
                                        </div>
                                        <div className="text-xs text-slate-400">
                                          Status: {container.status}
                                        </div>
                                      </div>
                                    </div>
                                  </SelectItem>
                                ))
                              ) : (
                                <SelectItem value="" disabled className="text-slate-500">
                                  No containers available
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex justify-end space-x-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                              setShowAddProductDialog(false)
                              setSelectedProductId('')
                              setSelectedContainerId('')
                            }}
                            className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600"
                          >
                            Cancel
                          </Button>
                          <Button
                            type="button"
                            onClick={() => addProductToShipment(selectedProductId, selectedContainerId)}
                            disabled={!selectedProductId || isAddingProduct}
                            className="bg-orange-500 hover:bg-orange-600 text-white"
                          >
                            {isAddingProduct && (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            )}
                            <Plus className="h-4 w-4 mr-2" />
                            Add Product
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notes */}
          <Card className="bg-slate-700 border-slate-600">
            <CardContent className="pt-6">
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Notes and Special Instructions
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter any additional notes or special instructions for stakeholders..."
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[100px]"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      This information will be visible to all stakeholders and
                      included in shipment documentation
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/shipments/${shipmentId}`)}
              disabled={isSubmitting}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || isUpdatingShipment}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              {(isSubmitting || isUpdatingShipment) && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              <Save className="h-4 w-4 mr-2" />
              Update Shipment
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
