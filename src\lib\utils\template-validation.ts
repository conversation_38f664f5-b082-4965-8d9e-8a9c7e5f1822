import type {
  DocumentTemplate,
  DocumentTemplateInsert,
  DocumentTemplateUpdate,
  DocumentType,
  PageSize,
  PageOrientation,
  TemplateValidationResult,
  TemplateValidationError
} from '@/types/document-template'

/**
 * Template validation utilities
 * Story 5.1: Document Template Management System
 */

// Valid document types matching database enum
export const VALID_DOCUMENT_TYPES: DocumentType[] = [
  'booking_confirmation',
  'invoice_fob',
  'invoice_cif',
  'shipping_instruction',
  'bill_of_lading',
  'photo_upload',
  'other'
]

// Valid page sizes matching database constraints
export const VALID_PAGE_SIZES: PageSize[] = ['A4', 'A3', 'Letter', 'Legal', 'A5']

// Valid page orientations matching database constraints
export const VALID_PAGE_ORIENTATIONS: PageOrientation[] = ['portrait', 'landscape']

// Supported languages (ISO format validation pattern)
export const LANGUAGE_PATTERN = /^[a-z]{2}(-[A-Z]{2})?$/

// Supported currency formats
export const VALID_CURRENCY_FORMATS = ['USD', 'EUR', 'GBP', 'THB', 'CNY', 'JPY']

// Supported date formats
export const VALID_DATE_FORMATS = [
  'YYYY-MM-DD',
  'DD/MM/YYYY',
  'MM/DD/YYYY',
  'DD-MM-YYYY',
  'MM-DD-YYYY'
]

// Supported number formats (locale identifiers)
export const VALID_NUMBER_FORMATS = [
  'en-US',
  'en-GB',
  'de-DE',
  'fr-FR',
  'th-TH',
  'zh-CN',
  'ja-JP'
]

/**
 * Validate template name
 */
export function validateTemplateName(name: string): TemplateValidationError[] {
  const errors: TemplateValidationError[] = []

  if (!name || name.trim().length === 0) {
    errors.push({
      field: 'template_name',
      message: 'Template name is required',
      code: 'REQUIRED'
    })
  } else {
    if (name.trim().length < 3) {
      errors.push({
        field: 'template_name',
        message: 'Template name must be at least 3 characters long',
        code: 'MIN_LENGTH'
      })
    }
    
    if (name.trim().length > 255) {
      errors.push({
        field: 'template_name',
        message: 'Template name must be no more than 255 characters long',
        code: 'MAX_LENGTH'
      })
    }

    // Check for invalid characters that might cause issues with file names
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(name)) {
      errors.push({
        field: 'template_name',
        message: 'Template name contains invalid characters',
        code: 'INVALID_CHARACTERS'
      })
    }
  }

  return errors
}

/**
 * Validate document type
 */
export function validateDocumentType(type: string): TemplateValidationError[] {
  const errors: TemplateValidationError[] = []

  if (!type) {
    errors.push({
      field: 'document_type',
      message: 'Document type is required',
      code: 'REQUIRED'
    })
  } else if (!VALID_DOCUMENT_TYPES.includes(type as DocumentType)) {
    errors.push({
      field: 'document_type',
      message: `Invalid document type. Must be one of: ${VALID_DOCUMENT_TYPES.join(', ')}`,
      code: 'INVALID_VALUE'
    })
  }

  return errors
}

/**
 * Validate template content
 */
export function validateTemplateContent(content: string): TemplateValidationError[] {
  const errors: TemplateValidationError[] = []

  if (!content || content.trim().length === 0) {
    errors.push({
      field: 'template_content',
      message: 'Template content is required',
      code: 'REQUIRED'
    })
  } else {
    // Check for potential XSS vulnerabilities in template content
    const suspiciousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /onload\s*=/gi,
      /onerror\s*=/gi,
      /onclick\s*=/gi
    ]

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(content)) {
        errors.push({
          field: 'template_content',
          message: 'Template content contains potentially unsafe code',
          code: 'SECURITY_RISK'
        })
        break
      }
    }

    // Validate template content length (reasonable limit for database storage)
    if (content.length > 1000000) { // 1MB limit
      errors.push({
        field: 'template_content',
        message: 'Template content is too large (maximum 1MB)',
        code: 'MAX_SIZE'
      })
    }
  }

  return errors
}

/**
 * Validate page configuration
 */
export function validatePageConfiguration(
  pageSize: string,
  pageOrientation: string,
  marginTop: number,
  marginBottom: number,
  marginLeft: number,
  marginRight: number
): TemplateValidationError[] {
  const errors: TemplateValidationError[] = []

  // Validate page size
  if (!VALID_PAGE_SIZES.includes(pageSize as PageSize)) {
    errors.push({
      field: 'page_size',
      message: `Invalid page size. Must be one of: ${VALID_PAGE_SIZES.join(', ')}`,
      code: 'INVALID_VALUE'
    })
  }

  // Validate page orientation
  if (!VALID_PAGE_ORIENTATIONS.includes(pageOrientation as PageOrientation)) {
    errors.push({
      field: 'page_orientation',
      message: `Invalid page orientation. Must be one of: ${VALID_PAGE_ORIENTATIONS.join(', ')}`,
      code: 'INVALID_VALUE'
    })
  }

  // Validate margins (must be non-negative and reasonable)
  const margins = [
    { value: marginTop, field: 'margin_top' },
    { value: marginBottom, field: 'margin_bottom' },
    { value: marginLeft, field: 'margin_left' },
    { value: marginRight, field: 'margin_right' }
  ]

  margins.forEach(({ value, field }) => {
    if (typeof value !== 'number' || isNaN(value)) {
      errors.push({
        field,
        message: `${field.replace('_', ' ')} must be a number`,
        code: 'INVALID_TYPE'
      })
    } else {
      if (value < 0) {
        errors.push({
          field,
          message: `${field.replace('_', ' ')} cannot be negative`,
          code: 'NEGATIVE_VALUE'
        })
      } else if (value > 200) { // Reasonable upper limit in mm
        errors.push({
          field,
          message: `${field.replace('_', ' ')} is too large (maximum 200mm)`,
          code: 'MAX_VALUE'
        })
      }
    }
  })

  return errors
}

/**
 * Validate format settings
 */
export function validateFormatSettings(
  language: string,
  currencyFormat: string,
  dateFormat: string,
  numberFormat: string
): TemplateValidationError[] {
  const errors: TemplateValidationError[] = []

  // Validate language code
  if (!LANGUAGE_PATTERN.test(language)) {
    errors.push({
      field: 'language',
      message: 'Language must be in ISO format (e.g., "en" or "en-US")',
      code: 'INVALID_FORMAT'
    })
  }

  // Validate currency format
  if (!VALID_CURRENCY_FORMATS.includes(currencyFormat)) {
    errors.push({
      field: 'currency_format',
      message: `Invalid currency format. Must be one of: ${VALID_CURRENCY_FORMATS.join(', ')}`,
      code: 'INVALID_VALUE'
    })
  }

  // Validate date format
  if (!VALID_DATE_FORMATS.includes(dateFormat)) {
    errors.push({
      field: 'date_format',
      message: `Invalid date format. Must be one of: ${VALID_DATE_FORMATS.join(', ')}`,
      code: 'INVALID_VALUE'
    })
  }

  // Validate number format
  if (!VALID_NUMBER_FORMATS.includes(numberFormat)) {
    errors.push({
      field: 'number_format',
      message: `Invalid number format. Must be one of: ${VALID_NUMBER_FORMATS.join(', ')}`,
      code: 'INVALID_VALUE'
    })
  }

  return errors
}

/**
 * Validate version string
 */
export function validateVersion(version: string): TemplateValidationError[] {
  const errors: TemplateValidationError[] = []

  if (!version || version.trim().length === 0) {
    errors.push({
      field: 'version',
      message: 'Version is required',
      code: 'REQUIRED'
    })
  } else {
    // Version should follow semantic versioning pattern (basic check)
    const versionPattern = /^\d+\.\d+(\.\d+)?(-[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*)?$/
    if (!versionPattern.test(version.trim())) {
      errors.push({
        field: 'version',
        message: 'Version should follow semantic versioning format (e.g., "1.0", "1.2.3", "2.0-beta")',
        code: 'INVALID_FORMAT'
      })
    }
  }

  return errors
}

/**
 * Validate required fields array
 */
export function validateRequiredFields(requiredFields: string[] | null): TemplateValidationError[] {
  const errors: TemplateValidationError[] = []

  if (requiredFields !== null && Array.isArray(requiredFields)) {
    // Check for empty or invalid field names
    requiredFields.forEach((field, index) => {
      if (typeof field !== 'string' || field.trim().length === 0) {
        errors.push({
          field: 'required_fields',
          message: `Required field at index ${index} is invalid`,
          code: 'INVALID_ARRAY_ITEM'
        })
      }
    })

    // Check for duplicates
    const uniqueFields = new Set(requiredFields)
    if (uniqueFields.size !== requiredFields.length) {
      errors.push({
        field: 'required_fields',
        message: 'Required fields array contains duplicates',
        code: 'DUPLICATE_VALUES'
      })
    }
  }

  return errors
}

/**
 * Validate complete template for creation
 */
export function validateTemplateInsert(template: DocumentTemplateInsert): TemplateValidationResult {
  const errors: TemplateValidationError[] = []

  // Validate required fields
  errors.push(...validateTemplateName(template.template_name))
  errors.push(...validateDocumentType(template.document_type))
  errors.push(...validateTemplateContent(template.template_content))

  // Validate optional fields with defaults
  const version = template.version || '1.0'
  const pageSize = template.page_size || 'A4'
  const pageOrientation = template.page_orientation || 'portrait'
  const marginTop = template.margin_top || 20
  const marginBottom = template.margin_bottom || 20
  const marginLeft = template.margin_left || 20
  const marginRight = template.margin_right || 20
  const language = template.language || 'en'
  const currencyFormat = template.currency_format || 'USD'
  const dateFormat = template.date_format || 'YYYY-MM-DD'
  const numberFormat = template.number_format || 'en-US'

  errors.push(...validateVersion(version))
  errors.push(...validatePageConfiguration(pageSize, pageOrientation, marginTop, marginBottom, marginLeft, marginRight))
  errors.push(...validateFormatSettings(language, currencyFormat, dateFormat, numberFormat))
  errors.push(...validateRequiredFields(template.required_fields || null))

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validate template update
 */
export function validateTemplateUpdate(updates: DocumentTemplateUpdate): TemplateValidationResult {
  const errors: TemplateValidationError[] = []

  // Validate only the fields that are being updated
  if (updates.template_name !== undefined) {
    errors.push(...validateTemplateName(updates.template_name))
  }

  if (updates.document_type !== undefined) {
    errors.push(...validateDocumentType(updates.document_type))
  }

  if (updates.template_content !== undefined) {
    errors.push(...validateTemplateContent(updates.template_content))
  }

  if (updates.version !== undefined) {
    errors.push(...validateVersion(updates.version))
  }

  // Validate page configuration if any page-related field is being updated
  if (updates.page_size !== undefined || updates.page_orientation !== undefined ||
      updates.margin_top !== undefined || updates.margin_bottom !== undefined ||
      updates.margin_left !== undefined || updates.margin_right !== undefined) {
    
    // Use existing values as defaults for validation
    const pageSize = updates.page_size || 'A4'
    const pageOrientation = updates.page_orientation || 'portrait'
    const marginTop = updates.margin_top || 20
    const marginBottom = updates.margin_bottom || 20
    const marginLeft = updates.margin_left || 20
    const marginRight = updates.margin_right || 20

    errors.push(...validatePageConfiguration(pageSize, pageOrientation, marginTop, marginBottom, marginLeft, marginRight))
  }

  // Validate format settings if any format-related field is being updated
  if (updates.language !== undefined || updates.currency_format !== undefined ||
      updates.date_format !== undefined || updates.number_format !== undefined) {
    
    const language = updates.language || 'en'
    const currencyFormat = updates.currency_format || 'USD'
    const dateFormat = updates.date_format || 'YYYY-MM-DD'
    const numberFormat = updates.number_format || 'en-US'

    errors.push(...validateFormatSettings(language, currencyFormat, dateFormat, numberFormat))
  }

  if (updates.required_fields !== undefined) {
    errors.push(...validateRequiredFields(updates.required_fields))
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validate template content for placeholders
 */
export function validateTemplatePlaceholders(content: string): TemplateValidationResult {
  const errors: TemplateValidationError[] = []
  const warnings: TemplateValidationError[] = []

  // Find all placeholders in the format {{placeholder_name}}
  const placeholderPattern = /\{\{([^}]*)\}\}/g
  const matches = Array.from(content.matchAll(placeholderPattern))

  if (matches.length === 0) {
    warnings.push({
      field: 'template_content',
      message: 'Template content contains no placeholders. Consider adding placeholders for dynamic data.',
      code: 'NO_PLACEHOLDERS'
    })
  }

  // Check for common placeholder naming issues
  matches.forEach((match, index) => {
    const placeholder = match[1].trim()
    
    if (placeholder.length === 0) {
      errors.push({
        field: 'template_content',
        message: `Empty placeholder found at position ${index + 1}`,
        code: 'EMPTY_PLACEHOLDER'
      })
    } else {
      // Check for invalid characters in placeholder names
      if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(placeholder)) {
        errors.push({
          field: 'template_content',
          message: `Invalid placeholder name "${placeholder}". Use only letters, numbers, underscores, and dots.`,
          code: 'INVALID_PLACEHOLDER_NAME'
        })
      }
    }
  })

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Get document type specific validation rules
 */
export function getDocumentTypeRequiredFields(documentType: DocumentType): string[] {
  const commonFields = ['created_date', 'created_by']
  
  switch (documentType) {
    case 'booking_confirmation':
      return [...commonFields, 'booking_number', 'vessel_name', 'etd_date', 'eta_date']
    
    case 'invoice_fob':
    case 'invoice_cif':
      return [...commonFields, 'invoice_number', 'customer_name', 'total_amount', 'currency']
    
    case 'shipping_instruction':
      return [...commonFields, 'shipment_number', 'vessel_name', 'port_of_loading', 'port_of_discharge']
    
    case 'bill_of_lading':
      return [...commonFields, 'bl_number', 'vessel_name', 'voyage_number', 'shipper', 'consignee']
    
    default:
      return commonFields
  }
}