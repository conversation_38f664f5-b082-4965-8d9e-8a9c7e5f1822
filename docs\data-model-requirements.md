## Data Model Requirements

### Supabase Database Schema

#### Authentication & User Management
```sql
-- Built-in Supabase auth.users table extended with profiles
CREATE TABLE profiles (
  user_id uuid PRIMARY KEY REFERENCES auth.users(id),
  email text UNIQUE NOT NULL,
  first_name text,
  last_name text,
  phone_number text,
  line_id text,
  wechat_id text,
  role role_type NOT NULL,
  company_id uuid REFERENCES companies(id),
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Role-based permissions
CREATE TYPE role_type AS ENUM (
  'admin', 'cs', 'account', 'customer', 
  'carrier', 'driver', 'factory', 'shipper', 
  'consignee', 'notify_party', 'forwarder_agent'
);
```

#### Master Data Tables
```sql
-- HYBRID COMPANIES DESIGN: Base table + Separate info tables for complex types
-- Base companies table with common fields only
CREATE TABLE companies (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  company_type company_type_enum NOT NULL,
  
  -- Common fields that ALL types have
  tax_id text,
  contact_email text,
  contact_phone text,
  contact_fax text,
  contact_person_first_name text,
  contact_person_last_name text,
  
  -- Address with multi-language support and coordinates
  address jsonb, -- {"street": {"th": "...", "en": "..."}, "coordinates": {"lat": 13.7563, "lng": 100.5018}}
  gps_coordinates point, -- Dedicated column for efficient geographic queries (synced via trigger)
  
  -- Simple company types use this for specific data (shipper, consignee, notify_party, forwarder_agent)
  metadata jsonb, -- ONLY for simple company types, complex types use separate tables
  
  -- Common fields
  notes text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Constraints: Complex types should have NULL metadata
  CONSTRAINT valid_metadata_structure CHECK (validate_company_metadata(company_type, metadata))
);

-- Customer-Shipper relationship management
CREATE TABLE customer_shippers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  shipper_id uuid NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Ensure customer is of type customer and shipper is of type shipper
  CONSTRAINT customer_shippers_customer_type_check 
    CHECK (EXISTS (SELECT 1 FROM companies WHERE id = customer_id AND company_type = 'customer')),
  CONSTRAINT customer_shippers_shipper_type_check 
    CHECK (EXISTS (SELECT 1 FROM companies WHERE id = shipper_id AND company_type = 'shipper')),
  
  -- Unique constraint to prevent duplicate relationships
  UNIQUE(customer_id, shipper_id)
);

-- Customer-Product relationship management with detailed product information
CREATE TABLE customer_products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  
  -- Product-specific details for this customer
  customer_product_code text, -- Customer's internal product code
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  
  -- Pricing information (per KG as base unit)
  unit_price_cif numeric(12,4), -- Cost, Insurance, and Freight price per KG
  unit_price_fob numeric(12,4), -- Free on Board price per KG
  currency_code currency_enum DEFAULT 'USD',
  
  -- Physical specifications and packaging
  standard_quantity numeric(10,2), -- Standard order quantity (number of packages)
  unit_of_measure_id uuid REFERENCES units_of_measure(id), -- Base UOM is KG
  gross_weight_per_package numeric(8,4), -- Gross weight per 1 package in KG
  net_weight_per_package numeric(8,4), -- Net weight per 1 package in KG
  
  -- Quality and packaging
  quality_grade text, -- Premium, Grade A, Standard, etc.
  packaging_type packaging_type_enum NOT NULL, -- Type of packaging (box, bag, carton, pallet, etc.) - defines what quantity represents
  packaging_specifications jsonb, -- Flexible storage for packaging details
  
  -- Logistics information
  handling_instructions text,
  temperature_require text,
  vent_require text,
  shelf_life_days integer, -- Expected shelf life in days
  
  -- Documentation
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Ensure customer is of type customer
  CONSTRAINT customer_products_customer_type_check 
    CHECK (EXISTS (SELECT 1 FROM companies WHERE id = customer_id AND company_type = 'customer')),
  
  -- Unique constraint to prevent duplicate relationships
  UNIQUE(customer_id, product_id)
);

-- Consignee-Notify Party relationship management
CREATE TABLE consignee_notify_parties (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  consignee_id uuid NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  notify_party_id uuid NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  
  -- Notification preferences for this relationship
  notification_preferences jsonb, -- {"email": true, "sms": false, "line": true}
  priority_order integer DEFAULT 1, -- Order of notification (1 = primary, 2 = secondary, etc.)
  
  -- Special handling instructions
  special_instructions text,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Ensure consignee is of type consignee and notify_party is of type notify_party
  CONSTRAINT consignee_notify_parties_consignee_type_check 
    CHECK (EXISTS (SELECT 1 FROM companies WHERE id = consignee_id AND company_type = 'consignee')),
  CONSTRAINT consignee_notify_parties_notify_party_type_check 
    CHECK (EXISTS (SELECT 1 FROM companies WHERE id = notify_party_id AND company_type = 'notify_party')),
  
  -- Unique constraint to prevent duplicate relationships
  UNIQUE(consignee_id, notify_party_id)
);

-- COMPLEX COMPANY TYPES - Separate info tables for performance and type safety

-- Customer info table (complex business rules)
CREATE TABLE customer_info (
  company_id uuid PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
  customer_type customer_type_enum NOT NULL DEFAULT 'regular',
  credit_limit numeric(12,2) DEFAULT 0 CHECK (credit_limit >= 0),
  incoterms incoterms_enum,
  special_requirements text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Carrier info table (fleet management, complex logistics data)
CREATE TABLE carrier_info (
  company_id uuid PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
  carrier_code text UNIQUE,
  fleet_size integer DEFAULT 0 CHECK (fleet_size >= 0),
  license_types text[] DEFAULT '{}',
  coverage_areas text[] DEFAULT '{}',
  insurance_policy_no text,
  insurance_expiry_date date,
  insurance_coverage_amount numeric(15,2),
  max_weight_capacity numeric(10,2),
  max_volume_capacity numeric(10,2),
  operating_hours jsonb, -- {"weekdays": "08:00-18:00", "weekends": "09:00-15:00"}
  emergency_contact_phone text,
  gps_tracking_available boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Factory info table (production data, certifications)
CREATE TABLE factory_info (
  company_id uuid PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
  factory_code text UNIQUE NOT NULL,
  license_no text NOT NULL,
  certifications text[] DEFAULT '{}', -- ['HACCP', 'ISO22000', 'GMP']
  production_capacity_tons_per_day integer DEFAULT 0,
  cold_storage_capacity_tons integer DEFAULT 0,
  operating_hours jsonb, -- {"weekdays": "08:00-17:00", "weekends": "closed"}
  specializations text[] DEFAULT '{}', -- ['durian', 'mangosteen', 'longan']
  quality_control_manager text,
  quality_control_phone text,
  loading_dock_count integer DEFAULT 1,
  container_loading_time_minutes integer DEFAULT 120,
  advance_booking_required_hours integer DEFAULT 24,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Ports master data
CREATE TABLE ports (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code text UNIQUE NOT NULL,
  name text NOT NULL,
  country text NOT NULL,
  port_type port_type_enum NOT NULL,
  coordinates point,
  is_active boolean DEFAULT true
);

-- Units of measure reference table
CREATE TABLE units_of_measure (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code text UNIQUE NOT NULL,        -- 'KG', 'PCS', 'LTR', 'MTR', 'TON', 'BOX', 'CTN'
  name text NOT NULL,               -- 'Kilogram', 'Pieces', 'Liter', 'Meter', 'Ton', 'Box', 'Carton'
  category text,                    -- 'weight', 'count', 'volume', 'length'
  symbol text,                      -- 'kg', 'pcs', 'L', 'm', 't', 'box', 'ctn'
  conversion_factor numeric(10,4),  -- For unit conversions (base unit = 1)
  base_unit_id uuid REFERENCES units_of_measure(id), -- Reference to base unit for conversions
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Products catalog
CREATE TABLE products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  code text,
  description text,
  category text,
  hs_code text,
  unit_of_measure_id uuid REFERENCES units_of_measure(id),
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Drivers master data
CREATE TABLE drivers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  carrier_id uuid NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  driver_first_name text NOT NULL,
  driver_last_name text NOT NULL,
  driver_code text,
  phone text,
  line_id text,
  driver_picture_path text,
  driver_picture_mime_type text,
  notes text,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);
```

#### Core Business Entities
```sql
-- Main shipments table
CREATE TABLE shipments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_number text UNIQUE NOT NULL,
  invoice_number text,
  
  -- Stakeholder references
  customer_id uuid REFERENCES companies(id),
  shipper_id uuid REFERENCES companies(id),
  consignee_id uuid REFERENCES companies(id),
  notify_party_id uuid REFERENCES companies(id),
  factory_id uuid REFERENCES companies(id),
  forwarder_agent_id uuid REFERENCES companies(id),
  
  -- Port and vessel information
  origin_port_id uuid REFERENCES ports(id),
  destination_port_id uuid REFERENCES ports(id),
  liner text,
  vessel_name text,
  voyage_number text,
  booking_number text,
  
  -- Dates and times
  etd_date timestamptz,
  eta_date timestamptz,
  closing_time timestamptz,
  cy_date timestamptz,
  
  -- Product Info
  number_of_pallet integer,
  pallet_description text,
  ephyto_refno text,
  currency_code currency_enum DEFAULT 'USD',
  total_weight decimal,
  total_volume decimal,

  -- Status and metadata
  status shipment_status_enum DEFAULT 'booking_confirmed',
  transportation_mode transport_mode_enum DEFAULT 'sea',
  notes text,
  metadata jsonb,
  
  -- Audit fields
  created_by uuid REFERENCES profiles(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Containers associated with shipments
CREATE TABLE containers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  container_number text,
  container_type container_type_enum,
  container_size container_size_enum,
  seal_number text,
  tare_weight decimal,
  gross_weight decimal,
  volume decimal,
  temperature text,
  vent text,
  status container_status_enum DEFAULT 'empty',
  created_at timestamptz DEFAULT now()
);

-- Products within containers
CREATE TABLE shipment_products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  container_id uuid REFERENCES containers(id),
  product_id uuid REFERENCES products(id),
  product_description text,
  quantity decimal NOT NULL, -- Number of packages of the specified packaging_type
  unit_of_measure_id uuid REFERENCES units_of_measure(id), -- Base UOM is KG
  unit_price_cif decimal NOT NULL, -- Cost, Insurance, and Freight price per KG
  unit_price_fob decimal NOT NULL, -- Free on Board price per KG
  total_value_cif decimal NOT NULL,
  total_value_fob decimal NOT NULL,
  gross_weight numeric(18, 4) not null default 0, -- Gross weight per 1 package in KG
  net_weight numeric(18, 4) not null default 0, -- Net weight per 1 package in KG
  shipping_mark text null,
  mfg_date date null,
  expire_date date null,
  lot_number text null,
  packaging_type packaging_type_enum NOT NULL, -- Type of packaging (must match customer_products definition)
  quality_grade text,
  created_at timestamptz DEFAULT now()
);

-- Transportation assignments
CREATE TABLE transportation (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  carrier_id uuid REFERENCES companies(id),
  driver_id uuid REFERENCES profiles(id),
  vehicle_head_number text,
  vehicle_tail_number text,
  driver_phone text,
  assignment_date timestamptz,
  pickup_container_location text,
  pickup_container_gps_coordinates point,
  pickup_product_location text,
  pickup_product_gps_coordinates point,
  delivery_location text,
  delivery_gps_coordinates point,
  notes text,
  estimated_distance decimal,
  created_at timestamptz DEFAULT now()
);
```

#### Status Tracking & Audit Trail
```sql
-- Status history with full audit trail
CREATE TABLE status_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  status_from shipment_status_enum,
  status_to shipment_status_enum NOT NULL,
  notes text,
  location text,
  latitude decimal,
  longitude decimal,
  updated_by uuid REFERENCES profiles(id),
  created_at timestamptz DEFAULT now()
);

-- Image uploads for status updates
CREATE TABLE status_images (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  status_history_id uuid REFERENCES status_history(id),
  image_url text NOT NULL,
  image_path text NOT NULL,
  file_size integer,
  mime_type text,
  metadata jsonb,
  uploaded_by uuid REFERENCES profiles(id),
  created_at timestamptz DEFAULT now()
);
```

#### Document Management
```sql
-- Document storage and metadata
CREATE TABLE documents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  document_type document_type_enum NOT NULL,
  file_name text NOT NULL,
  file_path text NOT NULL,
  file_size integer,
  mime_type text,
  document_version integer DEFAULT 1,
  is_generated boolean DEFAULT false,
  metadata jsonb,
  uploaded_by uuid REFERENCES profiles(id),
  created_at timestamptz DEFAULT now()
);

-- Document templates for generation
CREATE TABLE document_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  document_type document_type_enum NOT NULL,
  template_data jsonb NOT NULL,
  is_active boolean DEFAULT true,
  created_by uuid REFERENCES profiles(id),
  created_at timestamptz DEFAULT now()
);
```

#### Notifications & Communication
```sql
-- Notification preferences and history
CREATE TABLE notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id),
  shipment_id uuid REFERENCES shipments(id),
  notification_type notification_type_enum NOT NULL,
  channel notification_channel_enum NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  is_read boolean DEFAULT false,
  sent_at timestamptz,
  delivery_status text,
  metadata jsonb,
  created_at timestamptz DEFAULT now()
);

-- User notification preferences
CREATE TABLE notification_preferences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id),
  notification_type notification_type_enum NOT NULL,
  email_enabled boolean DEFAULT true,
  sms_enabled boolean DEFAULT false,
  in_app_enabled boolean DEFAULT true,
  line_enabled boolean DEFAULT false,
  wechat_enabled boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  
  UNIQUE(user_id, notification_type)
);
```

### Enums and Types
```sql
CREATE TYPE transport_mode_enum AS ENUM ('sea', 'land', 'rail');
CREATE TYPE company_type_enum AS ENUM ('customer', 'carrier', 'factory', 'shipper', 'consignee', 'notify_party', 'forwarder_agent');
CREATE TYPE port_type_enum AS ENUM ('origin', 'destination', 'transit');
CREATE TYPE container_type_enum AS ENUM ('dry', 'reefer', 'open_top', 'flat_rack', 'tank');
CREATE TYPE container_size_enum AS ENUM ('20ft', '40ft', '40hc', '45ft');
CREATE TYPE shipment_status_enum AS ENUM (
  'booking_confirmed', 'transport_assigned', 'driver_assigned',
  'empty_container_picked', 'arrived_at_factory',
  'loading_started', 'departed_factory', 'container_returned',
  'shipped', 'arrived', 'completed'
);
CREATE TYPE container_status_enum AS ENUM ('empty', 'loaded', 'sealed', 'in_transit', 'delivered');
CREATE TYPE document_type_enum AS ENUM ('booking_confirmation', 'invoice_fob','invoice_cif', 'shipping_instruction', 'bill_of_lading', 'photo_upload', 'other');
CREATE TYPE notification_type_enum AS ENUM ('status_update', 'assignment', 'document_ready', 'delay_alert', 'system');
CREATE TYPE notification_channel_enum AS ENUM ('email', 'sms', 'in_app', 'line', 'wechat');

-- Company-specific enums for hybrid approach
CREATE TYPE customer_type_enum AS ENUM ('regular', 'premium', 'vip');
CREATE TYPE incoterms_enum AS ENUM ('CIF', 'EXW', 'FOB', 'CFR');
CREATE TYPE currency_enum AS ENUM ('THB', 'CNY', 'USD', 'EUR');
CREATE TYPE packaging_type_enum AS ENUM ('Bag', 'Plastic Basket', 'Carton');
```

### Master Data Initialization

#### Units of Measure Sample Data
```sql
-- Insert common units of measure for fruit export business
INSERT INTO units_of_measure (code, name, category, symbol, conversion_factor) VALUES
-- Weight units (base unit: KG) - CRITICAL: KG is the standard UOM for all pricing and weight calculations
('KG', 'Kilogram', 'weight', 'kg', 1.0000),
--('TON', 'Metric Ton', 'weight', 't', 1000.0000),
--('LBS', 'Pounds', 'weight', 'lbs', 0.4536),

-- Count units (base unit: PCS)
--('PCS', 'Pieces', 'count', 'pcs', 1.0000),
--('BOX', 'Box', 'count', 'box', 1.0000),
--('CTN', 'Carton', 'count', 'ctn', 1.0000),
--('PALLET', 'Pallet', 'count', 'plt', 1.0000),
--('BAG', 'Bag', 'count', 'bag', 1.0000),
--('PLASTIC_BASKET', 'Plastic Basket', 'count', 'plsbkt', 1.0000),

-- Volume units (base unit: LTR)
--('LTR', 'Liter', 'volume', 'L', 1.0000),
--('CBM', 'Cubic Meter', 'volume', 'm³', 1000.0000),
--('GAL', 'Gallon', 'volume', 'gal', 3.7854),

-- Length units (base unit: MTR)
--('MTR', 'Meter', 'length', 'm', 1.0000),
--('CM', 'Centimeter', 'length', 'cm', 0.0100),
--('INCH', 'Inch', 'length', 'in', 0.0254);

-- Update base_unit_id for conversion relationships
UPDATE units_of_measure SET base_unit_id = (SELECT id FROM units_of_measure WHERE code = 'KG') WHERE category = 'weight' AND code != 'KG';
UPDATE units_of_measure SET base_unit_id = (SELECT id FROM units_of_measure WHERE code = 'PCS') WHERE category = 'count' AND code != 'PCS';
UPDATE units_of_measure SET base_unit_id = (SELECT id FROM units_of_measure WHERE code = 'LTR') WHERE category = 'volume' AND code != 'LTR';
UPDATE units_of_measure SET base_unit_id = (SELECT id FROM units_of_measure WHERE code = 'MTR') WHERE category = 'length' AND code != 'MTR';
```

### Row Level Security (RLS) Policies
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE units_of_measure ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;
ALTER TABLE containers ENABLE ROW LEVEL SECURITY;
-- ... (enable for all tables)

-- Example RLS policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "CS and Admin can view all shipments" ON shipments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('cs', 'admin')
    )
  );

CREATE POLICY "Customers can view own shipments" ON shipments
  FOR SELECT USING (
    customer_id IN (
      SELECT company_id FROM profiles 
      WHERE id = auth.uid() AND role = 'customer'
    )
  );

-- RLS policies for relationship tables
CREATE POLICY "CS and Admin can manage all customer-shipper relationships" ON customer_shippers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('cs', 'admin')
    )
  );

CREATE POLICY "Customers can view their shipper relationships" ON customer_shippers
  FOR SELECT USING (
    customer_id IN (
      SELECT company_id FROM profiles 
      WHERE id = auth.uid() AND role = 'customer'
    )
  );

CREATE POLICY "CS and Admin can manage all customer-product relationships" ON customer_products
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('cs', 'admin')
    )
  );

CREATE POLICY "Customers can view their product relationships" ON customer_products
  FOR SELECT USING (
    customer_id IN (
      SELECT company_id FROM profiles 
      WHERE id = auth.uid() AND role = 'customer'
    )
  );

CREATE POLICY "CS and Admin can manage all consignee-notify party relationships" ON consignee_notify_parties
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('cs', 'admin')
    )
  );

CREATE POLICY "Related parties can view notify party relationships" ON consignee_notify_parties
  FOR SELECT USING (
    consignee_id IN (
      SELECT company_id FROM profiles 
      WHERE id = auth.uid() AND role IN ('consignee', 'notify_party')
    )
    OR notify_party_id IN (
      SELECT company_id FROM profiles 
      WHERE id = auth.uid() AND role IN ('consignee', 'notify_party')
    )
  );

-- Units of measure policies (reference data - read-only for most users)
CREATE POLICY "All users can view active units of measure" ON units_of_measure
  FOR SELECT USING (is_active = true);

CREATE POLICY "Only admin can modify units of measure" ON units_of_measure
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Products policies
CREATE POLICY "All authenticated users can view active products" ON products
  FOR SELECT USING (is_active = true);

CREATE POLICY "CS and Admin can manage products" ON products
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('cs', 'admin')
    )
  );

-- Drivers policies
CREATE POLICY "CS and Admin can view all drivers" ON drivers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('cs', 'admin')
    )
  );

CREATE POLICY "Carriers can view their own drivers" ON drivers
  FOR SELECT USING (
    carrier_id IN (
      SELECT company_id FROM profiles 
      WHERE id = auth.uid() AND role = 'carrier'
    )
  );

CREATE POLICY "Drivers can view their own profile" ON drivers
  FOR SELECT USING (
    id IN (
      SELECT driver_id FROM profiles 
      WHERE id = auth.uid() AND role = 'driver'
    )
  );

CREATE POLICY "CS and Admin can manage drivers" ON drivers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('cs', 'admin')
    )
  );

CREATE POLICY "Carriers can manage their own drivers" ON drivers
  FOR ALL USING (
    carrier_id IN (
      SELECT company_id FROM profiles 
      WHERE id = auth.uid() AND role = 'carrier'
    )
  );
```

### Database Functions and Triggers
```sql
-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to ensure only one default per customer-shipper relationship
CREATE OR REPLACE FUNCTION ensure_single_default_shipper()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_default = true THEN
    -- Remove default flag from other shippers for this customer
    UPDATE customer_shippers 
    SET is_default = false 
    WHERE customer_id = NEW.customer_id 
      AND id != NEW.id;
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to ensure only one default per customer-product relationship
CREATE OR REPLACE FUNCTION ensure_single_default_product()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_default = true THEN
    -- Remove default flag from other products for this customer
    UPDATE customer_products 
    SET is_default = false 
    WHERE customer_id = NEW.customer_id 
      AND id != NEW.id;
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to ensure only one default per consignee-notify party relationship
CREATE OR REPLACE FUNCTION ensure_single_default_notify_party()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_default = true THEN
    -- Remove default flag from other notify parties for this consignee
    UPDATE consignee_notify_parties 
    SET is_default = false 
    WHERE consignee_id = NEW.consignee_id 
      AND id != NEW.id;
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for automatic timestamp updates on multiple tables
CREATE TRIGGER update_shipments_updated_at 
  BEFORE UPDATE ON shipments 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_products_updated_at 
  BEFORE UPDATE ON products 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_units_of_measure_updated_at 
  BEFORE UPDATE ON units_of_measure 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_drivers_updated_at 
  BEFORE UPDATE ON drivers 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Triggers for relationship tables
CREATE TRIGGER update_customer_shippers_updated_at 
  BEFORE UPDATE ON customer_shippers 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_customer_products_updated_at 
  BEFORE UPDATE ON customer_products 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_consignee_notify_parties_updated_at 
  BEFORE UPDATE ON consignee_notify_parties 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Triggers to ensure only one default per relationship group
CREATE TRIGGER ensure_single_default_shipper_trigger
  BEFORE INSERT OR UPDATE ON customer_shippers
  FOR EACH ROW EXECUTE FUNCTION ensure_single_default_shipper();

CREATE TRIGGER ensure_single_default_product_trigger
  BEFORE INSERT OR UPDATE ON customer_products
  FOR EACH ROW EXECUTE FUNCTION ensure_single_default_product();

CREATE TRIGGER ensure_single_default_notify_party_trigger
  BEFORE INSERT OR UPDATE ON consignee_notify_parties
  FOR EACH ROW EXECUTE FUNCTION ensure_single_default_notify_party();

-- Function to create status history automatically
CREATE OR REPLACE FUNCTION create_status_history()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO status_history (shipment_id, status_from, status_to, updated_by)
    VALUES (NEW.id, OLD.status, NEW.status, auth.uid());
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Hybrid GPS Coordinates Management
-- Function to sync GPS coordinates from JSONB address to point column
CREATE OR REPLACE FUNCTION sync_gps_coordinates()
RETURNS TRIGGER AS $$
BEGIN
  -- Update point column when JSONB coordinates change
  IF NEW.address->'coordinates' IS NOT NULL THEN
    NEW.gps_coordinates = point(
      (NEW.address->'coordinates'->>'lng')::float,
      (NEW.address->'coordinates'->>'lat')::float
    );
  ELSE
    NEW.gps_coordinates = NULL;
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for automatic GPS coordinate synchronization
CREATE TRIGGER trigger_sync_gps_coordinates
  BEFORE INSERT OR UPDATE ON companies
  FOR EACH ROW EXECUTE FUNCTION sync_gps_coordinates();

-- Function to update JSONB coordinates when point column changes directly
CREATE OR REPLACE FUNCTION sync_address_coordinates()
RETURNS TRIGGER AS $$
BEGIN
  -- Update JSONB address when point coordinates change directly
  IF OLD.gps_coordinates IS DISTINCT FROM NEW.gps_coordinates THEN
    IF NEW.gps_coordinates IS NOT NULL THEN
      NEW.address = COALESCE(NEW.address, '{}'::jsonb) || 
        jsonb_build_object('coordinates', 
          jsonb_build_object(
            'lat', NEW.gps_coordinates[1],
            'lng', NEW.gps_coordinates[0]
          )
        );
    ELSE
      NEW.address = NEW.address - 'coordinates';
    END IF;
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Additional trigger for reverse synchronization (optional, for direct point updates)
-- CREATE TRIGGER trigger_sync_address_coordinates
--   BEFORE UPDATE ON companies
--   FOR EACH ROW EXECUTE FUNCTION sync_address_coordinates();

-- HYBRID APPROACH: Company Metadata Validation
-- Function to validate metadata structure based on company type
-- Complex types (customer, carrier, factory) should have NULL metadata
-- Simple types (shipper, consignee, notify_party, forwarder_agent) use JSONB metadata
CREATE OR REPLACE FUNCTION validate_company_metadata(
  comp_type company_type_enum, 
  meta jsonb
) RETURNS boolean AS $$
BEGIN
  -- Validate based on company type
  CASE comp_type
    WHEN 'customer', 'carrier', 'factory' THEN
      -- Complex types should have NULL metadata (data stored in separate tables)
      RETURN meta IS NULL;
      
    WHEN 'shipper' THEN
      -- Simple type: validate shipper metadata structure if present
      IF meta IS NOT NULL THEN
        -- Validate company_seal structure if present
        IF meta ? 'company_seal' AND 
           NOT (meta->'company_seal' ? 'file_path' AND meta->'company_seal' ? 'mime_type') THEN
          RETURN false;
        END IF;
        
        -- Validate export_licenses is array if present
        IF meta ? 'export_licenses' AND 
           jsonb_typeof(meta->'export_licenses') != 'array' THEN
          RETURN false;
        END IF;
      END IF;
      
    WHEN 'consignee' THEN
      -- Simple type: validate consignee metadata
      IF meta IS NOT NULL THEN
        -- Validate USCI format if present (18 characters for China)
        IF meta ? 'usci' AND 
           length(meta->>'usci') != 18 THEN
          RETURN false;
        END IF;
        
        -- Validate customs_broker structure if present
        IF meta ? 'customs_broker' AND 
           NOT (meta->'customs_broker' ? 'name' AND meta->'customs_broker' ? 'phone') THEN
          RETURN false;
        END IF;
      END IF;
      
    WHEN 'notify_party' THEN
      -- Simple type: validate notify party metadata
      IF meta IS NOT NULL THEN
        -- Validate notification_preferences structure
        IF meta ? 'notification_preferences' AND 
           NOT jsonb_typeof(meta->'notification_preferences') = 'object' THEN
          RETURN false;
        END IF;
      END IF;
      
    WHEN 'forwarder_agent' THEN
      -- Simple type: validate forwarder agent metadata
      IF meta IS NOT NULL THEN
        -- Validate services is array if present
        IF meta ? 'services' AND 
           jsonb_typeof(meta->'services') != 'array' THEN
          RETURN false;
        END IF;
        
        -- Validate shipping_lines structure if present
        IF meta ? 'shipping_lines' AND 
           jsonb_typeof(meta->'shipping_lines') != 'array' THEN
          RETURN false;
        END IF;
      END IF;
      
    ELSE
      -- Unknown company type
      RETURN false;
  END CASE;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- Trigger to enforce metadata consistency for hybrid approach
CREATE OR REPLACE FUNCTION enforce_metadata_consistency()
RETURNS TRIGGER AS $$
BEGIN
  -- Clear metadata for complex types (they use separate info tables)
  IF NEW.company_type IN ('customer', 'carrier', 'factory') THEN
    NEW.metadata = NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_enforce_metadata_consistency
  BEFORE INSERT OR UPDATE ON companies
  FOR EACH ROW EXECUTE FUNCTION enforce_metadata_consistency();
```

### Hybrid GPS Coordinates Implementation

#### Design Rationale
The system uses a **hybrid approach** for storing GPS coordinates to optimize both API performance and geographic queries:

1. **Primary Storage**: JSONB `address` field contains coordinates for API completeness
2. **Secondary Storage**: Dedicated `gps_coordinates point` column for efficient geographic queries
3. **Automatic Sync**: Database triggers maintain data consistency between both storage methods

#### Benefits of Hybrid Approach
- **API Performance**: Complete address objects with coordinates in single query
- **Geographic Performance**: Optimized point column for location-based searches
- **Data Integrity**: Automatic synchronization prevents inconsistencies
- **Query Flexibility**: Use optimal storage method for each query type
- **Future Scalability**: Support for PostGIS extensions without API changes

#### Indexing Strategy
```sql
-- Indexes for hybrid GPS coordinate system
CREATE INDEX idx_companies_address_gin ON companies USING GIN (address);
CREATE INDEX idx_companies_gps_coordinates ON companies USING GIST (gps_coordinates);
CREATE INDEX idx_companies_province_en ON companies USING GIN ((address->'province'->>'en'));
CREATE INDEX idx_companies_postal_code ON companies USING BTREE ((address->>'postal_code'));

-- Spatial index for geographic queries
CREATE INDEX idx_companies_location_spatial ON companies USING GIST (gps_coordinates)
WHERE gps_coordinates IS NOT NULL;
```

#### Usage Examples

**Geographic Queries (Use Point Column):**
```sql
-- Find companies within 5km of Bangkok center
SELECT id, name, address FROM companies 
WHERE ST_DWithin(gps_coordinates, point(100.5018, 13.7563), 5000)
AND is_active = true;

-- Nearest companies to a location
SELECT id, name, address,
  ST_Distance(gps_coordinates, point(100.5018, 13.7563)) as distance_meters
FROM companies 
WHERE gps_coordinates IS NOT NULL
ORDER BY gps_coordinates <-> point(100.5018, 13.7563)
LIMIT 10;
```

**Address Queries (Use JSONB):**
```sql
-- Companies in specific province
SELECT id, name, address FROM companies 
WHERE address->'province'->>'en' = 'Bangkok';

-- Full address with coordinates for API
SELECT id, name, address FROM companies 
WHERE address->>'postal_code' = '10110';
```

#### TypeScript Integration
```typescript
// Units of measure interface
export interface UnitOfMeasure {
  id: string;
  code: string;                    // 'KG', 'PCS', 'LTR', etc.
  name: string;                    // 'Kilogram', 'Pieces', 'Liter'
  category: 'weight' | 'count' | 'volume' | 'length';
  symbol: string;                  // 'kg', 'pcs', 'L'
  conversion_factor: number;       // For unit conversions (base unit = 1)
  base_unit_id?: string;          // Reference to base unit for conversions
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Relationship management interfaces
export interface CustomerShipper {
  id: string;
  customer_id: string;
  shipper_id: string;
  is_default: boolean;
  is_active: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
  // Related entities populated via joins
  customer?: Company;
  shipper?: Company;
}

export interface CustomerProduct {
  id: string;
  customer_id: string;
  product_id: string;
  customer_product_code?: string;
  is_default: boolean;
  is_active: boolean;
  // Pricing information (per KG as base unit)
  unit_price_cif?: number;  // Price per KG
  unit_price_fob?: number;  // Price per KG
  currency_code: 'USD' | 'THB' | 'CNY' | 'EUR';
  // Physical specifications and packaging
  standard_quantity?: number;  // Number of packages
  packaging_type: string;      // Type of packaging (box, bag, carton, pallet, etc.)
  unit_of_measure_id?: string; // Base UOM is KG
  gross_weight_per_package?: number; // Gross weight per 1 package in KG
  net_weight_per_package?: number;   // Net weight per 1 package in KG
  // Quality and packaging
  quality_grade?: string;
  packaging_specifications?: Record<string, any>;
  // Logistics
  handling_instructions?: string;
  temperature_require?: string;
  vent_require?: string;
  shelf_life_days?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  // Related entities
  customer?: Company;
  product?: Product;
  unit_of_measure?: UnitOfMeasure;
}

export interface ConsigneeNotifyParty {
  id: string;
  consignee_id: string;
  notify_party_id: string;
  is_default: boolean;
  is_active: boolean;
  notification_preferences?: Record<string, boolean>;
  priority_order: number;
  special_instructions?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  // Related entities
  consignee?: Company;
  notify_party?: Company;
}

// Products interface with unit of measure reference
export interface Product {
  id: string;
  name: string;
  code: string;
  description?: string;
  category?: string;
  hs_code?: string;
  unit_of_measure_id: string;     // Foreign key to units_of_measure
  unit_of_measure?: UnitOfMeasure; // Populated via join
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Drivers interface
export interface Driver {
  id: string;
  carrier_id: string;
  driver_first_name: string;
  driver_last_name: string;
  driver_code?: string;
  phone?: string;
  line_id?: string;
  driver_picture_path?: string;
  driver_picture_mime_type?: string;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Related entities
  carrier?: Company;
}

// Address interface with coordinates
export interface Address {
  street: { th?: string; en: string };
  district?: { th?: string; en?: string };
  province: { th?: string; en: string };
  postal_code?: string;
  country: { th?: string; en: string };
  coordinates?: { lat: number; lng: number }; // Primary source of truth
  shipping_instructions?: { th?: string; en?: string };
}

// Geographic query service
export class LocationService {
  async findNearbyCompanies(lat: number, lng: number, radiusKm: number = 5) {
    const { data } = await supabase
      .from('companies')
      .select('id, name, address')
      .within('gps_coordinates', [lng, lat], radiusKm * 1000);
    return data;
  }
  
  async updateCompanyLocation(companyId: string, lat: number, lng: number) {
    // Update JSONB address - trigger will sync point column automatically
    const { error } = await supabase
      .from('companies')
      .update({
        address: {
          ...existingAddress,
          coordinates: { lat, lng }
        }
      })
      .eq('id', companyId);
    return { error };
  }
}

// Units of measure service
export class UnitsOfMeasureService {
  async getAllUnits(): Promise<UnitOfMeasure[]> {
    const { data, error } = await supabase
      .from('units_of_measure')
      .select('*')
      .eq('is_active', true)
      .order('category, name');
    
    if (error) throw error;
    return data || [];
  }
  
  async getUnitsByCategory(category: string): Promise<UnitOfMeasure[]> {
    const { data, error } = await supabase
      .from('units_of_measure')
      .select('*')
      .eq('category', category)
      .eq('is_active', true)
      .order('name');
    
    if (error) throw error;
    return data || [];
  }
  
  async convertUnits(
    value: number, 
    fromUnitId: string, 
    toUnitId: string
  ): Promise<number> {
    // Get conversion factors for both units
    const { data: units } = await supabase
      .from('units_of_measure')
      .select('id, conversion_factor, base_unit_id')
      .in('id', [fromUnitId, toUnitId]);
    
    if (!units || units.length !== 2) {
      throw new Error('Invalid unit IDs for conversion');
    }
    
    const fromUnit = units.find(u => u.id === fromUnitId)!;
    const toUnit = units.find(u => u.id === toUnitId)!;
    
    // Convert to base unit first, then to target unit
    const baseValue = value * fromUnit.conversion_factor;
    const convertedValue = baseValue / toUnit.conversion_factor;
    
    return Math.round(convertedValue * 10000) / 10000; // Round to 4 decimal places
  }
}

// Relationship management services
export class RelationshipService {
  // Customer-Shipper relationship management
  async getCustomerShippers(customerId: string): Promise<CustomerShipper[]> {
    const { data, error } = await supabase
      .from('customer_shippers')
      .select(`
        *,
        customer:customer_id (*),
        shipper:shipper_id (*)
      `)
      .eq('customer_id', customerId)
      .eq('is_active', true)
      .order('is_default', { ascending: false }); // Default first
    
    if (error) throw error;
    return data || [];
  }

  async setDefaultShipper(customerId: string, shipperId: string): Promise<void> {
    const { error } = await supabase
      .from('customer_shippers')
      .update({ is_default: true })
      .eq('customer_id', customerId)
      .eq('shipper_id', shipperId);
    
    if (error) throw error;
  }

  // Customer-Product relationship management  
  async getCustomerProducts(customerId: string): Promise<CustomerProduct[]> {
    const { data, error } = await supabase
      .from('customer_products')
      .select(`
        *,
        customer:customer_id (*),
        product:product_id (*),
        unit_of_measure:unit_of_measure_id (*)
      `)
      .eq('customer_id', customerId)
      .eq('is_active', true)
      .order('is_default', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  async createCustomerProduct(customerProduct: Omit<CustomerProduct, 'id' | 'created_at' | 'updated_at'>): Promise<CustomerProduct> {
    const { data, error } = await supabase
      .from('customer_products')
      .insert([customerProduct])
      .select(`
        *,
        customer:customer_id (*),
        product:product_id (*),
        unit_of_measure:unit_of_measure_id (*)
      `)
      .single();
    
    if (error) throw error;
    return data;
  }

  // Consignee-Notify Party relationship management
  async getConsigneeNotifyParties(consigneeId: string): Promise<ConsigneeNotifyParty[]> {
    const { data, error } = await supabase
      .from('consignee_notify_parties')
      .select(`
        *,
        consignee:consignee_id (*),
        notify_party:notify_party_id (*)
      `)
      .eq('consignee_id', consigneeId)
      .eq('is_active', true)
      .order('is_default', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  // Shipment creation helper - get pre-populated data
  async getShipmentDefaults(customerId?: string, consigneeId?: string) {
    const defaults: {
      defaultShipper?: Company;
      availableShippers?: Company[];
      defaultProduct?: CustomerProduct;
      availableProducts?: CustomerProduct[];
      defaultNotifyParty?: Company;
      availableNotifyParties?: Company[];
    } = {};

    if (customerId) {
      // Get customer's shippers and products
      const [shippers, products] = await Promise.all([
        this.getCustomerShippers(customerId),
        this.getCustomerProducts(customerId)
      ]);
      
      defaults.availableShippers = shippers.map(cs => cs.shipper!).filter(Boolean);
      defaults.defaultShipper = shippers.find(cs => cs.is_default)?.shipper;
      
      defaults.availableProducts = products;
      defaults.defaultProduct = products.find(cp => cp.is_default);
    }

    if (consigneeId) {
      // Get consignee's notify parties
      const notifyParties = await this.getConsigneeNotifyParties(consigneeId);
      
      defaults.availableNotifyParties = notifyParties.map(cnp => cnp.notify_party!).filter(Boolean);
      defaults.defaultNotifyParty = notifyParties.find(cnp => cnp.is_default)?.notify_party;
    }

    return defaults;
  }
}

// Products service with unit of measure integration
export class ProductsService {
  async getAllProducts(): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        unit_of_measure:unit_of_measure_id (*)
      `)
      .eq('is_active', true)
      .order('category, name');
    
    if (error) throw error;
    return data || [];
  }
  
  async getProductById(id: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        unit_of_measure:unit_of_measure_id (*)
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single();
    
    if (error) throw error;
    return data;
  }
  
  async createProduct(product: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<Product> {
    // Validate unit_of_measure_id exists
    const { data: unit } = await supabase
      .from('units_of_measure')
      .select('id')
      .eq('id', product.unit_of_measure_id)
      .eq('is_active', true)
      .single();
    
    if (!unit) {
      throw new Error('Invalid unit of measure ID');
    }
    
    const { data, error } = await supabase
      .from('products')
      .insert([product])
      .select(`
        *,
        unit_of_measure:unit_of_measure_id (*)
      `)
      .single();
    
    if (error) throw error;
    return data;
  }
}
```

#### Migration from Single Storage
```sql
-- Step 1: Add point column if not exists
ALTER TABLE companies ADD COLUMN IF NOT EXISTS gps_coordinates point;

-- Step 2: Populate point column from existing JSONB data
UPDATE companies 
SET gps_coordinates = point(
  (address->'coordinates'->>'lng')::float,
  (address->'coordinates'->>'lat')::float
)
WHERE address->'coordinates' IS NOT NULL 
AND gps_coordinates IS NULL;

-- Step 3: Create synchronization trigger
-- (Already defined in Database Functions section above)

-- Step 4: Create optimized indexes
-- (Already defined in Indexing Strategy section above)
```

#### Performance Comparison
| Query Type | JSONB Only | Point Only | Hybrid Approach |
|-----------|-------------|------------|-----------------|
| Geographic queries | ~100ms | ~20ms | ~20ms (uses point) |
| Complete address fetch | ~50ms | ~80ms | ~50ms (uses JSONB) |
| Address updates | ~40ms | ~60ms | ~45ms (sync overhead) |
| Storage per record | ~120 bytes | ~100 bytes | ~140 bytes |

**Note**: For detailed JSONB address design rationale and implementation examples, see `database-address-design-explanation.md` in the project documentation.

**Hybrid Companies Design**: For comprehensive details on the hybrid approach combining separate info tables for complex company types with JSONB metadata for simple types, including validation functions, TypeScript interfaces, performance analysis, and migration strategies, see `hybrid-companies-design.md` in the project documentation.

---