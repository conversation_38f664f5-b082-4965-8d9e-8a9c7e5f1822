import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

// Create Supabase clients
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
)

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const containerId = params.id
    
    if (!containerId) {
      console.error('Container status API: Missing container ID')
      return NextResponse.json({ 
        error: 'Bad Request', 
        details: 'Container ID is required' 
      }, { status: 400 })
    }

    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization?.startsWith('Bearer ')) {
      console.error('Container status API: Missing or invalid authorization header')
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Missing or invalid authorization header' 
      }, { status: 401 })
    }

    const token = authorization.split(' ')[1]
    if (!token) {
      console.error('Container status API: Empty authorization token')
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Empty authorization token' 
      }, { status: 401 })
    }

    // Verify user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.error('Container status API: Auth error:', authError)
      return NextResponse.json({ 
        error: 'Invalid token', 
        details: authError?.message || 'No user found for provided token' 
      }, { status: 401 })
    }

    // Get container with confirmation status and user details
    const { data: container, error: containerError } = await supabaseAdmin
      .from('containers')
      .select(`
        id,
        container_number,
        seal_number,
        container_number_confirmed,
        container_number_confirmed_by,
        container_number_confirmed_at,
        seal_number_confirmed,
        seal_number_confirmed_by,
        seal_number_confirmed_at,
        container_number_confirmer:profiles!containers_container_number_confirmed_by_fkey(
          first_name,
          last_name,
          role
        ),
        seal_number_confirmer:profiles!containers_seal_number_confirmed_by_fkey(
          first_name,
          last_name,
          role
        )
      `)
      .eq('id', containerId)
      .single()

    if (containerError) {
      console.error('Container status API: Container fetch error:', containerError)
      return NextResponse.json({ 
        error: 'Container not found', 
        details: containerError.message 
      }, { status: 404 })
    }

    // Format response with confirmation details
    const confirmationStatus = {
      container_id: container.id,
      container_number: container.container_number,
      seal_number: container.seal_number,
      container_number_confirmed: container.container_number_confirmed || false,
      container_number_confirmed_by: container.container_number_confirmed_by,
      container_number_confirmed_at: container.container_number_confirmed_at,
      container_number_confirmer: container.container_number_confirmer ? {
        name: `${container.container_number_confirmer.first_name} ${container.container_number_confirmer.last_name}`,
        role: container.container_number_confirmer.role
      } : null,
      seal_number_confirmed: container.seal_number_confirmed || false,
      seal_number_confirmed_by: container.seal_number_confirmed_by,
      seal_number_confirmed_at: container.seal_number_confirmed_at,
      seal_number_confirmer: container.seal_number_confirmer ? {
        name: `${container.seal_number_confirmer.first_name} ${container.seal_number_confirmer.last_name}`,
        role: container.seal_number_confirmer.role
      } : null
    }

    console.log(`Container status API: Successfully retrieved status for container ${containerId}`)

    return NextResponse.json({
      success: true,
      confirmation_status: confirmationStatus
    })

  } catch (error) {
    console.error('Container status API Error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}