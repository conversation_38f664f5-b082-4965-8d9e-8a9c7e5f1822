'use client'

import { useState, useEffect, useCallback, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { ArrowLeft, Package, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { StatusUpdateForm } from '@/components/mobile/status-update-form'
import { createClient } from '@/lib/supabase/client'
import { getStatusDisplayName } from '@/lib/utils/status-workflow'
import { useLanguage } from '@/hooks/use-language'
import type { ShipmentStatus } from '@/types/status-update'

interface Shipment {
  id: string
  shipment_number: string
  status: ShipmentStatus
  customer?: {
    name: string
  }
  factory?: {
    name: string
  }
}

function StatusUpdateContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { t } = useLanguage()
  const shipmentId = searchParams?.get('shipment_id')
  
  const [shipment, setShipment] = useState<Shipment | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createClient()

  const fetchShipment = useCallback(async () => {
    if (!shipmentId) return

    try {
      setIsLoading(true)
      setError(null)

      const { data, error: fetchError } = await supabase
        .from('shipments')
        .select(`
          id,
          shipment_number,
          status,
          customer:customer_id (
            name
          ),
          factory:factory_id (
            name
          )
        `)
        .eq('id', shipmentId)
        .single()

      if (fetchError) {
        throw fetchError
      }

      if (!data) {
        throw new Error('Shipment not found')
      }

      setShipment(data as Shipment)
    } catch (error) {
      console.error('Error fetching shipment:', error)
      setError(error instanceof Error ? error.message : 'Failed to load shipment')
    } finally {
      setIsLoading(false)
    }
  }, [shipmentId, supabase])

  useEffect(() => {
    if (!shipmentId) {
      setError('No shipment ID provided')
      setIsLoading(false)
      return
    }

    fetchShipment()
  }, [shipmentId, fetchShipment])

  const handleUpdateComplete = useCallback((result: any) => {
    console.log('Status update completed:', result)
    
    // Navigate back to dashboard or show success
    router.push('/driver/dashboard?updated=true')
  }, [router])

  const handleCancel = useCallback(() => {
    router.back()
  }, [router])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-900 px-4 py-6">
        <div className="max-w-md mx-auto">
          <div className="bg-slate-800 rounded-lg border border-slate-700 p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4" />
            <p className="text-slate-300">{t('common.loading')}</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !shipment) {
    return (
      <div className="min-h-screen bg-slate-900 px-4 py-6">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="flex items-center space-x-3 mb-6">
            <Button
              onClick={handleCancel}
              variant="ghost"
              size="sm"
              className="p-2 text-slate-400 hover:text-white"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <h1 className="text-xl font-semibold text-white">
              {t('statusUpdate.title')}
            </h1>
          </div>

          {/* Error Display */}
          <Alert className="border-red-500/30 bg-red-500/10">
            <AlertCircle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-300">
              {error || t('statusUpdate.error')}
            </AlertDescription>
          </Alert>

          <div className="mt-6">
            <Button
              onClick={handleCancel}
              variant="outline"
              className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              {t('common.back')}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 px-4 py-6">
      <div className="max-w-md mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3">
          <Button
            onClick={handleCancel}
            variant="ghost"
            size="sm"
            className="p-2 text-slate-400 hover:text-white"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <h1 className="text-xl font-semibold text-white">
            {t('statusUpdate.title')}
          </h1>
        </div>

        {/* Shipment Info */}
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
          <div className="flex items-center space-x-3 mb-3">
            <Package className="w-5 h-5 text-orange-500 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <h2 className="font-semibold text-white truncate">
                {shipment.shipment_number}
              </h2>
              <p className="text-sm text-slate-400">
                {t('statusUpdate.currentStatus')}: {t(`status.${shipment.status}`, getStatusDisplayName(shipment.status))}
              </p>
            </div>
          </div>

          {/* Additional Info */}
          <div className="space-y-2 text-sm">
            {shipment.customer && (
              <div className="flex justify-between">
                <span className="text-slate-400">{t('shipment.customer')}:</span>
                <span className="text-slate-300">{shipment.customer.name}</span>
              </div>
            )}
            
            {shipment.factory && (
              <div className="flex justify-between">
                <span className="text-slate-400">{t('shipment.factory')}:</span>
                <span className="text-slate-300">{shipment.factory.name}</span>
              </div>
            )}
          </div>
        </div>

        {/* Status Update Form */}
        <StatusUpdateForm
          shipmentId={shipment.id}
          currentStatus={shipment.status}
          onUpdateComplete={handleUpdateComplete}
          onCancel={handleCancel}
        />
      </div>
    </div>
  )
}

// Loading component to show while Suspense is resolving
function StatusUpdateLoading() {
  return (
    <div className="min-h-screen bg-slate-900 px-4 py-6">
      <div className="max-w-md mx-auto">
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4" />
          <p className="text-slate-300">กำลังโหลด...</p>
        </div>
      </div>
    </div>
  )
}

export default function StatusUpdatePage() {
  return (
    <Suspense fallback={<StatusUpdateLoading />}>
      <StatusUpdateContent />
    </Suspense>
  )
}