'use client'

import React, { useState } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter 
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Shield, 
  Check, 
  Clock, 
  User, 
  AlertTriangle, 
  Lock,
  Loader2,
  Package,
  CheckCircle
} from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'
import { createClient } from '@/lib/supabase/client'
import type { ContainerWithProducts } from '@/hooks/use-containers'

interface ContainerConfirmationDialogProps {
  container: ContainerWithProducts
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
}

interface ConfirmationState {
  container_number: boolean
  seal_number: boolean
}

export function ContainerConfirmationDialog({
  container,
  isOpen,
  onClose,
  onConfirm
}: ContainerConfirmationDialogProps) {
  const [confirmState, setConfirmState] = useState<ConfirmationState>({
    container_number: false,
    seal_number: false
  })
  const [notes, setNotes] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Mock auth hook - replace with actual auth implementation
  const user = { role: 'cs', name: 'Current User' } // This should come from your auth system

  const supabase = createClient()

  // Check if numbers are already confirmed
  const isContainerNumberConfirmed = container.container_number_confirmed
  const isSealNumberConfirmed = container.seal_number_confirmed

  // Format confirmation details
  const formatConfirmationDate = (dateString: string | null) => {
    if (!dateString) return null
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleConfirm = async () => {
    if (!confirmState.container_number && !confirmState.seal_number) {
      setError('Please select at least one number to confirm')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Get auth token
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        throw new Error('No active session')
      }

      const response = await fetch(`/api/containers/${container.id}/confirm-numbers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          confirm_container_number: confirmState.container_number,
          confirm_seal_number: confirmState.seal_number,
          confirmation_notes: notes.trim() || undefined
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.details || result.error || 'Confirmation failed')
      }

      console.log('Container numbers confirmed successfully:', result)
      onConfirm()
      onClose()
      
      // Reset state
      setConfirmState({ container_number: false, seal_number: false })
      setNotes('')
      
    } catch (error: any) {
      console.error('Confirmation error:', error)
      setError(error.message || 'Failed to confirm container numbers')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (loading) return
    setConfirmState({ container_number: false, seal_number: false })
    setNotes('')
    setError(null)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-slate-100">
            <Shield className="h-5 w-5 text-orange-500" />
            Confirm Container Numbers
          </DialogTitle>
          <DialogDescription className="text-slate-600">
            Confirm container and seal numbers to prevent driver modifications. Once confirmed, only CS and Admin can make changes.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Container Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Package className="h-4 w-4" />
                Container Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-slate-400">Products:</span>
                  <div className="font-medium">{container.total_products} items</div>
                </div>
                <div>
                  <span className="text-slate-400">Total Weight:</span>
                  <div className="font-medium">{container.total_weight.toFixed(1)} kg</div>
                </div>
                <div>
                  <span className="text-slate-400">Temperature:</span>
                  <div className="font-medium">{container.temperature}</div>
                </div>
                <div>
                  <span className="text-slate-400">Vent:</span>
                  <div className="font-medium">{container.vent}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Container Number Confirmation */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Container Number</CardTitle>
              <CardDescription>
                Confirm the container number to prevent driver modifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-mono text-lg font-medium text-green-600">
                    {container.container_number || 'Not assigned'}
                  </div>
                  {isContainerNumberConfirmed && (
                    <div className="flex items-center gap-2 text-sm text-green-600 mt-1">
                      <CheckCircle className="h-4 w-4" />
                      <span>Confirmed</span>
                      {container.container_number_confirmed_at && (
                        <span className="text-slate-500">
                          on {formatConfirmationDate(container.container_number_confirmed_at)}
                        </span>
                      )}
                    </div>
                  )}
                </div>
                {!isContainerNumberConfirmed && container.container_number && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="confirm-container-number"
                      checked={confirmState.container_number}
                      onChange={(e) => setConfirmState(prev => ({
                        ...prev,
                        container_number: e.target.checked
                      }))}
                      className="rounded border-slate-300 text-orange-500 focus:ring-orange-500"
                    />
                    <Label 
                      htmlFor="confirm-container-number"
                      className="text-sm font-medium cursor-pointer"
                    >
                      Confirm this number
                    </Label>
                  </div>
                )}
                {isContainerNumberConfirmed && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <Lock className="h-3 w-3 mr-1" />
                    Confirmed
                  </Badge>
                )}
              </div>
              
              {!container.container_number && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Container number must be assigned before confirmation
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Seal Number Confirmation */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Seal Number</CardTitle>
              <CardDescription>
                Confirm the seal number to prevent driver modifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-mono text-lg font-medium text-yellow-600">
                    {container.seal_number || 'Not assigned'}
                  </div>
                  {isSealNumberConfirmed && (
                    <div className="flex items-center gap-2 text-sm text-green-600 mt-1">
                      <CheckCircle className="h-4 w-4" />
                      <span>Confirmed</span>
                      {container.seal_number_confirmed_at && (
                        <span className="text-slate-500">
                          on {formatConfirmationDate(container.seal_number_confirmed_at)}
                        </span>
                      )}
                    </div>
                  )}
                </div>
                {!isSealNumberConfirmed && container.seal_number && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="confirm-seal-number"
                      checked={confirmState.seal_number}
                      onChange={(e) => setConfirmState(prev => ({
                        ...prev,
                        seal_number: e.target.checked
                      }))}
                      className="rounded border-slate-300 text-orange-500 focus:ring-orange-500"
                    />
                    <Label 
                      htmlFor="confirm-seal-number"
                      className="text-sm font-medium cursor-pointer"
                    >
                      Confirm this number
                    </Label>
                  </div>
                )}
                {isSealNumberConfirmed && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <Lock className="h-3 w-3 mr-1" />
                    Confirmed
                  </Badge>
                )}
              </div>
              
              {!container.seal_number && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Seal number must be assigned before confirmation
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Notes Section */}
          {(confirmState.container_number || confirmState.seal_number) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Confirmation Notes</CardTitle>
                <CardDescription>
                  Optional notes about this confirmation (optional)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Add any notes about this confirmation..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="min-h-[80px] resize-none"
                  maxLength={500}
                />
                <div className="text-xs text-slate-500 mt-1 text-right">
                  {notes.length}/500 characters
                </div>
              </CardContent>
            </Card>
          )}

          {/* Warning */}
          {(confirmState.container_number || confirmState.seal_number) && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-500" />
              <AlertDescription className="text-orange-800">
                Once confirmed, drivers will not be able to edit these numbers. 
                Only Customer Service and Admin can make changes after confirmation.
              </AlertDescription>
            </Alert>
          )}

          {/* Error Display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button 
            variant="outline" 
            onClick={handleClose}
            disabled={loading}
            className="border-slate-300 text-slate-100 hover:bg-slate-50 hover:text-black"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm}
            disabled={loading || (!confirmState.container_number && !confirmState.seal_number)}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Confirming...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Confirm Numbers
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}