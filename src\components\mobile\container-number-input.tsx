'use client'

import { useState, useEffect, useRef } from 'react'
import { Camera, Check, X, AlertTriangle, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { BarcodeScanner } from './barcode-scanner'
import { getContainerNumberExamples } from '@/lib/utils/container-validation'
import type { ContainerValidationResult } from '@/types/container'

interface ContainerNumberInputProps {
  value: string
  onChange: (value: string) => void
  onValidation: (result: ContainerValidationResult) => void
  validation?: ContainerValidationResult | null
  isValidatingUniqueness?: boolean
  disabled?: boolean
  enableScanning?: boolean
  className?: string
}

export function ContainerNumberInput({
  value,
  onChange,
  onValidation,
  validation,
  isValidatingUniqueness = false,
  disabled = false,
  enableScanning = true,
  className = ''
}: ContainerNumberInputProps) {
  const [showScanner, setShowScanner] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // Get validation state
  const isValid = validation?.isValid === true
  const hasErrors = validation && validation.errors.length > 0
  const isValidating = isValidatingUniqueness

  // Handle scanner result
  const handleScanComplete = (scannedValue: string) => {
    onChange(scannedValue)
    setShowScanner(false)
    
    // Focus input after scanning
    setTimeout(() => {
      inputRef.current?.focus()
    }, 100)
  }

  const handleScanError = (error: string) => {
    console.error('Barcode scan error:', error)
    setShowScanner(false)
    
    // Focus input on scan error
    setTimeout(() => {
      inputRef.current?.focus()
    }, 100)
  }

  // Get status icon
  const getStatusIcon = () => {
    if (isValidating) {
      return <Loader2 className="w-5 h-5 text-orange-400 animate-spin" />
    }
    
    if (value && isValid && !isValidating) {
      return <Check className="w-5 h-5 text-green-400" />
    }
    
    if (value && hasErrors) {
      return <X className="w-5 h-5 text-red-400" />
    }
    
    return null
  }

  // Get input border color based on validation state
  const getBorderColor = () => {
    if (isValidating) return 'border-orange-500/50'
    if (value && isValid && !isValidating) return 'border-green-500/50'
    if (value && hasErrors) return 'border-red-500/50'
    if (isFocused) return 'border-orange-500'
    return 'border-slate-600'
  }

  // Get examples for display
  const examples = getContainerNumberExamples()

  if (showScanner) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium text-slate-300">
            Container Number - Scan Barcode
          </Label>
          <Button
            onClick={() => setShowScanner(false)}
            variant="outline"
            size="sm"
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <X className="w-4 h-4 mr-1" />
            Cancel
          </Button>
        </div>
        
        <BarcodeScanner
          onScanComplete={handleScanComplete}
          onScanError={handleScanError}
          placeholder="Scanning for container number..."
          expectedFormat="container"
        />
        
        <div className="text-center">
          <p className="text-sm text-slate-400 mb-2">
            Point camera at container number barcode
          </p>
          <Button
            onClick={() => setShowScanner(false)}
            variant="outline"
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            Enter Manually Instead
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Label and Scanner Button */}
      <div className="flex items-center justify-between">
        <Label htmlFor="container-number" className="text-sm font-medium text-slate-300">
          Container Number *
        </Label>
        {enableScanning && (
          <Button
            onClick={() => setShowScanner(true)}
            disabled={disabled}
            variant="outline"
            size="sm"
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <Camera className="w-4 h-4 mr-1" />
            Scan
          </Button>
        )}
      </div>

      {/* Input Field */}
      <div className="relative">
        <Input
          ref={inputRef}
          id="container-number"
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value.toUpperCase())}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder="ABCD1234567"
          disabled={disabled}
          className={`
            bg-slate-700 text-white placeholder-slate-400 
            ${getBorderColor()} 
            pr-12 text-lg font-mono tracking-wider
            min-h-[56px] text-center
            focus:ring-2 focus:ring-orange-500/20
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          style={{
            fontSize: '18px',
            letterSpacing: '2px'
          }}
          maxLength={15}
        />
        
        {/* Status Icon */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          {getStatusIcon()}
        </div>
      </div>

      {/* Format Info */}
      <div className="bg-slate-800 rounded-lg border border-slate-700 p-3">
        <p className="text-xs text-slate-400 mb-2">
          Format: 4 letters + 7 digits (e.g., ABCD1234567)
        </p>
        <div className="flex flex-wrap gap-2">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => !disabled && onChange(example)}
              disabled={disabled}
              className="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded font-mono tracking-wide transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {example}
            </button>
          ))}
        </div>
      </div>

      {/* Validation Messages */}
      {isValidating && (
        <Alert className="border-orange-500/30 bg-orange-500/10">
          <Loader2 className="h-4 w-4 text-orange-400 animate-spin" />
          <AlertDescription className="text-orange-300">
            Checking container number uniqueness...
          </AlertDescription>
        </Alert>
      )}

      {hasErrors && !isValidating && (
        <Alert className="border-red-500/30 bg-red-500/10">
          <AlertTriangle className="h-4 w-4 text-red-400" />
          <AlertDescription className="text-red-300">
            {validation.errors[0]}
          </AlertDescription>
        </Alert>
      )}

      {isValid && !isValidating && value && (
        <Alert className="border-green-500/30 bg-green-500/10">
          <Check className="h-4 w-4 text-green-400" />
          <AlertDescription className="text-green-300">
            Valid container number
            {validation.formattedNumber && ` - ${validation.formattedNumber}`}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}