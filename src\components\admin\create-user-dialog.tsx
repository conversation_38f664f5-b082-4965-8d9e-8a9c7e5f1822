'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { createClient } from '@/lib/supabase/client'
import {
  adminUserCreateSchema,
  type AdminUserCreateFormData,
} from '@/lib/validations/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Loader2, X } from 'lucide-react'

interface CreateUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface Company {
  id: string
  name: string
  company_type: string
}

const roles = [
  { value: 'admin', label: 'Admin' },
  { value: 'cs', label: 'Customer Service' },
  { value: 'account', label: 'Account Manager' },
  { value: 'customer', label: 'Customer' },
  { value: 'carrier', label: 'Carrier' },
  { value: 'driver', label: 'Driver' },
  { value: 'factory', label: 'Factory' },
  { value: 'shipper', label: 'Shipper' },
  { value: 'consignee', label: 'Consignee' },
  { value: 'notify_party', label: 'Notify Party' },
  { value: 'forwarder_agent', label: 'Forwarder Agent' },
]

export function CreateUserDialog({
  open,
  onOpenChange,
}: CreateUserDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [companies, setCompanies] = useState<Company[]>([])
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  const form = useForm<AdminUserCreateFormData>({
    resolver: zodResolver(adminUserCreateSchema),
    defaultValues: {
      email: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      lineId: '',
      wechatId: '',
      role: 'customer',
      companyId: '',
      isActive: true,
      sendInviteEmail: true,
    },
  })

  const selectedRole = form.watch('role')

  // Get company type based on selected role
  const getCompanyTypeForRole = (role: string): string | null => {
    const roleCompanyMapping: Record<string, string> = {
      customer: 'customer',
      carrier: 'carrier',
      driver: 'carrier', // drivers work for carriers
      factory: 'factory',
      shipper: 'shipper',
      consignee: 'consignee',
      notify_party: 'notify_party',
      forwarder_agent: 'forwarder_agent',
    }
    return roleCompanyMapping[role] || null
  }

  // Clear company_id when role doesn't require company or when company type changes
  useEffect(() => {
    const staffRoles = ['admin', 'cs', 'account']
    if (selectedRole && staffRoles.includes(selectedRole)) {
      form.setValue('companyId', '')
    } else if (selectedRole) {
      // Clear company selection when switching to a role with different company type requirement
      const currentCompanyId = form.getValues('companyId')
      if (currentCompanyId) {
        const currentCompany = companies.find(c => c.id === currentCompanyId)
        const requiredCompanyType = getCompanyTypeForRole(selectedRole)
        if (
          currentCompany &&
          requiredCompanyType &&
          currentCompany.company_type !== requiredCompanyType
        ) {
          form.setValue('companyId', '')
        }
      }
    }
  }, [selectedRole, form, companies])

  useEffect(() => {
    if (open) {
      loadCompanies()
      form.reset()
      setError(null)
    }
  }, [open, form])

  async function loadCompanies() {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('id, name, company_type')
        .eq('is_active', true)
        .order('name')

      if (error) throw error
      setCompanies(data || [])
    } catch (err) {
      console.error('Error loading companies:', err)
    }
  }

  async function onSubmit(data: AdminUserCreateFormData) {
    setIsSubmitting(true)
    setError(null)

    try {
      // Get current user's session token for API authentication
      const {
        data: { session },
      } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('Not authenticated')
      }

      // Call our API route to create the user
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create user')
      }

      onOpenChange(false)
      // Refresh the parent component's user list
      window.location.reload()
    } catch (err) {
      console.error('Error creating user:', err)
      setError(err instanceof Error ? err.message : 'Failed to create user')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!open) return null

  const staffRoles = ['admin', 'cs', 'account']
  const requiresCompany = !staffRoles.includes(selectedRole)

  // Filter companies based on selected role
  const filteredCompanies = companies.filter(company => {
    const requiredCompanyType = getCompanyTypeForRole(selectedRole)
    return requiredCompanyType
      ? company.company_type === requiredCompanyType
      : true
  })

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-slate-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Create New User</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="text-slate-400 hover:text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4">
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        )}

        {/* Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Email *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="<EMAIL>"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* First Name */}
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">First Name *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="John"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Last Name */}
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Last Name *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Doe"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Phone Number */}
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Phone Number</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="+****************"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Line ID */}
            <FormField
              control={form.control}
              name="lineId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Line ID</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="@username or line_id"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* WeChat ID */}
            <FormField
              control={form.control}
              name="wechatId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">WeChat ID</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="wechat_username"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Role */}
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Role *</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-slate-800 border-slate-700">
                      {roles.map(role => (
                        <SelectItem
                          key={role.value}
                          value={role.value}
                          className="text-slate-300 hover:bg-slate-700"
                        >
                          {role.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Company (conditional) */}
            {requiresCompany && (
              <FormField
                control={form.control}
                name="companyId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Company *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select company" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700">
                        {filteredCompanies.map(company => (
                          <SelectItem
                            key={company.id}
                            value={company.id}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            {company.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage className="text-red-300" />
                  </FormItem>
                )}
              />
            )}

            {/* Note about password */}
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
              <p className="text-blue-300 text-sm">
                A password reset email will be sent to the user after account
                creation.
              </p>
            </div>

            {/* Actions */}
            <div className="flex space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
              >
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Create User
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}
