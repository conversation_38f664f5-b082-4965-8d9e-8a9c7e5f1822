'use client'

import { useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { useShipmentStore } from '@/stores/shipment-store'
import { useShipmentSearch } from '@/hooks/use-shipment-search'
import { ShipmentsTable } from '@/components/shipments/shipments-table'
import { ShipmentFilters } from '@/components/shipments/shipment-filters'
import { ShipmentSearch } from '@/components/shipments/shipment-search'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Plus, Filter, RefreshCw, Bell, X, LayoutGrid, Table2 } from 'lucide-react'
import { ShipmentCardList } from '@/components/shipments/shipment-card'
import { ShipmentsPagination } from '@/components/shipments/shipments-pagination'
import { useResponsiveView } from '@/hooks/use-responsive-view'

export default function ShipmentsPage() {
  const router = useRouter()
  
  // Responsive view management
  const { viewMode, screenInfo } = useResponsiveView()
  
  // Store state for UI
  const { 
    showFilters, 
    toggleFilters, 
    hasNewUpdates, 
    markNewUpdates,
    toggleViewMode,
    selectedShipments,
    expandedCards,
    toggleShipmentSelection,
    toggleCardExpansion
  } = useShipmentStore()
  
  // Optimized search and data fetching
  const {
    shipments,
    isLoading,
    error,
    pagination,
    refreshSearch,
    hasResults,
    isEmpty,
    hasError,
    goToPage,
    changePageSize,
    nextPage,
    previousPage,
  } = useShipmentSearch()

  // Handle refresh
  const handleRefresh = () => {
    refreshSearch()
    markNewUpdates(false) // Clear update notification after refresh
  }

  // Handle dismiss update notification
  const handleDismissUpdates = () => {
    markNewUpdates(false)
  }

  // Calculate statistics
  const stats = useMemo(() => {
    const statusCounts = shipments.reduce((acc, shipment) => {
      const status = shipment.status || 'unknown'
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      total: pagination.totalCount,
      showing: shipments.length,
      statusCounts,
    }
  }, [shipments, pagination.totalCount])

  if (isLoading && !hasResults) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-slate-300">Loading shipments...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Shipments</h1>
          <p className="text-slate-300 mt-1">
            Showing {stats.showing} of {stats.total} shipments
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          {/* View Toggle - Hidden on mobile since card view is enforced */}
          {!screenInfo.isMobile && (
            <div className="flex items-center border border-slate-600 rounded-md bg-slate-800/50">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => viewMode !== 'table' && toggleViewMode()}
                className={`h-8 px-3 rounded-l-md rounded-r-none ${
                  viewMode === 'table' 
                    ? 'bg-blue-600 text-white hover:bg-blue-700' 
                    : 'text-slate-400 hover:text-white hover:bg-slate-700'
                }`}
              >
                <Table2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => viewMode !== 'card' && toggleViewMode()}
                className={`h-8 px-3 rounded-r-md rounded-l-none ${
                  viewMode === 'card' 
                    ? 'bg-blue-600 text-white hover:bg-blue-700' 
                    : 'text-slate-400 hover:text-white hover:bg-slate-700'
                }`}
              >
                <LayoutGrid className="h-4 w-4" />
              </Button>
            </div>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={toggleFilters}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          
          <Button
            onClick={() => router.push('/shipments/create')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Shipment
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats.total > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-400">Total Shipments</p>
                  <p className="text-2xl font-bold text-white">{stats.total}</p>
                </div>
                <div className="text-blue-500">
                  <svg className="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          {Object.entries(stats.statusCounts).slice(0, 3).map(([status, count]) => (
            <Card key={status} className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400 capitalize">{status.replace('_', ' ')}</p>
                    <p className="text-2xl font-bold text-white">{count}</p>
                  </div>
                  <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                    {Math.round((count / stats.total) * 100)}%
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <Alert className="bg-red-900/20 border-red-500">
          <AlertDescription className="text-red-200">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Real-time Update Notification */}
      {hasNewUpdates && (
        <Alert className="bg-blue-900/20 border-blue-500">
          <Bell className="h-4 w-4" />
          <AlertDescription className="text-blue-200 flex items-center justify-between">
            <span>New shipment updates are available. Click refresh to see the latest changes.</span>
            <div className="flex items-center gap-2 ml-4">
              <Button
                size="sm"
                variant="ghost"
                onClick={handleRefresh}
                className="text-blue-100 bg-blue-600/20 hover:bg-blue-600/30 hover:text-white border border-blue-500/50 h-6 px-2"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Refresh
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleDismissUpdates}
                className="text-blue-100 bg-blue-600/20 hover:bg-red-600/30 hover:text-white border border-blue-500/50 hover:border-red-500 h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="lg:w-80">
            <ShipmentFilters />
          </div>
        )}

        {/* Main Table Area */}
        <div className="flex-1 space-y-4">
          {/* Search */}
          <ShipmentSearch />

          {/* Table or Card View */}
          {viewMode === 'table' ? (
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">
                  Shipment List
                  {isLoading && (
                    <Loader2 className="inline h-4 w-4 ml-2 animate-spin" />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <ShipmentsTable 
                  shipments={shipments}
                  isLoading={isLoading}
                  onSort={(field, direction) => {
                    // Handle sorting - this will be implemented in the table component
                  }}
                />
                
                {/* Pagination Controls for Table */}
                {pagination.totalCount > 0 && (
                  <div className="p-4 border-t border-slate-700">
                    <ShipmentsPagination
                      currentPage={pagination.page}
                      totalPages={pagination.totalPages}
                      totalCount={pagination.totalCount}
                      pageSize={pagination.pageSize}
                      currentCount={shipments.length}
                      isLoading={isLoading}
                      onPageChange={goToPage}
                      onPageSizeChange={changePageSize}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-white">
                  Shipment Cards
                  {isLoading && (
                    <Loader2 className="inline h-4 w-4 ml-2 animate-spin" />
                  )}
                </h2>
                <div className="text-sm text-slate-400">
                  {shipments.length} shipments
                </div>
              </div>
              
              {isLoading && shipments.length === 0 ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                    <p className="text-slate-300">Loading shipments...</p>
                  </div>
                </div>
              ) : (
                <>
                  <ShipmentCardList
                    shipments={shipments}
                    selectedShipments={selectedShipments}
                    expandedCards={expandedCards}
                    onToggleSelection={toggleShipmentSelection}
                    onToggleExpansion={toggleCardExpansion}
                    variant="standard"
                  />
                  
                  {/* Pagination Controls */}
                  {pagination.totalCount > 0 && (
                    <div className="mt-6 pt-4 border-t border-slate-700">
                      <ShipmentsPagination
                        currentPage={pagination.page}
                        totalPages={pagination.totalPages}
                        totalCount={pagination.totalCount}
                        pageSize={pagination.pageSize}
                        currentCount={shipments.length}
                        isLoading={isLoading}
                        onPageChange={goToPage}
                        onPageSizeChange={changePageSize}
                      />
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}