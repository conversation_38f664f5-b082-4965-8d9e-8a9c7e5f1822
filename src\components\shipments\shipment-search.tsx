'use client'

import { useState, useEffect } from 'react'
import { useShipmentStore } from '@/stores/shipment-store'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Search, X, Save, Settings } from 'lucide-react'

export function ShipmentSearch() {
  const {
    filters,
    savedSearches,
    activeSavedSearch,
    setSearchTerm,
    clearFilters,
    saveSearch,
    loadSavedSearch,
    deleteSavedSearch,
    toggleAdvancedSearch,
  } = useShipmentStore()

  const [searchInput, setSearchInput] = useState(filters.search)
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [saveSearchName, setSaveSearchName] = useState('')

  // Update local search input when store search changes
  useEffect(() => {
    setSearchInput(filters.search)
  }, [filters.search])

  // Update search term in store when input changes
  const handleSearchChange = (value: string) => {
    setSearchInput(value)
    setSearchTerm(value)
  }

  const handleSaveSearch = () => {
    if (saveSearchName.trim()) {
      saveSearch(saveSearchName.trim())
      setSaveSearchName('')
      setShowSaveDialog(false)
    }
  }

  const hasActiveFilters = 
    filters.search ||
    filters.status.length > 0 ||
    filters.customer_id.length > 0 ||
    filters.transportation_mode.length > 0 ||
    filters.date_range.start ||
    filters.date_range.end ||
    filters.origin_port_id.length > 0 ||
    filters.destination_port_id.length > 0

  return (
    <div className="space-y-4">
      {/* Main Search Bar */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search by shipment number, invoice, vessel name, or booking number..."
            value={searchInput}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500"
          />
          {searchInput && (
            <button
              onClick={() => handleSearchChange('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        
        <Button
          variant="outline"
          onClick={toggleAdvancedSearch}
          className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
        >
          <Settings className="h-4 w-4 mr-2" />
          Advanced
        </Button>
        
        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={clearFilters}
            className="border-orange-500 bg-orange-600/10 text-orange-100 hover:bg-orange-600/20 hover:border-orange-400"
          >
            <X className="h-4 w-4 mr-2" />
            Clear All
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-slate-400">Active filters:</span>
          
          {filters.search && (
            <Badge variant="secondary" className="bg-slate-700 text-slate-300">
              Search: "{filters.search}"
              <button
                onClick={() => handleSearchChange('')}
                className="ml-1 hover:text-white"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filters.status.map((status) => (
            <Badge key={status} variant="secondary" className="bg-slate-700 text-slate-300">
              Status: {status.replace('_', ' ')}
              <button
                onClick={() => {
                  // This would need to be implemented in the store
                  console.log('Remove status filter:', status)
                }}
                className="ml-1 hover:text-white"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          
          {filters.transportation_mode.map((mode) => (
            <Badge key={mode} variant="secondary" className="bg-slate-700 text-slate-300">
              Transport: {mode}
              <button
                onClick={() => {
                  // This would need to be implemented in the store
                  console.log('Remove transport mode filter:', mode)
                }}
                className="ml-1 hover:text-white"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          
          {(filters.date_range.start || filters.date_range.end) && (
            <Badge variant="secondary" className="bg-slate-700 text-slate-300">
              Date: {filters.date_range.start || 'Start'} - {filters.date_range.end || 'End'}
              <button
                onClick={() => {
                  // This would need to be implemented in the store
                  console.log('Remove date range filter')
                }}
                className="ml-1 hover:text-white"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}

      {/* Saved Searches */}
      {savedSearches.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-400">Saved Searches:</span>
            
            {hasActiveFilters && (
              <div className="flex items-center gap-2">
                {showSaveDialog ? (
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Search name..."
                      value={saveSearchName}
                      onChange={(e) => setSaveSearchName(e.target.value)}
                      className="w-32 h-8 bg-slate-700 border-slate-600 text-white"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveSearch()
                        if (e.key === 'Escape') setShowSaveDialog(false)
                      }}
                      autoFocus
                    />
                    <Button size="sm" onClick={handleSaveSearch} disabled={!saveSearchName.trim()}>
                      Save
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => setShowSaveDialog(false)}>
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowSaveDialog(true)}
                    className="text-slate-300 border-slate-600 hover:bg-slate-700"
                  >
                    <Save className="h-3 w-3 mr-1" />
                    Save
                  </Button>
                )}
              </div>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2">
            {savedSearches.map((savedSearch) => (
              <div key={savedSearch.id} className="flex items-center">
                <Button
                  size="sm"
                  variant={activeSavedSearch === savedSearch.id ? "default" : "outline"}
                  onClick={() => loadSavedSearch(savedSearch.id)}
                  className={activeSavedSearch === savedSearch.id 
                    ? "bg-blue-600 text-white" 
                    : "text-slate-300 border-slate-600 hover:bg-slate-700"
                  }
                >
                  {savedSearch.name}
                </Button>
                
                <button
                  onClick={() => deleteSavedSearch(savedSearch.id)}
                  className="ml-1 text-slate-400 hover:text-red-400"
                  title="Delete saved search"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}