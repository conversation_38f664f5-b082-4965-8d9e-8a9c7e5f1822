import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import DashboardPage from '../page'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  usePathname: () => '/dashboard',
}))

// Mock timers to speed up loading state
vi.useFakeTimers()

describe('Dashboard Page', () => {
  it('renders dashboard with title', async () => {
    render(<DashboardPage />)
    
    // Initially shows loading
    expect(screen.getByText('Loading dashboard...')).toBeInTheDocument()
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
    })
    
    expect(screen.getByText("Welcome back! Here's what's happening with your trading operations.")).toBeInTheDocument()
  })

  it('displays stat cards', async () => {
    render(<DashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Active Shipments')).toBeInTheDocument()
    })
    
    expect(screen.getByText('127')).toBeInTheDocument()
    expect(screen.getByText('Pending Documents')).toBeInTheDocument()
    expect(screen.getByText('8')).toBeInTheDocument()
    expect(screen.getByText('Drivers Online')).toBeInTheDocument()
    expect(screen.getByText('23')).toBeInTheDocument()
  })

  it('displays master data section', async () => {
    render(<DashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Master Data Management')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Companies')).toBeInTheDocument()
    expect(screen.getByText('Products')).toBeInTheDocument()
    expect(screen.getByText('Ports')).toBeInTheDocument()
    expect(screen.getByText('Drivers')).toBeInTheDocument()
  })

  it('displays recent activity section', async () => {
    render(<DashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Recent Activity')).toBeInTheDocument()
    })
    
    expect(screen.getByText(/New shipment EX3-BKK-250827-001 created/)).toBeInTheDocument()
  })

  it('displays system status section', async () => {
    render(<DashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete  
    await waitFor(() => {
      expect(screen.getByText('System Status')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Database')).toBeInTheDocument()
    expect(screen.getByText('API Services')).toBeInTheDocument()
  })

  it('displays quick actions section', async () => {
    render(<DashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Create New Shipment')).toBeInTheDocument()
    expect(screen.getByText('Manage Companies')).toBeInTheDocument()
    expect(screen.getByText('User Management')).toBeInTheDocument()
  })
})