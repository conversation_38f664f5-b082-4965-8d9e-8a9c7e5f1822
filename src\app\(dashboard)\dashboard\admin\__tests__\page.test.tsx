import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import AdminDashboardPage from '../page'

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  usePathname: () => '/dashboard/admin',
}))

// Mock auth hook
vi.mock('@/hooks/use-auth', () => ({
  useAuth: () => ({
    isAdmin: true,
    loading: false,
    profile: { role: 'admin' }
  }),
}))

// Mock timers to speed up loading state
vi.useFakeTimers()

describe('Admin Dashboard Page', () => {
  it('renders admin dashboard with title', async () => {
    render(<AdminDashboardPage />)
    
    // Initially shows loading
    expect(screen.getByText('Loading admin dashboard...')).toBeInTheDocument()
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    })
    
    expect(screen.getByText('System administration and management center')).toBeInTheDocument()
  })

  it('displays system stats cards', async () => {
    render(<AdminDashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Total Users')).toBeInTheDocument()
    })
    
    expect(screen.getByText('2,847')).toBeInTheDocument()
    expect(screen.getByText('Active Sessions')).toBeInTheDocument()
    expect(screen.getByText('156')).toBeInTheDocument()
    expect(screen.getByText('System Alerts')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
  })

  it('displays administration tools section', async () => {
    render(<AdminDashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('Administration Tools')).toBeInTheDocument()
    })
    
    expect(screen.getByText('User Management')).toBeInTheDocument()
    expect(screen.getByText('System Settings')).toBeInTheDocument()
    expect(screen.getByText('Audit Logs')).toBeInTheDocument()
    expect(screen.getByText('Security Center')).toBeInTheDocument()
  })

  it('displays system services status', async () => {
    render(<AdminDashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('System Services')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Web Server')).toBeInTheDocument()
    expect(screen.getByText('Database')).toBeInTheDocument()
    expect(screen.getByText('API Gateway')).toBeInTheDocument()
  })

  it('displays system resources section', async () => {
    render(<AdminDashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByText('System Resources')).toBeInTheDocument()
    })
    
    expect(screen.getByText('CPU Usage')).toBeInTheDocument()
    expect(screen.getByText('Memory Usage')).toBeInTheDocument()
    expect(screen.getByText('Disk Usage')).toBeInTheDocument()
  })
})

describe('Admin Dashboard Access Control', () => {
  it('shows access denied for non-admin users', async () => {
    // Mock non-admin user
    vi.mocked(vi.importActual('@/hooks/use-auth')).useAuth = () => ({
      isAdmin: false,
      loading: false,
      profile: { role: 'cs' }
    })
    
    render(<AdminDashboardPage />)
    
    // Fast-forward timers to complete loading
    vi.advanceTimersByTime(1000)
    
    await waitFor(() => {
      expect(screen.getByText('Access Denied')).toBeInTheDocument()
    })
    
    expect(screen.getByText("You don't have permission to access the admin dashboard.")).toBeInTheDocument()
  })
})