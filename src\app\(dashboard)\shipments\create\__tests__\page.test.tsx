import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ShipmentCreatePage from '../page'
import { useRouter } from 'next/navigation'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

// Mock the hooks
vi.mock('@/stores/shipment-creation-store', () => ({
  useShipmentCreationStore: () => ({
    currentStep: 1,
    formData: {
      transportMode: undefined,
      customerId: '',
      shipperId: '',
      consigneeId: '',
      notifyPartyId: '',
      portOfLoading: '',
      portOfDischarge: '',
      containerType: undefined,
      containerQuantity: 1,
      cargoDescription: '',
      grossWeight: 0,
      documents: [],
    },
    validation: {
      isValid: false,
      errors: {},
      touchedFields: new Set(),
    },
    isLoading: false,
    error: null,
    relationships: {
      recommendations: {
        shippers: [],
        consignees: [],
        notifyParties: [],
        products: [],
        routes: [],
      },
      analysis: null,
    },
    setStep: vi.fn(),
    updateFormData: vi.fn(),
    resetForm: vi.fn(),
    isStepValid: vi.fn(() => false),
    canSubmit: vi.fn(() => false),
    getFormCompletionPercentage: vi.fn(() => 0),
  }),
}))

vi.mock('@/hooks/use-shipments', () => ({
  useShipments: () => ({
    createShipment: vi.fn().mockResolvedValue({ id: 'ship_123' }),
    loading: false,
    error: null,
  }),
}))

vi.mock('@/hooks/use-companies', () => ({
  useCompanies: () => ({
    companies: [
      { id: 'comp_1', name: 'Company One', type: 'customer' },
      { id: 'comp_2', name: 'Company Two', type: 'shipper' },
    ],
    loading: false,
    error: null,
  }),
}))

vi.mock('@/hooks/use-ports', () => ({
  usePorts: () => ({
    ports: [
      { id: 'port_1', code: 'THBKK', name: 'Bangkok, Thailand' },
      { id: 'port_2', code: 'HKHKG', name: 'Hong Kong' },
    ],
    loading: false,
    error: null,
  }),
}))

vi.mock('@/hooks/use-shipment-relationships', () => ({
  useShipmentRelationships: () => ({
    recommendations: {
      shippers: [],
      consignees: [],
      notifyParties: [],
      products: [],
      routes: [],
    },
    loading: false,
    error: null,
    getRecommendations: vi.fn(),
    analyzeRelationship: vi.fn(),
  }),
}))

// Mock the components that might not be available in test environment
vi.mock('@/components/forms/shipment-form/transport-mode-modal', () => ({
  TransportModeModal: ({
    open,
    onSelect,
  }: {
    open: boolean
    onSelect: (mode: string) => void
  }) =>
    open ? (
      <div data-testid="transport-mode-modal">
        <button onClick={() => onSelect('sea')}>Sea Freight</button>
        <button onClick={() => onSelect('land')}>Land Transport</button>
        <button onClick={() => onSelect('rail')}>Rail Transport</button>
      </div>
    ) : null,
}))

vi.mock('@/components/forms/consignee-notify-party-integration', () => ({
  ConsigneeNotifyPartyIntegration: ({
    onUpdate,
  }: {
    onUpdate: (data: any) => void
  }) => (
    <div data-testid="consignee-notify-integration">
      <button
        onClick={() =>
          onUpdate({ consigneeId: 'cons_1', notifyPartyId: 'notify_1' })
        }
      >
        Select Relationship
      </button>
    </div>
  ),
}))

describe('ShipmentCreatePage', () => {
  const mockPush = vi.fn()

  beforeEach(() => {
    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      replace: vi.fn(),
      prefetch: vi.fn(),
    } as any)

    vi.clearAllMocks()
  })

  it('should render page title and description', () => {
    render(<ShipmentCreatePage />)

    expect(screen.getByText('Create New Shipment')).toBeInTheDocument()
    expect(
      screen.getByText('Create a new shipment with intelligent recommendations')
    ).toBeInTheDocument()
  })

  it('should render progress indicator', () => {
    render(<ShipmentCreatePage />)

    // Check for progress steps
    expect(screen.getByText('Transport')).toBeInTheDocument()
    expect(screen.getByText('Stakeholders')).toBeInTheDocument()
    expect(screen.getByText('Route')).toBeInTheDocument()
    expect(screen.getByText('Container')).toBeInTheDocument()
    expect(screen.getByText('Cargo')).toBeInTheDocument()
    expect(screen.getByText('Documents')).toBeInTheDocument()
  })

  it('should show transport mode selection modal on initial load', async () => {
    render(<ShipmentCreatePage />)

    // Modal should be open initially since no transport mode is selected
    expect(screen.getByTestId('transport-mode-modal')).toBeInTheDocument()
  })

  it('should handle transport mode selection', async () => {
    const user = userEvent.setup()
    render(<ShipmentCreatePage />)

    const seaButton = screen.getByText('Sea Freight')
    await user.click(seaButton)

    // The modal should close and form should update
    await waitFor(() => {
      expect(
        screen.queryByTestId('transport-mode-modal')
      ).not.toBeInTheDocument()
    })
  })

  it('should show step 1 content when transport mode is not selected', () => {
    render(<ShipmentCreatePage />)

    expect(screen.getByText('Transportation Mode')).toBeInTheDocument()
    expect(
      screen.getByText('Select your preferred mode of transportation')
    ).toBeInTheDocument()
  })

  it('should render form fields for each step', () => {
    // Mock the store to return step 2 with transport mode selected
    vi.mocked(
      require('@/stores/shipment-creation-store').useShipmentCreationStore
    ).mockReturnValue({
      currentStep: 2,
      formData: {
        transportMode: 'sea',
        customerId: '',
        shipperId: '',
        consigneeId: '',
        notifyPartyId: '',
        portOfLoading: '',
        portOfDischarge: '',
        containerType: undefined,
        containerQuantity: 1,
        cargoDescription: '',
        grossWeight: 0,
        documents: [],
      },
      validation: {
        isValid: false,
        errors: {},
        touchedFields: new Set(),
      },
      isLoading: false,
      error: null,
      relationships: {
        recommendations: {
          shippers: [],
          consignees: [],
          notifyParties: [],
          products: [],
          routes: [],
        },
        analysis: null,
      },
      setStep: vi.fn(),
      updateFormData: vi.fn(),
      resetForm: vi.fn(),
      isStepValid: vi.fn(() => false),
      canSubmit: vi.fn(() => false),
      getFormCompletionPercentage: vi.fn(() => 20),
    })

    render(<ShipmentCreatePage />)

    expect(screen.getByText('Stakeholder Information')).toBeInTheDocument()
    expect(
      screen.getByText('Select customer, shipper, consignee, and notify party')
    ).toBeInTheDocument()
  })

  it('should show navigation buttons', () => {
    render(<ShipmentCreatePage />)

    expect(screen.getByText('Previous')).toBeInTheDocument()
    expect(screen.getByText('Next')).toBeInTheDocument()
  })

  it('should handle step navigation', async () => {
    const mockSetStep = vi.fn()

    vi.mocked(
      require('@/stores/shipment-creation-store').useShipmentCreationStore
    ).mockReturnValue({
      currentStep: 2,
      formData: {
        transportMode: 'sea',
      },
      setStep: mockSetStep,
      isStepValid: vi.fn(() => true),
      canSubmit: vi.fn(() => false),
      // ... other required properties
    })

    const user = userEvent.setup()
    render(<ShipmentCreatePage />)

    const nextButton = screen.getByText('Next')
    await user.click(nextButton)

    expect(mockSetStep).toHaveBeenCalledWith(3)
  })

  it('should show cancel confirmation dialog', async () => {
    const user = userEvent.setup()
    render(<ShipmentCreatePage />)

    const cancelButton = screen.getByText('Cancel')
    await user.click(cancelButton)

    expect(
      screen.getByText('Are you sure you want to cancel?')
    ).toBeInTheDocument()
    expect(screen.getByText('Your progress will be lost.')).toBeInTheDocument()
  })

  it('should handle cancel confirmation', async () => {
    const user = userEvent.setup()
    const mockResetForm = vi.fn()

    vi.mocked(
      require('@/stores/shipment-creation-store').useShipmentCreationStore
    ).mockReturnValue({
      resetForm: mockResetForm,
      // ... other required properties with default values
      currentStep: 1,
      formData: {},
      validation: { isValid: false, errors: {}, touchedFields: new Set() },
      isLoading: false,
      error: null,
      relationships: { recommendations: {}, analysis: null },
    })

    render(<ShipmentCreatePage />)

    const cancelButton = screen.getByText('Cancel')
    await user.click(cancelButton)

    const confirmButton = screen.getByText('Yes, cancel')
    await user.click(confirmButton)

    expect(mockResetForm).toHaveBeenCalled()
    expect(mockPush).toHaveBeenCalledWith('/shipments')
  })

  it('should show save draft button when form has data', () => {
    vi.mocked(
      require('@/stores/shipment-creation-store').useShipmentCreationStore
    ).mockReturnValue({
      formData: {
        transportMode: 'sea',
        customerId: 'cust_1',
      },
      getFormCompletionPercentage: vi.fn(() => 30),
      // ... other required properties
      currentStep: 2,
      validation: { isValid: false, errors: {}, touchedFields: new Set() },
      isLoading: false,
      error: null,
      relationships: { recommendations: {}, analysis: null },
    })

    render(<ShipmentCreatePage />)

    expect(screen.getByText('Save Draft')).toBeInTheDocument()
  })

  it('should show submit button on final step', () => {
    vi.mocked(
      require('@/stores/shipment-creation-store').useShipmentCreationStore
    ).mockReturnValue({
      currentStep: 6,
      formData: {
        transportMode: 'sea',
        customerId: 'cust_1',
        shipperId: 'ship_1',
        portOfLoading: 'THBKK',
        portOfDischarge: 'HKHKG',
        containerType: 'dry_20',
        cargoDescription: 'Test cargo',
        grossWeight: 1000,
      },
      canSubmit: vi.fn(() => true),
      isStepValid: vi.fn(() => true),
      // ... other required properties
      validation: { isValid: true, errors: {}, touchedFields: new Set() },
      isLoading: false,
      error: null,
      relationships: { recommendations: {}, analysis: null },
    })

    render(<ShipmentCreatePage />)

    expect(screen.getByText('Create Shipment')).toBeInTheDocument()
  })

  it('should handle form submission', async () => {
    const mockCreateShipment = vi.fn().mockResolvedValue({ id: 'ship_123' })

    vi.mocked(require('@/hooks/use-shipments').useShipments).mockReturnValue({
      createShipment: mockCreateShipment,
      loading: false,
      error: null,
    })

    vi.mocked(
      require('@/stores/shipment-creation-store').useShipmentCreationStore
    ).mockReturnValue({
      currentStep: 6,
      formData: {
        transportMode: 'sea',
        customerId: 'cust_1',
        shipperId: 'ship_1',
        portOfLoading: 'THBKK',
        portOfDischarge: 'HKHKG',
        containerType: 'dry_20',
        cargoDescription: 'Test cargo',
        grossWeight: 1000,
      },
      canSubmit: vi.fn(() => true),
      isStepValid: vi.fn(() => true),
      resetForm: vi.fn(),
      // ... other required properties
      validation: { isValid: true, errors: {}, touchedFields: new Set() },
      isLoading: false,
      error: null,
      relationships: { recommendations: {}, analysis: null },
    })

    const user = userEvent.setup()
    render(<ShipmentCreatePage />)

    const submitButton = screen.getByText('Create Shipment')
    await user.click(submitButton)

    expect(mockCreateShipment).toHaveBeenCalled()

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/shipments/ship_123')
    })
  })

  it('should show error message when form submission fails', async () => {
    const mockCreateShipment = vi
      .fn()
      .mockRejectedValue(new Error('Creation failed'))

    vi.mocked(require('@/hooks/use-shipments').useShipments).mockReturnValue({
      createShipment: mockCreateShipment,
      loading: false,
      error: 'Creation failed',
    })

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(<ShipmentCreatePage />)

    // Mock store state for final step
    vi.mocked(
      require('@/stores/shipment-creation-store').useShipmentCreationStore
    ).mockReturnValue({
      currentStep: 6,
      canSubmit: vi.fn(() => true),
      formData: {
        transportMode: 'sea',
        customerId: 'cust_1',
        cargoDescription: 'Test',
      },
      setError: vi.fn(),
      // ... other required properties
      validation: { isValid: true, errors: {}, touchedFields: new Set() },
      isLoading: false,
      error: null,
      relationships: { recommendations: {}, analysis: null },
    })

    const user = userEvent.setup()
    const submitButton = screen.getByText('Create Shipment')
    await user.click(submitButton)

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to create shipment:',
        expect.any(Error)
      )
    })

    consoleSpy.mockRestore()
  })

  it('should show loading state during form submission', async () => {
    vi.mocked(require('@/hooks/use-shipments').useShipments).mockReturnValue({
      createShipment: vi.fn().mockImplementation(() => new Promise(() => {})), // Never resolves
      loading: true,
      error: null,
    })

    render(<ShipmentCreatePage />)

    expect(screen.getByText('Creating...')).toBeInTheDocument()
  })

  it('should be accessible with proper ARIA attributes', () => {
    render(<ShipmentCreatePage />)

    const main = screen.getByRole('main')
    expect(main).toBeInTheDocument()

    const progressList = screen.getByRole('list')
    expect(progressList).toBeInTheDocument()

    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toHaveAttribute('type')
    })
  })

  it('should handle keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<ShipmentCreatePage />)

    await user.tab()

    // Should focus on first interactive element
    expect(document.activeElement).toBeDefined()
  })
})
