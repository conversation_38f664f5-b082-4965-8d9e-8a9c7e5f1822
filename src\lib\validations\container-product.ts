import { z } from 'zod'
import {
  containerTypeSchema,
  containerSizeSchema,
  containerStatusSchema,
  packagingTypeSchema,
} from './shipment'

// Base container validation schema
export const containerValidationSchema = z.object({
  shipment_id: z.string().uuid('Invalid shipment ID'),
  container_number: z.string().min(1, 'Container number is required').optional(),
  container_type: containerTypeSchema,
  container_size: containerSizeSchema,
  seal_number: z
    .string()
    .min(1, 'Seal number must be at least 1 character')
    .max(50, 'Seal number must be at most 50 characters')
    .optional(),
  tare_weight: z
    .number()
    .min(0, 'Tare weight must be non-negative')
    .max(50000, 'Tare weight cannot exceed 50,000 kg')
    .optional(),
  gross_weight: z
    .number()
    .min(0, 'Gross weight must be non-negative')
    .max(50000, 'Gross weight cannot exceed 50,000 kg')
    .optional(),
  volume: z
    .number()
    .min(0, 'Volume must be non-negative')
    .max(100, 'Volume cannot exceed 100 cubic meters')
    .optional(),
  temperature: z
    .string()
    .max(100, 'Temperature requirement must be at most 100 characters')
    .optional(),
  vent: z
    .string()
    .max(100, 'Vent requirement must be at most 100 characters')
    .optional(),
  status: containerStatusSchema.default('empty'),
})

// Container creation validation with additional checks
export const containerCreationSchema = containerValidationSchema.extend({
  container_type: containerTypeSchema,
  container_size: containerSizeSchema,
})

// Container update validation (all fields optional except ID)
export const containerUpdateSchema = containerValidationSchema
  .partial()
  .extend({
    id: z.string().uuid('Invalid container ID'),
  })

// Shipment product validation schema with enhanced validation
export const shipmentProductValidationSchema = z
  .object({
    shipment_id: z.string().uuid('Invalid shipment ID'),
    container_id: z.string().uuid('Invalid container ID').optional(),
    product_id: z.string().uuid('Invalid product ID'),
    product_description: z
      .string()
      .max(500, 'Product description must be at most 500 characters')
      .optional(),
    quantity: z
      .number()
      .min(0.01, 'Quantity must be greater than 0')
      .max(1000000, 'Quantity cannot exceed 1,000,000'),
    unit_of_measure_id: z.string().uuid('Invalid unit of measure ID'),
    unit_price_cif: z
      .number()
      .min(0, 'CIF price must be non-negative')
      .max(1000000, 'CIF price cannot exceed 1,000,000'),
    unit_price_fob: z
      .number()
      .min(0, 'FOB price must be non-negative')
      .max(1000000, 'FOB price cannot exceed 1,000,000'),
    total_value_cif: z
      .number()
      .min(0, 'Total CIF value must be non-negative')
      .max(100000000, 'Total CIF value cannot exceed 100,000,000'),
    total_value_fob: z
      .number()
      .min(0, 'Total FOB value must be non-negative')
      .max(100000000, 'Total FOB value cannot exceed 100,000,000'),
    gross_weight: z
      .number()
      .min(0, 'Gross weight must be non-negative')
      .max(100000, 'Gross weight cannot exceed 100,000 kg')
      .default(0),
    net_weight: z
      .number()
      .min(0, 'Net weight must be non-negative')
      .max(100000, 'Net weight cannot exceed 100,000 kg')
      .default(0),
    shipping_mark: z
      .string()
      .max(200, 'Shipping mark must be at most 200 characters')
      .default('N/M'),
    mfg_date: z
      .string()
      .refine(
        date => {
          if (!date) return true // Optional field
          const parsed = new Date(date)
          const now = new Date()
          const minDate = new Date()
          minDate.setFullYear(now.getFullYear() - 5) // Max 5 years ago
          return parsed >= minDate && parsed <= now
        },
        { message: 'Manufacturing date must be within the last 5 years' }
      )
      .optional(),
    expire_date: z
      .string()
      .refine(
        date => {
          if (!date) return true // Optional field
          const parsed = new Date(date)
          const now = new Date()
          const maxDate = new Date()
          maxDate.setFullYear(now.getFullYear() + 10) // Max 10 years in future
          return parsed >= now && parsed <= maxDate
        },
        { message: 'Expiration date must be in the future and within 10 years' }
      )
      .optional(),
    lot_number: z
      .string()
      .min(1, 'Lot number must be at least 1 character')
      .max(50, 'Lot number must be at most 50 characters')
      .optional(),
    packaging_type: packagingTypeSchema,
    quality_grade: z
      .string()
      .max(50, 'Quality grade must be at most 50 characters')
      .optional(),
  })
  .refine(
    data => {
      // Validate that gross weight >= net weight
      return data.gross_weight >= data.net_weight
    },
    {
      message: 'Gross weight must be greater than or equal to net weight',
      path: ['gross_weight'],
    }
  )
  .refine(
    data => {
      // Validate that total values match quantity × net_weight × unit_prices
      const expected_cif = data.unit_price_cif * data.quantity * data.net_weight
      const expected_fob = data.unit_price_fob * data.quantity * data.net_weight
      const cif_diff = Math.abs(data.total_value_cif - expected_cif)
      const fob_diff = Math.abs(data.total_value_fob - expected_fob)
      
      // Allow small rounding differences (0.01)
      return cif_diff <= 0.01 && fob_diff <= 0.01
    },
    {
      message: 'Total values must equal quantity × net_weight × unit_price',
      path: ['total_value_cif'],
    }
  )
  .refine(
    data => {
      // Validate date sequence: mfg_date < expire_date
      if (!data.mfg_date || !data.expire_date) return true
      
      const mfgDate = new Date(data.mfg_date)
      const expDate = new Date(data.expire_date)
      
      return mfgDate < expDate
    },
    {
      message: 'Expiration date must be after manufacturing date',
      path: ['expire_date'],
    }
  )

// Container capacity validation
export const containerCapacityValidationSchema = z.object({
  container_type: containerTypeSchema,
  container_size: containerSizeSchema,
  total_weight: z.number().min(0),
  total_volume: z.number().min(0),
})

// Container allocation validation
export const containerAllocationSchema = z.object({
  shipment_id: z.string().uuid(),
  customer_id: z.string().uuid(),
  products: z.array(
    z.object({
      product_id: z.string().uuid(),
      quantity: z.number().min(0.01),
      packaging_type: packagingTypeSchema,
    })
  ),
  auto_generate: z.boolean().default(true),
  container_preferences: z
    .object({
      preferred_type: containerTypeSchema.optional(),
      preferred_size: containerSizeSchema.optional(),
      max_containers: z.number().min(1).max(20).optional(),
    })
    .optional(),
})

// Product allocation to container validation
export const productAllocationSchema = z.object({
  container_id: z.string().uuid(),
  allocations: z.array(
    z.object({
      product_id: z.string().uuid(),
      quantity: z.number().min(0.01),
      unit_price_cif: z.number().min(0),
      unit_price_fob: z.number().min(0),
    })
  ),
})

// Weight calculation validation
export const weightCalculationSchema = z.object({
  customer_id: z.string().uuid(),
  products: z.array(
    z.object({
      product_id: z.string().uuid(),
      quantity: z.number().min(0.01),
      packaging_type: packagingTypeSchema,
      unit_price_cif: z.number().min(0).optional(),
      unit_price_fob: z.number().min(0).optional(),
    })
  ),
})

// Master data consistency validation
export const masterDataConsistencySchema = z.object({
  shipment_id: z.string().uuid(),
  customer_id: z.string().uuid(),
  containers: z.array(
    z.object({
      id: z.string().uuid(),
      container_type: containerTypeSchema,
      container_size: containerSizeSchema,
    })
  ),
  products: z.array(
    z.object({
      product_id: z.string().uuid(),
      container_id: z.string().uuid(),
      quantity: z.number().min(0.01),
      packaging_type: packagingTypeSchema,
    })
  ),
})

// Bulk container creation validation
export const bulkContainerCreationSchema = z.object({
  shipment_id: z.string().uuid(),
  containers: z
    .array(containerCreationSchema.omit({ shipment_id: true }))
    .min(1, 'At least one container is required')
    .max(20, 'Cannot create more than 20 containers at once'),
})

// Bulk product allocation validation
export const bulkProductAllocationSchema = z.object({
  shipment_id: z.string().uuid(),
  products: z
    .array(shipmentProductValidationSchema.omit({ shipment_id: true }))
    .min(1, 'At least one product is required')
    .max(100, 'Cannot allocate more than 100 products at once'),
})

// Container edit form validation
export const containerEditFormSchema = containerUpdateSchema.extend({
  // Additional UI-specific validations
  seal_number: z
    .string()
    .regex(/^[A-Z0-9]{1,20}$/, 'Seal number must be alphanumeric and uppercase')
    .optional()
    .or(z.literal('')),
  container_number: z
    .string()
    .regex(/^[A-Z]{4}[0-9]{7}$/, 'Container number must follow format: ABCD1234567')
    .optional()
    .or(z.literal('')),
})

// Form submission validation schema
export const containerProductFormSchema = z.object({
  containers: z.array(containerEditFormSchema),
  products: z.array(shipmentProductValidationSchema),
  generate_automatically: z.boolean().default(true),
  validation_overrides: z
    .array(
      z.object({
        type: z.enum(['weight', 'volume', 'compatibility']),
        container_id: z.string().uuid(),
        acknowledged: z.boolean(),
        reason: z.string().min(10, 'Override reason must be at least 10 characters'),
      })
    )
    .optional(),
})

// API response validation schemas
export const containerGenerationResponseSchema = z.object({
  success: z.boolean(),
  containers: z.array(
    z.object({
      id: z.string().uuid(),
      container_number: z.string(),
      container_type: containerTypeSchema,
      container_size: containerSizeSchema,
      volume: z.number(),
      status: containerStatusSchema,
    })
  ),
  product_allocations: z.array(
    z.object({
      id: z.string().uuid(),
      container_id: z.string().uuid(),
      product_id: z.string().uuid(),
      quantity: z.number(),
      total_value_cif: z.number(),
      total_value_fob: z.number(),
      gross_weight: z.number(),
      net_weight: z.number(),
    })
  ),
  weight_summary: z.object({
    total_gross_weight: z.number(),
    total_net_weight: z.number(),
    total_value_cif: z.number(),
    total_value_fob: z.number(),
    container_count: z.number(),
    overweight_containers: z.number(),
  }),
  validation_warnings: z.array(z.string()),
  validation_errors: z.array(z.string()),
})

// Error response validation
export const containerProductErrorSchema = z.object({
  code: z.string(),
  message: z.string(),
  details: z.any().optional(),
  timestamp: z.string(),
})

// Validation helper functions
export const validateContainerCapacity = (
  container_type: string,
  container_size: string,
  weight: number,
  volume: number
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Container specifications for validation
  const SPECS = {
    '20ft': { dry: { volume: 33.2, weight: 28200 }, reefer: { volume: 28.3, weight: 27400 } },
    '40ft': { dry: { volume: 67.7, weight: 26700 }, reefer: { volume: 59.3, weight: 27300 } },
    '40hc': { dry: { volume: 76.4, weight: 26580 }, reefer: { volume: 67.3, weight: 27300 } },
    '45ft': { dry: { volume: 86.0, weight: 26000 }, reefer: { volume: 76.0, weight: 26800 } },
  } as any

  const spec = SPECS[container_size]?.[container_type]
  if (!spec) {
    errors.push('Invalid container type/size combination')
    return { isValid: false, errors }
  }

  if (weight > spec.weight * 0.95) {
    errors.push(`Weight ${weight}kg exceeds safe capacity ${spec.weight * 0.95}kg`)
  }

  if (volume > spec.volume * 0.9) {
    errors.push(`Volume ${volume}m³ exceeds safe capacity ${spec.volume * 0.9}m³`)
  }

  return { isValid: errors.length === 0, errors }
}

export const validatePackagingCompatibility = (
  packaging_types: string[]
): { isCompatible: boolean; warnings: string[] } => {
  const warnings: string[] = []
  
  const COMPATIBILITY = {
    Bag: ['Bag', 'Carton'],
    'Plastic Basket': ['Plastic Basket'],
    Carton: ['Carton', 'Bag'],
  } as any

  if (packaging_types.length <= 1) {
    return { isCompatible: true, warnings }
  }

  const first_type = packaging_types[0]
  const compatible_types = COMPATIBILITY[first_type] || []

  for (const type of packaging_types.slice(1)) {
    if (!compatible_types.includes(type)) {
      warnings.push(`${first_type} and ${type} packaging may not be compatible`)
    }
  }

  return { isCompatible: warnings.length === 0, warnings }
}

// Type exports
export type ContainerValidation = z.infer<typeof containerValidationSchema>
export type ContainerCreation = z.infer<typeof containerCreationSchema>
export type ContainerUpdate = z.infer<typeof containerUpdateSchema>
export type ShipmentProductValidation = z.infer<typeof shipmentProductValidationSchema>
export type ContainerCapacityValidation = z.infer<typeof containerCapacityValidationSchema>
export type ContainerAllocation = z.infer<typeof containerAllocationSchema>
export type ProductAllocation = z.infer<typeof productAllocationSchema>
export type WeightCalculation = z.infer<typeof weightCalculationSchema>
export type MasterDataConsistency = z.infer<typeof masterDataConsistencySchema>
export type BulkContainerCreation = z.infer<typeof bulkContainerCreationSchema>
export type BulkProductAllocation = z.infer<typeof bulkProductAllocationSchema>
export type ContainerEditForm = z.infer<typeof containerEditFormSchema>
export type ContainerProductForm = z.infer<typeof containerProductFormSchema>
export type ContainerGenerationResponse = z.infer<typeof containerGenerationResponseSchema>
export type ContainerProductError = z.infer<typeof containerProductErrorSchema>