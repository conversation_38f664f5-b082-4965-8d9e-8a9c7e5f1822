'use client'

import React from 'react'
import { User, Loader2 } from 'lucide-react'
import { useDriverPhotoUtils } from '@/hooks/use-drivers'
import type { Driver } from '@/stores/driver-store'

interface DriverPhotoProps {
  driver: Driver
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function DriverPhoto({
  driver,
  size = 'md',
  className,
}: DriverPhotoProps) {
  const { usePhotoUrl, hasPhoto } = useDriverPhotoUtils()
  const { photoUrl, loading } = usePhotoUrl(driver)

  // Size variants
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-16 h-16',
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-5 w-5',
    lg: 'h-8 w-8',
  }

  // Use custom className if provided, otherwise use size classes
  const containerClass =
    className?.includes('w-') && className?.includes('h-')
      ? `rounded-full bg-slate-600 flex items-center justify-center overflow-hidden ${className}`
      : `${sizeClasses[size]} rounded-full bg-slate-600 flex items-center justify-center overflow-hidden ${className || ''}`

  return (
    <div className={containerClass}>
      {loading ? (
        <Loader2 className={`${iconSizes[size]} text-slate-400 animate-spin`} />
      ) : hasPhoto(driver) && photoUrl ? (
        <img
          src={photoUrl}
          alt={`${driver.driver_first_name} ${driver.driver_last_name} photo`}
          className="w-full h-full object-cover"
          onError={e => {
            console.error('Failed to load driver photo:', e)
            // Hide image on error and show fallback
            e.currentTarget.style.display = 'none'
          }}
        />
      ) : (
        <User className={`${iconSizes[size]} text-slate-400`} />
      )}
    </div>
  )
}
