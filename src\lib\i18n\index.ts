'use client'

export type Locale = 'en' | 'th'

export interface I18nConfig {
  defaultLocale: Locale
  locales: Locale[]
}

export const i18nConfig: I18nConfig = {
  defaultLocale: 'en',
  locales: ['en', 'th']
}

// Simple translation hook for mobile driver interface
export function useTranslations(locale: Locale = 'en') {
  const t = (key: string, fallback?: string): string => {
    const translation = translations[locale]?.[key] || translations.en[key] || fallback || key
    return translation
  }

  return { t }
}

// Translation dictionaries
const translations = {
  en: {
    // Dashboard
    'dashboard.welcome': 'Welcome',
    'dashboard.driver': 'Driver Dashboard',
    'dashboard.offline': '(Offline)',
    'dashboard.signOut': 'Sign Out',
    'dashboard.activeJobs': 'Active Jobs',
    'dashboard.thisWeek': 'This Week',
    'dashboard.refreshing': 'Refreshing...',
    'dashboard.refresh': 'Refresh Assignments',
    'dashboard.pullToRefresh': 'Pull to refresh',
    'dashboard.releaseToRefresh': 'Release to refresh',
    'dashboard.offlineCached': 'Offline - Showing cached data',
    'dashboard.yourAssignments': 'Your Assignments',
    'dashboard.noAssignments': 'No Assignments Yet',
    'dashboard.noAssignmentsDesc': 'Your transportation assignments will appear here',

    // Shipment Card
    'shipment.customer': 'Customer',
    'shipment.factory': 'Factory', 
    'shipment.products': 'products',
    'shipment.moreProducts': 'more products',
    'shipment.pickupContainer': 'Pickup Container Location',
    'shipment.pickupProduct': 'Pickup Product Location',
    'shipment.delivery': 'Delivery Location',
    'shipment.locationTBA': 'Location TBA',
    'shipment.etd': 'ETD',
    'shipment.eta': 'ETA',
    'shipment.assigned': 'Assigned',
    'shipment.distance': 'Distance',
    'shipment.km': 'km',
    'shipment.notSet': 'Not set',
    'shipment.tapToUpdate': 'Tap to update status',
    'shipment.updateStatus': 'Update Status',
    'shipment.emptyContainerPickedTime': 'Container Picked',
    'shipment.arrivedAtFactoryTime': 'Arrived Factory',
    'shipment.loadingStartedTime': 'Loading Started',
    'shipment.departedFactoryTime': 'Departed Factory',
    'shipment.containerReturnedTime': 'Container Returned',
    'shipment.container': 'Container',
    'shipment.containerNumber': 'Container Number',
    'shipment.sealNumber': 'Seal Number',
    'shipment.type': 'Type',
    'shipment.size': 'Size',
    'shipment.edit': 'Edit',

    // Packaging Types
    'packaging.bag': 'Bag',
    'packaging.plastic_basket': 'Plastic Basket',
    'packaging.carton': 'Carton',
    'packaging.boxes': 'Boxes',
    'packaging.pallets': 'Pallets',
    'packaging.bags': 'Bags',
    'packaging.crates': 'Crates',
    'packaging.cartons': 'Cartons',
    'packaging.pieces': 'Pieces',
    'packaging.units': 'Units',

    // Status Labels
    'status.booking_confirmed': 'Booking Confirmed',
    'status.transport_assigned': 'Transport Assigned',
    'status.driver_assigned': 'Driver Assigned',
    'status.empty_container_picked': 'Empty Container Picked',
    'status.arrived_at_factory': 'Arrived at Factory',
    'status.loading_started': 'Loading Started',
    'status.departed_factory': 'Departed Factory',
    'status.container_returned': 'Container Returned',
    'status.shipped': 'Shipped',
    'status.arrived': 'Arrived',
    'status.completed': 'Completed',
    'status.cancelled': 'Cancelled',

    // Status Update Form
    'statusUpdate.title': 'Update Status',
    'statusUpdate.currentStatus': 'Current Status',
    'statusUpdate.nextStatus': 'Next Status',
    'statusUpdate.noUpdatesAvailable': 'No further status updates available',
    'statusUpdate.photos': 'Photos',
    'statusUpdate.photosRequired': 'photos required',
    'statusUpdate.notes': 'Notes',
    'statusUpdate.notesPlaceholder': 'Add notes about this status update...',
    'statusUpdate.location': 'Location',
    'statusUpdate.updating': 'Updating...',
    'statusUpdate.update': 'Update Status',
    'statusUpdate.success': 'Status updated successfully',
    'statusUpdate.error': 'Failed to update status',
    'statusUpdate.selectStatus': 'Please select a status to update to',
    'statusUpdate.gpsRequired': 'GPS location is required',
    'statusUpdate.notesLimit': 'Notes must be 500 characters or less',
    'statusUpdate.confirm': 'Confirm',
    'statusUpdate.step': 'Step',
    'statusUpdate.of': 'of',
    
    // Status Update Form Steps
    'statusUpdate.selectStatusStep': 'Select Status',
    'statusUpdate.addPhotosStep': 'Add Photos',
    'statusUpdate.confirmLocationStep': 'Confirm Location',
    'statusUpdate.addNotesStep': 'Add Notes',
    'statusUpdate.confirmUpdateStep': 'Confirm Update',
    'statusUpdate.statusUpdatedSuccessfully': 'Status Updated Successfully',
    'statusUpdate.shipmentStatusUpdatedTo': 'Shipment status has been updated to',
    'statusUpdate.photoRequirements': 'Photo Requirements',
    'statusUpdate.photosRequiredFor': 'photos required for',
    'statusUpdate.notesOptional': 'Notes (Optional)',
    'statusUpdate.notesOptionalDesc': 'Optional notes for this status update',
    'statusUpdate.confirmStatusUpdate': 'Confirm Status Update',
    'statusUpdate.statusChange': 'Status Change',
    'statusUpdate.photos': 'Photos',
    'statusUpdate.photo': 'photo',
    'statusUpdate.photos': 'photos',
    'statusUpdate.location': 'Location',
    'statusUpdate.notes': 'Notes',
    'statusUpdate.notCaptured': 'Not captured',
    'statusUpdate.uploadingPhotos': 'Uploading Photos',
    'statusUpdate.previous': 'Previous',
    'statusUpdate.next': 'Next',
    'statusUpdate.uploading': 'Uploading...',

    // Status Selector
    'statusSelector.currentStatus': 'Current Status',
    'statusSelector.complete': 'Complete',
    'statusSelector.updateTo': 'Update To',
    'statusSelector.progress': 'Progress',
    'statusSelector.final': 'Final',
    'statusSelector.next': 'Next',
    'statusSelector.shipmentComplete': 'Shipment Complete',
    'statusSelector.finalStatus': 'Final Status',
    'statusSelector.noFurtherUpdates': 'No further status updates available',

    // Photo Preview Grid
    'photoPreview.willAppearHere': 'Photos will appear here after capture or selection',
    'photoPreview.selectedPhotos': 'Selected Photos',
    'photoPreview.dragToReorder': 'Drag to reorder',
    'photoPreview.photo': 'Photo',
    'photoPreview.fullSize': 'Full size',
    'photoPreview.dragPhotosToReorder': 'Drag photos to reorder them. The first photo will be the primary image.',

    // Location Capture
    'locationCapture.notAvailable': 'Location Not Available',
    'locationCapture.noGpsSupport': 'This device does not support GPS location services',
    'locationCapture.gpsRequired': 'GPS Location Required',
    'locationCapture.required': 'Required',
    'locationCapture.gettingLocation': 'Getting Location...',
    'locationCapture.captureCurrentLocation': 'Capture Current Location',
    'locationCapture.accuracyTarget': 'Location accuracy target: within',
    'locationCapture.capturedLocation': 'Captured Location',
    'locationCapture.confirmed': 'Confirmed',
    'locationCapture.pendingConfirmation': 'Pending Confirmation',
    'locationCapture.confirmLocation': 'Confirm Location',
    'locationCapture.retry': 'Retry',
    'locationCapture.excellent': 'Excellent location accuracy',
    'locationCapture.good': 'Good location accuracy',
    'locationCapture.acceptable': 'Acceptable location accuracy',
    'locationCapture.low': 'Low location accuracy',
    'locationCapture.accessDenied': 'Location access denied. Please enable location permissions.',
    'locationCapture.unavailable': 'Location information unavailable. Please try again.',
    'locationCapture.timeout': 'Location request timed out. Please try again.',
    'locationCapture.lowAccuracyWarning': 'Location accuracy is {accuracy}m. Consider moving to an area with better GPS signal for more accurate results.',
    'locationCapture.locationDescription': 'Your current location will be captured and stored with this status update for verification purposes.',

    // Photo Capture
    'photoCapture.capture': 'Capture',
    'photoCapture.selectFromGallery': 'Choose Files',
    'photoCapture.takePhoto': 'Take Photo',
    'photoCapture.photosCount': '{count} of {max} photos',
    'photoCapture.maximumPhotos': 'Maximum {max} photos allowed. Currently have {current}.',

    // Container Data
    'containerData.title': 'Container Data Entry',
    'containerData.shipment': 'Shipment',
    'containerData.goBack': 'Go Back',
    'containerData.loadingForm': 'Loading container data form...',
    'containerData.missingParams': 'Missing required parameters: containerId and shipmentId',
    'containerData.mobileEntry': 'Mobile Container Data Entry',

    // Container Data Form
    'containerForm.title': 'Container Data Entry',
    'containerForm.containerNumber': 'Container Number',
    'containerForm.sealNumber': 'Seal Number',
    'containerForm.reviewSave': 'Review & Save',
    'containerForm.step': 'Step',
    'containerForm.of': 'of',
    'containerForm.reviewInfo': 'Review Container Information',
    'containerForm.containerNumberLabel': 'Container Number:',
    'containerForm.sealNumberLabel': 'Seal Number:',
    'containerForm.format': 'format',
    'containerForm.readyToSave': 'Ready to Save',
    'containerForm.validatedReady': 'All information has been validated and is ready for submission.',
    'containerForm.previous': 'Previous',
    'containerForm.cancel': 'Cancel',
    'containerForm.next': 'Next',
    'containerForm.saving': 'Saving...',
    'containerForm.saveContainerData': 'Save Container Data',
    'containerForm.confirmTitle': 'Confirm Container Data',
    'containerForm.confirmMessage': 'Are you sure you want to save this container information?',
    'containerForm.containerLabel': 'Container:',
    'containerForm.sealLabel': 'Seal:',
    'containerForm.confirmSave': 'Confirm & Save',
    'containerForm.dataUpdated': 'Container Data Updated',
    'containerForm.successMessage': 'Container number and seal information have been saved successfully',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.back': 'Back',
    'common.language': 'Language',
    'common.confirmed': 'Confirmed',
  },
  th: {
    // Dashboard
    'dashboard.welcome': 'ยินดีต้อนรับ',
    'dashboard.driver': 'หน้าหลักคนขับ',
    'dashboard.offline': '(ออฟไลน์)',
    'dashboard.signOut': 'ออกจากระบบ',
    'dashboard.activeJobs': 'งานที่ได้รับมอบหมาย',
    'dashboard.thisWeek': 'สัปดาห์นี้',
    'dashboard.refreshing': 'กำลังรีเฟรช...',
    'dashboard.refresh': 'รีเฟรชข้อมูลงาน',
    'dashboard.pullToRefresh': 'ดึงลงเพื่อรีเฟรช',
    'dashboard.releaseToRefresh': 'ปล่อยเพื่อรีเฟรช',
    'dashboard.offlineCached': 'ออฟไลน์ - แสดงข้อมูลที่บันทึกไว้',
    'dashboard.yourAssignments': 'งานที่ได้รับมอบหมาย',
    'dashboard.noAssignments': 'ยังไม่มีงานที่ได้รับมอบหมาย',
    'dashboard.noAssignmentsDesc': 'งานขนส่งของคุณจะแสดงที่นี่',

    // Shipment Card
    'shipment.customer': 'ลูกค้า',
    'shipment.factory': 'โรงงาน',
    'shipment.products': 'รายการสินค้า',
    'shipment.moreProducts': 'รายการเพิ่มเติม',
    'shipment.pickupContainer': 'สถานที่รับตู้คอนเทนเนอร์',
    'shipment.pickupProduct': 'สถานที่รับสินค้า',
    'shipment.delivery': 'สถานที่ส่งสินค้า',
    'shipment.locationTBA': 'ยังไม่ระบุสถานที่',
    'shipment.etd': 'วันที่ออกเดินทาง',
    'shipment.eta': 'วันที่ถึง',
    'shipment.assigned': 'วันที่มอบหมาย',
    'shipment.distance': 'ระยะทาง',
    'shipment.km': 'กม.',
    'shipment.notSet': 'ไม่ได้กำหนด',
    'shipment.tapToUpdate': 'แตะเพื่ออัพเดตสถานะ',
    'shipment.updateStatus': 'อัพเดตสถานะ',
    'shipment.emptyContainerPickedTime': 'เวลารับตู้เปล่า',
    'shipment.arrivedAtFactoryTime': 'เวลาถึงโรงงาน',
    'shipment.loadingStartedTime': 'เวลาเริ่มโหลดสินค้า',
    'shipment.departedFactoryTime': 'เวลาออกจากโรงงาน',
    'shipment.containerReturnedTime': 'เวลาคืนตู้',
    'shipment.container': 'ตู้คอนเทนเนอร์',
    'shipment.containerNumber': 'หมายเลขตู้คอนเทนเนอร์',
    'shipment.sealNumber': 'หมายเลขซีล',
    'shipment.type': 'ประเภท',
    'shipment.size': 'ขนาด',
    'shipment.edit': 'แก้ไข',

    // Packaging Types
    'packaging.bag': 'ถุง',
    'packaging.plastic_basket': 'ตะกร้าพลาสติก',
    'packaging.carton': 'กล่องกระดาษ',
    'packaging.boxes': 'กล่อง',
    'packaging.pallets': 'พาเลต',
    'packaging.bags': 'ถุง',
    'packaging.crates': 'ลัง',
    'packaging.cartons': 'กล่องกระดาษ',
    'packaging.pieces': 'ชิ้น',
    'packaging.units': 'หน่วย',

    // Status Labels
    'status.booking_confirmed': 'ยืนยันการจอง',
    'status.transport_assigned': 'มอบหมายการขนส่ง',
    'status.driver_assigned': 'มอบหมายคนขับ',
    'status.empty_container_picked': 'รับตู้เปล่าแล้ว',
    'status.arrived_at_factory': 'ถึงโรงงานแล้ว',
    'status.loading_started': 'เริ่มบรรทุกสินค้า',
    'status.departed_factory': 'ออกจากโรงงานแล้ว',
    'status.container_returned': 'คืนตู้คอนเทนเนอร์แล้ว',
    'status.shipped': 'จัดส่งแล้ว',
    'status.arrived': 'ถึงจุดหมายแล้ว',
    'status.completed': 'เสร็จสิ้น',
    'status.cancelled': 'ยกเลิก',

    // Status Update Form
    'statusUpdate.title': 'อัพเดตสถานะ',
    'statusUpdate.currentStatus': 'สถานะปัจจุบัน',
    'statusUpdate.nextStatus': 'สถานะถัดไป',
    'statusUpdate.noUpdatesAvailable': 'ไม่มีการอัพเดตสถานะเพิ่มเติม',
    'statusUpdate.photos': 'รูปภาพ',
    'statusUpdate.photosRequired': 'ต้องการรูปภาพ',
    'statusUpdate.notes': 'หมายเหตุ',
    'statusUpdate.notesPlaceholder': 'เพิ่มหมายเหตุเกี่ยวกับการอัพเดตสถานะ...',
    'statusUpdate.location': 'ตำแหน่ง',
    'statusUpdate.updating': 'กำลังอัพเดต...',
    'statusUpdate.update': 'อัพเดตสถานะ',
    'statusUpdate.success': 'อัพเดตสถานะเรียบร้อยแล้ว',
    'statusUpdate.error': 'ไม่สามารถอัพเดตสถานะได้',
    'statusUpdate.selectStatus': 'กรุณาเลือกสถานะที่ต้องการอัพเดต',
    'statusUpdate.gpsRequired': 'ต้องการตำแหน่ง GPS',
    'statusUpdate.notesLimit': 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร',
    'statusUpdate.confirm': 'ยืนยัน',
    'statusUpdate.step': 'ขั้นตอนที่',
    'statusUpdate.of': 'จาก',
    
    // Status Update Form Steps
    'statusUpdate.selectStatusStep': 'เลือกสถานะ',
    'statusUpdate.addPhotosStep': 'เพิ่มรูปภาพ',
    'statusUpdate.confirmLocationStep': 'ยืนยันตำแหน่ง',
    'statusUpdate.addNotesStep': 'เพิ่มหมายเหตุ',
    'statusUpdate.confirmUpdateStep': 'ยืนยันการอัพเดต',
    'statusUpdate.statusUpdatedSuccessfully': 'อัพเดตสถานะเรียบร้อยแล้ว',
    'statusUpdate.shipmentStatusUpdatedTo': 'สถานะการจัดส่งได้รับการอัพเดตเป็น',
    'statusUpdate.photoRequirements': 'ข้อกำหนดรูปภาพ',
    'statusUpdate.photosRequiredFor': 'รูปภาพที่ต้องการสำหรับ',
    'statusUpdate.notesOptional': 'หมายเหตุ (ไม่บังคับ)',
    'statusUpdate.notesOptionalDesc': 'หมายเหตุเพิ่มเติมสำหรับการอัพเดตสถานะนี้',
    'statusUpdate.confirmStatusUpdate': 'ยืนยันการอัพเดตสถานะ',
    'statusUpdate.statusChange': 'การเปลี่ยนแปลงสถานะ',
    'statusUpdate.photo': 'รูปภาพ',
    'statusUpdate.notCaptured': 'ยังไม่ได้บันทึก',
    'statusUpdate.uploadingPhotos': 'กำลังอัพโหลดรูปภาพ',
    'statusUpdate.previous': 'ก่อนหน้า',
    'statusUpdate.next': 'ถัดไป',
    'statusUpdate.uploading': 'กำลังอัพโหลด...',

    // Status Selector
    'statusSelector.currentStatus': 'สถานะปัจจุบัน',
    'statusSelector.complete': 'เสร็จสิ้น',
    'statusSelector.updateTo': 'อัพเดตเป็น',
    'statusSelector.progress': 'ความคืบหน้า',
    'statusSelector.final': 'สุดท้าย',
    'statusSelector.next': 'ถัดไป',
    'statusSelector.shipmentComplete': 'การจัดส่งเสร็จสิ้น',
    'statusSelector.finalStatus': 'สถานะสุดท้าย',
    'statusSelector.noFurtherUpdates': 'ไม่มีการอัพเดตสถานะเพิ่มเติม',

    // Photo Preview Grid
    'photoPreview.willAppearHere': 'รูปภาพจะปรากฏที่นี่หลังจากถ่ายหรือเลือก',
    'photoPreview.selectedPhotos': 'รูปภาพที่เลือก',
    'photoPreview.dragToReorder': 'ลากเพื่อเรียงลำดับ',
    'photoPreview.photo': 'รูปภาพ',
    'photoPreview.fullSize': 'ขนาดเต็ม',
    'photoPreview.dragPhotosToReorder': 'ลากรูปภาพเพื่อเรียงลำดับใหม่ รูปภาพแรกจะเป็นรูปหลัก',

    // Location Capture
    'locationCapture.notAvailable': 'ตำแหน่งไม่พร้อมใช้งาน',
    'locationCapture.noGpsSupport': 'อุปกรณ์นี้ไม่รองรับบริการระบุตำแหน่ง GPS',
    'locationCapture.gpsRequired': 'ต้องการตำแหน่ง GPS',
    'locationCapture.required': 'จำเป็น',
    'locationCapture.gettingLocation': 'กำลังหาตำแหน่ง...',
    'locationCapture.captureCurrentLocation': 'บันทึกตำแหน่งปัจจุบัน',
    'locationCapture.accuracyTarget': 'ความแม่นยำเป้าหมาย: ภายใน',
    'locationCapture.capturedLocation': 'ตำแหน่งที่บันทึก',
    'locationCapture.confirmed': 'ยืนยันแล้ว',
    'locationCapture.pendingConfirmation': 'รอการยืนยัน',
    'locationCapture.confirmLocation': 'ยืนยันตำแหน่ง',
    'locationCapture.retry': 'ลองใหม่',
    'locationCapture.excellent': 'ความแม่นยำตำแหน่งดีเยี่ยม',
    'locationCapture.good': 'ความแม่นยำตำแหน่งดี',
    'locationCapture.acceptable': 'ความแม่นยำตำแหน่งยอมรับได้',
    'locationCapture.low': 'ความแม่นยำตำแหน่งต่ำ',
    'locationCapture.accessDenied': 'การเข้าถึงตำแหน่งถูกปฏิเสธ กรุณาเปิดใช้สิทธิ์ตำแหน่ง',
    'locationCapture.unavailable': 'ข้อมูลตำแหน่งไม่พร้อมใช้งาน กรุณาลองใหม่',
    'locationCapture.timeout': 'คำขอตำแหน่งหมดเวลา กรุณาลองใหม่',
    'locationCapture.lowAccuracyWarning': 'ความแม่นยำตำแหน่งคือ {accuracy}ม. พิจารณาย้ายไปยังพื้นที่ที่มีสัญญาณ GPS ดีกว่า',
    'locationCapture.locationDescription': 'ตำแหน่งปัจจุบันของคุณจะถูกบันทึกและเก็บไว้กับการอัปเดตสถานะนี้เพื่อการตรวจสอบ',

    // Photo Capture
    'photoCapture.capture': 'ถ่ายภาพ',
    'photoCapture.selectFromGallery': 'เลือกไฟล์',
    'photoCapture.takePhoto': 'ถ่ายภาพ',
    'photoCapture.photosCount': '{count} จาก {max} รูปภาพ',
    'photoCapture.maximumPhotos': 'จำนวนรูปภาพสูงสุด {max} รูป ปัจจุบันมี {current} รูป',

    // Container Data
    'containerData.title': 'ระบบกรอกข้อมูลตู้คอนเทนเนอร์',
    'containerData.shipment': 'การจัดส่ง',
    'containerData.goBack': 'ย้อนกลับ',
    'containerData.loadingForm': 'กำลังโหลดฟอร์มข้อมูลตู้คอนเทนเนอร์...',
    'containerData.missingParams': 'ขาดข้อมูลที่จำเป็น: รหัสตู้คอนเทนเนอร์และรหัสการจัดส่ง',
    'containerData.mobileEntry': 'ระบบกรอกข้อมูลตู้คอนเทนเนอร์บนมือถือ',

    // Container Data Form
    'containerForm.title': 'ระบบกรอกข้อมูลตู้คอนเทนเนอร์',
    'containerForm.containerNumber': 'หมายเลขตู้คอนเทนเนอร์',
    'containerForm.sealNumber': 'หมายเลขซีล',
    'containerForm.reviewSave': 'ตรวจสอบและบันทึก',
    'containerForm.step': 'ขั้นตอนที่',
    'containerForm.of': 'จาก',
    'containerForm.reviewInfo': 'ตรวจสอบข้อมูลตู้คอนเทนเนอร์',
    'containerForm.containerNumberLabel': 'หมายเลขตู้คอนเทนเนอร์:',
    'containerForm.sealNumberLabel': 'หมายเลขซีล:',
    'containerForm.format': 'รูปแบบ',
    'containerForm.readyToSave': 'พร้อมบันทึก',
    'containerForm.validatedReady': 'ข้อมูลทั้งหมดได้รับการตรวจสอบแล้วและพร้อมส่ง',
    'containerForm.previous': 'ก่อนหน้า',
    'containerForm.cancel': 'ยกเลิก',
    'containerForm.next': 'ถัดไป',
    'containerForm.saving': 'กำลังบันทึก...',
    'containerForm.saveContainerData': 'บันทึกข้อมูลตู้คอนเทนเนอร์',
    'containerForm.confirmTitle': 'ยืนยันข้อมูลตู้คอนเทนเนอร์',
    'containerForm.confirmMessage': 'คุณแน่ใจหรือไม่ที่ต้องการบันทึกข้อมูลตู้คอนเทนเนอร์นี้?',
    'containerForm.containerLabel': 'ตู้คอนเทนเนอร์:',
    'containerForm.sealLabel': 'ซีล:',
    'containerForm.confirmSave': 'ยืนยันและบันทึก',
    'containerForm.dataUpdated': 'อัพเดตข้อมูลตู้คอนเทนเนอร์แล้ว',
    'containerForm.successMessage': 'หมายเลขตู้คอนเทนเนอร์และข้อมูลซีลได้รับการบันทึกเรียบร้อยแล้ว',

    // Common
    'common.loading': 'กำลังโหลด...',
    'common.error': 'เกิดข้อผิดพลาด',
    'common.save': 'บันทึก',
    'common.cancel': 'ยกเลิก',
    'common.back': 'กลับ',
    'common.language': 'ภาษา',
    'common.confirmed': 'ยืนยันแล้ว',
  }
} as const