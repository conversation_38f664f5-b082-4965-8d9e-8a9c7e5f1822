/**
 * Document Generation Interface Component Tests
 * Story 5.2: Automated Document Generation Engine
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DocumentGenerationInterface } from '../document-generation-interface'
import type { DocumentTemplate } from '@/types/document-template'
import type { GeneratedDocument, GenerationProgress } from '@/types/document-generation'

// Mock hooks
vi.mock('@/hooks/use-document-generation')
vi.mock('@/hooks/use-document-templates')

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  FileText: () => <div data-testid="file-text-icon">FileText</div>,
  Download: () => <div data-testid="download-icon">Download</div>,
  Eye: () => <div data-testid="eye-icon">Eye</div>,
  AlertCircle: () => <div data-testid="alert-circle-icon">AlertCircle</div>,
  CheckCircle: () => <div data-testid="check-circle-icon">CheckCircle</div>,
  Clock: () => <div data-testid="clock-icon">Clock</div>,
  Loader2: () => <div data-testid="loader2-icon">Loader2</div>
}))

// Mock data
const mockTemplates: DocumentTemplate[] = [
  {
    id: 'template-1',
    template_name: 'Booking Confirmation',
    document_type: 'booking_confirmation',
    version: '1.0',
    template_content: '<h1>Booking Confirmation</h1>',
    template_data: {},
    template_styles: null,
    page_size: 'A4',
    page_orientation: 'portrait',
    margin_top: 20,
    margin_bottom: 20,
    margin_left: 20,
    margin_right: 20,
    language: 'en',
    currency_format: 'USD',
    date_format: 'YYYY-MM-DD',
    number_format: 'en-US',
    description: 'Standard booking confirmation',
    usage_notes: null,
    required_fields: null,
    is_active: true,
    is_default: true,
    created_by: 'user-1',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'template-2',
    template_name: 'Invoice FOB',
    document_type: 'invoice_fob',
    version: '1.0',
    template_content: '<h1>Invoice FOB</h1>',
    template_data: {},
    template_styles: null,
    page_size: 'A4',
    page_orientation: 'portrait',
    margin_top: 20,
    margin_bottom: 20,
    margin_left: 20,
    margin_right: 20,
    language: 'en',
    currency_format: 'USD',
    date_format: 'YYYY-MM-DD',
    number_format: 'en-US',
    description: 'FOB Invoice template',
    usage_notes: null,
    required_fields: null,
    is_active: true,
    is_default: false,
    created_by: 'user-1',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

const mockGeneratedDocument: GeneratedDocument = {
  id: 'doc-1',
  shipment_id: 'shipment-1',
  document_type: 'booking_confirmation',
  document_name: 'Booking Confirmation',
  file_path: 'documents/test.pdf',
  file_name: 'booking_confirmation.pdf',
  file_size_bytes: 12345,
  file_type: 'application/pdf',
  version: 1,
  is_original: true,
  language: 'en',
  is_public: false,
  access_level: 'shipment',
  shared_with_customer: false,
  shared_with_carrier: false,
  is_verified: false,
  uploaded_by: 'user-1',
  upload_source: 'automated_generation',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

const mockProgress: GenerationProgress = {
  status: 'generating',
  progress: 50,
  message: 'Generating PDF...',
  currentStep: 'Creating PDF document'
}

describe('DocumentGenerationInterface', () => {
  let mockUseDocumentGeneration: any
  let mockUseDocumentTemplates: any

  beforeEach(() => {
    // Reset mocks
    const { useDocumentGeneration } = await import('@/hooks/use-document-generation')
    const { useDocumentTemplates } = await import('@/hooks/use-document-templates')
    
    mockUseDocumentGeneration = vi.mocked(useDocumentGeneration)
    mockUseDocumentTemplates = vi.mocked(useDocumentTemplates)

    // Setup default mock returns
    mockUseDocumentGeneration.mockReturnValue({
      generateDocument: vi.fn(),
      bulkGenerateDocuments: vi.fn(),
      isGenerating: false,
      progress: null,
      lastResult: null,
      error: null,
      clearError: vi.fn(),
      reset: vi.fn()
    })

    mockUseDocumentTemplates.mockReturnValue({
      templates: mockTemplates,
      loading: false,
      error: null,
      fetchTemplates: vi.fn()
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('should render the document generation interface', () => {
      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Document Generation')).toBeInTheDocument()
      expect(screen.getByText('Generate professional documents from shipment data')).toBeInTheDocument()
      expect(screen.getByText('Generation Configuration')).toBeInTheDocument()
      expect(screen.getByText('Generation Status')).toBeInTheDocument()
    })

    it('should render mode toggle buttons', () => {
      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Single')).toBeInTheDocument()
      expect(screen.getByText('Bulk')).toBeInTheDocument()
    })

    it('should render template selection area', () => {
      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Document Templates')).toBeInTheDocument()
      expect(screen.getByText('Booking Confirmation')).toBeInTheDocument()
      expect(screen.getByText('Invoice FOB')).toBeInTheDocument()
    })

    it('should show loading state for templates', () => {
      mockUseDocumentTemplates.mockReturnValue({
        templates: [],
        loading: true,
        error: null,
        fetchTemplates: vi.fn()
      })

      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Loading templates...')).toBeInTheDocument()
    })

    it('should show error state for templates', () => {
      const errorMessage = 'Failed to load templates'
      mockUseDocumentTemplates.mockReturnValue({
        templates: [],
        loading: false,
        error: errorMessage,
        fetchTemplates: vi.fn()
      })

      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })

    it('should render with pre-selected shipment ID', () => {
      render(<DocumentGenerationInterface shipmentId="shipment-123" />)
      
      // Should not show shipment selection input when shipmentId is provided
      expect(screen.queryByPlaceholderText('Enter shipment ID or number...')).not.toBeInTheDocument()
    })
  })

  describe('Mode Toggle', () => {
    it('should switch between single and bulk mode', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      // Should start in single mode
      expect(screen.getByText('Generate Document')).toBeInTheDocument()
      
      // Switch to bulk mode
      await user.click(screen.getByText('Bulk'))
      
      expect(screen.getByText('Generate 0 Documents')).toBeInTheDocument()
    })

    it('should reset template selection when switching modes', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      // Select template in single mode
      const bookingCheckbox = screen.getByLabelText(/template-1/)
      await user.click(bookingCheckbox)
      
      // Switch to bulk mode
      await user.click(screen.getByText('Bulk'))
      
      // Select multiple templates
      const invoiceCheckbox = screen.getByLabelText(/template-2/)
      await user.click(invoiceCheckbox)
      
      // Switch back to single mode
      await user.click(screen.getByText('Single'))
      
      // Should clear selections
      expect(screen.getByText('Generate Document')).toBeInTheDocument()
    })
  })

  describe('Template Selection', () => {
    it('should allow single template selection in single mode', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      const bookingCheckbox = screen.getByLabelText(/template-1/)
      await user.click(bookingCheckbox)
      
      expect(screen.getByText('1 selected')).toBeInTheDocument()
      expect(screen.getByText('Generate Document')).toBeInTheDocument()
    })

    it('should allow multiple template selection in bulk mode', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      // Switch to bulk mode
      await user.click(screen.getByText('Bulk'))
      
      const bookingCheckbox = screen.getByLabelText(/template-1/)
      const invoiceCheckbox = screen.getByLabelText(/template-2/)
      
      await user.click(bookingCheckbox)
      await user.click(invoiceCheckbox)
      
      expect(screen.getByText('2 selected')).toBeInTheDocument()
      expect(screen.getByText('Generate 2 Documents')).toBeInTheDocument()
    })

    it('should disable other templates when one is selected in single mode', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      const bookingCheckbox = screen.getByLabelText(/template-1/)
      const invoiceCheckbox = screen.getByLabelText(/template-2/)
      
      await user.click(bookingCheckbox)
      
      expect(invoiceCheckbox).toBeDisabled()
    })

    it('should not disable templates in bulk mode', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      // Switch to bulk mode
      await user.click(screen.getByText('Bulk'))
      
      const bookingCheckbox = screen.getByLabelText(/template-1/)
      const invoiceCheckbox = screen.getByLabelText(/template-2/)
      
      await user.click(bookingCheckbox)
      
      expect(invoiceCheckbox).not.toBeDisabled()
    })
  })

  describe('Document Generation', () => {
    it('should trigger single document generation', async () => {
      const mockGenerateDocument = vi.fn()
      mockUseDocumentGeneration.mockReturnValue({
        generateDocument: mockGenerateDocument,
        bulkGenerateDocuments: vi.fn(),
        isGenerating: false,
        progress: null,
        lastResult: null,
        error: null,
        clearError: vi.fn(),
        reset: vi.fn()
      })

      const user = userEvent.setup()
      render(<DocumentGenerationInterface shipmentId="shipment-123" />)
      
      // Select a template
      const bookingCheckbox = screen.getByLabelText(/template-1/)
      await user.click(bookingCheckbox)
      
      // Enter document number
      const documentNumberInput = screen.getByPlaceholderText('Auto-generated if not provided')
      await user.type(documentNumberInput, 'DOC-001')
      
      // Click generate
      const generateButton = screen.getByText('Generate Document')
      await user.click(generateButton)
      
      expect(mockGenerateDocument).toHaveBeenCalledWith({
        shipmentId: 'shipment-123',
        templateId: 'template-1',
        options: {
          documentNumber: 'DOC-001',
          additionalData: { notes: '' }
        }
      })
    })

    it('should trigger bulk document generation', async () => {
      const mockBulkGenerateDocuments = vi.fn()
      mockUseDocumentGeneration.mockReturnValue({
        generateDocument: vi.fn(),
        bulkGenerateDocuments: mockBulkGenerateDocuments,
        isGenerating: false,
        progress: null,
        lastResult: null,
        error: null,
        clearError: vi.fn(),
        reset: vi.fn()
      })

      const user = userEvent.setup()
      render(<DocumentGenerationInterface shipmentId="shipment-123" />)
      
      // Switch to bulk mode
      await user.click(screen.getByText('Bulk'))
      
      // Select multiple templates
      await user.click(screen.getByLabelText(/template-1/))
      await user.click(screen.getByLabelText(/template-2/))
      
      // Click generate
      const generateButton = screen.getByText('Generate 2 Documents')
      await user.click(generateButton)
      
      expect(mockBulkGenerateDocuments).toHaveBeenCalledWith({
        shipmentId: 'shipment-123',
        templateIds: ['template-1', 'template-2'],
        options: {
          additionalData: { notes: '' }
        }
      })
    })

    it('should disable generate button when requirements not met', () => {
      render(<DocumentGenerationInterface />)
      
      const generateButton = screen.getByText('Generate Document')
      expect(generateButton).toBeDisabled()
    })

    it('should disable generate button during generation', () => {
      mockUseDocumentGeneration.mockReturnValue({
        generateDocument: vi.fn(),
        bulkGenerateDocuments: vi.fn(),
        isGenerating: true,
        progress: mockProgress,
        lastResult: null,
        error: null,
        clearError: vi.fn(),
        reset: vi.fn()
      })

      render(<DocumentGenerationInterface shipmentId="shipment-123" />)
      
      const generateButton = screen.getByRole('button', { name: /Generate/ })
      expect(generateButton).toBeDisabled()
    })
  })

  describe('Progress Display', () => {
    it('should show generation progress', () => {
      mockUseDocumentGeneration.mockReturnValue({
        generateDocument: vi.fn(),
        bulkGenerateDocuments: vi.fn(),
        isGenerating: true,
        progress: mockProgress,
        lastResult: null,
        error: null,
        clearError: vi.fn(),
        reset: vi.fn()
      })

      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Generating PDF...')).toBeInTheDocument()
      expect(screen.getByText('Creating PDF document')).toBeInTheDocument()
      expect(screen.getByText('generating')).toBeInTheDocument()
    })

    it('should show completed generation result', () => {
      const successResult = {
        success: true,
        document: mockGeneratedDocument,
        templateId: 'template-1',
        processingTime: 1500
      }

      mockUseDocumentGeneration.mockReturnValue({
        generateDocument: vi.fn(),
        bulkGenerateDocuments: vi.fn(),
        isGenerating: false,
        progress: { status: 'complete', progress: 100, message: 'Complete!' },
        lastResult: successResult,
        error: null,
        clearError: vi.fn(),
        reset: vi.fn()
      })

      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Document Generated Successfully')).toBeInTheDocument()
      expect(screen.getByText('Booking Confirmation')).toBeInTheDocument()
      expect(screen.getByText('booking_confirmation.pdf')).toBeInTheDocument()
      expect(screen.getByText('1500ms')).toBeInTheDocument()
    })

    it('should show bulk generation results', () => {
      const bulkResult = {
        results: [
          { success: true, document: mockGeneratedDocument, templateId: 'template-1' },
          { success: false, error: 'Template error', templateId: 'template-2' }
        ],
        summary: {
          total: 2,
          successful: 1,
          failed: 1,
          totalProcessingTime: 2500
        }
      }

      mockUseDocumentGeneration.mockReturnValue({
        generateDocument: vi.fn(),
        bulkGenerateDocuments: vi.fn(),
        isGenerating: false,
        progress: { status: 'complete', progress: 100, message: 'Complete!' },
        lastResult: bulkResult,
        error: null,
        clearError: vi.fn(),
        reset: vi.fn()
      })

      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Bulk Generation Complete')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument() // Total
      expect(screen.getByText('1')).toBeInTheDocument() // Successful
      expect(screen.getByText('1')).toBeInTheDocument() // Failed
    })
  })

  describe('Error Handling', () => {
    it('should display generation errors', () => {
      const errorMessage = 'Failed to generate document'
      mockUseDocumentGeneration.mockReturnValue({
        generateDocument: vi.fn(),
        bulkGenerateDocuments: vi.fn(),
        isGenerating: false,
        progress: null,
        lastResult: null,
        error: errorMessage,
        clearError: vi.fn(),
        reset: vi.fn()
      })

      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Generation Error')).toBeInTheDocument()
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })

    it('should allow dismissing errors', async () => {
      const mockClearError = vi.fn()
      mockUseDocumentGeneration.mockReturnValue({
        generateDocument: vi.fn(),
        bulkGenerateDocuments: vi.fn(),
        isGenerating: false,
        progress: null,
        lastResult: null,
        error: 'Some error',
        clearError: mockClearError,
        reset: vi.fn()
      })

      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      const dismissButton = screen.getByText('Dismiss')
      await user.click(dismissButton)
      
      expect(mockClearError).toHaveBeenCalled()
    })
  })

  describe('Empty State', () => {
    it('should show empty state when no generation is happening', () => {
      render(<DocumentGenerationInterface />)
      
      expect(screen.getByText('Ready to Generate')).toBeInTheDocument()
      expect(screen.getByText('Configure your settings and click generate to start')).toBeInTheDocument()
    })
  })

  describe('Form Inputs', () => {
    it('should handle shipment ID input', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      const shipmentInput = screen.getByPlaceholderText('Enter shipment ID or number...')
      await user.type(shipmentInput, 'shipment-456')
      
      expect(shipmentInput).toHaveValue('shipment-456')
    })

    it('should handle document number input', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      const documentNumberInput = screen.getByPlaceholderText('Auto-generated if not provided')
      await user.type(documentNumberInput, 'DOC-123')
      
      expect(documentNumberInput).toHaveValue('DOC-123')
    })

    it('should handle notes input', async () => {
      const user = userEvent.setup()
      render(<DocumentGenerationInterface />)
      
      const notesInput = screen.getByPlaceholderText('Any additional information to include...')
      await user.type(notesInput, 'Special handling required')
      
      expect(notesInput).toHaveValue('Special handling required')
    })
  })

  describe('Integration', () => {
    it('should call onDocumentGenerated callback when document is generated', () => {
      const mockCallback = vi.fn()
      const successResult = {
        success: true,
        document: mockGeneratedDocument,
        templateId: 'template-1'
      }

      // Mock the hook to trigger the callback
      mockUseDocumentGeneration.mockImplementation((options) => {
        // Simulate calling the onComplete callback
        if (options?.onComplete) {
          options.onComplete(successResult)
        }
        
        return {
          generateDocument: vi.fn(),
          bulkGenerateDocuments: vi.fn(),
          isGenerating: false,
          progress: null,
          lastResult: successResult,
          error: null,
          clearError: vi.fn(),
          reset: vi.fn()
        }
      })

      render(<DocumentGenerationInterface onDocumentGenerated={mockCallback} />)
      
      expect(mockCallback).toHaveBeenCalledWith(mockGeneratedDocument)
    })

    it('should fetch templates on mount', () => {
      const mockFetchTemplates = vi.fn()
      mockUseDocumentTemplates.mockReturnValue({
        templates: [],
        loading: false,
        error: null,
        fetchTemplates: mockFetchTemplates
      })

      render(<DocumentGenerationInterface />)
      
      expect(mockFetchTemplates).toHaveBeenCalledWith({
        filters: { is_active: true },
        sort: { field: 'template_name', direction: 'asc' },
        pagination: { page: 1, pageSize: 50 }
      })
    })
  })
})