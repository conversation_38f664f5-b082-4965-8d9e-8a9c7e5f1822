-- Company-Specific Info Tables
-- Story 1.2: Database Schema & RLS Foundation
-- This migration creates company-specific info tables for complex company types

-- ============================================================================
-- CUSTOMER INFO TABLE
-- ============================================================================

-- Customer-specific information with credit limits and incoterms
CREATE TABLE customer_info (
    company_id UUID PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
    customer_type customer_type_enum NOT NULL DEFAULT 'regular',
    credit_limit NUMERIC(12,2) DEFAULT 0 CHECK (credit_limit >= 0),
    incoterms incoterms_enum,
    special_requirements TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- CARRIER INFO TABLE
-- ============================================================================

-- Carrier-specific information with fleet management data
CREATE TABLE carrier_info (
    company_id UUID PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
    carrier_code TEXT UNIQUE,
    fleet_size INTEGER DEFAULT 0 CHECK (fleet_size >= 0),
    license_types TEXT[] DEFAULT '{}',
    coverage_areas TEXT[] DEFAULT '{}',
    insurance_policy_no TEXT,
    insurance_expiry_date DATE,
    insurance_coverage_amount NUMERIC(15,2),
    max_weight_capacity NUMERIC(10,2),
    max_volume_capacity NUMERIC(10,2),
    operating_hours JSONB, -- {"weekdays": "08:00-18:00", "weekends": "09:00-15:00"}
    emergency_contact_phone TEXT,
    gps_tracking_available BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- FACTORY INFO TABLE
-- ============================================================================

-- Factory-specific information with production capacity and certifications
CREATE TABLE factory_info (
    company_id UUID PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
    factory_code TEXT UNIQUE NOT NULL,
    license_no TEXT NOT NULL,
    certifications TEXT[] DEFAULT '{}', -- ['HACCP', 'ISO22000', 'GMP']
    production_capacity_tons_per_day INTEGER DEFAULT 0,
    cold_storage_capacity_tons INTEGER DEFAULT 0,
    operating_hours JSONB, -- {"weekdays": "08:00-17:00", "weekends": "closed"}
    specializations TEXT[] DEFAULT '{}', -- ['durian', 'mangosteen', 'longan']
    quality_control_manager TEXT,
    quality_control_phone TEXT,
    loading_dock_count INTEGER DEFAULT 1,
    container_loading_time_minutes INTEGER DEFAULT 120,
    advance_booking_required_hours INTEGER DEFAULT 24,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_customer_info_updated_at 
    BEFORE UPDATE ON customer_info 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_carrier_info_updated_at 
    BEFORE UPDATE ON carrier_info 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_factory_info_updated_at 
    BEFORE UPDATE ON factory_info 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Customer info indexes
CREATE INDEX idx_customer_info_type ON customer_info(customer_type);

-- Carrier info indexes  
CREATE INDEX idx_carrier_info_fleet_size ON carrier_info(fleet_size);
CREATE INDEX idx_carrier_info_tracking ON carrier_info(gps_tracking_available);

-- Factory info indexes
CREATE INDEX idx_factory_info_capacity ON factory_info(production_capacity_tons_per_day);
CREATE INDEX idx_factory_info_specializations ON factory_info USING GIN(specializations);

-- ============================================================================
-- VALIDATION FUNCTIONS
-- ============================================================================

-- Function to validate company metadata based on type (already exists in initial schema migration)

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE customer_info IS 'Customer-specific information including credit limits, incoterms, and business metrics';
COMMENT ON TABLE carrier_info IS 'Carrier-specific information including fleet management, coverage areas, and performance metrics';
COMMENT ON TABLE factory_info IS 'Factory-specific information including production capacity, certifications, and facility details';

COMMENT ON COLUMN customer_info.preferred_ports IS 'Array of preferred port codes for this customer';
COMMENT ON COLUMN customer_info.annual_volume IS 'Annual shipping volume in standardized base units';
COMMENT ON COLUMN carrier_info.available_routes IS 'JSONB field for route-specific information and capabilities';
COMMENT ON COLUMN carrier_info.on_time_delivery_rate IS 'Percentage rate of on-time deliveries (0.00-100.00)';
COMMENT ON COLUMN factory_info.seasonal_capacity_variations IS 'JSONB field for tracking seasonal production capacity changes';
COMMENT ON COLUMN factory_info.production_capacity_monthly IS 'Monthly production capacity in standardized base units';

COMMENT ON FUNCTION validate_company_metadata() IS 'Validates that complex company types use dedicated info tables instead of JSONB metadata';