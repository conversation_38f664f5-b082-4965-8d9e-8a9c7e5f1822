import { describe, expect, it } from 'vitest'
import {
  productSchema,
  unitOfMeasureSchema,
  productFormSchema,
  unitOfMeasureFormSchema,
  unitCategorySchema,
} from '@/lib/validations/products'

describe('Products Validation', () => {
  describe('Product Schema', () => {
    it('should validate a valid product', () => {
      const validProduct = {
        name: 'Test Product',
        code: 'TEST-001',
        description: 'A test product',
        category: 'Electronics',
        hs_code: '1234.56.78',
        unit_of_measure_id: '550e8400-e29b-41d4-a716-************',
      }

      const result = productSchema.safeParse(validProduct)
      if (!result.success) {
        console.log('Validation errors:', result.error.issues)
      }
      expect(result.success).toBe(true)
    })

    it('should reject product with invalid name', () => {
      const invalidProduct = {
        name: 'A', // Too short
        unit_of_measure_id: '550e8400-e29b-41d4-a716-************',
      }

      const result = productSchema.safeParse(invalidProduct)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('at least 2 characters')
      }
    })

    it('should reject product with invalid HS code', () => {
      const invalidProduct = {
        name: 'Test Product',
        hs_code: 'ABC123', // Should only contain numbers and dots
        unit_of_measure_id: '550e8400-e29b-41d4-a716-************',
      }

      const result = productSchema.safeParse(invalidProduct)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('numbers and dots')
      }
    })

    it('should reject product with invalid unit_of_measure_id', () => {
      const invalidProduct = {
        name: 'Test Product',
        unit_of_measure_id: 'invalid-uuid',
      }

      const result = productSchema.safeParse(invalidProduct)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid unit of measure ID')
      }
    })

    it('should allow optional fields to be null or undefined', () => {
      const minimalProduct = {
        name: 'Test Product',
        unit_of_measure_id: '550e8400-e29b-41d4-a716-************',
      }

      const result = productSchema.safeParse(minimalProduct)
      expect(result.success).toBe(true)
    })
  })

  describe('Unit of Measure Schema', () => {
    it('should validate a valid unit of measure', () => {
      const validUnit = {
        code: 'KG',
        name: 'Kilogram',
        symbol: 'kg',
        category: 'weight' as const,
        conversion_factor: 1.0,
        base_unit_id: null,
        is_active: true,
      }

      const result = unitOfMeasureSchema.safeParse(validUnit)
      expect(result.success).toBe(true)
    })

    it('should reject unit with invalid code format', () => {
      const invalidUnit = {
        code: 'kg-invalid', // Should be uppercase letters, numbers, hyphens, underscores only
        name: 'Kilogram',
        symbol: 'kg',
        category: 'weight' as const,
      }

      const result = unitOfMeasureSchema.safeParse(invalidUnit)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('uppercase letters, numbers, hyphens, and underscores')
      }
    })

    it('should reject unit with invalid conversion factor', () => {
      const invalidUnit = {
        code: 'KG',
        name: 'Kilogram',
        symbol: 'kg',
        category: 'weight' as const,
        conversion_factor: -1.0, // Should be positive
      }

      const result = unitOfMeasureSchema.safeParse(invalidUnit)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('positive')
      }
    })

    it('should validate all category types', () => {
      const categories = ['weight', 'count', 'volume', 'length'] as const
      
      categories.forEach(category => {
        const unit = {
          code: 'TEST',
          name: 'Test Unit',
          symbol: 'test',
          category,
          conversion_factor: 1.0,
        }

        const result = unitOfMeasureSchema.safeParse(unit)
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid category', () => {
      const invalidUnit = {
        code: 'TEST',
        name: 'Test Unit',
        symbol: 'test',
        category: 'invalid_category',
        conversion_factor: 1.0,
      }

      const result = unitOfMeasureSchema.safeParse(invalidUnit)
      expect(result.success).toBe(false)
    })
  })

  describe('Form Schemas', () => {
    it('should validate product form data', () => {
      const formData = {
        name: 'Test Product',
        code: 'TEST-001',
        description: 'Test description',
        category: 'Electronics',
        hs_code: '1234.56',
        unit_of_measure_id: '550e8400-e29b-41d4-a716-************',
      }

      const result = productFormSchema.safeParse(formData)
      expect(result.success).toBe(true)
    })

    it('should validate unit form data', () => {
      const formData = {
        code: 'KG',
        name: 'Kilogram',
        symbol: 'kg',
        category: 'weight' as const,
        conversion_factor: 1.0,
        base_unit_id: null,
      }

      const result = unitOfMeasureFormSchema.safeParse(formData)
      expect(result.success).toBe(true)
    })
  })

  describe('Unit Category Schema', () => {
    it('should validate valid categories', () => {
      const validCategories = ['weight', 'count', 'volume', 'length']
      
      validCategories.forEach(category => {
        const result = unitCategorySchema.safeParse(category)
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid categories', () => {
      const invalidCategories = ['invalid', 'temperature', 'area']
      
      invalidCategories.forEach(category => {
        const result = unitCategorySchema.safeParse(category)
        expect(result.success).toBe(false)
      })
    })
  })
})