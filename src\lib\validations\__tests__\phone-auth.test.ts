import { describe, it, expect } from 'vitest'
import { phoneAuthSchema, otpVerificationSchema } from '../auth'

describe('Phone Authentication Validation', () => {
  describe('phoneAuthSchema', () => {
    it('should validate correct phone numbers', () => {
      const validPhones = [
        '+66812345678',
        '+1234567890',
        '+44123456789',
        '+8612345678901',
      ]

      validPhones.forEach(phone => {
        const result = phoneAuthSchema.safeParse({ phone })
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid phone numbers', () => {
      const testCases = [
        { phone: '', expected: false, reason: 'Empty' },
        { phone: '123', expected: false, reason: 'Too short' },
        { phone: '+123456789012345678', expected: false, reason: 'Too long' },
        { phone: 'abc123', expected: false, reason: 'Contains letters' },
        { phone: '+0123456789', expected: false, reason: 'Starts with 0' },
        { phone: '************', expected: false, reason: 'Missing country code' },
      ]

      testCases.forEach(({ phone, expected, reason }) => {
        const result = phoneAuthSchema.safeParse({ phone })
        if (result.success !== expected) {
          console.log(`Phone: "${phone}" (${reason}) - Success: ${result.success}`)
          if (!result.success) {
            console.log('Errors:', result.error.issues)
          }
        }
        expect(result.success).toBe(expected, `Phone: "${phone}" (${reason})`)
      })
    })

    it('should accept phone numbers with formatting characters', () => {
      const phoneWithFormatting = '+66 8-1234-5678'
      const result = phoneAuthSchema.safeParse({ phone: phoneWithFormatting })
      expect(result.success).toBe(true)
    })
  })

  describe('otpVerificationSchema', () => {
    it('should validate correct OTP data', () => {
      const validData = {
        phone: '+66812345678',
        token: '123456'
      }

      const result = otpVerificationSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject invalid OTP tokens', () => {
      const baseData = { phone: '+66812345678' }
      
      const invalidTokens = [
        '',        // Empty
        '123',     // Too short
        '1234567', // Too long
        'abc123',  // Contains letters
        '12 34',   // Contains spaces
      ]

      invalidTokens.forEach(token => {
        const result = otpVerificationSchema.safeParse({ ...baseData, token })
        expect(result.success).toBe(false)
      })
    })

    it('should reject invalid phone in OTP verification', () => {
      const invalidData = {
        phone: 'invalid-phone',
        token: '123456'
      }

      const result = otpVerificationSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })
})