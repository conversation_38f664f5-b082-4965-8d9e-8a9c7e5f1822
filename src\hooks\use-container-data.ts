'use client'

import { useState, useCallback, useRef, useEffect, useMemo } from 'react'
import { ContainerDataService } from '@/lib/services/container-data-service'
import { validateContainerNumber, validateSealNumber } from '@/lib/utils/container-validation'
import { useOfflineStatus } from '@/hooks/use-mobile'
import type { Container, ContainerUpdateData, ContainerValidationResult, SealValidationResult } from '@/types/container'

interface UseContainerDataOptions {
  containerId: string
  shipmentId: string
  onSuccess?: (container: Container) => void
  onError?: (error: string) => void
}

interface ContainerFormData {
  container_number: string
  seal_number: string
  container_type?: string
  container_size?: string
}

interface ValidationState {
  container_number: ContainerValidationResult | null
  seal_number: SealValidationResult | null
  uniqueness_checked: boolean
  is_validating_uniqueness: boolean
}

export function useContainerData({
  containerId,
  shipmentId,
  onSuccess,
  onError
}: UseContainerDataOptions) {
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [container, setContainer] = useState<Container | null>(null)
  
  const [formData, setFormData] = useState<ContainerFormData>({
    container_number: '',
    seal_number: '',
    container_type: undefined,
    container_size: undefined
  })

  const [validation, setValidation] = useState<ValidationState>({
    container_number: null,
    seal_number: null,
    uniqueness_checked: false,
    is_validating_uniqueness: false
  })

  const service = useRef(new ContainerDataService())
  const debounceTimer = useRef<NodeJS.Timeout>()
  const { isOnline } = useOfflineStatus()

  // Validation functions are now integrated directly into updateFormData to avoid circular dependencies

  // Load initial container data
  useEffect(() => {
    let mounted = true

    const loadContainer = async () => {
      if (!containerId) return

      setIsLoading(true)
      setError(null)

      try {
        const data = await service.current.getContainer(containerId)
        if (mounted && data) {
          setContainer(data)
          const newFormData = {
            container_number: data.container_number || '',
            seal_number: data.seal_number || '',
            container_type: data.container_type,
            container_size: data.container_size
          }
          setFormData(newFormData)
          
          // Trigger initial validation for existing data directly
          if (newFormData.container_number) {
            const containerResult = validateContainerNumber(newFormData.container_number)
            setValidation(prev => ({
              ...prev,
              container_number: containerResult,
              uniqueness_checked: false
            }))
            
            // Check uniqueness if valid
            if (containerResult.isValid && isOnline) {
              setValidation(prev => ({ ...prev, is_validating_uniqueness: true }))
              try {
                const isUnique = await service.current.validateContainerNumberUniqueness(
                  newFormData.container_number,
                  shipmentId,
                  containerId
                )
                
                setValidation(prev => ({
                  ...prev,
                  uniqueness_checked: true,
                  is_validating_uniqueness: false,
                  container_number: prev.container_number ? {
                    ...prev.container_number,
                    isValid: prev.container_number.isValid && isUnique,
                    errors: isUnique 
                      ? prev.container_number.errors 
                      : [...prev.container_number.errors, 'Container number already exists in this shipment']
                  } : null
                }))
              } catch (error) {
                setValidation(prev => ({
                  ...prev,
                  is_validating_uniqueness: false,
                  uniqueness_checked: false
                }))
              }
            }
          }
          
          if (newFormData.seal_number) {
            const sealResult = validateSealNumber(newFormData.seal_number)
            setValidation(prev => ({
              ...prev,
              seal_number: sealResult
            }))
          }
        }
      } catch (err) {
        if (mounted) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to load container data'
          setError(errorMessage)
          onError?.(errorMessage)
        }
      } finally {
        if (mounted) {
          setIsLoading(false)
        }
      }
    }

    loadContainer()

    return () => {
      mounted = false
    }
  }, [containerId, shipmentId, isOnline])

  // Update form data
  const updateFormData = useCallback((updates: Partial<ContainerFormData>) => {
    setFormData(prev => {
      const newData = { ...prev, ...updates }
      
      // Trigger validation when relevant fields change
      if ('container_number' in updates && updates.container_number !== prev.container_number) {
        const result = validateContainerNumber(updates.container_number || '')
        setValidation(prevValidation => ({
          ...prevValidation,
          container_number: result,
          uniqueness_checked: false
        }))

        // Debounced uniqueness check
        if (result.isValid && isOnline) {
          clearTimeout(debounceTimer.current)
          debounceTimer.current = setTimeout(async () => {
            setValidation(prevValidation => ({ ...prevValidation, is_validating_uniqueness: true }))
            
            try {
              const isUnique = await service.current.validateContainerNumberUniqueness(
                updates.container_number || '',
                shipmentId,
                containerId
              )
              
              setValidation(prevValidation => ({
                ...prevValidation,
                uniqueness_checked: true,
                is_validating_uniqueness: false,
                container_number: prevValidation.container_number ? {
                  ...prevValidation.container_number,
                  isValid: prevValidation.container_number.isValid && isUnique,
                  errors: isUnique 
                    ? prevValidation.container_number.errors 
                    : [...prevValidation.container_number.errors, 'Container number already exists in this shipment']
                } : null
              }))
            } catch (error) {
              setValidation(prevValidation => ({
                ...prevValidation,
                is_validating_uniqueness: false,
                uniqueness_checked: false
              }))
            }
          }, 500)
        }
      }
      
      if ('seal_number' in updates && updates.seal_number !== prev.seal_number) {
        const result = validateSealNumber(updates.seal_number || '')
        setValidation(prevValidation => ({
          ...prevValidation,
          seal_number: result
        }))
      }
      
      return newData
    })
  }, [containerId, shipmentId, isOnline])

  // Check if form is valid for submission (accounting for confirmed fields)
  const isFormValid = useMemo(() => {
    const containerConfirmed = container?.container_number_confirmed || false
    const sealConfirmed = container?.seal_number_confirmed || false

    // Container validation (skip if already confirmed)
    const containerValid = containerConfirmed || (
      validation.container_number?.isValid === true &&
      formData.container_number.trim() !== '' &&
      validation.uniqueness_checked
    )

    // Seal validation (skip if already confirmed)
    const sealValid = sealConfirmed || (
      validation.seal_number?.isValid === true &&
      formData.seal_number.trim() !== ''
    )

    return containerValid && sealValid
  }, [validation, formData, container])

  // Submit container data
  const submitContainerData = useCallback(async () => {
    if (!isFormValid || isSubmitting) return

    setIsSubmitting(true)
    setError(null)
    setSuccess(false)

    try {
      // Only include non-confirmed fields in the update
      const containerConfirmed = container?.container_number_confirmed || false
      const sealConfirmed = container?.seal_number_confirmed || false

      const updateData: ContainerUpdateData = {
        container_type: formData.container_type as any,
        container_size: formData.container_size as any
      }

      // Only include non-confirmed fields
      if (!containerConfirmed) {
        updateData.container_number = formData.container_number.trim()
      }

      if (!sealConfirmed) {
        updateData.seal_number = formData.seal_number.trim()
      }

      const updatedContainer = await service.current.upsertContainer(containerId, shipmentId, updateData)
      
      setContainer(updatedContainer)
      setSuccess(true)
      onSuccess?.(updatedContainer)
      
      // Reset form validation state
      setValidation({
        container_number: null,
        seal_number: null,
        uniqueness_checked: false,
        is_validating_uniqueness: false
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update container data'
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }, [isFormValid, isSubmitting, formData, containerId, onSuccess, onError])

  // Reset success state
  const resetSuccess = useCallback(() => {
    setSuccess(false)
  }, [])

  // Reset error state
  const resetError = useCallback(() => {
    setError(null)
  }, [])

  // Get validation errors for display
  const getValidationErrors = useCallback(() => {
    const errors: string[] = []
    
    if (validation.container_number && !validation.container_number.isValid) {
      errors.push(...validation.container_number.errors)
    }
    
    if (validation.seal_number && !validation.seal_number.isValid) {
      errors.push(...validation.seal_number.errors)
    }
    
    return errors
  }, [validation])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current)
      }
    }
  }, [])

  return {
    // Data
    container,
    formData,
    validation,
    
    // State flags
    isLoading,
    isSubmitting,
    error,
    success,
    isOnline,
    isFormValid,
    
    // Actions
    updateFormData,
    submitContainerData,
    resetSuccess,
    resetError,
    
    // Helper functions
    getValidationErrors
  }
}