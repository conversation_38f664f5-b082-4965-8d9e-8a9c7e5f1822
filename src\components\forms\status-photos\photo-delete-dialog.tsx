'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Trash2, 
  AlertTriangle, 
  Loader2, 
  Image as ImageIcon,
  User,
  Clock
} from 'lucide-react'
import { formatDistanceToNow, parseISO } from 'date-fns'
import type { StatusPhoto } from '@/hooks/use-status-photos'

interface PhotoDeleteDialogProps {
  photos: StatusPhoto[]
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: (deletedPhotos: StatusPhoto[]) => void
}

export function PhotoDeleteDialog({
  photos,
  isOpen,
  onOpenChange,
  onSuccess,
}: PhotoDeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const isMultiple = photos.length > 1

  // Delete photo from storage and database
  const deletePhoto = async (photo: StatusPhoto): Promise<boolean> => {
    try {
      // First, delete from storage
      const { error: storageError } = await supabase.storage
        .from('status-images')
        .remove([photo.image_path])

      if (storageError) {
        console.error('Storage deletion error:', storageError)
        // Continue with database deletion even if storage fails
        // The file might already be deleted or path might be invalid
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('status_images')
        .delete()
        .eq('id', photo.id)

      if (dbError) {
        throw dbError
      }

      return true
    } catch (error) {
      console.error('Error deleting photo:', error)
      throw error
    }
  }

  // Handle deletion
  const handleDelete = async () => {
    if (isDeleting) return

    setIsDeleting(true)
    setError(null)

    try {
      const deletionPromises = photos.map(photo => deletePhoto(photo))
      await Promise.all(deletionPromises)

      // Success - call callback and close dialog
      if (onSuccess) {
        onSuccess(photos)
      }
      onOpenChange(false)
    } catch (error) {
      console.error('Delete operation failed:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete photos')
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent className="bg-slate-800 border-slate-700 max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-slate-200 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Delete Photo{isMultiple ? 's' : ''}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-slate-300">
            {isMultiple 
              ? `Are you sure you want to delete these ${photos.length} photos? This action cannot be undone.`
              : 'Are you sure you want to delete this photo? This action cannot be undone.'
            }
          </AlertDialogDescription>
        </AlertDialogHeader>

        {/* Photo Preview */}
        <div className="space-y-4">
          {photos.slice(0, 3).map((photo, index) => (
            <div key={photo.id} className="flex items-center space-x-3 p-3 bg-slate-700/50 rounded-lg">
              <div className="flex-shrink-0">
                {photo.signed_url ? (
                  <img
                    src={photo.signed_url}
                    alt="Photo to delete"
                    className="w-12 h-12 object-cover rounded border border-slate-600"
                  />
                ) : (
                  <div className="w-12 h-12 bg-slate-600 rounded border border-slate-600 flex items-center justify-center">
                    <ImageIcon className="h-6 w-6 text-slate-400" />
                  </div>
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <Badge variant="secondary" className="bg-slate-600 text-slate-300 text-xs">
                    {photo.status_history?.status || 'Unknown Status'}
                  </Badge>
                </div>
                
                {photo.uploader_profile && (
                  <div className="flex items-center space-x-1 text-xs text-slate-400">
                    <User className="h-3 w-3" />
                    <span>{photo.uploader_profile.full_name || photo.uploader_profile.email}</span>
                  </div>
                )}
                
                <div className="flex items-center space-x-1 text-xs text-slate-400">
                  <Clock className="h-3 w-3" />
                  <span>{formatDistanceToNow(parseISO(photo.created_at), { addSuffix: true })}</span>
                </div>
              </div>
            </div>
          ))}
          
          {/* Show count if more than 3 photos */}
          {photos.length > 3 && (
            <div className="text-center p-3 bg-slate-700/30 rounded-lg">
              <p className="text-sm text-slate-400">
                +{photos.length - 3} more photo{photos.length - 3 !== 1 ? 's' : ''}
              </p>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <AlertDialogFooter>
          <AlertDialogCancel 
            disabled={isDeleting}
            className="bg-slate-600 border-slate-500 text-slate-200 hover:bg-slate-500"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Photo{isMultiple ? 's' : ''}
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Individual photo delete dialog for single photo actions
interface SinglePhotoDeleteDialogProps {
  photo: StatusPhoto | null
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: (deletedPhoto: StatusPhoto) => void
}

export function SinglePhotoDeleteDialog({
  photo,
  isOpen,
  onOpenChange,
  onSuccess,
}: SinglePhotoDeleteDialogProps) {
  if (!photo) return null

  return (
    <PhotoDeleteDialog
      photos={[photo]}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onSuccess={(deletedPhotos) => {
        if (onSuccess && deletedPhotos[0]) {
          onSuccess(deletedPhotos[0])
        }
      }}
    />
  )
}

// Bulk photo delete dialog with selection list
interface BulkPhotoDeleteDialogProps {
  selectedPhotos: StatusPhoto[]
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: (deletedPhotos: StatusPhoto[]) => void
}

export function BulkPhotoDeleteDialog({
  selectedPhotos,
  isOpen,
  onOpenChange,
  onSuccess,
}: BulkPhotoDeleteDialogProps) {
  if (selectedPhotos.length === 0) return null

  return (
    <PhotoDeleteDialog
      photos={selectedPhotos}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onSuccess={onSuccess}
    />
  )
}