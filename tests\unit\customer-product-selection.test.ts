/**
 * @file customer-product-selection.test.ts
 * @description Unit tests for Customer Product Selection Service
 */

import { describe, test, expect, vi, beforeEach } from 'vitest'
import type { CustomerProductRelationship } from '@/hooks/use-shipment-relationships'

// Mock Supabase client
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          limit: vi.fn(() => ({
            order: vi.fn(() => ({
              order: vi.fn(() => ({ data: [], error: null })),
            })),
          })),
          order: vi.fn(() => ({
            order: vi.fn(() => ({ data: [], error: null })),
          })),
          or: vi.fn(() => ({
            limit: vi.fn(() => ({ data: [], error: null })),
          })),
        })),
        single: vi.fn(),
        limit: vi.fn(() => ({
          order: vi.fn(() => ({
            order: vi.fn(() => ({ data: [], error: null })),
          })),
        })),
        order: vi.fn(() => ({
          order: vi.fn(() => ({ data: [], error: null })),
        })),
      })),
    })),
  })),
}

// Mock the Supabase client module
vi.mock('@/lib/supabase/client', () => ({
  createClient: () => mockSupabase,
}))

// Import after mocking
const { CustomerProductSelectionService } = await vi.importActual('@/lib/services/customer-product-selection') as any

describe('CustomerProductSelectionService', () => {
  let service: CustomerProductSelectionService
  const mockCustomerId = '123e4567-e89b-12d3-a456-426614174000'
  const mockProductId = '123e4567-e89b-12d3-a456-426614174001'

  beforeEach(() => {
    service = new CustomerProductSelectionService()
    vi.clearAllMocks()
  })

  describe('analyzeCustomerProducts', () => {
    test('should return single mode for single product', async () => {
      const mockSingleProduct = [{
        id: '1',
        customer_id: mockCustomerId,
        product_id: mockProductId,
        is_default: true,
        is_active: true,
        unit_price_cif: 100,
        unit_price_fob: 90,
        currency_code: 'USD',
        quality_grade: 'A',
        notes: null,
        created_at: '2023-01-01T00:00:00Z',
        product: {
          id: mockProductId,
          name: 'Test Product',
          code: 'TEST001',
          unit_of_measure_id: 'kg',
        },
      }]

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                order: vi.fn(() => ({ data: mockSingleProduct, error: null })),
              })),
            })),
          })),
        })),
      })

      const result = await service.analyzeCustomerProducts({
        customerId: mockCustomerId,
        autoSelectSingle: true,
      })

      expect(result.mode).toBe('single')
      expect(result.products).toHaveLength(1)
      expect(result.autoSelected).toBeDefined()
      expect(result.autoSelected?.product_id).toBe(mockProductId)
      expect(result.totalCount).toBe(1)
    })

    test('should return multiple mode for multiple products', async () => {
      const mockMultipleProducts = [
        {
          id: '1',
          customer_id: mockCustomerId,
          product_id: mockProductId,
          is_default: true,
          is_active: true,
          unit_price_cif: 100,
          unit_price_fob: 90,
          currency_code: 'USD',
          quality_grade: 'A',
          notes: null,
          created_at: '2023-01-01T00:00:00Z',
          product: {
            id: mockProductId,
            name: 'Test Product 1',
            code: 'TEST001',
            unit_of_measure_id: 'kg',
          },
        },
        {
          id: '2',
          customer_id: mockCustomerId,
          product_id: '123e4567-e89b-12d3-a456-426614174002',
          is_default: false,
          is_active: true,
          unit_price_cif: 150,
          unit_price_fob: 140,
          currency_code: 'USD',
          quality_grade: 'B',
          notes: null,
          created_at: '2023-01-01T00:00:00Z',
          product: {
            id: '123e4567-e89b-12d3-a456-426614174002',
            name: 'Test Product 2',
            code: 'TEST002',
            unit_of_measure_id: 'kg',
          },
        },
      ]

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                order: vi.fn(() => ({ data: mockMultipleProducts, error: null })),
              })),
            })),
          })),
        })),
      })

      const result = await service.analyzeCustomerProducts({
        customerId: mockCustomerId,
        preferDefaults: true,
      })

      expect(result.mode).toBe('multiple')
      expect(result.products).toHaveLength(2)
      expect(result.defaultProduct).toBeDefined()
      expect(result.defaultProduct?.is_default).toBe(true)
      expect(result.autoSelected?.is_default).toBe(true)
      expect(result.totalCount).toBe(2)
    })

    test('should return none mode for no products', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                order: vi.fn(() => ({ data: [], error: null })),
              })),
            })),
          })),
        })),
      })

      const result = await service.analyzeCustomerProducts({
        customerId: mockCustomerId,
      })

      expect(result.mode).toBe('none')
      expect(result.products).toHaveLength(0)
      expect(result.autoSelected).toBeUndefined()
      expect(result.totalCount).toBe(0)
    })

    test('should handle database errors gracefully', async () => {
      const mockError = { message: 'Database connection failed' }
      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                order: vi.fn(() => ({ data: null, error: mockError })),
              })),
            })),
          })),
        })),
      })

      await expect(service.analyzeCustomerProducts({
        customerId: mockCustomerId,
      })).rejects.toThrow('Failed to fetch customer products: Database connection failed')
    })
  })

  describe('validateProductSelection', () => {
    test('should validate successful product selection', async () => {
      const mockCustomer = { id: mockCustomerId, name: 'Test Customer' }
      const mockProductRelationship: CustomerProductRelationship = {
        id: '1',
        customer_id: mockCustomerId,
        product_id: mockProductId,
        product: {
          id: mockProductId,
          name: 'Test Product',
          code: 'TEST001',
          unit_of_measure_id: 'kg',
        },
        is_default: true,
        is_active: true,
        unit_price_cif: 100,
        unit_price_fob: 90,
        currency_code: 'USD',
        quality_grade: 'A',
        notes: null,
        created_at: '2023-01-01T00:00:00Z',
      }

      // Mock customer query
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'companies') {
          return {
            select: vi.fn(() => ({
              eq: vi.fn(() => ({
                single: vi.fn(() => ({ data: mockCustomer, error: null })),
              })),
            })),
          }
        }
        
        if (table === 'customer_products') {
          return {
            select: vi.fn(() => ({
              eq: vi.fn(() => ({
                eq: vi.fn(() => ({
                  eq: vi.fn(() => ({
                    single: vi.fn(() => ({ data: mockProductRelationship, error: null })),
                  })),
                })),
              })),
            })),
          }
        }
        
        return mockSupabase.from()
      })

      // Mock getProductRelationship to return valid relationship
      vi.spyOn(service, 'getProductRelationship').mockResolvedValue(mockProductRelationship)

      const result = await service.validateProductSelection(mockCustomerId, mockProductId)

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.relationship).toBeDefined()
      expect(result.relationship?.product_id).toBe(mockProductId)
    })

    test('should fail validation for non-existent customer', async () => {
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'companies') {
          return {
            select: vi.fn(() => ({
              eq: vi.fn(() => ({
                single: vi.fn(() => ({ data: null, error: { message: 'Not found' } })),
              })),
            })),
          }
        }
        
        return mockSupabase.from()
      })

      vi.spyOn(service, 'getProductRelationship').mockResolvedValue(null)

      const result = await service.validateProductSelection(mockCustomerId, mockProductId)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Customer not found')
      expect(result.errors).toContain('No active product relationship found for this customer-product combination')
    })
  })

  describe('getProductPricing', () => {
    test('should calculate correct pricing for quantity', async () => {
      const mockProductRelationship: CustomerProductRelationship = {
        id: '1',
        customer_id: mockCustomerId,
        product_id: mockProductId,
        product: {
          id: mockProductId,
          name: 'Test Product',
          code: 'TEST001',
          unit_of_measure_id: 'kg',
        },
        is_default: true,
        is_active: true,
        unit_price_cif: 100,
        unit_price_fob: 90,
        currency_code: 'USD',
        quality_grade: 'A',
        notes: null,
        created_at: '2023-01-01T00:00:00Z',
        net_weight_per_package: 1, // Added for correct calculation: quantity × net_weight × unit_price
        gross_weight_per_package: 1.2,
        packaging_type: 'box',
        standard_quantity: 1,
        shelf_life_days: 90,
      }

      vi.spyOn(service, 'getProductRelationship').mockResolvedValue(mockProductRelationship)

      const result = await service.getProductPricing(mockCustomerId, mockProductId, 10)

      expect(result).not.toBeNull()
      expect(result?.unit_price_cif).toBe(100)
      expect(result?.unit_price_fob).toBe(90)
      expect(result?.total_value_cif).toBe(1000)
      expect(result?.total_value_fob).toBe(900)
      expect(result?.currency_code).toBe('USD')
    })

    test('should return null for non-existent relationship', async () => {
      vi.spyOn(service, 'getProductRelationship').mockResolvedValue(null)

      const result = await service.getProductPricing(mockCustomerId, mockProductId, 10)

      expect(result).toBeNull()
    })
  })

  describe('searchCustomerProducts', () => {
    test('should search products by name', async () => {
      const mockSearchResults = [{
        id: '1',
        customer_id: mockCustomerId,
        product_id: mockProductId,
        is_default: true,
        is_active: true,
        unit_price_cif: 100,
        unit_price_fob: 90,
        currency_code: 'USD',
        quality_grade: 'A',
        notes: null,
        created_at: '2023-01-01T00:00:00Z',
        product: {
          id: mockProductId,
          name: 'Organic Apple',
          code: 'APPLE001',
          unit_of_measure_id: 'kg',
        },
      }]

      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              or: vi.fn(() => ({
                limit: vi.fn(() => ({ data: mockSearchResults, error: null })),
              })),
            })),
          })),
        })),
      })

      const result = await service.searchCustomerProducts(mockCustomerId, 'Apple')

      expect(result).toHaveLength(1)
      expect(result[0].product.name).toBe('Organic Apple')
    })
  })
})