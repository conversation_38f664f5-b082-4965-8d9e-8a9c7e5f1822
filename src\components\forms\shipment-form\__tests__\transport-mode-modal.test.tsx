import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TransportModeModal } from '../transport-mode-modal'

// Mock the hooks
vi.mock('@/hooks/use-shipments', () => ({
  useShipments: () => ({
    generateShipmentNumber: vi.fn().mockResolvedValue({
      fullNumber: 'EXSEA-THBKK-240315-001',
      prefix: 'EX',
      modeCode: 'SEA',
      portCode: 'THBKK',
      dateCode: '240315',
      runningNumber: 1,
    }),
  }),
}))

vi.mock('@/stores/shipment-creation-store', () => ({
  useShipmentCreationStore: () => ({
    updateFormData: vi.fn(),
    setStep: vi.fn(),
    formData: {
      portOfLoading: 'THBKK',
    },
  }),
}))

describe('TransportModeModal', () => {
  const defaultProps = {
    open: true,
    onOpenChange: vi.fn(),
    onSelect: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render modal when open', () => {
    render(<TransportModeModal {...defaultProps} />)

    expect(screen.getByText('Choose Transportation Mode')).toBeInTheDocument()
    expect(
      screen.getByText(
        'Select the primary mode of transportation for your shipment'
      )
    ).toBeInTheDocument()
  })

  it('should not render modal when closed', () => {
    render(<TransportModeModal {...defaultProps} open={false} />)

    expect(
      screen.queryByText('Choose Transportation Mode')
    ).not.toBeInTheDocument()
  })

  it('should render all transport mode options', () => {
    render(<TransportModeModal {...defaultProps} />)

    expect(screen.getByText('Sea Freight')).toBeInTheDocument()
    expect(screen.getByText('Land Transport')).toBeInTheDocument()
    expect(screen.getByText('Rail Transport')).toBeInTheDocument()
  })

  it('should display correct icons for each transport mode', () => {
    render(<TransportModeModal {...defaultProps} />)

    // Check for transport mode cards
    const seaCard = screen.getByText('Sea Freight').closest('[role="button"]')
    const landCard = screen
      .getByText('Land Transport')
      .closest('[role="button"]')
    const railCard = screen
      .getByText('Rail Transport')
      .closest('[role="button"]')

    expect(seaCard).toBeInTheDocument()
    expect(landCard).toBeInTheDocument()
    expect(railCard).toBeInTheDocument()
  })

  it('should show detailed information for each transport mode', () => {
    render(<TransportModeModal {...defaultProps} />)

    // Sea freight details
    expect(
      screen.getByText('Best for international shipping')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Cost-effective for large volumes')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Environmental friendly option')
    ).toBeInTheDocument()

    // Land transport details
    expect(screen.getByText('Door-to-door delivery')).toBeInTheDocument()
    expect(screen.getByText('Flexible scheduling')).toBeInTheDocument()
    expect(screen.getByText('Ideal for regional transport')).toBeInTheDocument()

    // Rail transport details
    expect(screen.getByText('Eco-friendly bulk transport')).toBeInTheDocument()
    expect(screen.getByText('Reliable scheduling')).toBeInTheDocument()
    expect(
      screen.getByText('Cost-effective for long distances')
    ).toBeInTheDocument()
  })

  it('should handle sea freight selection', async () => {
    const user = userEvent.setup()
    render(<TransportModeModal {...defaultProps} />)

    const seaOption = screen.getByText('Sea Freight').closest('[role="button"]')
    expect(seaOption).toBeInTheDocument()

    await user.click(seaOption!)

    await waitFor(() => {
      expect(defaultProps.onSelect).toHaveBeenCalledWith('sea')
    })
  })

  it('should handle land transport selection', async () => {
    const user = userEvent.setup()
    render(<TransportModeModal {...defaultProps} />)

    const landOption = screen
      .getByText('Land Transport')
      .closest('[role="button"]')
    expect(landOption).toBeInTheDocument()

    await user.click(landOption!)

    await waitFor(() => {
      expect(defaultProps.onSelect).toHaveBeenCalledWith('land')
    })
  })

  it('should handle rail transport selection', async () => {
    const user = userEvent.setup()
    render(<TransportModeModal {...defaultProps} />)

    const railOption = screen
      .getByText('Rail Transport')
      .closest('[role="button"]')
    expect(railOption).toBeInTheDocument()

    await user.click(railOption!)

    await waitFor(() => {
      expect(defaultProps.onSelect).toHaveBeenCalledWith('rail')
    })
  })

  it('should show hover effects on transport mode options', async () => {
    const user = userEvent.setup()
    render(<TransportModeModal {...defaultProps} />)

    const seaOption = screen.getByText('Sea Freight').closest('[role="button"]')
    expect(seaOption).toBeInTheDocument()

    await user.hover(seaOption!)

    // Check for hover state classes
    expect(seaOption).toHaveClass('hover:bg-slate-50', 'hover:border-blue-200')
  })

  it('should show requirements for each transport mode', () => {
    render(<TransportModeModal {...defaultProps} />)

    // Sea freight requirements
    expect(screen.getByText('Bill of Lading')).toBeInTheDocument()
    expect(screen.getByText('Commercial Invoice')).toBeInTheDocument()
    expect(screen.getByText('Packing List')).toBeInTheDocument()

    // Land transport requirements
    expect(screen.getByText('Commercial Invoice')).toBeInTheDocument()
    expect(screen.getByText('Delivery Receipt')).toBeInTheDocument()
    expect(screen.getByText('Transport Permit')).toBeInTheDocument()

    // Rail transport requirements
    expect(screen.getByText('Rail Waybill')).toBeInTheDocument()
    expect(screen.getByText('Commercial Invoice')).toBeInTheDocument()
    expect(screen.getByText('Loading Certificate')).toBeInTheDocument()
  })

  it('should show documentation needs section', () => {
    render(<TransportModeModal {...defaultProps} />)

    expect(screen.getByText('Documentation Needs')).toBeInTheDocument()
    expect(
      screen.getByText('Required documents will be requested in the next steps')
    ).toBeInTheDocument()
  })

  it('should handle modal close via overlay click', async () => {
    const user = userEvent.setup()
    render(<TransportModeModal {...defaultProps} />)

    // Click overlay (outside modal content)
    const overlay = screen.getByRole('dialog').firstElementChild
    expect(overlay).toBeInTheDocument()

    await user.click(overlay!)

    expect(defaultProps.onOpenChange).toHaveBeenCalledWith(false)
  })

  it('should handle keyboard navigation', async () => {
    const user = userEvent.setup()
    render(<TransportModeModal {...defaultProps} />)

    const seaOption = screen.getByText('Sea Freight').closest('[role="button"]')
    expect(seaOption).toBeInTheDocument()

    await user.tab()
    expect(seaOption).toHaveFocus()

    await user.keyboard('{Enter}')
    expect(defaultProps.onSelect).toHaveBeenCalledWith('sea')
  })

  it('should show correct transport mode features', () => {
    render(<TransportModeModal {...defaultProps} />)

    // Sea freight features
    expect(screen.getByText('Container shipping')).toBeInTheDocument()
    expect(screen.getByText('Bulk cargo')).toBeInTheDocument()
    expect(screen.getByText('International routes')).toBeInTheDocument()

    // Land transport features
    expect(screen.getByText('Truck delivery')).toBeInTheDocument()
    expect(screen.getByText('Express options')).toBeInTheDocument()
    expect(screen.getByText('Local/regional')).toBeInTheDocument()

    // Rail transport features
    expect(screen.getByText('Intermodal containers')).toBeInTheDocument()
    expect(screen.getByText('Bulk commodities')).toBeInTheDocument()
    expect(screen.getByText('Cross-border routes')).toBeInTheDocument()
  })

  it('should be accessible with proper ARIA attributes', () => {
    render(<TransportModeModal {...defaultProps} />)

    const dialog = screen.getByRole('dialog')
    expect(dialog).toHaveAttribute('aria-describedby')

    const transportOptions = screen.getAllByRole('button')
    transportOptions.forEach(option => {
      expect(option).toHaveAttribute('tabIndex', '0')
    })
  })

  it('should handle escape key to close modal', async () => {
    const user = userEvent.setup()
    render(<TransportModeModal {...defaultProps} />)

    await user.keyboard('{Escape}')

    expect(defaultProps.onOpenChange).toHaveBeenCalledWith(false)
  })

  it('should prevent selection when loading', () => {
    const onSelect = vi.fn()
    render(<TransportModeModal {...defaultProps} onSelect={onSelect} />)

    // Simulate loading state by checking if buttons are disabled during async operations
    const seaOption = screen.getByText('Sea Freight').closest('[role="button"]')

    fireEvent.click(seaOption!)

    // First click should work
    expect(onSelect).toHaveBeenCalledWith('sea')
  })

  it('should show transport mode descriptions correctly', () => {
    render(<TransportModeModal {...defaultProps} />)

    // Check for transport mode descriptions
    expect(
      screen.getByText(/Ocean freight for international shipping/)
    ).toBeInTheDocument()
    expect(
      screen.getByText(/Road transport for door-to-door delivery/)
    ).toBeInTheDocument()
    expect(
      screen.getByText(/Railway transport for efficient bulk movement/)
    ).toBeInTheDocument()
  })
})
