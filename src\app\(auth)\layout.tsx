import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Authentication | DYY Trading Management',
  description: 'Sign in to access your DYY Trading Management account',
}

interface AuthLayoutProps {
  children: React.ReactNode
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-md space-y-8">
          {/* Logo and Header */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 rounded-lg bg-orange-500 flex items-center justify-center mb-4">
              <span className="text-2xl font-bold text-white">DYY</span>
            </div>
            <h1 className="text-3xl font-bold text-white">
              Trading Management
            </h1>
            <p className="mt-2 text-sm text-slate-300">
              Global logistics and supply chain management
            </p>
          </div>

          {/* Auth Form Container */}
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-8 shadow-2xl">
            {children}
          </div>

          {/* Footer */}
          <div className="text-center text-xs text-slate-400">
            <p>© 2025 DYY Trading Management. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
