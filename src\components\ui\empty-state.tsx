'use client'

import { ReactNode } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { LucideIcon } from 'lucide-react'

interface EmptyStateProps {
  icon?: LucideIcon
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline' | 'secondary'
  }
  children?: ReactNode
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  children,
}: EmptyStateProps) {
  return (
    <div className="text-center py-8 px-4">
      {Icon && (
        <div className="flex justify-center mb-4">
          <Icon className="h-12 w-12 text-slate-400" />
        </div>
      )}
      <h3 className="text-lg font-medium text-slate-200 mb-2">{title}</h3>
      <p className="text-slate-400 mb-4">{description}</p>

      {action && (
        <Button
          onClick={action.onClick}
          variant={action.variant || 'default'}
          className="mb-4"
        >
          {action.label}
        </Button>
      )}

      {children}
    </div>
  )
}
