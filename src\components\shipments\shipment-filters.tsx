'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useShipmentStore } from '@/stores/shipment-store'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { X, ChevronDown, ChevronUp, CalendarDays } from 'lucide-react'
import type { 
  ShipmentStatus, 
  TransportMode, 
  Company, 
  Port 
} from '@/lib/supabase/types'

// Filter section component for collapsible sections
interface FilterSectionProps {
  title: string
  children: React.ReactNode
  defaultExpanded?: boolean
}

function FilterSection({ title, children, defaultExpanded = true }: FilterSectionProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  
  return (
    <div className="space-y-2">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full text-left text-sm font-medium text-slate-300 hover:text-white"
      >
        {title}
        {isExpanded ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </button>
      
      {isExpanded && (
        <div className="space-y-3 pl-2">
          {children}
        </div>
      )}
    </div>
  )
}

export function ShipmentFilters() {
  const supabase = createClient()
  
  const {
    filters,
    updateFilters,
    addStatusFilter,
    removeStatusFilter,
    addCustomerFilter,
    removeCustomerFilter,
    setDateRange,
    clearFilters,
  } = useShipmentStore()

  // Local state for dropdown data
  const [customers, setCustomers] = useState<Company[]>([])
  const [originPorts, setOriginPorts] = useState<Port[]>([])
  const [destinationPorts, setDestinationPorts] = useState<Port[]>([])
  const [isLoadingData, setIsLoadingData] = useState(true)

  // Load reference data
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        setIsLoadingData(true)
        
        const [customersResult, originPortsResult, destinationPortsResult] = await Promise.all([
          supabase
            .from('companies')
            .select('id, name, company_type')
            .eq('company_type', 'customer')
            .order('name'),
          supabase
            .from('ports')
            .select('id, name, code, country, port_type')
            .in('port_type', ['origin', 'transit'])
            .eq('is_active', true)
            .order('name'),
          supabase
            .from('ports')
            .select('id, name, code, country, port_type')
            .in('port_type', ['destination', 'transit'])
            .eq('is_active', true)
            .order('name')
        ])

        if (customersResult.data) setCustomers(customersResult.data)
        if (originPortsResult.data) setOriginPorts(originPortsResult.data)
        if (destinationPortsResult.data) setDestinationPorts(destinationPortsResult.data)
        
      } catch (error) {
        console.error('Error loading reference data:', error)
      } finally {
        setIsLoadingData(false)
      }
    }

    loadReferenceData()
  }, [supabase])

  // Status options
  const statusOptions: { value: ShipmentStatus; label: string }[] = [
    { value: 'booking_confirmed', label: 'Booking Confirmed' },
    { value: 'transport_assigned', label: 'Transport Assigned' },
    { value: 'driver_assigned', label: 'Driver Assigned' },
    { value: 'empty_container_picked', label: 'Empty Container Picked' },
    { value: 'arrived_at_factory', label: 'Arrived at Factory' },
    { value: 'loading_started', label: 'Loading Started' },
    { value: 'departed_factory', label: 'Departed Factory' },
    { value: 'container_returned', label: 'Container Returned' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'arrived', label: 'Arrived' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
  ]

  // Transport mode options
  const transportModeOptions: { value: TransportMode; label: string }[] = [
    { value: 'sea', label: 'Sea' },
    { value: 'air', label: 'Air' },
    { value: 'road', label: 'Road' },
    { value: 'rail', label: 'Rail' },
  ]

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white text-lg">Filters</CardTitle>
          <Button
            size="sm"
            variant="outline"
            onClick={clearFilters}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
          >
            Clear All
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status Filter */}
        <FilterSection title="Status">
          <div className="space-y-2">
            {statusOptions.map((status) => (
              <div key={status.value} className="flex items-center space-x-2">
                <Checkbox
                  id={status.value}
                  checked={filters.status.includes(status.value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      addStatusFilter(status.value)
                    } else {
                      removeStatusFilter(status.value)
                    }
                  }}
                  className="border-slate-400 bg-slate-700 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 data-[state=checked]:text-white"
                />
                <Label
                  htmlFor={status.value}
                  className="text-sm text-slate-300 cursor-pointer"
                >
                  {status.label}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Transportation Mode Filter */}
        <FilterSection title="Transportation Mode">
          <div className="space-y-2">
            {transportModeOptions.map((mode) => (
              <div key={mode.value} className="flex items-center space-x-2">
                <Checkbox
                  id={mode.value}
                  checked={filters.transportation_mode.includes(mode.value)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      updateFilters({
                        transportation_mode: [...filters.transportation_mode, mode.value]
                      })
                    } else {
                      updateFilters({
                        transportation_mode: filters.transportation_mode.filter(m => m !== mode.value)
                      })
                    }
                  }}
                  className="border-slate-400 bg-slate-700 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 data-[state=checked]:text-white"
                />
                <Label
                  htmlFor={mode.value}
                  className="text-sm text-slate-300 cursor-pointer"
                >
                  {mode.label}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Customer Filter */}
        <FilterSection title="Customer">
          <div className="space-y-2">
            <Select
              onValueChange={(value) => addCustomerFilter(value)}
              disabled={isLoadingData}
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder={isLoadingData ? "Loading..." : "Select customer"} />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600 text-white">
                {customers.map((customer) => (
                  <SelectItem key={customer.id} value={customer.id}>
                    {customer.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Selected customers */}
            {filters.customer_id.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {filters.customer_id.map((customerId) => {
                  const customer = customers.find(c => c.id === customerId)
                  return (
                    <Badge
                      key={customerId}
                      variant="secondary"
                      className="bg-slate-700 text-slate-300"
                    >
                      {customer?.name || 'Unknown'}
                      <button
                        onClick={() => removeCustomerFilter(customerId)}
                        className="ml-1 hover:text-white"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  )
                })}
              </div>
            )}
          </div>
        </FilterSection>

        {/* Date Range Filter */}
        <FilterSection title="ETD Date Range">
          <div className="space-y-2">
            <div className="grid grid-cols-1 gap-2">
              <div>
                <Label className="text-sm text-slate-300">From</Label>
                <div className="relative">
                  <Input
                    type="date"
                    value={filters.date_range.start || ''}
                    onChange={(e) => setDateRange(e.target.value, filters.date_range.end)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                  <CalendarDays className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 pointer-events-none" />
                </div>
              </div>
              
              <div>
                <Label className="text-sm text-slate-300">To</Label>
                <div className="relative">
                  <Input
                    type="date"
                    value={filters.date_range.end || ''}
                    onChange={(e) => setDateRange(filters.date_range.start, e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                  <CalendarDays className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 pointer-events-none" />
                </div>
              </div>
            </div>
            
            {(filters.date_range.start || filters.date_range.end) && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => setDateRange(null, null)}
                className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
              >
                Clear Date Range
              </Button>
            )}
          </div>
        </FilterSection>

        {/* Origin Port Filter */}
        <FilterSection title="Origin Port" defaultExpanded={false}>
          <div className="space-y-2">
            <Select
              onValueChange={(value) => {
                updateFilters({
                  origin_port_id: [...filters.origin_port_id, value]
                })
              }}
              disabled={isLoadingData}
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder={isLoadingData ? "Loading..." : "Select origin port"} />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600 text-white">
                {originPorts.map((port) => (
                  <SelectItem key={port.id} value={port.id}>
                    {port.name} ({port.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Selected origin ports */}
            {filters.origin_port_id.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {filters.origin_port_id.map((portId) => {
                  const port = originPorts.find(p => p.id === portId)
                  return (
                    <Badge
                      key={portId}
                      variant="secondary"
                      className="bg-slate-700 text-slate-300"
                    >
                      {port?.code || 'Unknown'}
                      <button
                        onClick={() => {
                          updateFilters({
                            origin_port_id: filters.origin_port_id.filter(id => id !== portId)
                          })
                        }}
                        className="ml-1 hover:text-white"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  )
                })}
              </div>
            )}
          </div>
        </FilterSection>

        {/* Destination Port Filter */}
        <FilterSection title="Destination Port" defaultExpanded={false}>
          <div className="space-y-2">
            <Select
              onValueChange={(value) => {
                updateFilters({
                  destination_port_id: [...filters.destination_port_id, value]
                })
              }}
              disabled={isLoadingData}
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder={isLoadingData ? "Loading..." : "Select destination port"} />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600 text-white">
                {destinationPorts.map((port) => (
                  <SelectItem key={port.id} value={port.id}>
                    {port.name} ({port.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Selected destination ports */}
            {filters.destination_port_id.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {filters.destination_port_id.map((portId) => {
                  const port = destinationPorts.find(p => p.id === portId)
                  return (
                    <Badge
                      key={portId}
                      variant="secondary"
                      className="bg-slate-700 text-slate-300"
                    >
                      {port?.code || 'Unknown'}
                      <button
                        onClick={() => {
                          updateFilters({
                            destination_port_id: filters.destination_port_id.filter(id => id !== portId)
                          })
                        }}
                        className="ml-1 hover:text-white"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  )
                })}
              </div>
            )}
          </div>
        </FilterSection>
      </CardContent>
    </Card>
  )
}