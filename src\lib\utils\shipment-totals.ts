import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/supabase/types'

export interface ShipmentTotals {
  total_quantity: number
  total_value_cif: number
  total_value_fob: number
  total_gross_weight: number
  total_net_weight: number
}

/**
 * Calculate shipment totals from all associated shipment_products
 */
export async function calculateShipmentTotals(
  supabase: SupabaseClient<Database>,
  shipmentId: string
): Promise<ShipmentTotals> {
  try {
    const { data: products, error } = await supabase
      .from('shipment_products')
      .select('quantity, total_value_cif, total_value_fob, total_gross_weight, total_net_weight')
      .eq('shipment_id', shipmentId)

    if (error) {
      throw new Error(`Failed to fetch shipment products: ${error.message}`)
    }

    // Calculate totals
    const totals = products.reduce(
      (acc, product) => ({
        total_quantity: acc.total_quantity + (product.quantity || 0),
        total_value_cif: acc.total_value_cif + (product.total_value_cif || 0),
        total_value_fob: acc.total_value_fob + (product.total_value_fob || 0),
        total_gross_weight: acc.total_gross_weight + (product.total_gross_weight || 0),
        total_net_weight: acc.total_net_weight + (product.total_net_weight || 0),
      }),
      {
        total_quantity: 0,
        total_value_cif: 0,
        total_value_fob: 0,
        total_gross_weight: 0,
        total_net_weight: 0,
      }
    )

    return totals
  } catch (error: any) {
    console.error('Error calculating shipment totals:', error)
    throw error
  }
}

/**
 * Update shipment totals based on current shipment_products
 */
export async function updateShipmentTotals(
  supabase: SupabaseClient<Database>,
  shipmentId: string
): Promise<void> {
  try {
    // Calculate new totals
    const totals = await calculateShipmentTotals(supabase, shipmentId)

    // Update shipment record with fields that exist in the database
    const { error } = await supabase
      .from('shipments')
      .update({
        total_weight: totals.total_gross_weight, // Use gross weight for total_weight
        total_quantity: totals.total_quantity,
        total_value_cif: totals.total_value_cif,
        total_value_fob: totals.total_value_fob,
        total_gross_weight: totals.total_gross_weight,
        total_net_weight: totals.total_net_weight,
        updated_at: new Date().toISOString(),
      } as any) // Type assertion to allow fields not in TypeScript types
      .eq('id', shipmentId)

    if (error) {
      throw new Error(`Failed to update shipment totals: ${error.message}`)
    }

    console.log('✓ Updated shipment totals:', {
      total_quantity: totals.total_quantity, // Calculated but not stored in DB
      total_weight: totals.total_gross_weight,
      total_value_cif: totals.total_value_cif,
      total_value_fob: totals.total_value_fob,
      total_gross_weight: totals.total_gross_weight,
      total_net_weight: totals.total_net_weight,
    })
  } catch (error: any) {
    console.error('Error updating shipment totals:', error)
    throw error
  }
}

/**
 * Calculate totals from a single shipment product data
 */
export function calculateProductTotals(product: {
  quantity: number
  unit_price_cif?: number | null
  unit_price_fob?: number | null
  gross_weight?: number | null
  net_weight?: number | null
}): {
  total_value_cif: number
  total_value_fob: number
  total_gross_weight: number
  total_net_weight: number
} {
  const quantity = product.quantity || 0
  const unitPriceCif = product.unit_price_cif || 0
  const unitPriceFob = product.unit_price_fob || 0
  const grossWeight = product.gross_weight || 0
  const netWeight = product.net_weight || 0

  return {
    total_value_cif: Math.round((quantity * netWeight * unitPriceCif) * 100) / 100,
    total_value_fob: Math.round((quantity * netWeight * unitPriceFob) * 100) / 100,
    total_gross_weight: Math.round((quantity * grossWeight) * 100) / 100,
    total_net_weight: Math.round((quantity * netWeight) * 100) / 100,
  }
}