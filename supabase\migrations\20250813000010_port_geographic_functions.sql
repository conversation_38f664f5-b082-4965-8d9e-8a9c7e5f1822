-- Port Geographic Functions
-- Story 2.2: Ports and Location Data Management
-- This migration creates PostGIS functions for port geographic search capabilities

-- ============================================================================
-- GEOGRAPHIC SEARCH FUNCTIONS
-- ============================================================================

-- Function to find ports within a specified radius from a center point
CREATE OR REPLACE FUNCTION find_ports_within_radius(
    center_lat FLOAT,
    center_lng FLOAT,
    radius_km INTEGER DEFAULT 100
)
RETURNS TABLE (
    id UUID,
    code TEXT,
    name TEXT,
    city TEXT,
    country TEXT,
    port_type port_type_enum,
    gps_coordinates GEOGRAPHY,
    distance_km NUMERIC,
    is_active BOOLEAN,
    timezone TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.code,
        p.name,
        p.city,
        p.country,
        p.port_type,
        p.gps_coordinates,
        ROUND(
            ST_Distance(
                ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
                p.gps_coordinates
            ) / 1000, 2
        ) AS distance_km,
        p.is_active,
        p.timezone,
        p.created_at,
        p.updated_at
    FROM ports p
    WHERE p.is_active = true
    AND p.gps_coordinates IS NOT NULL
    AND ST_DWithin(
        ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
        p.gps_coordinates,
        radius_km * 1000 -- Convert km to meters
    )
    ORDER BY ST_Distance(
        ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
        p.gps_coordinates
    );
END;
$$;

-- Function to calculate distance between two ports
CREATE OR REPLACE FUNCTION calculate_port_distance(
    port1_id UUID,
    port2_id UUID
)
RETURNS NUMERIC
LANGUAGE plpgsql
AS $$
DECLARE
    distance_km NUMERIC;
BEGIN
    SELECT ROUND(
        ST_Distance(
            p1.gps_coordinates,
            p2.gps_coordinates
        ) / 1000, 2
    )
    INTO distance_km
    FROM ports p1, ports p2
    WHERE p1.id = port1_id 
    AND p2.id = port2_id
    AND p1.gps_coordinates IS NOT NULL
    AND p2.gps_coordinates IS NOT NULL;
    
    RETURN COALESCE(distance_km, 0);
END;
$$;

-- Function to get ports by country with distance from a reference point
CREATE OR REPLACE FUNCTION get_ports_by_country_with_distance(
    target_country TEXT,
    ref_lat FLOAT DEFAULT NULL,
    ref_lng FLOAT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    code TEXT,
    name TEXT,
    city TEXT,
    country TEXT,
    port_type port_type_enum,
    gps_coordinates GEOGRAPHY,
    distance_km NUMERIC,
    is_active BOOLEAN,
    timezone TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
    IF ref_lat IS NULL OR ref_lng IS NULL THEN
        -- Return without distance calculation
        RETURN QUERY
        SELECT 
            p.id,
            p.code,
            p.name,
            p.city,
            p.country,
            p.port_type,
            p.gps_coordinates,
            NULL::NUMERIC AS distance_km,
            p.is_active,
            p.timezone,
            p.created_at,
            p.updated_at
        FROM ports p
        WHERE p.country ILIKE '%' || target_country || '%'
        AND p.is_active = true
        ORDER BY p.name;
    ELSE
        -- Return with distance calculation
        RETURN QUERY
        SELECT 
            p.id,
            p.code,
            p.name,
            p.city,
            p.country,
            p.port_type,
            p.gps_coordinates,
            CASE 
                WHEN p.gps_coordinates IS NOT NULL THEN
                    ROUND(
                        ST_Distance(
                            ST_GeogFromText('POINT(' || ref_lng || ' ' || ref_lat || ')'),
                            p.gps_coordinates
                        ) / 1000, 2
                    )
                ELSE NULL
            END AS distance_km,
            p.is_active,
            p.timezone,
            p.created_at,
            p.updated_at
        FROM ports p
        WHERE p.country ILIKE '%' || target_country || '%'
        AND p.is_active = true
        ORDER BY 
            CASE 
                WHEN p.gps_coordinates IS NOT NULL THEN
                    ST_Distance(
                        ST_GeogFromText('POINT(' || ref_lng || ' ' || ref_lat || ')'),
                        p.gps_coordinates
                    )
                ELSE 999999999 -- Put ports without coordinates at the end
            END;
    END IF;
END;
$$;

-- Function to find nearest ports to a given point
CREATE OR REPLACE FUNCTION find_nearest_ports(
    center_lat FLOAT,
    center_lng FLOAT,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    code TEXT,
    name TEXT,
    city TEXT,
    country TEXT,
    port_type port_type_enum,
    gps_coordinates GEOGRAPHY,
    distance_km NUMERIC,
    is_active BOOLEAN,
    timezone TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.code,
        p.name,
        p.city,
        p.country,
        p.port_type,
        p.gps_coordinates,
        ROUND(
            ST_Distance(
                ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
                p.gps_coordinates
            ) / 1000, 2
        ) AS distance_km,
        p.is_active,
        p.timezone
    FROM ports p
    WHERE p.is_active = true
    AND p.gps_coordinates IS NOT NULL
    ORDER BY ST_Distance(
        ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
        p.gps_coordinates
    )
    LIMIT limit_count;
END;
$$;

-- Function to get ports within a geographic bounding box
CREATE OR REPLACE FUNCTION get_ports_in_bounds(
    min_lat FLOAT,
    min_lng FLOAT,
    max_lat FLOAT,
    max_lng FLOAT
)
RETURNS TABLE (
    id UUID,
    code TEXT,
    name TEXT,
    city TEXT,
    country TEXT,
    port_type port_type_enum,
    gps_coordinates GEOGRAPHY,
    is_active BOOLEAN,
    timezone TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.code,
        p.name,
        p.city,
        p.country,
        p.port_type,
        p.gps_coordinates,
        p.is_active,
        p.timezone
    FROM ports p
    WHERE p.is_active = true
    AND p.gps_coordinates IS NOT NULL
    AND ST_Intersects(
        p.gps_coordinates,
        ST_GeogFromText('POLYGON((' || min_lng || ' ' || min_lat || ',' || max_lng || ' ' || min_lat || ',' || max_lng || ' ' || max_lat || ',' || min_lng || ' ' || max_lat || ',' || min_lng || ' ' || min_lat || '))')
    )
    ORDER BY p.name;
END;
$$;

-- ============================================================================
-- PERFORMANCE INDEXES FOR GEOGRAPHIC QUERIES
-- ============================================================================

-- Add spatial index for GPS coordinates if not exists
CREATE INDEX IF NOT EXISTS idx_ports_gps_coordinates 
ON ports USING GIST(gps_coordinates) 
WHERE is_active = true AND gps_coordinates IS NOT NULL;

-- Add composite index for country and coordinates
CREATE INDEX IF NOT EXISTS idx_ports_country_coordinates 
ON ports(country) 
WHERE is_active = true AND gps_coordinates IS NOT NULL;

-- Add index for port type and coordinates
CREATE INDEX IF NOT EXISTS idx_ports_type_coordinates 
ON ports(port_type) 
WHERE is_active = true AND gps_coordinates IS NOT NULL;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION find_ports_within_radius(FLOAT, FLOAT, INTEGER) IS 'Find all active ports within specified radius (km) from center coordinates, sorted by distance';
COMMENT ON FUNCTION calculate_port_distance(UUID, UUID) IS 'Calculate distance in kilometers between two ports using their GPS coordinates';
COMMENT ON FUNCTION get_ports_by_country_with_distance(TEXT, FLOAT, FLOAT) IS 'Get ports by country with optional distance calculation from reference point';
COMMENT ON FUNCTION find_nearest_ports(FLOAT, FLOAT, INTEGER) IS 'Find nearest active ports to given coordinates, limited by count';
COMMENT ON FUNCTION get_ports_in_bounds(FLOAT, FLOAT, FLOAT, FLOAT) IS 'Get all active ports within a geographic bounding box';

-- ============================================================================
-- SAMPLE USAGE EXAMPLES
-- ============================================================================

-- Example 1: Find ports within 100km of Bangkok
-- SELECT * FROM find_ports_within_radius(13.7563, 100.5018, 100);

-- Example 2: Calculate distance between two specific ports
-- SELECT calculate_port_distance('port1-id', 'port2-id');

-- Example 3: Find ports in Thailand sorted by distance from a reference point
-- SELECT * FROM get_ports_by_country_with_distance('Thailand', 13.7563, 100.5018);

-- Example 4: Find 5 nearest ports to a location
-- SELECT * FROM find_nearest_ports(13.7563, 100.5018, 5);

-- Example 5: Get ports in Southeast Asia bounding box
-- SELECT * FROM get_ports_in_bounds(10.0, 95.0, 20.0, 110.0);