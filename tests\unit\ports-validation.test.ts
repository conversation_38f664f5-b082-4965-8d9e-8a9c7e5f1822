import { describe, expect, it } from 'vitest'
import {
  portSchema,
  portFormSchema,
  portTypeSchema,
  coordinatesSchema,
  bilingualTextSchema,
  addressSchema,
  geographicSearchSchema,
  gpsCoordinateInputSchema,
  distanceCalculationSchema,
  validatePortCode,
  validateCoordinates,
  formatPortCode,
} from '@/lib/validations/ports'

describe('Ports Validation', () => {
  describe('Port Schema', () => {
    it('should validate a valid port', () => {
      const validPort = {
        code: 'THBKK',
        name: 'Bangkok Port',
        city: 'Bangkok',
        country: 'Thailand',
        port_type: 'origin' as const,
        gps_coordinates: { lat: 13.7563, lng: 100.5018 },
        timezone: 'Asia/Bangkok',
      }

      const result = portSchema.safeParse(validPort)
      if (!result.success) {
        console.log('Validation errors:', result.error.issues)
      }
      expect(result.success).toBe(true)
    })

    it('should reject port with invalid code format', () => {
      const invalidPort = {
        code: 'T1', // Too short, should be at least 3 characters
        name: 'Test Port',
        city: 'Test City',
        country: 'Test Country',
        port_type: 'origin' as const,
      }

      const result = portSchema.safeParse(invalidPort)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('at least 3 characters')
      }
    })

    it('should reject port with invalid code pattern', () => {
      const invalidPort = {
        code: '123ABC', // Should start with 2 letters
        name: 'Test Port',
        city: 'Test City',
        country: 'Test Country',
        port_type: 'origin' as const,
      }

      const result = portSchema.safeParse(invalidPort)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('2 uppercase letters')
      }
    })

    it('should auto-convert code to uppercase', () => {
      const port = {
        code: 'thbkk',
        name: 'Bangkok Port',
        city: 'Bangkok',
        country: 'Thailand',
        port_type: 'origin' as const,
      }

      const result = portSchema.safeParse(port)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.code).toBe('THBKK')
      }
    })

    it('should reject port with invalid timezone format', () => {
      const invalidPort = {
        code: 'THBKK',
        name: 'Bangkok Port',
        city: 'Bangkok',
        country: 'Thailand',
        port_type: 'origin' as const,
        timezone: 'GMT+7', // Should be Continent/City format
      }

      const result = portSchema.safeParse(invalidPort)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Continent/City')
      }
    })

    it('should validate all port types', () => {
      const portTypes = ['origin', 'destination', 'transit'] as const
      
      portTypes.forEach(portType => {
        const port = {
          code: 'THBKK',
          name: 'Test Port',
          city: 'Test City',
          country: 'Test Country',
          port_type: portType,
        }

        const result = portSchema.safeParse(port)
        expect(result.success).toBe(true)
      })
    })

    it('should allow optional fields to be undefined', () => {
      const minimalPort = {
        code: 'THBKK',
        name: 'Bangkok Port',
        city: 'Bangkok',
        country: 'Thailand',
        port_type: 'origin' as const,
      }

      const result = portSchema.safeParse(minimalPort)
      expect(result.success).toBe(true)
    })
  })

  describe('Coordinates Schema', () => {
    it('should validate valid coordinates', () => {
      const validCoords = { lat: 13.7563, lng: 100.5018 }
      
      const result = coordinatesSchema.safeParse(validCoords)
      expect(result.success).toBe(true)
    })

    it('should reject invalid latitude', () => {
      const invalidCoords = { lat: 91, lng: 100.5018 } // Latitude > 90
      
      const result = coordinatesSchema.safeParse(invalidCoords)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('between -90 and 90')
      }
    })

    it('should reject invalid longitude', () => {
      const invalidCoords = { lat: 13.7563, lng: 181 } // Longitude > 180
      
      const result = coordinatesSchema.safeParse(invalidCoords)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('between -180 and 180')
      }
    })

    it('should validate edge case coordinates', () => {
      const edgeCases = [
        { lat: -90, lng: -180 },
        { lat: 90, lng: 180 },
        { lat: 0, lng: 0 },
      ]
      
      edgeCases.forEach(coords => {
        const result = coordinatesSchema.safeParse(coords)
        expect(result.success).toBe(true)
      })
    })
  })

  describe('Bilingual Text Schema', () => {
    it('should validate text with both languages', () => {
      const text = { th: 'กรุงเทพ', en: 'Bangkok' }
      
      const result = bilingualTextSchema.safeParse(text)
      expect(result.success).toBe(true)
    })

    it('should validate text with only Thai', () => {
      const text = { th: 'กรุงเทพ' }
      
      const result = bilingualTextSchema.safeParse(text)
      expect(result.success).toBe(true)
    })

    it('should validate text with only English', () => {
      const text = { en: 'Bangkok' }
      
      const result = bilingualTextSchema.safeParse(text)
      expect(result.success).toBe(true)
    })

    it('should reject empty text object', () => {
      const text = {}
      
      const result = bilingualTextSchema.safeParse(text)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('At least one language')
      }
    })
  })

  describe('Address Schema', () => {
    it('should validate complete address', () => {
      const address = {
        street: { th: 'ถนนสีลม', en: 'Silom Road' },
        city: { th: 'กรุงเทพ', en: 'Bangkok' },
        province: { th: 'กรุงเทพมหานคร', en: 'Bangkok Metropolitan' },
        postal_code: '10500',
        country: { th: 'ไทย', en: 'Thailand' },
        coordinates: { lat: 13.7563, lng: 100.5018 },
      }
      
      const result = addressSchema.safeParse(address)
      expect(result.success).toBe(true)
    })

    it('should validate minimal address', () => {
      const address = {
        coordinates: { lat: 13.7563, lng: 100.5018 },
      }
      
      const result = addressSchema.safeParse(address)
      expect(result.success).toBe(true)
    })

    it('should validate empty address', () => {
      const address = {}
      
      const result = addressSchema.safeParse(address)
      expect(result.success).toBe(true)
    })
  })

  describe('Geographic Search Schema', () => {
    it('should validate valid geographic search', () => {
      const search = {
        center_lat: 13.7563,
        center_lng: 100.5018,
        radius_km: 100,
      }
      
      const result = geographicSearchSchema.safeParse(search)
      expect(result.success).toBe(true)
    })

    it('should use default radius when not provided', () => {
      const search = {
        center_lat: 13.7563,
        center_lng: 100.5018,
      }
      
      const result = geographicSearchSchema.safeParse(search)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.radius_km).toBe(100)
      }
    })

    it('should reject invalid radius', () => {
      const search = {
        center_lat: 13.7563,
        center_lng: 100.5018,
        radius_km: 0, // Should be at least 1
      }
      
      const result = geographicSearchSchema.safeParse(search)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('at least 1 km')
      }
    })

    it('should reject radius too large', () => {
      const search = {
        center_lat: 13.7563,
        center_lng: 100.5018,
        radius_km: 25000, // Should not exceed 20,000
      }
      
      const result = geographicSearchSchema.safeParse(search)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('cannot exceed 20,000 km')
      }
    })
  })

  describe('GPS Coordinate Input Schema', () => {
    it('should validate and transform string coordinates', () => {
      const input = {
        latitude: '13.7563',
        longitude: '100.5018',
      }
      
      const result = gpsCoordinateInputSchema.safeParse(input)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.latitude).toBe(13.7563)
        expect(result.data.longitude).toBe(100.5018)
      }
    })

    it('should reject invalid string coordinates', () => {
      const input = {
        latitude: 'invalid',
        longitude: '100.5018',
      }
      
      const result = gpsCoordinateInputSchema.safeParse(input)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('valid latitude')
      }
    })

    it('should reject out-of-range coordinates', () => {
      const input = {
        latitude: '95', // > 90
        longitude: '100.5018',
      }
      
      const result = gpsCoordinateInputSchema.safeParse(input)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('between -90 and 90')
      }
    })
  })

  describe('Distance Calculation Schema', () => {
    it('should validate valid port IDs', () => {
      const input = {
        port1_id: '550e8400-e29b-41d4-a716-************',
        port2_id: '550e8400-e29b-41d4-a716-************',
      }
      
      const result = distanceCalculationSchema.safeParse(input)
      expect(result.success).toBe(true)
    })

    it('should reject invalid UUID format', () => {
      const input = {
        port1_id: 'invalid-uuid',
        port2_id: '550e8400-e29b-41d4-a716-************',
      }
      
      const result = distanceCalculationSchema.safeParse(input)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid first port ID')
      }
    })
  })

  describe('Form Schemas', () => {
    it('should validate port form data', () => {
      const formData = {
        code: 'THBKK',
        name: 'Bangkok Port',
        city: 'Bangkok',
        country: 'Thailand',
        port_type: 'origin' as const,
        gps_coordinates: { lat: 13.7563, lng: 100.5018 },
        timezone: 'Asia/Bangkok',
      }

      const result = portFormSchema.safeParse(formData)
      expect(result.success).toBe(true)
    })

    it('should validate minimal port form data', () => {
      const formData = {
        code: 'THBKK',
        name: 'Bangkok Port',
        city: 'Bangkok',
        country: 'Thailand',
        port_type: 'origin' as const,
      }

      const result = portFormSchema.safeParse(formData)
      expect(result.success).toBe(true)
    })
  })

  describe('Port Type Schema', () => {
    it('should validate valid port types', () => {
      const validTypes = ['origin', 'destination', 'transit']
      
      validTypes.forEach(type => {
        const result = portTypeSchema.safeParse(type)
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid port types', () => {
      const invalidTypes = ['invalid', 'source', 'target', 'hub']
      
      invalidTypes.forEach(type => {
        const result = portTypeSchema.safeParse(type)
        expect(result.success).toBe(false)
      })
    })
  })

  describe('Validation Helper Functions', () => {
    describe('validatePortCode', () => {
      it('should validate correct port codes', () => {
        const validCodes = ['THBKK', 'CNSHA', 'USLAX', 'GB123']
        
        validCodes.forEach(code => {
          expect(validatePortCode(code)).toBe(true)
        })
      })

      it('should reject invalid port codes', () => {
        const invalidCodes = ['TH', '123ABC', 'thbkk', 'TH-BKK']
        
        invalidCodes.forEach(code => {
          expect(validatePortCode(code)).toBe(false)
        })
      })
    })

    describe('validateCoordinates', () => {
      it('should validate correct coordinates', () => {
        const validCoords = [
          [0, 0],
          [13.7563, 100.5018],
          [-90, -180],
          [90, 180],
        ]
        
        validCoords.forEach(([lat, lng]) => {
          expect(validateCoordinates(lat, lng)).toBe(true)
        })
      })

      it('should reject invalid coordinates', () => {
        const invalidCoords = [
          [91, 0],
          [-91, 0],
          [0, 181],
          [0, -181],
        ]
        
        invalidCoords.forEach(([lat, lng]) => {
          expect(validateCoordinates(lat, lng)).toBe(false)
        })
      })
    })

    describe('formatPortCode', () => {
      it('should format port codes correctly', () => {
        expect(formatPortCode('thbkk')).toBe('THBKK')
        expect(formatPortCode(' CNSHA ')).toBe('CNSHA')
        expect(formatPortCode('uslax')).toBe('USLAX')
      })
    })
  })
})