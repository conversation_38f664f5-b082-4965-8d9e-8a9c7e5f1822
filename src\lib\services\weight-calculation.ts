import { createClient } from '@/lib/supabase/client'
import type { PackagingType } from '@/lib/validations/shipment'

export interface WeightCalculationInput {
  product_id: string
  customer_id: string
  quantity: number
  packaging_type: PackagingType
  unit_price_cif?: number
  unit_price_fob?: number
}

export interface WeightCalculationResult {
  product_id: string
  quantity: number
  packaging_type: PackagingType
  gross_weight_per_package: number
  net_weight_per_package: number
  total_gross_weight: number
  total_net_weight: number
  unit_price_cif: number
  unit_price_fob: number
  total_value_cif: number
  total_value_fob: number
  quality_grade?: string
  shelf_life_days?: number
}

export interface ContainerWeightSummary {
  container_id: string
  total_gross_weight: number
  total_net_weight: number
  total_value_cif: number
  total_value_fob: number
  weight_utilization: number
  is_overweight: boolean
  max_weight_capacity: number
}

export interface ShipmentWeightSummary {
  shipment_id: string
  total_gross_weight: number
  total_net_weight: number
  total_value_cif: number
  total_value_fob: number
  total_containers: number
  average_weight_per_container: number
  overweight_containers: number
}

export interface WeightCalculationError {
  code: string
  message: string
  details?: any
  timestamp: Date
}

export class WeightCalculationService {
  private supabase = createClient()

  // Container weight limits (in kg)
  private readonly CONTAINER_WEIGHT_LIMITS = {
    '20ft': { dry: 28200, reefer: 27400, open_top: 27600, flat_rack: 30500, tank: 30480 },
    '40ft': { dry: 26700, reefer: 27300, open_top: 26500, flat_rack: 30500, tank: 30480 },
    '40hc': { dry: 26580, reefer: 27300, open_top: 26200, flat_rack: 30500, tank: 30480 },
    '45ft': { dry: 26000, reefer: 26800, open_top: 25800, flat_rack: 30500, tank: 30480 },
  } as const

  // Safety factor for weight calculations
  private readonly WEIGHT_SAFETY_FACTOR = 0.95 // 95% of maximum capacity

  private mapError(error: any): WeightCalculationError {
    const timestamp = new Date()

    if (error.code) {
      switch (error.code) {
        case 'PGRST301':
          return {
            code: 'CUSTOMER_PRODUCT_NOT_FOUND',
            message: 'Customer product configuration not found for weight calculation.',
            timestamp,
          }
        case '23503':
          return {
            code: 'FOREIGN_KEY_VIOLATION',
            message: 'Invalid reference data provided for weight calculation.',
            timestamp,
          }
        default:
          console.error('Weight calculation error:', error)
          return {
            code: 'DATABASE_ERROR',
            message: 'Database operation failed during weight calculation.',
            details: error,
            timestamp,
          }
      }
    }

    return {
      code: 'WEIGHT_CALCULATION_ERROR',
      message: error.message || 'Weight calculation failed.',
      details: error,
      timestamp,
    }
  }

  /**
   * Calculate weights for a single product
   */
  async calculateProductWeights(
    input: WeightCalculationInput
  ): Promise<WeightCalculationResult> {
    try {
      // Get customer product specifications
      const { data, error } = await this.supabase
        .from('customer_products')
        .select(`
          gross_weight_per_package,
          net_weight_per_package,
          unit_price_cif,
          unit_price_fob,
          packaging_type,
          quality_grade,
          shelf_life_days
        `)
        .eq('customer_id', input.customer_id)
        .eq('product_id', input.product_id)
        .eq('is_active', true)
        .single()

      if (error) throw error

      if (!data) {
        throw new Error(
          `No active customer product configuration found for customer ${input.customer_id} and product ${input.product_id}`
        )
      }

      // Validate packaging type consistency
      if (data.packaging_type !== input.packaging_type) {
        console.warn(
          `Packaging type mismatch: input=${input.packaging_type}, customer_product=${data.packaging_type}`
        )
      }

      // Calculate total weights (rounded to 2 decimal places)
      const total_gross_weight = Math.round((data.gross_weight_per_package * input.quantity) * 100) / 100
      const total_net_weight = Math.round((data.net_weight_per_package * input.quantity) * 100) / 100

      // Use provided pricing or fall back to customer product pricing
      const unit_price_cif = input.unit_price_cif ?? data.unit_price_cif
      const unit_price_fob = input.unit_price_fob ?? data.unit_price_fob

      // Calculate total values: quantity × net_weight × unit_price (rounded to 2 decimal places)
      const total_value_cif = Math.round((unit_price_cif * input.quantity * data.net_weight_per_package) * 100) / 100
      const total_value_fob = Math.round((unit_price_fob * input.quantity * data.net_weight_per_package) * 100) / 100

      return {
        product_id: input.product_id,
        quantity: input.quantity,
        packaging_type: data.packaging_type as PackagingType,
        gross_weight_per_package: data.gross_weight_per_package,
        net_weight_per_package: data.net_weight_per_package,
        total_gross_weight,
        total_net_weight,
        unit_price_cif,
        unit_price_fob,
        total_value_cif,
        total_value_fob,
        quality_grade: data.quality_grade,
        shelf_life_days: data.shelf_life_days,
      }
    } catch (error: any) {
      throw this.mapError(error)
    }
  }

  /**
   * Calculate weights for multiple products
   */
  async calculateMultipleProductWeights(
    inputs: WeightCalculationInput[]
  ): Promise<WeightCalculationResult[]> {
    try {
      const results: WeightCalculationResult[] = []

      for (const input of inputs) {
        const result = await this.calculateProductWeights(input)
        results.push(result)
      }

      return results
    } catch (error: any) {
      throw this.mapError(error)
    }
  }

  /**
   * Get container weight capacity
   */
  getContainerWeightCapacity(
    container_size: string,
    container_type: string
  ): number {
    const limits = this.CONTAINER_WEIGHT_LIMITS as any
    return limits[container_size]?.[container_type] || 26000 // Default fallback
  }

  /**
   * Calculate container weight summary
   */
  calculateContainerWeightSummary(
    container_id: string,
    container_size: string,
    container_type: string,
    products: WeightCalculationResult[]
  ): ContainerWeightSummary {
    const total_gross_weight = products.reduce((sum, p) => sum + p.total_gross_weight, 0)
    const total_net_weight = products.reduce((sum, p) => sum + p.total_net_weight, 0)
    const total_value_cif = products.reduce((sum, p) => sum + p.total_value_cif, 0)
    const total_value_fob = products.reduce((sum, p) => sum + p.total_value_fob, 0)

    const max_weight_capacity = this.getContainerWeightCapacity(container_size, container_type)
    const safe_weight_capacity = max_weight_capacity * this.WEIGHT_SAFETY_FACTOR
    const weight_utilization = total_gross_weight / max_weight_capacity
    const is_overweight = total_gross_weight > safe_weight_capacity

    return {
      container_id,
      total_gross_weight,
      total_net_weight,
      total_value_cif,
      total_value_fob,
      weight_utilization,
      is_overweight,
      max_weight_capacity,
    }
  }

  /**
   * Calculate shipment weight summary
   */
  calculateShipmentWeightSummary(
    shipment_id: string,
    container_summaries: ContainerWeightSummary[]
  ): ShipmentWeightSummary {
    const total_gross_weight = container_summaries.reduce(
      (sum, c) => sum + c.total_gross_weight,
      0
    )
    const total_net_weight = container_summaries.reduce(
      (sum, c) => sum + c.total_net_weight,
      0
    )
    const total_value_cif = container_summaries.reduce(
      (sum, c) => sum + c.total_value_cif,
      0
    )
    const total_value_fob = container_summaries.reduce(
      (sum, c) => sum + c.total_value_fob,
      0
    )

    const total_containers = container_summaries.length
    const average_weight_per_container = total_containers > 0 ? total_gross_weight / total_containers : 0
    const overweight_containers = container_summaries.filter(c => c.is_overweight).length

    return {
      shipment_id,
      total_gross_weight,
      total_net_weight,
      total_value_cif,
      total_value_fob,
      total_containers,
      average_weight_per_container,
      overweight_containers,
    }
  }

  /**
   * Validate weight against container capacity
   */
  validateContainerWeight(
    container_size: string,
    container_type: string,
    total_weight: number
  ): {
    is_valid: boolean
    max_capacity: number
    safe_capacity: number
    utilization: number
    exceeds_safe_limit: boolean
    exceeds_max_limit: boolean
  } {
    const max_capacity = this.getContainerWeightCapacity(container_size, container_type)
    const safe_capacity = max_capacity * this.WEIGHT_SAFETY_FACTOR
    const utilization = total_weight / max_capacity

    return {
      is_valid: total_weight <= safe_capacity,
      max_capacity,
      safe_capacity,
      utilization,
      exceeds_safe_limit: total_weight > safe_capacity,
      exceeds_max_limit: total_weight > max_capacity,
    }
  }

  /**
   * Get weight distribution recommendations
   */
  getWeightDistributionRecommendations(
    container_summaries: ContainerWeightSummary[]
  ): {
    balanced: boolean
    max_variance: number
    recommendations: string[]
  } {
    const recommendations: string[] = []

    if (container_summaries.length === 0) {
      return { balanced: true, max_variance: 0, recommendations }
    }

    // Calculate weight variance
    const weights = container_summaries.map(c => c.total_gross_weight)
    const average_weight = weights.reduce((sum, w) => sum + w, 0) / weights.length
    const variances = weights.map(w => Math.abs(w - average_weight) / average_weight)
    const max_variance = Math.max(...variances)

    const balanced = max_variance <= 0.2 // Consider balanced if variance <= 20%

    if (!balanced) {
      recommendations.push('Consider rebalancing products between containers for better weight distribution')
    }

    // Check for overweight containers
    const overweight = container_summaries.filter(c => c.is_overweight)
    if (overweight.length > 0) {
      recommendations.push(
        `${overweight.length} container(s) exceed safe weight limits. Consider redistributing cargo.`
      )
    }

    // Check for underutilized containers
    const underutilized = container_summaries.filter(c => c.weight_utilization < 0.6)
    if (underutilized.length > 0 && container_summaries.length > 1) {
      recommendations.push(
        `${underutilized.length} container(s) are underutilized. Consider consolidating cargo.`
      )
    }

    return {
      balanced,
      max_variance,
      recommendations,
    }
  }

  /**
   * Calculate packaging density metrics
   */
  calculatePackagingDensity(
    packaging_type: PackagingType,
    gross_weight_per_package: number,
    net_weight_per_package: number
  ): {
    packaging_weight: number
    packaging_weight_ratio: number
    density_category: 'light' | 'medium' | 'heavy'
  } {
    const packaging_weight = gross_weight_per_package - net_weight_per_package
    const packaging_weight_ratio = packaging_weight / gross_weight_per_package

    let density_category: 'light' | 'medium' | 'heavy' = 'medium'

    // Categorize based on packaging weight ratio
    if (packaging_weight_ratio < 0.1) {
      density_category = 'heavy' // Less than 10% packaging
    } else if (packaging_weight_ratio > 0.3) {
      density_category = 'light' // More than 30% packaging
    }

    return {
      packaging_weight,
      packaging_weight_ratio,
      density_category,
    }
  }

  /**
   * Update shipment totals based on calculated weights
   */
  async updateShipmentTotals(
    shipment_id: string,
    weight_summary: ShipmentWeightSummary
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('shipments')
        .update({
          total_weight: weight_summary.total_gross_weight,
          total_value_cif: weight_summary.total_value_cif,
          total_value_fob: weight_summary.total_value_fob,
          updated_at: new Date().toISOString(),
        })
        .eq('id', shipment_id)

      if (error) throw error
    } catch (error: any) {
      throw this.mapError(error)
    }
  }

  /**
   * Comprehensive weight calculation for shipment with containers
   */
  async calculateShipmentWeights(
    shipment_id: string,
    customer_id: string,
    containers: Array<{
      id: string
      container_size: string
      container_type: string
    }>,
    product_allocations: Array<{
      container_id: string
      product_id: string
      quantity: number
      packaging_type: PackagingType
      unit_price_cif?: number
      unit_price_fob?: number
    }>
  ): Promise<{
    product_weights: WeightCalculationResult[]
    container_summaries: ContainerWeightSummary[]
    shipment_summary: ShipmentWeightSummary
    validation_results: Array<{
      container_id: string
      is_valid: boolean
      recommendations: string[]
    }>
  }> {
    try {
      // Calculate individual product weights
      const weight_inputs: WeightCalculationInput[] = product_allocations.map(allocation => ({
        product_id: allocation.product_id,
        customer_id,
        quantity: allocation.quantity,
        packaging_type: allocation.packaging_type,
        unit_price_cif: allocation.unit_price_cif,
        unit_price_fob: allocation.unit_price_fob,
      }))

      const product_weights = await this.calculateMultipleProductWeights(weight_inputs)

      // Group products by container and calculate container summaries
      const container_summaries: ContainerWeightSummary[] = []
      const validation_results: Array<{ container_id: string; is_valid: boolean; recommendations: string[] }> = []

      for (const container of containers) {
        const container_products = product_weights.filter(pw => {
          const allocation = product_allocations.find(
            pa => pa.product_id === pw.product_id && pa.container_id === container.id
          )
          return allocation !== undefined
        })

        const summary = this.calculateContainerWeightSummary(
          container.id,
          container.container_size,
          container.container_type,
          container_products
        )

        container_summaries.push(summary)

        // Validate container weight
        const validation = this.validateContainerWeight(
          container.container_size,
          container.container_type,
          summary.total_gross_weight
        )

        const recommendations: string[] = []
        if (validation.exceeds_max_limit) {
          recommendations.push('Container exceeds maximum weight limit - immediate action required')
        } else if (validation.exceeds_safe_limit) {
          recommendations.push('Container exceeds safe weight limit - consider redistributing cargo')
        }

        validation_results.push({
          container_id: container.id,
          is_valid: validation.is_valid,
          recommendations,
        })
      }

      // Calculate shipment summary
      const shipment_summary = this.calculateShipmentWeightSummary(shipment_id, container_summaries)

      // Update shipment totals in database
      await this.updateShipmentTotals(shipment_id, shipment_summary)

      return {
        product_weights,
        container_summaries,
        shipment_summary,
        validation_results,
      }
    } catch (error: any) {
      throw this.mapError(error)
    }
  }
}

// Export singleton instance
export const weightCalculationService = new WeightCalculationService()