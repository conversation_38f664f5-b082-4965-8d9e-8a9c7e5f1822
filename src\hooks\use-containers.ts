'use client'

import { useCallback, useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { containerGenerationService } from '@/lib/services/container-generation'
import { productAllocationService } from '@/lib/services/product-allocation'
import { weightCalculationService } from '@/lib/services/weight-calculation'
import type {
  ContainerCreation,
  ContainerUpdate,
  ShipmentProductValidation,
} from '@/lib/validations/container-product'
import type {
  ContainerType,
  ContainerSize,
  ContainerStatus,
  PackagingType,
} from '@/lib/validations/shipment'

// Database types
export interface Container {
  id: string
  shipment_id: string
  container_number: string | null
  container_type: ContainerType | null
  container_size: ContainerSize | null
  seal_number: string | null
  tare_weight: number | null
  gross_weight: number | null
  volume: number | null
  temperature: string | null
  vent: string | null
  status: ContainerStatus
  created_at: string
  updated_at?: string
  // Confirmation fields
  container_number_confirmed?: boolean
  container_number_confirmed_by?: string | null
  container_number_confirmed_at?: string | null
  seal_number_confirmed?: boolean
  seal_number_confirmed_by?: string | null
  seal_number_confirmed_at?: string | null
}

export interface ShipmentProduct {
  id: string
  shipment_id: string
  container_id: string | null
  product_id: string
  product_description: string | null
  quantity: number
  unit_of_measure_id: string
  unit_price_cif: number
  unit_price_fob: number
  total_value_cif: number
  total_value_fob: number
  gross_weight: number
  net_weight: number
  shipping_mark: string | null
  mfg_date: string | null
  expire_date: string | null
  lot_number: string | null
  packaging_type: PackagingType
  quality_grade: string | null
  created_at: string
}

export interface ContainerWithProducts extends Container {
  products: ShipmentProduct[]
  total_products: number
  total_weight: number
  total_volume: number
  weight_utilization: number
  volume_utilization: number
}

export interface ContainerGenerationResult {
  containers: Container[]
  products: ShipmentProduct[]
  weight_summary: {
    total_gross_weight: number
    total_net_weight: number
    total_value_cif: number
    total_value_fob: number
    container_count: number
    overweight_containers: number
  }
  validation_warnings: string[]
  validation_errors: string[]
}

// Hook for container data management
export function useContainersData(shipment_id?: string) {
  const [containers, setContainers] = useState<ContainerWithProducts[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  // Fetch containers with products for a shipment
  const fetchContainers = useCallback(async () => {
    if (!shipment_id) return

    setLoading(true)
    setError(null)

    try {
      // Fetch containers
      const { data: containersData, error: containersError } = await supabase
        .from('containers')
        .select('*')
        .eq('shipment_id', shipment_id)
        .order('created_at', { ascending: true })

      if (containersError) throw containersError

      // Fetch products for these containers
      const { data: productsData, error: productsError } = await supabase
        .from('shipment_products')
        .select(`
          *,
          products (
            id,
            name,
            description
          ),
          units_of_measure (
            id,
            name,
            symbol
          )
        `)
        .eq('shipment_id', shipment_id)
        .order('created_at', { ascending: true })

      if (productsError) throw productsError

      // Combine containers with their products
      const containersWithProducts: ContainerWithProducts[] = (containersData || []).map(container => {
        const containerProducts = (productsData || []).filter(
          product => product.container_id === container.id
        )

        const total_weight = containerProducts.reduce((sum, p) => sum + p.gross_weight, 0)
        const total_volume = containerProducts.reduce((sum, p) => {
          // Estimate volume based on packaging if not available
          const estimatedVolume = estimateProductVolume(p.packaging_type, p.quantity)
          return sum + estimatedVolume
        }, 0)

        // Calculate utilizations
        const max_weight = getContainerWeightCapacity(container.container_size, container.container_type)
        const max_volume = container.volume || getContainerVolumeCapacity(container.container_size, container.container_type)

        const weight_utilization = max_weight > 0 ? total_weight / max_weight : 0
        const volume_utilization = max_volume > 0 ? total_volume / max_volume : 0

        return {
          ...container,
          products: containerProducts,
          total_products: containerProducts.length,
          total_weight,
          total_volume,
          weight_utilization,
          volume_utilization,
        }
      })

      setContainers(containersWithProducts)
    } catch (error: any) {
      setError(error.message)
      console.error('Error fetching containers:', error)
    } finally {
      setLoading(false)
    }
  }, [supabase, shipment_id])

  // Helper function to estimate product volume
  const estimateProductVolume = (packaging_type: PackagingType, quantity: number): number => {
    const VOLUME_ESTIMATES = {
      Bag: 0.05, // 50 liters per bag
      'Plastic Basket': 0.08, // 80 liters per basket
      Carton: 0.06, // 60 liters per carton
    }
    return VOLUME_ESTIMATES[packaging_type] * quantity
  }

  // Helper function to get container weight capacity
  const getContainerWeightCapacity = (size: ContainerSize | null, type: ContainerType | null): number => {
    if (!size || !type) return 26000
    const CAPACITY = {
      '20ft': { dry: 28200, reefer: 27400, open_top: 27600, flat_rack: 30500, tank: 30480 },
      '40ft': { dry: 26700, reefer: 27300, open_top: 26500, flat_rack: 30500, tank: 30480 },
      '40hc': { dry: 26580, reefer: 27300, open_top: 26200, flat_rack: 30500, tank: 30480 },
      '45ft': { dry: 26000, reefer: 26800, open_top: 25800, flat_rack: 30500, tank: 30480 },
    } as any
    return CAPACITY[size]?.[type] || 26000
  }

  // Helper function to get container volume capacity
  const getContainerVolumeCapacity = (size: ContainerSize | null, type: ContainerType | null): number => {
    if (!size || !type) return 67.7
    const CAPACITY = {
      '20ft': { dry: 33.2, reefer: 28.3, open_top: 32.4, flat_rack: 0, tank: 26.0 },
      '40ft': { dry: 67.7, reefer: 59.3, open_top: 65.9, flat_rack: 0, tank: 50.0 },
      '40hc': { dry: 76.4, reefer: 67.3, open_top: 74.3, flat_rack: 0, tank: 58.0 },
      '45ft': { dry: 86.0, reefer: 76.0, open_top: 84.0, flat_rack: 0, tank: 65.0 },
    } as any
    return CAPACITY[size]?.[type] || 67.7
  }

  // Subscribe to real-time updates
  useEffect(() => {
    if (!shipment_id) return

    const containersChannel = supabase
      .channel('containers_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'containers', filter: `shipment_id=eq.${shipment_id}` },
        () => {
          fetchContainers()
        }
      )
      .subscribe()

    const productsChannel = supabase
      .channel('shipment_products_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'shipment_products', filter: `shipment_id=eq.${shipment_id}` },
        () => {
          fetchContainers()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(containersChannel)
      supabase.removeChannel(productsChannel)
    }
  }, [supabase, shipment_id, fetchContainers])

  // Load initial data
  useEffect(() => {
    fetchContainers()
  }, [fetchContainers])

  return {
    containers,
    loading,
    error,
    refetch: fetchContainers,
  }
}

// Hook for container CRUD operations
export function useContainerCRUD() {
  const supabase = createClient()
  const [creating, setCreating] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [deleting, setDeleting] = useState(false)

  // Create a new container
  const createContainer = useCallback(
    async (containerData: ContainerCreation): Promise<Container> => {
      setCreating(true)

      try {
        const { data, error } = await supabase
          .from('containers')
          .insert(containerData)
          .select()
          .single()

        if (error) throw error

        return data
      } catch (error: any) {
        console.error('Error creating container:', error)
        throw error
      } finally {
        setCreating(false)
      }
    },
    [supabase]
  )

  // Update container
  const updateContainer = useCallback(
    async (id: string, updates: Partial<ContainerUpdate>): Promise<Container> => {
      setUpdating(true)

      try {
        const { data, error } = await supabase
          .from('containers')
          .update(updates)
          .eq('id', id)
          .select()
          .single()

        if (error) throw error

        return data
      } catch (error: any) {
        console.error('Error updating container:', error)
        throw error
      } finally {
        setUpdating(false)
      }
    },
    [supabase]
  )

  // Delete container
  const deleteContainer = useCallback(
    async (id: string): Promise<void> => {
      setDeleting(true)

      try {
        const { error } = await supabase.from('containers').delete().eq('id', id)

        if (error) throw error
      } catch (error: any) {
        console.error('Error deleting container:', error)
        throw error
      } finally {
        setDeleting(false)
      }
    },
    [supabase]
  )

  return {
    createContainer,
    updateContainer,
    deleteContainer,
    creating,
    updating,
    deleting,
    isLoading: creating || updating || deleting,
  }
}

// Hook for automatic container generation
export function useContainerGeneration() {
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generate containers automatically
  const generateContainers = useCallback(
    async (
      shipment_id: string,
      customer_id: string,
      products: Array<{ product_id: string; quantity: number }>
    ): Promise<ContainerGenerationResult> => {
      setGenerating(true)
      setError(null)

      try {
        // Generate containers using the service
        const containers = await containerGenerationService.generateContainersForShipment(
          shipment_id,
          customer_id,
          products
        )

        // Get customer product specifications for product allocation
        const product_ids = products.map(p => p.product_id)
        const product_specs = await containerGenerationService.getCustomerProductSpecs(
          customer_id,
          product_ids
        )

        // Merge quantities with specifications
        const product_allocations = product_specs.map(spec => {
          const product = products.find(p => p.product_id === spec.product_id)
          return {
            ...spec,
            quantity: product?.quantity || 0,
          }
        })

        // Insert containers into database
        const supabase = createClient()
        const { data: createdContainers, error: containerError } = await supabase
          .from('containers')
          .insert(
            containers.map(container => ({
              shipment_id,
              container_number: container.container_number,
              container_type: container.container_type,
              container_size: container.container_size,
              volume: container.volume,
              status: container.status,
            }))
          )
          .select()

        if (containerError) throw containerError

        // Initialize containers with capacity tracking
        const containersWithCapacity = productAllocationService.initializeContainerCapacity(
          containers.map((container, index) => ({
            ...container,
            id: createdContainers[index].id,
          })),
          shipment_id
        )

        // Allocate products to containers
        const allocations = await productAllocationService.allocateProductsToContainers(
          containersWithCapacity,
          product_allocations,
          customer_id,
          shipment_id
        )

        // Insert product allocations
        const { data: createdProducts, error: productsError } = await supabase
          .from('shipment_products')
          .insert(
            allocations.map(allocation => ({
              shipment_id,
              container_id: allocation.container_id,
              product_id: allocation.product_id,
              quantity: allocation.quantity,
              unit_of_measure_id: product_specs.find(p => p.product_id === allocation.product_id)?.unit_of_measure_id || '',
              unit_price_cif: allocation.unit_price_cif,
              unit_price_fob: allocation.unit_price_fob,
              total_value_cif: allocation.total_value_cif,
              total_value_fob: allocation.total_value_fob,
              gross_weight: allocation.gross_weight,
              net_weight: allocation.net_weight,
              packaging_type: allocation.packaging_type,
              shipping_mark: allocation.shipping_mark,
              mfg_date: allocation.mfg_date,
              expire_date: allocation.expire_date,
              lot_number: allocation.lot_number,
              quality_grade: allocation.quality_grade,
            }))
          )
          .select()

        if (productsError) throw productsError

        // Calculate weight summary
        const weight_summary = {
          total_gross_weight: allocations.reduce((sum, a) => sum + a.gross_weight, 0),
          total_net_weight: allocations.reduce((sum, a) => sum + (a.gross_weight * 0.9), 0), // Estimate
          total_value_cif: allocations.reduce((sum, a) => sum + a.total_value_cif, 0),
          total_value_fob: allocations.reduce((sum, a) => sum + a.total_value_fob, 0),
          container_count: createdContainers.length,
          overweight_containers: 0, // Calculate based on validation
        }

        return {
          containers: createdContainers,
          products: createdProducts,
          weight_summary,
          validation_warnings: [],
          validation_errors: [],
        }
      } catch (error: any) {
        setError(error.message)
        console.error('Error generating containers:', error)
        throw error
      } finally {
        setGenerating(false)
      }
    },
    []
  )

  return {
    generateContainers,
    generating,
    error,
  }
}

// Hook for container management operations
export function useContainerManagement(shipment_id?: string) {
  const data = useContainersData(shipment_id)
  const crud = useContainerCRUD()
  const generation = useContainerGeneration()

  const calculateShipmentTotals = useCallback(() => {
    if (data.containers.length === 0) {
      return {
        total_containers: 0,
        total_weight: 0,
        total_volume: 0,
        total_value_cif: 0,
        total_value_fob: 0,
        average_utilization: 0,
        overweight_containers: 0,
      }
    }

    const total_weight = data.containers.reduce((sum, c) => sum + c.total_weight, 0)
    const total_volume = data.containers.reduce((sum, c) => sum + c.total_volume, 0)
    
    // Calculate total values from products
    let total_value_cif = 0
    let total_value_fob = 0
    
    data.containers.forEach(container => {
      container.products.forEach(product => {
        total_value_cif += product.total_value_cif
        total_value_fob += product.total_value_fob
      })
    })

    const average_utilization = data.containers.reduce(
      (sum, c) => sum + Math.max(c.weight_utilization, c.volume_utilization),
      0
    ) / data.containers.length

    const overweight_containers = data.containers.filter(c => c.weight_utilization > 0.95).length

    return {
      total_containers: data.containers.length,
      total_weight,
      total_volume,
      total_value_cif,
      total_value_fob,
      average_utilization,
      overweight_containers,
    }
  }, [data.containers])

  return {
    ...data,
    ...crud,
    ...generation,
    calculateShipmentTotals,
  }
}