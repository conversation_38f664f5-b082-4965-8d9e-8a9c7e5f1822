'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  FileText, 
  Download, 
  CheckCircle, 
  Printer, 
  Mail,
  Loader2,
  AlertTriangle
} from 'lucide-react'

interface DocumentType {
  id: string
  name: string
  description: string
  category: 'shipping' | 'certificates' | 'financial' | 'internal'
  required: boolean
  available: boolean
  icon: React.ReactNode
}

interface DocumentGenerationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  shipmentId: string
  shipmentNumber: string
  isGenerating?: boolean
  onGenerate: (selectedDocuments: string[]) => Promise<void>
}

const DOCUMENT_TYPES: DocumentType[] = [
  // Shipping Documents
  {
    id: 'bill_of_lading',
    name: 'Bill of Lading',
    description: 'Official document acknowledging receipt of cargo for shipment',
    category: 'shipping',
    required: true,
    available: true,
    icon: <FileText className="h-4 w-4" />
  },
  {
    id: 'commercial_invoice',
    name: 'Commercial Invoice',
    description: 'Detailed invoice with product descriptions and values',
    category: 'shipping',
    required: true,
    available: true,
    icon: <FileText className="h-4 w-4" />
  },
  {
    id: 'packing_list',
    name: 'Packing List',
    description: 'Detailed list of all packaged goods and container contents',
    category: 'shipping',
    required: true,
    available: true,
    icon: <FileText className="h-4 w-4" />
  },
  {
    id: 'certificate_origin',
    name: 'Certificate of Origin',
    description: 'Document certifying the country where goods were manufactured',
    category: 'certificates',
    required: false,
    available: true,
    icon: <CheckCircle className="h-4 w-4" />
  },
  // Financial Documents
  {
    id: 'proforma_invoice',
    name: 'Proforma Invoice',
    description: 'Preliminary bill of sale with estimated costs',
    category: 'financial',
    required: false,
    available: true,
    icon: <FileText className="h-4 w-4" />
  },
  {
    id: 'insurance_certificate',
    name: 'Insurance Certificate',
    description: 'Proof of cargo insurance coverage',
    category: 'financial',
    required: false,
    available: false,
    icon: <CheckCircle className="h-4 w-4" />
  },
  // Internal Documents
  {
    id: 'delivery_order',
    name: 'Delivery Order',
    description: 'Internal order for cargo release and delivery',
    category: 'internal',
    required: false,
    available: true,
    icon: <FileText className="h-4 w-4" />
  },
  {
    id: 'cargo_manifest',
    name: 'Cargo Manifest',
    description: 'Complete list of cargo in the shipment',
    category: 'internal',
    required: false,
    available: true,
    icon: <FileText className="h-4 w-4" />
  }
]

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'shipping':
      return 'border-blue-500 text-blue-300 bg-blue-500/10'
    case 'certificates':
      return 'border-green-500 text-green-300 bg-green-500/10'
    case 'financial':
      return 'border-orange-500 text-orange-300 bg-orange-500/10'
    case 'internal':
      return 'border-purple-500 text-purple-300 bg-purple-500/10'
    default:
      return 'border-slate-500 text-slate-300 bg-slate-500/10'
  }
}

export function DocumentGenerationModal({
  open,
  onOpenChange,
  shipmentId,
  shipmentNumber,
  isGenerating = false,
  onGenerate
}: DocumentGenerationModalProps) {
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>(() => 
    DOCUMENT_TYPES.filter(doc => doc.required && doc.available).map(doc => doc.id)
  )

  const handleDocumentToggle = (documentId: string, checked: boolean) => {
    setSelectedDocuments(prev => 
      checked 
        ? [...prev, documentId]
        : prev.filter(id => id !== documentId)
    )
  }

  const handleSelectAll = () => {
    const availableDocs = DOCUMENT_TYPES.filter(doc => doc.available).map(doc => doc.id)
    setSelectedDocuments(availableDocs)
  }

  const handleSelectRequired = () => {
    const requiredDocs = DOCUMENT_TYPES.filter(doc => doc.required && doc.available).map(doc => doc.id)
    setSelectedDocuments(requiredDocs)
  }

  const handleClearAll = () => {
    setSelectedDocuments([])
  }

  const handleGenerate = async () => {
    if (selectedDocuments.length === 0) return
    await onGenerate(selectedDocuments)
  }

  const groupedDocuments = DOCUMENT_TYPES.reduce((acc, doc) => {
    if (!acc[doc.category]) {
      acc[doc.category] = []
    }
    acc[doc.category].push(doc)
    return acc
  }, {} as Record<string, DocumentType[]>)

  const categoryNames = {
    shipping: 'Shipping Documents',
    certificates: 'Certificates & Compliance',
    financial: 'Financial Documents',
    internal: 'Internal Documents'
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] bg-slate-800 border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Generate Documents - {shipmentNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectRequired}
              className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20"
            >
              <CheckCircle className="h-3 w-3 mr-2" />
              Required Only
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              className="border-green-500 bg-green-600/10 text-green-100 hover:bg-green-600/20"
            >
              Select All Available
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearAll}
              className="border-red-500 bg-red-600/10 text-red-100 hover:bg-red-600/20"
            >
              Clear Selection
            </Button>
          </div>

          {/* Document Categories */}
          <div className="space-y-4 max-h-[400px] overflow-y-auto">
            {Object.entries(groupedDocuments).map(([category, documents]) => (
              <Card key={category} className="bg-slate-900/50 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-white font-medium">
                      {categoryNames[category as keyof typeof categoryNames]}
                    </h4>
                    <Badge variant="outline" className={getCategoryColor(category)}>
                      {documents.filter(doc => doc.available).length} available
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    {documents.map((doc) => (
                      <div
                        key={doc.id}
                        className={`flex items-start gap-3 p-3 rounded-lg border ${
                          doc.available 
                            ? 'border-slate-600 bg-slate-800/30' 
                            : 'border-slate-700 bg-slate-800/10 opacity-50'
                        }`}
                      >
                        <div className="flex items-center gap-2 mt-1">
                          <Checkbox
                            checked={selectedDocuments.includes(doc.id)}
                            disabled={!doc.available || isGenerating}
                            onCheckedChange={(checked) => 
                              handleDocumentToggle(doc.id, checked as boolean)
                            }
                            className="border-slate-500"
                          />
                          <div className="text-slate-400">
                            {doc.icon}
                          </div>
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className={`font-medium ${
                              doc.available ? 'text-white' : 'text-slate-500'
                            }`}>
                              {doc.name}
                            </span>
                            {doc.required && (
                              <Badge variant="outline" className="border-red-500 text-red-300 bg-red-500/10 text-xs">
                                Required
                              </Badge>
                            )}
                            {!doc.available && (
                              <Badge variant="outline" className="border-gray-500 text-gray-400 bg-gray-500/10 text-xs">
                                <AlertTriangle className="h-2 w-2 mr-1" />
                                Unavailable
                              </Badge>
                            )}
                          </div>
                          <p className={`text-sm ${
                            doc.available ? 'text-slate-400' : 'text-slate-500'
                          }`}>
                            {doc.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Selection Summary */}
          {selectedDocuments.length > 0 && (
            <div className="p-3 bg-blue-900/20 rounded-lg border border-blue-500/20">
              <div className="flex items-center gap-2 text-blue-300 text-sm">
                <CheckCircle className="h-4 w-4" />
                <span>
                  {selectedDocuments.length} document(s) selected for generation
                </span>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isGenerating}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            Cancel
          </Button>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleGenerate}
              disabled={selectedDocuments.length === 0 || isGenerating}
              className="border-green-500 bg-green-600/10 text-green-100 hover:bg-green-600/20"
            >
              <Download className="h-4 w-4 mr-2" />
              Generate & Download
            </Button>
            <Button
              onClick={handleGenerate}
              disabled={selectedDocuments.length === 0 || isGenerating}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Documents
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}