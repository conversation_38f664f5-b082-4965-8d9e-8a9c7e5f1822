-- Core Business Tables
-- Story 1.2: Database Schema & RLS Foundation
-- This migration creates core business tables for shipments, containers, and transportation

-- ============================================================================
-- SHIPMENTS TABLE
-- ============================================================================

-- Core business entity with all stakeholder references
CREATE TABLE shipments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shipment_number TEXT UNIQUE NOT NULL,
    invoice_number TEXT,
    
    -- Stakeholder references
    customer_id UUID REFERENCES companies(id),
    shipper_id UUID REFERENCES companies(id),
    consignee_id UUID REFERENCES companies(id),
    notify_party_id UUID REFERENCES companies(id),
    factory_id UUID REFERENCES companies(id),
    forwarder_agent_id UUID REFERENCES companies(id),
    
    -- Port and vessel information
    origin_port_id UUID REFERENCES ports(id),
    destination_port_id UUID REFERENCES ports(id),
    liner TEXT,
    vessel_name TEXT,
    voyage_number TEXT,
    booking_number TEXT,
    
    -- Dates and times
    etd_date TIMESTAMPTZ,
    eta_date TIMESTAMPTZ,
    closing_time TIMESTAMPTZ,
    cy_date TIMESTAMPTZ,
    
    -- Product Info
    number_of_pallet INTEGER,
    pallet_description TEXT,
    ephyto_refno TEXT,
    currency_code currency_enum DEFAULT 'USD',
    total_weight DECIMAL,
    total_volume DECIMAL,

    -- Status and metadata
    status shipment_status_enum DEFAULT 'booking_confirmed',
    transportation_mode transport_mode_enum DEFAULT 'sea',
    notes TEXT,
    metadata JSONB,
    
    -- Audit fields
    created_by UUID REFERENCES profiles(user_id),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Constraints
    CONSTRAINT valid_date_sequence CHECK (
        (etd_date IS NULL OR eta_date IS NULL OR eta_date >= etd_date)
    )
);

-- ============================================================================
-- CONTAINERS TABLE
-- ============================================================================

-- Container tracking for shipments
CREATE TABLE containers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shipment_id UUID REFERENCES shipments(id) ON DELETE CASCADE,
    container_number TEXT,
    container_type container_type_enum,
    container_size container_size_enum,
    seal_number TEXT,
    tare_weight DECIMAL,
    gross_weight DECIMAL,
    volume DECIMAL,
    temperature TEXT,
    vent TEXT,
    status container_status_enum DEFAULT 'empty',
    created_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- SHIPMENT PRODUCTS TABLE
-- ============================================================================

-- Product line items for shipments
CREATE TABLE shipment_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shipment_id UUID REFERENCES shipments(id) ON DELETE CASCADE,
    container_id UUID REFERENCES containers(id),
    product_id UUID REFERENCES products(id),
    product_description TEXT,
    quantity DECIMAL NOT NULL, -- Number of packages of the specified packaging_type
    unit_of_measure_id UUID REFERENCES units_of_measure(id), -- Base UOM is KG
    unit_price_cif DECIMAL NOT NULL, -- Cost, Insurance, and Freight price per KG
    unit_price_fob DECIMAL NOT NULL, -- Free on Board price per KG
    total_value_cif DECIMAL NOT NULL,
    total_value_fob DECIMAL NOT NULL,
    gross_weight NUMERIC(18, 4) NOT NULL DEFAULT 0, -- Gross weight per 1 package in KG
    net_weight NUMERIC(18, 4) NOT NULL DEFAULT 0, -- Net weight per 1 package in KG
    shipping_mark TEXT NULL,
    mfg_date DATE NULL,
    expire_date DATE NULL,
    lot_number TEXT NULL,
    packaging_type packaging_type_enum NOT NULL, -- Type of packaging (must match customer_products definition)
    quality_grade TEXT,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- TRANSPORTATION TABLE
-- ============================================================================

-- Transportation management for logistics
CREATE TABLE transportation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shipment_id UUID REFERENCES shipments(id) ON DELETE CASCADE,
    carrier_id UUID REFERENCES companies(id),
    driver_id UUID REFERENCES profiles(user_id),
    vehicle_head_number TEXT,
    vehicle_tail_number TEXT,
    driver_phone TEXT,
    assignment_date TIMESTAMPTZ,
    pickup_container_location TEXT,
    pickup_container_gps_coordinates POINT,
    pickup_product_location TEXT,
    pickup_product_gps_coordinates POINT,
    delivery_location TEXT,
    delivery_gps_coordinates POINT,
    notes TEXT,
    estimated_distance DECIMAL,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- STATUS HISTORY TABLE
-- ============================================================================

-- Audit trail for shipment status changes with GPS coordinates
CREATE TABLE status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shipment_id UUID REFERENCES shipments(id) ON DELETE CASCADE,
    status_from shipment_status_enum,
    status_to shipment_status_enum NOT NULL,
    notes TEXT,
    location TEXT,
    latitude DECIMAL,
    longitude DECIMAL,
    updated_by UUID REFERENCES profiles(user_id),
    created_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- STATUS IMAGES TABLE
-- ============================================================================

-- Photo documentation support for status updates
CREATE TABLE status_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shipment_id UUID REFERENCES shipments(id) ON DELETE CASCADE,
    status_history_id UUID REFERENCES status_history(id),
    image_url TEXT NOT NULL,
    image_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    metadata JSONB,
    uploaded_by UUID REFERENCES profiles(user_id),
    created_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_shipments_updated_at 
    BEFORE UPDATE ON shipments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_containers_updated_at 
    BEFORE UPDATE ON containers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shipment_products_updated_at 
    BEFORE UPDATE ON shipment_products 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transportation_updated_at 
    BEFORE UPDATE ON transportation 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_status_images_updated_at 
    BEFORE UPDATE ON status_images 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- GPS coordinate sync triggers
CREATE TRIGGER sync_containers_gps_coordinates 
    BEFORE INSERT OR UPDATE ON containers 
    FOR EACH ROW EXECUTE FUNCTION sync_gps_coordinates();

CREATE TRIGGER sync_transportation_pickup_coordinates 
    BEFORE INSERT OR UPDATE ON transportation 
    FOR EACH ROW 
    EXECUTE FUNCTION sync_gps_coordinates();

CREATE TRIGGER sync_status_history_coordinates 
    BEFORE INSERT OR UPDATE ON status_history 
    FOR EACH ROW 
    EXECUTE FUNCTION sync_gps_coordinates();

CREATE TRIGGER sync_status_images_coordinates 
    BEFORE INSERT OR UPDATE ON status_images 
    FOR EACH ROW 
    EXECUTE FUNCTION sync_gps_coordinates();

-- ============================================================================
-- BUSINESS LOGIC TRIGGERS
-- ============================================================================

-- Function to auto-generate shipment numbers
CREATE OR REPLACE FUNCTION auto_generate_shipment_number()
RETURNS TRIGGER AS $$
BEGIN
    -- Only generate if shipment_number is not provided
    IF NEW.shipment_number IS NULL OR NEW.shipment_number = '' THEN
        -- Get port code from origin port
        DECLARE
            port_code TEXT;
        BEGIN
            SELECT ports.code INTO port_code 
            FROM ports 
            WHERE ports.id = NEW.origin_port_id;
            
            -- Use default if no port found
            IF port_code IS NULL THEN
                port_code := 'XXX';
            END IF;
            
            NEW.shipment_number := generate_shipment_number(NEW.transport_mode, port_code);
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply auto-generation trigger
CREATE TRIGGER auto_generate_shipment_number_trigger 
    BEFORE INSERT ON shipments 
    FOR EACH ROW EXECUTE FUNCTION auto_generate_shipment_number();

-- Function to automatically create status history on shipment status changes
CREATE OR REPLACE FUNCTION track_shipment_status_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create history record if status actually changed
    IF (TG_OP = 'INSERT') OR (OLD.status != NEW.status) THEN
        INSERT INTO status_history (
            shipment_id,
            status,
            previous_status,
            status_message,
            updated_by,
            updated_by_role,
            system_generated
        ) VALUES (
            NEW.id,
            NEW.status,
            CASE WHEN TG_OP = 'UPDATE' THEN OLD.status ELSE NULL END,
            'Status updated to ' || NEW.status::TEXT,
            NEW.status_updated_by,
            CASE 
                WHEN NEW.status_updated_by IS NOT NULL THEN 
                    (SELECT role FROM profiles WHERE id = NEW.status_updated_by)
                ELSE NULL 
            END,
            true -- System generated
        );
        
        -- Update the status timestamp
        NEW.status_updated_at := CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply status tracking trigger
CREATE TRIGGER track_shipment_status_changes_trigger 
    AFTER INSERT OR UPDATE ON shipments 
    FOR EACH ROW EXECUTE FUNCTION track_shipment_status_changes();

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE shipments IS 'Core business entity containing all stakeholder references and shipment details';
COMMENT ON TABLE containers IS 'Container tracking with weight measurements and location data';
COMMENT ON TABLE shipment_products IS 'Product line items for shipments with quantity and packaging specifications';
COMMENT ON TABLE transportation IS 'Transportation management for logistics coordination';
COMMENT ON TABLE status_history IS 'Audit trail for shipment status changes with GPS coordinates and photo support';
COMMENT ON TABLE status_images IS 'Photo documentation for status updates with GPS metadata';

COMMENT ON COLUMN shipments.shipment_number IS 'Auto-generated unique identifier: EX[Mode]-[Port]-YYMMDD-[Running]';
COMMENT ON COLUMN shipments.incoterms IS 'International commercial terms governing the shipment';
COMMENT ON COLUMN containers.gps_coordinates IS 'PostGIS point for efficient spatial queries, synced with lat/lng columns';
COMMENT ON COLUMN status_history.system_generated IS 'True if the status update was automatically generated by the system';
COMMENT ON COLUMN status_images.image_path IS 'Secure file path to stored image in Supabase Storage or similar service';