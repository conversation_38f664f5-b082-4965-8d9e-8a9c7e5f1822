# Requirements

## Functional

**FR1:** The system shall provide role-based authentication with support for Admin, CS, Account, Customer, Carrier, Driver, Factory, Shipper, Consignee, Notify Party, and Forwarder Agent user types.

**FR2:** The system shall enable CRUD operations for all master data entities including Products, Ports, Units of Measure, Companies (Customers, Carriers, Factories, Shippers, Consignees, Notify Parties, Forwarder Agents), and Drivers with appropriate role-based permissions.

**FR3:** The system shall manage Customer-Shipper relationships with default preferences and active/inactive status to streamline shipment creation.

**FR4:** The system shall manage Customer-Product relationships with pricing (CIF/FOB per KG), packaging specifications, and default product selection.

**FR5:** The system shall manage Consignee-Notify Party relationships with notification preferences and priority ordering.

**FR6:** The system shall create shipments with intelligent pre-population based on customer selection (auto-load associated shippers, products, and notify parties with defaults pre-selected).

**FR7:** The system shall support complete shipment lifecycle management from booking to delivery with status tracking across multiple transportation modes (sea, land, rail).

**FR8:** The system shall provide mobile-optimized driver interface for status updates with mandatory photo uploads and GPS location capture.

**FR9:** The system shall generate export documents (Invoice/Packing List, Commercial Contract, Shipping Instruction) with automated field population from shipment data.

**FR10:** The system shall provide multi-channel notification system supporting email, SMS, in-app, Line, and WeChat communications.

**FR11:** The system shall maintain comprehensive audit trail for all user actions and data modifications.

**FR12:** The system shall support container and shipment product management with precise weight calculations and packaging-type quantity management.

**FR13:** The system shall require Transportation Mode selection (Sea, Land, Rail) before shipment creation to configure appropriate workflow fields and requirements.

**FR14:** The system shall require Factory selection during shipment creation with factory location and capacity information display.

**FR15:** The system shall require Forwarder Agent selection during shipment creation with agent contact information and service details.

**FR16:** The system shall require ETD (Estimated Time of Departure), ETA (Estimated Time of Arrival), and Closing Time entry during shipment creation with date/time validation ensuring logical sequence (Closing Time < ETD < ETA).

**FR17:** The system shall require Destination Port selection during shipment creation for routing and documentation purposes, and require Origin Port with customer history suggestions.

**FR18:** The system shall provide optional Notes field during shipment creation for additional instructions visible to all stakeholders.

**FR19:** The system shall automatically generate unique shipment numbers upon save using format EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running] where Transportation Mode Code: 1=Land, 2=Rail, 3=Sea, and running number resets monthly per mode/port combination.

**FR20:** The system shall require Booking Confirmation Document upload during shipment creation with document validation, preview, and secure storage capabilities.

## Non Functional

**NFR1:** The system shall achieve sub-2 second page load times using Next.js optimization and Supabase infrastructure.

**NFR2:** The system shall maintain 99.9% uptime leveraging Supabase's managed infrastructure.

**NFR3:** The system shall support responsive design with mobile-first approach, optimized for field operations on mobile devices.

**NFR4:** The system shall implement dark blue theme (#1e293b, #0f172a, #334155) with orange accents (#f97316) optimized for logistics operations.

**NFR5:** The system shall comply with WCAG 2.1 AA accessibility standards minimum.

**NFR6:** The system shall support bilingual interface (Thai/English) with localized document generation.

**NFR7:** The system shall implement Row Level Security (RLS) policies for fine-grained data access control.

**NFR8:** The system shall support offline capability for mobile driver interface using Progressive Web App (PWA) technology.

**NFR9:** The system shall handle image uploads up to 10MB per photo with automatic compression and thumbnail generation.

**NFR10:** The system shall support real-time status updates using Supabase real-time subscriptions.
