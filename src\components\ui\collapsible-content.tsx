'use client'

import { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface CollapsibleContentProps {
  isOpen: boolean
  children: React.ReactNode
  className?: string
  duration?: number
}

export function CollapsibleContent({ 
  isOpen, 
  children, 
  className,
  duration = 300 
}: CollapsibleContentProps) {
  const [height, setHeight] = useState<number | undefined>(isOpen ? undefined : 0)
  const contentRef = useRef<HTMLDivElement>(null)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (!contentRef.current) return

    const element = contentRef.current
    const scrollHeight = element.scrollHeight

    if (isOpen) {
      setIsAnimating(true)
      setHeight(scrollHeight)
      
      // Reset height to auto after animation completes
      const timer = setTimeout(() => {
        setHeight(undefined)
        setIsAnimating(false)
      }, duration)
      
      return () => clearTimeout(timer)
    } else {
      setIsAnimating(true)
      setHeight(scrollHeight)
      
      // Force reflow then animate to 0
      requestAnimationFrame(() => {
        setHeight(0)
      })
      
      const timer = setTimeout(() => {
        setIsAnimating(false)
      }, duration)
      
      return () => clearTimeout(timer)
    }
  }, [isOpen, duration])

  return (
    <div
      ref={contentRef}
      className={cn(
        'overflow-hidden transition-all ease-out',
        className
      )}
      style={{
        height,
        transitionDuration: `${duration}ms`,
      }}
      aria-hidden={!isOpen}
    >
      <div className={cn(
        'transition-opacity ease-out',
        isOpen ? 'opacity-100' : 'opacity-0',
        isAnimating && 'duration-150'
      )}>
        {children}
      </div>
    </div>
  )
}