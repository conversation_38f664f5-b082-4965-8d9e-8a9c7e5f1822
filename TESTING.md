# Testing Guide - DYY Trading Management

This document provides comprehensive guidance on testing the DYY Trading Management system, specifically covering the **Story 3.1: Enhanced Intelligent Shipment Creation Interface** implementation.

## Overview

The testing suite covers three main areas:
- **Unit Tests**: Individual component and function testing
- **Integration Tests**: Cross-component interaction testing  
- **E2E Tests**: Complete user workflow testing

## Test Structure

### Unit Tests
Located in `src/**/__tests__/**/*.test.{ts,tsx}` alongside the source code:

```
src/
├── lib/utils/__tests__/
│   └── shipment-number-generator.test.ts
├── lib/validations/__tests__/
│   └── shipment.test.ts
├── hooks/__tests__/
│   └── use-shipment-relationships.test.ts
├── stores/__tests__/
│   └── shipment-creation-store.test.ts
├── components/forms/shipment-form/__tests__/
│   └── transport-mode-modal.test.tsx
└── app/(dashboard)/shipments/create/__tests__/
    └── page.test.tsx
```

### Integration Tests
Located in `tests/integration/` for cross-component testing:

```
tests/integration/
├── auth-flows.test.tsx
├── consignee-notify-party-store.test.tsx
├── health-api.test.ts
└── ports-store.test.tsx
```

### E2E Tests  
Located in `tests/e2e/` for complete workflow testing:

```
tests/e2e/
├── shipment-creation.spec.ts
├── auth-workflows.spec.ts
├── consignee-notify-party-management.spec.ts
├── ports-management.spec.ts
└── health-check.spec.ts
```

## Running Tests

### Quick Commands

```bash
# Run all unit tests
npm run test:unit

# Run all integration tests  
npm run test:integration

# Run E2E tests
npm run test:e2e

# Run all tests (unit + integration + e2e)
npm run test:all

# Run with coverage report
npm run test:coverage

# Watch mode for development
npm run test:watch

# CI mode with coverage
npm run test:ci
```

### Specific Test Categories

```bash
# Run only shipment creation tests
npx vitest run src/lib/utils/__tests__/shipment-number-generator.test.ts
npx vitest run src/stores/__tests__/shipment-creation-store.test.ts

# Run only component tests
npx vitest run src/components/**/__tests__/**/*.test.tsx

# Run E2E tests with UI
npm run test:e2e:ui

# Run specific E2E test file
npx playwright test tests/e2e/shipment-creation.spec.ts
```

## Test Coverage Goals

### Global Targets
- **Branches**: 80%
- **Functions**: 80%  
- **Lines**: 80%
- **Statements**: 80%

### Critical Component Targets
- **shipment-number-generator.ts**: 95%
- **shipment.ts (validations)**: 90%
- **shipment-creation-store.ts**: 85%

### Coverage Report
```bash
# Generate HTML coverage report
npm run test:coverage

# View coverage report
open coverage/index.html
```

## Test Categories

### 1. Shipment Number Generator Tests

**File**: `src/lib/utils/__tests__/shipment-number-generator.test.ts`

**Coverage**: 
- Number generation algorithm
- Database collision handling
- Fallback mechanisms
- Format validation
- Date extraction
- Transport mode mapping

**Key Test Cases**:
```typescript
// Format validation
expect(validateShipmentNumberFormat('EXSEA-THBKK-240315-001')).toBe(true)

// Collision detection and retry
await generateUniqueShipmentNumber({
  transportMode: 'sea', 
  portCode: 'THBKK'
})

// Fallback on database error  
// Should generate random number when DB fails
```

### 2. Validation Schema Tests

**File**: `src/lib/validations/__tests__/shipment.test.ts`

**Coverage**:
- Transport mode validation
- Port combination validation
- Date sequence validation (Closing < ETD < ETA)
- Weight validation (Net ≤ Gross)
- Container type validation
- Document validation

**Key Test Cases**:
```typescript
// Date sequence validation
const invalidSequence = {
  closingTime: new Date('2024-03-25'),
  etd: new Date('2024-03-20'), // Before closing time
  eta: new Date('2024-03-15')  // Before ETD
}
expect(() => dateSequenceSchema.parse(invalidSequence)).toThrow()

// Port combination validation  
const samePort = {
  portOfLoading: 'THBKK',
  portOfDischarge: 'THBKK', // Same as loading port
  transportMode: 'sea'
}
expect(() => portCombinationSchema.parse(samePort)).toThrow()
```

### 3. Relationship Intelligence Tests

**File**: `src/hooks/__tests__/use-shipment-relationships.test.ts`

**Coverage**:
- Customer-shipper relationship analysis
- Pricing intelligence calculations
- Route recommendations
- Historical pattern recognition
- Error handling for API failures

**Key Test Cases**:
```typescript
// Relationship strength calculation
const { result } = renderHook(() => 
  useRelationshipAnalysis('cust_1', 'ship_1')
)
await waitFor(() => {
  expect(result.current.data?.strength).toBeGreaterThan(0)
})

// Route optimization suggestions
const { result } = renderHook(() => 
  useRouteIntelligence('cust_1')
)
await waitFor(() => {
  expect(result.current.data?.suggestions[0].successRate).toBe(98)
})
```

### 4. Form State Management Tests

**File**: `src/stores/__tests__/shipment-creation-store.test.ts`

**Coverage**:
- Multi-step form navigation
- Field validation and error handling
- Draft saving and restoration
- Document upload state
- Computed selectors (completion percentage)

**Key Test Cases**:
```typescript
// Form completion calculation
act(() => {
  result.current.updateFormData({
    transportMode: 'sea',
    customerId: 'cust_123',
    // ... other required fields
  })
})
const completion = result.current.getFormCompletionPercentage()
expect(completion).toBeGreaterThan(70)

// Step validation
expect(result.current.isStepValid(1)).toBe(true) // Transport mode selected
```

### 5. Component Tests

**File**: `src/components/forms/shipment-form/__tests__/transport-mode-modal.test.tsx`

**Coverage**:
- Modal opening/closing behavior
- Transport mode selection
- Keyboard navigation
- Accessibility features
- Visual feedback states

**Key Test Cases**:
```typescript
// Modal interaction
await user.click(screen.getByText('Sea Freight'))
await waitFor(() => {
  expect(defaultProps.onSelect).toHaveBeenCalledWith('sea')
})

// Accessibility
const dialog = screen.getByRole('dialog')
expect(dialog).toHaveAttribute('aria-describedby')
```

### 6. Page Integration Tests

**File**: `src/app/(dashboard)/shipments/create/__tests__/page.test.tsx`

**Coverage**:
- Complete page rendering
- Step navigation
- Form submission
- Error handling
- Loading states
- Responsive behavior

**Key Test Cases**:
```typescript
// Form submission flow
const submitButton = screen.getByText('Create Shipment')
await user.click(submitButton)

expect(mockCreateShipment).toHaveBeenCalled()
await waitFor(() => {
  expect(mockPush).toHaveBeenCalledWith('/shipments/ship_123')
})
```

### 7. E2E Workflow Tests

**File**: `tests/e2e/shipment-creation.spec.ts`

**Coverage**:
- Complete shipment creation workflow
- Relationship intelligence integration
- Document upload process
- Form validation across steps
- Draft save/restore functionality
- Error handling scenarios

**Key Test Scenarios**:

```typescript
test('should complete full shipment creation workflow', async ({ page }) => {
  // Step 1: Transport mode selection
  await page.getByRole('button', { name: /Sea Freight/i }).click()
  
  // Step 2: Stakeholder selection with intelligence
  await page.getByLabel('Customer').click()
  await page.getByText('Test Customer Co.').click()
  
  // Step 3: Route configuration
  await page.getByLabel('Port of Loading').click()
  await page.getByText('Bangkok, Thailand (THBKK)').click()
  
  // ... complete workflow
  
  // Final: Submission and success verification
  await page.getByRole('button', { name: 'Create Shipment' }).click()
  await expect(page).toHaveURL(/\/shipments\/EXSEA-THBKK-\d{6}-\d{3}/)
})
```

## Test Data & Mocking

### Mock Data Patterns

**Companies**:
```typescript
const mockCompanies = [
  { id: 'comp_1', name: 'Test Customer Co.', type: 'customer' },
  { id: 'comp_2', name: 'Test Shipper Ltd.', type: 'shipper' }
]
```

**Ports**:
```typescript
const mockPorts = [
  { id: 'port_1', code: 'THBKK', name: 'Bangkok, Thailand' },
  { id: 'port_2', code: 'HKHKG', name: 'Hong Kong' }
]
```

**Shipment Numbers**:
```typescript
const mockShipmentNumber = {
  fullNumber: 'EXSEA-THBKK-240315-001',
  prefix: 'EX',
  modeCode: 'SEA', 
  portCode: 'THBKK',
  dateCode: '240315',
  runningNumber: 1
}
```

### Supabase Mocking

The test setup includes comprehensive Supabase mocking:

```typescript
// Automatic mock in src/test/setup.ts
vi.mock('@/lib/supabase/client', () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn().mockResolvedValue({ data: null, error: null })
        }))
      }))
    }))
  }))
}))
```

## Debugging Tests

### Common Issues

1. **Mock not working**: Ensure mock is defined before the component/hook that uses it
2. **Async test failures**: Use `waitFor()` for async operations
3. **DOM not updated**: Use `act()` for state changes
4. **Missing dependencies**: Check test setup file imports

### Debug Commands

```bash
# Run specific test with verbose output
npx vitest run --reporter=verbose src/lib/utils/__tests__/shipment-number-generator.test.ts

# Debug E2E test with browser UI
npx playwright test --debug tests/e2e/shipment-creation.spec.ts

# Run test in watch mode with coverage
npx vitest --watch --coverage
```

### Visual Debugging

```typescript
// Add to component tests for visual debugging
import { screen } from '@testing-library/react'

test('debug test', () => {
  render(<MyComponent />)
  screen.debug() // Prints current DOM state
})
```

## Performance Testing

### Load Testing Considerations

The E2E tests include performance validation:

```typescript
// Check form performance
test('should handle large form efficiently', async ({ page }) => {
  const startTime = Date.now()
  
  // Complete form steps
  await fillCompleteForm(page)
  
  const endTime = Date.now()
  expect(endTime - startTime).toBeLessThan(5000) // 5s threshold
})
```

### Memory Usage

Monitor for memory leaks in long-running tests:

```bash
# Run with memory profiling
node --max-old-space-size=4096 ./node_modules/.bin/vitest
```

## Continuous Integration

### GitHub Actions Integration

```yaml
# Example CI configuration
- name: Run Tests
  run: |
    npm run test:ci
    npm run test:e2e
    
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Quality Gates

Tests must pass these criteria:
- All unit tests pass
- Coverage thresholds met
- E2E critical paths pass
- No accessibility violations
- Performance benchmarks met

## Best Practices

1. **Test Naming**: Use descriptive test names that explain the expected behavior
2. **Test Structure**: Follow AAA pattern (Arrange, Act, Assert)
3. **Mock Strategy**: Mock external dependencies, test real logic
4. **Error Testing**: Always test error conditions and edge cases
5. **Accessibility**: Include accessibility testing in component tests
6. **Performance**: Include performance assertions in E2E tests

## Troubleshooting

### Common Test Failures

1. **"Cannot find module"**: Check import paths and aliases
2. **"timeout exceeded"**: Increase timeout or fix async handling
3. **"element not found"**: Check selectors and wait conditions
4. **"mock not called"**: Verify mock setup and component behavior

### Getting Help

1. Check test output for specific error messages
2. Review mock configurations in `src/test/setup.ts`
3. Verify component implementations match test expectations
4. Check for timing issues in async tests

This comprehensive testing suite ensures the reliability, performance, and user experience quality of the Enhanced Intelligent Shipment Creation Interface.