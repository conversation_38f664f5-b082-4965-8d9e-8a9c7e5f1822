'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  Edit,
  Package,
  Weight,
  AlertTriangle,
  CheckCircle,
  Info,
  Plus,
  RefreshCw,
  Download,
  ShieldCheck,
  BadgeCheck
} from 'lucide-react'
import { useContainerManagement } from '@/hooks/use-containers'
import { ContainerEditForm } from './container-edit-form'
import { ContainerConfirmationDialog } from './container-confirmation-dialog'
import { CONTAINER_TYPES, CONTAINER_SIZES } from '@/lib/validations/shipment'
import type { ContainerWithProducts } from '@/hooks/use-containers'

interface ContainerListProps {
  shipment_id: string
  editable?: boolean
  onContainerUpdate?: () => void
}

export function ContainerList({ shipment_id, editable = true, onContainerUpdate }: ContainerListProps) {
  const {
    containers,
    loading,
    error,
    refetch,
    calculateShipmentTotals,
  } = useContainerManagement(shipment_id)

  const [selectedContainer, setSelectedContainer] = useState<ContainerWithProducts | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false)

  const shipmentTotals = calculateShipmentTotals()

  // Helper functions
  const getContainerTypeLabel = (type: string | null) => {
    if (!type) return 'Unknown'
    return CONTAINER_TYPES.find(t => t.value === type)?.label || type
  }

  const getContainerSizeLabel = (size: string | null) => {
    if (!size) return 'Unknown'
    return CONTAINER_SIZES.find(s => s.value === size)?.label || size
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization > 0.95) return 'bg-red-500'
    if (utilization > 0.85) return 'bg-yellow-500'
    if (utilization < 0.6) return 'bg-blue-500'
    return 'bg-green-500'
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'empty': return 'secondary'
      case 'loaded': return 'default'
      case 'in_transit': return 'secondary'
      case 'delivered': return 'success'
      default: return 'secondary'
    }
  }

  const formatWeight = (weight: number) => {
    return `${weight.toLocaleString('en-US', { maximumFractionDigits: 1 })} kg`
  }

  const formatVolume = (volume: number) => {
    return `${volume.toLocaleString('en-US', { maximumFractionDigits: 2 })} m³`
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}`
  }

  const handleEditContainer = (container: ContainerWithProducts) => {
    setSelectedContainer(container)
    setEditDialogOpen(true)
  }

  const handleConfirmNumbers = (container: ContainerWithProducts) => {
    setSelectedContainer(container)
    setConfirmationDialogOpen(true)
  }

  const handleEditComplete = () => {
    setEditDialogOpen(false)
    setSelectedContainer(null)
    refetch()
    onContainerUpdate?.()
  }

  const handleConfirmationComplete = () => {
    setConfirmationDialogOpen(false)
    setSelectedContainer(null)
    refetch()
    onContainerUpdate?.()
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Loading Containers...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error Loading Containers</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Containers</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shipmentTotals.total_containers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Weight</CardTitle>
            <Weight className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatWeight(shipmentTotals.total_weight)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value (CIF)</CardTitle>
            <Info className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(shipmentTotals.total_value_cif)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Utilization</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(shipmentTotals.average_utilization * 100).toFixed(1)}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Warnings */}
      {shipmentTotals.overweight_containers > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Overweight Containers Detected</AlertTitle>
          <AlertDescription>
            {shipmentTotals.overweight_containers} container(s) exceed safe weight limits. 
            Review and redistribute cargo to prevent issues during transport.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Container List */}
      <Card className="bg-card border-secondary">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Containers ({containers.length})
              </CardTitle>
              <CardDescription>
                Manage container details, view allocations, and monitor utilization
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={refetch}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              {/*editable && (
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Container
                </Button>
              )*/}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {containers.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Containers Found</h3>
              <p className="text-muted-foreground mb-4">
                No containers have been created for this shipment yet.
              </p>
              {editable && (
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Container
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {containers.map((container) => (
                <Card key={container.id} className="relative">
                  <CardContent className="p-6 bg-dark/50 rounded-md">
                    <div className="grid gap-6 lg:grid-cols-3">
                      {/* Container Info */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <h4 className="font-semibold text-orange-400">
                              {container.container_number || 'Unassigned'}
                            </h4>
                            {container.container_number_confirmed && (
                              // <Badge variant="secondary" className="bg-green-600 text-white text-xs">
                              //   Confirmed
                              // </Badge>
                              <BadgeCheck className="h-5 w-5 text-green-500" />
                            )}
                          </div>
                          {editable && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleConfirmNumbers(container)}
                              className="text-orange-500 bg-orange-700/30 hover:text-orange-100 hover:bg-orange-700"
                            >
                              <ShieldCheck className="h-5 w-5" />
                            </Button>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-muted-foreground">Seal Number:</span>
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-yellow-400">
                                {container.seal_number || 'Unassigned'}
                              </span>
                              {container.seal_number_confirmed && (
                                // <Badge variant="secondary" className="bg-green-600 text-white text-xs">
                                //   Confirmed
                                // </Badge>
                                <BadgeCheck className="h-5 w-5 text-green-500" />
                              )}
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Status:</span>
                            <div className="flex items-center gap-2">
                              <Badge variant={getStatusBadgeVariant(container.status)}>
                                {container.status}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-muted-foreground">Type:</span>
                            <div className="font-medium">
                              {getContainerTypeLabel(container.container_type)}
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Size:</span>
                            <div className="font-medium">
                              {getContainerSizeLabel(container.container_size)}
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-muted-foreground">Temperature:</span>
                            <div className="font-medium text-cyan-400">
                              {container.temperature}
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Vent:</span>
                            <div className="font-medium text-cyan-400">
                              {container.vent}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Utilization */}
                      <div className="space-y-3">
                        <h5 className="font-medium text-sm">Utilization</h5>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Weight</span>
                            <span>{(container.weight_utilization * 100).toFixed(1)}%</span>
                          </div>
                          <Progress 
                            value={container.weight_utilization * 100} 
                            className="h-2"
                          />
                          <div className="text-xs text-muted-foreground">
                            {formatWeight(container.total_weight)} of capacity
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Volume</span>
                            <span>{(container.volume_utilization * 100).toFixed(1)}%</span>
                          </div>
                          <Progress 
                            value={container.volume_utilization * 100} 
                            className="h-2"
                          />
                          <div className="text-xs text-muted-foreground">
                            {formatVolume(container.total_volume)} of {formatVolume(container.volume || 0)}
                          </div>
                        </div>
                      </div>

                      {/* Products Summary */}
                      <div className="space-y-3">
                        <h5 className="font-medium text-sm">Products ({container.total_products})</h5>
                        
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-muted-foreground">Total Weight:</span>
                            <div className="font-medium">{formatWeight(container.total_weight)}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Total Volume:</span>
                            <div className="font-medium">{formatVolume(container.total_volume)}</div>
                          </div>
                        </div>

                        {container.products.length > 0 && (
                          <div className="space-y-1">
                            <div className="text-xs text-muted-foreground">Recent products:</div>
                            {container.products.slice(0, 2).map((product) => (
                              <div key={product.id} className="text-xs bg-muted/70 p-2 rounded">
                                {product.product_description} : {product.quantity} × {product.packaging_type}
                              </div>
                            ))}
                            {container.products.length > 2 && (
                              <div className="text-xs text-muted-foreground">
                                +{container.products.length - 2} more...
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Warning indicators */}
                    {container.weight_utilization > 0.95 && (
                      <div className="absolute top-2 right-2">
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Container Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Container</DialogTitle>
            <DialogDescription>
              Edit container details, specifications, and manage confirmation status.
            </DialogDescription>
          </DialogHeader>
          {selectedContainer && (
            <ContainerEditForm
              container={selectedContainer}
              onSave={handleEditComplete}
              onCancel={() => setEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Container Confirmation Dialog */}
      {selectedContainer && (
        <ContainerConfirmationDialog
          container={selectedContainer}
          isOpen={confirmationDialogOpen}
          onClose={() => setConfirmationDialogOpen(false)}
          onConfirm={handleConfirmationComplete}
        />
      )}
    </div>
  )
}