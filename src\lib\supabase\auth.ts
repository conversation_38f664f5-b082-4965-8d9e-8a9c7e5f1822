import { createClient as createB<PERSON>erClient } from './client'
import { redirect } from 'next/navigation'
import type { User } from '@supabase/supabase-js'

// Define user roles type based on database enum
export type UserRole =
  | 'admin'
  | 'cs'
  | 'account'
  | 'customer'
  | 'carrier'
  | 'driver'
  | 'factory'
  | 'shipper'
  | 'consignee'
  | 'notify_party'
  | 'forwarder_agent'

// User profile interface
export interface UserProfile {
  user_id: string
  email: string
  first_name?: string
  last_name?: string
  phone_number?: string
  line_id?: string
  wechat_id?: string
  role: UserRole
  company_id?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// Check if user has admin privileges
export function isAdmin(role: UserRole): boolean {
  return role === 'admin'
}

// Check if user has staff privileges (admin, cs, account)
export function isStaff(role: UserRole): boolean {
  return ['admin', 'cs', 'account'].includes(role)
}

// Check if user can manage other users
export function canManageUsers(role: UserRole): boolean {
  return isAdmin(role)
}

// Check if user can view specific data based on role
export function canViewAllData(role: UserRole): boolean {
  return isStaff(role)
}

// Validate role-company association
export function validateRoleCompanyAssociation(
  role: UserRole,
  companyType?: string
): boolean {
  switch (role) {
    case 'admin':
    case 'cs':
    case 'account':
      // Staff roles don't require specific company association
      return true
    case 'customer':
      return companyType === 'customer'
    case 'carrier':
    case 'driver':
      return companyType === 'carrier'
    case 'factory':
      return companyType === 'factory'
    case 'shipper':
      return companyType === 'shipper'
    case 'consignee':
      return companyType === 'consignee'
    case 'notify_party':
      return companyType === 'notify_party'
    case 'forwarder_agent':
      return companyType === 'forwarder_agent'
    default:
      return false
  }
}

// Sign out user
export async function signOut(): Promise<void> {
  const supabase = createBrowserClient()
  await supabase.auth.signOut()
  redirect('/login')
}

// Authentication error types
export interface AuthError {
  code: string
  message: string
  details?: any
  timestamp: Date
}

// Enhanced error handling for Supabase auth errors
export class AuthErrorHandler {
  static mapSupabaseError(error: any): AuthError {
    const timestamp = new Date()

    // Map Supabase auth errors to user-friendly messages
    switch (error.message || error.error_description) {
      case 'Invalid login credentials':
        return {
          code: 'INVALID_CREDENTIALS',
          message:
            'The email or password you entered is incorrect. Please try again.',
          timestamp,
        }
      case 'Email not confirmed':
        return {
          code: 'EMAIL_NOT_CONFIRMED',
          message:
            'Please check your email and click the confirmation link before signing in.',
          timestamp,
        }
      case 'Too many requests':
        return {
          code: 'RATE_LIMITED',
          message:
            'Too many login attempts. Please wait a few minutes before trying again.',
          timestamp,
        }
      case 'User not found':
        return {
          code: 'USER_NOT_FOUND',
          message: 'No account found with this email address.',
          timestamp,
        }
      case 'Signup disabled':
        return {
          code: 'SIGNUP_DISABLED',
          message:
            'Account registration is currently disabled. Please contact support.',
          timestamp,
        }
      case 'Password too short':
        return {
          code: 'WEAK_PASSWORD',
          message: 'Password must be at least 8 characters long.',
          timestamp,
        }
      case 'Email already in use':
      case 'User already registered':
        return {
          code: 'EMAIL_EXISTS',
          message:
            'An account with this email already exists. Try signing in instead.',
          timestamp,
        }
      case 'Invalid email':
        return {
          code: 'INVALID_EMAIL',
          message: 'Please enter a valid email address.',
          timestamp,
        }
      case 'Network error':
        return {
          code: 'NETWORK_ERROR',
          message:
            'Connection failed. Please check your internet connection and try again.',
          timestamp,
        }
      case 'Phone number not confirmed':
      case 'Invalid phone number':
        return {
          code: 'INVALID_PHONE',
          message: 'Please enter a valid phone number with country code (e.g., +***********)',
          timestamp,
        }
      case 'Phone rate limit exceeded':
        return {
          code: 'PHONE_RATE_LIMITED',
          message: 'Too many SMS requests. Please wait before requesting another code.',
          timestamp,
        }
      case 'Invalid token':
      case 'Token has expired':
        return {
          code: 'INVALID_OTP',
          message: 'Invalid or expired verification code. Please request a new code.',
          timestamp,
        }
      case 'Phone number already exists':
        return {
          code: 'PHONE_EXISTS',
          message: 'This phone number is already registered.',
          timestamp,
        }
      default:
        // Log unknown errors for debugging
        console.error('Unknown auth error:', error)
        return {
          code: 'UNKNOWN_ERROR',
          message:
            'An unexpected error occurred. Please try again or contact support.',
          details: error,
          timestamp,
        }
    }
  }

  static getRetryDelay(attemptCount: number): number {
    // Exponential backoff: 1s, 2s, 4s, 8s, 16s (max)
    return Math.min(1000 * Math.pow(2, attemptCount), 16000)
  }
}

// Client-side auth state management with enhanced error handling
export class AuthClient {
  private supabase = createBrowserClient()
  private loginAttempts = new Map<
    string,
    { count: number; lastAttempt: Date }
  >()

  private checkRateLimit(email: string): boolean {
    const attempts = this.loginAttempts.get(email)
    if (!attempts) return true

    const timeSinceLastAttempt = Date.now() - attempts.lastAttempt.getTime()
    const requiredDelay = AuthErrorHandler.getRetryDelay(attempts.count - 1)

    return timeSinceLastAttempt >= requiredDelay
  }

  private recordLoginAttempt(email: string, success: boolean): void {
    const current = this.loginAttempts.get(email) || {
      count: 0,
      lastAttempt: new Date(),
    }

    if (success) {
      // Reset on successful login
      this.loginAttempts.delete(email)
    } else {
      // Increment failed attempts
      this.loginAttempts.set(email, {
        count: current.count + 1,
        lastAttempt: new Date(),
      })
    }
  }

  async signUp(
    email: string,
    password: string,
    userData: Partial<UserProfile>
  ) {
    try {
      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      })

      if (error) {
        throw AuthErrorHandler.mapSupabaseError(error)
      }

      // If user was created successfully, create profile record
      if (data.user) {
        const { error: profileError } = await this.supabase
          .from('profiles')
          .insert({
            user_id: data.user.id,
            email: email,
            first_name: userData.first_name || null,
            last_name: userData.last_name || null,
            phone_number: userData.phone_number || null,
            line_id: userData.line_id || null,
            wechat_id: userData.wechat_id || null,
            role: userData.role || 'customer',
            company_id: userData.company_id || null,
            is_active: true,
          })

        if (profileError) {
          console.error('Error creating profile:', profileError)
          // Create a custom error for profile creation failure
          throw {
            code: 'PROFILE_CREATION_FAILED',
            message:
              'Account created but profile setup failed. Please contact support.',
            details: profileError,
            timestamp: new Date(),
          } as AuthError
        }
      }

      return data
    } catch (error) {
      if (error instanceof Error || (error as AuthError).code) {
        throw error
      }
      throw AuthErrorHandler.mapSupabaseError(error)
    }
  }

  async signIn(email: string, password: string) {
    try {
      // Check rate limiting
      if (!this.checkRateLimit(email)) {
        const attempts = this.loginAttempts.get(email)!
        const delay = AuthErrorHandler.getRetryDelay(attempts.count - 1)
        throw {
          code: 'RATE_LIMITED',
          message: `Too many failed attempts. Please wait ${Math.ceil(delay / 1000)} seconds before trying again.`,
          timestamp: new Date(),
        } as AuthError
      }

      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        this.recordLoginAttempt(email, false)
        throw AuthErrorHandler.mapSupabaseError(error)
      }

      // Check if user profile exists and is active
      if (data.user) {
        const { data: profile, error: profileError } = await this.supabase
          .from('profiles')
          .select('is_active, role')
          .eq('user_id', data.user.id)
          .single()

        if (profileError) {
          throw {
            code: 'PROFILE_NOT_FOUND',
            message: 'User profile not found. Please contact support.',
            timestamp: new Date(),
          } as AuthError
        }

        if (!profile.is_active) {
          // Sign out the user since their account is deactivated
          await this.supabase.auth.signOut()
          throw {
            code: 'ACCOUNT_DEACTIVATED',
            message:
              'Your account has been deactivated. Please contact support to reactivate.',
            timestamp: new Date(),
          } as AuthError
        }
      }

      this.recordLoginAttempt(email, true)
      return data
    } catch (error) {
      if (error instanceof Error || (error as AuthError).code) {
        throw error
      }
      throw AuthErrorHandler.mapSupabaseError(error)
    }
  }

  async signOut() {
    try {
      const { error } = await this.supabase.auth.signOut()
      if (error) {
        throw AuthErrorHandler.mapSupabaseError(error)
      }
    } catch (error) {
      if (error instanceof Error || (error as AuthError).code) {
        throw error
      }
      throw AuthErrorHandler.mapSupabaseError(error)
    }
  }

  async resetPassword(email: string) {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })

      if (error) {
        throw AuthErrorHandler.mapSupabaseError(error)
      }
    } catch (error) {
      if (error instanceof Error || (error as AuthError).code) {
        throw error
      }
      throw AuthErrorHandler.mapSupabaseError(error)
    }
  }

  async updatePassword(password: string) {
    try {
      const { error } = await this.supabase.auth.updateUser({
        password,
      })

      if (error) {
        throw AuthErrorHandler.mapSupabaseError(error)
      }
    } catch (error) {
      if (error instanceof Error || (error as AuthError).code) {
        throw error
      }
      throw AuthErrorHandler.mapSupabaseError(error)
    }
  }

  async changePassword(currentPassword: string, newPassword: string) {
    try {
      // First verify current password by attempting to sign in
      const user = await this.supabase.auth.getUser()
      if (!user.data.user) {
        throw {
          code: 'NOT_AUTHENTICATED',
          message: 'You must be signed in to change your password.',
          timestamp: new Date(),
        } as AuthError
      }

      // Verify current password
      const { error: verifyError } =
        await this.supabase.auth.signInWithPassword({
          email: user.data.user.email!,
          password: currentPassword,
        })

      if (verifyError) {
        throw {
          code: 'CURRENT_PASSWORD_INCORRECT',
          message: 'Current password is incorrect.',
          timestamp: new Date(),
        } as AuthError
      }

      // Update to new password
      const { error } = await this.supabase.auth.updateUser({
        password: newPassword,
      })

      if (error) {
        throw AuthErrorHandler.mapSupabaseError(error)
      }
    } catch (error) {
      if (error instanceof Error || (error as AuthError).code) {
        throw error
      }
      throw AuthErrorHandler.mapSupabaseError(error)
    }
  }

  async signInWithPhone(phone: string) {
    try {
      const { data, error } = await this.supabase.auth.signInWithOtp({
        phone: phone,
        options: {
          // Set 6-digit token length and 5-minute expiry
          data: { phone },
        },
      })

      if (error) {
        throw AuthErrorHandler.mapSupabaseError(error)
      }

      return data
    } catch (error) {
      if (error instanceof Error || (error as AuthError).code) {
        throw error
      }
      throw AuthErrorHandler.mapSupabaseError(error)
    }
  }

  async verifyPhoneOtp(phone: string, token: string) {
    try {
      const { data, error } = await this.supabase.auth.verifyOtp({
        phone: phone,
        token: token,
        type: 'sms',
      })

      if (error) {
        throw AuthErrorHandler.mapSupabaseError(error)
      }

      // Check if user exists in profiles and has driver role
      if (data.user) {
        const { data: profile, error: profileError } = await this.supabase
          .from('profiles')
          .select('is_active, role')
          .eq('user_id', data.user.id)
          .single()

        if (profileError) {
          // User doesn't exist in profiles - check if they exist as a driver by phone
          const { data: driver, error: driverError } = await this.supabase
            .from('drivers')
            .select('id, driver_first_name, driver_last_name, is_active, carrier_id, carrier:companies(name)')
            .eq('phone', phone)
            .eq('is_active', true)
            .single()

          if (driverError || !driver) {
            // Sign out the user since no driver record found
            await this.supabase.auth.signOut()
            throw {
              code: 'DRIVER_NOT_FOUND',
              message: 'No active driver account found with this phone number. Please contact your dispatcher.',
              timestamp: new Date(),
            } as AuthError
          }

          // Create profile for the driver
          const { error: createProfileError } = await this.supabase
            .from('profiles')
            .insert({
              user_id: data.user.id,
              email: `driver_${driver.id}@temp.dyy`, // Temporary email
              first_name: driver.driver_first_name,
              last_name: driver.driver_last_name,
              phone_number: phone,
              role: 'driver',
              company_id: driver.carrier_id,
              is_active: true,
            })

          if (createProfileError) {
            console.error('Error creating driver profile:', createProfileError)
            throw {
              code: 'PROFILE_CREATION_FAILED',
              message: 'Failed to create driver profile. Please contact support.',
              details: createProfileError,
              timestamp: new Date(),
            } as AuthError
          }

          // Update driver record with user_id
          await this.supabase
            .from('drivers')
            .update({ user_id: data.user.id })
            .eq('id', driver.id)

        } else {
          // Profile exists - check if it's a driver role
          if (profile.role !== 'driver') {
            await this.supabase.auth.signOut()
            throw {
              code: 'INVALID_ROLE',
              message: 'This phone authentication is only for drivers. Please use email login for other roles.',
              timestamp: new Date(),
            } as AuthError
          }

          if (!profile.is_active) {
            await this.supabase.auth.signOut()
            throw {
              code: 'ACCOUNT_DEACTIVATED',
              message: 'Your driver account has been deactivated. Please contact your dispatcher.',
              timestamp: new Date(),
            } as AuthError
          }
        }
      }

      return data
    } catch (error) {
      if (error instanceof Error || (error as AuthError).code) {
        throw error
      }
      throw AuthErrorHandler.mapSupabaseError(error)
    }
  }

  onAuthStateChange(callback: (event: string, session: any) => void) {
    return this.supabase.auth.onAuthStateChange(callback)
  }
}

// Export singleton instance
export const authClient = new AuthClient()
