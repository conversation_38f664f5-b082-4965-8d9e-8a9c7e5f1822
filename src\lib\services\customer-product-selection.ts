/**
 * Customer Product Selection Service
 * Handles logic for automatic and manual product selection during shipment creation
 */

import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/types/database'
import type { CustomerProductRelationship } from '@/hooks/use-shipment-relationships'

type Tables = Database['public']['Tables']
type CustomerProduct = Tables['customer_products']['Row'] & {
  product: Tables['products']['Row']
}

export interface ProductSelectionResult {
  /**
   * Selection mode based on available products
   */
  mode: 'single' | 'multiple' | 'none'
  
  /**
   * Available products for the customer
   */
  products: CustomerProductRelationship[]
  
  /**
   * Auto-selected product (for single mode)
   */
  autoSelected?: CustomerProductRelationship
  
  /**
   * Default product if available
   */
  defaultProduct?: CustomerProductRelationship
  
  /**
   * Total count of active products for customer
   */
  totalCount: number
}

export interface ProductSelectionOptions {
  /**
   * Customer ID to get products for
   */
  customerId: string
  
  /**
   * Whether to auto-select single products
   */
  autoSelectSingle?: boolean
  
  /**
   * Whether to prefer default products
   */
  preferDefaults?: boolean
  
  /**
   * Include inactive relationships
   */
  includeInactive?: boolean
}

export class CustomerProductSelectionService {
  private supabase = createClient()

  /**
   * Analyze customer-product relationships and determine selection strategy
   */
  async analyzeCustomerProducts(
    options: ProductSelectionOptions
  ): Promise<ProductSelectionResult> {
    const {
      customerId,
      autoSelectSingle = true,
      preferDefaults = true,
      includeInactive = false,
    } = options

    try {
      // Query customer products with product details
      let query = this.supabase
        .from('customer_products')
        .select(`
          id,
          customer_id,
          product_id,
          is_default,
          is_active,
          unit_price_cif,
          unit_price_fob,
          currency_code,
          quality_grade,
          standard_quantity,
          gross_weight_per_package,
          net_weight_per_package,
          packaging_type,
          notes,
          created_at,
          product:products!inner(
            id,
            name,
            code,
            unit_of_measure_id
          )
        `)
        .eq('customer_id', customerId)

      if (!includeInactive) {
        query = query.eq('is_active', true)
      }

      // Order by default first, then by name
      query = query.order('is_default', { ascending: false })
                  .order('created_at', { ascending: false })

      const { data: customerProducts, error } = await query

      if (error) {
        console.error('Error fetching customer products:', error)
        throw new Error(`Failed to fetch customer products: ${error.message}`)
      }

      if (!customerProducts || customerProducts.length === 0) {
        return {
          mode: 'none',
          products: [],
          totalCount: 0,
        }
      }

      // Transform to expected format
      const products: CustomerProductRelationship[] = customerProducts.map(cp => ({
        id: cp.id,
        customer_id: cp.customer_id,
        product_id: cp.product_id,
        product: {
          id: cp.product.id,
          name: cp.product.name,
          code: cp.product.code,
          unit_of_measure_id: cp.product.unit_of_measure_id,
        },
        is_default: cp.is_default,
        is_active: cp.is_active,
        unit_price_cif: cp.unit_price_cif,
        unit_price_fob: cp.unit_price_fob,
        currency_code: cp.currency_code,
        quality_grade: cp.quality_grade,
        standard_quantity: cp.standard_quantity,
        gross_weight_per_package: cp.gross_weight_per_package,
        net_weight_per_package: cp.net_weight_per_package,
        packaging_type: cp.packaging_type,
        notes: cp.notes,
        created_at: cp.created_at,
      }))

      const totalCount = products.length
      const defaultProduct = products.find(p => p.is_default)

      // Determine selection mode and auto-selection
      let mode: ProductSelectionResult['mode']
      let autoSelected: CustomerProductRelationship | undefined

      if (totalCount === 1) {
        mode = 'single'
        if (autoSelectSingle) {
          autoSelected = products[0]
        }
      } else if (totalCount > 1) {
        mode = 'multiple'
        // Auto-select default product if preferred and available
        if (preferDefaults && defaultProduct) {
          autoSelected = defaultProduct
        }
      } else {
        mode = 'none'
      }

      return {
        mode,
        products,
        autoSelected,
        defaultProduct,
        totalCount,
      }
    } catch (error) {
      console.error('Error analyzing customer products:', error)
      throw error
    }
  }

  /**
   * Get product details by customer and product ID
   */
  async getProductRelationship(
    customerId: string,
    productId: string
  ): Promise<CustomerProductRelationship | null> {
    try {
      const { data, error } = await this.supabase
        .from('customer_products')
        .select(`
          id,
          customer_id,
          product_id,
          is_default,
          is_active,
          unit_price_cif,
          unit_price_fob,
          currency_code,
          quality_grade,
          standard_quantity,
          gross_weight_per_package,
          net_weight_per_package,
          packaging_type,
          notes,
          created_at,
          product:products!inner(
            id,
            name,
            code,
            unit_of_measure_id
          )
        `)
        .eq('customer_id', customerId)
        .eq('product_id', productId)
        .eq('is_active', true)
        .single()

      if (error) {
        console.error('Error fetching product relationship:', error)
        return null
      }

      if (!data) {
        return null
      }

      return {
        id: data.id,
        customer_id: data.customer_id,
        product_id: data.product_id,
        product: {
          id: data.product.id,
          name: data.product.name,
          code: data.product.code,
          unit_of_measure_id: data.product.unit_of_measure_id,
        },
        is_default: data.is_default,
        is_active: data.is_active,
        unit_price_cif: data.unit_price_cif,
        unit_price_fob: data.unit_price_fob,
        currency_code: data.currency_code,
        quality_grade: data.quality_grade,
        standard_quantity: data.standard_quantity,
        gross_weight_per_package: data.gross_weight_per_package,
        net_weight_per_package: data.net_weight_per_package,
        packaging_type: data.packaging_type,
        notes: data.notes,
        created_at: data.created_at,
      }
    } catch (error) {
      console.error('Error getting product relationship:', error)
      return null
    }
  }

  /**
   * Validate product selection against customer relationships
   */
  async validateProductSelection(
    customerId: string,
    productId: string
  ): Promise<{
    isValid: boolean
    errors: string[]
    relationship?: CustomerProductRelationship
  }> {
    const errors: string[] = []

    try {
      // Check if customer exists
      const { data: customer, error: customerError } = await this.supabase
        .from('companies')
        .select('id, name')
        .eq('id', customerId)
        .single()

      if (customerError || !customer) {
        errors.push('Customer not found')
      }

      // Check if product relationship exists and is active
      const relationship = await this.getProductRelationship(customerId, productId)

      if (!relationship) {
        errors.push('No active product relationship found for this customer-product combination')
      } else if (!relationship.is_active) {
        errors.push('Product relationship is not active')
      }

      return {
        isValid: errors.length === 0,
        errors,
        relationship: relationship || undefined,
      }
    } catch (error) {
      console.error('Error validating product selection:', error)
      return {
        isValid: false,
        errors: ['Validation failed due to system error'],
      }
    }
  }

  /**
   * Get pricing information for a customer-product relationship
   */
  async getProductPricing(
    customerId: string,
    productId: string,
    quantity: number = 1
  ): Promise<{
    unit_price_cif: number
    unit_price_fob: number
    total_value_cif: number
    total_value_fob: number
    currency_code: string
  } | null> {
    try {
      const relationship = await this.getProductRelationship(customerId, productId)

      if (!relationship) {
        return null
      }

      return {
        unit_price_cif: relationship.unit_price_cif,
        unit_price_fob: relationship.unit_price_fob,
        total_value_cif: Math.round((relationship.unit_price_cif * quantity * relationship.net_weight_per_package) * 100) / 100,
        total_value_fob: Math.round((relationship.unit_price_fob * quantity * relationship.net_weight_per_package) * 100) / 100,
        currency_code: relationship.currency_code,
      }
    } catch (error) {
      console.error('Error getting product pricing:', error)
      return null
    }
  }

  /**
   * Search customer products by name or code
   */
  async searchCustomerProducts(
    customerId: string,
    searchTerm: string,
    options: {
      includeInactive?: boolean
      limit?: number
    } = {}
  ): Promise<CustomerProductRelationship[]> {
    const { includeInactive = false, limit = 20 } = options

    try {
      let query = this.supabase
        .from('customer_products')
        .select(`
          id,
          customer_id,
          product_id,
          is_default,
          is_active,
          unit_price_cif,
          unit_price_fob,
          currency_code,
          quality_grade,
          standard_quantity,
          gross_weight_per_package,
          net_weight_per_package,
          packaging_type,
          notes,
          created_at,
          product:products!inner(
            id,
            name,
            code,
            unit_of_measure_id
          )
        `)
        .eq('customer_id', customerId)

      if (!includeInactive) {
        query = query.eq('is_active', true)
      }

      // Search by product name or code
      query = query.or(
        `product.name.ilike.%${searchTerm}%,product.code.ilike.%${searchTerm}%`
      )

      query = query.limit(limit)

      const { data, error } = await query

      if (error) {
        console.error('Error searching customer products:', error)
        return []
      }

      if (!data) {
        return []
      }

      return data.map(cp => ({
        id: cp.id,
        customer_id: cp.customer_id,
        product_id: cp.product_id,
        product: {
          id: cp.product.id,
          name: cp.product.name,
          code: cp.product.code,
          unit_of_measure_id: cp.product.unit_of_measure_id,
        },
        is_default: cp.is_default,
        is_active: cp.is_active,
        unit_price_cif: cp.unit_price_cif,
        unit_price_fob: cp.unit_price_fob,
        currency_code: cp.currency_code,
        quality_grade: cp.quality_grade,
        standard_quantity: cp.standard_quantity,
        gross_weight_per_package: cp.gross_weight_per_package,
        net_weight_per_package: cp.net_weight_per_package,
        packaging_type: cp.packaging_type,
        notes: cp.notes,
        created_at: cp.created_at,
      }))
    } catch (error) {
      console.error('Error searching customer products:', error)
      return []
    }
  }
}

// Export singleton instance
export const customerProductSelectionService = new CustomerProductSelectionService()

// Export convenience functions
export const analyzeCustomerProducts = (options: ProductSelectionOptions) =>
  customerProductSelectionService.analyzeCustomerProducts(options)

export const getProductRelationship = (customerId: string, productId: string) =>
  customerProductSelectionService.getProductRelationship(customerId, productId)

export const validateProductSelection = (customerId: string, productId: string) =>
  customerProductSelectionService.validateProductSelection(customerId, productId)

export const getProductPricing = (
  customerId: string,
  productId: string,
  quantity?: number
) => customerProductSelectionService.getProductPricing(customerId, productId, quantity)

export const searchCustomerProducts = (
  customerId: string,
  searchTerm: string,
  options?: { includeInactive?: boolean; limit?: number }
) => customerProductSelectionService.searchCustomerProducts(customerId, searchTerm, options)