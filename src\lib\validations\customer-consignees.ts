import { z } from 'zod'

// Base customer-consignee relationship schema
export const customerConsigneeSchema = z.object({
  customer_id: z.string().uuid('Invalid customer ID format'),
  consignee_id: z.string().uuid('Invalid consignee ID format'),
  is_default: z.boolean().default(false),
  is_active: z.boolean().default(true),
  notes: z.string().optional().nullable(),
})

// Form schema for creating/updating relationships
export const customerConsigneeFormSchema = z
  .object({
    customer_id: z
      .string({
        required_error: 'Customer selection is required',
      })
      .uuid('Invalid customer ID format'),

    consignee_id: z
      .string({
        required_error: 'Consignee selection is required',
      })
      .uuid('Invalid consignee ID format'),

    is_default: z.boolean().default(false),

    is_active: z.boolean().default(true),

    notes: z
      .string()
      .optional()
      .nullable()
      .transform(val => (val === '' ? null : val)),
  })
  .refine(data => data.customer_id !== data.consignee_id, {
    message: 'Customer and consignee cannot be the same company',
    path: ['consignee_id'],
  })

// Update schema (all fields optional except IDs when provided)
export const customerConsigneeUpdateSchema = customerConsigneeFormSchema
  .partial()
  .extend({
    id: z.string().uuid().optional(),
  })

// Filter schema for search and filtering
export const customerConsigneeFilterSchema = z
  .object({
    customer_id: z.string().uuid().optional(),
    consignee_id: z.string().uuid().optional(),
    is_active: z.boolean().optional(),
    is_default: z.boolean().optional(),
    search: z.string().optional(),
  })
  .partial()

// Bulk import schema
export const customerConsigneeBulkImportSchema = z.object({
  customer_name: z.string().min(1, 'Customer name is required'),
  consignee_name: z.string().min(1, 'Consignee name is required'),
  is_default: z
    .union([
      z.boolean(),
      z.string().transform(val => {
        const lower = val.toLowerCase().trim()
        return (
          lower === 'true' || lower === '1' || lower === 'yes' || lower === 'y'
        )
      }),
    ])
    .default(false),
  notes: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
})

export const customerConsigneeBulkImportArraySchema = z.array(
  customerConsigneeBulkImportSchema
)

// Sort options
export const customerConsigneeSortSchema = z
  .enum([
    'customer_name',
    'consignee_name',
    'is_default',
    'is_active',
    'created_at',
    'updated_at',
  ])
  .default('customer_name')

// Sort order
export const sortOrderSchema = z.enum(['asc', 'desc']).default('asc')

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
})

// Combined query schema for API endpoints
export const customerConsigneeQuerySchema = customerConsigneeFilterSchema
  .extend({
    sort: customerConsigneeSortSchema,
    order: sortOrderSchema,
  })
  .merge(paginationSchema)

// Type exports
export type CustomerConsigneeForm = z.infer<typeof customerConsigneeFormSchema>
export type CustomerConsigneeUpdate = z.infer<typeof customerConsigneeUpdateSchema>
export type CustomerConsigneeFilter = z.infer<typeof customerConsigneeFilterSchema>
export type CustomerConsigneeBulkImport = z.infer<
  typeof customerConsigneeBulkImportSchema
>
export type CustomerConsigneeSort = z.infer<typeof customerConsigneeSortSchema>
export type CustomerConsigneeQuery = z.infer<typeof customerConsigneeQuerySchema>

// Validation functions
export function validateCustomerConsigneeForm(
  data: unknown
): CustomerConsigneeForm {
  return customerConsigneeFormSchema.parse(data)
}

export function validateCustomerConsigneeFilter(
  data: unknown
): CustomerConsigneeFilter {
  return customerConsigneeFilterSchema.parse(data)
}

export function validateBulkImportData(
  data: unknown[]
): CustomerConsigneeBulkImport[] {
  return customerConsigneeBulkImportArraySchema.parse(data)
}

// Helper function to validate CSV headers
export function validateCSVHeaders(headers: string[]): boolean {
  const requiredHeaders = ['customer_name', 'consignee_name']
  const optionalHeaders = ['is_default', 'notes']
  const allowedHeaders = [...requiredHeaders, ...optionalHeaders]

  // Check if all required headers are present
  const hasAllRequired = requiredHeaders.every(header =>
    headers.some(h => h.toLowerCase().trim() === header.toLowerCase())
  )

  // Check if all headers are valid
  const hasOnlyValid = headers.every(header =>
    allowedHeaders.some(
      allowed => allowed.toLowerCase() === header.toLowerCase().trim()
    )
  )

  return hasAllRequired && hasOnlyValid
}

// Error messages
export const CustomerConsigneeErrorMessages = {
  DUPLICATE_RELATIONSHIP: 'This customer-consignee relationship already exists',
  CUSTOMER_NOT_FOUND:
    'Selected customer company not found or is not a customer type',
  CONSIGNEE_NOT_FOUND:
    'Selected consignee company not found or is not a consignee type',
  INVALID_COMPANY_TYPE: 'Invalid company type for this relationship',
  SAME_COMPANY: 'Customer and consignee cannot be the same company',
  DEFAULT_CONFLICT:
    'Another relationship is already set as default for this customer',
  BULK_IMPORT_INVALID: 'Invalid bulk import data format',
  CSV_HEADERS_INVALID:
    'CSV file must contain valid headers: customer_name, consignee_name, and optionally is_default, notes',
} as const