import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { StatusUpdateForm } from '../status-update-form'
import type { ShipmentStatus } from '@/lib/supabase/types'

// Mock the hooks
jest.mock('@/hooks/use-auth', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' },
    profile: { role: 'cs' },
    isStaff: true,
  }),
}))

describe('StatusUpdateForm', () => {
  const mockOnSubmit = jest.fn()
  const mockOnCancel = jest.fn()
  
  const defaultProps = {
    currentStatus: 'booking_confirmed' as ShipmentStatus,
    onSubmit: mockOnSubmit,
    shipment_id: 'test-shipment-id',
    isSubmitting: false,
    onCancel: mockOnCancel,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the form with current status and next valid statuses', () => {
    render(<StatusUpdateForm {...defaultProps} />)
    
    expect(screen.getByText('Update Status')).toBeInTheDocument()
    expect(screen.getByText('Current:')).toBeInTheDocument()
    expect(screen.getByText('Booking Confirmed')).toBeInTheDocument()
    expect(screen.getByLabelText(/New Status/)).toBeInTheDocument()
  })

  it('shows valid status transitions in the dropdown', async () => {
    const user = userEvent.setup()
    render(<StatusUpdateForm {...defaultProps} />)
    
    // Click on the status dropdown
    await user.click(screen.getByRole('combobox'))
    
    // Should show valid next statuses for booking_confirmed
    expect(screen.getByText('Transport Assigned')).toBeInTheDocument()
    expect(screen.getByText('Cancelled')).toBeInTheDocument()
  })

  it('handles form submission correctly', async () => {
    const user = userEvent.setup()
    render(<StatusUpdateForm {...defaultProps} />)
    
    // Select a new status
    await user.click(screen.getByRole('combobox'))
    await user.click(screen.getByText('Transport Assigned'))
    
    // Fill in location
    await user.type(screen.getByPlaceholderText(/e.g., Bangkok Port/), 'Test Location')
    
    // Fill in notes
    await user.type(screen.getByPlaceholderText(/Add any additional notes/), 'Test notes')
    
    // Submit the form
    await user.click(screen.getByRole('button', { name: /Update Status/ }))
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        status_to: 'transport_assigned',
        location: 'Test Location',
        notes: 'Test notes',
        shipment_id: 'test-shipment-id',
        coordinates: undefined,
      })
    })
  })

  it('shows loading state when submitting', () => {
    render(<StatusUpdateForm {...defaultProps} isSubmitting={true} />)
    
    expect(screen.getByText('Updating...')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Updating.../ })).toBeDisabled()
  })

  it('handles cancel action', async () => {
    const user = userEvent.setup()
    render(<StatusUpdateForm {...defaultProps} />)
    
    await user.click(screen.getByRole('button', { name: /Cancel/ }))
    
    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('shows terminal status message when no valid transitions exist', () => {
    render(<StatusUpdateForm {...defaultProps} currentStatus="completed" />)
    
    expect(screen.getByText('No Status Updates Available')).toBeInTheDocument()
    expect(screen.getByText(/terminal state/)).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<StatusUpdateForm {...defaultProps} />)
    
    // Try to submit without making changes
    const submitButton = screen.getByRole('button', { name: /Update Status/ })
    expect(submitButton).toBeDisabled() // Should be disabled when form is not dirty
  })
})