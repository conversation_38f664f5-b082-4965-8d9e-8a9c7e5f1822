# DYY Trading Management System - Frontend UX Specification

## Document Overview

This document defines the comprehensive user experience goals, design system, information architecture, user flows, and visual design specifications for the DYY Trading Fruit Export Management System's user interface. It serves as the foundation for visual design and frontend development, ensuring a cohesive, efficient, and user-centered experience that supports the logistics industry's unique requirements.

**Version:** 1.0  
**Date:** 2024-12-XX  
**Author:** UX Expert - Sally  
**Target Framework:** ShadCN UI + Next.js 14+ with Tailwind CSS  

---

## Overall UX Goals & Principles

### Primary UX Vision

Create a modern, logistics-optimized interface with dark blue professional theme that prioritizes efficiency for export operations staff while providing intuitive mobile experiences for field workers. The design emphasizes rapid data entry through intelligent pre-population, clear visual hierarchy with strategic orange accent highlights, and seamless workflows that achieve the target 50% reduction in manual coordination time.

### Core UX Principles

1. **Efficiency Through Intelligence**
   - Cascading selection workflows with pre-population
   - 60% reduction in data entry through relationship automation
   - Context-aware interfaces that anticipate user needs

2. **Mobile-First Operations**
   - Touch-optimized interfaces (44px minimum touch targets)
   - Offline-capable Progressive Web App for drivers
   - Responsive design across desktop, tablet, and mobile

3. **Role-Based Clarity**
   - 11 distinct user types with tailored interfaces
   - Progressive disclosure of complexity based on user role
   - Clear visual hierarchy for decision-making speed

4. **Professional Logistics Aesthetic**
   - Dark blue theme optimized for extended use
   - High contrast ratios for outdoor/mobile use
   - Orange accents for critical actions and status

5. **Accessibility & Inclusion**
   - WCAG 2.1 AA compliance minimum
   - Keyboard navigation support
   - Bilingual interface (Thai/English)

### Success Metrics

- **Coordination Time Reduction:** 50% decrease in manual coordination workflows
- **Data Entry Efficiency:** 60% reduction through intelligent pre-population
- **Mobile Performance:** Sub-2 second load times on 3G networks
- **Status Visibility:** 90% real-time status tracking across shipments
- **Document Accuracy:** 95% automated generation accuracy

---

## User Personas & Use Cases

### Primary Users (Internal Operations)

#### 1. CS Representative - Sarah Chen
**Role:** Customer Service Manager  
**Primary Goals:** Efficient shipment creation, stakeholder coordination, status tracking  
**Device Usage:** Desktop/laptop primary, tablet occasional  
**Technical Comfort:** High  
**Key Workflows:** 
- Create shipments with cascading customer→shipper/product selection
- Track shipment progress across multiple transportation modes
- Generate and distribute export documentation
- Coordinate with drivers and external stakeholders

**Pain Points Addressed:**
- Manual data entry through relationship pre-population
- Status coordination through real-time updates
- Document preparation through automated generation

#### 2. Admin Operations - Michael Wang
**Role:** System Administrator  
**Primary Goals:** Master data management, user account control, system configuration  
**Device Usage:** Desktop primary  
**Technical Comfort:** Very High  
**Key Workflows:**
- Manage master data (companies, products, ports, relationships)
- Configure user roles and permissions
- Monitor system performance and analytics
- Set up notification preferences and templates

### External Stakeholders (Mobile-First)

#### 3. Driver - Somchai Tanakit
**Role:** Transportation Driver  
**Primary Goals:** Receive assignments, update status, capture proof photos  
**Device Usage:** Mobile phone only  
**Technical Comfort:** Medium  
**Key Workflows:**
- View assigned shipments on mobile dashboard
- Update status with mandatory photo uploads
- Capture GPS location for tracking
- Work offline when network unavailable

**Critical UX Requirements:**
- Large touch targets (44px minimum)
- Simple, linear workflows
- Clear visual feedback for actions
- Offline capability with sync

#### 4. Customer Portal User - Lisa Morrison
**Role:** Export Customer  
**Primary Goals:** Track shipments, access documents, receive notifications  
**Device Usage:** Desktop, tablet, mobile  
**Technical Comfort:** Medium  
**Key Workflows:**
- Monitor shipment status in real-time
- Download export documents
- Receive status notifications via preferred channels
- Review shipment history and analytics

---

## Information Architecture

### Navigation Hierarchy

```
DYY Trading Management
├── Dashboard (Role-specific landing)
├── Shipments
│   ├── Create New Shipment
│   ├── Active Shipments
│   ├── Shipment History
│   └── Status Updates
├── Master Data (Admin/CS only)
│   ├── Customer Management
│   ├── Factory Management
│   ├── Shipper Management
│   ├── Consignee Management
│   ├── Notify Party Management
│   ├── Forwarder Agents Management
│   ├── Carrier Management
│   ├── Driver Management
│   ├── Products
│   ├── Ports
│   └── Relationship Configuration
├── Documents
│   ├── Generate Documents
│   ├── Document Library
│   └── Templates (Admin only)
├── Communications
│   ├── Notification Center
│   ├── Channel Preferences
│   └── Message Templates (Admin only)
└── Account & Settings
    ├── User Profile
    ├── Preferences
    └── System Settings (Admin only)
```

### Mobile Navigation (Driver Interface)

```
Driver Mobile App
├── My Assignments (Dashboard)
├── Update Status
├── Photo Gallery
├── Work History
└── Profile & Settings
```

### Content Organization Principles

1. **Role-Based Information Hierarchy**
   - Most critical information at top level for each user type
   - Progressive disclosure prevents information overload
   - Context-sensitive navigation shows relevant sections only

2. **Task-Oriented Grouping**
   - Shipment lifecycle as primary organizing principle
   - Master data grouped by functional relationships
   - Communication tools consolidated for efficiency

3. **Mobile-First Information Prioritization**
   - Essential actions accessible within 2 taps on mobile
   - Status information prominently displayed
   - Secondary information available through clear navigation

---

## Key User Flows

### Flow 1: Intelligent Shipment Creation (CS Representative)

**Objective:** Create new shipment with minimal data entry through cascading selection and automatic container generation

**Flow Steps:**
1. **Transportation Mode Selection (Pre-Creation)**
   - Transportation mode selection modal before entering creation form
   - Options: Sea, Land, Rail
   - Each mode adjusts available fields and workflow requirements
   - Confirmation leads to main creation form

2. **Customer & Factory Selection**
   - Type-ahead search with customer suggestions
   - Customer selection triggers automatic loading of:
     - Associated shippers (default pre-selected)
     - Associated products with pricing (default pre-selected)
     - Previous shipment patterns for auto-completion
   - **Mandatory Factory Selection**
     - Factory dropdown (required field)
     - Factory-specific information and requirements

3. **Shipper & Product Refinement**
   - Cascaded shipper dropdown (pre-filtered to customer's associates)
   - Product selection shows customer-specific pricing (CIF/FOB per KG)
   - Packaging specifications auto-populate from customer-product relationships

4. **Forwarder Agent & Documentation**
   - **Mandatory Forwarder Agent Selection**
     - Forwarder agent dropdown (required field)
     - Agent contact information and service details
   - **Required Date & Time Information**
     - ETD (Estimated Time of Departure) date entry (required)
     - ETA (Estimated Time of Arrival) date entry (required)
     - Closing Time entry (required)
   - **Booking Confirm Document Upload**
     - File upload interface for booking confirmation
     - Document validation and storage
     - Preview and replacement capabilities

5. **Consignee & Notification Setup**
   - Consignee selection triggers automatic loading of:
     - Associated notify parties (default pre-selected)
     - Communication preferences by channel
     - Special handling instructions

6. **Shipment Details Completion**
   - **Mandatory Destination Port** - User must select destination port (required field)
   - Origin port information with suggestions based on customer history
   - **Optional Notes Field** - User can enter additional notes or special instructions
   - Document generation preferences
   - **No Container Entry Required** - System generates dummy containers automatically upon save
   - **Automatic Shipment Number Generation** - System generates shipment number upon save with format: EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running]
     - Transportation Mode Code: 1 (Land), 2 (Rail), 3 (Sea)
     - Running number based on Transportation Mode and Port, resets to 1 every month

**Key UX Elements:**
- **Transportation Mode Modal:** Clear selection interface before form entry (Sea/Land/Rail)
- **Factory Selection:** Mandatory field with location and capacity information  
- **Forwarder Agent Selection:** Required with contact details and service offerings
- **ETD/ETA/Closing Time:** Required date and time fields for logistics coordination
- **Destination Port:** Mandatory port selection for shipment routing
- **Document Upload:** Drag-and-drop interface with preview and validation
- **Notes Field:** Optional text area for additional instructions or comments
- **Automatic Shipment Number:** System generates unique number with transportation mode, port, and date
- **Automatic Container Generation:** Clear indication that containers are auto-created
- Real-time form validation with clear error messaging
- Progress indicators showing completion status
- Save-as-draft functionality for complex shipments
- Confirmation screen with summary before submission

### Flow 2: Mobile Driver Status Update

**Objective:** Update shipment status with photo documentation on mobile device

**Flow Steps:**
1. **Assignment Dashboard**
   - Swipe-based navigation through assigned shipments
   - Status cards with clear visual hierarchy
   - GPS-based sorting (nearest first)

2. **Status Update Selection**
   - Current status clearly displayed
   - Available next statuses as large touch targets
   - Status transition rules enforced automatically

3. **Photo Documentation**
   - Camera interface with guidelines overlay
   - Multiple photo capture (up to 5 per update)
   - Automatic GPS coordinate capture
   - Thumbnail preview with retake option

4. **Confirmation & Sync**
   - Review screen with all captured information
   - Offline queue indication if network unavailable
   - Success feedback with next assignment suggestion

**Key UX Elements:**
- Gesture-based navigation optimized for one-handed use
- High contrast interface for outdoor visibility
- Immediate feedback for all user actions
- Graceful offline/online state management

### Flow 3: Cascading Product Selection (Customer Portal)

**Objective:** Enable customers to easily track products across shipments

**Flow Steps:**
1. **Product-Based Navigation**
   - Product categories with visual indicators
   - Search with auto-complete for product codes
   - Filter by shipment status and date ranges

2. **Shipment Association Discovery**
   - Products linked to active shipments
   - Status progression visualization
   - Document availability indicators

3. **Real-Time Status Monitoring**
   - Live status updates via websocket connection
   - Notification preferences easily accessible
   - Historical tracking with trend analysis

**Key UX Elements:**
- Responsive design adapting to device capabilities
- Rich data visualization for complex logistics information
- Contextual help and onboarding flows

### Flow 4: Shipment Viewing and Editing (CS Representative)

**Objective:** Enable CS representatives to efficiently search, view, and edit existing shipments with comprehensive management capabilities

**Flow Steps:**
1. **Shipment Search and Discovery**
   - Advanced search interface with multiple filter criteria
   - Search by shipment number, customer, container number, or invoice number
   - Filter by status, transportation mode, date ranges, and destination
   - Saved search preferences for frequently used filters
   - Real-time search results with pagination and sorting

2. **Shipment List View with Quick Actions**
   - Condensed shipment cards showing key information
   - Status indicators with color coding and progress visualization
   - Quick action buttons for common operations (view, edit, duplicate)
   - Bulk operations for multiple shipment management
   - Export functionality for reporting and analysis

3. **Comprehensive Shipment Detail View**
   - Tabbed interface organizing information by category:
     - **Overview:** Key details, status timeline, stakeholder summary
     - **Logistics:** Transportation details, ports, dates, container information
     - **Products:** Product details, quantities, pricing, specifications
     - **Documents:** Generated documents, uploaded files, document history
     - **Communication:** Notification history, stakeholder messages, notes
     - **Audit Trail:** Complete change history with user attribution

4. **Inline Editing with Smart Validation**
   - Edit mode toggle for individual sections or entire shipment
   - Real-time validation maintaining business rule compliance
   - Field-level permissions based on shipment status and user role
   - Unsaved changes indicator with auto-save functionality
   - Conflict resolution for concurrent editing scenarios

5. **Container Management Post-Creation**
   - Container details editing (type, size, seal numbers, weights)
   - Product allocation adjustments between containers
   - Container addition/removal with automatic weight recalculation
   - Container status tracking and photo documentation
   - Seal verification and security management

6. **Status Management and Progression**
   - Status update interface with dropdown of valid next statuses
   - Mandatory fields for specific status transitions
   - Automatic stakeholder notifications on status changes
   - Status rollback capability with approval workflow
   - Custom status notes and location information

7. **Stakeholder Communication Hub**
   - Integrated messaging interface for shipment-specific communication
   - Contact information display for all shipment stakeholders
   - Communication history timeline with message threading
   - Quick notification sending with template selection
   - Stakeholder preference management and channel selection

**Key UX Elements:**
- **Comprehensive Search:** Advanced filtering with saved preferences and real-time results
- **Tabbed Information Architecture:** Organized data presentation preventing information overload
- **Contextual Editing:** Inline editing with smart validation and permission awareness
- **Visual Status Tracking:** Clear progress indicators and timeline visualization
- **Integrated Communication:** Seamless stakeholder coordination within shipment context
- **Audit Transparency:** Complete change history with user attribution and timestamps
- **Responsive Design:** Optimized for desktop primary use with tablet compatibility
- **Keyboard Shortcuts:** Power user efficiency with keyboard navigation support

---

## Design System Implementation

### Color Palette (Dark Blue Logistics Theme)

#### Primary Colors
```css
/* Core Dark Blues */
--primary-900: #0f172a    /* Navy Blue - main backgrounds */
--primary-800: #1e293b    /* Dark Blue - cards, panels */
--primary-700: #334155    /* Slate Blue - borders, dividers */

/* Orange Accents */
--accent-500: #f97316     /* Primary Orange - CTAs, highlights */
--accent-400: #fb923c     /* Light Orange - hover states */
--accent-600: #ea580c     /* Dark Orange - active states */

/* Supporting Colors */
--neutral-50: #f8fafc     /* Light text on dark backgrounds */
--neutral-200: #e2e8f0    /* Secondary text */
--neutral-500: #64748b    /* Muted text */
--neutral-800: #1e293b    /* Text on light backgrounds */
```

#### Status Colors
```css
/* Status Indicators */
--success-500: #10b981    /* Completed, successful operations */
--warning-500: #f59e0b    /* In progress, attention needed */
--error-500: #ef4444      /* Failed, critical issues */
--info-500: #3b82f6      /* Information, pending states */
```

#### Accessibility Compliance
- **Contrast Ratios:** All color combinations meet WCAG 2.1 AA (4.5:1 minimum)
- **Primary on Navy:** #f8fafc on #0f172a = 15.5:1 ✅
- **Orange on Dark Blue:** #f97316 on #1e293b = 6.2:1 ✅
- **Secondary Text:** #e2e8f0 on #334155 = 8.1:1 ✅

### Typography System

#### Font Families
```css
/* Primary Interface Font */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

/* Monospace for IDs/Codes */
--font-mono: 'Roboto Mono', 'SF Mono', Monaco, monospace;

/* Regional Support */
--font-thai: 'Noto Sans Thai', 'Inter', sans-serif;
```

#### Type Scale
```css
/* Headings */
--text-h1: 2.25rem; font-weight: 700; line-height: 1.2; /* 36px */
--text-h2: 1.875rem; font-weight: 600; line-height: 1.3; /* 30px */
--text-h3: 1.5rem; font-weight: 600; line-height: 1.4; /* 24px */
--text-h4: 1.25rem; font-weight: 500; line-height: 1.4; /* 20px */

/* Body Text */
--text-lg: 1.125rem; font-weight: 400; line-height: 1.6; /* 18px */
--text-base: 1rem; font-weight: 400; line-height: 1.5; /* 16px */
--text-sm: 0.875rem; font-weight: 400; line-height: 1.4; /* 14px */
--text-xs: 0.75rem; font-weight: 400; line-height: 1.3; /* 12px */

/* Specialized */
--text-mono: 0.875rem; font-family: var(--font-mono); /* Tracking numbers */
--text-button: 0.875rem; font-weight: 500; /* Button labels */
```

### Spacing & Layout System

#### Spacing Scale (8px base unit)
```css
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-3: 0.75rem;  /* 12px */
--space-4: 1rem;     /* 16px */
--space-5: 1.25rem;  /* 20px */
--space-6: 1.5rem;   /* 24px */
--space-8: 2rem;     /* 32px */
--space-10: 2.5rem;  /* 40px */
--space-12: 3rem;    /* 48px */
--space-16: 4rem;    /* 64px */
--space-20: 5rem;    /* 80px */
```

#### Layout Containers
```css
/* Page Containers */
--container-sm: 640px;   /* Mobile landscape */
--container-md: 768px;   /* Tablet */
--container-lg: 1024px;  /* Desktop */
--container-xl: 1280px;  /* Large desktop */
--container-2xl: 1400px; /* Logistics dashboards max-width */

/* Component Constraints */
--content-max: 65ch;     /* Readable text line length */
--form-max: 480px;       /* Optimal form width */
--card-max: 400px;       /* Card component maximum */
```

### Component Library (ShadCN UI Customization)

#### Button System
```tsx
// Primary Action Button (Orange)
<Button className="bg-accent-500 hover:bg-accent-400 text-white font-medium">
  Create Shipment
</Button>

// Secondary Button (Dark Blue)
<Button variant="secondary" className="bg-primary-800 hover:bg-primary-700 text-neutral-50">
  View Details
</Button>

// Destructive Action
<Button variant="destructive" className="bg-error-500 hover:bg-error-600">
  Delete Shipment
</Button>

// Mobile Touch Target (44px minimum)
<Button className="min-h-[44px] min-w-[44px] text-lg" size="lg">
  Update Status
</Button>
```

#### Form Components
```tsx
// Cascading Select with Pre-population
<Select onValueChange={handleCustomerChange}>
  <SelectTrigger className="bg-primary-800 border-primary-700">
    <SelectValue placeholder="Select Customer" />
  </SelectTrigger>
  <SelectContent>
    {customers.map(customer => (
      <SelectItem value={customer.id}>{customer.name}</SelectItem>
    ))}
  </SelectContent>
</Select>

// Auto-populated dependent select
<Select disabled={!selectedCustomer} value={defaultShipper}>
  <SelectTrigger className="bg-primary-800 border-primary-700">
    <SelectValue placeholder="Shipper (auto-loaded)" />
  </SelectTrigger>
</Select>
```

#### Card Components
```tsx
// Status Card for Dashboard
<Card className="bg-primary-800 border-primary-700">
  <CardHeader className="pb-3">
    <div className="flex items-center justify-between">
      <CardTitle className="text-neutral-50">Shipment #SH-2024-001</CardTitle>
      <Badge variant="outline" className="bg-warning-500 text-black">
        In Transit
      </Badge>
    </div>
  </CardHeader>
  <CardContent>
    <div className="space-y-2 text-neutral-200">
      <p>Customer: ABC Trading Co.</p>
      <p>Destination: Bangkok Port</p>
      <p>ETA: Dec 15, 2024</p>
    </div>
  </CardContent>
</Card>
```

#### Navigation Components
```tsx
// Main Navigation
<nav className="bg-primary-900 border-b border-primary-700">
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div className="flex justify-between h-16">
      <div className="flex space-x-8">
        <NavigationMenuItem active={pathname === '/dashboard'}>
          Dashboard
        </NavigationMenuItem>
        <NavigationMenuItem active={pathname.startsWith('/shipments')}>
          Shipments
        </NavigationMenuItem>
      </div>
    </div>
  </div>
</nav>

// Mobile Tab Navigation
<div className="fixed bottom-0 left-0 right-0 bg-primary-900 border-t border-primary-700">
  <div className="grid grid-cols-4 h-16">
    <TabButton icon={<HomeIcon />} label="Dashboard" active />
    <TabButton icon={<TruckIcon />} label="Assignments" />
    <TabButton icon={<CameraIcon />} label="Update" />
    <TabButton icon={<HistoryIcon />} label="History" />
  </div>
</div>
```

---

## Interface Specifications

### Admin Dashboard Layout

#### Header Section
```tsx
<header className="bg-primary-900 border-b border-primary-700 px-6 py-4">
  <div className="flex items-center justify-between max-w-2xl mx-auto">
    <div className="flex items-center space-x-4">
      <img src="/logo.svg" alt="DYY Trading" className="h-8 w-auto" />
      <h1 className="text-xl font-semibold text-neutral-50">
        Admin Dashboard
      </h1>
    </div>
    <div className="flex items-center space-x-4">
      <NotificationButton />
      <UserMenu />
    </div>
  </div>
</header>
```

#### Main Content Grid
```tsx
<main className="max-w-2xl mx-auto px-6 py-8">
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    
    {/* Quick Stats Cards */}
    <StatCard 
      title="Active Shipments" 
      value="127" 
      trend="+12%" 
      icon={<ShipIcon />}
      color="info"
    />
    <StatCard 
      title="Pending Documents" 
      value="8" 
      trend="-2" 
      icon={<DocumentIcon />}
      color="warning"
    />
    <StatCard 
      title="Drivers Online" 
      value="23" 
      trend="+5" 
      icon={<TruckIcon />}
      color="success"
    />
    
    {/* Master Data Management */}
    <Card className="md:col-span-2 lg:col-span-3">
      <CardHeader>
        <CardTitle>Master Data Management</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <MasterDataButton 
            icon={<BuildingIcon />}
            label="Companies"
            count="145"
            href="/master-data/companies"
          />
          <MasterDataButton 
            icon={<PackageIcon />}
            label="Products"
            count="89"
            href="/master-data/products"
          />
          <MasterDataButton 
            icon={<AnchorIcon />}
            label="Ports"
            count="56"
            href="/master-data/ports"
          />
          <MasterDataButton 
            icon={<UserIcon />}
            label="Drivers"
            count="34"
            href="/master-data/drivers"
          />
        </div>
      </CardContent>
    </Card>
    
    {/* Recent Activity */}
    <Card className="md:col-span-2 lg:col-span-2">
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <ActivityFeed items={recentActivity} />
      </CardContent>
    </Card>
    
    {/* System Status */}
    <Card>
      <CardHeader>
        <CardTitle>System Status</CardTitle>
      </CardHeader>
      <CardContent>
        <SystemStatusIndicators />
      </CardContent>
    </Card>
    
  </div>
</main>
```

### CS Shipment Management Interface

#### Transportation Mode Selection Modal (Pre-Creation)
```tsx
{/* Transportation Mode Selection Modal */}
<Dialog open={transportModeModalOpen} onOpenChange={setTransportModeModalOpen}>
  <DialogContent className="bg-primary-800 border-primary-700 max-w-md">
    <DialogHeader>
      <DialogTitle className="text-neutral-50">Select Transportation Mode</DialogTitle>
      <DialogDescription className="text-neutral-300">
        Choose the transportation mode for this shipment. This will configure the appropriate workflow and required fields.
      </DialogDescription>
    </DialogHeader>
    
    <div className="space-y-3 py-4">
      {transportModes.map(mode => (
        <Button
          key={mode.value}
          variant={selectedTransportMode === mode.value ? "default" : "secondary"}
          className={`w-full justify-start p-4 h-auto ${
            selectedTransportMode === mode.value 
              ? 'bg-accent-500 hover:bg-accent-400' 
              : 'bg-primary-700 hover:bg-primary-600'
          }`}
          onClick={() => setSelectedTransportMode(mode.value)}
        >
          <div className="text-left">
            <div className="flex items-center space-x-2">
              <mode.icon className="w-5 h-5" />
              <span className="font-medium">{mode.label}</span>
            </div>
            <div className="text-xs opacity-75 mt-1">{mode.description}</div>
            <div className="text-xs opacity-60 mt-1">
              Required fields: {mode.requiredFields.join(', ')}
            </div>
          </div>
        </Button>
      ))}
    </div>
    
    <DialogFooter>
      <Button
        variant="secondary"
        onClick={() => setTransportModeModalOpen(false)}
        className="bg-primary-700 hover:bg-primary-600"
      >
        Cancel
      </Button>
      <Button
        onClick={handleTransportModeConfirm}
        disabled={!selectedTransportMode}
        className="bg-accent-500 hover:bg-accent-400"
      >
        Continue to Create Shipment
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

#### Shipment Creation Form with Cascading Selection
```tsx
<form className="space-y-6 max-w-4xl mx-auto p-6">
  
  {/* Section 1: Customer & Factory Information */}
  <Card>
    <CardHeader>
      <CardTitle>Customer & Factory Information</CardTitle>
      <CardDescription>
        Selecting a customer will automatically load associated shippers and products
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      
      {/* Customer and Factory Selection */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="customer">Customer *</Label>
          <Select onValueChange={handleCustomerSelection}>
            <SelectTrigger>
              <SelectValue placeholder="Search and select customer..." />
            </SelectTrigger>
            <SelectContent>
              {customers.map(customer => (
                <SelectItem key={customer.id} value={customer.id}>
                  <div className="flex items-center space-x-2">
                    <span>{customer.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {customer.activeShipments} active
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Mandatory Factory Selection */}
        <div>
          <Label htmlFor="factory">Factory *</Label>
          <Select onValueChange={setSelectedFactory} required>
            <SelectTrigger>
              <SelectValue placeholder="Select factory..." />
            </SelectTrigger>
            <SelectContent>
              {factories.map(factory => (
                <SelectItem key={factory.id} value={factory.id}>
                  <div>
                    <div className="font-medium">{factory.name}</div>
                    <div className="text-xs text-neutral-400">
                      {factory.location} • {factory.capacity}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Auto-populated Shipper */}
        <div>
          <Label htmlFor="shipper">Shipper (auto-loaded) *</Label>
          <Select 
            value={defaultShipper} 
            onValueChange={setSelectedShipper}
            disabled={!selectedCustomer}
          >
            <SelectTrigger className={!selectedCustomer ? 'opacity-50' : ''}>
              <SelectValue placeholder="Select shipper..." />
            </SelectTrigger>
            <SelectContent>
              {customerShippers.map(shipper => (
                <SelectItem key={shipper.id} value={shipper.id}>
                  <div className="flex items-center justify-between w-full">
                    <span>{shipper.name}</span>
                    {shipper.isDefault && (
                      <Badge className="bg-accent-500 text-xs">Default</Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Auto-populated Products with Pricing */}
      <div>
        <Label>Products (auto-loaded with pricing)</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          {customerProducts.map(product => (
            <Card key={product.id} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{product.name}</h4>
                  <p className="text-sm text-neutral-400">
                    {product.packaging} • {product.weightPerPackage}kg
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-mono text-sm">
                    {product.priceType}: ${product.pricePerKg}/kg
                  </p>
                  <Checkbox 
                    checked={product.isDefault}
                    onCheckedChange={() => toggleProductSelection(product.id)}
                  />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
      
    </CardContent>
  </Card>
  
  {/* Section 2: Forwarder Agent & Documentation */}
  <Card>
    <CardHeader>
      <CardTitle>Forwarder Agent & Booking Documentation</CardTitle>
      <CardDescription>
        Select forwarder agent and upload booking confirmation document
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Mandatory Forwarder Agent Selection */}
        <div>
          <Label htmlFor="forwarderAgent">Forwarder Agent *</Label>
          <Select onValueChange={setSelectedForwarderAgent} required>
            <SelectTrigger>
              <SelectValue placeholder="Select forwarder agent..." />
            </SelectTrigger>
            <SelectContent>
              {forwarderAgents.map(agent => (
                <SelectItem key={agent.id} value={agent.id}>
                  <div>
                    <div className="font-medium">{agent.name}</div>
                    <div className="text-xs text-neutral-400">
                      {agent.location} • {agent.services.join(', ')}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* Display selected agent details */}
          {selectedForwarderAgent && (
            <div className="mt-2 p-3 bg-primary-900 rounded-lg border border-primary-600">
              <div className="text-sm">
                <p className="text-neutral-200 font-medium">{selectedAgent.name}</p>
                <p className="text-neutral-400 text-xs">Contact: {selectedAgent.contact}</p>
                <p className="text-neutral-400 text-xs">Email: {selectedAgent.email}</p>
                <p className="text-neutral-400 text-xs">Services: {selectedAgent.services.join(', ')}</p>
              </div>
            </div>
          )}
        </div>
        
        {/* Booking Confirm Document Upload */}
        <div>
          <Label htmlFor="bookingDocument">Booking Confirmation Document *</Label>
          <div className="mt-2">
            <div className="border-2 border-dashed border-primary-600 rounded-lg p-4 text-center">
              <input
                type="file"
                id="bookingDocument"
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                onChange={handleBookingDocumentUpload}
                className="hidden"
                required
              />
              <label 
                htmlFor="bookingDocument" 
                className="cursor-pointer block"
              >
                <UploadIcon className="w-8 h-8 mx-auto text-neutral-400 mb-2" />
                <p className="text-sm text-neutral-300 mb-1">
                  Click to upload booking confirmation
                </p>
                <p className="text-xs text-neutral-400">
                  Supports PDF, JPG, PNG, DOC (max 10MB)
                </p>
              </label>
            </div>
            
            {/* Display uploaded document */}
            {bookingDocument && (
              <div className="mt-3 p-3 bg-primary-900 rounded-lg border border-primary-600">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <DocumentIcon className="w-4 h-4 text-accent-500" />
                    <div>
                      <p className="text-sm text-neutral-200">{bookingDocument.name}</p>
                      <p className="text-xs text-neutral-400">
                        {(bookingDocument.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button 
                      size="sm" 
                      variant="secondary"
                      onClick={() => previewDocument(bookingDocument)}
                      className="bg-primary-700 hover:bg-primary-600"
                    >
                      <EyeIcon className="w-3 h-3" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => removeBookingDocument()}
                    >
                      <XIcon className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Required Date & Time Information */}
      <div>
        <h4 className="text-sm font-medium text-neutral-50 mb-3">
          Logistics Schedule Information *
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          
          {/* ETD - Estimated Time of Departure */}
          <div>
            <Label htmlFor="etdDate">ETD (Estimated Time of Departure) *</Label>
            <Input
              type="datetime-local"
              id="etdDate"
              value={etdDate}
              onChange={(e) => setEtdDate(e.target.value)}
              className="bg-primary-900 border-primary-600 text-neutral-50"
              required
            />
            <p className="text-xs text-neutral-400 mt-1">
              When shipment is expected to depart from origin
            </p>
          </div>
          
          {/* ETA - Estimated Time of Arrival */}
          <div>
            <Label htmlFor="etaDate">ETA (Estimated Time of Arrival) *</Label>
            <Input
              type="datetime-local"
              id="etaDate"
              value={etaDate}
              onChange={(e) => setEtaDate(e.target.value)}
              className="bg-primary-900 border-primary-600 text-neutral-50"
              required
              min={etdDate} // ETA cannot be before ETD
            />
            <p className="text-xs text-neutral-400 mt-1">
              When shipment is expected to arrive at destination
            </p>
          </div>
          
          {/* Closing Time */}
          <div>
            <Label htmlFor="closingTime">Closing Time *</Label>
            <Input
              type="datetime-local"
              id="closingTime"
              value={closingTime}
              onChange={(e) => setClosingTime(e.target.value)}
              className="bg-primary-900 border-primary-600 text-neutral-50"
              required
              max={etdDate} // Closing time must be before ETD
            />
            <p className="text-xs text-neutral-400 mt-1">
              Last time to submit cargo for this shipment
            </p>
          </div>
          
        </div>
        
        {/* Date validation feedback */}
        {(etdDate && etaDate && etdDate >= etaDate) && (
          <Alert className="bg-warning-500/10 border-warning-500 mt-3">
            <AlertTriangleIcon className="w-4 h-4" />
            <AlertTitle>Date Validation</AlertTitle>
            <AlertDescription>
              ETA date must be after ETD date. Please adjust the dates accordingly.
            </AlertDescription>
          </Alert>
        )}
        
        {(closingTime && etdDate && closingTime >= etdDate) && (
          <Alert className="bg-warning-500/10 border-warning-500 mt-3">
            <AlertTriangleIcon className="w-4 h-4" />
            <AlertTitle>Date Validation</AlertTitle>
            <AlertDescription>
              Closing time must be before ETD. Please adjust the times accordingly.
            </AlertDescription>
          </Alert>
        )}
      </div>
      
    </CardContent>
  </Card>
  
  {/* Section 3: Consignee & Notify Party Cascading */}
  <Card>
    <CardHeader>
      <CardTitle>Destination Information</CardTitle>
      <CardDescription>
        Consignee selection will auto-load associated notify parties
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Consignee Selection - Triggers notify party loading */}
        <div>
          <Label htmlFor="consignee">Consignee *</Label>
          <Select onValueChange={handleConsigneeSelection}>
            <SelectTrigger>
              <SelectValue placeholder="Select consignee..." />
            </SelectTrigger>
            <SelectContent>
              {consignees.map(consignee => (
                <SelectItem key={consignee.id} value={consignee.id}>
                  <div>
                    <div className="font-medium">{consignee.name}</div>
                    <div className="text-xs text-neutral-400">
                      {consignee.city}, {consignee.country}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Auto-populated Notify Parties */}
        <div>
          <Label>Notify Parties (auto-loaded)</Label>
          <div className="space-y-2 mt-2">
            {consigneeNotifyParties.map(notifyParty => (
              <div key={notifyParty.id} className="flex items-center space-x-2">
                <Checkbox 
                  checked={notifyParty.isDefault}
                  onCheckedChange={() => toggleNotifyParty(notifyParty.id)}
                />
                <div className="flex-1">
                  <span className="text-sm">{notifyParty.name}</span>
                  <div className="text-xs text-neutral-400">
                    {notifyParty.preferredChannels.join(', ')}
                  </div>
                </div>
                {notifyParty.isDefault && (
                  <Badge className="bg-accent-500 text-xs">Default</Badge>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
      
    </CardContent>
  </Card>
  
  {/* Section 3: Transportation & Logistics */}
  <Card>
    <CardHeader>
      <CardTitle>Transportation Details</CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      
      {/* Transportation Mode Selection */}
      <div>
        <Label>Transportation Mode *</Label>
        <RadioGroup value={transportMode} onValueChange={setTransportMode}>
          <div className="flex space-x-6 mt-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="sea" id="sea" />
              <Label htmlFor="sea">Sea Freight</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="land" id="land" />
              <Label htmlFor="land">Land Transport</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="rail" id="rail" />
              <Label htmlFor="rail">Rail Transport</Label>
            </div>
          </div>
        </RadioGroup>
      </div>
      
      {/* Dynamic fields based on transport mode */}
      {transportMode === 'sea' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="vesselName">Vessel Name</Label>
            <Input id="vesselName" placeholder="Enter vessel name" />
          </div>
          <div>
            <Label htmlFor="voyageNumber">Voyage Number</Label>
            <Input id="voyageNumber" placeholder="Enter voyage #" />
          </div>
          <div>
            <Label htmlFor="bookingNumber">Booking Number</Label>
            <Input id="bookingNumber" placeholder="Enter booking #" />
          </div>
        </div>
      )}
      
      {/* Port Selection with Suggestions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="originPort">Origin Port</Label>
          <Select onValueChange={setSelectedOriginPort}>
            <SelectTrigger>
              <SelectValue placeholder="Select origin port..." />
            </SelectTrigger>
            <SelectContent>
              {originPorts.map(port => (
                <SelectItem key={port.id} value={port.id}>
                  <div>
                    <div className="font-medium">{port.name}</div>
                    <div className="text-xs text-neutral-400">
                      {port.code} • {port.country}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-neutral-400 mt-1">
            Suggested based on customer history
          </p>
        </div>
        
        <div>
          <Label htmlFor="destinationPort">Destination Port *</Label>
          <Select onValueChange={setSelectedDestinationPort} required>
            <SelectTrigger>
              <SelectValue placeholder="Select destination port..." />
            </SelectTrigger>
            <SelectContent>
              {destinationPorts.map(port => (
                <SelectItem key={port.id} value={port.id}>
                  <div>
                    <div className="font-medium">{port.name}</div>
                    <div className="text-xs text-neutral-400">
                      {port.code} • {port.country}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-neutral-400 mt-1">
            Required for shipment routing and documentation
          </p>
        </div>
      </div>
      
      {/* Optional Notes Field */}
      <div>
        <Label htmlFor="notes">Notes (Optional)</Label>
        <Textarea
          id="notes"
          placeholder="Enter any additional notes, special instructions, or comments for this shipment..."
          value={shipmentNotes}
          onChange={(e) => setShipmentNotes(e.target.value)}
          className="bg-primary-900 border-primary-600 text-neutral-50 min-h-[100px]"
          rows={4}
        />
        <p className="text-xs text-neutral-400 mt-1">
          These notes will be visible to all stakeholders and included in shipment documentation
        </p>
      </div>
      
      {/* Auto-Generation Notice */}
      <div className="space-y-3">
        <Alert className="bg-info-500/10 border-info-500">
          <InfoIcon className="w-4 h-4" />
          <AlertTitle>Automatic Shipment Number Generation</AlertTitle>
          <AlertDescription>
            Shipment number will be automatically generated when saved using format: 
            <code className="bg-primary-800 px-1 rounded text-accent-500 font-mono text-xs">
              EX[Mode]-[Port]-YYMMDD-[Running]
            </code>
            <br />
            <span className="text-xs">
              Mode: 1=Land, 2=Rail, 3=Sea • Running number resets monthly per mode/port combination
            </span>
          </AlertDescription>
        </Alert>
        
        <Alert className="bg-info-500/10 border-info-500">
          <InfoIcon className="w-4 h-4" />
          <AlertTitle>Automatic Container Management</AlertTitle>
          <AlertDescription>
            Containers will be automatically generated when the shipment is saved. 
            Products will be allocated to containers based on quantity and packaging specifications.
            You can manage container details after shipment creation.
          </AlertDescription>
        </Alert>
      </div>
      
    </CardContent>
  </Card>
  
  {/* Form Actions */}
  <div className="flex justify-end space-x-4 pt-6 border-t border-primary-700">
    <Button variant="secondary" type="button">
      Save as Draft
    </Button>
    <Button type="submit" className="bg-accent-500 hover:bg-accent-400">
      Create Shipment
    </Button>
  </div>
  
</form>
```

### Driver Mobile Interface

#### Mobile Dashboard (Assignments)
```tsx
<div className="min-h-screen bg-primary-900 pb-16">
  
  {/* Mobile Header */}
  <header className="bg-primary-800 px-4 py-3 border-b border-primary-700">
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-lg font-semibold text-neutral-50">My Assignments</h1>
        <p className="text-sm text-neutral-400">
          {assignedShipments.length} active assignments
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <NetworkStatusIndicator />
        <NotificationBadge count={unreadNotifications} />
      </div>
    </div>
  </header>
  
  {/* Pull-to-refresh indicator */}
  <PullToRefresh onRefresh={refreshAssignments}>
    
    {/* Assignment Cards */}
    <div className="px-4 py-4 space-y-4">
      {assignedShipments.map(shipment => (
        <Card key={shipment.id} className="bg-primary-800 border-primary-700">
          <CardContent className="p-4">
            
            {/* Header with Status */}
            <div className="flex items-start justify-between mb-3">
              <div>
                <h3 className="font-mono text-sm text-accent-500">
                  {shipment.number}
                </h3>
                <p className="text-neutral-50 font-medium">
                  {shipment.customer.name}
                </p>
              </div>
              <StatusBadge status={shipment.status} />
            </div>
            
            {/* Location Information */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center text-sm text-neutral-200">
                <MapPinIcon className="w-4 h-4 mr-2 text-accent-500" />
                <span>Pickup: {shipment.pickupLocation}</span>
              </div>
              <div className="flex items-center text-sm text-neutral-200">
                <FlagIcon className="w-4 h-4 mr-2 text-accent-500" />
                <span>Delivery: {shipment.deliveryLocation}</span>
              </div>
              <div className="flex items-center text-sm text-neutral-400">
                <ClockIcon className="w-4 h-4 mr-2" />
                <span>ETA: {shipment.estimatedDelivery}</span>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex space-x-2">
              <Button 
                size="sm" 
                className="flex-1 bg-accent-500 hover:bg-accent-400 min-h-[44px]"
                onClick={() => navigateToUpdateStatus(shipment.id)}
              >
                <CameraIcon className="w-4 h-4 mr-2" />
                Update Status
              </Button>
              <Button 
                size="sm" 
                variant="secondary" 
                className="bg-primary-700 hover:bg-primary-600 min-h-[44px]"
                onClick={() => openNavigation(shipment.pickupLocation)}
              >
                <NavigationIcon className="w-4 h-4" />
              </Button>
            </div>
            
          </CardContent>
        </Card>
      ))}
    </div>
    
  </PullToRefresh>
  
  {/* Mobile Tab Navigation */}
  <nav className="fixed bottom-0 left-0 right-0 bg-primary-900 border-t border-primary-700">
    <div className="grid grid-cols-4">
      <TabButton 
        icon={<HomeIcon />} 
        label="Dashboard" 
        active 
        href="/driver/dashboard"
      />
      <TabButton 
        icon={<CameraIcon />} 
        label="Update" 
        href="/driver/update"
      />
      <TabButton 
        icon={<PhotoIcon />} 
        label="Photos" 
        href="/driver/photos"
      />
      <TabButton 
        icon={<HistoryIcon />} 
        label="History" 
        href="/driver/history"
      />
    </div>
  </nav>
  
</div>
```

#### Mobile Status Update Interface
```tsx
<div className="min-h-screen bg-primary-900">
  
  {/* Header with Back Navigation */}
  <header className="bg-primary-800 px-4 py-3 border-b border-primary-700">
    <div className="flex items-center space-x-3">
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => router.back()}
        className="text-neutral-50"
      >
        <ArrowLeftIcon className="w-5 h-5" />
      </Button>
      <div>
        <h1 className="text-lg font-semibold text-neutral-50">Update Status</h1>
        <p className="text-sm text-neutral-400 font-mono">
          {shipment.number}
        </p>
      </div>
    </div>
  </header>
  
  <div className="px-4 py-6 space-y-6">
    
    {/* Current Status Display */}
    <Card className="bg-primary-800 border-primary-700">
      <CardContent className="p-4">
        <div className="text-center">
          <StatusIcon status={currentStatus} className="w-12 h-12 mx-auto mb-2" />
          <h2 className="text-lg font-medium text-neutral-50 mb-1">
            Current Status
          </h2>
          <p className="text-accent-500 font-medium">
            {getStatusDisplayName(currentStatus)}
          </p>
          <p className="text-xs text-neutral-400 mt-1">
            Last updated: {formatTimestamp(lastUpdate)}
          </p>
        </div>
      </CardContent>
    </Card>
    
    {/* Status Selection */}
    <Card className="bg-primary-800 border-primary-700">
      <CardHeader className="pb-3">
        <CardTitle className="text-neutral-50">Select New Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {availableStatuses.map(status => (
          <Button
            key={status.value}
            variant={selectedStatus === status.value ? "default" : "secondary"}
            className={`w-full justify-start min-h-[44px] ${
              selectedStatus === status.value 
                ? 'bg-accent-500 hover:bg-accent-400' 
                : 'bg-primary-700 hover:bg-primary-600'
            }`}
            onClick={() => setSelectedStatus(status.value)}
          >
            <status.icon className="w-5 h-5 mr-3" />
            <div className="text-left">
              <div className="font-medium">{status.label}</div>
              <div className="text-xs opacity-75">{status.description}</div>
            </div>
          </Button>
        ))}
      </CardContent>
    </Card>
    
    {/* Photo Capture Section */}
    <Card className="bg-primary-800 border-primary-700">
      <CardHeader className="pb-3">
        <CardTitle className="text-neutral-50">Documentation Required</CardTitle>
        <CardDescription>
          Take photos to document the status update
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        
        {/* Camera Interface */}
        <div className="relative">
          <div className="aspect-video bg-primary-900 rounded-lg border-2 border-dashed border-primary-600 flex items-center justify-center">
            {cameraActive ? (
              <CameraPreview onCapture={handlePhotoCapture} />
            ) : (
              <Button 
                onClick={activateCamera}
                className="bg-accent-500 hover:bg-accent-400 min-h-[44px]"
              >
                <CameraIcon className="w-5 h-5 mr-2" />
                Open Camera
              </Button>
            )}
          </div>
        </div>
        
        {/* Photo Gallery */}
        {capturedPhotos.length > 0 && (
          <div>
            <Label className="text-neutral-50 mb-2 block">
              Captured Photos ({capturedPhotos.length}/5)
            </Label>
            <div className="grid grid-cols-3 gap-2">
              {capturedPhotos.map((photo, index) => (
                <div key={index} className="relative">
                  <img 
                    src={photo.thumbnail} 
                    alt={`Photo ${index + 1}`}
                    className="aspect-square object-cover rounded-lg"
                  />
                  <Button
                    size="sm"
                    variant="destructive"
                    className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                    onClick={() => removePhoto(index)}
                  >
                    <XIcon className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
        
      </CardContent>
    </Card>
    
    {/* Location & Notes */}
    <Card className="bg-primary-800 border-primary-700">
      <CardContent className="p-4 space-y-4">
        
        {/* GPS Location */}
        <div>
          <Label className="text-neutral-50 mb-2 block">Location</Label>
          <div className="flex items-center space-x-2 p-3 bg-primary-900 rounded-lg">
            <MapPinIcon className="w-4 h-4 text-accent-500" />
            <div className="flex-1 text-sm">
              {currentLocation ? (
                <div>
                  <p className="text-neutral-50">{currentLocation.address}</p>
                  <p className="text-neutral-400 font-mono text-xs">
                    {currentLocation.coordinates}
                  </p>
                </div>
              ) : (
                <span className="text-neutral-400">Getting location...</span>
              )}
            </div>
            <Button 
              size="sm" 
              variant="secondary"
              onClick={refreshLocation}
              className="bg-primary-700"
            >
              <RefreshIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        {/* Optional Notes */}
        <div>
          <Label htmlFor="notes" className="text-neutral-50 mb-2 block">
            Notes (Optional)
          </Label>
          <Textarea
            id="notes"
            placeholder="Add any additional notes..."
            value={statusNotes}
            onChange={(e) => setStatusNotes(e.target.value)}
            className="bg-primary-900 border-primary-600 text-neutral-50"
            rows={3}
          />
        </div>
        
      </CardContent>
    </Card>
    
    {/* Submit Actions */}
    <div className="space-y-3 pb-6">
      
      {/* Offline Warning */}
      {!isOnline && (
        <Alert className="bg-warning-500/10 border-warning-500">
          <WifiOffIcon className="w-4 h-4" />
          <AlertTitle>Offline Mode</AlertTitle>
          <AlertDescription>
            Update will be queued and sent when connection is restored
          </AlertDescription>
        </Alert>
      )}
      
      {/* Action Buttons */}
      <div className="flex space-x-3">
        <Button 
          variant="secondary" 
          className="flex-1 min-h-[44px] bg-primary-700 hover:bg-primary-600"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button 
          className="flex-1 min-h-[44px] bg-accent-500 hover:bg-accent-400"
          onClick={handleStatusUpdate}
          disabled={!selectedStatus || (!isOnline && capturedPhotos.length === 0)}
        >
          {isOnline ? 'Update Status' : 'Queue Update'}
        </Button>
      </div>
      
    </div>
    
  </div>
  
</div>
```

### Shipment Viewing and Editing Interface

#### Shipment Search and List View
```tsx
<div className="max-w-7xl mx-auto px-6 py-8">
  
  {/* Search and Filter Header */}
  <div className="mb-8">
    <div className="flex items-center justify-between mb-6">
      <h1 className="text-2xl font-semibold text-neutral-50">Shipment Management</h1>
      <div className="flex items-center space-x-4">
        <Button className="bg-accent-500 hover:bg-accent-400">
          <PlusIcon className="w-4 h-4 mr-2" />
          Create New Shipment
        </Button>
        <Button variant="secondary" className="bg-primary-700 hover:bg-primary-600">
          <DownloadIcon className="w-4 h-4 mr-2" />
          Export
        </Button>
      </div>
    </div>
    
    {/* Advanced Search Interface */}
    <Card className="bg-primary-800 border-primary-700">
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          
          {/* Quick Search */}
          <div className="lg:col-span-2">
            <Label htmlFor="quickSearch">Quick Search</Label>
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
              <Input
                id="quickSearch"
                placeholder="Shipment number, customer, container..."
                className="pl-10 bg-primary-900 border-primary-600"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          {/* Status Filter */}
          <div>
            <Label htmlFor="statusFilter">Status</Label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="bg-primary-900 border-primary-600">
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="booking_confirmed">Booking Confirmed</SelectItem>
                <SelectItem value="in_transit">In Transit</SelectItem>
                <SelectItem value="arrived">Arrived</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Transportation Mode Filter */}
          <div>
            <Label htmlFor="modeFilter">Mode</Label>
            <Select value={modeFilter} onValueChange={setModeFilter}>
              <SelectTrigger className="bg-primary-900 border-primary-600">
                <SelectValue placeholder="All modes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Modes</SelectItem>
                <SelectItem value="sea">Sea Freight</SelectItem>
                <SelectItem value="land">Land Transport</SelectItem>
                <SelectItem value="rail">Rail Transport</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
        </div>
        
        {/* Advanced Filters (Expandable) */}
        <Collapsible open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="text-neutral-300 hover:text-neutral-50 p-0">
              <ChevronDownIcon className={`w-4 h-4 mr-2 transition-transform ${showAdvancedFilters ? 'rotate-180' : ''}`} />
              Advanced Filters
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              
              <div>
                <Label htmlFor="customerFilter">Customer</Label>
                <Select value={customerFilter} onValueChange={setCustomerFilter}>
                  <SelectTrigger className="bg-primary-900 border-primary-600">
                    <SelectValue placeholder="Select customer" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.map(customer => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="dateRangeFrom">Date From</Label>
                <Input
                  type="date"
                  id="dateRangeFrom"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="bg-primary-900 border-primary-600"
                />
              </div>
              
              <div>
                <Label htmlFor="dateRangeTo">Date To</Label>
                <Input
                  type="date"
                  id="dateRangeTo"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="bg-primary-900 border-primary-600"
                />
              </div>
              
              <div className="flex items-end">
                <Button 
                  onClick={clearAllFilters}
                  variant="secondary"
                  className="bg-primary-700 hover:bg-primary-600"
                >
                  Clear Filters
                </Button>
              </div>
              
            </div>
          </CollapsibleContent>
        </Collapsible>
        
      </CardContent>
    </Card>
  </div>
  
  {/* Results Summary and Controls */}
  <div className="flex items-center justify-between mb-6">
    <div className="flex items-center space-x-4 text-neutral-300">
      <span>{filteredShipments.length} shipments found</span>
      {hasActiveFilters && (
        <Badge variant="secondary" className="bg-accent-500 text-black">
          Filtered
        </Badge>
      )}
    </div>
    
    <div className="flex items-center space-x-2">
      <Select value={sortBy} onValueChange={setSortBy}>
        <SelectTrigger className="w-40 bg-primary-800 border-primary-700">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="created_desc">Newest First</SelectItem>
          <SelectItem value="created_asc">Oldest First</SelectItem>
          <SelectItem value="status">Status</SelectItem>
          <SelectItem value="customer">Customer</SelectItem>
          <SelectItem value="eta">ETA</SelectItem>
        </SelectContent>
      </Select>
      
      <div className="flex border border-primary-700 rounded-lg">
        <Button
          variant={viewMode === 'list' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('list')}
          className={viewMode === 'list' ? 'bg-accent-500' : 'bg-transparent'}
        >
          <ListIcon className="w-4 h-4" />
        </Button>
        <Button
          variant={viewMode === 'grid' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('grid')}
          className={viewMode === 'grid' ? 'bg-accent-500' : 'bg-transparent'}
        >
          <GridIcon className="w-4 h-4" />
        </Button>
      </div>
    </div>
  </div>
  
  {/* Shipment Cards/List */}
  <div className={`gap-6 ${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'space-y-4'}`}>
    {filteredShipments.map(shipment => (
      <Card 
        key={shipment.id} 
        className="bg-primary-800 border-primary-700 hover:border-primary-600 transition-colors cursor-pointer"
        onClick={() => openShipmentDetail(shipment.id)}
      >
        <CardContent className="p-6">
          
          {/* Header with Status */}
          <div className="flex items-start justify-between mb-4">
            <div>
              <h3 className="font-mono text-accent-500 font-medium">
                {shipment.number}
              </h3>
              <p className="text-neutral-50 font-medium">{shipment.customer.name}</p>
            </div>
            <StatusBadge status={shipment.status} />
          </div>
          
          {/* Key Information */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center text-sm text-neutral-300">
              <TruckIcon className="w-4 h-4 mr-2 text-accent-500" />
              <span>{getTransportModeLabel(shipment.transportMode)}</span>
            </div>
            <div className="flex items-center text-sm text-neutral-300">
              <MapPinIcon className="w-4 h-4 mr-2 text-accent-500" />
              <span>{shipment.originPort} → {shipment.destinationPort}</span>
            </div>
            <div className="flex items-center text-sm text-neutral-300">
              <CalendarIcon className="w-4 h-4 mr-2 text-accent-500" />
              <span>ETA: {formatDate(shipment.eta)}</span>
            </div>
          </div>
          
          {/* Quick Actions */}
          <div className="flex space-x-2">
            <Button 
              size="sm" 
              variant="secondary"
              className="flex-1 bg-primary-700 hover:bg-primary-600"
              onClick={(e) => {
                e.stopPropagation();
                openShipmentDetail(shipment.id);
              }}
            >
              <EyeIcon className="w-3 h-3 mr-1" />
              View
            </Button>
            <Button 
              size="sm" 
              variant="secondary"
              className="flex-1 bg-primary-700 hover:bg-primary-600"
              onClick={(e) => {
                e.stopPropagation();
                editShipment(shipment.id);
              }}
            >
              <EditIcon className="w-3 h-3 mr-1" />
              Edit
            </Button>
            <Button 
              size="sm" 
              variant="secondary"
              className="bg-primary-700 hover:bg-primary-600"
              onClick={(e) => {
                e.stopPropagation();
                openQuickActions(shipment.id);
              }}
            >
              <MoreVerticalIcon className="w-3 h-3" />
            </Button>
          </div>
          
        </CardContent>
      </Card>
    ))}
  </div>
  
  {/* Pagination */}
  {totalPages > 1 && (
    <div className="flex items-center justify-center mt-8">
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              href="#" 
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              className={currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}
            />
          </PaginationItem>
          
          {[...Array(totalPages)].map((_, i) => (
            <PaginationItem key={i}>
              <PaginationLink 
                href="#" 
                onClick={() => setCurrentPage(i + 1)}
                isActive={currentPage === i + 1}
              >
                {i + 1}
              </PaginationLink>
            </PaginationItem>
          ))}
          
          <PaginationItem>
            <PaginationNext 
              href="#" 
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              className={currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  )}
  
</div>
```

#### Shipment Detail View with Tabbed Interface
```tsx
<div className="max-w-7xl mx-auto px-6 py-8">
  
  {/* Header with Actions */}
  <div className="flex items-center justify-between mb-8">
    <div className="flex items-center space-x-4">
      <Button 
        variant="ghost" 
        onClick={() => router.back()}
        className="text-neutral-400 hover:text-neutral-50"
      >
        <ArrowLeftIcon className="w-5 h-5 mr-2" />
        Back to Shipments
      </Button>
      <div>
        <h1 className="text-2xl font-semibold text-neutral-50">{shipment.number}</h1>
        <p className="text-neutral-400">{shipment.customer.name}</p>
      </div>
    </div>
    
    <div className="flex items-center space-x-4">
      <StatusBadge status={shipment.status} size="lg" />
      <div className="flex space-x-2">
        <Button 
          variant="secondary"
          className="bg-primary-700 hover:bg-primary-600"
          onClick={() => setEditMode(!editMode)}
        >
          <EditIcon className="w-4 h-4 mr-2" />
          {editMode ? 'Cancel Edit' : 'Edit Shipment'}
        </Button>
        <Button className="bg-accent-500 hover:bg-accent-400">
          <DocumentIcon className="w-4 h-4 mr-2" />
          Generate Documents
        </Button>
      </div>
    </div>
  </div>
  
  {/* Edit Mode Warning */}
  {editMode && (
    <Alert className="mb-6 bg-warning-500/10 border-warning-500">
      <EditIcon className="w-4 h-4" />
      <AlertTitle>Edit Mode Active</AlertTitle>
      <AlertDescription>
        You are now editing this shipment. Changes will be automatically saved. 
        Click "Cancel Edit" to exit without saving unsaved changes.
      </AlertDescription>
    </Alert>
  )}
  
  {/* Tabbed Content */}
  <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
    
    {/* Tab Navigation */}
    <TabsList className="grid w-full grid-cols-6 bg-primary-800 border border-primary-700">
      <TabsTrigger value="overview" className="data-[state=active]:bg-accent-500">
        <HomeIcon className="w-4 h-4 mr-2" />
        Overview
      </TabsTrigger>
      <TabsTrigger value="logistics" className="data-[state=active]:bg-accent-500">
        <TruckIcon className="w-4 h-4 mr-2" />
        Logistics
      </TabsTrigger>
      <TabsTrigger value="products" className="data-[state=active]:bg-accent-500">
        <PackageIcon className="w-4 h-4 mr-2" />
        Products
      </TabsTrigger>
      <TabsTrigger value="documents" className="data-[state=active]:bg-accent-500">
        <DocumentIcon className="w-4 h-4 mr-2" />
        Documents
      </TabsTrigger>
      <TabsTrigger value="communication" className="data-[state=active]:bg-accent-500">
        <MessageSquareIcon className="w-4 h-4 mr-2" />
        Communication
      </TabsTrigger>
      <TabsTrigger value="audit" className="data-[state=active]:bg-accent-500">
        <ClockIcon className="w-4 h-4 mr-2" />
        Audit Trail
      </TabsTrigger>
    </TabsList>
    
    {/* Overview Tab */}
    <TabsContent value="overview" className="space-y-6">
      
      {/* Key Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        
        <Card className="bg-primary-800 border-primary-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-neutral-400">Transportation</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-semibold text-neutral-50">
              {getTransportModeLabel(shipment.transportMode)}
            </p>
            <p className="text-sm text-neutral-400">
              {shipment.originPort} → {shipment.destinationPort}
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-primary-800 border-primary-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-neutral-400">Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <p className="text-sm">
                <span className="text-neutral-400">ETD:</span>
                <span className="text-neutral-50 ml-2">{formatDateTime(shipment.etd)}</span>
              </p>
              <p className="text-sm">
                <span className="text-neutral-400">ETA:</span>
                <span className="text-neutral-50 ml-2">{formatDateTime(shipment.eta)}</span>
              </p>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-primary-800 border-primary-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-neutral-400">Containers</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-semibold text-neutral-50">
              {shipment.containers.length}
            </p>
            <p className="text-sm text-neutral-400">
              Total Weight: {shipment.totalWeight}kg
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-primary-800 border-primary-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-neutral-400">Value</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-semibold text-neutral-50">
              ${shipment.totalValue.toLocaleString()}
            </p>
            <p className="text-sm text-neutral-400">
              {shipment.products.length} product types
            </p>
          </CardContent>
        </Card>
        
      </div>
      
      {/* Status Timeline */}
      <Card className="bg-primary-800 border-primary-700">
        <CardHeader>
          <CardTitle className="text-neutral-50">Status Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {shipment.statusHistory.map((status, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className={`w-3 h-3 rounded-full ${
                    status.completed ? 'bg-accent-500' : 'bg-neutral-600'
                  }`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-neutral-50">
                    {getStatusDisplayName(status.status)}
                  </p>
                  <p className="text-xs text-neutral-400">
                    {formatDateTime(status.timestamp)} • {status.user}
                  </p>
                  {status.notes && (
                    <p className="text-xs text-neutral-300 mt-1">{status.notes}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
    </TabsContent>
    
    {/* Additional tabs would be implemented similarly... */}
    
  </Tabs>
  
</div>
```

---

## Responsive Design Implementation

### Breakpoint Strategy

#### Device Categories
```css
/* Mobile First Approach */
/* Mobile (default): 0px - 767px */
.mobile-first { /* base styles */ }

/* Tablet: 768px - 1023px */
@media (min-width: 768px) {
  .tablet-up { /* tablet and desktop styles */ }
}

/* Desktop: 1024px - 1279px */
@media (min-width: 1024px) {
  .desktop-up { /* desktop styles */ }
}

/* Large Desktop: 1280px+ */
@media (min-width: 1280px) {
  .large-desktop { /* large screen styles */ }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi { /* retina/high-dpi styles */ }
}
```

#### Touch Target Optimization
```css
/* Minimum Touch Target Requirements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
}

/* Mobile-Specific Interactions */
@media (max-width: 767px) {
  .mobile-touch {
    font-size: 16px; /* Prevent zoom on iOS */
    line-height: 1.5;
    letter-spacing: 0.01em;
  }
  
  .mobile-form-control {
    height: 44px;
    font-size: 16px;
    border-radius: 8px;
  }
  
  .mobile-button {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}
```

### Layout Adaptations

#### Dashboard Grid Responsive Behavior
```tsx
{/* Admin Dashboard Grid - Responsive */}
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
  
  {/* Stats Cards - Full width on mobile, adaptive on larger screens */}
  <Card className="sm:col-span-2 lg:col-span-1">
    <StatCard title="Active Shipments" value="127" />
  </Card>
  
  {/* Master Data Section - Responsive column spanning */}
  <Card className="col-span-full lg:col-span-2">
    <MasterDataGrid />
  </Card>
  
  {/* Activity Feed - Adaptive positioning */}
  <Card className="col-span-full lg:col-span-2 xl:col-span-3">
    <ActivityFeed />
  </Card>
  
</div>
```

#### Mobile Navigation Patterns
```tsx
{/* Desktop: Horizontal Navigation */}
<nav className="hidden md:flex space-x-8">
  <NavigationLink href="/dashboard">Dashboard</NavigationLink>
  <NavigationLink href="/shipments">Shipments</NavigationLink>
  <NavigationLink href="/master-data">Master Data</NavigationLink>
</nav>

{/* Mobile: Hamburger Menu */}
<div className="md:hidden">
  <Sheet>
    <SheetTrigger asChild>
      <Button variant="ghost" size="sm">
        <MenuIcon className="w-5 h-5" />
      </Button>
    </SheetTrigger>
    <SheetContent side="left" className="bg-primary-900 border-primary-700">
      <nav className="space-y-4 mt-8">
        <MobileNavigationLink href="/dashboard" icon={<HomeIcon />}>
          Dashboard
        </MobileNavigationLink>
        <MobileNavigationLink href="/shipments" icon={<TruckIcon />}>
          Shipments
        </MobileNavigationLink>
        <MobileNavigationLink href="/master-data" icon={<DatabaseIcon />}>
          Master Data
        </MobileNavigationLink>
      </nav>
    </SheetContent>
  </Sheet>
</div>

{/* Mobile: Bottom Tab Navigation (Driver Interface) */}
<div className="md:hidden fixed bottom-0 left-0 right-0 bg-primary-900 border-t border-primary-700 z-50">
  <div className="grid grid-cols-4 h-16">
    {tabs.map(tab => (
      <Link 
        key={tab.href}
        href={tab.href}
        className={`flex flex-col items-center justify-center space-y-1 ${
          pathname === tab.href ? 'text-accent-500' : 'text-neutral-400'
        }`}
      >
        <tab.icon className="w-5 h-5" />
        <span className="text-xs font-medium">{tab.label}</span>
      </Link>
    ))}
  </div>
</div>
```

#### Form Layout Responsive Adaptations
```tsx
{/* Shipment Creation Form - Responsive Layout */}
<form className="space-y-6">
  
  {/* Customer Information - Responsive Grid */}
  <Card>
    <CardContent className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 p-6">
      
      {/* Customer Selection - Full width on mobile */}
      <div className="md:col-span-2 xl:col-span-1">
        <Label htmlFor="customer">Customer *</Label>
        <CustomerSelect />
      </div>
      
      {/* Shipper & Product - Stack on mobile, side-by-side on larger */}
      <div>
        <Label htmlFor="shipper">Shipper</Label>
        <ShipperSelect />
      </div>
      
      <div>
        <Label htmlFor="product">Product</Label>
        <ProductSelect />
      </div>
      
    </CardContent>
  </Card>
  
  {/* Product Details - Responsive Table/Cards */}
  <Card>
    <CardContent className="p-6">
      
      {/* Desktop: Data Table */}
      <div className="hidden md:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Unit Price</TableHead>
              <TableHead>Total Value</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.map(product => (
              <ProductTableRow key={product.id} product={product} />
            ))}
          </TableBody>
        </Table>
      </div>
      
      {/* Mobile: Card Layout */}
      <div className="md:hidden space-y-4">
        {products.map(product => (
          <Card key={product.id} className="bg-primary-800">
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-neutral-50">{product.name}</h4>
                <ProductActionMenu product={product} />
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-neutral-400">Quantity:</span>
                  <span className="ml-2 text-neutral-50">{product.quantity}</span>
                </div>
                <div>
                  <span className="text-neutral-400">Unit Price:</span>
                  <span className="ml-2 text-neutral-50 font-mono">${product.unitPrice}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-neutral-400">Total Value:</span>
                  <span className="ml-2 text-accent-500 font-mono font-medium">
                    ${product.totalValue}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
    </CardContent>
  </Card>
  
</form>
```

---

## Accessibility Implementation

### WCAG 2.1 AA Compliance

#### Color Contrast Validation
```css
/* Validated Color Combinations */
.contrast-validation {
  /* Primary Text on Dark Background */
  color: #f8fafc; /* Light gray */
  background: #0f172a; /* Navy blue */
  /* Contrast Ratio: 15.5:1 ✅ (exceeds AA requirement 4.5:1) */
}

.contrast-secondary {
  /* Secondary Text on Medium Background */
  color: #e2e8f0; /* Light gray */
  background: #334155; /* Slate blue */
  /* Contrast Ratio: 8.1:1 ✅ */
}

.contrast-accent {
  /* Orange Accent on Dark Background */
  color: #f97316; /* Orange */
  background: #1e293b; /* Dark blue */
  /* Contrast Ratio: 6.2:1 ✅ */
}

.contrast-interactive {
  /* Interactive Elements */
  color: #ffffff; /* White */
  background: #f97316; /* Orange */
  /* Contrast Ratio: 5.9:1 ✅ */
}
```

#### Keyboard Navigation Implementation
```tsx
// Keyboard Navigation Component
const KeyboardNavigableForm = () => {
  const [focusedIndex, setFocusedIndex] = useState(0);
  const fieldRefs = useRef([]);
  
  const handleKeyDown = (event) => {
    switch (event.key) {
      case 'Tab':
        // Natural tab order maintained by proper HTML structure
        break;
        
      case 'Enter':
        if (event.target.type === 'submit') {
          handleFormSubmit();
        } else if (event.target.tagName === 'BUTTON') {
          event.target.click();
        }
        break;
        
      case 'Escape':
        if (modalOpen) {
          closeModal();
        } else {
          event.target.blur();
        }
        break;
        
      case 'ArrowDown':
      case 'ArrowUp':
        // Custom arrow navigation for select components
        if (event.target.getAttribute('role') === 'combobox') {
          handleSelectNavigation(event);
        }
        break;
    }
  };
  
  return (
    <form onKeyDown={handleKeyDown} className="space-y-4">
      {/* All form elements include proper tab order and ARIA attributes */}
      <div>
        <Label htmlFor="customer" className="required-field">
          Customer *
        </Label>
        <Select 
          id="customer"
          aria-required="true"
          aria-describedby="customer-help"
          onKeyDown={handleSelectKeyDown}
        >
          <SelectTrigger 
            ref={el => fieldRefs.current[0] = el}
            className="focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
          >
            <SelectValue placeholder="Select customer..." />
          </SelectTrigger>
        </Select>
        <div id="customer-help" className="sr-only">
          Selecting a customer will automatically load associated shippers and products
        </div>
      </div>
    </form>
  );
};
```

#### Screen Reader Support
```tsx
// ARIA Labels and Descriptions
const AccessibleStatusCard = ({ shipment }) => {
  const statusAnnouncement = `Shipment ${shipment.number} status: ${shipment.status}. 
    Customer: ${shipment.customer}. 
    Last updated: ${formatDateTime(shipment.lastUpdate)}`;
    
  return (
    <Card 
      role="article"
      aria-label={statusAnnouncement}
      className="bg-primary-800 border-primary-700"
    >
      <CardContent className="p-4">
        
        {/* Hidden screen reader content */}
        <div className="sr-only">
          Shipment card. Use Enter key to view details or arrow keys to navigate between shipments.
        </div>
        
        {/* Visual content with ARIA labels */}
        <div className="flex justify-between items-start">
          <div>
            <h3 
              id={`shipment-${shipment.id}-title`}
              className="font-mono text-accent-500"
            >
              {shipment.number}
            </h3>
            <p 
              aria-labelledby={`shipment-${shipment.id}-title`}
              className="text-neutral-50"
            >
              {shipment.customer}
            </p>
          </div>
          
          <StatusBadge 
            status={shipment.status}
            aria-label={`Status: ${getStatusDisplayName(shipment.status)}`}
          />
        </div>
        
        {/* Location information with landmarks */}
        <dl className="mt-3 space-y-1">
          <div className="flex items-center text-sm">
            <dt className="sr-only">Pickup location:</dt>
            <MapPinIcon className="w-4 h-4 mr-2 text-accent-500" aria-hidden="true" />
            <dd className="text-neutral-200">{shipment.pickupLocation}</dd>
          </div>
          
          <div className="flex items-center text-sm">
            <dt className="sr-only">Delivery location:</dt>
            <FlagIcon className="w-4 h-4 mr-2 text-accent-500" aria-hidden="true" />
            <dd className="text-neutral-200">{shipment.deliveryLocation}</dd>
          </div>
          
          <div className="flex items-center text-sm">
            <dt className="sr-only">Estimated delivery:</dt>
            <ClockIcon className="w-4 h-4 mr-2 text-neutral-400" aria-hidden="true" />
            <dd className="text-neutral-400">
              <time dateTime={shipment.estimatedDeliveryISO}>
                {shipment.estimatedDelivery}
              </time>
            </dd>
          </div>
        </dl>
        
        {/* Action buttons with clear labels */}
        <div className="flex space-x-2 mt-4">
          <Button 
            size="sm"
            className="flex-1 bg-accent-500 hover:bg-accent-400"
            aria-describedby={`update-help-${shipment.id}`}
          >
            <CameraIcon className="w-4 h-4 mr-2" aria-hidden="true" />
            Update Status
          </Button>
          <div id={`update-help-${shipment.id}`} className="sr-only">
            Update status for shipment {shipment.number}. This will open the mobile status update interface.
          </div>
          
          <Button 
            size="sm"
            variant="secondary"
            className="bg-primary-700 hover:bg-primary-600"
            aria-label="Open navigation to pickup location"
          >
            <NavigationIcon className="w-4 h-4" aria-hidden="true" />
          </Button>
        </div>
        
      </CardContent>
    </Card>
  );
};
```

#### Focus Management
```tsx
// Focus Management for Modal Dialogs
const AccessibleModal = ({ isOpen, onClose, children }) => {
  const modalRef = useRef(null);
  const previousFocusRef = useRef(null);
  
  useEffect(() => {
    if (isOpen) {
      // Store current focus
      previousFocusRef.current = document.activeElement;
      
      // Focus first focusable element in modal
      const firstFocusable = modalRef.current?.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      firstFocusable?.focus();
      
      // Trap focus within modal
      const handleKeyDown = (event) => {
        if (event.key === 'Tab') {
          const focusableElements = modalRef.current.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          const firstFocusable = focusableElements[0];
          const lastFocusable = focusableElements[focusableElements.length - 1];
          
          if (event.shiftKey) {
            if (document.activeElement === firstFocusable) {
              lastFocusable.focus();
              event.preventDefault();
            }
          } else {
            if (document.activeElement === lastFocusable) {
              firstFocusable.focus();
              event.preventDefault();
            }
          }
        } else if (event.key === 'Escape') {
          onClose();
        }
      };
      
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    } else {
      // Restore focus
      previousFocusRef.current?.focus();
    }
  }, [isOpen, onClose]);
  
  if (!isOpen) return null;
  
  return (
    <div 
      className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      onClick={onClose}
    >
      <div 
        ref={modalRef}
        className="bg-primary-800 border border-primary-700 rounded-lg max-w-md w-full p-6"
        onClick={e => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  );
};
```

#### Error Handling and Validation
```tsx
// Accessible Form Validation
const AccessibleFormField = ({ 
  id, 
  label, 
  required, 
  error, 
  helpText, 
  children 
}) => {
  const fieldId = id || `field-${Math.random()}`;
  const errorId = `${fieldId}-error`;
  const helpId = `${fieldId}-help`;
  
  return (
    <div className="space-y-2">
      <Label 
        htmlFor={fieldId}
        className={`text-neutral-50 ${required ? 'required-field' : ''}`}
      >
        {label}
        {required && (
          <span className="text-accent-500 ml-1" aria-label="required">*</span>
        )}
      </Label>
      
      <div className="relative">
        {cloneElement(children, {
          id: fieldId,
          'aria-required': required,
          'aria-invalid': !!error,
          'aria-describedby': [
            error ? errorId : null,
            helpText ? helpId : null
          ].filter(Boolean).join(' ') || undefined,
          className: `${children.props.className} ${
            error 
              ? 'border-error-500 focus:border-error-500 focus:ring-error-500' 
              : 'border-primary-700 focus:border-accent-500 focus:ring-accent-500'
          }`
        })}
        
        {error && (
          <ExclamationTriangleIcon 
            className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-error-500"
            aria-hidden="true"
          />
        )}
      </div>
      
      {helpText && (
        <p id={helpId} className="text-sm text-neutral-400">
          {helpText}
        </p>
      )}
      
      {error && (
        <p 
          id={errorId} 
          role="alert"
          className="text-sm text-error-500 flex items-center"
        >
          <ExclamationTriangleIcon 
            className="w-4 h-4 mr-1 flex-shrink-0" 
            aria-hidden="true" 
          />
          {error}
        </p>
      )}
    </div>
  );
};
```

---

## Performance Optimization

### Loading Strategy

#### Progressive Loading Implementation
```tsx
// Lazy Loading for Route Components
const AdminDashboard = lazy(() => import('./pages/AdminDashboard'));
const ShipmentManagement = lazy(() => import('./pages/ShipmentManagement'));
const MasterDataManagement = lazy(() => import('./pages/MasterDataManagement'));
const DriverMobile = lazy(() => import('./pages/DriverMobile'));

// Code splitting with Suspense boundaries
const AppRoutes = () => (
  <Router>
    <Routes>
      <Route path="/admin/*" element={
        <Suspense fallback={<AdminDashboardSkeleton />}>
          <AdminDashboard />
        </Suspense>
      } />
      
      <Route path="/shipments/*" element={
        <Suspense fallback={<ShipmentManagementSkeleton />}>
          <ShipmentManagement />
        </Suspense>
      } />
      
      <Route path="/driver/*" element={
        <Suspense fallback={<DriverMobileSkeleton />}>
          <DriverMobile />
        </Suspense>
      } />
    </Routes>
  </Router>
);
```

#### Image Optimization for Mobile
```tsx
// Optimized Image Component for Status Photos
const OptimizedImage = ({ 
  src, 
  alt, 
  className, 
  priority = false,
  quality = 75 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  
  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-primary-700 animate-pulse rounded-lg" />
      )}
      
      <img
        src={src}
        alt={alt}
        className={`${className} transition-opacity duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
        loading={priority ? 'eager' : 'lazy'}
        onLoad={() => setIsLoading(false)}
        onError={() => setError(true)}
        style={{
          maxWidth: '100%',
          height: 'auto',
        }}
      />
      
      {error && (
        <div className="absolute inset-0 bg-primary-700 flex items-center justify-center rounded-lg">
          <PhotoIcon className="w-8 h-8 text-neutral-400" />
        </div>
      )}
    </div>
  );
};

// Progressive Enhancement for Photo Upload
const PhotoUploadComponent = () => {
  const [photos, setPhotos] = useState([]);
  const [uploading, setUploading] = useState(false);
  
  const handlePhotoCapture = async (file) => {
    // Create thumbnail immediately for UI responsiveness
    const thumbnail = await createThumbnail(file, { width: 150, height: 150 });
    
    // Add to UI immediately with loading state
    const tempPhoto = {
      id: Date.now(),
      file,
      thumbnail,
      uploading: true,
      uploaded: false
    };
    
    setPhotos(prev => [...prev, tempPhoto]);
    
    // Upload in background
    try {
      const uploadedUrl = await uploadToSupabase(file);
      setPhotos(prev => prev.map(photo => 
        photo.id === tempPhoto.id 
          ? { ...photo, url: uploadedUrl, uploading: false, uploaded: true }
          : photo
      ));
    } catch (error) {
      setPhotos(prev => prev.map(photo => 
        photo.id === tempPhoto.id 
          ? { ...photo, uploading: false, error: true }
          : photo
      ));
    }
  };
  
  return (
    <div className="space-y-4">
      <Button 
        onClick={activateCamera}
        className="w-full bg-accent-500 hover:bg-accent-400 min-h-[44px]"
        disabled={uploading || photos.length >= 5}
      >
        <CameraIcon className="w-5 h-5 mr-2" />
        Capture Photo ({photos.length}/5)
      </Button>
      
      <div className="grid grid-cols-3 gap-2">
        {photos.map(photo => (
          <div key={photo.id} className="relative aspect-square">
            <OptimizedImage 
              src={photo.thumbnail} 
              alt="Captured photo"
              className="w-full h-full object-cover rounded-lg"
            />
            
            {photo.uploading && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
                <Loader2 className="w-6 h-6 text-white animate-spin" />
              </div>
            )}
            
            {photo.uploaded && (
              <div className="absolute top-1 right-1">
                <CheckCircleIcon className="w-5 h-5 text-green-500" />
              </div>
            )}
            
            {photo.error && (
              <div className="absolute top-1 right-1">
                <XCircleIcon className="w-5 h-5 text-red-500" />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
```

#### Offline Capability Implementation
```tsx
// Service Worker Integration for PWA
const useOfflineSync = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pendingUpdates, setPendingUpdates] = useState([]);
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // Register service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          console.log('SW registered:', registration);
        })
        .catch(error => {
          console.log('SW registration failed:', error);
        });
    }
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  const queueUpdate = useCallback((updateData) => {
    const queuedUpdate = {
      id: Date.now(),
      data: updateData,
      timestamp: new Date().toISOString(),
      type: 'status_update'
    };
    
    setPendingUpdates(prev => [...prev, queuedUpdate]);
    
    // Store in localStorage for persistence
    const stored = JSON.parse(localStorage.getItem('pendingUpdates') || '[]');
    localStorage.setItem('pendingUpdates', JSON.stringify([...stored, queuedUpdate]));
    
    // If online, try to sync immediately
    if (isOnline) {
      syncPendingUpdates();
    }
  }, [isOnline]);
  
  const syncPendingUpdates = useCallback(async () => {
    if (!isOnline || pendingUpdates.length === 0) return;
    
    const stored = JSON.parse(localStorage.getItem('pendingUpdates') || '[]');
    
    for (const update of stored) {
      try {
        await api.updateShipmentStatus(update.data);
        
        // Remove from pending after successful sync
        const remaining = stored.filter(item => item.id !== update.id);
        localStorage.setItem('pendingUpdates', JSON.stringify(remaining));
        setPendingUpdates(remaining);
        
        // Show success notification
        toast.success(`Status update synced: ${update.data.shipmentNumber}`);
        
      } catch (error) {
        console.error('Sync failed for update:', update.id, error);
        // Keep in queue for retry
      }
    }
  }, [isOnline, pendingUpdates]);
  
  // Auto-sync when coming back online
  useEffect(() => {
    if (isOnline) {
      syncPendingUpdates();
    }
  }, [isOnline, syncPendingUpdates]);
  
  return {
    isOnline,
    pendingUpdates,
    queueUpdate,
    syncPendingUpdates
  };
};

// Offline-First Status Update Component
const OfflineStatusUpdate = () => {
  const { isOnline, queueUpdate } = useOfflineSync();
  const [statusData, setStatusData] = useState({});
  
  const handleSubmit = async (formData) => {
    if (isOnline) {
      try {
        await api.updateShipmentStatus(formData);
        toast.success('Status updated successfully');
        router.back();
      } catch (error) {
        toast.error('Update failed, queuing for later sync');
        queueUpdate(formData);
      }
    } else {
      queueUpdate(formData);
      toast.info('Update queued - will sync when online');
      router.back();
    }
  };
  
  return (
    <div className="space-y-4">
      {!isOnline && (
        <Alert className="bg-warning-500/10 border-warning-500">
          <WifiOffIcon className="w-4 h-4" />
          <AlertTitle>Offline Mode</AlertTitle>
          <AlertDescription>
            Updates will be queued and synced when connection is restored
          </AlertDescription>
        </Alert>
      )}
      
      <StatusUpdateForm 
        onSubmit={handleSubmit}
        offlineMode={!isOnline}
      />
    </div>
  );
};
```

---

## Implementation Roadmap

### Phase 1: Foundation & Core Components (Weeks 1-2)

#### Week 1: Design System Setup
- [ ] Initialize ShadCN UI with dark blue theme customization
- [ ] Implement color palette and typography system
- [ ] Create base component library (Button, Card, Input, Select)
- [ ] Set up responsive breakpoints and spacing system
- [ ] Implement accessibility helpers and focus management

#### Week 2: Authentication & Navigation
- [ ] Build role-based navigation components
- [ ] Create mobile navigation patterns (hamburger menu, bottom tabs)
- [ ] Implement authentication UI flows
- [ ] Design skeleton loading components
- [ ] Set up error handling and validation patterns

### Phase 2: Core Interface Development (Weeks 3-6)

#### Week 3: Admin Dashboard
- [ ] Master data management interfaces
- [ ] Dashboard grid layout with responsive behavior
- [ ] Statistics cards and activity feed
- [ ] System status indicators

#### Week 4: CS Shipment Management
- [ ] Intelligent shipment creation form with cascading selection
- [ ] Customer→Shipper/Product pre-population logic
- [ ] Consignee→Notify Party automation
- [ ] Form validation and error handling

#### Week 5: Shipment Tracking & Management
- [ ] Shipment list with advanced filtering
- [ ] Shipment detail views
- [ ] Status update interfaces
- [ ] Container and product management

#### Week 6: Master Data Interfaces
- [ ] Company management with type-specific forms
- [ ] Product and port management
- [ ] Driver management and photo upload
- [ ] Relationship configuration interfaces

### Phase 3: Mobile-First Driver Interface (Weeks 7-8)

#### Week 7: Mobile Dashboard & Navigation
- [ ] Driver assignment dashboard
- [ ] Mobile navigation patterns
- [ ] Pull-to-refresh functionality
- [ ] Touch-optimized interface elements

#### Week 8: Status Updates & Offline Capability
- [ ] Mobile status update workflow
- [ ] Photo capture integration
- [ ] GPS location services
- [ ] Offline sync implementation
- [ ] PWA configuration

### Phase 4: Advanced Features & Polish (Weeks 9-10)

#### Week 9: Document Management & Communication
- [ ] Document generation interfaces
- [ ] Notification center
- [ ] Multi-channel communication preferences
- [ ] Real-time status updates

#### Week 10: Performance & Accessibility
- [ ] Performance optimization (lazy loading, image optimization)
- [ ] Full accessibility testing and compliance
- [ ] Cross-browser testing
- [ ] Mobile device testing
- [ ] User acceptance testing

### Phase 5: Testing & Deployment (Weeks 11-12)

#### Week 11: Comprehensive Testing
- [ ] End-to-end workflow testing
- [ ] Accessibility compliance verification
- [ ] Performance benchmarking
- [ ] Mobile PWA testing
- [ ] Offline functionality validation

#### Week 12: Deployment & Documentation
- [ ] Production deployment preparation
- [ ] User training materials
- [ ] Technical documentation
- [ ] Performance monitoring setup
- [ ] Go-live support

---

## Quality Assurance

### Testing Strategy

#### Accessibility Testing Protocol
```bash
# Automated Accessibility Testing
npm install --save-dev @axe-core/react jest-axe
npm install --save-dev @testing-library/jest-dom

# Manual Testing Checklist
# ✅ Screen reader compatibility (NVDA, JAWS, VoiceOver)
# ✅ Keyboard navigation completeness
# ✅ Color contrast validation
# ✅ Focus management
# ✅ ARIA label accuracy
```

#### Performance Testing Benchmarks
```bash
# Core Web Vitals Targets
# LCP (Largest Contentful Paint): < 2.5 seconds
# FID (First Input Delay): < 100 milliseconds  
# CLS (Cumulative Layout Shift): < 0.1

# Mobile Performance Tests
# 3G Network: < 3 seconds page load
# Slow 3G: < 5 seconds page load
# Offline Mode: < 1 second cache response
```

#### Cross-Browser Testing Matrix
```yaml
Desktop Browsers:
  - Chrome: Latest 2 versions
  - Firefox: Latest 2 versions  
  - Safari: Latest 2 versions
  - Edge: Latest 2 versions

Mobile Browsers:
  - iOS Safari: Latest 2 versions
  - Chrome Mobile: Latest 2 versions
  - Samsung Internet: Latest version

Device Testing:
  - iPhone 12/13/14 (various sizes)
  - Samsung Galaxy S21/S22
  - iPad (9th/10th generation)
  - Desktop: 1920x1080, 1366x768
```

### Success Metrics

#### User Experience KPIs
- **Coordination Time Reduction:** 50% decrease in manual workflows
- **Data Entry Efficiency:** 60% reduction through pre-population
- **Mobile Task Completion:** 95% success rate for driver status updates
- **Document Generation Speed:** < 30 seconds for complete export documents
- **User Error Rate:** < 5% for critical workflows

#### Technical Performance KPIs
- **Page Load Time:** < 2 seconds on desktop, < 3 seconds on mobile 3G
- **Accessibility Score:** 100% WCAG 2.1 AA compliance
- **PWA Performance:** Lighthouse score > 90 for all metrics
- **Offline Functionality:** 100% status update success rate when offline
- **Cross-Browser Compatibility:** 100% feature parity across supported browsers

#### User Satisfaction Metrics
- **Net Promoter Score (NPS):** Target > 50
- **Task Success Rate:** > 95% for core workflows
- **User Onboarding Time:** < 30 minutes for new driver training
- **Support Ticket Reduction:** 30% decrease in UI-related support requests
- **Mobile Adoption Rate:** > 90% of drivers using mobile interface within 30 days

---

## Conclusion

This comprehensive UX specification provides the foundation for building a modern, efficient, and accessible fruit export management system that meets the unique needs of logistics operations. The design system prioritizes:

1. **Operational Efficiency:** Through intelligent cascading selection, pre-population workflows, and streamlined interfaces that support the 50% coordination time reduction goal

2. **Mobile-First Excellence:** With touch-optimized interfaces, offline capabilities, and PWA technology that enables reliable field operations

3. **Accessibility & Inclusion:** Through WCAG 2.1 AA compliance, comprehensive keyboard navigation, and support for diverse user needs

4. **Professional Aesthetics:** Using the dark blue logistics theme with strategic orange accents optimized for extended use and outdoor visibility

5. **Technical Performance:** With sub-2 second load times, real-time updates, and robust offline sync capabilities

The implementation roadmap provides a structured 12-week development path that builds from foundation components through advanced features, ensuring systematic delivery of business value while maintaining quality standards throughout the development process.

This specification serves as the definitive guide for designers, developers, and stakeholders to create a logistics management system that transforms manual coordination processes into an efficient, digital-first operation that meets the demanding requirements of international fruit export business.