'use client'

import { useEffect, useCallback } from 'react'
import {
  useCompanyStore,
  useCompanies,
  useCompaniesLoading,
  useCompaniesError,
  useCompanyActions,
  type Company,
  type CompanyInsert,
  type CompanyUpdate,
} from '@/stores/company-store'
import type { CompanyFilter, Coordinates } from '@/lib/validations/companies'

export function useCompaniesData() {
  const companies = useCompanies()
  const loading = useCompaniesLoading()
  const error = useCompaniesError()
  const {
    fetchCompanies,
    fetchAllCompaniesForDropdown,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    clearError,
    subscribeToCompanies,
  } = useCompanyActions()

  // Pagination and filtering state
  const currentPage = useCompanyStore(state => state.currentPage)
  const pageSize = useCompanyStore(state => state.pageSize)
  const totalCount = useCompanyStore(state => state.totalCount)
  const filter = useCompanyStore(state => state.filter)
  const searchTerm = useCompanyStore(state => state.searchTerm)
  const sortBy = useCompanyStore(state => state.sortBy)
  const sortOrder = useCompanyStore(state => state.sortOrder)

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / pageSize)
  const hasNextPage = currentPage < totalPages
  const hasPreviousPage = currentPage > 1

  // Load companies on mount and setup real-time subscription
  useEffect(() => {
    fetchCompanies()

    // Setup real-time subscription
    const unsubscribe = subscribeToCompanies()

    return () => {
      unsubscribe()
    }
  }, [fetchCompanies, subscribeToCompanies])

  // Filtered companies for dropdown/selection purposes
  const activeCompanies = companies.filter(company => company.is_active)

  const companiesByType = companies.reduce(
    (acc, company) => {
      const type = company.company_type
      if (!acc[type]) acc[type] = []
      acc[type].push(company)
      return acc
    },
    {} as Record<string, typeof companies>
  )


  // Complex vs simple company types
  const complexCompanies = companies.filter(company =>
    ['customer', 'carrier', 'factory'].includes(company.company_type)
  )

  const simpleCompanies = companies.filter(company =>
    ['shipper', 'consignee', 'notify_party', 'forwarder_agent'].includes(
      company.company_type
    )
  )

  return {
    // Data
    companies,
    activeCompanies,
    complexCompanies,
    simpleCompanies,
    companiesByType,
    loading,
    error,

    // Pagination
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    hasNextPage,
    hasPreviousPage,

    // Filters and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Actions
    fetchCompanies,
    fetchAllCompaniesForDropdown,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage: (page: number) => setPage(Math.max(1, Math.min(page, totalPages))),
    nextPage: () => hasNextPage && setPage(currentPage + 1),
    previousPage: () => hasPreviousPage && setPage(currentPage - 1),
    clearError,

    // Utility functions
    refreshCompanies: fetchCompanies,
    refreshAllCompaniesForDropdown: fetchAllCompaniesForDropdown,
    getCompanyById: (id: string) =>
      companies.find(company => company.id === id),
    getCompanyByName: (name: string) =>
      companies.find(
        company => company.name.toLowerCase() === name.toLowerCase()
      ),
  }
}

export function useCompanyCRUD() {
  const {
    createCompany,
    updateCompany,
    deleteCompany,
    deleteCompanies,
    fetchCompanyById,
  } = useCompanyActions()

  const isCreating = useCompanyStore(state => state.isCreating)
  const isUpdating = useCompanyStore(state => state.isUpdating)
  const isDeleting = useCompanyStore(state => state.isDeleting)

  const handleCreate = useCallback(
    async (companyData: CompanyInsert): Promise<Company> => {
      try {
        const result = await createCompany(companyData)
        return result
      } catch (error) {
        console.error('Failed to create company:', error)
        throw error
      }
    },
    [createCompany]
  )

  const handleUpdate = useCallback(
    async (id: string, updates: Partial<CompanyUpdate>): Promise<Company> => {
      try {
        const result = await updateCompany(id, updates)
        return result
      } catch (error) {
        console.error('Failed to update company:', error)
        throw error
      }
    },
    [updateCompany]
  )

  const handleDelete = useCallback(
    async (id: string): Promise<void> => {
      try {
        await deleteCompany(id)
      } catch (error) {
        console.error('Failed to delete company:', error)
        throw error
      }
    },
    [deleteCompany]
  )

  const handleBulkDelete = useCallback(
    async (ids: string[]): Promise<void> => {
      try {
        await deleteCompanies(ids)
      } catch (error) {
        console.error('Failed to delete companies:', error)
        throw error
      }
    },
    [deleteCompanies]
  )

  const handleFetchById = useCallback(
    async (id: string) => {
      try {
        return await fetchCompanyById(id)
      } catch (error) {
        console.error('Failed to fetch company:', error)
        throw error
      }
    },
    [fetchCompanyById]
  )

  return {
    // Loading states
    isCreating,
    isUpdating,
    isDeleting,

    // CRUD operations
    createCompany: handleCreate,
    updateCompany: handleUpdate,
    deleteCompany: handleDelete,
    bulkDeleteCompanies: handleBulkDelete,
    fetchCompanyById: handleFetchById,

    // Utility
    isLoading: isCreating || isUpdating || isDeleting,
  }
}

export function useCompanySelection() {
  const selectedCompanies = useCompanyStore(state => state.selectedCompanies)
  const { selectCompany, deselectCompany, clearSelection } = useCompanyActions()

  const selectAllCompanies = useCompanyStore(state => state.selectAllCompanies)
  const companies = useCompanies()

  const selectedIds = Array.from(selectedCompanies)
  const selectedCount = selectedCompanies.size
  const isSelected = (id: string) => selectedCompanies.has(id)
  const isAllSelected =
    companies.length > 0 && selectedCompanies.size === companies.length
  const isPartiallySelected =
    selectedCompanies.size > 0 && selectedCompanies.size < companies.length

  const toggleCompany = useCallback(
    (id: string) => {
      if (selectedCompanies.has(id)) {
        deselectCompany(id)
      } else {
        selectCompany(id)
      }
    },
    [selectedCompanies, selectCompany, deselectCompany]
  )

  const toggleAll = useCallback(() => {
    if (isAllSelected) {
      clearSelection()
    } else {
      selectAllCompanies()
    }
  }, [isAllSelected, clearSelection, selectAllCompanies])

  return {
    selectedCompanies: selectedIds,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,
    selectCompany,
    deselectCompany,
    toggleCompany,
    selectAllCompanies,
    toggleAll,
    clearSelection,
  }
}

// Validation hooks
export function useCompanyValidation() {
  const companies = useCompanies()

  const validateUniqueName = useCallback(
    (name: string, companyType: string, excludeId?: string) => {
      if (!name) return { isValid: false, message: 'Company name is required' }

      const existing = companies.find(
        company =>
          company.name.toLowerCase() === name.toLowerCase() &&
          company.company_type === companyType &&
          company.is_active &&
          company.id !== excludeId
      )

      return {
        isValid: !existing,
        message: existing
          ? `Company with name "${name}" already exists as ${companyType}`
          : undefined,
      }
    },
    [companies]
  )

  const validateCompanyName = useCallback((name: string) => {
    if (!name) return { isValid: false, message: 'Company name is required' }

    if (name.length < 2) {
      return {
        isValid: false,
        message: 'Company name must be at least 2 characters long',
      }
    }

    if (name.length > 100) {
      return {
        isValid: false,
        message: 'Company name must be 100 characters or less',
      }
    }

    return { isValid: true }
  }, [])

  const validateTaxId = useCallback((taxId: string) => {
    if (!taxId) return { isValid: true } // Optional field

    if (taxId.length < 3) {
      return {
        isValid: false,
        message: 'Tax ID must be at least 3 characters long',
      }
    }

    return { isValid: true }
  }, [])

  const validateEmail = useCallback((email: string) => {
    if (!email) return { isValid: true } // Optional field

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    if (!emailRegex.test(email)) {
      return {
        isValid: false,
        message: 'Must be a valid email address',
      }
    }

    return { isValid: true }
  }, [])

  const validatePhoneNumber = useCallback((phone: string) => {
    if (!phone) return { isValid: true } // Optional field

    const phoneRegex = /^[+]?[\d\s-()]+$/

    if (!phoneRegex.test(phone)) {
      return {
        isValid: false,
        message:
          'Phone number must contain only digits, spaces, hyphens, parentheses, and plus sign',
      }
    }

    return { isValid: true }
  }, [])

  const validateCoordinates = useCallback((lat: number, lng: number) => {
    const errors: string[] = []

    if (lat < -90 || lat > 90) {
      errors.push('Latitude must be between -90 and 90')
    }

    if (lng < -180 || lng > 180) {
      errors.push('Longitude must be between -180 and 180')
    }

    return {
      isValid: errors.length === 0,
      message: errors.join(', '),
    }
  }, [])

  const validateFactoryCode = useCallback(
    (code: string, excludeId?: string) => {
      if (!code) return { isValid: false, message: 'Factory code is required' }

      // Check uniqueness in factory_info table (would need to query)
      // For now, just basic validation
      if (code.length < 1) {
        return {
          isValid: false,
          message: 'Factory code is required',
        }
      }

      if (code.length > 20) {
        return {
          isValid: false,
          message: 'Factory code must be 20 characters or less',
        }
      }

      return { isValid: true }
    },
    []
  )

  const validateCarrierCode = useCallback(
    (code: string, excludeId?: string) => {
      if (!code) return { isValid: true } // Optional field

      if (code.length > 20) {
        return {
          isValid: false,
          message: 'Carrier code must be 20 characters or less',
        }
      }

      return { isValid: true }
    },
    []
  )

  return {
    validateUniqueName,
    validateCompanyName,
    validateTaxId,
    validateEmail,
    validatePhoneNumber,
    validateCoordinates,
    validateFactoryCode,
    validateCarrierCode,
  }
}

// Company types and categories management
export function useCompanyCategories() {
  const { companiesByType } = useCompaniesData()

  const createCompanyTypeOptions = useCallback(() => {
    return [
      { value: 'customer', label: 'Customer', complex: true },
      { value: 'carrier', label: 'Carrier', complex: true },
      { value: 'factory', label: 'Factory', complex: true },
      { value: 'shipper', label: 'Shipper', complex: false },
      { value: 'consignee', label: 'Consignee', complex: false },
      { value: 'notify_party', label: 'Notify Party', complex: false },
      { value: 'forwarder_agent', label: 'Forwarder Agent', complex: false },
    ]
  }, [])

  const createCustomerTypeOptions = useCallback(() => {
    return [
      { value: 'regular', label: 'Regular' },
      { value: 'premium', label: 'Premium' },
      { value: 'vip', label: 'VIP' },
    ]
  }, [])

  const createIncotermsOptions = useCallback(() => {
    return [
      { value: 'FOB', label: 'FOB (Free on Board)' },
      { value: 'CIF', label: 'CIF (Cost, Insurance & Freight)' },
      { value: 'EXW', label: 'EXW (Ex Works)' },
      { value: 'CFR', label: 'CFR (Cost & Freight)' },
    ]
  }, [])

  const getCompanyTypeLabel = useCallback(
    (type: string) => {
      const options = createCompanyTypeOptions()
      return options.find(option => option.value === type)?.label || type
    },
    [createCompanyTypeOptions]
  )

  const getCustomerTypeLabel = useCallback(
    (type: string) => {
      const options = createCustomerTypeOptions()
      return options.find(option => option.value === type)?.label || type
    },
    [createCustomerTypeOptions]
  )

  const getIncotermsLabel = useCallback(
    (incoterms: string) => {
      const options = createIncotermsOptions()
      return (
        options.find(option => option.value === incoterms)?.label || incoterms
      )
    },
    [createIncotermsOptions]
  )

  const isComplexCompanyType = useCallback((type: string) => {
    return ['customer', 'carrier', 'factory'].includes(type)
  }, [])

  return {
    companiesByType,
    companyTypeOptions: createCompanyTypeOptions(),
    customerTypeOptions: createCustomerTypeOptions(),
    incotermsOptions: createIncotermsOptions(),
    getCompanyTypeLabel,
    getCustomerTypeLabel,
    getIncotermsLabel,
    isComplexCompanyType,
  }
}

// Coordinate utilities (reused from ports)
export function useCoordinateUtils() {
  const formatCoordinates = useCallback((lat: number, lng: number) => {
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  }, [])

  const formatCoordinatesDMS = useCallback((lat: number, lng: number) => {
    const formatDMS = (decimal: number, isLatitude: boolean) => {
      const absolute = Math.abs(decimal)
      const degrees = Math.floor(absolute)
      const minutesFloat = (absolute - degrees) * 60
      const minutes = Math.floor(minutesFloat)
      const seconds = ((minutesFloat - minutes) * 60).toFixed(2)

      const direction = isLatitude
        ? decimal >= 0
          ? 'N'
          : 'S'
        : decimal >= 0
          ? 'E'
          : 'W'

      return `${degrees}°${minutes}'${seconds}"${direction}`
    }

    return `${formatDMS(lat, true)} ${formatDMS(lng, false)}`
  }, [])

  const parseCoordinates = useCallback(
    (gpsCoordinates: string | object | null): Coordinates | null => {
      if (!gpsCoordinates) return null

      try {
        // Handle different PostGIS geography formats that Supabase might return
        const coordString =
          typeof gpsCoordinates === 'object'
            ? JSON.stringify(gpsCoordinates)
            : gpsCoordinates.toString()

        // ST_AsText returns format: POINT(lng lat)
        const pointMatch = coordString.match(
          /POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
        )
        if (pointMatch) {
          return {
            lng: parseFloat(pointMatch[1]),
            lat: parseFloat(pointMatch[2]),
          }
        }

        // Handle WKT format with SRID: SRID=4326;POINT(lng lat)
        const sridMatch = coordString.match(
          /SRID=\d+;POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
        )
        if (sridMatch) {
          return {
            lng: parseFloat(sridMatch[1]),
            lat: parseFloat(sridMatch[2]),
          }
        }

        // Supabase might return as JSON: {"type":"Point","coordinates":[lng,lat]}
        const jsonMatch = coordString.match(/^\{.*\}$/)
        if (jsonMatch) {
          const parsed = JSON.parse(coordString)
          if (
            parsed.type === 'Point' &&
            parsed.coordinates &&
            parsed.coordinates.length === 2
          ) {
            return {
              lng: parsed.coordinates[0],
              lat: parsed.coordinates[1],
            }
          }
        }

        return null
      } catch (error) {
        console.error('Failed to parse coordinates:', error)
        return null
      }
    },
    []
  )

  const parseCoordinatesFromString = useCallback(
    (coordString: string): Coordinates | null => {
      try {
        // Try comma-separated format: "lat, lng"
        const parts = coordString.split(',').map(s => s.trim())
        if (parts.length === 2) {
          const lat = parseFloat(parts[0])
          const lng = parseFloat(parts[1])

          if (!isNaN(lat) && !isNaN(lng)) {
            return { lat, lng }
          }
        }
        return null
      } catch {
        return null
      }
    },
    []
  )

  return {
    formatCoordinates,
    formatCoordinatesDMS,
    parseCoordinates,
    parseCoordinatesFromString,
  }
}

// Constants
export const DEFAULT_COMPANY_FILTER: CompanyFilter = {
  is_active: true,
}

// Common company statuses for display
export const COMPANY_STATUSES = [
  { value: true, label: 'Active', variant: 'default' as const },
  { value: false, label: 'Inactive', variant: 'secondary' as const },
] as const

export const COMPANY_TYPE_BADGES = {
  customer: { label: 'Customer', className: 'bg-blue-500 hover:bg-blue-600' },
  carrier: { label: 'Carrier', className: 'bg-green-500 hover:bg-green-600' },
  factory: { label: 'Factory', className: 'bg-purple-500 hover:bg-purple-600' },
  shipper: { label: 'Shipper', className: 'bg-yellow-500 hover:bg-yellow-600' },
  consignee: {
    label: 'Consignee',
    className: 'bg-orange-500 hover:bg-orange-600',
  },
  notify_party: {
    label: 'Notify Party',
    className: 'bg-pink-500 hover:bg-pink-600',
  },
  forwarder_agent: {
    label: 'Forwarder Agent',
    className: 'bg-indigo-500 hover:bg-indigo-600',
  },
} as const

export const LICENSE_TYPES = [
  'Commercial Transport',
  'Hazardous Materials',
  'Refrigerated Transport',
  'Container Transport',
  'Heavy Machinery',
  'International Transport',
] as const

export const COVERAGE_AREAS = [
  'Bangkok Metropolitan',
  'Central Region',
  'Northern Region',
  'Northeastern Region',
  'Eastern Region',
  'Western Region',
  'Southern Region',
  'International',
] as const

export const CERTIFICATIONS = [
  'HACCP',
  'ISO22000',
  'GMP',
  'BRC',
  'IFS',
  'Organic',
  'Halal',
  'FDA',
] as const

export const SPECIALIZATIONS = [
  'durian',
  'mangosteen',
  'longan',
  'rambutan',
  'coconut',
  'pineapple',
  'mango',
  'dragon_fruit',
  'frozen_fruits',
  'dried_fruits',
] as const

// Hook specifically for dropdown/selection usage - fetches all companies
export function useCompaniesForDropdown() {
  const companies = useCompanies()
  const loading = useCompaniesLoading()
  const error = useCompaniesError()
  const isAllCompaniesLoaded = useCompanyStore(state => state.isAllCompaniesLoaded)
  const { fetchAllCompaniesForDropdown, subscribeToCompanies } = useCompanyActions()

  // Load all companies for dropdown on mount - ONLY if not already loaded
  useEffect(() => {
    // Only fetch if we don't already have all companies loaded
    if (!isAllCompaniesLoaded) {
      fetchAllCompaniesForDropdown()
    }

    // Setup real-time subscription
    const unsubscribe = subscribeToCompanies()

    return () => {
      unsubscribe()
    }
  }, [fetchAllCompaniesForDropdown, subscribeToCompanies, isAllCompaniesLoaded])

  // Filtered companies for dropdown/selection purposes
  const activeCompanies = companies.filter(company => company.is_active)

  const companiesByType = companies.reduce(
    (acc, company) => {
      const type = company.company_type
      if (!acc[type]) acc[type] = []
      acc[type].push(company)
      return acc
    },
    {} as Record<string, typeof companies>
  )

  return {
    companies,
    activeCompanies,
    companiesByType,
    loading,
    error,
    refreshAll: fetchAllCompaniesForDropdown,
  }
}

// Main hook that combines all functionality
export function useCompaniesManagement() {
  const data = useCompaniesData()
  const crud = useCompanyCRUD()
  const selection = useCompanySelection()
  const validation = useCompanyValidation()
  const categories = useCompanyCategories()
  const coordinates = useCoordinateUtils()

  return {
    ...data,
    ...crud,
    ...selection,
    ...validation,
    ...categories,
    ...coordinates,

    // Convenience methods
    resetFilters: () => data.setFilter(DEFAULT_COMPANY_FILTER),
    clearSearch: () => data.setSearchTerm(''),
  }
}
