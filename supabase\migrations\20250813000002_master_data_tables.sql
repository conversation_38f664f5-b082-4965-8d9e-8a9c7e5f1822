-- Master Data Tables
-- Story 1.2: Database Schema & RLS Foundation
-- This migration creates user profiles and master data tables with hybrid company design

-- ============================================================================
-- PROFILES TABLE (Extending Supabase Auth)
-- ============================================================================

-- User profiles extending auth.users with role and company association
CREATE TABLE profiles (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id),
    email TEXT UNIQUE NOT NULL,
    first_name TEXT,
    last_name TEXT,
    phone_number TEXT,
    line_id TEXT,
    wechat_id TEXT,
    role role_type NOT NULL,
    company_id UUID REFERENCES companies(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- UNITS OF MEASURE TABLE
-- ============================================================================

-- Standardized measurement units (base unit: KG)
CREATE TABLE units_of_measure (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code TEXT UNIQUE NOT NULL,        -- 'KG', 'PCS', 'LTR', 'MTR', 'TON', 'BOX', 'CTN'
    name TEXT NOT NULL,               -- 'Kilogram', 'Pieces', 'Liter', 'Meter', 'Ton', 'Box', 'Carton'
    category TEXT,                    -- 'weight', 'count', 'volume', 'length'
    symbol TEXT,                      -- 'kg', 'pcs', 'L', 'm', 't', 'box', 'ctn'
    conversion_factor NUMERIC(10,4),  -- For unit conversions (base unit = 1)
    base_unit_id UUID REFERENCES units_of_measure(id), -- Reference to base unit for conversions
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- PORTS TABLE
-- ============================================================================

-- Ports for origin/destination tracking
CREATE TABLE ports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    country TEXT NOT NULL,
    port_type port_type_enum NOT NULL,
    coordinates POINT,
    is_active BOOLEAN DEFAULT true
);

-- ============================================================================
-- COMPANIES TABLE (Hybrid Design)
-- ============================================================================

-- Companies table with hybrid approach (base table + JSONB metadata)
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    company_type company_type_enum NOT NULL,
    
    -- Common fields that ALL types have
    tax_id TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    contact_fax TEXT,
    contact_person_first_name TEXT,
    contact_person_last_name TEXT,
    
    -- Address with multi-language support and coordinates
    address JSONB, -- {"street": {"th": "...", "en": "..."}, "coordinates": {"lat": 13.7563, "lng": 100.5018}}
    gps_coordinates POINT, -- Dedicated column for efficient geographic queries (synced via trigger)
    
    -- Simple company types use this for specific data (shipper, consignee, notify_party, forwarder_agent)
    metadata JSONB, -- ONLY for simple company types, complex types use separate tables
    
    -- Common fields
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Constraints: Complex types should have NULL metadata
    CONSTRAINT valid_metadata_structure CHECK (validate_company_metadata(company_type, metadata))
);

-- ============================================================================
-- PRODUCTS TABLE
-- ============================================================================

-- Products with standardized measurement units
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    code TEXT,
    description TEXT,
    category TEXT,
    hs_code TEXT,
    unit_of_measure_id UUID REFERENCES units_of_measure(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- ============================================================================
-- DRIVERS TABLE
-- ============================================================================

-- Drivers linked to carrier companies only
CREATE TABLE drivers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    carrier_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    driver_first_name TEXT NOT NULL,
    driver_last_name TEXT NOT NULL,
    driver_code TEXT,
    phone TEXT,
    line_id TEXT,
    driver_picture_path TEXT,
    driver_picture_mime_type TEXT,
    notes TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- ============================================================================
-- UPDATE PROFILES TABLE TO REFERENCE COMPANIES
-- ============================================================================

-- Note: Foreign key constraint for company_id is already included in table definition above

-- Add constraint to validate role-company relationships
ALTER TABLE profiles ADD CONSTRAINT valid_role_company_relationship CHECK (
    (role IN ('admin', 'cs', 'account') AND company_id IS NULL) OR
    (role NOT IN ('admin', 'cs', 'account') AND company_id IS NOT NULL)
);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_units_of_measure_updated_at 
    BEFORE UPDATE ON units_of_measure 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ports_updated_at 
    BEFORE UPDATE ON ports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at 
    BEFORE UPDATE ON companies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drivers_updated_at 
    BEFORE UPDATE ON drivers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- GPS COORDINATE SYNC TRIGGER
-- ============================================================================

-- Function to sync GPS coordinates from JSONB address to point column
CREATE OR REPLACE FUNCTION sync_gps_coordinates()
RETURNS TRIGGER AS $$
BEGIN
    -- Update point column when JSONB coordinates change
    IF NEW.address->'coordinates' IS NOT NULL THEN
        NEW.gps_coordinates = point(
            (NEW.address->'coordinates'->>'lng')::float,
            (NEW.address->'coordinates'->>'lat')::float
        );
    ELSE
        NEW.gps_coordinates = NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply GPS sync trigger to companies table
CREATE TRIGGER sync_companies_gps_coordinates 
    BEFORE INSERT OR UPDATE ON companies 
    FOR EACH ROW EXECUTE FUNCTION sync_gps_coordinates();

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Profiles table indexes
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_company_id ON profiles(company_id);
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_active ON profiles(is_active);

-- Companies table indexes
CREATE INDEX idx_companies_type ON companies(company_type);
CREATE INDEX idx_companies_name ON companies(name);
CREATE INDEX idx_companies_active ON companies(is_active);
CREATE INDEX idx_companies_country ON companies(country_code);
CREATE INDEX idx_companies_gps ON companies USING GIST(gps_coordinates);

-- Products table indexes
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_hs_code ON products(hs_code);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_products_unit_measure ON products(unit_of_measure_id);

-- Drivers table indexes
CREATE INDEX idx_drivers_carrier ON drivers(carrier_id);
CREATE INDEX idx_drivers_code ON drivers(driver_code);
CREATE INDEX idx_drivers_phone ON drivers(phone);
CREATE INDEX idx_drivers_active ON drivers(is_active);

-- Ports table indexes
CREATE INDEX idx_ports_code ON ports(code);
CREATE INDEX idx_ports_country ON ports(country_code);
CREATE INDEX idx_ports_type ON ports(port_type);
CREATE INDEX idx_ports_active ON ports(is_active);

-- Units of measure indexes
CREATE INDEX idx_units_category ON units_of_measure(category);
CREATE INDEX idx_units_active ON units_of_measure(is_active);

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE profiles IS 'User profiles extending Supabase auth with role and company associations';
COMMENT ON TABLE companies IS 'Hybrid company design - base table with JSONB metadata for simple types only';
COMMENT ON TABLE products IS 'Product catalog with standardized measurement units and HS codes';
COMMENT ON TABLE drivers IS 'Driver information linked to carrier companies with mobile app support';
COMMENT ON TABLE ports IS 'Port information for origin/destination tracking with GPS coordinates';
COMMENT ON TABLE units_of_measure IS 'Standardized measurement units with KG as base unit for weight conversions';

COMMENT ON COLUMN companies.metadata IS 'JSONB field used ONLY for simple company types: shipper, consignee, notify_party, forwarder_agent';
COMMENT ON COLUMN companies.gps_coordinates IS 'PostGIS point for efficient spatial queries, synced with lat/lng columns';
COMMENT ON COLUMN drivers.line_id IS 'Line messaging app ID for mobile driver communication';
COMMENT ON COLUMN profiles.company_id IS 'Company association - NULL for admin/cs/account roles, required for others';