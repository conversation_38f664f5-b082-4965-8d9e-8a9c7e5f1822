/**
 * PDF Generation Service
 * Story 5.2: Automated Document Generation Engine
 * 
 * Professional PDF generation using jsPDF and html2canvas for client-side HTML-to-PDF conversion
 * This service renders HTML templates directly to PDF with full CSS support in the browser
 */

import type { DocumentTemplate } from '@/types/document-template'
import type { PDFGenerationOptions } from '@/types/document-generation'

/**
 * PDF generation service using jsPDF and html2canvas for client-side conversion
 * Compatible with Next.js client-side and server-side environments
 */
export class PDFGenerationService {
  private jsPDF: any = null
  private html2canvas: any = null

  constructor() {
    // Libraries will be dynamically imported when needed
  }

  /**
   * Generate PDF buffer from processed HTML content and template
   */
  async generatePDF(content: string, template: DocumentTemplate): Promise<Buffer> {
    try {
      // Load libraries dynamically
      await this.loadLibraries()
      
      // Create a temporary DOM element with the HTML content
      const tempDiv = this.createTempElement(content, template)
      
      try {
        // Convert HTML to canvas using html2canvas
        const canvas = await this.html2canvas(tempDiv, {
          scale: 2, // High quality
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: tempDiv.offsetWidth,
          height: tempDiv.offsetHeight
        })

        // Get page dimensions
        const pageSize = this.getPageDimensions(template.page_size || 'A4')
        const orientation = template.page_orientation || 'portrait'

        // Create PDF with jsPDF
        const pdf = new this.jsPDF({
          orientation,
          unit: 'mm',
          format: template.page_size || 'A4'
        })

        // Calculate scaling to fit page
        const imgWidth = pageSize.width - (template.margin_left || 20) - (template.margin_right || 20)
        const imgHeight = (canvas.height * imgWidth) / canvas.width
        
        // Add image to PDF
        const imgData = canvas.toDataURL('image/png')
        pdf.addImage(
          imgData, 
          'PNG', 
          template.margin_left || 10,
          template.margin_top || 10,
          imgWidth,
          imgHeight
        )

        // Convert to buffer
        const pdfArrayBuffer = pdf.output('arraybuffer')
        const pdfBuffer = Buffer.from(pdfArrayBuffer)

        // Cleanup
        document.body.removeChild(tempDiv)

        return pdfBuffer

      } catch (error) {
        // Cleanup on error
        if (tempDiv.parentNode) {
          document.body.removeChild(tempDiv)
        }
        throw error
      }

    } catch (error) {
      console.error('PDF generation failed:', error)
      throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate PDF with custom options
   */
  async generatePDFWithOptions(options: PDFGenerationOptions): Promise<Buffer> {
    const { template, content, metadata } = options
    
    try {
      // Load libraries
      await this.loadLibraries()
      
      // Create enhanced HTML with metadata
      const enhancedContent = this.addMetadataToContent(content, metadata)
      const tempDiv = this.createTempElement(enhancedContent, template)
      
      try {
        // Convert to canvas
        const canvas = await this.html2canvas(tempDiv, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        })

        // Create PDF with metadata
        const pdf = new this.jsPDF({
          orientation: template.page_orientation || 'portrait',
          unit: 'mm',
          format: template.page_size || 'A4'
        })

        // Add metadata
        if (metadata) {
          pdf.setProperties({
            title: metadata.title || `${template.document_type}_${template.template_name}`,
            author: metadata.author || 'DYY Trading Management System',
            subject: metadata.subject || template.description || `Generated ${template.document_type}`,
            creator: metadata.creator || 'DYY Trading Management System',
            producer: 'jsPDF Generator'
          })
        }

        // Calculate page dimensions
        const pageSize = this.getPageDimensions(template.page_size || 'A4')
        const imgWidth = pageSize.width - (template.margin_left || 20) - (template.margin_right || 20)
        const imgHeight = (canvas.height * imgWidth) / canvas.width

        // Add header if provided
        if (metadata?.headerTemplate) {
          pdf.setFontSize(10)
          pdf.text(metadata.headerTemplate, template.margin_left || 10, 10)
        }

        // Add main content
        const imgData = canvas.toDataURL('image/png')
        const contentY = metadata?.headerTemplate ? 20 : (template.margin_top || 10)
        
        pdf.addImage(imgData, 'PNG', 
          template.margin_left || 10,
          contentY,
          imgWidth,
          imgHeight
        )

        // Add footer if provided
        if (metadata?.footerTemplate) {
          const footerY = pageSize.height - 15
          pdf.setFontSize(8)
          pdf.text(metadata.footerTemplate, template.margin_left || 10, footerY)
        }

        // Convert to buffer
        const pdfArrayBuffer = pdf.output('arraybuffer')
        const pdfBuffer = Buffer.from(pdfArrayBuffer)

        // Cleanup
        document.body.removeChild(tempDiv)
        return pdfBuffer

      } catch (error) {
        if (tempDiv.parentNode) {
          document.body.removeChild(tempDiv)
        }
        throw error
      }

    } catch (error) {
      console.error('PDF generation with options failed:', error)
      throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Load jsPDF and html2canvas libraries dynamically
   */
  private async loadLibraries(): Promise<void> {
    if (!this.jsPDF || !this.html2canvas) {
      try {
        // Dynamic import for client-side compatibility
        const [jsPDFModule, html2canvasModule] = await Promise.all([
          import('jspdf'),
          import('html2canvas')
        ])
        
        this.jsPDF = jsPDFModule.default || jsPDFModule.jsPDF
        this.html2canvas = html2canvasModule.default || html2canvasModule
        
        if (!this.jsPDF || !this.html2canvas) {
          throw new Error('Failed to load PDF generation libraries')
        }
      } catch (error) {
        console.error('Failed to load PDF libraries:', error)
        throw new Error('PDF libraries not available. Make sure jspdf and html2canvas are installed.')
      }
    }
  }

  /**
   * Create temporary DOM element from HTML content
   */
  private createTempElement(content: string, template: DocumentTemplate): HTMLElement {
    const tempDiv = document.createElement('div')
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    tempDiv.style.top = '-9999px'
    tempDiv.style.width = '210mm' // A4 width
    tempDiv.style.backgroundColor = '#ffffff'
    tempDiv.style.padding = '10px'
    
    // Add CSS styles
    const styleEl = document.createElement('style')
    styleEl.textContent = `
      ${this.getDefaultStyles()}
      ${template.template_styles || ''}
    `
    tempDiv.appendChild(styleEl)
    
    // Add content
    const contentEl = document.createElement('div')
    contentEl.innerHTML = content
    tempDiv.appendChild(contentEl)
    
    // Add to DOM temporarily
    document.body.appendChild(tempDiv)
    
    return tempDiv
  }

  /**
   * Add metadata to HTML content
   */
  private addMetadataToContent(content: string, metadata?: any): string {
    if (!metadata?.headerTemplate && !metadata?.footerTemplate) {
      return content
    }
    
    let enhancedContent = content
    
    if (metadata.headerTemplate) {
      enhancedContent = `
        <div class="pdf-header" style="text-align: center; font-size: 10px; margin-bottom: 20px; border-bottom: 1px solid #ddd; padding-bottom: 10px;">
          ${metadata.headerTemplate}
        </div>
        ${enhancedContent}
      `
    }
    
    if (metadata.footerTemplate) {
      enhancedContent = `
        ${enhancedContent}
        <div class="pdf-footer" style="text-align: center; font-size: 8px; margin-top: 20px; border-top: 1px solid #ddd; padding-top: 10px;">
          ${metadata.footerTemplate}
        </div>
      `
    }
    
    return enhancedContent
  }

  /**
   * Get default CSS styles for PDF generation
   */
  private getDefaultStyles(): string {
    return `
      * {
        box-sizing: border-box;
      }
      
      body {
        font-family: Arial, sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        margin: 0;
        padding: 0;
      }
      
      .container {
        width: 100%;
        max-width: 210mm;
        margin: 0 auto;
        padding: 10mm;
      }
      
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
      }
      
      table, th, td {
        border: 1px solid #000;
      }
      
      th, td {
        padding: 4px 6px;
        text-align: left;
        vertical-align: top;
      }
      
      th {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: center;
      }
      
      .text-center { text-align: center; }
      .text-right { text-align: right; }
      .text-left { text-align: left; }
      
      .font-bold { font-weight: bold; }
      .font-normal { font-weight: normal; }
      
      .mb-1 { margin-bottom: 5px; }
      .mb-2 { margin-bottom: 10px; }
      .mb-3 { margin-bottom: 15px; }
      .mt-1 { margin-top: 5px; }
      .mt-2 { margin-top: 10px; }
      .mt-3 { margin-top: 15px; }
    `
  }

  /**
   * Get page dimensions in mm for jsPDF
   */
  private getPageDimensions(pageSize: string = 'A4'): { width: number; height: number } {
    const sizes: Record<string, { width: number; height: number }> = {
      'A4': { width: 210, height: 297 }, // mm
      'A3': { width: 297, height: 420 },
      'A5': { width: 148, height: 210 },
      'Letter': { width: 216, height: 279 },
      'Legal': { width: 216, height: 356 }
    }
    
    return sizes[pageSize] || sizes['A4']
  }

  /**
   * Cleanup resources (no browser to close for client-side generation)
   */
  async dispose(): Promise<void> {
    // Clear library references
    this.jsPDF = null
    this.html2canvas = null
  }

  /**
   * Add logo to PDF by injecting image into HTML before generation
   */
  async addLogo(logoPath: string, position: { x: number; y: number; width?: number; height?: number }): Promise<string> {
    // Return HTML img tag that can be inserted into template
    const style = `position: absolute; left: ${position.x}px; top: ${position.y}px;` + 
      (position.width ? ` width: ${position.width}px;` : '') +
      (position.height ? ` height: ${position.height}px;` : '')
    
    return `<img src="${logoPath}" style="${style}" alt="Logo" class="logo" />`
  }

  /**
   * Add signature to PDF by injecting image into HTML before generation
   */
  async addSignature(signaturePath: string, position: { x: number; y: number; width?: number; height?: number }): Promise<string> {
    // Return HTML img tag that can be inserted into template
    const style = `position: absolute; left: ${position.x}px; top: ${position.y}px;` + 
      (position.width ? ` width: ${position.width}px;` : '') +
      (position.height ? ` height: ${position.height}px;` : '')
    
    return `<img src="${signaturePath}" style="${style}" alt="Signature" class="signature" />`
  }

  /**
   * Validate PDF generation requirements
   */
  validateRequirements(template: DocumentTemplate): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check required template fields
    if (!template.template_content) {
      errors.push('Template content is required')
    }

    if (!template.page_size) {
      errors.push('Page size is required')
    }

    if (!template.page_orientation) {
      errors.push('Page orientation is required')
    }

    // Check margins
    if (template.margin_top < 0 || template.margin_bottom < 0 || 
        template.margin_left < 0 || template.margin_right < 0) {
      errors.push('Margins must be non-negative')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}