import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { adminUserCreateSchema } from '@/lib/validations/auth'

// Create Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
)

// Create regular client for checking current user permissions
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authorization.split(' ')[1]

    // Verify user is authenticated and get user info
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Check if user has admin role using service role (bypasses RLS)
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin') {
      console.error('Admin access check failed:', {
        profileError,
        profile,
        userRole: profile?.role,
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = adminUserCreateSchema.parse(body)

    // Generate temporary password
    const tempPassword =
      Math.random().toString(36).slice(-12) +
      Math.random().toString(36).slice(-12)

    // Create user in Supabase Auth using admin client
    const { data: authData, error: createUserError } =
      await supabaseAdmin.auth.admin.createUser({
        email: validatedData.email,
        password: tempPassword,
        email_confirm: true,
        user_metadata: {
          first_name: validatedData.firstName,
          last_name: validatedData.lastName,
          role: validatedData.role,
          company_id: validatedData.companyId || null,
        },
      })

    if (createUserError) {
      console.error('Error creating user in auth:', createUserError)
      return NextResponse.json(
        { error: createUserError.message },
        { status: 400 }
      )
    }

    // Create profile record
    const { error: profileCreateError } = await supabaseAdmin
      .from('profiles')
      .insert({
        user_id: authData.user.id,
        email: validatedData.email,
        first_name: validatedData.firstName,
        last_name: validatedData.lastName,
        phone_number: validatedData.phoneNumber || null,
        role: validatedData.role,
        company_id: validatedData.companyId || null,
        is_active: true,
      })

    if (profileCreateError) {
      console.error('Error creating profile:', profileCreateError)
      // Clean up auth user if profile creation fails
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      return NextResponse.json(
        { error: 'Failed to create user profile' },
        { status: 500 }
      )
    }

    // Send password reset email
    const { error: resetError } =
      await supabaseAdmin.auth.resetPasswordForEmail(validatedData.email, {
        redirectTo: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/reset-password`,
      })

    if (resetError) {
      console.error('Error sending password reset email:', resetError)
      // Don't fail the request, just log the error
    }

    return NextResponse.json({
      success: true,
      user: {
        id: authData.user.id,
        email: authData.user.email,
        role: validatedData.role,
      },
    })
  } catch (error) {
    console.error('API Error:', error)
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
