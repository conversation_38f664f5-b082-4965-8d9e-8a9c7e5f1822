'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronDown, ChevronRight, LogOut } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/use-auth'
import type { UserProfile } from '@/lib/supabase/auth'
import {
  getNavigationForRole,
  type NavigationItem,
} from '@/lib/constants/routes'

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const { profile, loading, signOut } = useAuth()
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>([])
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const pathname = usePathname()

  useEffect(() => {
    if (profile) {
      setNavigationItems(getNavigationForRole(profile.role))
    }
  }, [profile])

  // Auto-expand parent items if child is active
  useEffect(() => {
    const activeItem = findActiveItem(navigationItems, pathname)
    if (activeItem?.parent) {
      setExpandedItems(
        prev => new Set([...Array.from(prev), activeItem.parent])
      )
    }
  }, [navigationItems, pathname])

  function findActiveItem(
    items: NavigationItem[],
    path: string
  ): { item: NavigationItem; parent?: string } | null {
    for (const item of items) {
      if (item.href === path) {
        return { item }
      }
      if (item.children) {
        for (const child of item.children) {
          if (child.href === path) {
            return { item: child, parent: item.id }
          }
        }
      }
    }
    return null
  }

  function toggleExpanded(itemId: string) {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  function isActive(href: string): boolean {
    if (href === '/dashboard/overview') {
      return pathname === '/dashboard' || pathname === '/dashboard/overview'
    }
    return pathname === href || pathname.startsWith(href + '/')
  }

  async function handleSignOut() {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  if (loading) {
    return (
      <div
        className={cn(
          'w-64 bg-slate-900 border-r border-slate-800 p-4',
          className
        )}
      >
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-slate-800 rounded"></div>
          <div className="space-y-2">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="h-10 bg-slate-800 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return null
  }

  return (
    <div
      className={cn(
        'w-64 bg-slate-900 border-r border-slate-800 flex flex-col',
        className
      )}
    >
      {/* Logo and User Info */}
      <div className="p-4 border-b border-slate-800">
        <div className="flex items-center space-x-3 mb-4">
          <div className="h-8 w-8 rounded-lg bg-orange-500 flex items-center justify-center">
            <span className="text-sm font-bold text-white">DYY</span>
          </div>
          <span className="text-white font-semibold">Trading Management</span>
        </div>

        <div className="text-sm">
          <p className="text-white font-medium">
            {profile.first_name} {profile.last_name}
          </p>
          <p className="text-slate-400 capitalize">
            {profile.role.replace('_', ' ')}
          </p>
          {profile.company_id && (
            <p className="text-slate-500 text-xs mt-1">
              Company ID: {profile.company_id.slice(-8)}
            </p>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {navigationItems.map(item => (
          <div key={item.id}>
            {item.children ? (
              // Parent item with children
              <div>
                <button
                  onClick={() => toggleExpanded(item.id)}
                  className={cn(
                    'w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors',
                    'text-slate-300 hover:text-white hover:bg-slate-800'
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="h-5 w-5" />
                    <span>{item.title}</span>
                  </div>
                  {expandedItems.has(item.id) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </button>

                {expandedItems.has(item.id) && (
                  <div className="ml-6 mt-1 space-y-1">
                    {item.children.map(child => (
                      <Link
                        key={child.id}
                        href={child.href}
                        className={cn(
                          'flex items-center space-x-3 px-3 py-2 text-sm rounded-lg transition-colors',
                          isActive(child.href)
                            ? 'bg-orange-500/20 text-orange-400 border-r-2 border-orange-500'
                            : 'text-slate-400 hover:text-white hover:bg-slate-800'
                        )}
                      >
                        <child.icon className="h-4 w-4" />
                        <span>{child.title}</span>
                        {child.badge && (
                          <span className="ml-auto bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                            {child.badge}
                          </span>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              // Single item without children
              <Link
                href={item.href}
                className={cn(
                  'flex items-center space-x-3 px-3 py-2 text-sm rounded-lg transition-colors',
                  isActive(item.href)
                    ? 'bg-orange-500/20 text-orange-400 border-r-2 border-orange-500'
                    : 'text-slate-300 hover:text-white hover:bg-slate-800'
                )}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.title}</span>
                {item.badge && (
                  <span className="ml-auto bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                    {item.badge}
                  </span>
                )}
              </Link>
            )}
          </div>
        ))}
      </nav>

      {/* Sign Out */}
      <div className="p-4 border-t border-slate-800">
        <Button
          onClick={handleSignOut}
          variant="ghost"
          className="w-full justify-start text-slate-300 hover:text-white hover:bg-slate-800"
        >
          <LogOut className="h-4 w-4 mr-3" />
          Sign Out
        </Button>
      </div>
    </div>
  )
}
