# Design System Implementation

## Color Palette (Dark Blue Logistics Theme)

### Primary Colors
```css
/* Core Dark Blues */
--primary-900: #0f172a    /* Navy Blue - main backgrounds */
--primary-800: #1e293b    /* Dark Blue - cards, panels */
--primary-700: #334155    /* Slate Blue - borders, dividers */

/* Orange Accents */
--accent-500: #f97316     /* Primary Orange - CTAs, highlights */
--accent-400: #fb923c     /* Light Orange - hover states */
--accent-600: #ea580c     /* Dark Orange - active states */

/* Supporting Colors */
--neutral-50: #f8fafc     /* Light text on dark backgrounds */
--neutral-200: #e2e8f0    /* Secondary text */
--neutral-500: #64748b    /* Muted text */
--neutral-800: #1e293b    /* Text on light backgrounds */
```

### Status Colors
```css
/* Status Indicators */
--success-500: #10b981    /* Completed, successful operations */
--warning-500: #f59e0b    /* In progress, attention needed */
--error-500: #ef4444      /* Failed, critical issues */
--info-500: #3b82f6      /* Information, pending states */
```

### Accessibility Compliance
- **Contrast Ratios:** All color combinations meet WCAG 2.1 AA (4.5:1 minimum)
- **Primary on Navy:** #f8fafc on #0f172a = 15.5:1 ✅
- **Orange on Dark Blue:** #f97316 on #1e293b = 6.2:1 ✅
- **Secondary Text:** #e2e8f0 on #334155 = 8.1:1 ✅

## Typography System

### Font Families
```css
/* Primary Interface Font */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

/* Monospace for IDs/Codes */
--font-mono: 'Roboto Mono', 'SF Mono', Monaco, monospace;

/* Regional Support */
--font-thai: 'Noto Sans Thai', 'Inter', sans-serif;
```

### Type Scale
```css
/* Headings */
--text-h1: 2.25rem; font-weight: 700; line-height: 1.2; /* 36px */
--text-h2: 1.875rem; font-weight: 600; line-height: 1.3; /* 30px */
--text-h3: 1.5rem; font-weight: 600; line-height: 1.4; /* 24px */
--text-h4: 1.25rem; font-weight: 500; line-height: 1.4; /* 20px */

/* Body Text */
--text-lg: 1.125rem; font-weight: 400; line-height: 1.6; /* 18px */
--text-base: 1rem; font-weight: 400; line-height: 1.5; /* 16px */
--text-sm: 0.875rem; font-weight: 400; line-height: 1.4; /* 14px */
--text-xs: 0.75rem; font-weight: 400; line-height: 1.3; /* 12px */

/* Specialized */
--text-mono: 0.875rem; font-family: var(--font-mono); /* Tracking numbers */
--text-button: 0.875rem; font-weight: 500; /* Button labels */
```

## Spacing & Layout System

### Spacing Scale (8px base unit)
```css
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-3: 0.75rem;  /* 12px */
--space-4: 1rem;     /* 16px */
--space-5: 1.25rem;  /* 20px */
--space-6: 1.5rem;   /* 24px */
--space-8: 2rem;     /* 32px */
--space-10: 2.5rem;  /* 40px */
--space-12: 3rem;    /* 48px */
--space-16: 4rem;    /* 64px */
--space-20: 5rem;    /* 80px */
```

### Layout Containers
```css
/* Page Containers */
--container-sm: 640px;   /* Mobile landscape */
--container-md: 768px;   /* Tablet */
--container-lg: 1024px;  /* Desktop */
--container-xl: 1280px;  /* Large desktop */
--container-2xl: 1400px; /* Logistics dashboards max-width */

/* Component Constraints */
--content-max: 65ch;     /* Readable text line length */
--form-max: 480px;       /* Optimal form width */
--card-max: 400px;       /* Card component maximum */
```

## Component Library (ShadCN UI Customization)

### Button System
```tsx
// Primary Action Button (Orange)
<Button className="bg-accent-500 hover:bg-accent-400 text-white font-medium">
  Create Shipment
</Button>

// Secondary Button (Dark Blue)
<Button variant="secondary" className="bg-primary-800 hover:bg-primary-700 text-neutral-50">
  View Details
</Button>

// Destructive Action
<Button variant="destructive" className="bg-error-500 hover:bg-error-600">
  Delete Shipment
</Button>

// Mobile Touch Target (44px minimum)
<Button className="min-h-[44px] min-w-[44px] text-lg" size="lg">
  Update Status
</Button>
```

### Form Components
```tsx
// Cascading Select with Pre-population
<Select onValueChange={handleCustomerChange}>
  <SelectTrigger className="bg-primary-800 border-primary-700">
    <SelectValue placeholder="Select Customer" />
  </SelectTrigger>
  <SelectContent>
    {customers.map(customer => (
      <SelectItem value={customer.id}>{customer.name}</SelectItem>
    ))}
  </SelectContent>
</Select>

// Auto-populated dependent select
<Select disabled={!selectedCustomer} value={defaultShipper}>
  <SelectTrigger className="bg-primary-800 border-primary-700">
    <SelectValue placeholder="Shipper (auto-loaded)" />
  </SelectTrigger>
</Select>
```

### Card Components
```tsx
// Status Card for Dashboard
<Card className="bg-primary-800 border-primary-700">
  <CardHeader className="pb-3">
    <div className="flex items-center justify-between">
      <CardTitle className="text-neutral-50">Shipment #SH-2024-001</CardTitle>
      <Badge variant="outline" className="bg-warning-500 text-black">
        In Transit
      </Badge>
    </div>
  </CardHeader>
  <CardContent>
    <div className="space-y-2 text-neutral-200">
      <p>Customer: ABC Trading Co.</p>
      <p>Destination: Bangkok Port</p>
      <p>ETA: Dec 15, 2024</p>
    </div>
  </CardContent>
</Card>
```

### Navigation Components
```tsx
// Main Navigation
<nav className="bg-primary-900 border-b border-primary-700">
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div className="flex justify-between h-16">
      <div className="flex space-x-8">
        <NavigationMenuItem active={pathname === '/dashboard'}>
          Dashboard
        </NavigationMenuItem>
        <NavigationMenuItem active={pathname.startsWith('/shipments')}>
          Shipments
        </NavigationMenuItem>
      </div>
    </div>
  </div>
</nav>

// Mobile Tab Navigation
<div className="fixed bottom-0 left-0 right-0 bg-primary-900 border-t border-primary-700">
  <div className="grid grid-cols-4 h-16">
    <TabButton icon={<HomeIcon />} label="Dashboard" active />
    <TabButton icon={<TruckIcon />} label="Assignments" />
    <TabButton icon={<CameraIcon />} label="Update" />
    <TabButton icon={<HistoryIcon />} label="History" />
  </div>
</div>
```

---
