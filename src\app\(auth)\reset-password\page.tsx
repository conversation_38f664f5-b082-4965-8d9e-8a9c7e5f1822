'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Eye, EyeOff, Loader2, CheckCircle } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  passwordResetSchema,
  type PasswordResetFormData,
} from '@/lib/validations/auth'
import { authClient, type AuthError } from '@/lib/supabase/auth'
import { AuthErrorDisplay } from '@/components/auth/auth-error-display'
import { PasswordStrengthIndicator } from '@/components/auth/password-strength-indicator'
import { createClient } from '@/lib/supabase/client'

function ResetPasswordForm() {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<AuthError | null>(null)
  const [success, setSuccess] = useState(false)
  const [isValidLink, setIsValidLink] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()

  const form = useForm<PasswordResetFormData>({
    resolver: zodResolver(passwordResetSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const watchPassword = form.watch('password')

  useEffect(() => {
    let mounted = true

    async function handlePasswordReset() {
      const supabase = createClient()

      // First, check for URL hash parameters (modern Supabase flow)
      const hashParams = new URLSearchParams(window.location.hash.substring(1))
      const accessToken =
        hashParams.get('access_token') || searchParams.get('access_token')
      const refreshToken =
        hashParams.get('refresh_token') || searchParams.get('refresh_token')
      const tokenType =
        hashParams.get('token_type') || searchParams.get('token_type')
      const type = hashParams.get('type') || searchParams.get('type')
      const error = hashParams.get('error') || searchParams.get('error')

      console.log('Reset password check:', {
        hasAccessToken: !!accessToken,
        hasRefreshToken: !!refreshToken,
        tokenType,
        type,
        error,
        hashParams: Object.fromEntries(hashParams.entries()),
        searchParams: Object.fromEntries(searchParams.entries()),
      })

      // Check for errors first
      if (error) {
        console.log('Reset link has error:', error)
        if (mounted) {
          setError({
            code: 'INVALID_RESET_LINK',
            message:
              'Invalid or expired reset link. Please request a new password reset.',
            timestamp: new Date(),
          })
        }
        return
      }

      // Handle Supabase auth redirect with tokens in hash
      if (accessToken && refreshToken && type === 'recovery') {
        try {
          console.log('Setting session with tokens from hash/URL')
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken,
          })

          if (error) {
            console.error('Error setting session:', error)
            if (mounted) {
              setError({
                code: 'INVALID_RESET_LINK',
                message:
                  'Invalid or expired reset link. Please request a new password reset.',
                timestamp: new Date(),
              })
            }
            return
          }

          if (data.session && mounted) {
            console.log('Password reset session established successfully')
            setIsValidLink(true)
            // Clear the hash to clean up the URL
            window.history.replaceState(null, '', window.location.pathname)
          }
        } catch (err) {
          console.error('Error in password reset setup:', err)
          if (mounted) {
            setError({
              code: 'INVALID_RESET_LINK',
              message:
                'Invalid or expired reset link. Please request a new password reset.',
              timestamp: new Date(),
            })
          }
        }
        return
      }

      // Check if we already have a valid session (user came from auth redirect)
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession()
        if (session && mounted) {
          console.log('Found existing session for password reset')
          setIsValidLink(true)
          return
        }
      } catch (err) {
        console.error('Error checking session:', err)
      }

      // If we reach here and have no valid session, show error
      if (mounted) {
        console.log('No valid reset session found')
        setError({
          code: 'INVALID_RESET_LINK',
          message:
            'Invalid or expired reset link. Please request a new password reset.',
          timestamp: new Date(),
        })
      }
    }

    // Also listen for auth state changes (when Supabase redirects with auth data)
    const supabase = createClient()
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state change in reset password:', event, !!session)
      if (event === 'PASSWORD_RECOVERY' && session && mounted) {
        console.log('Password recovery event detected')
        setIsValidLink(true)
        setError(null)
      }
    })

    handlePasswordReset()

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [searchParams])

  async function onSubmit(data: PasswordResetFormData) {
    if (!isValidLink) {
      setError({
        code: 'INVALID_RESET_LINK',
        message:
          'Invalid or expired reset link. Please request a new password reset.',
        timestamp: new Date(),
      })
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Use the existing authClient which should now have the session
      const supabase = createClient()
      const { error } = await supabase.auth.updateUser({
        password: data.password,
      })

      if (error) {
        throw error
      }

      setSuccess(true)

      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push('/login')
      }, 3000)
    } catch (error) {
      console.error('Password reset error:', error)
      if ((error as AuthError).code) {
        setError(error as AuthError)
      } else {
        setError({
          code: 'UNKNOWN_ERROR',
          message:
            error instanceof Error
              ? error.message
              : 'Failed to reset password. Please try again.',
          timestamp: new Date(),
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  function handleRetry() {
    setError(null)
    form.handleSubmit(onSubmit)()
  }

  if (success) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">
            Password reset successful
          </h2>
          <p className="text-slate-300">
            Your password has been successfully updated. You can now sign in
            with your new password.
          </p>
        </div>

        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
          <p className="text-green-300 text-sm text-center">
            Redirecting to sign in page in 3 seconds...
          </p>
        </div>

        <div className="text-center">
          <Link
            href="/login"
            className="inline-flex items-center gap-2 text-sm text-orange-400 hover:text-orange-300 transition-colors"
          >
            Continue to sign in
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">
          Reset your password
        </h2>
        <p className="text-slate-300">Enter a new password for your account</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <AuthErrorDisplay
            error={error}
            onRetry={handleRetry}
            className="mb-4"
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">New Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Enter your new password"
                      className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 pr-10"
                      disabled={isLoading || !isValidLink}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                      disabled={isLoading || !isValidLink}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage className="text-red-300" />

                <PasswordStrengthIndicator
                  password={watchPassword}
                  className="mt-3"
                />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">
                  Confirm New Password
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder="Confirm your new password"
                      className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 pr-10"
                      disabled={isLoading || !isValidLink}
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                      disabled={isLoading || !isValidLink}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage className="text-red-300" />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full bg-orange-500 hover:bg-orange-600 text-white"
            disabled={isLoading || !!error || !isValidLink}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Reset password
          </Button>
        </form>
      </Form>

      <div className="text-center">
        <Link
          href="/login"
          className="text-sm text-orange-400 hover:text-orange-300 transition-colors"
        >
          Back to sign in
        </Link>
      </div>
    </div>
  )
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center p-4 text-white">Loading...</div>}>
      <ResetPasswordForm />
    </Suspense>
  )
}
