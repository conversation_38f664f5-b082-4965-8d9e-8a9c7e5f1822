'use client'

import { useState, useCallback, useEffect } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Ship,
  Factory,
  Building2,
  MapPin,
  Calendar,
  FileText,
  Upload,
  AlertCircle,
  CheckCircle,
  Loader2,
  Clock,
  Package,
  Users,
  Truck,
  ArrowRight,
  Info,
  AlertTriangle,
} from 'lucide-react'

import {
  TransportModeModal,
  type TransportMode,
  type TransportModeConfig,
} from '@/components/forms/shipment-form/transport-mode-modal'
import { EnhancedConsigneeIntegration } from '@/components/shipments/enhanced-consignee-integration'
import { ProductSelectorCards } from '@/components/shipments/product-selector-cards'
import {
  shipmentCreationSchema,
  type ShipmentCreation,
  type TransportModeConfig as ValidationTransportModeConfig,
} from '@/lib/validations/shipment'
import { useCompaniesForDropdown } from '@/hooks/use-companies'
import { usePortsManagement } from '@/hooks/use-ports'
import { useShipmentRelationships } from '@/hooks/use-shipment-relationships'
import { useShipmentCRUD } from '@/hooks/use-shipments'
import { createClient } from '@/lib/supabase/client'

interface ShipmentCreationPageProps {}

export default function ShipmentCreationPage({}: ShipmentCreationPageProps) {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isCheckingRelationships, setIsCheckingRelationships] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [createdShipmentNumber, setCreatedShipmentNumber] = useState<string>('')

  // Transport mode modal state
  const [showTransportModal, setShowTransportModal] = useState(true)
  const [selectedTransportMode, setSelectedTransportMode] =
    useState<TransportMode | null>(null)
  const [transportConfig, setTransportConfig] =
    useState<TransportModeConfig | null>(null)

  // Document upload state
  const [uploadedDocument, setUploadedDocument] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)

  // Data hooks
  const { companiesByType, loading: companiesLoading, error: companiesError } = useCompaniesForDropdown()
  const { activePorts: ports } = usePortsManagement()
  const { createShipment, creating: isCreatingShipment } = useShipmentCRUD()

  // Comprehensive relationship integration
  const {
    state: relationshipState,
    actions: relationshipActions,
    config,
  } = useShipmentRelationships({
    autoSelectDefaults: true,
    enablePricePopulation: true,
    enableRouteIntelligence: true,
    enableHistoryAnalysis: true,
    validateRelationships: true,
  })

  // Handle success dialog OK button
  const handleSuccessDialogOK = () => {
    setShowSuccessDialog(false)
    setCreatedShipmentNumber('')
    // Navigate to shipment list page (or companies page as fallback)
    router.push('/shipments')
  }

  // Filter companies by type
  const customers = companiesByType.customer || []
  const factories = companiesByType.factory || []
  const forwarderAgents = companiesByType.forwarder_agent || []


  // Use the full validation schema (excluding only shipment_number which is auto-generated)
  const formValidationSchema = shipmentCreationSchema
    .omit({ shipment_number: true })

  const form = useForm<Omit<ShipmentCreation, 'shipment_number'>>({
    resolver: zodResolver(formValidationSchema),
    defaultValues: {
      transportation_mode: 'sea',
      customer_id: '',
      factory_id: '',
      forwarder_agent_id: '',
      shipper_id: '',
      consignee_id: '',
      notify_party_id: '',
      origin_port_id: '',
      destination_port_id: '',
      etd_date: '',
      eta_date: '',
      closing_time: '',
      cy_date: '',
      liner: '',
      vessel_name: '',
      voyage_number: '',
      booking_number: '',
      invoice_number: '',
      number_of_pallet: 0,
      pallet_description: '',
      total_weight: 0,
      total_volume: 0,
      total_value_cif: 0,
      total_value_fob: 0,
      ephyto_refno: '',
      currency_code: 'USD',
      status: 'booking_confirmed',
      notes: '',
    },
  })

  // Handle transport mode selection
  const handleTransportModeSelect = useCallback(
    (mode: TransportMode, config: TransportModeConfig) => {
      setSelectedTransportMode(mode)
      setTransportConfig(config)
      form.setValue('transportation_mode', mode)
      setShowTransportModal(false)
    },
    [form]
  )

  // Check if customer has any relationships (shippers or products)
  const checkCustomerRelationships = useCallback(
    async (customerId: string): Promise<boolean> => {
      try {
        const supabase = createClient()

        // Run both queries in parallel for better performance
        const [shipperResult, productResult] = await Promise.all([
          supabase
            .from('customer_shippers')
            .select('id')
            .eq('customer_id', customerId)
            .eq('is_active', true)
            .limit(1),
          supabase
            .from('customer_products')
            .select('id')
            .eq('customer_id', customerId)
            .eq('is_active', true)
            .limit(1),
        ])

        // Log any errors but don't fail the check
        if (shipperResult.error) {
          console.warn(
            'Error checking shipper relationships:',
            shipperResult.error
          )
        }
        if (productResult.error) {
          console.warn(
            'Error checking product relationships:',
            productResult.error
          )
        }

        const hasShipperRelationships =
          shipperResult.data && shipperResult.data.length > 0
        const hasProductRelationships =
          productResult.data && productResult.data.length > 0
        const hasAnyRelationships =
          hasShipperRelationships || hasProductRelationships

        console.log(`Customer ${customerId} relationship check:`, {
          hasShipperRelationships,
          hasProductRelationships,
          hasAnyRelationships,
          shipperCount: shipperResult.data?.length || 0,
          productCount: productResult.data?.length || 0,
        })

        return hasAnyRelationships
      } catch (error) {
        console.error('Error checking customer relationships:', error)
        // Return true to avoid blocking shipment creation in case of system error
        return true
      }
    },
    []
  )

  // Handle customer selection with intelligent pre-population and relationship validation
  const handleCustomerChange = useCallback(
    async (customerId: string) => {
      const customer = customers.find(c => c.id === customerId)
      if (customer) {
        try {
          setIsCheckingRelationships(true)
          setError(null) // Clear previous errors

          // First check if customer has any relationships
          const hasRelationships = await checkCustomerRelationships(customerId)

          if (!hasRelationships) {
            setError(
              `Customer "${customer.name}" has no shipper or product relationships configured. Please add relationships in Master Data → Customer-Shipper Relationships and Customer-Product Relationships before creating shipments.`
            )
            // Don't set the customer in the form to prevent proceeding
            return
          }

          // Set customer in form if validation passes
          form.setValue('customer_id', customerId)

          // Clear dependent fields when customer changes
          form.setValue('shipper_id', '')
          form.setValue('consignee_id', '')
          form.setValue('notify_party_id', '')

          // Use comprehensive relationship actions
          relationshipActions.selectCustomer(customerId)
        } catch (error) {
          console.error('Error during customer selection:', error)
          setError(
            'Failed to validate customer relationships. Please try again.'
          )
        } finally {
          setIsCheckingRelationships(false)
        }
      }
    },
    [customers, form, relationshipActions, checkCustomerRelationships]
  )

  // Handle shipper selection
  const handleShipperChange = useCallback(
    (shipperId: string) => {
      form.setValue('shipper_id', shipperId)
      relationshipActions.selectShipper(shipperId)
    },
    [form, relationshipActions]
  )

  // Handle consignee selection for notify party intelligence
  const handleConsigneeChange = useCallback(
    (consigneeId: string, consigneeData?: any) => {
      form.setValue('consignee_id', consigneeId, { shouldValidate: true })
      relationshipActions.selectConsignee(consigneeId)
    },
    [form, relationshipActions]
  )

  // Handle notify party selection with auto-population
  const handleNotifyPartyChange = useCallback(
    (notifyPartyId: string, relationship?: any) => {
      form.setValue('notify_party_id', notifyPartyId, { shouldValidate: true })
      relationshipActions.selectNotifyParty(notifyPartyId)
    },
    [form, relationshipActions]
  )

  // Handle document upload
  const handleDocumentUpload = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]

    // Validate file type and size
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
    ]
    if (!allowedTypes.includes(file.type)) {
      setError('File must be PDF, DOC, DOCX, JPG, or PNG')
      return
    }

    if (file.size > 10 * 1024 * 1024) {
      setError('File size must be less than 10MB')
      return
    }

    setIsUploading(true)
    setError(null)

    try {
      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 10) {
        setUploadProgress(progress)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      setUploadedDocument(file)
      setUploadProgress(100)
    } catch (error) {
      console.error('Upload error:', error)
      setError('Failed to upload document')
    } finally {
      setIsUploading(false)
    }
  }, [])

  // Form submission
  const handleSubmit = async (
    data: Omit<ShipmentCreation, 'shipment_number'>
  ) => {
    console.log('Form submission started with data:', data)

    try {
      setIsSubmitting(true)
      setError(null)

      // First, validate the form data manually to get better error messages
      console.log('Running form validation...')
      console.log('Form data being validated:', {
        consignee_id: data.consignee_id,
        notify_party_id: data.notify_party_id,
        vessel_name: data.vessel_name,
        booking_number: data.booking_number,
        etd_date: data.etd_date,
        eta_date: data.eta_date,
        closing_time: data.closing_time,
      })
      try {
        formValidationSchema.parse(data)
        console.log('Form validation passed')
      } catch (validationError: any) {
        console.error('Form validation failed:', validationError)
        if (validationError.errors) {
          const errorMessages = validationError.errors
            .map((err: any) => `${err.path?.join('.')}: ${err.message}`)
            .join('; ')
          setError(`Form validation failed: ${errorMessages}`)

          // Show detailed validation errors in console for debugging
          console.table(
            validationError.errors.map((err: any) => ({
              field: err.path?.join('.'),
              message: err.message,
              value: err.path?.reduce((obj: any, key: any) => obj?.[key], data),
            }))
          )
        } else {
          setError(`Form validation failed: ${validationError.message}`)
        }
        return
      }

      console.log('Validating transport mode:', selectedTransportMode)
      // Validate transport mode is selected
      if (!selectedTransportMode) {
        setError('Please select a transportation mode')
        setShowTransportModal(true)
        return
      }

      console.log('Validating uploaded document:', uploadedDocument?.name)
      // Validate booking confirmation document is uploaded
      if (!uploadedDocument) {
        setError('Booking confirmation document is required')
        return
      }

      // Validate relationship selections
      try {
        console.log('Validating relationships...')
        const isValidRelationship =
          await relationshipActions.validateAllRelationships()
        if (!isValidRelationship) {
          const errors = relationshipActions.getRelationshipErrors()
          console.log('Relationship validation errors:', errors)
          setError(`Relationship validation failed: ${errors.join('; ')}`)
          return
        }
        console.log('Relationships validated successfully')
      } catch (validationError) {
        console.warn('Relationship validation failed:', validationError)
        // Continue without relationship validation for now
      }

      // Prepare documents for upload
      console.log('Preparing documents for upload...')
      const documents: Record<string, File> = {
        booking_confirmation: uploadedDocument,
      }

      // Create the shipment using the actual API
      console.log('Creating shipment with API...')

      // Convert datetime-local format to ISO format for database (for closing_time and cy_date)
      const convertToISODateTime = (dateTimeLocal: string) => {
        if (!dateTimeLocal || dateTimeLocal.trim() === '') {
          return null // Return null for empty dates instead of empty string
        }
        try {
          // datetime-local format: "2025-08-29T14:30"
          // Convert to ISO format: "2025-08-29T14:30:00.000Z"
          const isoString = new Date(dateTimeLocal).toISOString()
          console.log(`Converting datetime: ${dateTimeLocal} -> ${isoString}`)
          return isoString
        } catch (error) {
          console.error(`Invalid datetime format: ${dateTimeLocal}`, error)
          return null
        }
      }

      // Convert date-only format to ISO format with 00:00:00 time (for ETD and ETA)
      const convertDateToISOWithMidnight = (dateOnly: string) => {
        if (!dateOnly || dateOnly.trim() === '') {
          return null // Return null for empty dates instead of empty string
        }
        try {
          // date format: "2025-08-29"
          // Convert to ISO format with 00:00:00: "2025-08-29T00:00:00.000Z"
          const dateObj = new Date(dateOnly + 'T00:00:00.000Z')
          const isoString = dateObj.toISOString()
          console.log(`Converting date to midnight: ${dateOnly} -> ${isoString}`)
          return isoString
        } catch (error) {
          console.error(`Invalid date format: ${dateOnly}`, error)
          return null
        }
      }

      // Add placeholder shipment_number and fix date formats
      const shipmentData: ShipmentCreation = {
        ...data,
        shipment_number: 'PLACEHOLDER', // Will be auto-generated by createShipment
        etd_date:
          convertDateToISOWithMidnight(data.etd_date) || new Date().toISOString(), // Use current date as fallback
        eta_date:
          convertDateToISOWithMidnight(data.eta_date) ||
          new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow as fallback
        closing_time:
          convertToISODateTime(data.closing_time) || new Date().toISOString(), // Use current time as fallback
        cy_date: convertToISODateTime(data.cy_date), // Can be null

        // Handle other potentially empty fields
        shipper_id: data.shipper_id || null,
        consignee_id: data.consignee_id || null,
        notify_party_id: data.notify_party_id || null,
        liner: data.liner || null,
        vessel_name: data.vessel_name || null,
        voyage_number: data.voyage_number || null,
        booking_number: data.booking_number || null,
        invoice_number: data.invoice_number || null,
        ephyto_refno: data.ephyto_refno || null,
        notes: data.notes || null,
        pallet_description: data.pallet_description || null,

        // Include selected product ID in metadata for shipment_products table
        metadata: {
          selectedProductId: relationshipState.selectedProductId,
          formVersion: '3.2',
          createdWith: 'enhanced-product-selection',
        },
      }

      console.log('Shipment data with converted dates:', shipmentData)
      const createdShipment = await createShipment(shipmentData, documents)

      console.log('Shipment created successfully:', createdShipment)

      // Clear any existing errors
      setError('')
      
      // Store shipment number and show success dialog
      setCreatedShipmentNumber(createdShipment.shipment_number)
      setShowSuccessDialog(true)
      
      // Don't navigate immediately - wait for user to click OK in dialog
      
    } catch (error) {
      console.error('Form submission error:', error)
      
      // Extract user-friendly error message
      let errorMessage = 'Failed to create shipment. Please try again.'
      
      if (error instanceof Error) {
        errorMessage = error.message
      }
      
      // Set error message to display to user
      setError(errorMessage)
      
      // DON'T redirect on error - stay on the page so user can see error and retry
      console.log('Staying on page due to error, user can retry')
      
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get current form values for display
  const watchedValues = form.watch()

  // Test validation function for debugging
  const testValidation = () => {
    const currentData = form.getValues()
    console.log('Testing validation with current form data:', currentData)
    
    try {
      formValidationSchema.parse(currentData)
      console.log('✅ Validation passed')
    } catch (validationError: any) {
      console.error('❌ Validation failed:', validationError)
      if (validationError.errors) {
        console.table(
          validationError.errors.map((err: any) => ({
            field: err.path?.join('.'),
            message: err.message,
            value: err.path?.reduce((obj: any, key: any) => obj?.[key], currentData),
          }))
        )
      }
    }
  }

  // Debug button disabled state
  const buttonDisabled =
    isSubmitting ||
    isCreatingShipment ||
    !selectedTransportMode ||
    !uploadedDocument
  console.log('Button disabled state:', {
    isSubmitting,
    isCreatingShipment,
    selectedTransportMode: !!selectedTransportMode,
    uploadedDocument: !!uploadedDocument,
    buttonDisabled,
  })

  // Log current form values
  console.log('Current form values:', watchedValues)

  // Quick test function to populate form with mock data for testing
  const fillTestData = () => {
    // Use actual available data
    const originPorts = ports.filter(port => port.port_type === 'origin')
    const destinationPorts = ports.filter(port => port.port_type === 'destination')
    
    if (customers.length > 0) form.setValue('customer_id', customers[0].id)
    if (factories.length > 0) form.setValue('factory_id', factories[0].id)
    if (forwarderAgents.length > 0) form.setValue('forwarder_agent_id', forwarderAgents[0].id)
    if (originPorts.length > 0) form.setValue('origin_port_id', originPorts[0].id)
    if (destinationPorts.length > 0) form.setValue('destination_port_id', destinationPorts[0].id)

    // Set dates for testing (tomorrow for all dates)
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const tomorrowString = tomorrow.toISOString().slice(0, 16) // Format for datetime-local input (closing_time)

    const dayAfter = new Date()
    dayAfter.setDate(dayAfter.getDate() + 2)
    const dayAfterDateString = dayAfter.toISOString().slice(0, 10) // Format for date input (etd_date)

    const threeDaysLater = new Date()
    threeDaysLater.setDate(threeDaysLater.getDate() + 3)
    const threeDaysLaterDateString = threeDaysLater.toISOString().slice(0, 10) // Format for date input (eta_date)

    form.setValue('closing_time', tomorrowString)
    form.setValue('etd_date', dayAfterDateString)
    form.setValue('eta_date', threeDaysLaterDateString)

    console.log('Test data filled')
  }

  // Add test data button for debugging
  if (typeof window !== 'undefined') {
    ;(window as any).fillTestData = fillTestData
  }

  return (
    <div className="container mx-auto py-6 px-4 max-w-6xl">
      {/* Transport Mode Selection Modal */}
      <TransportModeModal
        open={showTransportModal}
        onOpenChange={setShowTransportModal}
        onModeSelect={handleTransportModeSelect}
        selectedMode={selectedTransportMode || undefined}
        disabled={isSubmitting}
      />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Create New Shipment</h1>
          <p className="text-slate-400 mt-1">
            Configure a new shipment with intelligent pre-population and
            validation
          </p>
        </div>

        {selectedTransportMode && (
          <div className="flex items-center space-x-2">
            <Badge
              variant="outline"
              className="bg-orange-600 text-white border-orange-400"
            >
              {transportConfig?.name}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTransportModal(true)}
              disabled={isSubmitting}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600"
            >
              Change Mode
            </Button>
          </div>
        )}
      </div>

      {/* Progress Indicator */}
      {selectedTransportMode && (
        <div className="bg-slate-700 rounded-lg p-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-green-400">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">Transport Mode Selected</span>
            </div>
            <ArrowRight className="h-4 w-4 text-slate-400" />
            <div
              className={`flex items-center space-x-2 ${watchedValues.customer_id ? 'text-green-400' : 'text-slate-400'}`}
            >
              <div
                className={`h-4 w-4 rounded-full border-2 ${watchedValues.customer_id ? 'bg-green-400 border-green-400' : 'border-slate-400'}`}
              />
              <span className="text-sm">Stakeholders</span>
            </div>
            <ArrowRight className="h-4 w-4 text-slate-400" />
            <div
              className={`flex items-center space-x-2 ${watchedValues.etd_date ? 'text-green-400' : 'text-slate-400'}`}
            >
              <div
                className={`h-4 w-4 rounded-full border-2 ${watchedValues.etd_date ? 'bg-green-400 border-green-400' : 'border-slate-400'}`}
              />
              <span className="text-sm">Schedule</span>
            </div>
            <ArrowRight className="h-4 w-4 text-slate-400" />
            <div
              className={`flex items-center space-x-2 ${uploadedDocument ? 'text-green-400' : 'text-slate-400'}`}
            >
              <div
                className={`h-4 w-4 rounded-full border-2 ${uploadedDocument ? 'bg-green-400 border-green-400' : 'border-slate-400'}`}
              />
              <span className="text-sm">Documents</span>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Form */}
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-8"
          onSubmitCapture={() => console.log('Form onSubmitCapture triggered')}
        >
          {/* Relationship Requirements Info */}
          <Alert variant="info">
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Required Relationships:</strong> Each customer must have
              configured relationships with shippers and products before
              creating shipments. If a customer has no relationships, please set
              them up in{' '}
              <strong>Master Data → Customer-Shipper Relationships</strong> and{' '}
              <strong>Customer-Product Relationships</strong> first.
            </AlertDescription>
          </Alert>

          {/* Stakeholder Information */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-blue-200 flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                Stakeholder Information
              </CardTitle>
              <CardDescription className="text-slate-400">
                Configure the key stakeholders for this shipment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Mandatory Stakeholders */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Customer Selection */}
                <FormField
                  control={form.control}
                  name="customer_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Customer *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={handleCustomerChange}
                        disabled={isSubmitting || isCheckingRelationships}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            {isCheckingRelationships ? (
                              <div className="flex items-center space-x-2">
                                <Loader2 className="h-3 w-3 animate-spin" />
                                <span>Checking relationships...</span>
                              </div>
                            ) : (
                              <SelectValue placeholder="Select customer" />
                            )}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {customers.map(customer => (
                            <SelectItem
                              key={customer.id}
                              value={customer.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Building2 className="h-3 w-3 text-blue-500" />
                                <span>{customer.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Factory Selection */}
                <FormField
                  control={form.control}
                  name="factory_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Factory *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select factory" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {factories.map(factory => (
                            <SelectItem
                              key={factory.id}
                              value={factory.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Factory className="h-3 w-3 text-purple-500" />
                                <div>
                                  <div>{factory.name}</div>
                                  {factory.contact_phone && (
                                    <div className="text-xs text-slate-400">
                                      Phone: {factory.contact_phone}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Forwarder Agent Selection */}
                <FormField
                  control={form.control}
                  name="forwarder_agent_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Forwarder Agent *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select forwarder agent" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {forwarderAgents.map(agent => (
                            <SelectItem
                              key={agent.id}
                              value={agent.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Truck className="h-3 w-3 text-indigo-500" />
                                <div>
                                  <div>{agent.name}</div>
                                  {agent.contact_phone && (
                                    <div className="text-xs text-slate-400">
                                      Phone: {agent.contact_phone}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Customer-dependent selections */}
              {watchedValues.customer_id && (
                <div className="border-t border-slate-600 pt-6 space-y-6">
                  {/* Shipper Selection */}
                  {relationshipState.availableShippers.length > 0 && (
                    <div>
                      <Label className="text-slate-200 text-base font-medium mb-3 block">
                        Available Shippers for{' '}
                        {
                          customers.find(
                            c => c.id === watchedValues.customer_id
                          )?.name
                        }
                      </Label>
                      <FormField
                        control={form.control}
                        name="shipper_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-slate-200">
                              Shipper *
                            </FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={handleShipperChange}
                              disabled={isSubmitting}
                            >
                              <FormControl>
                                <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                                  <SelectValue placeholder="Select shipper" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent className="bg-slate-800 border-slate-700">
                                {relationshipState.availableShippers.map(
                                  shipperRel => (
                                    <SelectItem
                                      key={shipperRel.id}
                                      value={shipperRel.shipper_id}
                                      className="text-slate-300 hover:bg-slate-700"
                                    >
                                      <div className="flex items-center space-x-2">
                                        <Ship className="h-3 w-3 text-cyan-500" />
                                        <div>
                                          <div className="flex items-center gap-2">
                                            {shipperRel.shipper.name}
                                            {shipperRel.is_default && (
                                              <Badge
                                                variant="outline"
                                                className="text-xs bg-green-500/20 text-green-300 border-green-400"
                                              >
                                                Default
                                              </Badge>
                                            )}
                                          </div>
                                          {shipperRel.shipper.contact_phone && (
                                            <div className="text-xs text-slate-400">
                                              Phone:{' '}
                                              {shipperRel.shipper.contact_phone}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </SelectItem>
                                  )
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}

                  {/* Warning for Customer with No Shipper Relationships */}
                  {watchedValues.customer_id &&
                   !relationshipState.loadingShippers &&
                   relationshipState.availableShippers.length === 0 && (
                    <Alert variant="warning">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>No Shipper Relationships Found:</strong> The selected customer "
                        {customers.find(c => c.id === watchedValues.customer_id)?.name || 'Unknown Customer'}"
                        has no configured shipper relationships. Please set up shipper relationships in{' '}
                        <strong>Master Data → Customer-Shipper Relationships</strong> before creating a shipment.
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Warning for Customer with No Product Relationships */}
                  {watchedValues.customer_id &&
                   !relationshipState.loadingProducts &&
                   relationshipState.availableProducts.length === 0 && (
                    <Alert variant="warning">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>No Product Relationships Found:</strong> The selected customer "
                        {customers.find(c => c.id === watchedValues.customer_id)?.name || 'Unknown Customer'}"
                        has no configured product relationships. Please set up product relationships in{' '}
                        <strong>Master Data → Customer-Product Relationships</strong> before creating a shipment.
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Enhanced Product Selection with Cards */}
                  {relationshipState.availableProducts.length > 0 && (
                    <div>
                      <ProductSelectorCards
                        products={relationshipState.availableProducts}
                        selectedProductId={relationshipState.selectedProductId}
                        onProductSelect={(productId, product) => {
                          relationshipActions.selectProduct(productId)
                        }}
                        customerName={customers.find(c => c.id === watchedValues.customer_id)?.name}
                        disabled={isSubmitting}
                        loading={relationshipState.loadingProducts}
                        showDetails={true}
                        compact={false}
                      />
                    </div>
                  )}

                  {/* Enhanced Consignee-Notify Party Integration with Customer Relationships */}
                  <div>
                    <EnhancedConsigneeIntegration
                      selectedCustomerId={watchedValues.customer_id || undefined}
                      selectedConsigneeId={
                        watchedValues.consignee_id || undefined
                      }
                      selectedNotifyPartyId={
                        watchedValues.notify_party_id || undefined
                      }
                      onConsigneeChange={handleConsigneeChange}
                      onNotifyPartyChange={handleNotifyPartyChange}
                      autoSelectDefault={config.autoSelectDefaults}
                      showRelationshipDetails={true}
                      disabled={isSubmitting}
                      compact={true}
                    />
                    
                    {/* Validation Error Messages */}
                    {form.formState.errors.consignee_id && (
                      <Alert className="mt-2">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-red-400">
                          {form.formState.errors.consignee_id.message}
                        </AlertDescription>
                      </Alert>
                    )}
                    {form.formState.errors.notify_party_id && (
                      <Alert className="mt-2">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-red-400">
                          {form.formState.errors.notify_party_id.message}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Route and Schedule Information */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-green-200 flex items-center gap-2">
                <MapPin className="h-5 w-5 text-green-500" />
                Route & Schedule
              </CardTitle>
              <CardDescription className="text-slate-400">
                Configure the routing and timing for this shipment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Port Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="origin_port_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Origin Port *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select origin port" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                          {ports.filter(port => port.port_type === 'origin').length === 0 ? (
                            <div className="p-4 text-center text-slate-400">
                              No origin ports available
                            </div>
                          ) : (
                            ports.filter(port => port.port_type === 'origin').map(port => (
                            <SelectItem
                              key={port.id}
                              value={port.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Ship className="h-3 w-3 text-blue-500" />
                                <div>
                                  <div>
                                    {port.port_name} ({port.code})
                                  </div>
                                  <div className="text-xs text-slate-400">
                                    {port.country}
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="destination_port_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Destination Port *
                      </FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select destination port" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                          {ports.filter(port => port.port_type === 'destination').length === 0 ? (
                            <div className="p-4 text-center text-slate-400">
                              No destination ports available
                            </div>
                          ) : (
                            ports.filter(port => port.port_type === 'destination').map(port => (
                            <SelectItem
                              key={port.id}
                              value={port.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Ship className="h-3 w-3 text-green-500" />
                                <div>
                                  <div>
                                    {port.port_name} ({port.code})
                                  </div>
                                  <div className="text-xs text-slate-400">
                                    {port.country}
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Date/Time Fields with Sequence Validation */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="closing_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Closing Time *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="datetime-local"
                          className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400 text-xs">
                        Document submission deadline
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="etd_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        ETD (Departure) *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="date"
                          className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400 text-xs">
                        Estimated departure date
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="eta_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        ETA (Arrival) *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="date"
                          className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400 text-xs">
                        Estimated arrival date
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Additional Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="booking_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Booking Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter booking number"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vessel_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Vessel Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter vessel name"
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Document Upload */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-yellow-200 flex items-center gap-2">
                <FileText className="h-5 w-5 text-yellow-500" />
                Document Upload
              </CardTitle>
              <CardDescription className="text-slate-400">
                Upload the mandatory booking confirmation document
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Document Upload Area */}
              <div className="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center hover:border-slate-500 transition-colors">
                {!uploadedDocument ? (
                  <Label
                    htmlFor="document-upload"
                    className="cursor-pointer block space-y-4"
                  >
                    <div className="mx-auto w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center">
                      <Upload className="h-6 w-6 text-slate-400" />
                    </div>
                    <div>
                      <p className="text-slate-200 hover:text-white transition-colors">
                        Click to upload booking confirmation document
                      </p>
                      <p className="text-slate-400 text-sm mt-1">
                        PDF, DOC, DOCX, JPG, PNG up to 10MB
                      </p>
                    </div>
                  </Label>
                ) : (
                  <div className="flex items-center justify-center space-x-4">
                    <CheckCircle className="h-8 w-8 text-green-400" />
                    <div className="text-left">
                      <p className="text-white font-medium">
                        {uploadedDocument.name}
                      </p>
                      <p className="text-slate-400 text-sm">
                        {(uploadedDocument.size / 1024 / 1024).toFixed(2)} MB -
                        Uploaded successfully
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setUploadedDocument(null)
                        setUploadProgress(0)
                        // Reset the file input value to allow reselecting the same file
                        const fileInput = document.getElementById(
                          'document-upload'
                        ) as HTMLInputElement
                        if (fileInput) {
                          fileInput.value = ''
                        }
                      }}
                      disabled={isSubmitting}
                      className="bg-slate-800 border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      Change
                    </Button>
                  </div>
                )}
              </div>
              <Input
                id="document-upload"
                type="file"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={e => handleDocumentUpload(e.target.files)}
                className="hidden"
                disabled={isSubmitting || isUploading}
              />

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Uploading...</span>
                    <span className="text-slate-400">{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-slate-600 rounded-full h-2">
                    <div
                      className="bg-orange-500 h-2 rounded-full transition-all duration-200"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notes */}
          <Card className="bg-slate-700 border-slate-600">
            <CardContent className="pt-6">
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Notes and Special Instructions
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter any additional notes or special instructions for stakeholders..."
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[100px]"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      This information will be visible to all stakeholders and
                      included in shipment documentation
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isSubmitting}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isSubmitting ||
                isCreatingShipment ||
                !selectedTransportMode ||
                !uploadedDocument
              }
              className="bg-orange-500 hover:bg-orange-600 text-white"
              onClick={() => console.log('Create Shipment button clicked')}
            >
              {(isSubmitting || isCreatingShipment) && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Create Shipment
            </Button>
          </div>
        </form>
      </Form>

      {/* Debug Section - remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-4 bg-slate-800 rounded-lg">
          <div className="flex items-center gap-4 mb-4">
            <Button 
              type="button" 
              onClick={testValidation}
              variant="outline"
              size="sm"
              className="bg-blue-600 border-blue-500 text-white hover:bg-blue-500"
            >
              Test Validation
            </Button>
            <Button 
              type="button" 
              onClick={() => {
                console.log('Clearing form fields for testing...')
                form.setValue('consignee_id', '', { shouldValidate: true })
                form.setValue('notify_party_id', '', { shouldValidate: true })
                form.setValue('vessel_name', '', { shouldValidate: true })
                form.setValue('booking_number', '', { shouldValidate: true })
                form.trigger() // Trigger validation for all fields
              }}
              variant="outline"
              size="sm"
              className="bg-red-600 border-red-500 text-white hover:bg-red-500"
            >
              Clear & Validate
            </Button>
          </div>
          <div className="text-xs text-slate-400">
            <div>Form Errors:</div>
            <pre className="mt-2 overflow-auto max-h-32">
              {JSON.stringify(form.formState.errors, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Success Dialog */}
      <AlertDialog open={showSuccessDialog && !isSubmitting} onOpenChange={setShowSuccessDialog}>
        <AlertDialogContent className="bg-slate-900 border-slate-700">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-green-400 flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Shipment Created Successfully!
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-300">
              Your shipment has been created successfully and is ready for processing.
              
              <div className="mt-4 p-4 bg-slate-800 rounded-lg border border-slate-600">
                <div className="flex items-center justify-between">
                  <span className="text-slate-400 flex items-center gap-2">
                    <Ship className="h-4 w-4" />
                    Shipment Number:
                  </span>
                  <span className="text-green-400 font-mono font-bold text-xl">
                    {createdShipmentNumber}
                  </span>
                </div>
              </div>
              
              <div className="mt-3 p-3 bg-green-500/10 rounded-lg border border-green-500/30">
                <p className="text-green-300 text-sm flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  Please save this shipment number for tracking and future reference.
                </p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction 
              onClick={handleSuccessDialogOK}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              OK
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
