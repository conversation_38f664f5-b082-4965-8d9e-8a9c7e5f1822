@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215 27% 17%;
    --card: 0 0% 100%;
    --card-foreground: 215 27% 17%;
    --popover: 0 0% 100%;
    --popover-foreground: 215 27% 17%;
    --primary: 215 27% 17%;
    --primary-foreground: 0 0% 98%;
    --secondary: 215 16% 47%;
    --secondary-foreground: 0 0% 98%;
    --muted: 210 40% 98%;
    --muted-foreground: 215 16% 47%;
    --accent: 25 95% 53%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 215 27% 17%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222 84% 5%;
    --foreground: 210 40% 98%;
    --card: 215 27% 17%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 5%;
    --popover-foreground: 210 40% 98%;
    --primary: 215 27% 17%;
    --primary-foreground: 0 0% 98%;
    --secondary: 215 16% 47%;
    --secondary-foreground: 0 0% 98%;
    --muted: 215 27% 17%;
    --muted-foreground: 215 16% 65%;
    --accent: 25 95% 53%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 98%;
    --border: 215 27% 17%;
    --input: 215 27% 17%;
    --ring: 215 16% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
