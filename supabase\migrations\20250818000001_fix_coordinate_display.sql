-- Fix coordinate display by updating PostGIS functions to return text coordinates
-- This addresses the issue where coordinates are returned in binary format

-- ============================================================================
-- UPDATE GEOGRAPHIC SEARCH FUNCTIONS TO RETURN TEXT COORDINATES
-- ============================================================================

-- Function to find ports within a specified radius from a center point
CREATE OR REPLACE FUNCTION find_ports_within_radius(
    center_lat FLOAT,
    center_lng FLOAT,
    radius_km INTEGER DEFAULT 100
)
RETURNS TABLE (
    id UUID,
    code TEXT,
    name TEXT,
    city TEXT,
    country TEXT,
    port_type port_type_enum,
    gps_coordinates TEXT,  -- Changed from GEOGRAPHY to TEXT
    distance_km NUMERIC,
    is_active BOOLEAN,
    timezone TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.code,
        p.name,
        p.city,
        p.country,
        p.port_type,
        ST_AsText(p.gps_coordinates) as gps_coordinates,  -- Convert to text
        ROUND(
            ST_Distance(
                ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
                p.gps_coordinates
            ) / 1000, 2
        ) AS distance_km,
        p.is_active,
        p.timezone,
        p.created_at,
        p.updated_at
    FROM ports p
    WHERE p.is_active = true
    AND p.gps_coordinates IS NOT NULL
    AND ST_DWithin(
        ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
        p.gps_coordinates,
        radius_km * 1000 -- Convert km to meters
    )
    ORDER BY ST_Distance(
        ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
        p.gps_coordinates
    );
END;
$$;

-- Function to get ports by country with distance from a reference point
CREATE OR REPLACE FUNCTION get_ports_by_country_with_distance(
    target_country TEXT,
    ref_lat FLOAT DEFAULT NULL,
    ref_lng FLOAT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    code TEXT,
    name TEXT,
    city TEXT,
    country TEXT,
    port_type port_type_enum,
    gps_coordinates TEXT,  -- Changed from GEOGRAPHY to TEXT
    distance_km NUMERIC,
    is_active BOOLEAN,
    timezone TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
    IF ref_lat IS NULL OR ref_lng IS NULL THEN
        -- Return without distance calculation
        RETURN QUERY
        SELECT 
            p.id,
            p.code,
            p.name,
            p.city,
            p.country,
            p.port_type,
            ST_AsText(p.gps_coordinates) as gps_coordinates,  -- Convert to text
            NULL::NUMERIC AS distance_km,
            p.is_active,
            p.timezone,
            p.created_at,
            p.updated_at
        FROM ports p
        WHERE p.country ILIKE '%' || target_country || '%'
        AND p.is_active = true
        ORDER BY p.name;
    ELSE
        -- Return with distance calculation
        RETURN QUERY
        SELECT 
            p.id,
            p.code,
            p.name,
            p.city,
            p.country,
            p.port_type,
            ST_AsText(p.gps_coordinates) as gps_coordinates,  -- Convert to text
            CASE 
                WHEN p.gps_coordinates IS NOT NULL THEN
                    ROUND(
                        ST_Distance(
                            ST_GeogFromText('POINT(' || ref_lng || ' ' || ref_lat || ')'),
                            p.gps_coordinates
                        ) / 1000, 2
                    )
                ELSE NULL
            END AS distance_km,
            p.is_active,
            p.timezone,
            p.created_at,
            p.updated_at
        FROM ports p
        WHERE p.country ILIKE '%' || target_country || '%'
        AND p.is_active = true
        ORDER BY 
            CASE 
                WHEN p.gps_coordinates IS NOT NULL THEN
                    ST_Distance(
                        ST_GeogFromText('POINT(' || ref_lng || ' ' || ref_lat || ')'),
                        p.gps_coordinates
                    )
                ELSE 999999999 -- Put ports without coordinates at the end
            END;
    END IF;
END;
$$;

-- Function to find nearest ports to a given point
CREATE OR REPLACE FUNCTION find_nearest_ports(
    center_lat FLOAT,
    center_lng FLOAT,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    code TEXT,
    name TEXT,
    city TEXT,
    country TEXT,
    port_type port_type_enum,
    gps_coordinates TEXT,  -- Changed from GEOGRAPHY to TEXT
    distance_km NUMERIC,
    is_active BOOLEAN,
    timezone TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.code,
        p.name,
        p.city,
        p.country,
        p.port_type,
        ST_AsText(p.gps_coordinates) as gps_coordinates,  -- Convert to text
        ROUND(
            ST_Distance(
                ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
                p.gps_coordinates
            ) / 1000, 2
        ) AS distance_km,
        p.is_active,
        p.timezone
    FROM ports p
    WHERE p.is_active = true
    AND p.gps_coordinates IS NOT NULL
    ORDER BY ST_Distance(
        ST_GeogFromText('POINT(' || center_lng || ' ' || center_lat || ')'),
        p.gps_coordinates
    )
    LIMIT limit_count;
END;
$$;

-- Function to get ports within a geographic bounding box
CREATE OR REPLACE FUNCTION get_ports_in_bounds(
    min_lat FLOAT,
    min_lng FLOAT,
    max_lat FLOAT,
    max_lng FLOAT
)
RETURNS TABLE (
    id UUID,
    code TEXT,
    name TEXT,
    city TEXT,
    country TEXT,
    port_type port_type_enum,
    gps_coordinates TEXT,  -- Changed from GEOGRAPHY to TEXT
    is_active BOOLEAN,
    timezone TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.code,
        p.name,
        p.city,
        p.country,
        p.port_type,
        ST_AsText(p.gps_coordinates) as gps_coordinates,  -- Convert to text
        p.is_active,
        p.timezone
    FROM ports p
    WHERE p.is_active = true
    AND p.gps_coordinates IS NOT NULL
    AND ST_Intersects(
        p.gps_coordinates,
        ST_GeogFromText('POLYGON((' || min_lng || ' ' || min_lat || ',' || max_lng || ' ' || min_lat || ',' || max_lng || ' ' || max_lat || ',' || min_lng || ' ' || max_lat || ',' || min_lng || ' ' || min_lat || '))')
    )
    ORDER BY p.name;
END;
$$;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION find_ports_within_radius(FLOAT, FLOAT, INTEGER) IS 'Find all active ports within specified radius (km) from center coordinates, sorted by distance. Returns coordinates as WKT text.';
COMMENT ON FUNCTION get_ports_by_country_with_distance(TEXT, FLOAT, FLOAT) IS 'Get ports by country with optional distance calculation from reference point. Returns coordinates as WKT text.';
COMMENT ON FUNCTION find_nearest_ports(FLOAT, FLOAT, INTEGER) IS 'Find nearest active ports to given coordinates, limited by count. Returns coordinates as WKT text.';
COMMENT ON FUNCTION get_ports_in_bounds(FLOAT, FLOAT, FLOAT, FLOAT) IS 'Get all active ports within a geographic bounding box. Returns coordinates as WKT text.';