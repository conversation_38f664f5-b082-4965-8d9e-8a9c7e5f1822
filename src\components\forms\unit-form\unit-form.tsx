'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Info, Settings } from 'lucide-react'
import {
  unitOfMeasureFormSchema,
  type UnitOfMeasureForm,
} from '@/lib/validations/products'
import {
  useUnitsData,
  useUnitValidation,
  UNIT_CATEGORIES,
} from '@/hooks/use-units'
import type { UnitOfMeasure } from '@/lib/supabase/types'

interface UnitFormProps {
  unit?: UnitOfMeasure | null
  onSubmit: (data: UnitOfMeasureForm) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  className?: string
}

export function UnitForm({
  unit,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: UnitFormProps) {
  const [error, setError] = useState<string | null>(null)
  const { activeUnits } = useUnitsData()
  const {
    validateUniqueCode,
    validateConversionFactor,
    validateCircularReference,
  } = useUnitValidation()

  // Filter base units - exclude current unit and its descendants
  const availableBaseUnits = activeUnits.filter(baseUnit => {
    if (unit && baseUnit.id === unit.id) return false
    if (unit && baseUnit.base_unit_id === unit.id) return false
    return true
  })

  const form = useForm<UnitOfMeasureForm>({
    resolver: zodResolver(unitOfMeasureFormSchema),
    defaultValues: {
      code: unit?.code || '',
      name: unit?.name || '',
      symbol: unit?.symbol || '',
      category: (unit?.category as any) || 'count',
      conversion_factor: unit?.conversion_factor || 1.0,
      base_unit_id: unit?.base_unit_id || null,
    },
  })

  const watchedCategory = form.watch('category')
  const watchedBaseUnitId = form.watch('base_unit_id')
  const watchedConversionFactor = form.watch('conversion_factor')

  // Filter base units by category
  const filteredBaseUnits = availableBaseUnits.filter(
    baseUnit => baseUnit.category === watchedCategory
  )

  // Auto-adjust conversion factor when base unit changes
  useEffect(() => {
    if (!watchedBaseUnitId || watchedBaseUnitId === 'none') {
      form.setValue('conversion_factor', 1.0)
    }
  }, [watchedBaseUnitId, form])

  // Real-time validation
  const watchedCode = form.watch('code')
  useEffect(() => {
    if (watchedCode && !validateUniqueCode(watchedCode, unit?.id)) {
      form.setError('code', {
        type: 'manual',
        message: 'Unit code already exists',
      })
    }
  }, [watchedCode, validateUniqueCode, unit?.id, form])

  // Validate conversion factor
  useEffect(() => {
    const factorError = validateConversionFactor(
      watchedConversionFactor,
      watchedBaseUnitId || undefined
    )
    if (factorError) {
      form.setError('conversion_factor', {
        type: 'manual',
        message: factorError,
      })
    } else {
      form.clearErrors('conversion_factor')
    }
  }, [
    watchedConversionFactor,
    watchedBaseUnitId,
    validateConversionFactor,
    form,
  ])

  // Validate circular reference
  useEffect(() => {
    if (unit?.id && watchedBaseUnitId) {
      const circularError = validateCircularReference(
        unit.id,
        watchedBaseUnitId
      )
      if (circularError) {
        form.setError('base_unit_id', {
          type: 'manual',
          message: circularError,
        })
      } else {
        form.clearErrors('base_unit_id')
      }
    }
  }, [unit?.id, watchedBaseUnitId, validateCircularReference, form])

  const handleSubmit = async (data: UnitOfMeasureForm) => {
    try {
      setError(null)
      // Ensure base_unit_id is properly set to null if empty or 'none'
      const formData = {
        ...data,
        base_unit_id: data.base_unit_id === 'none' ? null : data.base_unit_id,
      }
      await onSubmit(formData)
    } catch (error) {
      console.error('Form submission error:', error)
      setError(error instanceof Error ? error.message : 'Failed to save unit')
    }
  }

  const isBaseUnit = !watchedBaseUnitId || watchedBaseUnitId === 'none'

  return (
    <Card className={`bg-slate-800 border-slate-700 ${className}`}>
      <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
        <CardTitle className="text-white flex items-center gap-2">
          <Settings className="h-5 w-5 text-orange-500" />
          {unit ? 'Edit Unit of Measure' : 'Create Unit of Measure'}
        </CardTitle>
      </CardHeader>
      <CardContent className="bg-slate-800 text-slate-100">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      Code *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., KG, PCS, LTR"
                        className="uppercase bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        onChange={e =>
                          field.onChange(e.target.value.toUpperCase())
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Unique identifier (uppercase letters, numbers, hyphens,
                      underscores)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="symbol"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      Symbol *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., kg, pcs, L"
                        maxLength={10}
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Display symbol (max 10 characters)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200 font-medium">
                    Name *
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      placeholder="e.g., Kilogram, Pieces, Liter"
                    />
                  </FormControl>
                  <FormDescription className="text-slate-400">
                    Full name of the unit of measure
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200 font-medium">
                    Category *
                  </FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                        <SelectValue
                          placeholder="Select category"
                          className="text-slate-400"
                        />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        {UNIT_CATEGORIES.map(category => (
                          <SelectItem
                            key={category.value}
                            value={category.value}
                            className="text-white hover:bg-slate-600"
                          >
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription className="text-slate-400">
                    Type of measurement (weight, count, volume, length)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Base Unit and Conversion */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Info className="h-4 w-4 text-blue-500" />
                <span className="text-sm text-slate-600">
                  {isBaseUnit
                    ? 'This will be a base unit with conversion factor 1.0'
                    : 'Select a base unit and specify the conversion factor'}
                </span>
              </div>

              <FormField
                control={form.control}
                name="base_unit_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      Base Unit (Optional)
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value || 'none'}
                        onValueChange={value =>
                          field.onChange(value === 'none' ? null : value)
                        }
                      >
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue
                            placeholder="None (This is a base unit)"
                            className="text-slate-400"
                          />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          <SelectItem
                            value="none"
                            className="text-white hover:bg-slate-600"
                          >
                            None (This is a base unit)
                          </SelectItem>
                          {filteredBaseUnits.map(baseUnit => (
                            <SelectItem
                              key={baseUnit.id}
                              value={baseUnit.id}
                              className="text-white hover:bg-slate-600"
                            >
                              {baseUnit.name} ({baseUnit.symbol})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Base unit for conversion calculations (must be same
                      category)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="conversion_factor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      Conversion Factor *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.000001"
                        min="0.000001"
                        disabled={isBaseUnit}
                        className={`${isBaseUnit ? 'bg-slate-600 text-slate-400' : 'bg-slate-700 text-white'} border-slate-600 placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500`}
                        onChange={e =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      {isBaseUnit
                        ? 'Base units always have a conversion factor of 1.0'
                        : 'How many of this unit equals 1 base unit (e.g., 1000 for grams to kilograms)'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-4 border-t border-slate-600 mt-6">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Cancel
                </Button>
              )}
              <Button
                type="submit"
                disabled={isLoading || !form.formState.isValid}
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white border-none"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {unit ? 'Update Unit' : 'Create Unit'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
