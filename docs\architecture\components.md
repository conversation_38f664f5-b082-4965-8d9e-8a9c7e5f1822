# Components

Based on the architectural patterns, Next.js/Supabase tech stack, and data models, the system is organized into logical components that span both frontend and backend concerns for the fruit export management domain.

## Frontend Application Layer

**Responsibility:** Next.js App Router application providing web dashboard for CS/Admin users and mobile-responsive interfaces for customers and stakeholders.

**Key Interfaces:**
- React Server Components for optimal performance
- Client Components for real-time interactivity
- ShadCN UI components with dark blue theme
- Progressive Web App service worker registration

**Dependencies:** Supabase Client, Zustand State Management, Tailwind CSS

**Technology Stack:** Next.js 14+ App Router, TypeScript, ShadCN UI, Tailwind CSS

## Mobile PWA Layer

**Responsibility:** Progressive Web App providing offline-capable mobile interface specifically for drivers with photo capture, GPS location, and status update capabilities.

**Key Interfaces:**
- Service Worker for offline functionality
- Camera API for photo capture
- Geolocation API for GPS coordinates
- Background sync for queued status updates
- Push notifications for assignment alerts

**Dependencies:** Frontend Application Layer, Supabase Storage, Browser APIs

**Technology Stack:** Next.js PWA configuration, Service Worker, Web APIs

## Real-time Subscription Manager

**Responsibility:** Manages Supabase real-time subscriptions for live shipment status updates, assignment notifications, and multi-user coordination.

**Key Interfaces:**
- WebSocket connection management
- Subscription lifecycle handling
- State synchronization with Zustand
- Connection retry and error handling

**Dependencies:** Supabase Client, Frontend State Management

**Technology Stack:** Supabase Real-time, WebSocket API

## Relationship Intelligence Engine

**Responsibility:** Implements the intelligent pre-population logic for cascading selections (customer → shippers/products, consignee → notify parties) to achieve the 60% data entry reduction goal.

**Key Interfaces:**
- Customer defaults resolution API
- Relationship validation and management
- Default preference management
- Bulk relationship operations

**Dependencies:** Supabase Client, Database Relationship Tables

**Technology Stack:** TypeScript business logic, Supabase queries

## Authentication & Authorization Module

**Responsibility:** Role-based access control supporting 11 user types with Row Level Security policy enforcement and session management.

**Key Interfaces:**
- Supabase Auth integration
- Role-based route protection
- User profile management
- Company association handling

**Dependencies:** Supabase Auth, Next.js middleware

**Technology Stack:** Supabase Auth, Next.js App Router middleware

## Component Diagrams

```mermaid
graph TB
    subgraph "Frontend Layer"
        FE[Frontend Application Layer]
        PWA[Mobile PWA Layer]
        RTM[Real-time Subscription Manager]
        RIE[Relationship Intelligence Engine]
    end
    
    subgraph "Business Logic Layer"
        AUTH[Authentication & Authorization Module]
        MDM[Master Data Management System]
        SWE[Shipment Workflow Engine]
        DGS[Document Generation Service]
    end
    
    subgraph "Infrastructure Layer"
        MNS[Multi-Channel Notification System]
        FSM[File Storage & Processing Manager]
        DSM[Database Schema Manager]
    end
    
    subgraph "External Services"
        EMAIL[Email Service]
        SMS[SMS Service]
        LINE[Line API]
        WECHAT[WeChat API]
    end
    
    subgraph "Supabase Platform"
        DB[(Database)]
        STORAGE[Storage]
        EDGE[Edge Functions]
        REALTIME[Real-time]
        SAUTH[Supabase Auth]
    end
    
    FE --> AUTH
    FE --> MDM
    FE --> SWE
    PWA --> FSM
    PWA --> SWE
    RTM --> REALTIME
    RIE --> DB
    
    AUTH --> SAUTH
    MDM --> DB
    SWE --> DB
    DGS --> EDGE
    DGS --> STORAGE
    
    MNS --> EDGE
    MNS --> EMAIL
    MNS --> SMS
    MNS --> LINE
    MNS --> WECHAT
    
    FSM --> STORAGE
    DSM --> DB
    
    EDGE --> DB
    EDGE --> STORAGE
```
