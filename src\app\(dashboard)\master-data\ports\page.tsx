'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  MapPin,
  Eye,
  X,
  Navigation,
  Globe,
  Calendar,
} from 'lucide-react'
import { PortForm } from '@/components/forms/port-form/port-form'
import { usePortsManagement } from '@/hooks/use-ports'
import type { Port } from '@/stores/port-store'
import type { PortForm as PortFormData } from '@/lib/validations/ports'
import { formatDistanceToNow, format } from 'date-fns'

export default function PortsPage() {
  const {
    // Data
    ports,
    loading,
    error,
    countries,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedPorts,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createPort,
    updatePort,
    deletePort,
    bulkDeletePorts,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    togglePort,
    toggleAll,
    clearSelection,
    clearError,
    refreshPorts,

    // Geographic utilities
    parseCoordinates,
    formatCoordinates,

    // Categories
    portTypeOptions,
    getPortTypeLabel,
  } = usePortsManagement()

  // UI state
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingPort, setEditingPort] = useState<Port | null>(null)
  const [viewingPort, setViewingPort] = useState<Port | null>(null)
  const [deletingPort, setDeletingPort] = useState<Port | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // Handle create port
  const handleCreate = async (data: PortFormData) => {
    try {
      // Convert GPS coordinates object to string for database storage
      const processedData = {
        ...data,
        gps_coordinates: data.gps_coordinates
          ? JSON.stringify(data.gps_coordinates)
          : undefined,
      }
      await createPort(processedData)
      setShowCreateDialog(false)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle update port
  const handleUpdate = async (data: PortFormData) => {
    if (!editingPort) return

    try {
      // Convert GPS coordinates object to string for database storage
      const processedData = {
        ...data,
        gps_coordinates: data.gps_coordinates
          ? JSON.stringify(data.gps_coordinates)
          : undefined,
      }
      await updatePort(editingPort.id, processedData)
      setEditingPort(null)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle delete port
  const handleDelete = async (port: Port) => {
    try {
      await deletePort(port.id)
      setDeletingPort(null)
    } catch (error) {
      // Error handling is done in the store
      console.error('Delete failed:', error)
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      await bulkDeletePorts(selectedPorts)
      setShowBulkDeleteDialog(false)
      clearSelection()
    } catch (error) {
      // Error handling is done in the store
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle sorting
  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSorting(column, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSorting(column, 'asc')
    }
  }

  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  // Format coordinates for display
  const formatPortCoordinates = (gpsCoordinates: string | object | null) => {
    if (!gpsCoordinates) return 'No coordinates'

    const coords = parseCoordinates(gpsCoordinates)
    if (!coords) return 'Invalid coordinates'

    return formatCoordinates(coords.lat, coords.lng)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Ports</h1>
          <p className="text-slate-400 mt-1">
            Manage port locations and shipping routes
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Port
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">Create Port</DialogTitle>
              <DialogDescription className="text-slate-400">
                Add a new port with location data and GPS coordinates
              </DialogDescription>
            </DialogHeader>
            <PortForm
              onSubmit={handleCreate}
              onCancel={() => setShowCreateDialog(false)}
              isLoading={isCreating}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Search */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {(searchTerm || filter.port_type || filter.country) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFilter({})
                setSearchTerm('')
              }}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Search Ports
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search by name, code, city, or country..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
          </div>

          {/* Port Type Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Port Type
            </label>
            <Select
              value={filter.port_type || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  port_type: value === 'all' ? undefined : (value as any),
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Port Types" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Port Types
                </SelectItem>
                {portTypeOptions.map(type => (
                  <SelectItem
                    key={type.value}
                    value={type.value}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Country Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Country
            </label>
            <Select
              value={filter.country || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  country: value === 'all' ? undefined : value,
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Countries" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Countries
                </SelectItem>
                {countries.map(country => (
                  <SelectItem
                    key={country}
                    value={country}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {country}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {(searchTerm || filter.port_type || filter.country) && (
          <div className="flex flex-wrap gap-2 pt-2">
            {searchTerm && (
              <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-2 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.port_type && (
              <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                Type: {getPortTypeLabel(filter.port_type)}
                <button
                  onClick={() => setFilter({ ...filter, port_type: undefined })}
                  className="ml-2 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.country && (
              <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
                Country: {filter.country}
                <button
                  onClick={() => setFilter({ ...filter, country: undefined })}
                  className="ml-2 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedCount} port{selectedCount !== 1 ? 's' : ''} selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowBulkDeleteDialog(true)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Ports Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <MapPin className="h-5 w-5 text-orange-500" />
              Ports ({totalCount})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshPorts}
              disabled={loading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {loading && ports.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-slate-300">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              <span className="ml-2">Loading ports...</span>
            </div>
          ) : ports.length === 0 ? (
            <div className="text-center py-8">
              <MapPin className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No ports found</p>
              <p className="text-sm text-slate-400 mt-1">
                Create your first port to get started
              </p>
            </div>
          ) : (
            <>
              <Table className="bg-slate-800">
                <TableHeader className="bg-slate-700">
                  <TableRow className="border-slate-600 hover:bg-slate-700">
                    <TableHead className="w-12 text-slate-200">
                      <Checkbox
                        checked={isAllSelected || isPartiallySelected}
                        onCheckedChange={toggleAll}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('code')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Code {getSortIcon('code')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('name')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Name {getSortIcon('name')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">Location</TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('port_type')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Type {getSortIcon('port_type')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      Coordinates
                    </TableHead>
                    <TableHead className="text-slate-200">Status</TableHead>
                    <TableHead className="w-32 text-slate-200">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-slate-800">
                  {ports.map(port => (
                    <TableRow
                      key={port.id}
                      className="border-slate-600 hover:bg-slate-700 text-slate-200"
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected(port.id)}
                          onCheckedChange={() => togglePort(port.id)}
                          className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                      </TableCell>
                      <TableCell className="font-medium font-mono">
                        {port.code}
                      </TableCell>
                      <TableCell className="font-medium">{port.name}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{port.city}</div>
                          <div className="text-slate-400">{port.country}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`${
                            port.port_type === 'origin'
                              ? 'border-blue-400 text-blue-200 bg-blue-500/20'
                              : port.port_type === 'destination'
                                ? 'border-green-400 text-green-200 bg-green-500/20'
                                : 'border-yellow-400 text-yellow-200 bg-yellow-500/20'
                          }`}
                        >
                          {getPortTypeLabel(port.port_type)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-slate-300 font-mono">
                          {formatPortCoordinates(port.gps_coordinates)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={port.is_active ? 'default' : 'secondary'}
                          className={
                            port.is_active
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                          }
                        >
                          {port.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setViewingPort(port)}
                            className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingPort(port)}
                            className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingPort(port)}
                            className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-6 pb-6">
                  <div className="text-sm text-slate-300">
                    Page {currentPage} of {totalPages} ({totalCount} ports)
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(1)}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={previousPage}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* View Port Dialog */}
      {viewingPort && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-slate-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">Port Details</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewingPort(null)}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Port Avatar and Basic Info */}
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl font-medium">
                    {viewingPort.code.slice(0, 2).toUpperCase()}
                  </span>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white">
                    {viewingPort.name}
                  </h3>
                  <p className="text-slate-400">{viewingPort.code}</p>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge
                      variant="outline"
                      className={`${
                        viewingPort.port_type === 'origin'
                          ? 'border-blue-400 text-blue-200 bg-blue-500/20'
                          : viewingPort.port_type === 'destination'
                            ? 'border-green-400 text-green-200 bg-green-500/20'
                            : 'border-yellow-400 text-yellow-200 bg-yellow-500/20'
                      }`}
                    >
                      <MapPin className="h-3 w-3 mr-1" />
                      {getPortTypeLabel(viewingPort.port_type)}
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <div
                        className={`w-2 h-2 rounded-full ${viewingPort.is_active ? 'bg-green-400' : 'bg-red-400'}`}
                      />
                      <span
                        className={`text-sm ${viewingPort.is_active ? 'text-green-300' : 'text-red-300'}`}
                      >
                        {viewingPort.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Location Information */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Globe className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Location</span>
                </div>
                <p className="text-slate-300 text-sm">
                  {viewingPort.city
                    ? `${viewingPort.city}, ${viewingPort.country}`
                    : viewingPort.country}
                </p>
              </div>

              {/* Port Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <MapPin className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Port Code</span>
                  </div>
                  <p className="text-slate-300 font-mono">{viewingPort.code}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Globe className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Country</span>
                  </div>
                  <p className="text-slate-300">{viewingPort.country}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Navigation className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">City</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingPort.city || 'No city specified'}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Globe className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Timezone</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingPort.timezone || 'No timezone specified'}
                  </p>
                </div>
              </div>

              {/* GPS Coordinates */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Navigation className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">
                    GPS Coordinates
                  </span>
                </div>
                <p className="text-slate-300 font-mono text-sm">
                  {formatPortCoordinates(viewingPort.gps_coordinates)}
                </p>
              </div>

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Created</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingPort.created_at
                      ? format(new Date(viewingPort.created_at), 'PPP')
                      : 'Unknown'}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {viewingPort.created_at
                      ? formatDistanceToNow(new Date(viewingPort.created_at), {
                          addSuffix: true,
                        })
                      : ''}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Edit className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Last Updated</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingPort.updated_at
                      ? format(new Date(viewingPort.updated_at), 'PPP')
                      : 'Unknown'}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {viewingPort.updated_at
                      ? formatDistanceToNow(new Date(viewingPort.updated_at), {
                          addSuffix: true,
                        })
                      : ''}
                  </p>
                </div>
              </div>

              {/* Port ID */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <MapPin className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Port ID</span>
                </div>
                <p className="text-slate-300 font-mono text-sm">
                  {viewingPort.id}
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => setViewingPort(null)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Port Dialog */}
      <Dialog open={!!editingPort} onOpenChange={() => setEditingPort(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">Edit Port</DialogTitle>
            <DialogDescription className="text-slate-400">
              Update port information and location data
            </DialogDescription>
          </DialogHeader>
          {editingPort && (
            <PortForm
              port={editingPort}
              onSubmit={handleUpdate}
              onCancel={() => setEditingPort(null)}
              isLoading={isUpdating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Port Dialog */}
      <AlertDialog
        open={!!deletingPort}
        onOpenChange={() => setDeletingPort(null)}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Port
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete &quot;{deletingPort?.name}
              &quot; ({deletingPort?.code})? This action cannot be undone.
              {deletingPort && (
                <div className="mt-2 p-2 bg-slate-700 rounded text-sm border border-slate-600">
                  <strong className="text-white">Code:</strong>{' '}
                  <span className="text-slate-200 font-mono">
                    {deletingPort.code}
                  </span>
                  <br />
                  <strong className="text-white">Name:</strong>{' '}
                  <span className="text-slate-200">{deletingPort.name}</span>
                  <br />
                  <strong className="text-white">Location:</strong>{' '}
                  <span className="text-slate-200">
                    {deletingPort.city}, {deletingPort.country}
                  </span>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setDeletingPort(null)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deletingPort && handleDelete(deletingPort)}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Port
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Selected Ports
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete {selectedCount} selected port
              {selectedCount !== 1 ? 's' : ''}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowBulkDeleteDialog(false)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Ports
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
