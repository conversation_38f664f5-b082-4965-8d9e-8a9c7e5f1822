import { renderHook, waitFor } from '@testing-library/react'
import { act } from '@testing-library/react'
import { vi } from 'vitest'
import { 
  useTransportation, 
  useCarriers, 
  useDriversByCarrier, 
  useCoordinateUtils 
} from '@/hooks/use-transportation'
import { createClient } from '@/lib/supabase/client'

// Mock Supabase client
vi.mock('@/lib/supabase/client')
vi.mock('@/hooks/use-auth', () => ({
  useAuth: () => ({
    user: { id: 'user-123' },
    isStaff: true,
  })
}))

// Mock data
const mockTransportation = {
  id: 'transport-1',
  shipment_id: 'shipment-123',
  carrier_id: 'carrier-1',
  driver_id: 'driver-1',
  vehicle_head_number: 'HEAD-123',
  vehicle_tail_number: 'TAIL-456',
  driver_phone: '+66123456789',
  assignment_date: '2024-01-01T10:00:00Z',
  pickup_container_location: 'Container Terminal A',
  pickup_container_gps_coordinates: 'POINT(100.5018 13.7563)',
  pickup_product_location: 'Factory B',
  pickup_product_gps_coordinates: 'POINT(100.5118 13.7663)',
  delivery_location: 'Warehouse C',
  delivery_gps_coordinates: 'POINT(100.5218 13.7763)',
  notes: 'Handle with care',
  estimated_distance: 15.5,
  created_at: '2024-01-01T09:00:00Z',
  carrier: {
    id: 'carrier-1',
    name: 'Test Carrier',
    company_type: 'carrier'
  },
  driver: {
    user_id: 'driver-1',
    driver_first_name: 'John',
    driver_last_name: 'Doe',
    phone: '+66123456789',
    line_id: 'johndoe',
    is_active: true
  },
  shipment: {
    id: 'shipment-123',
    shipment_number: 'SHP-2024-001',
    status: 'booking_confirmed'
  }
}

const mockCarriers = [
  {
    id: 'carrier-1',
    name: 'Test Carrier 1',
    company_type: 'carrier',
    phone: '+66123456789',
    email: '<EMAIL>',
    is_active: true,
  },
  {
    id: 'carrier-2',
    name: 'Test Carrier 2', 
    company_type: 'carrier',
    phone: '+66987654321',
    email: '<EMAIL>',
    is_active: true,
  },
]

const mockDrivers = [
  {
    id: 'driver-1',
    carrier_id: 'carrier-1',
    driver_first_name: 'John',
    driver_last_name: 'Doe',
    driver_code: 'D001',
    phone: '+66111222333',
    line_id: 'johndoe',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'driver-2',
    carrier_id: 'carrier-1',
    driver_first_name: 'Jane',
    driver_last_name: 'Smith',
    driver_code: 'D002',
    phone: '+66444555666',
    line_id: 'janesmith',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
]

describe('useTransportation', () => {
  const mockSupabaseClient = {
    from: vi.fn(() => mockSupabaseClient),
    select: vi.fn(() => mockSupabaseClient),
    insert: vi.fn(() => mockSupabaseClient),
    update: vi.fn(() => mockSupabaseClient),
    delete: vi.fn(() => mockSupabaseClient),
    eq: vi.fn(() => mockSupabaseClient),
    maybeSingle: vi.fn(),
    single: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(createClient).mockReturnValue(mockSupabaseClient as any)
  })

  describe('fetchTransportation', () => {
    it('fetches transportation assignment successfully', async () => {
      mockSupabaseClient.maybeSingle.mockResolvedValue({
        data: mockTransportation,
        error: null,
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      await waitFor(() => {
        expect(result.current.transportation).toEqual(mockTransportation)
        expect(result.current.isLoading).toBe(false)
        expect(result.current.error).toBe(null)
      })

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('transportation')
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('shipment_id', 'shipment-123')
    })

    it('handles fetch error gracefully', async () => {
      mockSupabaseClient.maybeSingle.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      await waitFor(() => {
        expect(result.current.transportation).toBe(null)
        expect(result.current.isLoading).toBe(false)
        expect(result.current.error).toBe('Database error')
      })
    })

    it('returns null when no transportation assignment exists', async () => {
      mockSupabaseClient.maybeSingle.mockResolvedValue({
        data: null,
        error: null,
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      await waitFor(() => {
        expect(result.current.transportation).toBe(null)
        expect(result.current.isLoading).toBe(false)
        expect(result.current.error).toBe(null)
      })
    })
  })

  describe('createTransportationAssignment', () => {
    it('creates transportation assignment successfully', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: mockTransportation,
        error: null,
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const formData = {
        shipment_id: 'shipment-123',
        carrier_id: 'carrier-1',
        driver_id: 'driver-1',
        vehicle_head_number: 'HEAD-123',
        vehicle_tail_number: 'TAIL-456',
        driver_phone: '+66123456789',
        assignment_date: '2024-01-01T10:00:00Z',
        pickup_container_location: 'Container Terminal A',
        pickup_product_location: 'Factory B',
        delivery_location: 'Warehouse C',
        estimated_distance: 15.5,
        notes: 'Handle with care',
      }

      await act(async () => {
        const result_data = await result.current.createTransportationAssignment(formData)
        expect(result_data).toEqual(mockTransportation)
      })

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('transportation')
      expect(mockSupabaseClient.insert).toHaveBeenCalledWith([
        expect.objectContaining({
          shipment_id: 'shipment-123',
          carrier_id: 'carrier-1',
          delivery_location: 'Warehouse C',
        })
      ])
    })

    it('updates shipment status after successful creation', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: mockTransportation,
        error: null,
      })

      // Mock shipment status update
      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({ error: null })
        })
      })
      mockSupabaseClient.update = mockUpdate

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const formData = {
        shipment_id: 'shipment-123',
        carrier_id: 'carrier-1',
        delivery_location: 'Warehouse C',
        assignment_date: '2024-01-01T10:00:00Z',
      }

      await act(async () => {
        await result.current.createTransportationAssignment(formData)
      })

      // Should update shipment status
      expect(mockUpdate).toHaveBeenCalledWith({
        status: 'transport_assigned',
        updated_at: expect.any(String),
      })
    })

    it('handles creation error gracefully', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: null,
        error: { message: 'Insert failed' },
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const formData = {
        shipment_id: 'shipment-123',
        carrier_id: 'carrier-1',
        delivery_location: 'Warehouse C',
        assignment_date: '2024-01-01T10:00:00Z',
      }

      await act(async () => {
        await expect(
          result.current.createTransportationAssignment(formData)
        ).rejects.toThrow('Insert failed')
      })
    })

    it('converts coordinates to PostGIS format', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: mockTransportation,
        error: null,
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const formData = {
        shipment_id: 'shipment-123',
        carrier_id: 'carrier-1',
        delivery_location: 'Warehouse C',
        assignment_date: '2024-01-01T10:00:00Z',
        pickup_product_gps_coordinates: { lat: 13.7563, lng: 100.5018 },
        delivery_gps_coordinates: { lat: 13.7763, lng: 100.5218 },
      }

      await act(async () => {
        await result.current.createTransportationAssignment(formData)
      })

      expect(mockSupabaseClient.insert).toHaveBeenCalledWith([
        expect.objectContaining({
          pickup_product_gps_coordinates: '(100.5018,13.7563)',
          delivery_gps_coordinates: '(100.5218,13.7763)',
        })
      ])
    })

    it('updates shipment status to driver_assigned when driver is selected', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: mockTransportation,
        error: null,
      })

      // Mock shipment status update calls
      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({ error: null })
        })
      })
      mockSupabaseClient.update = mockUpdate

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const formDataWithDriver = {
        shipment_id: 'shipment-123',
        carrier_id: 'carrier-1',
        driver_id: 'driver-1', // Driver is assigned
        delivery_location: 'Warehouse C',
        assignment_date: '2024-01-01T10:00:00Z',
      }

      await act(async () => {
        await result.current.createTransportationAssignment(formDataWithDriver)
      })

      // Should be called twice - once for transport_assigned, once for driver_assigned
      expect(mockUpdate).toHaveBeenCalledTimes(2)
      
      // First call: transport_assigned
      expect(mockUpdate).toHaveBeenNthCalledWith(1, {
        status: 'transport_assigned',
        updated_at: expect.any(String),
      })
      
      // Second call: driver_assigned
      expect(mockUpdate).toHaveBeenNthCalledWith(2, {
        status: 'driver_assigned',
        updated_at: expect.any(String),
      })
    })

    it('only updates to transport_assigned when no driver is selected', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: { ...mockTransportation, driver_id: null },
        error: null,
      })

      // Mock shipment status update
      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({ error: null })
        })
      })
      mockSupabaseClient.update = mockUpdate

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const formDataNoDriver = {
        shipment_id: 'shipment-123',
        carrier_id: 'carrier-1',
        // driver_id is not provided
        delivery_location: 'Warehouse C',
        assignment_date: '2024-01-01T10:00:00Z',
      }

      await act(async () => {
        await result.current.createTransportationAssignment(formDataNoDriver)
      })

      // Should only be called once for transport_assigned
      expect(mockUpdate).toHaveBeenCalledTimes(1)
      expect(mockUpdate).toHaveBeenCalledWith({
        status: 'transport_assigned',
        updated_at: expect.any(String),
      })
    })
  })

  describe('updateTransportationAssignment', () => {
    it('updates transportation assignment successfully', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: { ...mockTransportation, notes: 'Updated notes' },
        error: null,
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const updateData = {
        id: 'transport-1',
        notes: 'Updated notes',
      }

      await act(async () => {
        const updatedTransportation = await result.current.updateTransportationAssignment(updateData)
        expect(updatedTransportation.notes).toBe('Updated notes')
      })

      expect(mockSupabaseClient.update).toHaveBeenCalled()
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', 'transport-1')
    })

    it('handles update error gracefully', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: null,
        error: { message: 'Update failed' },
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const updateData = {
        id: 'transport-1',
        notes: 'Updated notes',
      }

      await act(async () => {
        await expect(
          result.current.updateTransportationAssignment(updateData)
        ).rejects.toThrow('Update failed')
      })
    })

    it('updates shipment status to driver_assigned when driver is added', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: { ...mockTransportation, driver_id: 'driver-2' },
        error: null,
      })

      // Mock shipment status update
      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({ error: null })
        }),
        in: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({ error: null })
        })
      })
      mockSupabaseClient.update = mockUpdate

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const updateData = {
        id: 'transport-1',
        driver_id: 'driver-2', // Adding a driver
      }

      await act(async () => {
        await result.current.updateTransportationAssignment(updateData)
      })

      // Should update shipment status to driver_assigned
      expect(mockUpdate).toHaveBeenCalledWith({
        status: 'driver_assigned',
        updated_at: expect.any(String),
      })
    })

    it('updates shipment status to transport_assigned when driver is removed', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: { ...mockTransportation, driver_id: null },
        error: null,
      })

      // Mock shipment status update
      const mockUpdate = vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({ error: null })
        })
      })
      mockSupabaseClient.update = mockUpdate

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const updateData = {
        id: 'transport-1',
        driver_id: null, // Removing driver
      }

      await act(async () => {
        await result.current.updateTransportationAssignment(updateData)
      })

      // Should revert shipment status to transport_assigned
      expect(mockUpdate).toHaveBeenCalledWith({
        status: 'transport_assigned',
        updated_at: expect.any(String),
      })
    })

    it('does not update shipment status when driver is not changed', async () => {
      mockSupabaseClient.single.mockResolvedValue({
        data: { ...mockTransportation, notes: 'Updated notes' },
        error: null,
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      const updateData = {
        id: 'transport-1',
        notes: 'Updated notes', // No driver change
      }

      await act(async () => {
        await result.current.updateTransportationAssignment(updateData)
      })

      // Should not make any shipment status update calls
      expect(mockSupabaseClient.update).toHaveBeenCalledTimes(1) // Only the transportation update
    })
  })

  describe('deleteTransportationAssignment', () => {
    it('deletes transportation assignment successfully', async () => {
      mockSupabaseClient.delete = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: null })
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      await act(async () => {
        await result.current.deleteTransportationAssignment('transport-1')
      })

      expect(mockSupabaseClient.delete).toHaveBeenCalled()
    })

    it('handles delete error gracefully', async () => {
      mockSupabaseClient.delete = vi.fn().mockReturnValue({
        eq: vi.fn().mockResolvedValue({ error: { message: 'Delete failed' } })
      })

      const { result } = renderHook(() => useTransportation('shipment-123'))

      await act(async () => {
        await expect(
          result.current.deleteTransportationAssignment('transport-1')
        ).rejects.toThrow('Delete failed')
      })
    })
  })
})

describe('useCarriers', () => {
  const mockSupabaseClient = {
    from: vi.fn(() => mockSupabaseClient),
    select: vi.fn(() => mockSupabaseClient),
    eq: vi.fn(() => mockSupabaseClient),
    order: vi.fn(() => mockSupabaseClient),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(createClient).mockReturnValue(mockSupabaseClient as any)
  })

  it('fetches carrier companies successfully', async () => {
    mockSupabaseClient.order.mockResolvedValue({
      data: mockCarriers,
      error: null,
    })

    const { result } = renderHook(() => useCarriers())

    await waitFor(() => {
      expect(result.current.carriers).toEqual(mockCarriers)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe(null)
    })

    expect(mockSupabaseClient.eq).toHaveBeenCalledWith('company_type', 'carrier')
    expect(mockSupabaseClient.eq).toHaveBeenCalledWith('is_active', true)
  })

  it('handles fetch error gracefully', async () => {
    mockSupabaseClient.order.mockResolvedValue({
      data: null,
      error: { message: 'Failed to fetch carriers' },
    })

    const { result } = renderHook(() => useCarriers())

    await waitFor(() => {
      expect(result.current.carriers).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch carriers')
    })
  })
})

describe('useDriversByCarrier', () => {
  const mockSupabaseClient = {
    from: vi.fn(() => mockSupabaseClient),
    select: vi.fn(() => mockSupabaseClient),
    eq: vi.fn(() => mockSupabaseClient),
    order: vi.fn(() => mockSupabaseClient),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(createClient).mockReturnValue(mockSupabaseClient as any)
  })

  it('fetches drivers for carrier successfully', async () => {
    mockSupabaseClient.order.mockResolvedValue({
      data: mockDrivers,
      error: null,
    })

    const { result } = renderHook(() => useDriversByCarrier('carrier-1'))

    await waitFor(() => {
      expect(result.current.drivers).toEqual(mockDrivers)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe(null)
    })

    expect(mockSupabaseClient.eq).toHaveBeenCalledWith('carrier_id', 'carrier-1')
    expect(mockSupabaseClient.eq).toHaveBeenCalledWith('is_active', true)
  })

  it('returns empty array when no carrier is provided', async () => {
    const { result } = renderHook(() => useDriversByCarrier(undefined))

    expect(result.current.drivers).toEqual([])
    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBe(null)
  })

  it('handles fetch error gracefully', async () => {
    mockSupabaseClient.order.mockResolvedValue({
      data: null,
      error: { message: 'Failed to fetch drivers' },
    })

    const { result } = renderHook(() => useDriversByCarrier('carrier-1'))

    await waitFor(() => {
      expect(result.current.drivers).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch drivers')
    })
  })
})

describe('useCoordinateUtils', () => {
  it('parses PostGIS POINT coordinates correctly', () => {
    const { result } = renderHook(() => useCoordinateUtils())

    const coordinates = result.current.parseCoordinates('POINT(100.5018 13.7563)')
    expect(coordinates).toEqual({
      lat: 13.7563,
      lng: 100.5018,
    })
  })

  it('returns undefined for invalid coordinates', () => {
    const { result } = renderHook(() => useCoordinateUtils())

    expect(result.current.parseCoordinates(null)).toBeUndefined()
    expect(result.current.parseCoordinates(undefined)).toBeUndefined()
    expect(result.current.parseCoordinates('invalid')).toBeUndefined()
  })

  it('formats coordinates for display correctly', () => {
    const { result } = renderHook(() => useCoordinateUtils())

    const formatted = result.current.formatCoordinates(13.7563, 100.5018)
    expect(formatted).toBe('13.756300, 100.501800')
  })
})