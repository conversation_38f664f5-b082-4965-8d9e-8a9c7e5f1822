/**
 * Document Viewer Utilities
 *
 * Handles viewing/previewing documents from Supabase Storage
 */

import { getDocumentDownloadUrl } from './document-download'

export interface ViewResult {
  success: boolean
  error?: {
    message: string
    code?: string
  }
}

/**
 * Get MIME type from file extension
 */
function getMimeTypeFromFileName(fileName: string): string {
  const parts = fileName.toLowerCase().split('.')
  const extension = parts.length > 1 ? parts.pop() || '' : ''

  const mimeTypes: Record<string, string> = {
    'pdf': 'application/pdf',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'txt': 'text/plain',
    'html': 'text/html',
    'csv': 'text/csv',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  }

  return mimeTypes[extension || ''] || 'application/octet-stream'
}

/**
 * Check if file type can be viewed in browser
 */
function canViewInBrowser(fileName: string): boolean {
  const viewableTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'txt', 'html', 'csv']
  const parts = fileName.toLowerCase().split('.')
  const extension = parts.length > 1 ? parts.pop() || '' : ''
  return viewableTypes.includes(extension)
}

/**
 * View a document in browser (new tab/window)
 * @param filePath - The file path in storage
 * @param fileName - The filename (used for MIME type detection)
 * @param bucket - Optional bucket name (defaults to 'documents')
 * @returns Promise<ViewResult>
 */
export async function viewDocument(filePath: string, fileName: string, bucket?: string): Promise<ViewResult> {
  try {
    // Check if document can be viewed in browser
    if (!canViewInBrowser(fileName)) {
      return {
        success: false,
        error: {
          message: `File type '${fileName.split('.').pop()}' cannot be previewed in browser. Please download to view.`,
          code: 'UNSUPPORTED_FILE_TYPE'
        }
      }
    }

    // Get signed URL for viewing
    const urlResult = await getDocumentDownloadUrl(filePath, 3600, bucket) // 1 hour expiry

    if (!urlResult.success || !urlResult.url) {
      return {
        success: false,
        error: {
          message: urlResult.error?.message || 'Failed to generate preview URL',
          code: urlResult.error?.code
        }
      }
    }

    // Try to open in new tab/window
    try {
      const newWindow = window.open(urlResult.url, '_blank', 'noopener,noreferrer')

      // Some browsers return null even when the window opens successfully
      // We'll assume success unless we can definitively detect a failure
      if (newWindow === null) {
        // Check if popup blocker is likely the cause
        // This is a heuristic - if window.open returns null immediately, it might be blocked
        return {
          success: false,
          error: {
            message: 'Failed to open preview window. Please check your popup blocker settings.',
            code: 'POPUP_BLOCKED'
          }
        }
      }

      // If we get a window object (even if we can't access its properties), consider it success
      return { success: true }

    } catch (error) {
      // If there's an exception, it's likely a popup blocker or security restriction
      return {
        success: false,
        error: {
          message: 'Failed to open preview window. Please check your popup blocker settings.',
          code: 'POPUP_BLOCKED'
        }
      }
    }

  } catch (error) {
    console.error('Unexpected error during document viewing:', error)
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unexpected error occurred'
      }
    }
  }
}

/**
 * Get preview info for a document
 * @param fileName - The filename
 * @returns Object with preview capabilities and info
 */
export function getDocumentPreviewInfo(fileName: string) {
  const parts = fileName.toLowerCase().split('.')
  const extension = parts.length > 1 ? parts.pop() || '' : ''
  const mimeType = getMimeTypeFromFileName(fileName)
  const canPreview = canViewInBrowser(fileName)

  let previewType: 'pdf' | 'image' | 'text' | 'unsupported' = 'unsupported'

  if (extension === 'pdf') {
    previewType = 'pdf'
  } else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    previewType = 'image'
  } else if (['txt', 'html', 'csv'].includes(extension)) {
    previewType = 'text'
  }

  return {
    canPreview,
    previewType,
    mimeType,
    extension,
    fileName
  }
}

/**
 * Open document in modal/iframe (for in-page viewing)
 * @param filePath - The file path in storage
 * @param fileName - The filename
 * @param bucket - Optional bucket name
 * @returns Promise with signed URL for embedding
 */
export async function getDocumentEmbedUrl(filePath: string, fileName: string, bucket?: string) {
  try {
    const previewInfo = getDocumentPreviewInfo(fileName)

    if (!previewInfo.canPreview) {
      return {
        success: false,
        error: {
          message: `File type '${previewInfo.extension}' cannot be embedded for preview`,
          code: 'UNSUPPORTED_EMBED_TYPE'
        }
      }
    }

    // Get signed URL for embedding
    const urlResult = await getDocumentDownloadUrl(filePath, 3600, bucket)

    if (!urlResult.success || !urlResult.url) {
      return {
        success: false,
        error: {
          message: urlResult.error?.message || 'Failed to generate embed URL',
          code: urlResult.error?.code
        }
      }
    }

    return {
      success: true,
      url: urlResult.url,
      previewInfo
    }

  } catch (error) {
    console.error('Unexpected error generating embed URL:', error)
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unexpected error occurred'
      }
    }
  }
}