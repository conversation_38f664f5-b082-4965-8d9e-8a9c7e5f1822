'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { DocumentTemplateForm } from '@/components/forms/document-template-form/document-template-form'
import { useDocumentTemplate, useDocumentTemplates } from '@/hooks/use-document-templates'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent } from '@/components/ui/card'
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react'
import type { DocumentTemplateUpdate } from '@/types/document-template'

interface EditTemplatePageProps {
  params: {
    id: string
  }
}

/**
 * Edit Document Template Page
 * Story 5.1: Document Template Management System
 * 
 * Page for editing existing document templates with form validation,
 * loading states, and admin-only access control.
 */
export default function EditTemplatePage({ params }: EditTemplatePageProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  
  const { template, loading, error } = useDocumentTemplate(params.id)
  const { updateTemplate } = useDocumentTemplates({ autoRefresh: false })

  const handleSubmit = async (data: DocumentTemplateUpdate) => {
    setIsSubmitting(true)
    setSubmitError(null)
    setSubmitSuccess(false)

    try {
      const result = await updateTemplate(params.id, data)
      
      if (result.success) {
        setSubmitSuccess(true)
        // Redirect to the template detail page after a short delay
        setTimeout(() => {
          router.push(`/documents/templates/${params.id}`)
        }, 2000)
      } else {
        setSubmitError(result.error || 'Failed to update template')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update template'
      setSubmitError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.push(`/documents/templates/${params.id}`)
  }

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto p-6 max-w-6xl">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin mr-4" />
            <span className="text-lg">Loading template...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Error state
  if (error || !template) {
    return (
      <div className="container mx-auto p-6 max-w-6xl">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Template not found'}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Success message */}
      {submitSuccess && (
        <Alert className="mb-6 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertDescription className="text-green-800 dark:text-green-200">
            Template updated successfully! Redirecting to template view...
          </AlertDescription>
        </Alert>
      )}

      {/* Error message */}
      {submitError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      <DocumentTemplateForm
        template={template}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isSubmitting}
        isEditing={true}
      />
    </div>
  )
}