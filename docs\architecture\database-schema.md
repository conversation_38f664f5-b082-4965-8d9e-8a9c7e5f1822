# Database Schema

## Core Schema Implementation

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Enums and Types
CREATE TYPE role_type AS ENUM (
  'admin', 'cs', 'account', 'customer', 
  'carrier', 'driver', 'factory', 'shipper', 
  'consignee', 'notify_party', 'forwarder_agent'
);

CREATE TYPE company_type_enum AS ENUM (
  'customer', 'carrier', 'factory', 'shipper', 
  'consignee', 'notify_party', 'forwarder_agent'
);

CREATE TYPE transport_mode_enum AS ENUM ('sea', 'land', 'rail');

CREATE TYPE shipment_status_enum AS ENUM (
  'booking_confirmed', 'transport_assigned', 'driver_assigned',
  'empty_container_picked', 'arrived_at_factory',
  'loading_started', 'departed_factory', 'container_returned',
  'shipped', 'arrived', 'completed'
);

CREATE TYPE currency_enum AS ENUM ('THB', 'CNY', 'USD', 'EUR');
CREATE TYPE packaging_type_enum AS ENUM ('Bag', 'Plastic Basket', 'Carton');
CREATE TYPE document_type_enum AS ENUM (
  'booking_confirmation', 'invoice_fob', 'invoice_cif', 'contract', 
  'shipping_instruction', 'packing_list', 'certificate', 'other'
);

-- User Profiles (extends Supabase auth.users)
CREATE TABLE profiles (
  user_id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  first_name text,
  last_name text,
  phone_number text,
  line_id text,
  wechat_id text,
  role role_type NOT NULL,
  company_id uuid,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- HYBRID COMPANIES DESIGN: Base table with common fields
CREATE TABLE companies (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  company_type company_type_enum NOT NULL,
  
  -- Common fields for all company types
  tax_id text,
  contact_email text,
  contact_phone text,
  contact_fax text,
  contact_person_first_name text,
  contact_person_last_name text,
  
  -- Hybrid address storage: JSONB + dedicated point column
  address jsonb, -- {"street": {"th": "...", "en": "..."}, "coordinates": {"lat": 13.7563, "lng": 100.5018}}
  gps_coordinates point, -- Synced automatically via trigger for efficient geographic queries
  
  -- JSONB metadata ONLY for simple company types
  metadata jsonb,
  
  -- Common audit fields
  notes text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Constraint to ensure complex types have NULL metadata
  CONSTRAINT valid_metadata_structure CHECK (validate_company_metadata(company_type, metadata))
);

-- Customer info table (complex business rules)
CREATE TABLE customer_info (
  company_id uuid PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
  customer_type customer_type_enum NOT NULL DEFAULT 'regular',
  credit_limit numeric(12,2) DEFAULT 0 CHECK (credit_limit >= 0),
  incoterms incoterms_enum,
  special_requirements text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Carrier info table (fleet management, complex logistics data)
CREATE TABLE carrier_info (
  company_id uuid PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
  carrier_code text UNIQUE,
  fleet_size integer DEFAULT 0 CHECK (fleet_size >= 0),
  license_types text[] DEFAULT '{}',
  coverage_areas text[] DEFAULT '{}',
  insurance_policy_no text,
  insurance_expiry_date date,
  insurance_coverage_amount numeric(15,2),
  max_weight_capacity numeric(10,2),
  max_volume_capacity numeric(10,2),
  operating_hours jsonb, -- {"weekdays": "08:00-18:00", "weekends": "09:00-15:00"}
  emergency_contact_phone text,
  gps_tracking_available boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Factory info table (production data, certifications)
CREATE TABLE factory_info (
  company_id uuid PRIMARY KEY REFERENCES companies(id) ON DELETE CASCADE,
  factory_code text UNIQUE NOT NULL,
  license_no text NOT NULL,
  certifications text[] DEFAULT '{}', -- ['HACCP', 'ISO22000', 'GMP']
  production_capacity_tons_per_day integer DEFAULT 0,
  cold_storage_capacity_tons integer DEFAULT 0,
  operating_hours jsonb, -- {"weekdays": "08:00-17:00", "weekends": "closed"}
  specializations text[] DEFAULT '{}', -- ['durian', 'mangosteen', 'longan']
  quality_control_manager text,
  quality_control_phone text,
  loading_dock_count integer DEFAULT 1,
  container_loading_time_minutes integer DEFAULT 120,
  advance_booking_required_hours integer DEFAULT 24,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Main shipments table with all required fields
CREATE TABLE shipments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_number text UNIQUE NOT NULL,
  invoice_number text,
  
  -- Stakeholder references
  customer_id uuid REFERENCES companies(id),
  shipper_id uuid REFERENCES companies(id),
  consignee_id uuid REFERENCES companies(id),
  notify_party_id uuid REFERENCES companies(id),
  factory_id uuid REFERENCES companies(id),
  forwarder_agent_id uuid REFERENCES companies(id),
  
  -- Port and vessel information
  origin_port_id uuid REFERENCES ports(id),
  destination_port_id uuid REFERENCES ports(id),
  liner text,
  vessel_name text,
  voyage_number text,
  booking_number text,
  
  -- Dates and times
  etd_date timestamptz,
  eta_date timestamptz,
  closing_time timestamptz,
  cy_date timestamptz,
  
  -- Product Info (including missing fields from requirements)
  number_of_pallet integer,
  pallet_description text,
  ephyto_refno text,
  currency_code currency_enum DEFAULT 'USD',
  total_weight numeric(10,2),
  total_volume numeric(10,2),
  
  -- Status and metadata
  status shipment_status_enum DEFAULT 'booking_confirmed',
  transportation_mode transport_mode_enum DEFAULT 'sea',
  notes text,
  metadata jsonb,
  
  -- Audit fields
  created_by uuid REFERENCES profiles(user_id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Date validation constraints
  CONSTRAINT valid_date_sequence CHECK (
    closing_time IS NULL OR etd_date IS NULL OR closing_time <= etd_date
  ),
  CONSTRAINT valid_etd_eta CHECK (
    etd_date IS NULL OR eta_date IS NULL OR etd_date <= eta_date
  )
);

-- Customer-Product relationships with pricing and specifications
CREATE TABLE customer_products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  
  -- Product-specific details for this customer
  customer_product_code text,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  
  -- Pricing information (per KG as base unit)
  unit_price_cif numeric(12,4), -- Cost, Insurance, and Freight price per KG
  unit_price_fob numeric(12,4), -- Free on Board price per KG
  currency_code currency_enum DEFAULT 'USD',
  
  -- Physical specifications and packaging
  standard_quantity numeric(10,2), -- Standard order quantity (number of packages)
  unit_of_measure_id uuid REFERENCES units_of_measure(id),
  gross_weight_per_package numeric(8,4), -- Gross weight per 1 package in KG
  net_weight_per_package numeric(8,4), -- Net weight per 1 package in KG
  
  -- Quality and packaging
  quality_grade text,
  packaging_type packaging_type_enum NOT NULL,
  packaging_specifications jsonb,
  
  -- Logistics information
  handling_instructions text,
  temperature_require text,
  vent_require text,
  shelf_life_days integer,
  
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Type validation
  CONSTRAINT customer_products_customer_type_check 
    CHECK (EXISTS (SELECT 1 FROM companies WHERE id = customer_id AND company_type = 'customer')),
  
  -- Unique relationship constraint
  UNIQUE(customer_id, product_id)
);

-- Status history with full audit trail
CREATE TABLE status_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  status_from shipment_status_enum,
  status_to shipment_status_enum NOT NULL,
  notes text,
  location text,
  latitude numeric(10,7),
  longitude numeric(10,7),
  updated_by uuid REFERENCES profiles(user_id),
  created_at timestamptz DEFAULT now()
);

-- Image uploads for status updates
CREATE TABLE status_images (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  status_history_id uuid REFERENCES status_history(id),
  image_url text NOT NULL,
  image_path text NOT NULL,
  file_size integer,
  mime_type text,
  metadata jsonb,
  uploaded_by uuid REFERENCES profiles(user_id),
  created_at timestamptz DEFAULT now()
);
```

## Performance Indexes

```sql
-- Core performance indexes
CREATE INDEX idx_companies_type ON companies(company_type);
CREATE INDEX idx_companies_active ON companies(is_active) WHERE is_active = true;
CREATE INDEX idx_companies_address_gin ON companies USING GIN (address);
CREATE INDEX idx_companies_gps_coordinates ON companies USING GIST (gps_coordinates);

-- Shipment indexes
CREATE INDEX idx_shipments_status ON shipments(status);
CREATE INDEX idx_shipments_customer ON shipments(customer_id);
CREATE INDEX idx_shipments_created_at ON shipments(created_at);
CREATE INDEX idx_shipments_number ON shipments(shipment_number);
CREATE INDEX idx_shipments_transportation_mode ON shipments(transportation_mode);

-- Relationship indexes for intelligent pre-population
CREATE INDEX idx_customer_products_customer ON customer_products(customer_id) WHERE is_active = true;
CREATE INDEX idx_customer_products_default ON customer_products(customer_id, is_default) WHERE is_active = true;

-- Status tracking indexes
CREATE INDEX idx_status_history_shipment ON status_history(shipment_id);
CREATE INDEX idx_status_history_created ON status_history(created_at);
```
