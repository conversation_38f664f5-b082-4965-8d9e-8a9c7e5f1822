'use client'

import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type { CustomerProductFilter } from '@/lib/validations/customer-products'

// Customer-Product types (based on database schema)
export interface CustomerProduct {
  id: string
  customer_id: string
  product_id: string
  customer_product_code: string | null
  is_default: boolean | null
  is_active: boolean | null
  unit_price_cif: number | null
  unit_price_fob: number | null
  currency_code: 'THB' | 'CNY' | 'USD' | 'EUR' | null
  standard_quantity: number | null
  unit_of_measure_id: string | null
  gross_weight_per_package: number | null
  net_weight_per_package: number | null
  quality_grade: string | null
  packaging_type: 'Bag' | 'Plastic Basket' | 'Carton'
  packaging_specifications: any | null // JSONB
  handling_instructions: string | null
  temperature_require: string | null
  vent_require: string | null
  shelf_life_days: number | null
  notes: string | null
  created_at: string | null
  updated_at: string | null
  // Joined data
  customer?: {
    id: string
    name: string
    company_type: string
    contact_phone: string | null
    contact_email: string | null
  } | null
  product?: {
    id: string
    name: string
    code: string | null
    category: string | null
  } | null
  unit_of_measure?: {
    id: string
    name: string
    symbol: string | null
  } | null
}

export interface CustomerProductBulkData {
  customer_name: string
  product_name: string
  customer_product_code?: string | null
  unit_price_cif?: number | null
  unit_price_fob?: number | null
  currency_code?: 'THB' | 'CNY' | 'USD' | 'EUR'
  standard_quantity?: number | null
  gross_weight_per_package?: number | null
  net_weight_per_package?: number | null
  quality_grade?: string | null
  packaging_type: 'Bag' | 'Plastic Basket' | 'Carton'
  handling_instructions?: string | null
  temperature_require?: string | null
  vent_require?: string | null
  shelf_life_days?: number | null
  is_default?: boolean
  notes?: string | null
}

export interface CustomerProductBulkResult {
  summary: {
    total: number
    successful: number
    failed: number
    created: number
    updated: number
  }
  errors: Array<{
    row: number
    error: string
  }>
  created_relationships: CustomerProduct[]
  updated_relationships: CustomerProduct[]
}

interface CustomerProductStore {
  // Data state
  customerProducts: CustomerProduct[]
  loading: boolean
  error: string | null

  // Pagination state
  currentPage: number
  pageSize: number
  totalCount: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean

  // Filter and search state
  filter: CustomerProductFilter
  searchTerm: string
  sortBy: keyof CustomerProduct
  sortOrder: 'asc' | 'desc'

  // Selection state
  selectedCustomerProducts: Set<string>

  // Loading states for operations
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  isBulkImporting: boolean

  // Actions - Data fetching
  fetchCustomerProducts: () => Promise<void>
  refreshCustomerProducts: () => Promise<void>

  // Actions - CRUD operations
  createCustomerProduct: (data: any) => Promise<CustomerProduct>
  updateCustomerProduct: (id: string, data: any) => Promise<CustomerProduct>
  deleteCustomerProduct: (id: string) => Promise<void>
  bulkDeleteCustomerProducts: (ids: string[]) => Promise<void>

  // Actions - Default product management
  setDefaultProduct: (
    customerId: string,
    customerProductId: string
  ) => Promise<void>
  getDefaultProduct: (customerId: string) => Promise<CustomerProduct | null>

  // Actions - Bulk operations
  bulkImportCustomerProducts: (
    data: CustomerProductBulkData[]
  ) => Promise<CustomerProductBulkResult>

  // Actions - Filter and search
  setFilter: (filter: Partial<CustomerProductFilter>) => void
  setSearchTerm: (term: string) => void
  setSorting: (sortBy: keyof CustomerProduct, sortOrder: 'asc' | 'desc') => void
  clearFilters: () => void

  // Actions - Pagination
  setPage: (page: number) => void
  nextPage: () => void
  previousPage: () => void

  // Actions - Selection
  toggleCustomerProduct: (id: string) => void
  toggleAll: () => void
  clearSelection: () => void

  // Actions - Error handling
  clearError: () => void
}

export const useCustomerProductStore = create<CustomerProductStore>(
  (set, get) => ({
    // Initial state
    customerProducts: [],
    loading: false,
    error: null,

    // Pagination state
    currentPage: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,

    // Filter and search state
    filter: {},
    searchTerm: '',
    sortBy: 'created_at',
    sortOrder: 'desc',

    // Selection state
    selectedCustomerProducts: new Set<string>(),

    // Loading states
    isCreating: false,
    isUpdating: false,
    isDeleting: false,
    isBulkImporting: false,

    // Data fetching
    fetchCustomerProducts: async () => {
      const state = get()
      set({ loading: true, error: null })

      try {
        const supabase = createClient()

        // Build the base query
        let query = supabase.from('customer_products').select(`
          *,
          customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
          product:products!product_id(id, name, code, category),
          unit_of_measure:units_of_measure(id, name, symbol)
        `)

        // Apply filters
        if (state.filter.customer_id) {
          query = query.eq('customer_id', state.filter.customer_id)
        }

        if (state.filter.product_id) {
          query = query.eq('product_id', state.filter.product_id)
        }

        if (state.filter.currency_code) {
          query = query.eq('currency_code', state.filter.currency_code)
        }

        if (state.filter.packaging_type) {
          query = query.eq('packaging_type', state.filter.packaging_type)
        }

        if (state.filter.is_default !== undefined) {
          query = query.eq('is_default', state.filter.is_default)
        }

        if (state.filter.is_active !== undefined) {
          query = query.eq('is_active', state.filter.is_active)
        } else {
          // Default to active relationships only
          query = query.eq('is_active', true)
        }

        // Get product IDs for search if needed
        let productIds: string[] = []
        if (state.searchTerm) {
          // First get product IDs that match the search term
          const { data: matchingProducts } = await supabase
            .from('products')
            .select('id')
            .ilike('code', `%${state.searchTerm}%`)

          productIds = matchingProducts?.map(p => p.id) || []

          // Apply search conditions to main query
          if (productIds.length > 0) {
            query = query.or(
              `customer_product_code.ilike.%${state.searchTerm}%,product_id.in.(${productIds.join(',')})`
            )
          } else {
            query = query.ilike(
              'customer_product_code',
              `%${state.searchTerm}%`
            )
          }
        }

        // Get total count
        let countQuery = supabase
          .from('customer_products')
          .select('*', { count: 'exact', head: true })

        // Apply same filters for count (simplified)
        if (state.filter.customer_id) {
          countQuery = countQuery.eq('customer_id', state.filter.customer_id)
        }

        if (state.filter.product_id) {
          countQuery = countQuery.eq('product_id', state.filter.product_id)
        }

        if (state.filter.currency_code) {
          countQuery = countQuery.eq(
            'currency_code',
            state.filter.currency_code
          )
        }

        if (state.filter.packaging_type) {
          countQuery = countQuery.eq(
            'packaging_type',
            state.filter.packaging_type
          )
        }

        if (state.filter.is_default !== undefined) {
          countQuery = countQuery.eq('is_default', state.filter.is_default)
        }

        if (state.filter.is_active !== undefined) {
          countQuery = countQuery.eq('is_active', state.filter.is_active)
        } else {
          countQuery = countQuery.eq('is_active', true)
        }

        // Apply search to count query (use same product IDs)
        if (state.searchTerm) {
          if (productIds.length > 0) {
            countQuery = countQuery.or(
              `customer_product_code.ilike.%${state.searchTerm}%,product_id.in.(${productIds.join(',')})`
            )
          } else {
            countQuery = countQuery.ilike(
              'customer_product_code',
              `%${state.searchTerm}%`
            )
          }
        }

        const { count } = await countQuery

        // Apply sorting - use simple column names for now
        const sortColumn =
          state.sortBy === 'customer'
            ? 'customer_id'
            : state.sortBy === 'product'
              ? 'product_id'
              : state.sortBy
        query = query.order(sortColumn, {
          ascending: state.sortOrder === 'asc',
        })

        // Apply pagination
        const from = (state.currentPage - 1) * state.pageSize
        const to = from + state.pageSize - 1
        query = query.range(from, to)

        const { data, error } = await query

        if (error) throw error

        const totalCount = count || 0
        const totalPages = Math.ceil(totalCount / state.pageSize)

        set({
          customerProducts: data || [],
          totalCount,
          totalPages,
          hasNextPage: state.currentPage < totalPages,
          hasPreviousPage: state.currentPage > 1,
          loading: false,
        })
      } catch (error) {
        console.error('Error fetching customer-products:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to load customer-products',
          loading: false,
        })
      }
    },

    refreshCustomerProducts: async () => {
      await get().fetchCustomerProducts()
    },

    // CRUD operations
    createCustomerProduct: async data => {
      set({ isCreating: true, error: null })

      try {
        const supabase = createClient()

        // If setting as default, first reset other defaults for this customer
        if (data.is_default) {
          await supabase
            .from('customer_products')
            .update({ is_default: false })
            .eq('customer_id', data.customer_id)
        }

        const { data: created, error } = await supabase
          .from('customer_products')
          .insert(data)
          .select(
            `
          *,
          customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
          product:products!product_id(id, name, code, category),
          unit_of_measure:units_of_measure(id, name, symbol)
        `
          )
          .single()

        if (error) throw error

        // Refresh the list
        await get().fetchCustomerProducts()

        set({ isCreating: false })
        return created
      } catch (error) {
        console.error('Error creating customer-product:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to create customer-product',
          isCreating: false,
        })
        throw error
      }
    },

    updateCustomerProduct: async (id, data) => {
      set({ isUpdating: true, error: null })

      try {
        const supabase = createClient()

        // If setting as default, first reset other defaults for this customer
        if (data.is_default) {
          await supabase
            .from('customer_products')
            .update({ is_default: false })
            .eq('customer_id', data.customer_id)
            .neq('id', id)
        }

        const { data: updated, error } = await supabase
          .from('customer_products')
          .update(data)
          .eq('id', id)
          .select(
            `
          *,
          customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
          product:products!product_id(id, name, code, category),
          unit_of_measure:units_of_measure(id, name, symbol)
        `
          )
          .single()

        if (error) throw error

        // Refresh the list
        await get().fetchCustomerProducts()

        set({ isUpdating: false })
        return updated
      } catch (error) {
        console.error('Error updating customer-product:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to update customer-product',
          isUpdating: false,
        })
        throw error
      }
    },

    deleteCustomerProduct: async id => {
      set({ isDeleting: true, error: null })

      try {
        const supabase = createClient()

        const { error } = await supabase
          .from('customer_products')
          .delete()
          .eq('id', id)

        if (error) throw error

        // Refresh the list
        await get().fetchCustomerProducts()

        set({ isDeleting: false })
      } catch (error) {
        console.error('Error deleting customer-product:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to delete customer-product',
          isDeleting: false,
        })
        throw error
      }
    },

    bulkDeleteCustomerProducts: async ids => {
      set({ isDeleting: true, error: null })

      try {
        const supabase = createClient()

        const { error } = await supabase
          .from('customer_products')
          .delete()
          .in('id', ids)

        if (error) throw error

        // Clear selection and refresh
        set({ selectedCustomerProducts: new Set() })
        await get().fetchCustomerProducts()

        set({ isDeleting: false })
      } catch (error) {
        console.error('Error bulk deleting customer-products:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to delete customer-products',
          isDeleting: false,
        })
        throw error
      }
    },

    // Default product management
    setDefaultProduct: async (customerId, customerProductId) => {
      set({ isUpdating: true, error: null })

      try {
        const supabase = createClient()

        // Reset all defaults for this customer
        await supabase
          .from('customer_products')
          .update({ is_default: false })
          .eq('customer_id', customerId)

        // Set new default
        const { error } = await supabase
          .from('customer_products')
          .update({ is_default: true })
          .eq('id', customerProductId)

        if (error) throw error

        // Refresh the list
        await get().fetchCustomerProducts()

        set({ isUpdating: false })
      } catch (error) {
        console.error('Error setting default product:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to set default product',
          isUpdating: false,
        })
        throw error
      }
    },

    getDefaultProduct: async customerId => {
      try {
        const supabase = createClient()

        const { data, error } = await supabase
          .from('customer_products')
          .select(
            `
          *,
          customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
          product:products!product_id(id, name, code, category),
          unit_of_measure:units_of_measure(id, name, symbol)
        `
          )
          .eq('customer_id', customerId)
          .eq('is_default', true)
          .eq('is_active', true)
          .single()

        if (error && error.code !== 'PGRST116') throw error // PGRST116 = no rows returned

        return data || null
      } catch (error) {
        console.error('Error getting default product:', error)
        return null
      }
    },

    // Bulk import
    bulkImportCustomerProducts: async data => {
      set({ isBulkImporting: true, error: null })

      try {
        const supabase = createClient()

        // Get all customers and products for matching
        const [customersResponse, productsResponse] = await Promise.all([
          supabase
            .from('companies')
            .select('id, name')
            .eq('company_type', 'customer')
            .eq('is_active', true),
          supabase.from('products').select('id, name').eq('is_active', true),
        ])

        if (customersResponse.error) throw customersResponse.error
        if (productsResponse.error) throw productsResponse.error

        const customers = customersResponse.data || []
        const products = productsResponse.data || []

        // Create lookup maps for efficient matching
        const customerMap = new Map(
          customers.map(c => [c.name.toLowerCase(), c.id])
        )
        const productMap = new Map(
          products.map(p => [p.name.toLowerCase(), p.id])
        )

        const result: CustomerProductBulkResult = {
          summary: {
            total: data.length,
            successful: 0,
            failed: 0,
            created: 0,
            updated: 0,
          },
          errors: [],
          created_relationships: [],
          updated_relationships: [],
        }

        // Process each row
        for (let i = 0; i < data.length; i++) {
          const row = data[i]
          const rowNumber = i + 2 // Account for header row

          try {
            // Find customer and product IDs
            const customerId = customerMap.get(row.customer_name.toLowerCase())
            const productId = productMap.get(row.product_name.toLowerCase())

            if (!customerId) {
              result.errors.push({
                row: rowNumber,
                error: `Customer "${row.customer_name}" not found`,
              })
              result.summary.failed++
              continue
            }

            if (!productId) {
              result.errors.push({
                row: rowNumber,
                error: `Product "${row.product_name}" not found`,
              })
              result.summary.failed++
              continue
            }

            // Check if relationship already exists for upsert
            const { data: existingRelation } = await supabase
              .from('customer_products')
              .select('id')
              .eq('customer_id', customerId)
              .eq('product_id', productId)
              .single()

            const isUpdate = !!existingRelation

            // Validate required fields
            if (!row.unit_price_cif && !row.unit_price_fob) {
              result.errors.push({
                row: rowNumber,
                error: 'At least one price (CIF or FOB) must be provided',
              })
              result.summary.failed++
              continue
            }

            // Validate weights
            if (
              row.gross_weight_per_package &&
              row.net_weight_per_package &&
              row.net_weight_per_package > row.gross_weight_per_package
            ) {
              result.errors.push({
                row: rowNumber,
                error: 'Net weight cannot exceed gross weight',
              })
              result.summary.failed++
              continue
            }

            // If setting as default, reset other defaults for this customer
            if (row.is_default) {
              await supabase
                .from('customer_products')
                .update({ is_default: false })
                .eq('customer_id', customerId)
            }

            // Prepare data for create or update
            const relationshipData = {
              customer_id: customerId,
              product_id: productId,
              customer_product_code: row.customer_product_code,
              is_default: row.is_default || false,
              is_active: true,
              unit_price_cif: row.unit_price_cif,
              unit_price_fob: row.unit_price_fob,
              currency_code: row.currency_code || 'USD',
              standard_quantity: row.standard_quantity,
              gross_weight_per_package: row.gross_weight_per_package,
              net_weight_per_package: row.net_weight_per_package,
              quality_grade: row.quality_grade,
              packaging_type: row.packaging_type,
              handling_instructions: row.handling_instructions,
              temperature_require: row.temperature_require,
              vent_require: row.vent_require,
              shelf_life_days: row.shelf_life_days,
              notes: row.notes,
            }

            let relationshipResult

            if (isUpdate) {
              // Update existing relationship
              const { data: updated, error } = await supabase
                .from('customer_products')
                .update(relationshipData)
                .eq('id', existingRelation.id)
                .select(
                  `
                *,
                customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
                product:products!product_id(id, name, code, category),
                unit_of_measure:units_of_measure(id, name, symbol)
              `
                )
                .single()

              if (error) throw error

              result.updated_relationships.push(updated)
              result.summary.updated++
              relationshipResult = updated
            } else {
              // Create new relationship
              const { data: created, error } = await supabase
                .from('customer_products')
                .insert(relationshipData)
                .select(
                  `
                *,
                customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
                product:products!product_id(id, name, code, category),
                unit_of_measure:units_of_measure(id, name, symbol)
              `
                )
                .single()

              if (error) throw error

              result.created_relationships.push(created)
              result.summary.created++
              relationshipResult = created
            }

            result.summary.successful++
          } catch (rowError) {
            console.error(`Error processing row ${rowNumber}:`, rowError)
            result.errors.push({
              row: rowNumber,
              error:
                rowError instanceof Error
                  ? rowError.message
                  : 'Unknown error occurred',
            })
            result.summary.failed++
          }
        }

        // Refresh the list after import
        await get().fetchCustomerProducts()

        set({ isBulkImporting: false })
        return result
      } catch (error) {
        console.error('Error bulk importing customer-products:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to import customer-products',
          isBulkImporting: false,
        })
        throw error
      }
    },

    // Filter and search actions
    setFilter: newFilter => {
      set(state => ({
        filter: { ...state.filter, ...newFilter },
        currentPage: 1,
      }))
      get().fetchCustomerProducts()
    },

    setSearchTerm: term => {
      set({ searchTerm: term, currentPage: 1 })
      get().fetchCustomerProducts()
    },

    setSorting: (sortBy, sortOrder) => {
      set({ sortBy, sortOrder, currentPage: 1 })
      get().fetchCustomerProducts()
    },

    clearFilters: () => {
      set({
        filter: {},
        searchTerm: '',
        currentPage: 1,
      })
      get().fetchCustomerProducts()
    },

    // Pagination actions
    setPage: page => {
      set({ currentPage: page })
      get().fetchCustomerProducts()
    },

    nextPage: () => {
      const state = get()
      if (state.hasNextPage) {
        set({ currentPage: state.currentPage + 1 })
        get().fetchCustomerProducts()
      }
    },

    previousPage: () => {
      const state = get()
      if (state.hasPreviousPage) {
        set({ currentPage: state.currentPage - 1 })
        get().fetchCustomerProducts()
      }
    },

    // Selection actions
    toggleCustomerProduct: id => {
      set(state => {
        const newSelection = new Set(state.selectedCustomerProducts)
        if (newSelection.has(id)) {
          newSelection.delete(id)
        } else {
          newSelection.add(id)
        }
        return { selectedCustomerProducts: newSelection }
      })
    },

    toggleAll: () => {
      set(state => {
        const allIds = state.customerProducts.map(cp => cp.id)
        const isAllSelected = allIds.every(id =>
          state.selectedCustomerProducts.has(id)
        )

        if (isAllSelected) {
          return { selectedCustomerProducts: new Set() }
        } else {
          return { selectedCustomerProducts: new Set(allIds) }
        }
      })
    },

    clearSelection: () => {
      set({ selectedCustomerProducts: new Set() })
    },

    // Error handling
    clearError: () => {
      set({ error: null })
    },
  })
)

// Computed selectors
export const useCustomerProductsData = () => {
  const store = useCustomerProductStore()
  return {
    customerProducts: store.customerProducts,
    loading: store.loading,
    error: store.error,
    totalCount: store.totalCount,
    currentPage: store.currentPage,
    totalPages: store.totalPages,
    hasNextPage: store.hasNextPage,
    hasPreviousPage: store.hasPreviousPage,
  }
}

export const useCustomerProductsActions = () => {
  const store = useCustomerProductStore()
  return {
    fetchCustomerProducts: store.fetchCustomerProducts,
    refreshCustomerProducts: store.refreshCustomerProducts,
    createCustomerProduct: store.createCustomerProduct,
    updateCustomerProduct: store.updateCustomerProduct,
    deleteCustomerProduct: store.deleteCustomerProduct,
    bulkDeleteCustomerProducts: store.bulkDeleteCustomerProducts,
    setDefaultProduct: store.setDefaultProduct,
    getDefaultProduct: store.getDefaultProduct,
    bulkImportCustomerProducts: store.bulkImportCustomerProducts,
    setFilter: store.setFilter,
    setSearchTerm: store.setSearchTerm,
    setSorting: store.setSorting,
    clearFilters: store.clearFilters,
    setPage: store.setPage,
    nextPage: store.nextPage,
    previousPage: store.previousPage,
    toggleCustomerProduct: store.toggleCustomerProduct,
    toggleAll: store.toggleAll,
    clearSelection: store.clearSelection,
    clearError: store.clearError,
  }
}

export const useCustomerProductsSelection = () => {
  const store = useCustomerProductStore()
  const selectedCount = store.selectedCustomerProducts.size
  const isAllSelected =
    store.customerProducts.length > 0 &&
    store.customerProducts.every(cp =>
      store.selectedCustomerProducts.has(cp.id)
    )
  const isPartiallySelected = selectedCount > 0 && !isAllSelected

  return {
    selectedCustomerProducts: store.selectedCustomerProducts,
    selectedCount,
    isSelected: (id: string) => store.selectedCustomerProducts.has(id),
    isAllSelected,
    isPartiallySelected,
  }
}
