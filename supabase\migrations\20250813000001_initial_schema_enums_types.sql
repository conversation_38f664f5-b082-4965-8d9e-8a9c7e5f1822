-- Initial Database Schema - Enums and Types
-- Story 1.2: Database Schema & RLS Foundation
-- This migration creates all core enums and types required for the trading management system

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- ============================================================================
-- CORE ENUMS SECTION
-- ============================================================================

-- Role types for the 11 user roles in the system
CREATE TYPE role_type AS ENUM (
    'admin',
    'cs', 
    'account',
    'customer',
    'carrier',
    'driver',
    'factory',
    'shipper',
    'consignee',
    'notify_party',
    'forwarder_agent'
);

-- Company types for stakeholder categorization
CREATE TYPE company_type_enum AS ENUM (
    'customer',
    'carrier', 
    'factory',
    'shipper',
    'consignee',
    'notify_party',
    'forwarder_agent'
);

-- Shipment status tracking (11 statuses)
CREATE TYPE shipment_status_enum AS ENUM (
    'booking_confirmed',
    'transport_assigned',
    'driver_assigned',
    'empty_container_picked',
    'arrived_at_factory',
    'loading_started',
    'departed_factory',
    'container_returned',
    'shipped',
    'arrived',
    'completed'
);

-- Transportation modes
CREATE TYPE transport_mode_enum AS ENUM (
    'sea',
    'land',
    'rail',
    'air'
);

-- Currency codes
CREATE TYPE currency_enum AS ENUM (
    'THB',
    'CNY', 
    'USD',
    'EUR'
);

-- Packaging types for products
CREATE TYPE packaging_type_enum AS ENUM (
    'Bag',
    'Plastic Basket',
    'Carton'
);

-- Port types for logistics
CREATE TYPE port_type_enum AS ENUM (
    'origin',
    'destination',
    'transit'
);

-- Container specifications
CREATE TYPE container_type_enum AS ENUM (
    'dry',
    'reefer',
    'open_top',
    'flat_rack',
    'tank'
);

CREATE TYPE container_size_enum AS ENUM (
    '20ft',
    '40ft',
    '40hc',
    '45ft'
);

CREATE TYPE container_status_enum AS ENUM (
    'empty',
    'loaded',
    'sealed',
    'in_transit',
    'delivered'
);

-- Document types for document management
CREATE TYPE document_type_enum AS ENUM (
    'booking_confirmation',
    'invoice_fob',
    'invoice_cif',
    'shipping_instruction',
    'bill_of_lading',
    'photo_upload',
    'other'
);

-- Customer classification
CREATE TYPE customer_type_enum AS ENUM (
    'regular',
    'premium',
    'vip'
);

-- International trade terms
CREATE TYPE incoterms_enum AS ENUM (
    'FOB',
    'CIF',
    'EXW',
    'CFR'
);

-- Notification system types
CREATE TYPE notification_type_enum AS ENUM (
    'status_update',
    'assignment',
    'document_ready',
    'delay_alert',
    'system'
);

CREATE TYPE notification_channel_enum AS ENUM (
    'email',
    'sms',
    'line',
    'wechat',
    'in_app'
);

-- ============================================================================
-- HELPER FUNCTIONS SECTION
-- ============================================================================

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate shipment numbers: EX[Mode]-[Port]-YYMMDD-[Running]
CREATE OR REPLACE FUNCTION generate_shipment_number(
    p_transport_mode transport_mode_enum,
    p_port_code TEXT
)
RETURNS TEXT AS $$
DECLARE
    mode_prefix TEXT;
    date_str TEXT;
    running_number INTEGER;
    final_number TEXT;
BEGIN
    -- Get mode prefix
    mode_prefix := CASE p_transport_mode
        WHEN 'sea' THEN 'EXS'
        WHEN 'land' THEN 'EXL'
        WHEN 'rail' THEN 'EXR'
        WHEN 'air' THEN 'EXA'
        ELSE 'EXX'
    END;
    
    -- Get date string (YYMMDD)
    date_str := TO_CHAR(CURRENT_DATE, 'YYMMDD');
    
    -- Get running number for today
    SELECT COALESCE(MAX(
        CAST(SUBSTRING(shipment_number FROM '.*-([0-9]+)$') AS INTEGER)
    ), 0) + 1
    INTO running_number
    FROM shipments 
    WHERE shipment_number LIKE mode_prefix || '-' || p_port_code || '-' || date_str || '-%';
    
    -- Format final number
    final_number := mode_prefix || '-' || p_port_code || '-' || date_str || '-' || LPAD(running_number::TEXT, 3, '0');
    
    RETURN final_number;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TYPE role_type IS 'User roles in the trading management system - supports 11 distinct roles for multi-stakeholder access';
COMMENT ON TYPE company_type_enum IS 'Company stakeholder categories using hybrid design approach';
COMMENT ON TYPE shipment_status_enum IS 'Complete shipment lifecycle tracking from booking to completion';
COMMENT ON TYPE transport_mode_enum IS 'Available transportation modes for shipments';
COMMENT ON TYPE currency_enum IS 'Supported currencies for international trading';
COMMENT ON TYPE packaging_type_enum IS 'Product packaging specifications for weight calculations';
COMMENT ON TYPE container_type_enum IS 'Container types for different cargo requirements';
COMMENT ON TYPE container_size_enum IS 'Standard container sizes in the shipping industry';
COMMENT ON TYPE document_type_enum IS 'Document categories for shipment document management';
COMMENT ON TYPE notification_type_enum IS 'Notification categories for the communication system';
COMMENT ON TYPE notification_channel_enum IS 'Available communication channels including Asian messaging apps';

-- Function to validate metadata structure based on company type
-- Complex types (customer, carrier, factory) should have NULL metadata
-- Simple types (shipper, consignee, notify_party, forwarder_agent) use JSONB metadata
CREATE OR REPLACE FUNCTION validate_company_metadata(
  comp_type company_type_enum, 
  meta jsonb
) RETURNS boolean AS $$
BEGIN
  -- Validate based on company type
  CASE comp_type
    WHEN 'customer', 'carrier', 'factory' THEN
      -- Complex types should have NULL metadata (data stored in separate tables)
      RETURN meta IS NULL;
      
    WHEN 'shipper' THEN
      -- Simple type: validate shipper metadata structure if present
      IF meta IS NOT NULL THEN
        -- Validate company_seal structure if present
        IF meta ? 'company_seal' AND 
           NOT (meta->'company_seal' ? 'file_path' AND meta->'company_seal' ? 'mime_type') THEN
          RETURN false;
        END IF;
        
        -- Validate export_licenses is array if present
        IF meta ? 'export_licenses' AND 
           jsonb_typeof(meta->'export_licenses') != 'array' THEN
          RETURN false;
        END IF;
      END IF;
      
    WHEN 'consignee' THEN
      -- Simple type: validate consignee metadata
      IF meta IS NOT NULL THEN
        -- Validate USCI format if present (18 characters for China)
        IF meta ? 'usci' AND 
           length(meta->>'usci') != 18 THEN
          RETURN false;
        END IF;
        
        -- Validate customs_broker structure if present
        IF meta ? 'customs_broker' AND 
           NOT (meta->'customs_broker' ? 'name' AND meta->'customs_broker' ? 'phone') THEN
          RETURN false;
        END IF;
      END IF;
      
    WHEN 'notify_party' THEN
      -- Simple type: validate notify party metadata
      IF meta IS NOT NULL THEN
        -- Validate notification_preferences structure
        IF meta ? 'notification_preferences' AND 
           NOT jsonb_typeof(meta->'notification_preferences') = 'object' THEN
          RETURN false;
        END IF;
      END IF;
      
    WHEN 'forwarder_agent' THEN
      -- Simple type: validate forwarder agent metadata
      IF meta IS NOT NULL THEN
        -- Validate services is array if present
        IF meta ? 'services' AND 
           jsonb_typeof(meta->'services') != 'array' THEN
          RETURN false;
        END IF;
        
        -- Validate shipping_lines structure if present
        IF meta ? 'shipping_lines' AND 
           jsonb_typeof(meta->'shipping_lines') != 'array' THEN
          RETURN false;
        END IF;
      END IF;
      
    ELSE
      -- Unknown company type
      RETURN false;
  END CASE;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION update_updated_at_column() IS 'Trigger function to automatically update updated_at timestamps';
COMMENT ON FUNCTION generate_shipment_number(transport_mode_enum, TEXT) IS 'Generates standardized shipment numbers with format EX[Mode]-[Port]-YYMMDD-[Running]';
COMMENT ON FUNCTION validate_company_metadata(company_type_enum, jsonb) IS 'Validates company metadata structure based on hybrid design - complex types must have NULL metadata';