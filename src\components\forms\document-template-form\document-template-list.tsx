'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Loader2,
  <PERSON>,
  Plus,
  MoreH<PERSON><PERSON><PERSON>,
  Eye,
  Edit,
  Copy,
  Star,
  StarOff,
  ToggleLeft,
  Trash2,
  SlidersHorizontal,
  FileText,
  AlertCircle,
} from 'lucide-react'
import { useDocumentTemplates } from '@/hooks/use-document-templates'
import { useAuth } from '@/hooks/use-auth'
import type {
  DocumentTemplate,
  DocumentTemplateFilters,
  DocumentTemplateSortConfig,
  DocumentType
} from '@/types/document-template'
import { VALID_DOCUMENT_TYPES } from '@/lib/utils/template-validation'

interface DocumentTemplateListProps {
  onCreateNew?: () => void
  onEdit?: (template: DocumentTemplate) => void
  onView?: (template: DocumentTemplate) => void
  className?: string
}

interface DeleteDialogState {
  isOpen: boolean
  template: DocumentTemplate | null
}

interface CloneDialogState {
  isOpen: boolean
  template: DocumentTemplate | null
  newVersion: string
}

/**
 * Document template list component with filtering and actions
 * Story 5.1: Document Template Management System
 */
export function DocumentTemplateList({
  onCreateNew,
  onEdit,
  onView,
  className,
}: DocumentTemplateListProps) {
  // State for filters
  const [filters, setFilters] = useState<DocumentTemplateFilters>({})
  const [sort, setSort] = useState<DocumentTemplateSortConfig>({
    field: 'updated_at',
    direction: 'desc'
  })
  const [searchTerm, setSearchTerm] = useState('')
  
  // Dialog states
  const [deleteDialog, setDeleteDialog] = useState<DeleteDialogState>({
    isOpen: false,
    template: null
  })
  const [cloneDialog, setCloneDialog] = useState<CloneDialogState>({
    isOpen: false,
    template: null,
    newVersion: ''
  })

  // Use the custom hooks
  const { user, profile, isAuthenticated, isAdmin } = useAuth()
  
  // Debug logging - removed for production
  
  const {
    templates,
    totalCount,
    currentPage,
    totalPages,
    loading,
    error,
    deleteTemplate,
    toggleTemplateStatus,
    setDefaultTemplate,
    cloneTemplate,
    goToNextPage,
    goToPreviousPage,
    goToPage,
    refresh,
    clearError,
  } = useDocumentTemplates({
    filters: { ...filters, search: searchTerm },
    sort,
    pageSize: 20,
    autoRefresh: true
  })

  // Apply search filter with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters(prev => ({ ...prev, search: searchTerm }))
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // Document type options for filter
  const documentTypeOptions = VALID_DOCUMENT_TYPES.map(type => ({
    value: type,
    label: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }))

  const handleFilterChange = (key: keyof DocumentTemplateFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value
    }))
  }

  const handleSortChange = (field: keyof DocumentTemplate) => {
    setSort(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }))
  }

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.template) return

    const result = await deleteTemplate(deleteDialog.template.id)
    if (result.success) {
      setDeleteDialog({ isOpen: false, template: null })
    }
  }

  const handleCloneConfirm = async () => {
    if (!cloneDialog.template || !cloneDialog.newVersion || !user) return
    
    const result = await cloneTemplate(
      cloneDialog.template.id,
      cloneDialog.newVersion,
      user.id
    )
    
    if (result.success) {
      setCloneDialog({ isOpen: false, template: null, newVersion: '' })
    }
  }

  const getStatusBadge = (template: DocumentTemplate) => {
    if (!template.is_active) {
      return <Badge className="bg-slate-500 text-white hover:bg-slate-600">Inactive</Badge>
    }
    if (template.is_default) {
      return <Badge className="bg-blue-600 text-white hover:bg-blue-700">Default</Badge>
    }
    return <Badge className="bg-green-600 text-white hover:bg-green-700">Active</Badge>
  }

  const getDocumentTypeLabel = (type: DocumentType) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const getDocumentTypeBadge = (type: DocumentType) => {
    const label = getDocumentTypeLabel(type)
    
    // Color mapping similar to company types
    const colorMap: Record<string, string> = {
      'Invoice Cif': 'bg-blue-600 hover:bg-blue-700',
      'Invoice Fob': 'bg-blue-500 hover:bg-blue-600',
      'Packing List': 'bg-orange-600 hover:bg-orange-700',
      'Certificate': 'bg-purple-600 hover:bg-purple-700',
      'Bill Of Lading': 'bg-green-600 hover:bg-green-700',
      'Commercial Invoice': 'bg-indigo-600 hover:bg-indigo-700',
    }
    
    const colorClass = colorMap[label] || 'bg-slate-600 hover:bg-slate-700'
    
    return <Badge className={`${colorClass} text-white`}>{label}</Badge>
  }

  return (
    <div className={className}>
      <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div>
            <CardTitle className="text-xl font-semibold text-slate-800 dark:text-slate-200">
              Document Templates
            </CardTitle>
            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
              Manage templates for export documents with version control
            </p>
          </div>
          {onCreateNew && (
            <Button onClick={onCreateNew} className="bg-orange-600 hover:bg-orange-700">
              <Plus className="h-4 w-4 mr-2" />
              New Template
            </Button>
          )}
        </CardHeader>

        <CardContent className="bg-white dark:bg-slate-900">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <Select
              value={filters.document_type?.[0] || 'all'}
              onValueChange={(value) => 
                handleFilterChange('document_type', value === 'all' ? undefined : [value])
              }
            >
              <SelectTrigger className="w-48">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {documentTypeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={filters.is_active === undefined ? 'all' : filters.is_active ? 'active' : 'inactive'}
              onValueChange={(value) => 
                handleFilterChange('is_active', value === 'all' ? undefined : value === 'active')
              }
            >
              <SelectTrigger className="w-36">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Debug info removed - issue fixed */}

          {/* Error message */}
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearError}
                  className="ml-2 h-6 px-2"
                >
                  Dismiss
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Loading state */}
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading templates...</span>
            </div>
          )}

          {/* Templates table */}
          {!loading && (
            <>
              <div className="border rounded-lg bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-800">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead 
                        className="cursor-pointer hover:bg-slate-300 dark:hover:bg-slate-800"
                        onClick={() => handleSortChange('template_name')}
                      >
                        Name
                        {sort.field === 'template_name' && (
                          <span className="ml-2">
                            {sort.direction === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Version</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead
                        className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800"
                        onClick={() => handleSortChange('updated_at')}
                      >
                        Updated
                        {sort.field === 'updated_at' && (
                          <span className="ml-2">
                            {sort.direction === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </TableHead>
                      <TableHead className="w-12"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {templates.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8 text-slate-500">
                          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No templates found</p>
                          <p className="text-sm mt-1">
                            {searchTerm || filters.document_type || filters.is_active !== undefined
                              ? 'Try adjusting your filters'
                              : 'Create your first template to get started'
                            }
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      templates.map((template) => (
                        <TableRow key={template.id} className="hover:bg-muted/50 dark:hover:bg-slate-800/50">
                          <TableCell>
                            <div className="font-medium text-slate-900 dark:text-slate-100">
                              {template.template_name}
                            </div>
                            {template.description && (
                              <div className="text-sm text-slate-500 mt-1">
                                {template.description}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {getDocumentTypeBadge(template.document_type)}
                          </TableCell>
                          <TableCell className="font-mono text-sm text-slate-200">
                            {template.version}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(template)}
                          </TableCell>
                          <TableCell className="text-sm text-slate-200">
                            {new Date(template.updated_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  className="dark:bg-orange-500 hover:bg-orange-100 dark:hover:bg-orange-600 text-slate-600 dark:text-slate-200"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-48">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                
                                {onView && (
                                  <DropdownMenuItem onClick={() => onView(template)}>
                                    <Eye className="h-4 w-4 mr-2" />
                                    View
                                  </DropdownMenuItem>
                                )}
                                
                                {onEdit && (
                                  <DropdownMenuItem onClick={() => onEdit(template)}>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                )}
                                
                                <DropdownMenuItem
                                  onClick={() => 
                                    setCloneDialog({ 
                                      isOpen: true, 
                                      template, 
                                      newVersion: template.version.includes('.') 
                                        ? template.version.split('.').map((v, i) => i === 1 ? String(Number(v) + 1) : v).join('.')
                                        : `${template.version}.1`
                                    })
                                  }
                                >
                                  <Copy className="h-4 w-4 mr-2" />
                                  Clone
                                </DropdownMenuItem>

                                <DropdownMenuSeparator />

                                <DropdownMenuItem
                                  onClick={() => toggleTemplateStatus(template.id, !template.is_active)}
                                >
                                  <ToggleLeft className="h-4 w-4 mr-2" />
                                  {template.is_active ? 'Deactivate' : 'Activate'}
                                </DropdownMenuItem>

                                {template.is_active && !template.is_default && (
                                  <DropdownMenuItem
                                    onClick={() => setDefaultTemplate(template.id)}
                                  >
                                    <Star className="h-4 w-4 mr-2" />
                                    Set as Default
                                  </DropdownMenuItem>
                                )}

                                <DropdownMenuSeparator />

                                <DropdownMenuItem
                                  onClick={() => setDeleteDialog({ isOpen: true, template })}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Showing {(currentPage - 1) * 20 + 1} to {Math.min(currentPage * 20, totalCount)} of {totalCount} templates
                  </p>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={goToPreviousPage}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1
                      return (
                        <Button
                          key={page}
                          variant={page === currentPage ? "default" : "outline"}
                          size="sm"
                          onClick={() => goToPage(page)}
                        >
                          {page}
                        </Button>
                      )
                    })}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.isOpen} onOpenChange={(open) => !open && setDeleteDialog({ isOpen: false, template: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Template</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{deleteDialog.template?.template_name}"? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({ isOpen: false, template: null })}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={loading}
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Clone Template Dialog */}
      <Dialog open={cloneDialog.isOpen} onOpenChange={(open) => !open && setCloneDialog({ isOpen: false, template: null, newVersion: '' })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Clone Template</DialogTitle>
            <DialogDescription>
              Create a copy of "{cloneDialog.template?.template_name}" with a new version number.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <label htmlFor="version" className="block text-sm font-medium mb-2">
              New Version
            </label>
            <Input
              id="version"
              value={cloneDialog.newVersion}
              onChange={(e) => setCloneDialog(prev => ({ ...prev, newVersion: e.target.value }))}
              placeholder="e.g., 2.0, 1.1-beta"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCloneDialog({ isOpen: false, template: null, newVersion: '' })}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCloneConfirm}
              disabled={loading || !cloneDialog.newVersion.trim()}
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Clone Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}