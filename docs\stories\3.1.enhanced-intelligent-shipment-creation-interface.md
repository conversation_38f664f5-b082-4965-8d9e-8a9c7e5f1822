# Story 3.1: Enhanced Intelligent Shipment Creation Interface

## Status
Done

## Story
**As a** CS representative,  
**I want** to create shipments with Transportation Mode pre-selection, mandatory stakeholder fields, and intelligent pre-population based on relationships,  
**so that** I can efficiently initiate export processes with minimal data entry while ensuring all critical information is captured.

## Acceptance Criteria

**1:** Transportation Mode selection modal (Sea/Land/Rail) appears before shipment creation form and configures appropriate workflow fields and requirements.

**2:** Factory selection is mandatory with dropdown showing location and capacity information.

**3:** Forwarder Agent selection is mandatory with contact information and service details display.

**4:** ETD (Estimated Time of Departure), ETA (Estimated Time of Arrival), and Closing Time are mandatory datetime fields with validation ensuring logical sequence (Closing Time < ETD < ETA).

**5:** Destination Port selection is mandatory for routing and documentation, and require Origin Port with customer history suggestions.

**6:** Customer selection automatically loads associated shippers with default shipper pre-selected in cascading dropdown.

**7:** Customer selection automatically loads associated products with default product pre-selected and pricing auto-populated (CIF/FOB per KG).

**8:** Consignee selection automatically loads associated notify parties with default notify party pre-selected.

**9:** Booking Confirmation Document upload is mandatory with validation, preview, and secure storage capabilities.

**10:** Optional Notes field allows additional instructions visible to all stakeholders and included in shipment documentation.

**11:** Shipment number is automatically generated upon save using format EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running] where running number resets monthly per mode/port combination.

**12:** Form validation ensures data consistency, required field completion, and date/time logic validation before submission.

## Tasks / Subtasks

- [ ] Create Transportation Mode Selection Modal Component (AC: 1)
  - [ ] Create `src/components/forms/shipment-form/transport-mode-modal.tsx` with sea/land/rail selection
  - [ ] Implement modal backdrop with ShadCN UI Dialog component
  - [ ] Add transport mode configuration logic to adjust workflow fields dynamically
  - [ ] Integrate mode selection into shipment creation navigation flow

- [ ] Implement Enhanced Shipment Creation Form Structure (AC: 2, 3, 4, 5, 9, 10, 12)
  - [ ] Create `src/app/(dashboard)/shipments/create/page.tsx` with intelligent form layout
  - [ ] Implement mandatory factory selection dropdown with company type filtering (factory only)
  - [ ] Create forwarder agent selection dropdown with company type filtering (forwarder_agent only)
  - [ ] Add ETD/ETA/Closing Time datetime fields with sequence validation (Closing < ETD < ETA)
  - [ ] Implement origin/destination port selection with customer history suggestions
  - [ ] Add booking confirmation document upload with Supabase Storage integration
  - [ ] Create optional notes field for stakeholder instructions
  - [ ] Apply comprehensive form validation with Zod schema

- [ ] Implement Relationship Intelligence Engine Integration (AC: 6, 7, 8)
  - [ ] Create `src/hooks/use-shipment-relationships.ts` for intelligent pre-population
  - [ ] Implement customer selection with automatic shipper relationship loading
  - [ ] Add customer-product relationship loading with default product pre-selection
  - [ ] Implement consignee selection with notify party relationship pre-population
  - [ ] Add real-time cascading dropdown updates based on customer changes
  - [ ] Pre-populate CIF/FOB pricing from customer_products table automatically

- [ ] Implement Automatic Shipment Number Generation (AC: 11)
  - [ ] Create `src/lib/utils/shipment-number-generator.ts` with format logic
  - [ ] Implement EX[Mode]-[Port]-YYMMDD-[Running] generation pattern
  - [ ] Add monthly running number reset per mode/port combination
  - [ ] Integrate with shipment creation workflow for automatic assignment

- [ ] Create Form State Management and Validation (AC: 12)
  - [ ] Extend `src/lib/validations/shipment.ts` with comprehensive validation schema
  - [ ] Create `src/stores/shipment-creation-store.ts` for form state management
  - [ ] Implement real-time validation with error handling and user feedback
  - [ ] Add form persistence and draft saving capabilities

- [ ] Integrate with Database Schema and API Patterns (All ACs)
  - [ ] Update `src/hooks/use-shipments.ts` for enhanced creation operations
  - [ ] Implement relationship queries following established patterns from Story 2.8
  - [ ] Add Supabase Storage integration for document upload handling
  - [ ] Create shipment creation with initial status tracking

- [ ] Create Comprehensive Testing Suite (All ACs)
  - [ ] Write unit tests for transport mode modal and form validation
  - [ ] Test relationship intelligence engine with mock customer data
  - [ ] Create integration tests for shipment creation workflow with validation
  - [ ] Test document upload functionality with Supabase Storage integration
  - [ ] Validate shipment number generation patterns and collision handling
  - [ ] Test cascading dropdown updates and real-time pre-population
  - [ ] Create E2E tests for complete shipment creation workflow

## Dev Notes

### Previous Story Insights
From Story 2.8: Consignee-Notify Party Relationship Management completed with comprehensive relationship intelligence patterns. The established patterns for company type filtering, default designation logic, and real-time integration with shipment workflows provide a solid foundation for implementing the intelligent pre-population features in shipment creation. The relationship intelligence engine patterns can be extended to handle customer-shipper and customer-product relationships.

### Database Schema Context
**Shipments Table Design:**
[Source: User-provided shipments_schema.md]
The shipments table provides complete foundation for Story 3.1 implementation:
- id: UUID primary key with auto-generation (gen_random_uuid())
- shipment_number: text (unique) - Auto-generated with format EX[Mode]-[Port]-YYMMDD-[Running]
- transportation_mode: transport_mode_enum (sea/land/rail) - Controls workflow configuration
- customer_id, shipper_id, consignee_id, notify_party_id: UUID references to companies table
- factory_id, forwarder_agent_id: UUID references to companies table (mandatory fields)
- origin_port_id, destination_port_id: UUID references to ports table (mandatory)
- etd_date, eta_date, closing_time: timestamptz fields with logical sequence validation
- status: shipment_status_enum (default: 'booking_confirmed')
- currency_code: currency_enum (default: 'USD')
- notes: text field for stakeholder instructions
- metadata: jsonb for additional data storage
- created_by: UUID reference to profiles table for audit trail

**Database Constraints and Validation:**
- Unique constraint on shipment_number prevents duplicates
- Foreign key constraints with CASCADE for data integrity
- Date sequence validation: valid_date_sequence check ensures ETA >= ETD when both present
- Company type validation required through application logic for stakeholder roles
- Performance indexes on customer/status, ETD/ETA dates, and shipment numbers

**Related Tables Integration:**
- Companies table: stakeholder management with company_type filtering
- Ports table: origin/destination selection with routing information
- Customer_shippers table: relationship intelligence for shipper pre-population
- Customer_products table: product relationships with pricing (CIF/FOB per KG)
- Consignee_notify_parties table: notification workflow pre-population

### API Specifications and Database Access Patterns
[Source: backend-architecture.md#data-access-layer]
**Enhanced Shipment Creation Pattern:**
```typescript
// Intelligent shipment creation with relationship pre-population
const { data: shipment } = await supabase
  .from('shipments')
  .insert({
    shipment_number: generatedNumber, // EX[Mode]-[Port]-YYMMDD-[Running]
    transportation_mode,
    customer_id,
    shipper_id, // From customer_shippers relationship
    consignee_id,
    notify_party_id, // From consignee_notify_parties relationship
    factory_id, // Mandatory selection
    forwarder_agent_id, // Mandatory selection
    origin_port_id,
    destination_port_id,
    etd_date,
    eta_date,
    closing_time,
    status: 'booking_confirmed',
    currency_code: 'USD',
    notes,
    created_by: user_id
  })
  .select(`
    *,
    customer:companies!customer_id(name, company_type),
    shipper:companies!shipper_id(name),
    consignee:companies!consignee_id(name),
    notify_party:companies!notify_party_id(name),
    factory:companies!factory_id(name, contact_phone),
    forwarder_agent:companies!forwarder_agent_id(name, contact_phone),
    origin_port:ports!origin_port_id(port_name, country),
    destination_port:ports!destination_port_id(port_name, country)
  `)
  .single()
```

**Relationship Intelligence Queries:**
```typescript
// Customer shipper relationships for pre-population
const { data: customerShippers } = await supabase
  .from('customer_shippers')
  .select(`
    *,
    shipper:companies!shipper_id(name, contact_phone)
  `)
  .eq('customer_id', customer_id)
  .eq('is_active', true)
  .order('is_default', { ascending: false })

// Customer product relationships with pricing
const { data: customerProducts } = await supabase
  .from('customer_products')
  .select(`
    *,
    product:products(name, code, unit_of_measure_id)
  `)
  .eq('customer_id', customer_id)
  .eq('is_active', true)
  .order('is_default', { ascending: false })
```

### Component Architecture and Integration Patterns
[Source: frontend-architecture.md#component-architecture]
**Shipment Creation Form Architecture:**
- Enhanced creation page at `src/app/(dashboard)/shipments/create/page.tsx`
- Transport mode modal at `src/components/forms/shipment-form/transport-mode-modal.tsx`
- Main form component at `src/components/forms/shipment-form/shipment-form.tsx`
- Stakeholder section at `src/components/forms/shipment-form/stakeholder-section.tsx`
- Logistics section at `src/components/forms/shipment-form/logistics-section.tsx`

**ShadCN UI Component Usage:**
- Use Dialog component for transport mode selection modal
- Implement Select components for stakeholder dropdowns with company type filtering
- Use DateTimePicker components for ETD/ETA/Closing Time fields
- Implement file upload component for booking confirmation documents
- Use Textarea component for notes field with stakeholder visibility
- Apply Form components with react-hook-form and Zod validation
- Leverage existing DataTable patterns for relationship lookups

**Real-time Integration Patterns:**
[Source: core-workflows.md#intelligent-shipment-creation-workflow]
- Relationship Intelligence Engine integration for automatic pre-population
- Real-time cascading dropdown updates when customer selection changes
- Intelligent default selections based on customer relationship patterns
- Real-time form validation with immediate user feedback

### File Locations and Project Structure Alignment
[Source: unified-project-structure.md]
**Shipment Creation File Structure:**
- Main creation page: `src/app/(dashboard)/shipments/create/page.tsx`
- Transport mode modal: `src/components/forms/shipment-form/transport-mode-modal.tsx`
- Enhanced shipment form: `src/components/forms/shipment-form/shipment-form.tsx`
- Stakeholder section: `src/components/forms/shipment-form/stakeholder-section.tsx`
- Logistics section: `src/components/forms/shipment-form/logistics-section.tsx`
- Validation: `src/lib/validations/shipment.ts` for comprehensive form validation
- State management: `src/stores/shipment-creation-store.ts` for creation workflow
- Hooks: `src/hooks/use-shipment-relationships.ts` for relationship intelligence
- Utils: `src/lib/utils/shipment-number-generator.ts` for auto-generation

### Technical Constraints and Requirements
[Source: tech-stack.md#technology-stack-table]
- Maintain consistency with Next.js 14.2+ App Router and TypeScript 5.3+
- Follow Supabase Client API patterns with auto-generated TypeScript types
- Use PostgreSQL timestamptz for date/time fields with timezone support
- Maintain ShadCN UI components with established dark blue theme
- Follow Zustand 4.5+ state management patterns for form state
- Apply Zod validation patterns with comprehensive field validation
- Integrate Supabase Storage for secure document upload handling
- Support transportation mode enum: 'sea', 'land', 'rail'

### Company Type Validation and Filtering Requirements
**Stakeholder Selection Filtering:**
- Factory selection: Filter to company_type = 'factory' only
- Forwarder Agent selection: Filter to company_type = 'forwarder_agent' only  
- Customer selection: Filter to company_type = 'customer' only
- Shipper selection: Filter to company_type = 'shipper' from customer relationships
- Consignee selection: Filter to company_type = 'consignee' only
- Notify Party selection: Filter from consignee relationships with company_type = 'notify_party'

**Relationship Intelligence Requirements:**
- Customer selection triggers automatic loading of associated shippers
- Default shipper pre-selected based on customer_shippers.is_default = true
- Customer selection loads associated products with pricing (CIF/FOB per KG)
- Default product pre-selected based on customer_products.is_default = true
- Consignee selection loads associated notify parties
- Default notify party pre-selected based on consignee_notify_parties.is_default = true

### Document Upload and Storage Requirements
**Booking Confirmation Document:**
- Mandatory file upload using Supabase Storage integration
- File type validation: PDF, DOC, DOCX, JPG, PNG (max 10MB)
- Secure storage with Row Level Security policies
- Preview capability for document verification
- File metadata tracking in database (filename, size, mime_type)

### Form Validation and User Experience Requirements
**Validation Rules:**
- All mandatory fields must be completed before submission
- Date sequence validation: Closing Time < ETD < ETA
- ETD must be future date (cannot be in the past)
- Customer, Factory, Forwarder Agent selection required
- Origin and Destination Port selection required
- Booking confirmation document upload required

**User Experience Patterns:**
- Transport mode modal appears first to configure workflow
- Intelligent pre-population reduces manual data entry
- Real-time validation with immediate error feedback
- Form persistence for draft saving and recovery
- Clear indication of mandatory vs. optional fields
- Loading states during relationship intelligence queries

### Testing Standards
**Test Location**: `tests/` directory with `unit/`, `integration/`, and `e2e/` subdirectories for shipment creation tests
**Testing Framework**: Vitest + Testing Library for unit tests, Playwright for E2E shipment workflows
**Testing Patterns**: 
- Component testing for transport mode modal and shipment creation form
- Integration testing with local Supabase instance for relationship queries
- Mock data for customer relationships and intelligent pre-population
- E2E testing for complete shipment creation workflow
- Document upload testing with Supabase Storage integration

**Specific Testing Requirements for This Story**:
- Test transport mode selection modal with workflow field configuration
- Validate mandatory field enforcement and form submission blocking
- Test relationship intelligence engine with customer/shipper/product pre-population
- Verify consignee/notify party relationship integration
- Test shipment number generation with format validation and collision handling
- Validate date sequence logic (Closing Time < ETD < ETA)
- Test document upload with file validation and storage integration
- Verify real-time cascading dropdown updates
- Test form persistence and draft saving capabilities
- Validate complete shipment creation workflow with all stakeholders

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-25 | 1.0 | Initial story creation with comprehensive architecture context and shipments schema integration | Scrum Master |

## Dev Agent Record

### Agent Model Used

Claude Sonnet 4 (20250514) - Full Stack Developer Agent (James) - Started implementation on 2025-08-25

### Debug Log References

*This section will be populated by the development agent during implementation*

### Completion Notes List

*This section will be populated by the development agent during implementation*

### File List

*This section will be populated by the development agent during implementation*

## QA Results

*This section will be populated by the QA agent during review*