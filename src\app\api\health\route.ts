import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()

    // Test database connection with a simple query instead of specific table
    const { data: dbHealth, error: dbError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1)

    // Test authentication service
    const { data: authHealth, error: authError } = await supabase.auth.getUser()

    // Test storage service (basic check)
    const { data: storageHealth, error: storageError } =
      await supabase.storage.listBuckets()

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services: {
        database: {
          status: dbError ? 'unhealthy' : 'healthy',
          error: dbError?.message || null,
          details: 'PostgreSQL connection test',
        },
        authentication: {
          status: authError ? 'unhealthy' : 'healthy',
          error: authError?.message || null,
          details: 'Supabase Auth service test',
        },
        storage: {
          status: storageError ? 'unhealthy' : 'healthy',
          error: storageError?.message || null,
          details: 'Supabase Storage service test',
        },
        realtime: {
          status: 'healthy', // Basic check - would need actual connection test
          error: null,
          details: 'Supabase Realtime service (not tested in health check)',
        },
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        nextJsVersion: process.env.npm_package_version || 'unknown',
        supabaseConfigured: !!(
          process.env.NEXT_PUBLIC_SUPABASE_URL &&
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        ),
      },
    }

    // Determine overall status
    const hasUnhealthyService = Object.values(healthStatus.services).some(
      service => service.status === 'unhealthy'
    )

    if (hasUnhealthyService) {
      healthStatus.status = 'degraded'
    }

    const httpStatus = healthStatus.status === 'healthy' ? 200 : 503

    return NextResponse.json(healthStatus, { status: httpStatus })
  } catch (error) {
    const errorResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      services: {
        database: {
          status: 'unknown',
          error: 'Could not test',
          details: 'Health check failed',
        },
        authentication: {
          status: 'unknown',
          error: 'Could not test',
          details: 'Health check failed',
        },
        storage: {
          status: 'unknown',
          error: 'Could not test',
          details: 'Health check failed',
        },
        realtime: {
          status: 'unknown',
          error: 'Could not test',
          details: 'Health check failed',
        },
      },
    }

    return NextResponse.json(errorResponse, { status: 500 })
  }
}
