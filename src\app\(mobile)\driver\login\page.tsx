'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Eye, EyeOff, Loader2, Smartphone, Mail } from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { loginSchema, phoneAuthSchema, otpVerificationSchema, type LoginFormData, type PhoneAuthFormData, type OtpVerificationFormData } from '@/lib/validations/auth'
import { authClient, type AuthError } from '@/lib/supabase/auth'
import { AuthErrorDisplay } from '@/components/auth/auth-error-display'

interface MobileLoginFormData extends LoginFormData {
  loginType?: 'email' | 'phone'
}

type PhoneAuthStep = 'phone' | 'otp'

function MobileLoginForm() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<AuthError | null>(null)
  const [loginType, setLoginType] = useState<'email' | 'phone'>('email')
  const [phoneAuthStep, setPhoneAuthStep] = useState<PhoneAuthStep>('phone')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [otpCountdown, setOtpCountdown] = useState(0)
  const router = useRouter()
  const searchParams = useSearchParams()
  const { isAuthenticated, loading, profile } = useAuth()

  const form = useForm<MobileLoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  })

  const phoneForm = useForm<PhoneAuthFormData>({
    resolver: zodResolver(phoneAuthSchema),
    defaultValues: {
      phone: '',
    },
  })

  const otpForm = useForm<OtpVerificationFormData>({
    resolver: zodResolver(otpVerificationSchema),
    defaultValues: {
      phone: '',
      token: '',
    },
  })

  // Handle navigation when authentication state changes
  useEffect(() => {
    if (!loading && isAuthenticated) {
      // Only allow drivers to access this mobile app
      if (profile?.role === 'driver') {
        const redirectTo = searchParams.get('redirectTo')
        const destination = redirectTo || '/driver/dashboard'
        console.log('Driver login: User authenticated, redirecting to:', destination)
        router.push(destination)
      } else {
        // Non-drivers get redirected to main app
        router.push('/dashboard')
      }
    }
  }, [isAuthenticated, loading, profile, router, searchParams])

  // OTP countdown timer effect
  useEffect(() => {
    if (otpCountdown > 0) {
      const timer = setTimeout(() => {
        setOtpCountdown(otpCountdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [otpCountdown])

  // Reset phone auth step when login type changes
  useEffect(() => {
    if (loginType === 'phone') {
      setPhoneAuthStep('phone')
      setError(null)
    }
  }, [loginType])

  async function onSubmit(data: MobileLoginFormData) {
    setIsLoading(true)
    setError(null)

    try {
      const result = await authClient.signIn(data.email, data.password)
      
      // Additional driver role validation
      if (result.user) {
        const { data: profile } = await authClient.supabase
          .from('profiles')
          .select('role')
          .eq('user_id', result.user.id)
          .single()
        
        if (profile?.role !== 'driver') {
          await authClient.signOut()
          throw {
            code: 'INVALID_ROLE',
            message: 'This mobile app is only for drivers. Please use the main application.',
            timestamp: new Date(),
          } as AuthError
        }
      }
    } catch (error) {
      console.error('Driver login error:', error)
      if ((error as AuthError).code) {
        setError(error as AuthError)
      } else {
        setError({
          code: 'UNKNOWN_ERROR',
          message:
            error instanceof Error
              ? error.message
              : 'An error occurred during login. Please try again.',
          timestamp: new Date(),
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  async function onPhoneSubmit(data: PhoneAuthFormData) {
    setIsLoading(true)
    setError(null)

    try {
      await authClient.signInWithPhone(data.phone)
      setPhoneNumber(data.phone)
      setPhoneAuthStep('otp')
      setOtpCountdown(60) // 1 minute countdown
      
      // Set phone in OTP form
      otpForm.setValue('phone', data.phone)
      
    } catch (error) {
      console.error('Phone auth error:', error)
      if ((error as AuthError).code) {
        setError(error as AuthError)
      } else {
        setError({
          code: 'UNKNOWN_ERROR',
          message:
            error instanceof Error
              ? error.message
              : 'Failed to send verification code. Please try again.',
          timestamp: new Date(),
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  async function onOtpSubmit(data: OtpVerificationFormData) {
    setIsLoading(true)
    setError(null)

    try {
      await authClient.verifyPhoneOtp(data.phone, data.token)
      // Navigation will be handled by useEffect when auth state changes
      
    } catch (error) {
      console.error('OTP verification error:', error)
      if ((error as AuthError).code) {
        setError(error as AuthError)
      } else {
        setError({
          code: 'UNKNOWN_ERROR',
          message:
            error instanceof Error
              ? error.message
              : 'Invalid verification code. Please try again.',
          timestamp: new Date(),
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  function handleRetry() {
    setError(null)
    if (loginType === 'phone') {
      if (phoneAuthStep === 'phone') {
        phoneForm.handleSubmit(onPhoneSubmit)()
      } else {
        otpForm.handleSubmit(onOtpSubmit)()
      }
    } else {
      form.handleSubmit(onSubmit)()
    }
  }

  function handleResendCode() {
    if (otpCountdown === 0) {
      phoneForm.handleSubmit(onPhoneSubmit)()
    }
  }

  function handleBackToPhone() {
    setPhoneAuthStep('phone')
    setError(null)
    otpForm.reset()
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 p-6 text-center bg-gradient-to-b from-slate-800 to-slate-900">
        <div className="mx-auto h-20 w-20 rounded-2xl bg-orange-500 flex items-center justify-center mb-4 shadow-lg">
          <span className="text-3xl font-bold text-white">DYY</span>
        </div>
        <h1 className="text-2xl font-bold text-white mb-2">
          Driver App
        </h1>
        <p className="text-slate-300 text-sm">
          Sign in to access your assignments
        </p>
      </div>

      {/* Login Form */}
      <div className="flex-1 px-6 py-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <AuthErrorDisplay
              error={error}
              onRetry={handleRetry}
              className="mb-4"
            />

            {/* Login Type Tabs */}
            <Tabs
              value={loginType}
              onValueChange={(value) => setLoginType(value as 'email' | 'phone')}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2 bg-slate-800 border border-slate-700 p-1 rounded-lg h-auto">
                <TabsTrigger 
                  value="email" 
                  className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-slate-300 h-12 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center gap-2 px-4 py-2 border-0 shadow-none"
                >
                  <Mail className="w-4 h-4 flex-shrink-0" />
                  <span>Email</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="phone" 
                  className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-slate-300 h-12 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center gap-2 px-4 py-2 border-0 shadow-none"
                >
                  <Smartphone className="w-4 h-4 flex-shrink-0" />
                  <span>Phone</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="email" className="mt-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white text-base font-medium">Email Address</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="Enter your email"
                          className="h-14 text-lg bg-slate-800 border-slate-700 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 rounded-lg"
                          disabled={isLoading}
                          autoComplete="email"
                        />
                      </FormControl>
                      <FormMessage className="text-red-400 text-sm" />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="phone" className="mt-6">
                {phoneAuthStep === 'phone' ? (
                  <Form {...phoneForm}>
                    <form onSubmit={phoneForm.handleSubmit(onPhoneSubmit)} className="space-y-4">
                      <FormField
                        control={phoneForm.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white text-base font-medium">Phone Number</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="tel"
                                placeholder="+66 XX XXX XXXX"
                                className="h-14 text-lg bg-slate-800 border-slate-700 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 rounded-lg"
                                disabled={isLoading}
                                autoComplete="tel"
                              />
                            </FormControl>
                            <FormMessage className="text-red-400 text-sm" />
                          </FormItem>
                        )}
                      />
                      
                      <Button
                        type="submit"
                        className="w-full h-14 text-lg font-semibold bg-orange-500 hover:bg-orange-600 text-white rounded-lg shadow-lg mt-4"
                        disabled={isLoading}
                      >
                        {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                        Send Verification Code
                      </Button>

                      <p className="text-sm text-slate-400 text-center mt-2">
                        We'll send a 6-digit code to your phone number
                      </p>
                    </form>
                  </Form>
                ) : (
                  <Form {...otpForm}>
                    <form onSubmit={otpForm.handleSubmit(onOtpSubmit)} className="space-y-4">
                      <div className="text-center mb-4">
                        <p className="text-white text-base font-medium mb-1">Verification Code</p>
                        <p className="text-sm text-slate-400">
                          Enter the 6-digit code sent to {phoneNumber}
                        </p>
                      </div>

                      <FormField
                        control={otpForm.control}
                        name="token"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                {...field}
                                type="text"
                                inputMode="numeric"
                                pattern="[0-9]*"
                                placeholder="000000"
                                maxLength={6}
                                className="h-14 text-lg bg-slate-800 border-slate-700 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 rounded-lg text-center tracking-widest"
                                disabled={isLoading}
                                autoComplete="one-time-code"
                              />
                            </FormControl>
                            <FormMessage className="text-red-400 text-sm" />
                          </FormItem>
                        )}
                      />
                      
                      <Button
                        type="submit"
                        className="w-full h-14 text-lg font-semibold bg-orange-500 hover:bg-orange-600 text-white rounded-lg shadow-lg"
                        disabled={isLoading}
                      >
                        {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                        Verify Code
                      </Button>

                      <div className="flex justify-between items-center mt-4">
                        <button
                          type="button"
                          onClick={handleBackToPhone}
                          className="text-slate-400 hover:text-white transition-colors text-sm"
                          disabled={isLoading}
                        >
                          ← Change phone number
                        </button>
                        
                        <button
                          type="button"
                          onClick={handleResendCode}
                          className={`text-sm transition-colors ${
                            otpCountdown > 0 
                              ? 'text-slate-500 cursor-not-allowed' 
                              : 'text-orange-400 hover:text-orange-300'
                          }`}
                          disabled={isLoading || otpCountdown > 0}
                        >
                          {otpCountdown > 0 ? `Resend in ${otpCountdown}s` : 'Resend code'}
                        </button>
                      </div>
                    </form>
                  </Form>
                )}
              </TabsContent>
            </Tabs>

            {loginType === 'email' && (
              <>
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white text-base font-medium">Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type={showPassword ? 'text' : 'password'}
                            placeholder="Enter your password"
                            className="h-14 text-lg bg-slate-800 border-slate-700 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 rounded-lg pr-14"
                            disabled={isLoading}
                            autoComplete="current-password"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-white transition-colors p-2"
                            disabled={isLoading}
                          >
                            {showPassword ? (
                              <EyeOff className="h-5 w-5" />
                            ) : (
                              <Eye className="h-5 w-5" />
                            )}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-400 text-sm" />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full h-14 text-lg font-semibold bg-orange-500 hover:bg-orange-600 text-white rounded-lg shadow-lg"
                  disabled={isLoading}
                >
                  {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                  Sign In
                </Button>
              </>
            )}
          </form>
        </Form>

        {/* Footer Links */}
        <div className="mt-8 text-center">
          <Link
            href="/forgot-password"
            className="text-orange-400 hover:text-orange-300 transition-colors text-base font-medium"
          >
            Forgot your password?
          </Link>
        </div>
      </div>

      {/* Bottom Info */}
      <div className="flex-shrink-0 p-6 text-center bg-slate-800 border-t border-slate-700">
        <p className="text-slate-400 text-sm">
          Need help? Contact your dispatcher
        </p>
        <p className="text-slate-500 text-xs mt-2">
          © 2025 DYY Trading Management
        </p>
      </div>
    </div>
  )
}

export default function MobileDriverLoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="flex items-center space-x-2 text-white">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="text-lg">Loading...</span>
        </div>
      </div>
    }>
      <MobileLoginForm />
    </Suspense>
  )
}