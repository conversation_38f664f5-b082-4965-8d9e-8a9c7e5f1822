import { renderHook, act } from '@testing-library/react'
import { useStatusUpdates, useStatusValidation } from '../use-status-updates'
import { createClient } from '@/lib/supabase/client'

// Mock dependencies
jest.mock('@/lib/supabase/client')
jest.mock('../use-auth')
jest.mock('@/stores/shipment-store')

const mockSupabase = {
  from: jest.fn(),
  rpc: jest.fn(),
}

const mockAuth = {
  user: { id: 'test-user-id' },
  profile: { role: 'cs' },
}

const mockShipmentStore = {
  updateShipment: jest.fn(),
}

beforeEach(() => {
  jest.clearAllMocks()
  ;(createClient as jest.Mock).mockReturnValue(mockSupabase)
  
  // Mock useAuth
  const useAuth = require('../use-auth').useAuth
  useAuth.mockReturnValue(mockAuth)
  
  // Mock useShipmentStore
  const useShipmentStore = require('@/stores/shipment-store').useShipmentStore
  useShipmentStore.mockReturnValue(mockShipmentStore)
})

describe('useStatusUpdates', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useStatusUpdates())
    
    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBe(null)
    expect(typeof result.current.updateStatus).toBe('function')
    expect(typeof result.current.getStatusHistory).toBe('function')
    expect(typeof result.current.clearError).toBe('function')
  })

  it('should update status successfully', async () => {
    // Mock successful shipment status fetch
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { status: 'booking_confirmed' },
            error: null,
          }),
        }),
      }),
    })

    // Mock successful RPC call
    mockSupabase.rpc.mockResolvedValue({
      error: null,
    })

    const { result } = renderHook(() => useStatusUpdates())
    
    await act(async () => {
      await result.current.updateStatus({
        shipment_id: 'test-shipment-id',
        status_to: 'transport_assigned',
        notes: 'Test notes',
        location: 'Test location',
      })
    })

    expect(mockSupabase.rpc).toHaveBeenCalledWith('update_shipment_status', {
      p_shipment_id: 'test-shipment-id',
      p_status_from: 'booking_confirmed',
      p_status_to: 'transport_assigned',
      p_updated_by: 'test-user-id',
      p_notes: 'Test notes',
      p_location: 'Test location',
      p_latitude: undefined,
      p_longitude: undefined,
    })

    expect(mockShipmentStore.updateShipment).toHaveBeenCalledWith(
      'test-shipment-id',
      expect.objectContaining({
        status: 'transport_assigned',
      })
    )
  })

  it('should handle invalid status transitions', async () => {
    // Mock shipment status fetch
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { status: 'completed' },
            error: null,
          }),
        }),
      }),
    })

    const { result } = renderHook(() => useStatusUpdates())
    
    await act(async () => {
      try {
        await result.current.updateStatus({
          shipment_id: 'test-shipment-id',
          status_to: 'booking_confirmed',
        })
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect((error as Error).message).toContain('Invalid status transition')
      }
    })
  })

  it('should fetch status history successfully', async () => {
    const mockStatusHistory = [
      {
        id: '1',
        shipment_id: 'test-shipment-id',
        status_from: 'booking_confirmed',
        status_to: 'transport_assigned',
        notes: 'Test notes',
        created_at: '2024-01-01T00:00:00Z',
      },
    ]

    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: mockStatusHistory,
            error: null,
          }),
        }),
      }),
    })

    const { result } = renderHook(() => useStatusUpdates())
    
    let history: any[] = []
    await act(async () => {
      history = await result.current.getStatusHistory('test-shipment-id')
    })

    expect(history).toEqual(mockStatusHistory)
    expect(mockSupabase.from).toHaveBeenCalledWith('status_history')
  })

  it('should handle errors gracefully', async () => {
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Shipment not found' },
          }),
        }),
      }),
    })

    const { result } = renderHook(() => useStatusUpdates())
    
    await act(async () => {
      try {
        await result.current.updateStatus({
          shipment_id: 'invalid-id',
          status_to: 'transport_assigned',
        })
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
      }
    })
  })

  it('should clear error state', () => {
    const { result } = renderHook(() => useStatusUpdates())
    
    act(() => {
      result.current.clearError()
    })

    expect(result.current.error).toBe(null)
  })
})

describe('useStatusValidation', () => {
  it('should validate status transitions correctly', () => {
    const { result } = renderHook(() => useStatusValidation())
    
    // Valid transition
    expect(
      result.current.validateTransition('booking_confirmed', 'transport_assigned')
    ).toBe(true)
    
    // Invalid transition
    expect(
      result.current.validateTransition('completed', 'booking_confirmed')
    ).toBe(false)
  })

  it('should return valid next statuses', () => {
    const { result } = renderHook(() => useStatusValidation())
    
    const nextStatuses = result.current.getValidNextStatuses('booking_confirmed')
    expect(nextStatuses).toContain('transport_assigned')
    expect(nextStatuses).toContain('cancelled')
  })

  it('should identify terminal statuses', () => {
    const { result } = renderHook(() => useStatusValidation())
    
    expect(result.current.isTerminalStatus('completed')).toBe(true)
    expect(result.current.isTerminalStatus('cancelled')).toBe(true)
    expect(result.current.isTerminalStatus('booking_confirmed')).toBe(false)
  })
})