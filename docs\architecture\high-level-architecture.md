# High Level Architecture

## Technical Summary

The DYY Trading Fruit Export Management System follows a modern serverless-first architecture built on Next.js 14+ App Router with Supabase as the Backend-as-a-Service platform. The frontend leverages server-side rendering for optimal performance while maintaining client-side interactivity through real-time subscriptions. The backend utilizes Supabase's PostgreSQL database with Row Level Security policies for multi-tenant data access, complemented by Edge Functions for multi-channel notifications and document processing. The system integrates seamlessly with mobile Progressive Web App capabilities for offline driver operations, supporting the complete fruit export workflow from booking to delivery with sub-2 second page load times and 99.9% uptime requirements.

## Platform and Infrastructure Choice

**Platform:** Vercel (Frontend) + Supabase (Backend)
**Key Services:** Vercel Edge Functions, Supabase Database, Supabase Auth, Supabase Storage, Supabase Edge Functions, Supabase Real-time
**Deployment Host and Regions:** Vercel Global Edge Network (primary: Asia-Pacific for Thailand operations), Supabase Asia-Southeast (Singapore)

**Recommendation: Vercel + Supabase**

**Rationale**: 
- **Performance Alignment**: Vercel's edge network directly supports the <2s load time requirement
- **Development Velocity**: Essential for 6-month development timeline with 6 epics
- **Supabase Synergy**: Native integration reduces complexity for real-time features and authentication
- **Mobile PWA**: Vercel's edge deployment ideal for PWA service worker distribution

## Repository Structure

**Structure:** Monorepo with integrated frontend/backend
**Monorepo Tool:** Native Next.js project structure (no additional monorepo tool needed)
**Package Organization:** 
- `/app` - Next.js App Router pages and API routes
- `/components` - Shared UI components  
- `/lib` - Shared utilities and Supabase client
- `/types` - Shared TypeScript interfaces
- `/supabase` - Database schema, migrations, and Edge Functions

## High Level Architecture Diagram

```mermaid
graph TB
    subgraph "User Interfaces"
        WEB[Web Dashboard<br/>CS/Admin Users]
        PWA[Mobile PWA<br/>Drivers/Field Users]
        MOBILE[Mobile Web<br/>Customers/Stakeholders]
    end
    
    subgraph "Vercel Edge Network"
        EDGE[Edge Functions<br/>Middleware/Auth]
        CDN[Global CDN<br/>Static Assets]
        SSR[Next.js SSR<br/>App Router]
    end
    
    subgraph "Next.js Application"
        APP[App Router Pages]
        API[API Routes]
        COMP[Components/UI]
        PWA_SW[Service Worker<br/>Offline Support]
    end
    
    subgraph "Supabase Platform"
        AUTH[Supabase Auth<br/>Role-based Access]
        DB[(PostgreSQL<br/>RLS Policies)]
        STORAGE[Supabase Storage<br/>Documents/Images]
        REALTIME[Real-time<br/>Subscriptions]
        EDGE_FUNC[Edge Functions<br/>Notifications/Processing]
    end
    
    subgraph "External Services"
        EMAIL[Email Service<br/>SMTP]
        SMS[SMS Service<br/>Twilio]
        LINE[Line API<br/>Thai Market]
        WECHAT[WeChat API<br/>Chinese Market]
        PDF[PDF Generation<br/>Document Service]
    end
    
    WEB --> EDGE
    PWA --> EDGE  
    MOBILE --> EDGE
    EDGE --> SSR
    SSR --> APP
    APP --> API
    API --> AUTH
    API --> DB
    API --> STORAGE
    API --> REALTIME
    PWA_SW --> API
    EDGE_FUNC --> EMAIL
    EDGE_FUNC --> SMS
    EDGE_FUNC --> LINE
    EDGE_FUNC --> WECHAT
    EDGE_FUNC --> PDF
    DB --> EDGE_FUNC
    REALTIME --> APP
```

## Architectural Patterns

- **Jamstack Architecture:** Static site generation with serverless APIs for optimal performance and scalability suitable for international fruit export operations - _Rationale:_ Enables <2s load times globally while supporting dynamic real-time features through Supabase subscriptions

- **Progressive Web App Pattern:** Service worker-enabled mobile interface with offline capabilities - _Rationale:_ Critical for driver operations in areas with poor network connectivity during transportation

- **Row Level Security Pattern:** Database-level security policies for multi-tenant data access - _Rationale:_ Ensures data isolation between customers, carriers, and other stakeholders without application-level complexity

- **Real-time Subscription Pattern:** WebSocket-based status updates for live shipment tracking - _Rationale:_ Provides immediate visibility for the 90% real-time status tracking requirement

- **Edge Computing Pattern:** Global edge deployment for reduced latency - _Rationale:_ Supports international operations with consistent performance across regions

- **Repository Pattern:** Abstracted data access layer with Supabase client - _Rationale:_ Enables testing and provides consistent API across the application

- **Component-Based UI Pattern:** Reusable ShadCN UI components with TypeScript - _Rationale:_ Maintains consistency across desktop and mobile interfaces while supporting the dark blue theme requirements
