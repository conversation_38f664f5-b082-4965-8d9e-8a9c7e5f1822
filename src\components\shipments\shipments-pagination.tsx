'use client'

import { useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  Loader2 
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ShipmentsPaginationProps {
  currentPage: number
  totalPages: number
  totalCount: number
  pageSize: number
  currentCount: number
  isLoading?: boolean
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
  className?: string
}

const PAGE_SIZE_OPTIONS = [10, 25, 50, 100]

export function ShipmentsPagination({
  currentPage,
  totalPages,
  totalCount,
  pageSize,
  currentCount,
  isLoading = false,
  onPageChange,
  onPageSizeChange,
  className,
}: ShipmentsPaginationProps) {
  // Generate page numbers to display
  const visiblePages = useMemo(() => {
    const pages: (number | 'ellipsis')[] = []
    const maxVisiblePages = 7

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // Always show first page
      pages.push(1)

      if (currentPage <= 4) {
        // Show pages 2-5, then ellipsis, then last page
        for (let i = 2; i <= Math.min(5, totalPages - 1); i++) {
          pages.push(i)
        }
        if (totalPages > 5) {
          pages.push('ellipsis')
        }
      } else if (currentPage >= totalPages - 3) {
        // Show first page, ellipsis, then last 4 pages
        pages.push('ellipsis')
        for (let i = Math.max(2, totalPages - 4); i <= totalPages - 1; i++) {
          pages.push(i)
        }
      } else {
        // Show first page, ellipsis, current page ± 1, ellipsis, last page
        pages.push('ellipsis')
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i)
        }
        pages.push('ellipsis')
      }

      // Always show last page if not already included
      if (!pages.includes(totalPages)) {
        pages.push(totalPages)
      }
    }

    return pages
  }, [currentPage, totalPages])

  const startItem = (currentPage - 1) * pageSize + 1
  const endItem = Math.min(currentPage * pageSize, totalCount)

  const handlePageChange = (page: number) => {
    if (page !== currentPage && page >= 1 && page <= totalPages && !isLoading) {
      onPageChange(page)
    }
  }

  const handlePageSizeChange = (newPageSize: string) => {
    if (!isLoading) {
      onPageSizeChange(parseInt(newPageSize))
    }
  }

  const canGoPrevious = currentPage > 1 && !isLoading
  const canGoNext = currentPage < totalPages && !isLoading

  if (totalCount === 0) {
    return null
  }

  return (
    <div className={cn('flex items-center justify-between', className)}>
      {/* Left side: Results info and page size selector */}
      <div className="flex items-center gap-4">
        <div className="text-sm text-slate-400">
          Showing {startItem.toLocaleString()} to {endItem.toLocaleString()} of {totalCount.toLocaleString()} results
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-slate-400">Show</span>
          <Select
            value={pageSize.toString()}
            onValueChange={handlePageSizeChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-[70px] h-8 bg-slate-800 border-slate-600 text-slate-200">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-600">
              {PAGE_SIZE_OPTIONS.map((size) => (
                <SelectItem 
                  key={size} 
                  value={size.toString()}
                  className="text-slate-200 focus:bg-slate-700"
                >
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-sm text-slate-400">per page</span>
        </div>
      </div>

      {/* Right side: Pagination controls */}
      <div className="flex items-center gap-2">
        {/* First page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(1)}
          disabled={!canGoPrevious}
          className="h-8 w-8 p-0 border-slate-600 bg-slate-800/50 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50"
        >
          <ChevronsLeft className="h-4 w-4" />
          <span className="sr-only">First page</span>
        </Button>

        {/* Previous page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={!canGoPrevious}
          className="h-8 px-3 border-slate-600 bg-slate-800/50 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Previous
        </Button>

        {/* Page numbers */}
        <div className="flex items-center gap-1">
          {visiblePages.map((page, index) => (
            page === 'ellipsis' ? (
              <span
                key={`ellipsis-${index}`}
                className="h-8 w-8 flex items-center justify-center text-slate-400"
              >
                ...
              </span>
            ) : (
              <Button
                key={page}
                variant={page === currentPage ? "default" : "outline"}
                size="sm"
                onClick={() => handlePageChange(page)}
                disabled={isLoading}
                className={cn(
                  "h-8 w-8 p-0",
                  page === currentPage
                    ? "bg-blue-600 text-white hover:bg-blue-700"
                    : "border-slate-600 bg-slate-800/50 text-slate-300 hover:bg-slate-700 hover:text-white"
                )}
              >
                {page}
              </Button>
            )
          ))}
        </div>

        {/* Next page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={!canGoNext}
          className="h-8 px-3 border-slate-600 bg-slate-800/50 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50"
        >
          Next
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>

        {/* Last page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(totalPages)}
          disabled={!canGoNext}
          className="h-8 w-8 p-0 border-slate-600 bg-slate-800/50 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50"
        >
          <ChevronsRight className="h-4 w-4" />
          <span className="sr-only">Last page</span>
        </Button>

        {/* Loading indicator */}
        {isLoading && (
          <div className="flex items-center ml-2">
            <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
          </div>
        )}
      </div>
    </div>
  )
}