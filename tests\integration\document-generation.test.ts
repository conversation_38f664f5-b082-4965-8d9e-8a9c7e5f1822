/**
 * Document Generation Integration Tests
 * Story 5.2: Automated Document Generation Engine
 * 
 * Tests the complete document generation workflow including:
 * - Service integration with Supabase
 * - Template processing and PDF generation
 * - File storage and document record creation
 * - Error handling and edge cases
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createClient } from '@supabase/supabase-js'
import { DocumentGenerationService } from '@/lib/services/document-generation-service'
import { PDFGenerationService } from '@/lib/services/pdf-generation-service'
import type { DocumentGenerationRequest } from '@/types/document-generation'
import type { DocumentTemplate } from '@/types/document-template'

// Mock Supabase client
vi.mock('@supabase/supabase-js')
vi.mock('@/lib/supabase/client')

// Mock PDF generation service
vi.mock('@/lib/services/pdf-generation-service')

// Test data
const mockShipmentData = {
  id: 'shipment-123',
  shipment_number: 'EXSEA-BKK-240101-001',
  invoice_number: 'INV-2024-001',
  status: 'booking_confirmed',
  transportation_mode: 'sea',
  vessel_name: '<PERSON> <PERSON>',
  voyage_number: 'V001',
  etd_date: '2024-01-15T10:00:00Z',
  eta_date: '2024-02-01T14:00:00Z',
  customer: {
    id: 'customer-1',
    name: 'ABC Trading Co., Ltd.',
    type: 'customer',
    address: '123 Business Street, Bangkok, Thailand',
    phone: '+66-2-123-4567',
    email: '<EMAIL>'
  },
  shipper: {
    id: 'shipper-1',
    name: 'XYZ Exports Ltd.',
    type: 'shipper',
    address: '456 Export Ave, Bangkok, Thailand'
  },
  consignee: {
    id: 'consignee-1',
    name: 'Import Partners LLC',
    type: 'consignee',
    address: '789 Import Blvd, Los Angeles, USA'
  },
  origin_port: {
    id: 'port-1',
    port_name: 'Bangkok Port',
    port_code: 'BKK',
    country: 'Thailand'
  },
  destination_port: {
    id: 'port-2',
    port_name: 'Los Angeles Port',
    port_code: 'LAX',
    country: 'United States'
  },
  shipment_products: [
    {
      id: 'product-1',
      product_description: 'Fresh Mangoes',
      quantity: 1000,
      unit_of_measure: {
        name: 'Kilogram',
        abbreviation: 'kg'
      },
      unit_price_cif: 2.50,
      unit_price_fob: 2.20,
      total_value_cif: 2500.00,
      total_value_fob: 2200.00,
      gross_weight: 1200,
      net_weight: 1000,
      packaging_type: 'carton'
    }
  ],
  containers: [
    {
      id: 'container-1',
      container_number: 'ABCD1234567',
      container_type: 'dry',
      container_size: '20ft',
      gross_weight: 22000,
      volume: 33.2,
      status: 'loaded'
    }
  ],
  total_weight: 22000,
  total_volume: 33.2,
  total_value_cif: 2500.00,
  total_value_fob: 2200.00,
  currency_code: 'USD',
  created_at: '2024-01-01T08:00:00Z',
  updated_at: '2024-01-01T08:00:00Z'
}

const mockTemplate: DocumentTemplate = {
  id: 'template-1',
  template_name: 'Booking Confirmation Template',
  document_type: 'booking_confirmation',
  version: '1.0',
  template_content: `
    <div class="document">
      <h1>Booking Confirmation</h1>
      <div class="header-info">
        <p><strong>Shipment Number:</strong> {{shipment.shipment_number}}</p>
        <p><strong>Customer:</strong> {{shipment.customer.name}}</p>
        <p><strong>Shipper:</strong> {{shipment.shipper.name}}</p>
        <p><strong>Consignee:</strong> {{shipment.consignee.name}}</p>
      </div>
      
      <div class="shipping-details">
        <h2>Shipping Details</h2>
        <p><strong>Vessel:</strong> {{shipment.vessel_name}} / {{shipment.voyage_number}}</p>
        <p><strong>Origin:</strong> {{shipment.origin_port.port_name}} ({{shipment.origin_port.port_code}})</p>
        <p><strong>Destination:</strong> {{shipment.destination_port.port_name}} ({{shipment.destination_port.port_code}})</p>
        <p><strong>ETD:</strong> {{shipment.etd_date}}</p>
        <p><strong>ETA:</strong> {{shipment.eta_date}}</p>
      </div>
      
      <div class="cargo-details">
        <h2>Cargo Information</h2>
        <table>
          <thead>
            <tr>
              <th>Description</th>
              <th>Quantity</th>
              <th>Unit</th>
              <th>Gross Weight</th>
              <th>Net Weight</th>
            </tr>
          </thead>
          <tbody>
            {{#each shipment.products}}
            <tr>
              <td>{{product_description}}</td>
              <td>{{quantity}}</td>
              <td>{{unit_of_measure.abbreviation}}</td>
              <td>{{gross_weight}}</td>
              <td>{{net_weight}}</td>
            </tr>
            {{/each}}
          </tbody>
        </table>
      </div>
      
      <div class="totals">
        <p><strong>Total Weight:</strong> {{shipment.total_weight}} kg</p>
        <p><strong>Total Volume:</strong> {{shipment.total_volume}} CBM</p>
        <p><strong>Total Value (CIF):</strong> {{shipment.currency_code}} {{shipment.total_value_cif}}</p>
      </div>
    </div>
  `,
  template_data: {},
  template_styles: `
    .document { font-family: Arial, sans-serif; }
    .header-info { margin-bottom: 20px; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
  `,
  page_size: 'A4',
  page_orientation: 'portrait',
  margin_top: 20,
  margin_bottom: 20,
  margin_left: 20,
  margin_right: 20,
  language: 'en',
  currency_format: 'USD',
  date_format: 'YYYY-MM-DD',
  number_format: 'en-US',
  description: 'Standard booking confirmation template',
  usage_notes: null,
  required_fields: ['shipment_number', 'customer.name'],
  is_active: true,
  is_default: true,
  created_by: 'user-1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

describe('Document Generation Integration', () => {
  let documentService: DocumentGenerationService
  let mockSupabase: any

  beforeEach(() => {
    // Setup mock Supabase client
    mockSupabase = {
      from: vi.fn((table) => {
        if (table === 'shipments') {
          return {
            select: vi.fn(() => ({
              eq: vi.fn(() => ({
                single: vi.fn().mockResolvedValue({
                  data: mockShipmentData,
                  error: null
                })
              }))
            }))
          }
        }
        
        if (table === 'document_templates') {
          return {
            select: vi.fn(() => ({
              eq: vi.fn(() => ({
                single: vi.fn().mockResolvedValue({
                  data: mockTemplate,
                  error: null
                })
              }))
            }))
          }
        }
        
        if (table === 'documents') {
          return {
            insert: vi.fn(() => ({
              select: vi.fn(() => ({
                single: vi.fn().mockResolvedValue({
                  data: {
                    id: 'doc-1',
                    shipment_id: 'shipment-123',
                    document_type: 'booking_confirmation',
                    document_name: 'Booking Confirmation Template',
                    file_path: 'documents/EXSEA-BKK-240101-001/booking_confirmation/booking_confirmation-EXSEA-BKK-240101-001-v1.pdf',
                    file_name: 'booking_confirmation-EXSEA-BKK-240101-001-v1.pdf',
                    uploaded_by: 'user-1',
                    created_at: '2024-01-01T08:00:00Z'
                  },
                  error: null
                })
              }))
            }))
          }
        }
        
        return {
          select: vi.fn(() => ({ eq: vi.fn() })),
          insert: vi.fn(() => ({ select: vi.fn() }))
        }
      }),
      storage: {
        from: vi.fn(() => ({
          upload: vi.fn().mockResolvedValue({
            data: { path: 'documents/test.pdf' },
            error: null
          }),
          getPublicUrl: vi.fn(() => ({
            data: { publicUrl: 'https://example.com/documents/test.pdf' }
          }))
        }))
      },
      auth: {
        getUser: vi.fn().mockResolvedValue({
          data: { user: { id: 'user-1' } }
        })
      }
    }

    // Setup mock PDF service
    const mockPDFService = vi.mocked(PDFGenerationService)
    mockPDFService.mockImplementation(() => ({
      validateRequirements: vi.fn(() => ({ isValid: true, errors: [] })),
      generatePDF: vi.fn().mockResolvedValue(Buffer.from('mock pdf content')),
      generatePDFWithOptions: vi.fn().mockResolvedValue(Buffer.from('mock pdf content')),
      addLogo: vi.fn(),
      addSignature: vi.fn()
    }) as any)

    documentService = new DocumentGenerationService(mockSupabase)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Complete Document Generation Workflow', () => {
    it('should successfully complete the entire document generation process', async () => {
      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1',
        options: {
          documentNumber: 'DOC-001',
          additionalData: { notes: 'Integration test document' }
        }
      }

      const result = await documentService.generateDocument(request)

      // Verify successful generation
      expect(result.success).toBe(true)
      expect(result.document).toBeDefined()
      expect(result.templateId).toBe('template-1')
      expect(result.processingTime).toBeGreaterThan(0)

      // Verify Supabase interactions
      expect(mockSupabase.from).toHaveBeenCalledWith('shipments')
      expect(mockSupabase.from).toHaveBeenCalledWith('document_templates')
      expect(mockSupabase.from).toHaveBeenCalledWith('documents')
      expect(mockSupabase.storage.from).toHaveBeenCalledWith('documents')
    })

    it('should handle template placeholder population correctly', async () => {
      const mockPDFService = vi.mocked(PDFGenerationService)
      let processedContent = ''
      
      const mockPDFInstance = {
        validateRequirements: vi.fn(() => ({ isValid: true, errors: [] })),
        generatePDF: vi.fn((content: string) => {
          processedContent = content
          return Promise.resolve(Buffer.from('mock pdf content'))
        })
      }
      
      mockPDFService.mockImplementation(() => mockPDFInstance as any)

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1'
      }

      const result = await documentService.generateDocument(request)

      expect(result.success).toBe(true)

      // Verify that placeholders were populated correctly
      expect(processedContent).toContain('EXSEA-BKK-240101-001') // shipment_number
      expect(processedContent).toContain('ABC Trading Co., Ltd.') // customer.name
      expect(processedContent).toContain('XYZ Exports Ltd.') // shipper.name
      expect(processedContent).toContain('Ever Given') // vessel_name
      expect(processedContent).toContain('Bangkok Port') // origin_port.port_name
      expect(processedContent).toContain('22000') // total_weight
      expect(processedContent).toContain('USD') // currency_code
    })

    it('should create proper file storage structure', async () => {
      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1'
      }

      const result = await documentService.generateDocument(request)

      expect(result.success).toBe(true)

      // Verify storage upload was called with correct parameters
      const storageFromCall = mockSupabase.storage.from
      const uploadCall = mockSupabase.storage.from().upload

      expect(storageFromCall).toHaveBeenCalledWith('documents')
      expect(uploadCall).toHaveBeenCalledWith(
        expect.stringContaining('documents/EXSEA-BKK-240101-001/booking_confirmation/'),
        expect.any(Buffer),
        expect.objectContaining({
          contentType: 'application/pdf',
          upsert: false
        })
      )
    })

    it('should create document record with correct metadata', async () => {
      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1',
        options: {
          documentNumber: 'DOC-001'
        }
      }

      const result = await documentService.generateDocument(request)

      expect(result.success).toBe(true)

      // Verify document insertion
      const insertCall = mockSupabase.from('documents').insert
      expect(insertCall).toHaveBeenCalledWith(
        expect.objectContaining({
          shipment_id: 'shipment-123',
          document_type: 'booking_confirmation',
          document_name: 'Booking Confirmation Template',
          document_number: 'DOC-001',
          file_type: 'application/pdf',
          version: 1,
          is_original: true,
          access_level: 'shipment',
          uploaded_by: 'user-1',
          upload_source: 'automated_generation'
        })
      )
    })
  })

  describe('Bulk Generation Workflow', () => {
    it('should successfully generate multiple documents', async () => {
      const request = {
        shipmentId: 'shipment-123',
        templateIds: ['template-1', 'template-2']
      }

      // Setup second template
      const secondTemplate = { ...mockTemplate, id: 'template-2', document_type: 'invoice_fob' as const }
      
      mockSupabase.from = vi.fn((table) => {
        if (table === 'document_templates') {
          return {
            select: vi.fn(() => ({
              eq: vi.fn((id) => ({
                single: vi.fn().mockImplementation(() => {
                  if (id === 'template-1') {
                    return Promise.resolve({ data: mockTemplate, error: null })
                  } else if (id === 'template-2') {
                    return Promise.resolve({ data: secondTemplate, error: null })
                  }
                  return Promise.resolve({ data: null, error: { message: 'Not found' } })
                })
              }))
            }))
          }
        }
        return {
          select: vi.fn(() => ({ 
            eq: vi.fn(() => ({ 
              single: vi.fn().mockResolvedValue({ data: mockShipmentData, error: null })
            }))
          })),
          insert: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn().mockResolvedValue({
                data: { id: 'doc-1', document_name: 'Test Document' },
                error: null
              })
            }))
          }))
        }
      })

      const result = await documentService.bulkGenerateDocuments(request)

      expect(result.summary.total).toBe(2)
      expect(result.summary.successful).toBe(2)
      expect(result.summary.failed).toBe(0)
      expect(result.results).toHaveLength(2)
      expect(result.results[0].success).toBe(true)
      expect(result.results[1].success).toBe(true)
    })

    it('should handle mixed success/failure in bulk generation', async () => {
      const request = {
        shipmentId: 'shipment-123',
        templateIds: ['template-1', 'invalid-template']
      }

      // Setup template queries to return success for first, error for second
      mockSupabase.from = vi.fn((table) => {
        if (table === 'document_templates') {
          return {
            select: vi.fn(() => ({
              eq: vi.fn((id) => ({
                single: vi.fn().mockImplementation(() => {
                  if (id === 'template-1') {
                    return Promise.resolve({ data: mockTemplate, error: null })
                  }
                  return Promise.resolve({ data: null, error: { message: 'Template not found' } })
                })
              }))
            }))
          }
        }
        return {
          select: vi.fn(() => ({ 
            eq: vi.fn(() => ({ 
              single: vi.fn().mockResolvedValue({ data: mockShipmentData, error: null })
            }))
          })),
          insert: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn().mockResolvedValue({
                data: { id: 'doc-1', document_name: 'Test Document' },
                error: null
              })
            }))
          }))
        }
      })

      const result = await documentService.bulkGenerateDocuments(request)

      expect(result.summary.total).toBe(2)
      expect(result.summary.successful).toBe(1)
      expect(result.summary.failed).toBe(1)
      expect(result.results[0].success).toBe(true)
      expect(result.results[1].success).toBe(false)
      expect(result.results[1].error).toContain('Template not found')
    })
  })

  describe('Error Handling Integration', () => {
    it('should handle database connection errors gracefully', async () => {
      mockSupabase.from = vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn().mockRejectedValue(new Error('Database connection failed'))
          }))
        }))
      }))

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1'
      }

      const result = await documentService.generateDocument(request)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Database connection failed')
    })

    it('should handle PDF generation failures', async () => {
      const mockPDFService = vi.mocked(PDFGenerationService)
      mockPDFService.mockImplementation(() => ({
        validateRequirements: vi.fn(() => ({ isValid: true, errors: [] })),
        generatePDF: vi.fn().mockRejectedValue(new Error('PDF generation failed')),
        generatePDFWithOptions: vi.fn(),
        addLogo: vi.fn(),
        addSignature: vi.fn()
      }) as any)

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1'
      }

      const result = await documentService.generateDocument(request)

      expect(result.success).toBe(false)
      expect(result.error).toContain('PDF generation failed')
    })

    it('should handle storage upload failures', async () => {
      mockSupabase.storage.from = vi.fn(() => ({
        upload: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Storage quota exceeded' }
        }),
        getPublicUrl: vi.fn(() => ({
          data: { publicUrl: 'https://example.com/file.pdf' }
        }))
      }))

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1'
      }

      const result = await documentService.generateDocument(request)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Failed to store document')
      expect(result.error).toContain('Storage quota exceeded')
    })

    it('should handle document record creation failures', async () => {
      mockSupabase.from = vi.fn((table) => {
        if (table === 'documents') {
          return {
            insert: vi.fn(() => ({
              select: vi.fn(() => ({
                single: vi.fn().mockResolvedValue({
                  data: null,
                  error: { message: 'Unique constraint violation' }
                })
              }))
            }))
          }
        }
        
        // Return success for other tables
        return {
          select: vi.fn(() => ({
            eq: vi.fn(() => ({
              single: vi.fn().mockResolvedValue({
                data: table === 'shipments' ? mockShipmentData : mockTemplate,
                error: null
              })
            }))
          }))
        }
      })

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1'
      }

      const result = await documentService.generateDocument(request)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Failed to create document record')
    })
  })

  describe('Performance and Scalability', () => {
    it('should complete generation within reasonable time limits', async () => {
      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1'
      }

      const startTime = Date.now()
      const result = await documentService.generateDocument(request)
      const endTime = Date.now()

      expect(result.success).toBe(true)
      expect(endTime - startTime).toBeLessThan(5000) // Should complete within 5 seconds
    })

    it('should handle large document content efficiently', async () => {
      // Create a template with large content
      const largeTemplate = {
        ...mockTemplate,
        template_content: `
          <div class="document">
            <h1>Large Document</h1>
            ${Array.from({ length: 1000 }, (_, i) => `
              <p>This is paragraph ${i + 1} with shipment data: {{shipment.shipment_number}}</p>
            `).join('')}
          </div>
        `
      }

      mockSupabase.from = vi.fn((table) => {
        if (table === 'document_templates') {
          return {
            select: vi.fn(() => ({
              eq: vi.fn(() => ({
                single: vi.fn().mockResolvedValue({
                  data: largeTemplate,
                  error: null
                })
              }))
            }))
          }
        }
        return {
          select: vi.fn(() => ({ 
            eq: vi.fn(() => ({ 
              single: vi.fn().mockResolvedValue({ data: mockShipmentData, error: null })
            }))
          })),
          insert: vi.fn(() => ({
            select: vi.fn(() => ({
              single: vi.fn().mockResolvedValue({
                data: { id: 'doc-1', document_name: 'Large Document' },
                error: null
              })
            }))
          }))
        }
      })

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-123',
        templateId: 'template-1'
      }

      const result = await documentService.generateDocument(request)

      expect(result.success).toBe(true)
      expect(result.processingTime).toBeGreaterThan(0)
    })
  })
})