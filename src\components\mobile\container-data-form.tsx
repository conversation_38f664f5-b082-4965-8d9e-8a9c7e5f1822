'use client'

import { useState, useEffect } from 'react'
import { ArrowR<PERSON>, CheckCircle, X, AlertTriangle, Save } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ContainerNumberInput } from './container-number-input'
import { SealNumberInput } from './seal-number-input'
import { OfflineIndicator } from './offline-indicator'
import { useContainerData } from '@/hooks/use-container-data'
import { useLanguage } from '@/hooks/use-language'
import type { Container } from '@/types/container'

interface ContainerDataFormProps {
  containerId: string
  shipmentId: string
  onComplete?: (container: Container) => void
  onCancel?: () => void
  disabled?: boolean
  className?: string
}

type FormStep = 'container_number' | 'seal_number' | 'review'

export function ContainerDataForm({
  containerId,
  shipmentId,
  onComplete,
  onCancel,
  disabled = false,
  className = ''
}: ContainerDataFormProps) {
  const [currentStep, setCurrentStep] = useState<FormStep>('container_number')
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [initialStepSet, setInitialStepSet] = useState(false)
  const { t } = useLanguage()

  const {
    // Data
    container,
    formData,
    validation,
    
    // State flags
    isLoading,
    isSubmitting,
    error,
    success,
    isOnline,
    isFormValid,
    
    // Actions
    updateFormData,
    submitContainerData,
    resetSuccess,
    resetError,
    
    // Helper functions
    getValidationErrors
  } = useContainerData({
    containerId,
    shipmentId,
    onSuccess: onComplete,
    onError: (error) => console.error('Container data error:', error)
  })

  // Set initial step based on confirmation status when container loads
  useEffect(() => {
    if (container && !initialStepSet && !isLoading) {
      const containerConfirmed = container.container_number_confirmed || false
      const sealConfirmed = container.seal_number_confirmed || false

      if (containerConfirmed && !sealConfirmed) {
        // Container confirmed, seal not confirmed -> skip to seal step
        setCurrentStep('seal_number')
      } else if (!containerConfirmed && sealConfirmed) {
        // Seal confirmed, container not confirmed -> start with container step
        setCurrentStep('container_number')
      } else if (containerConfirmed && sealConfirmed) {
        // Both confirmed -> go to review
        setCurrentStep('review')
      } else {
        // Neither confirmed -> start with container step (default)
        setCurrentStep('container_number')
      }

      setInitialStepSet(true)
    }
  }, [container, initialStepSet, isLoading])

  // Reset success state after delay
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        resetSuccess()
        setCurrentStep('container_number')
        setShowConfirmation(false)
        setInitialStepSet(false)
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [success, resetSuccess])

  // Removed the validation trigger useEffect as it's now handled in the hook itself

  // Get current step validation (accounting for confirmed fields)
  const isCurrentStepValid = (): boolean => {
    const containerConfirmed = container?.container_number_confirmed || false
    const sealConfirmed = container?.seal_number_confirmed || false

    switch (currentStep) {
      case 'container_number':
        if (containerConfirmed) return true // Skip validation for confirmed fields
        const hasContainerNumber = formData.container_number.trim() !== ''
        const containerIsValid = validation.container_number?.isValid === true
        const uniquenessChecked = validation.uniqueness_checked || validation.is_validating_uniqueness
        return hasContainerNumber && containerIsValid && uniquenessChecked
      case 'seal_number':
        if (sealConfirmed) return true // Skip validation for confirmed fields
        const hasSealNumber = formData.seal_number.trim() !== ''
        const sealIsValid = validation.seal_number?.isValid === true
        return hasSealNumber && sealIsValid
      case 'review':
        // For review step, validate only non-confirmed fields
        let reviewValid = true

        if (!containerConfirmed) {
          const hasContainerNumber = formData.container_number.trim() !== ''
          const containerIsValid = validation.container_number?.isValid === true
          const uniquenessChecked = validation.uniqueness_checked || validation.is_validating_uniqueness
          reviewValid = reviewValid && hasContainerNumber && containerIsValid && uniquenessChecked
        }

        if (!sealConfirmed) {
          const hasSealNumber = formData.seal_number.trim() !== ''
          const sealIsValid = validation.seal_number?.isValid === true
          reviewValid = reviewValid && hasSealNumber && sealIsValid
        }

        return reviewValid
      default:
        return false
    }
  }

  // Get step progress (accounting for skipped steps)
  const getStepProgress = (): number => {
    const containerConfirmed = container?.container_number_confirmed || false
    const sealConfirmed = container?.seal_number_confirmed || false

    // Calculate total available steps based on confirmation status
    const availableSteps = []

    if (!containerConfirmed) availableSteps.push('container_number')
    if (!sealConfirmed) availableSteps.push('seal_number')
    availableSteps.push('review') // Review step is always available

    const currentIndex = availableSteps.indexOf(currentStep)
    if (currentIndex === -1) {
      // If current step is not in available steps, it means it's confirmed
      // Show as completed
      return 100
    }

    return ((currentIndex + 1) / availableSteps.length) * 100
  }

  // Get step title
  const getStepTitle = (step: FormStep): string => {
    switch (step) {
      case 'container_number':
        return t('containerForm.containerNumber')
      case 'seal_number':
        return t('containerForm.sealNumber')
      case 'review':
        return t('containerForm.reviewSave')
      default:
        return ''
    }
  }

  // Navigate to next step (skipping confirmed steps)
  const handleNextStep = () => {
    if (!isCurrentStepValid()) return

    const containerConfirmed = container?.container_number_confirmed || false
    const sealConfirmed = container?.seal_number_confirmed || false

    if (currentStep === 'container_number') {
      if (sealConfirmed) {
        // Seal is confirmed, skip seal step and go to review
        setCurrentStep('review')
      } else {
        // Seal not confirmed, go to seal step
        setCurrentStep('seal_number')
      }
    } else if (currentStep === 'seal_number') {
      // From seal step, always go to review
      setCurrentStep('review')
    } else {
      // From review step, show confirmation
      setShowConfirmation(true)
    }
  }

  // Navigate to previous step (skipping confirmed steps)
  const handlePreviousStep = () => {
    const containerConfirmed = container?.container_number_confirmed || false
    const sealConfirmed = container?.seal_number_confirmed || false

    if (currentStep === 'review') {
      if (sealConfirmed && !containerConfirmed) {
        // Seal confirmed, container not confirmed -> go to container step
        setCurrentStep('container_number')
      } else if (!sealConfirmed) {
        // Seal not confirmed -> go to seal step
        setCurrentStep('seal_number')
      } else {
        // Both confirmed or container confirmed -> shouldn't reach here, but fallback to container
        setCurrentStep('container_number')
      }
    } else if (currentStep === 'seal_number') {
      if (!containerConfirmed) {
        // Container not confirmed -> go to container step
        setCurrentStep('container_number')
      }
      // If container is confirmed, we shouldn't be able to go back from seal step
    }
    // If we're on container_number step, there's no previous step
  }

  // Handle form submission (only submit non-confirmed fields)
  const handleSubmit = async () => {
    console.log('handleSubmit called')

    if (isSubmitting) {
      console.log('Already submitting, returning early')
      return
    }

    try {
      console.log('Current success state:', success)
      console.log('Current isFormValid state:', isFormValid)

      // Just call the existing submit function - the filtering should happen in the backend
      console.log('About to call submitContainerData')

      await submitContainerData()

      console.log('submitContainerData completed, success state:', success)
    } catch (error) {
      console.error('Error in handleSubmit:', error)
    }
  }

  // Show success state
  if (success) {
    return (
      <div className={`${className} space-y-6`}>
        <div className="bg-green-900/20 rounded-lg border border-green-500/30 p-6 text-center">
          <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-white mb-2">
            {t('containerForm.dataUpdated')}
          </h3>
          <p className="text-green-300 text-sm mb-4">
            {t('containerForm.successMessage')}
          </p>
          <div className="bg-green-900/30 rounded-lg p-3">
            <div className="text-sm text-green-200 space-y-1">
              <div><strong>{t('containerForm.containerLabel')}</strong> {formData.container_number}</div>
              <div><strong>{t('containerForm.sealLabel')}</strong> {formData.seal_number}</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`${className} space-y-6`}>
      {/* Offline Indicator */}
      {!isOnline && <OfflineIndicator />}

      {/* Progress Header */}
      <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-medium text-white">
            {t('containerForm.title')}
          </h2>
          <span className="text-sm text-slate-400">
            {getStepTitle(currentStep)}
          </span>
        </div>
        
        <Progress value={getStepProgress()} className="h-2 mb-2" />
        
        <div className="text-xs text-slate-500">
          {(() => {
            const containerConfirmed = container?.container_number_confirmed || false
            const sealConfirmed = container?.seal_number_confirmed || false

            // Calculate available steps and current position
            const availableSteps = []
            if (!containerConfirmed) availableSteps.push('container_number')
            if (!sealConfirmed) availableSteps.push('seal_number')
            availableSteps.push('review')

            const currentIndex = availableSteps.indexOf(currentStep)
            const currentStepNum = currentIndex + 1
            const totalSteps = availableSteps.length

            return `${t('containerForm.step')} ${currentStepNum} ${t('containerForm.of')} ${totalSteps}`
          })()}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Alert className="border-red-500/30 bg-red-500/10">
          <AlertTriangle className="h-4 w-4 text-red-400" />
          <AlertDescription className="text-red-300">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Step Content */}
      <div className="space-y-6">
        {currentStep === 'container_number' && (
          <ContainerNumberInput
            value={formData.container_number}
            onChange={(value) => updateFormData({ container_number: value })}
            onValidation={() => {}} // Handled by hook
            validation={validation.container_number}
            isValidatingUniqueness={validation.is_validating_uniqueness}
            disabled={disabled || isSubmitting || isLoading}
            enableScanning={true}
          />
        )}

        {currentStep === 'seal_number' && (
          <SealNumberInput
            value={formData.seal_number}
            onChange={(value) => updateFormData({ seal_number: value })}
            onValidation={() => {}} // Handled by hook
            validation={validation.seal_number}
            disabled={disabled || isSubmitting || isLoading}
            enableScanning={true}
          />
        )}

        {currentStep === 'review' && (
          <div className="space-y-4">
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
              <h3 className="text-sm font-medium text-slate-300 mb-4">
                {t('containerForm.reviewInfo')}
              </h3>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-slate-700 rounded">
                  <span className="text-slate-400">{t('containerForm.containerNumberLabel')}</span>
                  <div className="text-right">
                    <div className="text-white font-mono text-lg">
                      {formData.container_number}
                    </div>
                    {validation.container_number?.formattedNumber && (
                      <div className="text-xs text-slate-400">
                        {validation.container_number.formattedNumber}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-between items-center p-3 bg-slate-700 rounded">
                  <span className="text-slate-400">{t('containerForm.sealNumberLabel')}</span>
                  <div className="text-right">
                    <div className="text-white font-mono text-lg">
                      {formData.seal_number}
                    </div>
                    {validation.seal_number?.detectedFormat && (
                      <div className="text-xs text-slate-400">
                        {validation.seal_number.detectedFormat} {t('containerForm.format')}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Validation Summary */}
            <div className="bg-green-900/20 rounded-lg border border-green-500/30 p-4">
              <div className="flex items-center mb-2">
                <CheckCircle className="w-4 h-4 text-green-400 mr-2" />
                <span className="text-sm text-green-300">{t('containerForm.readyToSave')}</span>
              </div>
              <div className="text-xs text-green-400">
                {t('containerForm.validatedReady')}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="flex space-x-3">
        {(() => {
          const containerConfirmed = container?.container_number_confirmed || false
          const sealConfirmed = container?.seal_number_confirmed || false

          // Show previous button based on smart logic
          const showPrevious = (currentStep === 'seal_number' && !containerConfirmed) ||
                               (currentStep === 'review' && (!containerConfirmed || !sealConfirmed))

          return showPrevious && (
            <Button
              onClick={handlePreviousStep}
              disabled={disabled || isSubmitting || isLoading}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              {t('containerForm.previous')}
            </Button>
          )
        })()}
        
        {onCancel && (
          <Button
            onClick={onCancel}
            disabled={isSubmitting || isLoading}
            variant="outline"
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <X className="w-4 h-4 mr-2" />
            {t('containerForm.cancel')}
          </Button>
        )}
        
        <Button
          onClick={currentStep === 'review' ? handleSubmit : handleNextStep}
          disabled={disabled || isSubmitting || isLoading || !isCurrentStepValid()}
          className="flex-1 bg-orange-600 hover:bg-orange-700 text-white min-h-[44px]"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              {t('containerForm.saving')}
            </>
          ) : currentStep === 'review' ? (
            <>
              <Save className="w-4 h-4 mr-2" />
              {t('containerForm.saveContainerData')}
            </>
          ) : (
            <>
              {t('containerForm.next')}
              <ArrowRight className="w-4 h-4 ml-2" />
            </>
          )}
        </Button>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle>{t('containerForm.confirmTitle')}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-slate-300">
              {t('containerForm.confirmMessage')}
            </p>
            
            <div className="bg-slate-700 rounded p-3 space-y-2">
              <div><strong>{t('containerForm.containerLabel')}</strong> {formData.container_number}</div>
              <div><strong>{t('containerForm.sealLabel')}</strong> {formData.seal_number}</div>
            </div>
            
            <div className="flex space-x-3">
              <Button
                onClick={() => setShowConfirmation(false)}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                {t('containerForm.cancel')}
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
              >
                {isSubmitting ? t('containerForm.saving') : t('containerForm.confirmSave')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}