# DYY Trading Management Product Requirements Document (PRD)

## Goals and Background Context

### Goals
- Streamline fruit export operations from manual to digital processes
- Reduce coordination time by 50% through automated workflows
- Achieve 90% real-time status visibility across all shipments
- Improve document accuracy to 95% through automated generation
- Enable mobile-first field operations for drivers and stakeholders
- Establish comprehensive audit trail compliance (100%)
- Create intelligent relationship management for streamlined shipment creation
- Achieve 60% reduction in data entry through pre-population workflows

### Background Context

DYY TRADING INTL Co.,Ltd currently manages complex fruit export operations manually across multiple stakeholders including customers, factories, carriers, and shipping agents. This manual process creates communication gaps, status tracking difficulties, and documentation errors. The company needs a modern, integrated platform that supports sea, land, and rail transportation with real-time coordination.

The system will serve internal operations staff (Admin, CS, Account) as primary users while providing specialized interfaces for external stakeholders (customers, carriers, drivers, factory personnel). The solution leverages Next.js and Supabase for modern web performance with mobile accessibility for field operations.

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2024-12-XX | 1.0 | Initial PRD creation from existing specifications | Product Manager |
| 2024-12-XX | 1.1 | Added Transportation Mode pre-selection, mandatory Factory/Forwarder Agent fields, ETD/ETA/Closing Time requirements, mandatory Destination Port, automatic shipment number generation, and enhanced master data structure | Product Manager |

## Requirements

### Functional

**FR1:** The system shall provide role-based authentication with support for Admin, CS, Account, Customer, Carrier, Driver, Factory, Shipper, Consignee, Notify Party, and Forwarder Agent user types.

**FR2:** The system shall enable CRUD operations for all master data entities including Products, Ports, Units of Measure, Companies (Customers, Carriers, Factories, Shippers, Consignees, Notify Parties, Forwarder Agents), and Drivers with appropriate role-based permissions.

**FR3:** The system shall manage Customer-Shipper relationships with default preferences and active/inactive status to streamline shipment creation.

**FR4:** The system shall manage Customer-Product relationships with pricing (CIF/FOB per KG), packaging specifications, and default product selection.

**FR5:** The system shall manage Consignee-Notify Party relationships with notification preferences and priority ordering.

**FR6:** The system shall create shipments with intelligent pre-population based on customer selection (auto-load associated shippers, products, and notify parties with defaults pre-selected).

**FR7:** The system shall support complete shipment lifecycle management from booking to delivery with status tracking across multiple transportation modes (sea, land, rail).

**FR8:** The system shall provide mobile-optimized driver interface for status updates with mandatory photo uploads and GPS location capture.

**FR9:** The system shall generate export documents (Invoice/Packing List, Commercial Contract, Shipping Instruction) with automated field population from shipment data.

**FR10:** The system shall provide multi-channel notification system supporting email, SMS, in-app, Line, and WeChat communications.

**FR11:** The system shall maintain comprehensive audit trail for all user actions and data modifications.

**FR12:** The system shall support container and shipment product management with precise weight calculations and packaging-type quantity management.

**FR13:** The system shall require Transportation Mode selection (Sea, Land, Rail) before shipment creation to configure appropriate workflow fields and requirements.

**FR14:** The system shall require Factory selection during shipment creation with factory location and capacity information display.

**FR15:** The system shall require Forwarder Agent selection during shipment creation with agent contact information and service details.

**FR16:** The system shall require ETD (Estimated Time of Departure), ETA (Estimated Time of Arrival), and Closing Time entry during shipment creation with date/time validation ensuring logical sequence (Closing Time < ETD < ETA).

**FR17:** The system shall require Destination Port selection during shipment creation for routing and documentation purposes, and require Origin Port with customer history suggestions.

**FR18:** The system shall provide optional Notes field during shipment creation for additional instructions visible to all stakeholders.

**FR19:** The system shall automatically generate unique shipment numbers upon save using format EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running] where Transportation Mode Code: 1=Land, 2=Rail, 3=Sea, and running number resets monthly per mode/port combination.

**FR20:** The system shall require Booking Confirmation Document upload during shipment creation with document validation, preview, and secure storage capabilities.

### Non Functional

**NFR1:** The system shall achieve sub-2 second page load times using Next.js optimization and Supabase infrastructure.

**NFR2:** The system shall maintain 99.9% uptime leveraging Supabase's managed infrastructure.

**NFR3:** The system shall support responsive design with mobile-first approach, optimized for field operations on mobile devices.

**NFR4:** The system shall implement dark blue theme (#1e293b, #0f172a, #334155) with orange accents (#f97316) optimized for logistics operations.

**NFR5:** The system shall comply with WCAG 2.1 AA accessibility standards minimum.

**NFR6:** The system shall support bilingual interface (Thai/English) with localized document generation.

**NFR7:** The system shall implement Row Level Security (RLS) policies for fine-grained data access control.

**NFR8:** The system shall support offline capability for mobile driver interface using Progressive Web App (PWA) technology.

**NFR9:** The system shall handle image uploads up to 10MB per photo with automatic compression and thumbnail generation.

**NFR10:** The system shall support real-time status updates using Supabase real-time subscriptions.

## User Interface Design Goals

### Overall UX Vision

Create a modern, logistics-optimized interface with dark blue theme that prioritizes efficiency for export operations staff while providing intuitive mobile experiences for field workers. The design emphasizes rapid data entry through intelligent pre-population, clear visual hierarchy with orange accent highlights, and seamless workflows that reduce manual coordination time by 50%.

### Key Interaction Paradigms

- **Transportation Mode Pre-Selection**: Transportation Mode (Sea/Land/Rail) selection modal before shipment creation configures appropriate workflow fields and requirements
- **Cascading Selection**: Customer selection automatically loads associated shippers and products with defaults pre-selected, Consignee selection automatically loads associated notify parties with defaults pre-selected
- **Mandatory Field Validation**: Factory, Forwarder Agent, ETD/ETA/Closing Time, and Destination Port selections required with real-time validation
- **Intelligent Date/Time Validation**: System enforces logical sequence (Closing Time < ETD < ETA) with immediate feedback
- **Automatic Number Generation**: Shipment numbers auto-generated using transportation mode, port, and date with monthly reset logic
- **Mobile-First Touch Targets**: Minimum 44px touch targets for field operations with large, accessible controls
- **Progressive Disclosure**: Complex shipment data organized into logical sections with expandable details
- **Status-Driven Visual Feedback**: Color-coded status indicators with real-time updates and smooth transitions
- **Context-Aware Navigation**: Role-based menus showing only relevant functions with breadcrumb navigation for complex workflows

### Core Screens and Views

- **Admin Dashboard**: Master data management with comprehensive CRUD interfaces
- **CS Shipment Management**: Primary shipment creation and tracking interface with relationship management
- **Customer Mobile Portal**: Real-time shipment tracking and status visibility
- **Driver Mobile Interface**: Simple status update screens with photo capture and GPS integration
- **Document Generation Center**: Automated document creation and management
- **Master Data Management**: Comprehensive interface with dedicated management for Customer, Factory, Shipper, Consignee, Notify Party, Forwarder Agent, Carrier, Driver, Products, Ports, and relationship configuration
- **Notification Center**: Multi-channel communication management and preferences

### Accessibility: WCAG AA

WCAG 2.1 AA compliance minimum with high contrast ratios (4.5:1), keyboard navigation support, screen reader compatibility, and color-blind friendly palette.

### Branding

Dark blue logistics theme with professional orange accents:
- **Primary**: Dark Blue (#1e293b), Navy Blue (#0f172a), Slate Blue (#334155)
- **Accents**: Orange (#f97316), Light Orange (#fb923c), Amber (#f59e0b)
- **Typography**: Inter primary font, Roboto Mono for tracking numbers/IDs
- **Layout**: 1400px max width for logistics dashboards, 8px border radius standard

### Target Device and Platforms: Web Responsive

Web responsive design with mobile-first approach. Primary devices: Desktop/laptop for CS and Admin operations, mobile phones for drivers and customers, tablets for factory personnel.

## Technical Assumptions

### Repository Structure: Monorepo

Single repository structure to manage the complete Next.js application with Supabase backend integration. This approach supports the integrated nature of the fruit export management system with shared components across different user interfaces.

### Service Architecture

**Next.js Full-Stack Application with Supabase Backend**: Monolithic frontend application built with Next.js App Router, leveraging Supabase as Backend-as-a-Service for authentication, database, storage, and real-time subscriptions. Server-side rendering for optimal performance with client-side interactivity for real-time features.

**Key Architectural Components:**
- **Frontend**: Next.js 14+ with App Router, TypeScript, Tailwind CSS for dark blue theme, ShadCN UI component library
- **Backend**: Supabase PostgreSQL with Row Level Security (RLS) policies
- **Authentication**: Supabase Auth with custom user metadata for role management
- **Real-time**: Supabase real-time subscriptions for status updates
- **Storage**: Supabase Storage for document and image management
- **Notifications**: Supabase Edge Functions for multi-channel communications
- **Mobile**: Progressive Web App (PWA) for offline driver capabilities

### Testing Requirements

**Unit + Integration Testing**: Comprehensive testing strategy including unit tests for business logic, integration tests for database operations and API endpoints, and end-to-end testing for critical user workflows. Manual testing support for mobile PWA functionality and multi-channel notifications.

**Testing Stack:**
- **Unit/Integration**: Jest, React Testing Library for frontend components
- **Database Testing**: Supabase local development environment for data layer testing
- **E2E Testing**: Playwright for cross-browser workflow validation
- **Manual Testing**: Device testing for mobile PWA and photo upload functionality

### Additional Technical Assumptions and Requests

- **Database Design**: Hybrid companies approach with base companies table and separate info tables for complex types (customers, carriers, factories) using JSONB metadata for simple types (shippers, consignees, notify parties, forwarder agents)
- **GPS Coordinates**: Hybrid storage using JSONB address field with dedicated point column for efficient geographic queries
- **Image Processing**: Automatic compression and thumbnail generation for photo uploads with 10MB size limits
- **Offline Support**: Service worker implementation for driver mobile interface with local data synchronization
- **Internationalization**: Built-in Thai/English language support with localized document generation
- **Performance**: Target sub-2 second page load times with Next.js optimization and Supabase edge caching
- **Security**: Comprehensive Row Level Security policies for multi-tenant data access control
- **Document Generation**: Server-side PDF generation using React PDF or similar library
- **Color Theme**: Exact implementation of dark blue color palette (#1e293b, #0f172a, #334155) with orange accents (#f97316)

## Epic List

**Epic 1: Foundation & Authentication Infrastructure**  
Establish Next.js/Supabase project setup, authentication system, and basic user management with role-based access control.

**Epic 2: Master Data Management System**  
Create comprehensive CRUD operations for all master data entities including products, ports, companies (customers, factories, shippers, consignees, notify parties, forwarder agents, carriers), drivers, and relationship management with intelligent pre-population capabilities.

**Epic 3: Core Shipment Management**  
Implement complete shipment lifecycle management from creation through delivery with status tracking and stakeholder coordination.

**Epic 4: Mobile Driver Interface & Status Updates**  
Build mobile-optimized PWA for drivers with status updates, photo uploads, and offline capabilities.

**Epic 5: Document Generation & Management**  
Develop automated document generation system for export documents with centralized storage and distribution.

**Epic 6: Notification & Communication System**  
Implement multi-channel notification system supporting email, SMS, in-app, Line, and WeChat communications.

## Epic 1 Foundation & Authentication Infrastructure

**Epic Goal:** Establish the technical foundation with Next.js/Supabase project setup, implement role-based authentication system supporting all 11 user types, and deliver basic user management capabilities with a functional health check interface to validate the infrastructure is operational and ready for business functionality development.

### Story 1.1 Project Infrastructure Setup

As a Developer,  
I want to establish the Next.js/Supabase project foundation with ShadCN UI,  
so that the development team has a working application infrastructure with proper tooling and deployment pipeline.

#### Acceptance Criteria

**1:** Project is initialized with Next.js 14+ App Router, TypeScript, Tailwind CSS, and ShadCN UI component library configured with dark blue theme colors (#1e293b, #0f172a, #334155, #f97316).

**2:** Supabase project is configured with PostgreSQL database, authentication, storage, and real-time subscriptions enabled.

**3:** Development environment includes ESLint, Prettier, and Jest testing framework with initial test suite.

**4:** CI/CD pipeline is configured for automated testing and deployment to staging environment.

**5:** Application renders a basic health check page confirming all integrations are functional.

### Story 1.2 Database Schema & RLS Foundation

As a System Administrator,  
I want the core database schema established with Row Level Security policies,  
so that data access is properly secured and the foundation is ready for master data operations.

#### Acceptance Criteria

**1:** All master data tables are created (profiles, companies, products, ports, units_of_measure, drivers) with proper relationships and constraints.

**2:** Core business tables are created (shipments, containers, shipment_products, transportation, status_history) with audit trail support.

**3:** Row Level Security (RLS) is enabled on all tables with basic policies for role-based access control.

**4:** Database functions and triggers are implemented for automatic timestamp updates and data consistency.

**5:** Sample reference data is loaded (units of measure, basic port data) to support application testing.

### Story 1.3 Authentication System Implementation

As an Admin,  
I want to manage user accounts with role-based access control,  
so that I can control system access and ensure proper security for all user types.

#### Acceptance Criteria

**1:** Supabase Auth is configured to support all 11 user types (admin, cs, account, customer, carrier, driver, factory, shipper, consignee, notify_party, forwarder_agent).

**2:** User registration and login flows are implemented with email/password authentication.

**3:** Custom user metadata stores role information with proper validation and constraints.

**4:** Role-based navigation displays appropriate menu options based on user permissions.

**5:** Admin interface allows creating, editing, and managing user accounts with role assignment.

### Story 1.4 Basic User Management Interface

As an Admin,  
I want to view and manage user profiles through a web interface,  
so that I can perform day-to-day user administration tasks efficiently.

#### Acceptance Criteria

**1:** User management dashboard displays all users with filtering by role and status.

**2:** Create user form captures all required profile information with role selection and company association.

**3:** Edit user functionality allows updating profile information, role changes, and account activation/deactivation.

**4:** User detail view shows complete profile information, role permissions, and audit trail.

**5:** Interface follows ShadCN UI patterns with dark blue theme and responsive design.

## Epic 2 Master Data Management System

**Epic Goal:** Implement comprehensive CRUD operations for all master data entities (Products, Ports, Units of Measure, Companies including Customers, Factories, Shippers, Consignees, Notify Parties, Forwarder Agents, Carriers, and Drivers) with intelligent relationship management enabling streamlined shipment creation through cascading selection and default pre-population workflows.

### Story 2.1 Products and Units of Measure Management

As a CS representative,  
I want to create and manage products with their units of measure,  
so that I can maintain accurate product catalogs with consistent measurement standards for shipment creation.

#### Acceptance Criteria

**1:** Product management interface allows creating products with name, code, description, category, HS code, and unit of measure selection.

**2:** Units of measure management supports creating measurement units with conversion factors, categories (weight, count, volume, length), and base unit relationships.

**3:** Product list view displays all products with search, filtering by category, and sorting capabilities.

**4:** Edit and delete operations maintain data integrity with validation for products in use by existing shipments.

**5:** Interface follows ShadCN UI patterns with responsive design and proper error handling.

### Story 2.2 Ports and Location Data Management

As an Admin,  
I want to manage port information with location data,  
so that shipping routes can be properly configured for shipment planning.

#### Acceptance Criteria

**1:** Port management interface supports creating ports with code, name, country, port type (origin, destination, transit), and GPS coordinates.

**2:** Location data entry includes address fields with bilingual support (Thai/English) and coordinate capture.

**3:** Port list displays with search by name/code/country and filtering by port type.

**4:** Hybrid GPS coordinate storage automatically syncs between JSONB address field and dedicated point column.

**5:** Geographic search capabilities allow finding ports within specified radius of coordinates.

### Story 2.3 Company Management with Type-Specific Data

As a CS representative,  
I want to manage all company types with their specific information requirements,  
so that I can maintain accurate stakeholder data for shipment coordination.

#### Acceptance Criteria

**1:** Company creation interface dynamically shows appropriate fields based on company type selection (customer, carrier, factory, shipper, consignee, notify_party, forwarder_agent).

**2:** Complex company types (customer, carrier, factory) use dedicated info tables while simple types use JSONB metadata storage.

**3:** Customer info captures customer type, credit limit, incoterms, and special requirements.

**4:** Carrier info includes fleet data, license types, coverage areas, insurance details, and capacity information.

**5:** Factory info stores production capacity, certifications, operating hours, and quality control details.

### Story 2.4 Factory and Forwarder Agent Management

As a CS representative,  
I want to manage factory and forwarder agent information with detailed profiles,  
so that I can efficiently select appropriate factories and agents during shipment creation.

#### Acceptance Criteria

**1:** Factory management interface creates factories with name, location, capacity, certifications, and operational details.

**2:** Factory profile captures production capacity, operating hours, quality control standards, and contact information.

**3:** Forwarder Agent management interface creates agents with company details, service offerings, and contact information.

**4:** Forwarder Agent profile captures name, location, services provided (sea/land/rail), contact details, and operational coverage areas.

**5:** Both Factory and Forwarder Agent profiles support active/inactive status for operational control during shipment creation.

### Story 2.5 Driver Management and Carrier Association

As a CS representative,  
I want to manage driver information linked to carrier companies,  
so that transportation assignments can be efficiently coordinated.

#### Acceptance Criteria

**1:** Driver management interface creates drivers associated with specific carrier companies.

**2:** Driver profile captures name, code, phone, Line ID, photo upload, and availability status.

**3:** Carrier company selection is restricted to companies with carrier type only.

**4:** Driver list view shows carrier association with filtering and search capabilities.

**5:** Photo upload supports image compression and secure storage via Supabase Storage.

### Story 2.6 Customer-Shipper Relationship Management

As a CS representative,  
I want to configure customer-shipper relationships with default preferences,  
so that shipment creation is streamlined with intelligent pre-population.

#### Acceptance Criteria

**1:** Customer-shipper relationship interface allows associating customers with multiple shippers.

**2:** Default shipper designation ensures only one default per customer with automatic toggle of previous defaults.

**3:** Relationship status (active/inactive) controls visibility in shipment creation workflows.

**4:** Bulk import functionality supports initial relationship setup from existing data.

**5:** Relationship changes trigger real-time updates in shipment creation interfaces.

### Story 2.7 Customer-Product Relationship with Pricing Management

As a CS representative,  
I want to manage customer-product relationships with detailed pricing and specifications,  
so that product information is consistent and automatically populated during shipment creation.

#### Acceptance Criteria

**1:** Customer-product interface captures pricing (CIF/FOB per KG), packaging specifications, and weight details.

**2:** Product specifications include packaging type, gross/net weight per package, quality grade, and handling instructions.

**3:** Default product designation per customer supports streamlined shipment creation.

**4:** Currency selection (THB, CNY, USD, EUR) with validation and conversion capabilities.

**5:** Temperature and ventilation requirements are captured for specialized products.

### Story 2.8 Consignee-Notify Party Relationship Management

As a CS representative,  
I want to configure consignee-notify party relationships with communication preferences,  
so that notification workflows are properly established for shipment coordination.

#### Acceptance Criteria

**1:** Consignee-notify party relationship interface supports multiple notify parties per consignee.

**2:** Notification preferences capture communication channels (email, SMS, Line, WeChat) and priority ordering.

**3:** Default notify party selection enables automatic population during shipment creation.

**4:** Special instructions field captures consignee-specific handling or communication requirements.

**5:** Relationship validation ensures both entities have appropriate company types.

## Epic 3 Core Shipment Management

**Epic Goal:** Implement complete shipment lifecycle management from creation through delivery with intelligent cascading selection, status tracking across multiple transportation modes, container management, and stakeholder coordination to deliver the core business value of the fruit export management system.

### Story 3.1 Enhanced Intelligent Shipment Creation Interface

As a CS representative,  
I want to create shipments with Transportation Mode pre-selection, mandatory stakeholder fields, and intelligent pre-population based on relationships,  
so that I can efficiently initiate export processes with minimal data entry while ensuring all critical information is captured.

#### Acceptance Criteria

**1:** Transportation Mode selection modal (Sea/Land/Rail) appears before shipment creation form and configures appropriate workflow fields and requirements.

**2:** Factory selection is mandatory with dropdown showing location and capacity information.

**3:** Forwarder Agent selection is mandatory with contact information and service details display.

**4:** ETD (Estimated Time of Departure), ETA (Estimated Time of Arrival), and Closing Time are mandatory datetime fields with validation ensuring logical sequence (Closing Time < ETD < ETA).

**5:** Destination Port selection is mandatory for routing and documentation, and require Origin Port with customer history suggestions.

**6:** Customer selection automatically loads associated shippers with default shipper pre-selected in cascading dropdown.

**7:** Customer selection automatically loads associated products with default product pre-selected and pricing auto-populated (CIF/FOB per KG).

**8:** Consignee selection automatically loads associated notify parties with default notify party pre-selected.

**9:** Booking Confirmation Document upload is mandatory with validation, preview, and secure storage capabilities.

**10:** Optional Notes field allows additional instructions visible to all stakeholders and included in shipment documentation.

**11:** Shipment number is automatically generated upon save using format EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running] where running number resets monthly per mode/port combination.

**12:** Form validation ensures data consistency, required field completion, and date/time logic validation before submission.

### Story 3.2 Automatic Container Generation and Product Management

As a CS representative,  
I want containers to be automatically generated and products allocated when shipments are saved,  
so that I can focus on shipment details without manual container entry while ensuring accurate cargo tracking.

#### Acceptance Criteria

**1:** System automatically generates dummy containers when shipment is saved based on product quantities and packaging specifications.

**2:** Products are automatically allocated to containers based on quantity (number of packages), packaging specifications, and optimal container utilization.

**3:** Weight calculations automatically compute gross/net weights based on packaging-type quantities and per-package weights from customer-product relationships.

**4:** Container management interface allows post-creation editing of container details including type, size, seal number, and weight information.

**5:** Product details include shipping marks, manufacturing dates, expiration dates, lot numbers, and quality grades captured during shipment creation.

**6:** Container and product data validation ensures consistency with master data and customer-product specifications throughout the process.

### Story 3.3 Shipment Status Lifecycle Management

As a CS representative,  
I want to track and update shipment status throughout the export process,  
so that I can coordinate with stakeholders and maintain accurate records.

#### Acceptance Criteria

**1:** Status workflow supports complete lifecycle: booking_confirmed → transport_assigned → driver_assigned → empty_container_picked → arrived_at_factory → loading_started → departed_factory → container_returned → shipped → arrived → completed.

**2:** Status updates create automatic audit trail with timestamp, user, location, and notes.

**3:** Status history displays complete timeline with all transitions and responsible parties.

**4:** Manual status updates include location information and optional notes for context.

**5:** Status changes trigger real-time updates using Supabase subscriptions for connected users.

### Story 3.4 Transportation Assignment Management

As a CS representative,  
I want to assign transportation resources to shipments,  
so that I can coordinate pickup, delivery, and logistics operations.

#### Acceptance Criteria

**1:** Transportation assignment interface supports both company vehicles (with driver assignment) and external carrier companies.

**2:** Carrier selection is filtered to companies with carrier type and shows available drivers.

**3:** Assignment captures vehicle details (head/tail numbers), driver contact, pickup/delivery locations with GPS coordinates.

**4:** Transportation details include estimated distance and special handling instructions.

**5:** Assignment changes update shipment status automatically and notify relevant parties.

### Story 3.5 Shipment Search and Filtering

As a CS representative,  
I want to search and filter shipments efficiently,  
so that I can quickly locate specific shipments and manage my workload.

#### Acceptance Criteria

**1:** Shipment list displays with search by shipment number, invoice number, customer name, or container number.

**2:** Filtering options include status, customer, carrier, date ranges, and transportation mode.

**3:** Sorting capabilities support multiple columns (date, status, customer, destination).

**4:** Pagination and infinite scroll support large shipment datasets efficiently.

**5:** Advanced search supports compound filters and saved search preferences.

### Story 3.6 Shipment Detail and Overview

As a CS representative,  
I want comprehensive shipment detail views,  
so that I can review all information and coordinate with stakeholders effectively.

#### Acceptance Criteria

**1:** Shipment detail page displays complete information including stakeholders, containers, products, status history, and documents.

**2:** Editable sections allow updates to shipment details with proper validation and permission checks.

**3:** Related information shows customer-product pricing, shipper details, and notify party preferences automatically.

**4:** Action buttons provide quick access to common operations (status update, document generation, notifications).

**5:** Responsive design ensures detail view works effectively on tablets and mobile devices.

### Story 3.7 Multi-Modal Transportation Support

As a CS representative,  
I want to manage shipments across different transportation modes,  
so that I can handle sea, land, and rail export operations.

#### Acceptance Criteria

**1:** Transportation mode selection (sea, land, rail) dynamically adjusts available fields and workflow options.

**2:** Sea transportation captures vessel name, voyage number, booking number, and port information.

**3:** Land transportation focuses on vehicle details, route information, and border crossing requirements.

**4:** Rail transportation includes rail line details, car numbers, and station information.

**5:** Status workflows adapt to transportation mode while maintaining consistent core progression.

## Epic 4 Mobile Driver Interface & Status Updates

**Epic Goal:** Build mobile-optimized Progressive Web App (PWA) for drivers with intuitive status updates, mandatory photo uploads, GPS location capture, and offline capabilities to enable reliable field operations regardless of network connectivity, supporting the complete driver workflow from assignment notification through delivery confirmation.

### Story 4.1 Driver Mobile Authentication and Dashboard

As a Driver,  
I want to access my assigned shipments through a mobile interface,  
so that I can see my work assignments and begin the transportation process.

#### Acceptance Criteria

**1:** Mobile-optimized login interface with large touch targets (minimum 44px) and simplified authentication flow.

**2:** Driver dashboard displays assigned shipments with clear visual hierarchy and dark theme optimized for outdoor use.

**3:** Shipment cards show essential information: shipment number, customer name, pickup/delivery locations, and current status.

**4:** Pull-to-refresh functionality updates assignment list with real-time data from Supabase.

**5:** Progressive Web App (PWA) installation prompts and offline capability indicators are clearly visible.

### Story 4.2 Status Update Interface with Photo Requirements

As a Driver,  
I want to update shipment status with photo documentation,  
so that I can report progress and provide visual confirmation of operations.

#### Acceptance Criteria

**1:** Status update interface shows current status and available next status options based on workflow progression.

**2:** Photo capture is mandatory for each status update with camera API integration and image compression.

**3:** Multiple photo upload support (up to 5 photos per status update) with thumbnail previews.

**4:** GPS location is automatically captured and stored with each status update for location verification.

**5:** Status update form includes optional notes field and location confirmation before submission.

### Story 4.3 Container and Seal Number Data Entry

As a Driver,  
I want to input container numbers and seal details,  
so that tracking information is accurate and complete.

#### Acceptance Criteria

**1:** Container number input with large, clear text fields optimized for mobile typing.

**2:** Seal number entry with validation patterns and format checking for common seal number formats.

**3:** Barcode/QR code scanning capability for container numbers with fallback to manual entry.

**4:** Data validation ensures container numbers match expected formats and constraints.

**5:** Review screen shows entered information with edit capabilities before final submission.

### Story 4.4 Offline Capability and Data Synchronization

As a Driver,  
I want the mobile app to work in areas with poor network connectivity,  
so that I can continue operations regardless of signal strength.

#### Acceptance Criteria

**1:** Service worker implementation enables offline functionality with local data storage.

**2:** Status updates and photos are queued locally when offline with clear visual indicators.

**3:** Automatic synchronization when network connectivity is restored with progress indicators.

**4:** Conflict resolution handles cases where shipment data changes while driver is offline.

**5:** Offline mode displays cached shipment data and prevents data loss during network interruptions.

### Story 4.5 GPS Integration and Location Services

As a Driver,  
I want automatic location capture and navigation assistance,  
so that I can efficiently navigate to pickup and delivery locations.

#### Acceptance Criteria

**1:** Automatic GPS location capture for all status updates with accuracy indicators.

**2:** Location display shows current position and destination with distance calculations.

**3:** Integration with device navigation apps (Google Maps, Apple Maps) for turn-by-turn directions.

**4:** Location history tracking for audit trail and route optimization analysis.

**5:** Privacy controls allow drivers to understand and manage location data sharing.

### Story 4.6 Real-time Communication and Notifications

As a Driver,  
I want to receive notifications about assignment changes and communicate with dispatchers,  
so that I can stay informed and coordinate effectively.

#### Acceptance Criteria

**1:** Push notifications for new assignments, route changes, and urgent communications.

**2:** In-app messaging system for communication with CS representatives and dispatchers.

**3:** Quick action buttons for common communications (arrived, delayed, completed).

**4:** Notification preferences allow drivers to control alert types and timing.

**5:** Notification history maintains record of all communications and status changes.

### Story 4.7 Driver Performance Dashboard and History

As a Driver,  
I want to view my work history and performance metrics,  
so that I can track my assignments and maintain good service records.

#### Acceptance Criteria

**1:** Work history displays completed shipments with dates, locations, and performance metrics.

**2:** Performance indicators show on-time delivery rates, photo compliance, and customer feedback.

**3:** Monthly and weekly summary views with visual charts and progress indicators.

**4:** Achievement badges and recognition for consistent performance and compliance.

**5:** Export capability allows drivers to share performance data for employment verification.

## Epic 5 Document Generation & Management

**Epic Goal:** Develop automated document generation system for export documents (Invoice/Packing List, Commercial Contract, Shipping Instruction) with template-based creation, centralized storage, version control, and multi-channel distribution to eliminate manual document preparation and ensure compliance with export regulations.

### Story 5.1 Document Template Management System

As an Admin,  
I want to create and manage document templates for export documents,  
so that CS representatives can generate consistent, compliant documents with automated field population.

#### Acceptance Criteria

**1:** Template management interface allows creating templates for Invoice/Packing List, Commercial Contract, and Shipping Instruction document types.

**2:** Template editor supports field placeholders that map to shipment, customer, product, and stakeholder data.

**3:** Template versioning maintains history of changes with approval workflow for template updates.

**4:** Preview functionality shows template layout with sample data for validation before activation.

**5:** Template library supports multiple versions per document type with effective date controls.

### Story 5.2 Automated Document Generation Engine

As a CS representative,  
I want to generate export documents automatically from shipment data,  
so that I can produce accurate, complete documentation without manual data entry.

#### Acceptance Criteria

**1:** Document generation interface shows available document types for selected shipment with generation status.

**2:** Field population automatically maps shipment data to template placeholders including customer details, products, weights, and pricing.

**3:** PDF generation uses React PDF or similar library to create professional, print-ready documents.

**4:** Generated documents include proper formatting, logos, signatures, and compliance elements.

**5:** Bulk document generation supports creating multiple document types simultaneously for efficiency.

### Story 5.3 Document Storage and Version Control

As a CS representative,  
I want centralized document storage with version management,  
so that I can maintain organized records and track document revisions.

#### Acceptance Criteria

**1:** Document storage organizes files by shipment with clear folder structure and naming conventions.

**2:** Version control tracks document revisions with timestamps, user information, and change descriptions.

**3:** Document metadata includes file size, mime type, generation timestamp, and template version used.

**4:** Search functionality allows finding documents by shipment number, document type, date range, or customer.

**5:** Access control ensures only authorized users can view, download, or modify documents based on role permissions.

### Story 5.4 Document Distribution and Sharing

As a CS representative,  
I want to distribute documents to stakeholders efficiently,  
so that all parties receive required documentation in a timely manner.

#### Acceptance Criteria

**1:** Email distribution allows sending documents to multiple recipients with customizable email templates.

**2:** Stakeholder selection automatically populates email addresses based on shipment relationships (customer, shipper, consignee, notify party).

**3:** Distribution tracking records when documents were sent, to whom, and delivery confirmation status.

**4:** Secure document links provide time-limited access to documents without requiring system login.

**5:** Bulk distribution supports sending multiple documents to multiple stakeholders in single operation.

### Story 5.5 Document Approval Workflow

As a CS representative,  
I want document review and approval processes,  
so that documents meet quality standards and compliance requirements before distribution.

#### Acceptance Criteria

**1:** Approval workflow routes generated documents to designated reviewers based on document type and value thresholds.

**2:** Review interface allows approvers to examine documents with annotation and comment capabilities.

**3:** Approval status tracking shows document progression through review stages with timestamps and reviewer information.

**4:** Revision requests allow reviewers to request changes with specific feedback and regeneration workflows.

**5:** Final approval locks documents from further modification and enables distribution to external parties.

### Story 5.6 Compliance and Regulatory Features

As a CS representative,  
I want documents to meet export compliance requirements,  
so that shipments clear customs and regulatory inspections successfully.

#### Acceptance Criteria

**1:** Compliance validation checks ensure required fields are populated and meet regulatory format requirements.

**2:** HS code validation confirms product classifications match customs requirements for destination countries.

**3:** Certificate of origin generation includes proper authentication and signature requirements.

**4:** Regulatory templates support different destination country requirements with automatic selection based on consignee location.

**5:** Compliance audit trail maintains record of all document generations with regulatory reference numbers.

### Story 5.7 Document Analytics and Reporting

As an Admin,  
I want analytics on document generation and distribution,  
so that I can monitor system usage and identify process improvements.

#### Acceptance Criteria

**1:** Document generation metrics track volume by type, user, time period, and success rates.

**2:** Distribution analytics show delivery rates, recipient engagement, and communication effectiveness.

**3:** Template usage reports identify most/least used templates and opportunities for optimization.

**4:** Error reporting captures document generation failures with root cause analysis and resolution tracking.

**5:** Performance metrics monitor document generation times, storage usage, and system resource utilization.

## Epic 6 Notification & Communication System

**Epic Goal:** Implement comprehensive multi-channel notification system supporting email, SMS, in-app, Line, and WeChat communications with intelligent stakeholder targeting, preference management, and automated triggers to ensure timely information flow and reduce coordination overhead across all export operation participants.

### Story 6.1 Notification Preferences and Channel Management

As a User,  
I want to configure my notification preferences across multiple communication channels,  
so that I receive relevant updates through my preferred methods without information overload.

#### Acceptance Criteria

**1:** Notification preference interface allows users to configure settings for email, SMS, in-app, Line, and WeChat channels per notification type.

**2:** Notification types include status updates, assignment notifications, document ready alerts, delay warnings, and system messages.

**3:** Preference management supports different settings for urgent vs routine notifications with escalation rules.

**4:** Channel verification ensures contact information (phone numbers, Line IDs, WeChat IDs) is valid and deliverable.

**5:** Default preferences are automatically configured based on user role with ability to customize individual settings.

### Story 6.2 Automated Status Update Notifications

As a Stakeholder,  
I want to receive automatic notifications when shipment status changes,  
so that I stay informed about progress without constantly checking the system.

#### Acceptance Criteria

**1:** Status change triggers automatically identify relevant recipients based on shipment relationships and user roles.

**2:** Notification content includes shipment details, status change information, timestamp, and relevant next actions.

**3:** Intelligent recipient targeting sends notifications only to stakeholders who need specific status updates.

**4:** Notification timing respects user preferences and time zone settings for optimal delivery.

**5:** Status notification history maintains record of all communications sent with delivery confirmation tracking.

### Story 6.3 Assignment and Task Notifications

As a Carrier or Driver,  
I want immediate notifications about new assignments and changes,  
so that I can respond quickly and maintain service quality.

#### Acceptance Criteria

**1:** Transportation assignment notifications are sent immediately when drivers or carriers are assigned to shipments.

**2:** Assignment details include pickup/delivery locations, contact information, special instructions, and timeline requirements.

**3:** Change notifications alert assigned parties about route modifications, timing updates, or requirement changes.

**4:** Acknowledgment system allows recipients to confirm receipt and acceptance of assignments.

**5:** Escalation workflow sends reminders and alerts supervisors if assignments are not acknowledged within defined timeframes.

### Story 6.4 Document Ready and Distribution Alerts

As a Stakeholder,  
I want notifications when documents are generated and ready for review,  
so that I can access required paperwork promptly for customs and logistics coordination.

#### Acceptance Criteria

**1:** Document generation triggers notifications to relevant stakeholders based on document type and shipment relationships.

**2:** Document alerts include direct links for immediate access with secure, time-limited authentication.

**3:** Distribution confirmations notify senders when documents are successfully delivered and accessed by recipients.

**4:** Document update notifications alert stakeholders when revised versions are available or approvals are required.

**5:** Regulatory deadline reminders ensure stakeholders are notified about time-sensitive document requirements.

### Story 6.5 Emergency and Delay Alert System

As a CS Representative,  
I want to send urgent notifications about delays or problems,  
so that stakeholders can take immediate action to minimize impact.

#### Acceptance Criteria

**1:** Emergency notification system bypasses normal preference settings to ensure critical messages reach all relevant parties.

**2:** Delay alert templates provide structured information about cause, expected impact, revised timelines, and mitigation actions.

**3:** Escalation chains automatically notify supervisors and management when critical issues are reported.

**4:** Broadcast messaging allows sending urgent updates to multiple stakeholder groups simultaneously.

**5:** Emergency response tracking monitors acknowledgment and response to critical notifications.

### Story 6.6 Multi-Channel Integration and API Connections

As a System Administrator,  
I want integration with external communication services,  
so that notifications can be delivered through diverse channels reliably.

#### Acceptance Criteria

**1:** Email integration using Supabase Edge Functions with SMTP service configuration and template support.

**2:** SMS service integration (Twilio or similar) with international number support and delivery tracking.

**3:** Line API integration for Thai market communication with rich message formatting and quick reply options.

**4:** WeChat API integration for Chinese market stakeholders with message templates and group communication.

**5:** In-app notification system using Supabase real-time subscriptions with push notification support for mobile PWA.

### Story 6.7 Communication Analytics and Performance Monitoring

As an Admin,  
I want analytics on notification delivery and engagement,  
so that I can optimize communication effectiveness and identify system issues.

#### Acceptance Criteria

**1:** Delivery metrics track success rates by channel, message type, and recipient with failure analysis.

**2:** Engagement analytics measure open rates, response times, and action completion following notifications.

**3:** Channel performance comparison identifies most effective communication methods for different stakeholder types.

**4:** Cost analysis tracks communication expenses by channel and volume with budget monitoring.

**5:** System health monitoring alerts administrators about API failures, quota limits, and service disruptions.

### Story 6.8 Notification Templates and Customization

As a CS Representative,  
I want customizable notification templates for different scenarios,  
so that communications are professional, consistent, and contextually appropriate.

#### Acceptance Criteria

**1:** Template library supports different message types with customizable content, formatting, and branding elements.

**2:** Multi-language templates provide Thai and English versions with automatic language selection based on recipient preferences.

**3:** Dynamic field insertion populates templates with shipment data, stakeholder information, and contextual details.

**4:** Template testing allows previewing messages with sample data before activation.

**5:** Template approval workflow ensures message content meets professional standards and regulatory requirements.

## Checklist Results Report

### Executive Summary

- **Overall PRD Completeness**: 85%
- **MVP Scope Appropriateness**: Just Right - Well-balanced for 6-month development timeline
- **Readiness for Architecture Phase**: Ready - Technical constraints clearly defined
- **Most Critical Gaps**: Missing explicit user research documentation and MVP validation approach

### Category Analysis Table

| Category                         | Status  | Critical Issues |
| -------------------------------- | ------- | --------------- |
| 1. Problem Definition & Context  | PASS    | Missing user research evidence |
| 2. MVP Scope Definition          | PASS    | Well-scoped with clear boundaries |
| 3. User Experience Requirements  | PASS    | Comprehensive UI/UX goals defined |
| 4. Functional Requirements       | PASS    | Complete FR/NFR coverage |
| 5. Non-Functional Requirements   | PASS    | Specific performance targets |
| 6. Epic & Story Structure        | PASS    | Logical sequencing, appropriate sizing |
| 7. Technical Guidance            | PASS    | Clear architecture direction |
| 8. Cross-Functional Requirements | PARTIAL | Data migration strategy not detailed |
| 9. Clarity & Communication       | PASS    | Well-structured documentation |

### Top Issues by Priority

**BLOCKERS: None**

**HIGH Priority Issues:**
- **User Research Gap**: While personas are defined, explicit user research findings and validation are not documented
- **Data Migration Strategy**: Approach for migrating existing manual process data not specified

**MEDIUM Priority Issues:**
- **MVP Validation Approach**: Method for testing MVP success could be more specific
- **Third-party Integration Details**: Line/WeChat API integration specifics need validation

**LOW Priority Issues:**
- **Competitive Analysis**: Not explicitly included in documentation
- **Performance Baseline**: Current system performance metrics not documented

### MVP Scope Assessment

**Scope Appropriateness: JUST RIGHT**
- 6 epics for 6-month timeline is realistic
- Each epic delivers incremental business value
- Foundation → Master Data → Core Business → Mobile → Documents → Notifications follows logical progression
- Technical complexity is balanced across epics

### Technical Readiness

**Architecture Clarity: EXCELLENT**
- Next.js/Supabase stack clearly specified
- Database design (hybrid companies approach) well-defined
- Security model (RLS policies) established
- Performance targets specific and measurable

### Final Decision

**✅ READY FOR ARCHITECT**: The PRD and epics are comprehensive, properly structured, and provide clear technical direction. The identified gaps are not blockers for architectural design work and can be addressed during implementation planning.

## Next Steps

### UX Expert Prompt

You are being asked to create a comprehensive UX design system for the DYY Trading Fruit Export Management System. Please review the attached PRD (docs/prd.md) and create detailed UI/UX specifications including:

1. **Design System Implementation** - Convert the specified dark blue theme (#1e293b, #0f172a, #334155) with orange accents (#f97316) into a complete design system using ShadCN UI components
2. **User Interface Mockups** - Design the core screens identified in the PRD: Admin Dashboard, CS Shipment Management, Customer Mobile Portal, Driver Mobile Interface, and Master Data Management
3. **Mobile-First Responsive Design** - Ensure all interfaces work effectively across desktop, tablet, and mobile with particular attention to the driver mobile experience
4. **Cascading Selection Workflows** - Design the intelligent pre-population interfaces where customer selection loads associated shippers/products and consignee selection loads notify parties
5. **Accessibility Compliance** - Ensure WCAG 2.1 AA compliance with proper contrast ratios and keyboard navigation

Focus on creating a logistics-optimized interface that supports the 50% coordination time reduction goal through intuitive workflows and clear visual hierarchy.

### Architect Prompt

You are being asked to create the technical architecture for the DYY Trading Fruit Export Management System. Please review the attached PRD (docs/prd.md) and create comprehensive technical specifications including:

1. **Next.js/Supabase Architecture** - Design the complete application architecture using Next.js 14+ App Router with Supabase backend, implementing the specified hybrid database design for companies and relationship management
2. **Database Schema Implementation** - Convert the data model requirements into production-ready Supabase schema with Row Level Security policies, triggers, and functions
3. **Progressive Web App Design** - Architect the mobile driver interface as a PWA with offline capabilities, image upload, and real-time synchronization
4. **Multi-Channel Notification System** - Design the technical implementation for email, SMS, Line, WeChat, and in-app notifications using Supabase Edge Functions
5. **Security and Performance Architecture** - Implement the specified sub-2 second performance targets, 99.9% uptime requirements, and comprehensive audit trail system

Focus on creating a scalable, maintainable architecture that supports the 6-epic development roadmap and international fruit export operation requirements.

---

**Final PRD Status: COMPLETE ✅**

The Product Requirements Document has been successfully created with comprehensive coverage of all sections, epics, user stories, and validation results. The PRD provides a solid foundation for the development team to build the DYY Trading Fruit Export Management System.