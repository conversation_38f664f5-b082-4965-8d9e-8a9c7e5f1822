import { z } from 'zod'

// Notification preferences schema (JSONB structure)
export const notificationPreferencesSchema = z.object({
  email: z.boolean().default(false),
  sms: z.boolean().default(false),
  line: z.boolean().default(false),
  wechat: z.boolean().default(false),
})

// Base consignee-notify party validation schema
export const consigneeNotifyPartyBaseSchema = z.object({
  consignee_id: z.string().uuid('Invalid consignee ID'),
  notify_party_id: z.string().uuid('Invalid notify party ID'),
  is_default: z.boolean().optional().default(false),
  is_active: z.boolean().optional().default(true),
  notification_preferences: notificationPreferencesSchema,
  priority_order: z
    .number()
    .int('Priority order must be a whole number')
    .min(1, 'Priority order must be at least 1')
    .max(999, 'Priority order cannot exceed 999')
    .default(1),
  special_instructions: z
    .string()
    .max(2000, 'Special instructions must be 2000 characters or less')
    .optional()
    .nullable(),
  notes: z
    .string()
    .max(1000, 'Notes must be 1000 characters or less')
    .optional()
    .nullable(),
})

// Consignee-notify party form schema with additional validation
export const consigneeNotifyPartyFormSchema = consigneeNotifyPartyBaseSchema
  .refine(
    data => {
      // At least one notification channel must be enabled
      return (
        data.notification_preferences.email ||
        data.notification_preferences.sms ||
        data.notification_preferences.line ||
        data.notification_preferences.wechat
      )
    },
    {
      message: 'At least one notification channel must be enabled',
      path: ['notification_preferences'],
    }
  )
  .refine(
    data => {
      // Cannot set the same consignee and notify party (self-reference prevention)
      return data.consignee_id !== data.notify_party_id
    },
    {
      message: 'Consignee and notify party cannot be the same company',
      path: ['notify_party_id'],
    }
  )

// Update schema (all fields optional except id)
export const consigneeNotifyPartyUpdateSchema = consigneeNotifyPartyFormSchema
  .partial()
  .extend({
    id: z.string().uuid('Invalid consignee-notify party relationship ID'),
  })

// Search and filter schemas
export const consigneeNotifyPartyFilterSchema = z.object({
  consignee_id: z.string().uuid().optional(),
  notify_party_id: z.string().uuid().optional(),
  is_default: z.boolean().optional(),
  is_active: z.boolean().optional(),
  search: z.string().optional(),
  priority_order: z.number().int().min(1).max(999).optional(),
})

// Bulk operations schemas
export const bulkDeleteConsigneeNotifyPartiesSchema = z.object({
  ids: z
    .array(z.string().uuid('Invalid consignee-notify party relationship ID'))
    .min(1, 'At least one relationship must be selected'),
})

// Default notify party management validation
export const setDefaultNotifyPartySchema = z.object({
  consignee_id: z.string().uuid('Invalid consignee ID'),
  relationship_id: z.string().uuid('Invalid relationship ID'),
})

// Company type validation schemas
export const consigneeCompanyTypeSchema = z.object({
  id: z.string().uuid('Invalid company ID'),
  company_type: z.literal('consignee', {
    errorMap: () => ({
      message: 'Selected company must be of type "consignee"',
    }),
  }),
})

export const notifyPartyCompanyTypeSchema = z.object({
  id: z.string().uuid('Invalid company ID'),
  company_type: z.literal('notify_party', {
    errorMap: () => ({
      message: 'Selected company must be of type "notify_party"',
    }),
  }),
})

// Relationship integrity validation
export const relationshipIntegritySchema = z
  .object({
    consignee_id: z.string().uuid(),
    notify_party_id: z.string().uuid(),
    existing_relationships: z.array(
      z.object({
        id: z.string().uuid(),
        consignee_id: z.string().uuid(),
        notify_party_id: z.string().uuid(),
      })
    ),
  })
  .refine(
    data => {
      // Check for duplicate relationships
      return !data.existing_relationships.some(
        rel =>
          rel.consignee_id === data.consignee_id &&
          rel.notify_party_id === data.notify_party_id
      )
    },
    {
      message:
        'Relationship between this consignee and notify party already exists',
    }
  )

// Priority order validation (ensures unique priority per consignee)
export const priorityOrderValidationSchema = z
  .object({
    consignee_id: z.string().uuid(),
    priority_order: z.number().int().min(1).max(999),
    relationship_id: z.string().uuid().optional(), // For updates
    existing_relationships: z.array(
      z.object({
        id: z.string().uuid(),
        consignee_id: z.string().uuid(),
        priority_order: z.number().int(),
      })
    ),
  })
  .refine(
    data => {
      // Allow duplicate priorities - they indicate sequence preference, not uniqueness requirement
      // This validation is kept for future reference but allows duplicates
      return true
    },
    {
      message: 'Priority order validation passed',
    }
  )

// Default designation validation
export const defaultDesignationSchema = z
  .object({
    consignee_id: z.string().uuid(),
    relationship_id: z.string().uuid(),
    is_default: z.boolean(),
    existing_relationships: z.array(
      z.object({
        id: z.string().uuid(),
        consignee_id: z.string().uuid(),
        is_default: z.boolean(),
      })
    ),
  })
  .refine(
    data => {
      if (!data.is_default) return true // If not setting as default, no validation needed

      // Check if there's already a default relationship for this consignee (excluding current)
      const existingDefault = data.existing_relationships.find(
        rel =>
          rel.consignee_id === data.consignee_id &&
          rel.is_default &&
          rel.id !== data.relationship_id
      )

      return !existingDefault // Return false if there's already a default
    },
    {
      message: 'Only one default notify party is allowed per consignee',
      path: ['is_default'],
    }
  )

// Type exports
export type ConsigneeNotifyPartyForm = z.infer<
  typeof consigneeNotifyPartyFormSchema
>
export type ConsigneeNotifyPartyUpdate = z.infer<
  typeof consigneeNotifyPartyUpdateSchema
>
export type ConsigneeNotifyPartyFilter = z.infer<
  typeof consigneeNotifyPartyFilterSchema
>
export type NotificationPreferences = z.infer<
  typeof notificationPreferencesSchema
>
export type SetDefaultNotifyParty = z.infer<typeof setDefaultNotifyPartySchema>
export type ConsigneeCompanyType = z.infer<typeof consigneeCompanyTypeSchema>
export type NotifyPartyCompanyType = z.infer<
  typeof notifyPartyCompanyTypeSchema
>

// Constants for UI
export const NOTIFICATION_CHANNEL_OPTIONS = [
  { value: 'email', label: 'Email', icon: 'Mail', color: 'text-blue-400' },
  { value: 'sms', label: 'SMS', icon: 'Smartphone', color: 'text-green-400' },
  {
    value: 'line',
    label: 'LINE',
    icon: 'MessageSquare',
    color: 'text-green-500',
  },
  {
    value: 'wechat',
    label: 'WeChat',
    icon: 'MessageSquare',
    color: 'text-green-600',
  },
] as const

export const PRIORITY_ORDER_OPTIONS = Array.from({ length: 10 }, (_, i) => ({
  value: i + 1,
  label: `Priority ${i + 1}`,
  description:
    i === 0 ? 'Highest priority' : i === 9 ? 'Lowest priority' : undefined,
}))

// Validation helpers
export const validateNotificationChannels = (
  preferences: NotificationPreferences
): boolean => {
  return (
    preferences.email ||
    preferences.sms ||
    preferences.line ||
    preferences.wechat
  )
}

export const validateCompanyTypes = (
  consigneeType: string,
  notifyPartyType: string
): boolean => {
  return consigneeType === 'consignee' && notifyPartyType === 'notify_party'
}

export const validatePriorityOrder = (priority: number): boolean => {
  return Number.isInteger(priority) && priority >= 1 && priority <= 999
}

export const validateRelationshipUniqueness = (
  consigneeId: string,
  notifyPartyId: string,
  existingRelationships: Array<{
    consignee_id: string
    notify_party_id: string
  }>
): boolean => {
  return !existingRelationships.some(
    rel =>
      rel.consignee_id === consigneeId && rel.notify_party_id === notifyPartyId
  )
}

export const validateDefaultDesignation = (
  consigneeId: string,
  relationshipId: string,
  isDefault: boolean,
  existingRelationships: Array<{
    id: string
    consignee_id: string
    is_default: boolean
  }>
): boolean => {
  if (!isDefault) return true

  const existingDefault = existingRelationships.find(
    rel =>
      rel.consignee_id === consigneeId &&
      rel.is_default &&
      rel.id !== relationshipId
  )

  return !existingDefault
}

// Formatting helpers
export const formatNotificationChannels = (
  preferences: NotificationPreferences
): string[] => {
  const enabled = []
  if (preferences.email) enabled.push('Email')
  if (preferences.sms) enabled.push('SMS')
  if (preferences.line) enabled.push('LINE')
  if (preferences.wechat) enabled.push('WeChat')
  return enabled
}

export const formatPriorityOrder = (priority: number): string => {
  const suffix =
    priority === 1 ? 'st' : priority === 2 ? 'nd' : priority === 3 ? 'rd' : 'th'
  return `${priority}${suffix}`
}

// Default values for form initialization
export const DEFAULT_CONSIGNEE_NOTIFY_PARTY = {
  consignee_id: '',
  notify_party_id: '',
  is_default: false,
  is_active: true,
  notification_preferences: {
    email: true,
    sms: false,
    line: false,
    wechat: false,
  },
  priority_order: 1,
  special_instructions: null,
  notes: null,
} as const

// Error messages
export const ERROR_MESSAGES = {
  REQUIRED_CONSIGNEE: 'Consignee company is required',
  REQUIRED_NOTIFY_PARTY: 'Notify party company is required',
  INVALID_COMPANY_TYPE: 'Selected company has invalid type',
  DUPLICATE_RELATIONSHIP: 'Relationship between these companies already exists',
  NO_NOTIFICATION_CHANNELS: 'At least one notification channel must be enabled',
  INVALID_PRIORITY: 'Priority order must be between 1 and 999',
  MULTIPLE_DEFAULTS: 'Only one default notify party is allowed per consignee',
  SAME_COMPANY: 'Consignee and notify party cannot be the same company',
  SPECIAL_INSTRUCTIONS_TOO_LONG:
    'Special instructions must be 2000 characters or less',
  NOTES_TOO_LONG: 'Notes must be 1000 characters or less',
} as const
