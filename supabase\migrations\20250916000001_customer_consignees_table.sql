-- Customer-Consignee Relationship Table
-- Add missing customer_consignees table for relationship intelligence
-- This table links customers to their consignees for intelligent pre-population

-- ============================================================================
-- CUSTOMER CONSIGNEES RELATIONSHIP TABLE
-- ============================================================================

-- Customer-consignee relationships for intelligent pre-population
CREATE TABLE customer_consignees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    consignee_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),

    -- Ensure customer is of type customer and consignee is of type consignee
    CONSTRAINT customer_consignees_customer_type_check
        CHECK (EXISTS (SELECT 1 FROM companies WHERE id = customer_id AND company_type = 'customer')),
    CONSTRAINT customer_consignees_consignee_type_check
        CHECK (EXISTS (SELECT 1 FROM companies WHERE id = consignee_id AND company_type = 'consignee')),

    -- Unique constraint to prevent duplicate relationships
    UNIQUE(customer_id, consignee_id)
);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_customer_consignees_updated_at
    BEFORE UPDATE ON customer_consignees
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Customer consignees indexes
CREATE INDEX idx_customer_consignees_customer ON customer_consignees(customer_id);
CREATE INDEX idx_customer_consignees_consignee ON customer_consignees(consignee_id);
CREATE INDEX idx_customer_consignees_default ON customer_consignees(customer_id, is_default) WHERE is_default = true;
CREATE INDEX idx_customer_consignees_active ON customer_consignees(is_active);

-- ============================================================================
-- INTELLIGENCE FUNCTIONS
-- ============================================================================

-- Function to get intelligent suggestions for customer consignees
CREATE OR REPLACE FUNCTION get_customer_consignee_suggestions(
    p_customer_id UUID,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE (
    consignee_id UUID,
    consignee_name TEXT,
    is_default BOOLEAN,
    last_used_date TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        cc.consignee_id,
        c.name,
        cc.is_default,
        cc.updated_at as last_used_date
    FROM customer_consignees cc
    JOIN companies c ON c.id = cc.consignee_id
    WHERE cc.customer_id = p_customer_id
    AND cc.is_active = true
    AND c.is_active = true
    ORDER BY cc.is_default DESC, cc.updated_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to get default consignee for a customer
CREATE OR REPLACE FUNCTION get_default_consignee_for_customer(
    p_customer_id UUID
)
RETURNS TABLE (
    consignee_id UUID,
    consignee_name TEXT,
    relationship_id UUID
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        cc.consignee_id,
        c.name,
        cc.id
    FROM customer_consignees cc
    JOIN companies c ON c.id = cc.consignee_id
    WHERE cc.customer_id = p_customer_id
    AND cc.is_default = true
    AND cc.is_active = true
    AND c.is_active = true
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE customer_consignees IS 'Relationship intelligence table linking customers to their preferred consignees with default flagging';
COMMENT ON COLUMN customer_consignees.is_default IS 'Flag to mark the default consignee for intelligent pre-population (only one per customer)';
COMMENT ON COLUMN customer_consignees.is_active IS 'Controls visibility in shipment workflows';
COMMENT ON FUNCTION get_customer_consignee_suggestions(UUID, INTEGER) IS 'Returns intelligent consignee suggestions based on relationship history';
COMMENT ON FUNCTION get_default_consignee_for_customer(UUID) IS 'Returns the default consignee for a customer if set';