import { describe, it, expect } from 'vitest'
import { 
  isAdmin, 
  isStaff, 
  canManageUsers, 
  canViewAllData, 
  validateRoleCompanyAssociation,
  UserRole 
} from '@/lib/supabase/auth'

describe('Authentication Helper Functions', () => {
  describe('isAdmin', () => {
    it('should return true for admin role', () => {
      expect(isAdmin('admin')).toBe(true)
    })

    it('should return false for non-admin roles', () => {
      const nonAdminRoles: UserRole[] = [
        'cs', 'account', 'customer', 'carrier', 'driver', 
        'factory', 'shipper', 'consignee', 'notify_party', 'forwarder_agent'
      ]
      
      nonAdminRoles.forEach(role => {
        expect(isAdmin(role)).toBe(false)
      })
    })
  })

  describe('isStaff', () => {
    it('should return true for staff roles', () => {
      const staffRoles: UserRole[] = ['admin', 'cs', 'account']
      
      staffRoles.forEach(role => {
        expect(isStaff(role)).toBe(true)
      })
    })

    it('should return false for non-staff roles', () => {
      const nonStaffRoles: UserRole[] = [
        'customer', 'carrier', 'driver', 'factory', 
        'shipper', 'consignee', 'notify_party', 'forwarder_agent'
      ]
      
      nonStaffRoles.forEach(role => {
        expect(isStaff(role)).toBe(false)
      })
    })
  })

  describe('canManageUsers', () => {
    it('should return true only for admin role', () => {
      expect(canManageUsers('admin')).toBe(true)
    })

    it('should return false for non-admin roles', () => {
      const nonAdminRoles: UserRole[] = [
        'cs', 'account', 'customer', 'carrier', 'driver', 
        'factory', 'shipper', 'consignee', 'notify_party', 'forwarder_agent'
      ]
      
      nonAdminRoles.forEach(role => {
        expect(canManageUsers(role)).toBe(false)
      })
    })
  })

  describe('canViewAllData', () => {
    it('should return true for staff roles', () => {
      const staffRoles: UserRole[] = ['admin', 'cs', 'account']
      
      staffRoles.forEach(role => {
        expect(canViewAllData(role)).toBe(true)
      })
    })

    it('should return false for non-staff roles', () => {
      const nonStaffRoles: UserRole[] = [
        'customer', 'carrier', 'driver', 'factory', 
        'shipper', 'consignee', 'notify_party', 'forwarder_agent'
      ]
      
      nonStaffRoles.forEach(role => {
        expect(canViewAllData(role)).toBe(false)
      })
    })
  })

  describe('validateRoleCompanyAssociation', () => {
    it('should allow staff roles without company association', () => {
      const staffRoles: UserRole[] = ['admin', 'cs', 'account']
      
      staffRoles.forEach(role => {
        expect(validateRoleCompanyAssociation(role)).toBe(true)
        expect(validateRoleCompanyAssociation(role, undefined)).toBe(true)
        expect(validateRoleCompanyAssociation(role, 'customer')).toBe(true)
      })
    })

    it('should validate role-company type matching', () => {
      const roleCompanyMapping: Array<{ role: UserRole; companyType: string; expected: boolean }> = [
        { role: 'customer', companyType: 'customer', expected: true },
        { role: 'customer', companyType: 'carrier', expected: false },
        { role: 'carrier', companyType: 'carrier', expected: true },
        { role: 'driver', companyType: 'carrier', expected: true },
        { role: 'driver', companyType: 'customer', expected: false },
        { role: 'factory', companyType: 'factory', expected: true },
        { role: 'shipper', companyType: 'shipper', expected: true },
        { role: 'consignee', companyType: 'consignee', expected: true },
        { role: 'notify_party', companyType: 'notify_party', expected: true },
        { role: 'forwarder_agent', companyType: 'forwarder_agent', expected: true }
      ]

      roleCompanyMapping.forEach(({ role, companyType, expected }) => {
        expect(validateRoleCompanyAssociation(role, companyType)).toBe(expected)
      })
    })

    it('should require company association for non-staff roles', () => {
      const nonStaffRoles: UserRole[] = [
        'customer', 'carrier', 'driver', 'factory', 
        'shipper', 'consignee', 'notify_party', 'forwarder_agent'
      ]
      
      nonStaffRoles.forEach(role => {
        expect(validateRoleCompanyAssociation(role)).toBe(false)
        expect(validateRoleCompanyAssociation(role, undefined)).toBe(false)
      })
    })
  })
})