import { describe, it, expect } from 'vitest'
import {
  transportModeSchema,
  shipmentStatusSchema,
  containerTypeSchema,
  shipmentFormSchema,
  shipmentNumberSchema,
  portCombinationSchema,
  dateSequenceSchema,
} from '../shipment'

describe('Shipment Validation Schemas', () => {
  describe('transportModeSchema', () => {
    it('should accept valid transport modes', () => {
      expect(transportModeSchema.parse('sea')).toBe('sea')
      expect(transportModeSchema.parse('land')).toBe('land')
      expect(transportModeSchema.parse('rail')).toBe('rail')
    })

    it('should reject invalid transport modes', () => {
      expect(() => transportModeSchema.parse('air')).toThrow()
      expect(() => transportModeSchema.parse('')).toThrow()
      expect(() => transportModeSchema.parse(null)).toThrow()
    })
  })

  describe('shipmentStatusSchema', () => {
    it('should accept valid shipment statuses', () => {
      const validStatuses = [
        'draft',
        'confirmed',
        'in_transit',
        'customs_clearance',
        'delivered',
        'cancelled',
        'on_hold',
      ]

      validStatuses.forEach(status => {
        expect(shipmentStatusSchema.parse(status)).toBe(status)
      })
    })

    it('should reject invalid shipment statuses', () => {
      expect(() => shipmentStatusSchema.parse('invalid_status')).toThrow()
      expect(() => shipmentStatusSchema.parse('')).toThrow()
    })
  })

  describe('containerTypeSchema', () => {
    it('should accept valid container types', () => {
      const validTypes = [
        'dry_20',
        'dry_40',
        'dry_40_hc',
        'reefer_20',
        'reefer_40',
        'open_top_20',
        'open_top_40',
        'flat_rack_20',
        'flat_rack_40',
        'tank_20',
        'tank_40',
        'lcl',
      ]

      validTypes.forEach(type => {
        expect(containerTypeSchema.parse(type)).toBe(type)
      })
    })

    it('should reject invalid container types', () => {
      expect(() => containerTypeSchema.parse('invalid_container')).toThrow()
      expect(() => containerTypeSchema.parse('')).toThrow()
    })
  })

  describe('shipmentNumberSchema', () => {
    it('should accept valid shipment number formats', () => {
      const validNumbers = [
        'EXSEA-THBKK-240315-001',
        'EXLND-HKHKG-231201-999',
        'EXRAL-SGSIN-240229-123',
        'EXSEA-USNYC-241225-456',
      ]

      validNumbers.forEach(number => {
        expect(shipmentNumberSchema.parse(number)).toBe(number)
      })
    })

    it('should reject invalid shipment number formats', () => {
      const invalidNumbers = [
        'INVALID-FORMAT',
        'EX-THBKK-240315-001',
        'EXSEA-TH-240315-001',
        'EXSEA-THBKK-24031-001',
        'EXSEA-THBKK-240315-01',
        'EXSEA-THBKK-240315-1234',
        'EXAIR-THBKK-240315-001',
      ]

      invalidNumbers.forEach(number => {
        expect(() => shipmentNumberSchema.parse(number)).toThrow()
      })
    })
  })

  describe('portCombinationSchema', () => {
    it('should accept valid port combinations', () => {
      const validCombination = {
        portOfLoading: 'THBKK',
        portOfDischarge: 'HKHKG',
        transportMode: 'sea' as const,
      }

      expect(portCombinationSchema.parse(validCombination)).toEqual(
        validCombination
      )
    })

    it('should reject when ports are the same', () => {
      const invalidCombination = {
        portOfLoading: 'THBKK',
        portOfDischarge: 'THBKK',
        transportMode: 'sea' as const,
      }

      expect(() => portCombinationSchema.parse(invalidCombination)).toThrow(
        'Port of Loading and Port of Discharge cannot be the same'
      )
    })

    it('should accept same ports for different transport modes (transshipment)', () => {
      const validTransshipment = {
        portOfLoading: 'THBKK',
        portOfDischarge: 'THBKK',
        transportMode: 'land' as const,
      }

      // This should be valid for land transport (trucking within same port area)
      expect(portCombinationSchema.parse(validTransshipment)).toEqual(
        validTransshipment
      )
    })
  })

  describe('dateSequenceSchema', () => {
    const baseDate = new Date('2024-03-15T10:00:00Z')
    const etdDate = new Date('2024-03-20T10:00:00Z')
    const etaDate = new Date('2024-03-25T10:00:00Z')

    it('should accept valid date sequences', () => {
      const validSequence = {
        closingTime: baseDate,
        etd: etdDate,
        eta: etaDate,
      }

      expect(dateSequenceSchema.parse(validSequence)).toEqual(validSequence)
    })

    it('should reject when closing time is after ETD', () => {
      const invalidSequence = {
        closingTime: etdDate,
        etd: baseDate,
        eta: etaDate,
      }

      expect(() => dateSequenceSchema.parse(invalidSequence)).toThrow(
        'Closing Time must be before ETD'
      )
    })

    it('should reject when ETD is after ETA', () => {
      const invalidSequence = {
        closingTime: baseDate,
        etd: etaDate,
        eta: etdDate,
      }

      expect(() => dateSequenceSchema.parse(invalidSequence)).toThrow(
        'ETD must be before ETA'
      )
    })

    it('should accept partial date sequences', () => {
      const partialSequence = {
        closingTime: baseDate,
        etd: etdDate,
      }

      expect(dateSequenceSchema.parse(partialSequence)).toEqual(partialSequence)
    })
  })

  describe('shipmentFormSchema', () => {
    const validShipmentData = {
      // Basic Information
      shipmentNumber: 'EXSEA-THBKK-240315-001',
      transportMode: 'sea' as const,
      status: 'draft' as const,

      // Stakeholders
      customerId: 'cust_123',
      shipperId: 'ship_456',
      consigneeId: 'cons_789',
      notifyPartyId: 'notify_101',

      // Route Information
      portOfLoading: 'THBKK',
      portOfDischarge: 'HKHKG',

      // Dates
      closingTime: new Date('2024-03-15T10:00:00Z'),
      etd: new Date('2024-03-20T10:00:00Z'),
      eta: new Date('2024-03-25T10:00:00Z'),

      // Container Information
      containerType: 'dry_20' as const,
      containerQuantity: 2,

      // Cargo Information
      cargoDescription: 'Electronics',
      grossWeight: 15000,
      netWeight: 14000,
      volume: 25.5,

      // Documents
      documents: [],

      // Additional Information
      remarks: 'Handle with care',
      incoterm: 'FOB',
      freightTerms: 'collect' as const,

      // System fields
      createdBy: 'user_123',
      companyId: 'comp_456',
    }

    it('should accept valid complete shipment data', () => {
      expect(() => shipmentFormSchema.parse(validShipmentData)).not.toThrow()
    })

    it('should accept minimal required data', () => {
      const minimalData = {
        transportMode: 'sea' as const,
        customerId: 'cust_123',
        shipperId: 'ship_456',
        portOfLoading: 'THBKK',
        portOfDischarge: 'HKHKG',
        containerType: 'dry_20' as const,
        containerQuantity: 1,
        cargoDescription: 'General cargo',
        grossWeight: 1000,
        documents: [],
        createdBy: 'user_123',
        companyId: 'comp_456',
      }

      expect(() => shipmentFormSchema.parse(minimalData)).not.toThrow()
    })

    it('should reject data missing required fields', () => {
      const incompleteData = {
        transportMode: 'sea' as const,
        // Missing customerId
        shipperId: 'ship_456',
        portOfLoading: 'THBKK',
        portOfDischarge: 'HKHKG',
      }

      expect(() => shipmentFormSchema.parse(incompleteData)).toThrow()
    })

    it('should validate port combination', () => {
      const samePortData = {
        ...validShipmentData,
        portOfLoading: 'THBKK',
        portOfDischarge: 'THBKK',
      }

      expect(() => shipmentFormSchema.parse(samePortData)).toThrow(
        'Port of Loading and Port of Discharge cannot be the same'
      )
    })

    it('should validate date sequence', () => {
      const invalidDateData = {
        ...validShipmentData,
        closingTime: new Date('2024-03-25T10:00:00Z'),
        etd: new Date('2024-03-20T10:00:00Z'),
        eta: new Date('2024-03-15T10:00:00Z'),
      }

      expect(() => shipmentFormSchema.parse(invalidDateData)).toThrow()
    })

    it('should validate container quantity is positive', () => {
      const negativeQuantityData = {
        ...validShipmentData,
        containerQuantity: -1,
      }

      expect(() => shipmentFormSchema.parse(negativeQuantityData)).toThrow()
    })

    it('should validate weights are positive', () => {
      const negativeWeightData = {
        ...validShipmentData,
        grossWeight: -100,
      }

      expect(() => shipmentFormSchema.parse(negativeWeightData)).toThrow()
    })

    it('should validate net weight is not greater than gross weight', () => {
      const invalidWeightData = {
        ...validShipmentData,
        grossWeight: 1000,
        netWeight: 1500,
      }

      expect(() => shipmentFormSchema.parse(invalidWeightData)).toThrow(
        'Net weight cannot be greater than gross weight'
      )
    })

    it('should validate volume is positive when provided', () => {
      const negativeVolumeData = {
        ...validShipmentData,
        volume: -10,
      }

      expect(() => shipmentFormSchema.parse(negativeVolumeData)).toThrow()
    })

    it('should validate freight terms', () => {
      const invalidFreightTermsData = {
        ...validShipmentData,
        freightTerms: 'invalid' as any,
      }

      expect(() => shipmentFormSchema.parse(invalidFreightTermsData)).toThrow()
    })

    it('should validate documents array', () => {
      const validDocumentsData = {
        ...validShipmentData,
        documents: [
          {
            id: 'doc_123',
            name: 'invoice.pdf',
            type: 'invoice',
            url: 'https://example.com/invoice.pdf',
            uploadedAt: new Date(),
            uploadedBy: 'user_123',
          },
        ],
      }

      expect(() => shipmentFormSchema.parse(validDocumentsData)).not.toThrow()
    })

    it('should validate string field lengths', () => {
      const longStringData = {
        ...validShipmentData,
        cargoDescription: 'A'.repeat(1001), // Over 1000 character limit
      }

      expect(() => shipmentFormSchema.parse(longStringData)).toThrow()
    })

    it('should validate ID format requirements', () => {
      const invalidIdData = {
        ...validShipmentData,
        customerId: '', // Empty string
      }

      expect(() => shipmentFormSchema.parse(invalidIdData)).toThrow()
    })
  })
})
