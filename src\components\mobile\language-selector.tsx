'use client'

import { Languages } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/hooks/use-language'
import type { Locale } from '@/lib/i18n'

export function LanguageSelector() {
  const { locale, setLocale, t } = useLanguage()

  const toggleLanguage = () => {
    setLocale(locale === 'en' ? 'th' : 'en')
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="text-slate-400 hover:text-white hover:bg-slate-700 flex items-center space-x-2"
      title={t('common.language')}
    >
      <Languages className="w-4 h-4" />
      <span className="text-sm font-medium">
        {locale === 'th' ? 'ไทย' : 'ENG'}
      </span>
    </Button>
  )
}