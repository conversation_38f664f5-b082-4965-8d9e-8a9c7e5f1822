'use client'

import React, { useEffect, useCallback } from 'react'
import {
  useDriverStore,
  useDrivers,
  useDriversLoading,
  useDriversError,
  useDriverActions,
  type Driver,
  type DriverInsert,
  type DriverUpdate,
} from '@/stores/driver-store'
import { createClient } from '@/lib/supabase/client'
import type { DriverFilter } from '@/lib/validations/drivers'

// Carrier company interface for dropdown selection
export interface CarrierCompany {
  id: string
  name: string
  contact_phone: string | null
  is_active: boolean
}

// Driver profile interface for user binding
export interface DriverProfile {
  user_id: string
  email: string
  first_name: string | null
  last_name: string | null
  full_name: string | null
  is_active: boolean
}

export function useDriversData() {
  const drivers = useDrivers()
  const loading = useDriversLoading()
  const error = useDriversError()
  const {
    fetchDrivers,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    clearError,
    subscribeToDrivers,
  } = useDriverActions()

  // Pagination and filtering state
  const currentPage = useDriverStore(state => state.currentPage)
  const pageSize = useDriverStore(state => state.pageSize)
  const totalCount = useDriverStore(state => state.totalCount)
  const filter = useDriverStore(state => state.filter)
  const searchTerm = useDriverStore(state => state.searchTerm)
  const sortBy = useDriverStore(state => state.sortBy)
  const sortOrder = useDriverStore(state => state.sortOrder)

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / pageSize)
  const hasNextPage = currentPage < totalPages
  const hasPreviousPage = currentPage > 1

  // Load drivers on mount and setup real-time subscription
  useEffect(() => {
    fetchDrivers()

    // Setup real-time subscription
    const unsubscribe = subscribeToDrivers()

    return () => {
      unsubscribe()
    }
  }, [fetchDrivers, subscribeToDrivers])

  // Filtered drivers for dropdown/selection purposes
  const activeDrivers = drivers.filter(driver => driver.is_active)

  const driversByCarrier = drivers.reduce(
    (acc, driver) => {
      const carrierId = driver.carrier_id
      if (!acc[carrierId]) acc[carrierId] = []
      acc[carrierId].push(driver)
      return acc
    },
    {} as Record<string, typeof drivers>
  )

  return {
    // Data
    drivers,
    activeDrivers,
    driversByCarrier,
    loading,
    error,

    // Pagination
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    hasNextPage,
    hasPreviousPage,

    // Filters and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Actions
    fetchDrivers,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage: (page: number) => setPage(Math.max(1, Math.min(page, totalPages))),
    nextPage: () => hasNextPage && setPage(currentPage + 1),
    previousPage: () => hasPreviousPage && setPage(currentPage - 1),
    clearError,

    // Utility functions
    refreshDrivers: fetchDrivers,
    getDriverById: (id: string) => drivers.find(driver => driver.id === id),
    getDriverByCode: (code: string) =>
      drivers.find(
        driver => driver.driver_code?.toLowerCase() === code.toLowerCase()
      ),
    getDriversByCarrier: (carrierId: string) =>
      drivers.filter(driver => driver.carrier_id === carrierId),
  }
}

export function useDriverCRUD() {
  const {
    createDriver,
    updateDriver,
    deleteDriver,
    deleteDrivers,
    fetchDriverById,
    uploadDriverPhoto,
    deleteDriverPhoto,
  } = useDriverActions()

  const isCreating = useDriverStore(state => state.isCreating)
  const isUpdating = useDriverStore(state => state.isUpdating)
  const isDeleting = useDriverStore(state => state.isDeleting)
  const isUploadingPhoto = useDriverStore(state => state.isUploadingPhoto)

  const handleCreate = useCallback(
    async (driverData: DriverInsert): Promise<Driver> => {
      try {
        const result = await createDriver(driverData)
        return result
      } catch (error) {
        console.error('Failed to create driver:', error)
        throw error
      }
    },
    [createDriver]
  )

  const handleUpdate = useCallback(
    async (id: string, updates: Partial<DriverUpdate>): Promise<Driver> => {
      try {
        const result = await updateDriver(id, updates)
        return result
      } catch (error) {
        console.error('Failed to update driver:', error)
        throw error
      }
    },
    [updateDriver]
  )

  const handleDelete = useCallback(
    async (id: string): Promise<void> => {
      try {
        await deleteDriver(id)
      } catch (error) {
        console.error('Failed to delete driver:', error)
        throw error
      }
    },
    [deleteDriver]
  )

  const handleBulkDelete = useCallback(
    async (ids: string[]): Promise<void> => {
      try {
        await deleteDrivers(ids)
      } catch (error) {
        console.error('Failed to delete drivers:', error)
        throw error
      }
    },
    [deleteDrivers]
  )

  const handleFetchById = useCallback(
    async (id: string) => {
      try {
        return await fetchDriverById(id)
      } catch (error) {
        console.error('Failed to fetch driver:', error)
        throw error
      }
    },
    [fetchDriverById]
  )

  const handleUploadPhoto = useCallback(
    async (driverId: string, file: File): Promise<string> => {
      try {
        return await uploadDriverPhoto(driverId, file)
      } catch (error) {
        console.error('Failed to upload driver photo:', error)
        throw error
      }
    },
    [uploadDriverPhoto]
  )

  const handleDeletePhoto = useCallback(
    async (driverId: string): Promise<void> => {
      try {
        await deleteDriverPhoto(driverId)
      } catch (error) {
        console.error('Failed to delete driver photo:', error)
        throw error
      }
    },
    [deleteDriverPhoto]
  )

  return {
    // Loading states
    isCreating,
    isUpdating,
    isDeleting,
    isUploadingPhoto,

    // CRUD operations
    createDriver: handleCreate,
    updateDriver: handleUpdate,
    deleteDriver: handleDelete,
    bulkDeleteDrivers: handleBulkDelete,
    fetchDriverById: handleFetchById,

    // Photo operations
    uploadDriverPhoto: handleUploadPhoto,
    deleteDriverPhoto: handleDeletePhoto,

    // Utility
    isLoading: isCreating || isUpdating || isDeleting || isUploadingPhoto,
  }
}

export function useDriverSelection() {
  const selectedDrivers = useDriverStore(state => state.selectedDrivers)
  const { selectDriver, deselectDriver, clearSelection } = useDriverActions()

  const selectAllDrivers = useDriverStore(state => state.selectAllDrivers)
  const drivers = useDrivers()

  const selectedIds = Array.from(selectedDrivers)
  const selectedCount = selectedDrivers.size
  const isSelected = (id: string) => selectedDrivers.has(id)
  const isAllSelected =
    drivers.length > 0 && selectedDrivers.size === drivers.length
  const isPartiallySelected =
    selectedDrivers.size > 0 && selectedDrivers.size < drivers.length

  const toggleDriver = useCallback(
    (id: string) => {
      if (selectedDrivers.has(id)) {
        deselectDriver(id)
      } else {
        selectDriver(id)
      }
    },
    [selectedDrivers, selectDriver, deselectDriver]
  )

  const toggleAll = useCallback(() => {
    if (isAllSelected) {
      clearSelection()
    } else {
      selectAllDrivers()
    }
  }, [isAllSelected, clearSelection, selectAllDrivers])

  return {
    selectedDrivers: selectedIds,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,
    selectDriver,
    deselectDriver,
    toggleDriver,
    selectAllDrivers,
    toggleAll,
    clearSelection,
  }
}

// Validation hooks
export function useDriverValidation() {
  const drivers = useDrivers()

  const validateUniqueDriverCode = useCallback(
    (code: string, excludeId?: string) => {
      if (!code) return { isValid: true } // Optional field

      const existing = drivers.find(
        driver =>
          driver.driver_code?.toLowerCase() === code.toLowerCase() &&
          driver.is_active &&
          driver.id !== excludeId
      )

      return {
        isValid: !existing,
        message: existing
          ? `Driver with code "${code}" already exists`
          : undefined,
      }
    },
    [drivers]
  )

  const validateDriverName = useCallback((name: string) => {
    if (!name) return { isValid: false, message: 'Name is required' }

    if (name.length < 2) {
      return {
        isValid: false,
        message: 'Name must be at least 2 characters long',
      }
    }

    if (name.length > 50) {
      return {
        isValid: false,
        message: 'Name must be 50 characters or less',
      }
    }

    const nameRegex = /^[a-zA-Z\s-']+$/
    if (!nameRegex.test(name)) {
      return {
        isValid: false,
        message:
          'Name can only contain letters, spaces, hyphens, and apostrophes',
      }
    }

    return { isValid: true }
  }, [])

  const validateDriverCode = useCallback((code: string) => {
    if (!code) return { isValid: true } // Optional field

    if (code.length > 20) {
      return {
        isValid: false,
        message: 'Driver code must be 20 characters or less',
      }
    }

    const codeRegex = /^[A-Z0-9-]+$/
    if (!codeRegex.test(code)) {
      return {
        isValid: false,
        message:
          'Driver code can only contain uppercase letters, numbers, and hyphens',
      }
    }

    return { isValid: true }
  }, [])

  const validatePhoneNumber = useCallback((phone: string) => {
    if (!phone) return { isValid: true } // Optional field

    if (phone.length > 20) {
      return {
        isValid: false,
        message: 'Phone number must be 20 characters or less',
      }
    }

    const phoneRegex = /^[+]?[\d\s-()]+$/

    if (!phoneRegex.test(phone)) {
      return {
        isValid: false,
        message:
          'Phone number must contain only digits, spaces, hyphens, parentheses, and plus sign',
      }
    }

    return { isValid: true }
  }, [])

  const validateLineId = useCallback((lineId: string) => {
    if (!lineId) return { isValid: true } // Optional field

    if (lineId.length > 50) {
      return {
        isValid: false,
        message: 'Line ID must be 50 characters or less',
      }
    }

    return { isValid: true }
  }, [])

  const validateNotes = useCallback((notes: string) => {
    if (!notes) return { isValid: true } // Optional field

    if (notes.length > 500) {
      return {
        isValid: false,
        message: 'Notes must be 500 characters or less',
      }
    }

    return { isValid: true }
  }, [])

  const validatePhotoFile = useCallback((file: File) => {
    const errors: string[] = []

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      errors.push('File size must be less than 2MB')
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      errors.push('File must be a JPEG, PNG, or WebP image')
    }

    // Check filename length
    if (file.name.length > 255) {
      errors.push('Filename must be less than 255 characters')
    }

    return {
      isValid: errors.length === 0,
      message: errors.join(', '),
    }
  }, [])

  const validateUniqueUserId = useCallback(
    (userId: string | null | undefined, excludeId?: string) => {
      if (!userId || userId === null || userId === undefined) return { isValid: true } // Optional field

      const existing = drivers.find(
        driver =>
          driver.user_id === userId &&
          driver.is_active &&
          driver.id !== excludeId
      )

      return {
        isValid: !existing,
        message: existing
          ? 'This user account is already linked to another driver'
          : undefined,
      }
    },
    [drivers]
  )

  return {
    validateUniqueDriverCode,
    validateDriverName,
    validateDriverCode,
    validatePhoneNumber,
    validateLineId,
    validateNotes,
    validatePhotoFile,
    validateUniqueUserId,
  }
}

// Carrier companies management
export function useCarrierCompanies() {
  const [carrierCompanies, setCarrierCompanies] = React.useState<
    CarrierCompany[]
  >([])
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  const fetchCarrierCompanies = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('companies')
        .select('id, name, contact_phone, is_active')
        .eq('company_type', 'carrier')
        .eq('is_active', true)
        .order('name')

      if (error) {
        throw new Error(error.message)
      }

      setCarrierCompanies(data || [])
    } catch (error) {
      console.error('Error fetching carrier companies:', error)
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to fetch carrier companies'
      )
    } finally {
      setLoading(false)
    }
  }, [])

  const getCarrierCompanyOptions = useCallback(() => {
    return carrierCompanies.map(carrier => ({
      value: carrier.id,
      label: carrier.name,
      phone: carrier.contact_phone,
    }))
  }, [carrierCompanies])

  const getCarrierCompanyName = useCallback(
    (carrierId: string) => {
      return (
        carrierCompanies.find(carrier => carrier.id === carrierId)?.name ||
        'Unknown Carrier'
      )
    },
    [carrierCompanies]
  )

  const validateCarrierCompany = useCallback(
    async (carrierId: string): Promise<boolean> => {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('companies')
          .select('company_type, is_active')
          .eq('id', carrierId)
          .single()

        if (error || !data) {
          return false
        }

        return data.company_type === 'carrier' && data.is_active
      } catch (error) {
        console.error('Error validating carrier company:', error)
        return false
      }
    },
    []
  )

  // Load carrier companies on mount
  useEffect(() => {
    fetchCarrierCompanies()
  }, [fetchCarrierCompanies])

  return {
    carrierCompanies,
    loading,
    error,
    carrierCompanyOptions: getCarrierCompanyOptions(),
    fetchCarrierCompanies,
    getCarrierCompanyName,
    validateCarrierCompany,
  }
}

// Driver profiles management for user binding
export function useDriverProfiles() {
  const [driverProfiles, setDriverProfiles] = React.useState<DriverProfile[]>(
    []
  )
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  const fetchDriverProfiles = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('profiles')
        .select('user_id, email, first_name, last_name, full_name, is_active')
        .eq('role', 'driver')
        .eq('is_active', true)
        .order('email')

      if (error) {
        throw new Error(error.message)
      }

      setDriverProfiles(data || [])
    } catch (error) {
      console.error('Error fetching driver profiles:', error)
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to fetch driver profiles'
      )
    } finally {
      setLoading(false)
    }
  }, [])

  const getDriverProfileOptions = useCallback(() => {
    return driverProfiles.map(profile => ({
      value: profile.user_id,
      label: profile.email,
      displayName: profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
    }))
  }, [driverProfiles])

  const getDriverProfileByEmail = useCallback(
    (email: string) => {
      return driverProfiles.find(profile => profile.email === email)
    },
    [driverProfiles]
  )

  const getDriverProfileById = useCallback(
    (userId: string) => {
      return driverProfiles.find(profile => profile.user_id === userId)
    },
    [driverProfiles]
  )

  // Load driver profiles on mount
  useEffect(() => {
    fetchDriverProfiles()
  }, [fetchDriverProfiles])

  return {
    driverProfiles,
    loading,
    error,
    driverProfileOptions: getDriverProfileOptions(),
    fetchDriverProfiles,
    getDriverProfileByEmail,
    getDriverProfileById,
  }
}

// Photo utilities
export function useDriverPhotoUtils() {
  const getPhotoUrl = useCallback(
    async (driver: Driver): Promise<string | null> => {
      if (!driver.driver_picture_path) return null

      try {
        const supabase = createClient()
        const { data, error } = await supabase.storage
          .from('driver-photos')
          .createSignedUrl(driver.driver_picture_path, 3600) // 1 hour expiry

        if (error) {
          console.error('Error creating signed URL:', error)
          return null
        }

        return data.signedUrl
      } catch (error) {
        console.error('Error getting photo URL:', error)
        return null
      }
    },
    []
  )

  const getPhotoDisplayName = useCallback((driver: Driver): string => {
    return `${driver.driver_first_name} ${driver.driver_last_name} Photo`
  }, [])

  const hasPhoto = useCallback((driver: Driver): boolean => {
    return !!driver.driver_picture_path && !!driver.driver_picture_mime_type
  }, [])

  // Hook to load photo URL with caching
  const usePhotoUrl = useCallback(
    (driver: Driver | null) => {
      const [photoUrl, setPhotoUrl] = React.useState<string | null>(null)
      const [loading, setLoading] = React.useState(false)

      useEffect(() => {
        if (!driver || !hasPhoto(driver)) {
          setPhotoUrl(null)
          return
        }

        let cancelled = false
        setLoading(true)

        getPhotoUrl(driver)
          .then(url => {
            if (!cancelled) {
              setPhotoUrl(url)
              setLoading(false)
            }
          })
          .catch(error => {
            if (!cancelled) {
              console.error('Failed to load photo URL:', error)
              setPhotoUrl(null)
              setLoading(false)
            }
          })

        return () => {
          cancelled = true
        }
      }, [driver?.id, driver?.driver_picture_path, driver?.updated_at])

      return { photoUrl, loading }
    },
    [getPhotoUrl, hasPhoto]
  )

  return {
    getPhotoUrl,
    getPhotoDisplayName,
    hasPhoto,
    usePhotoUrl,
  }
}

// Constants
export const DEFAULT_DRIVER_FILTER: DriverFilter = {
  is_active: true,
}

// Common driver statuses for display
export const DRIVER_STATUSES = [
  { value: true, label: 'Active', variant: 'default' as const },
  { value: false, label: 'Inactive', variant: 'secondary' as const },
] as const

// Main hook that combines all functionality
export function useDriversManagement() {
  const data = useDriversData()
  const crud = useDriverCRUD()
  const selection = useDriverSelection()
  const validation = useDriverValidation()
  const carriers = useCarrierCompanies()
  const driverProfiles = useDriverProfiles()
  const photoUtils = useDriverPhotoUtils()

  return {
    ...data,
    ...crud,
    ...selection,
    ...validation,
    ...carriers,
    ...driverProfiles,
    ...photoUtils,

    // Convenience methods
    resetFilters: () => data.setFilter(DEFAULT_DRIVER_FILTER),
    clearSearch: () => data.setSearchTerm(''),
    getDriverFullName: (driver: Driver) =>
      `${driver.driver_first_name} ${driver.driver_last_name}`,
    getDriverDisplayName: (driver: Driver) => {
      const fullName = `${driver.driver_first_name} ${driver.driver_last_name}`
      return driver.driver_code
        ? `${fullName} (${driver.driver_code})`
        : fullName
    },
  }
}
