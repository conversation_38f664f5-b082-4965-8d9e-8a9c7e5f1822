import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { act } from 'react-dom/test-utils'
import { useRouter } from 'next/navigation'

// Mock Next.js router
const mockPush = vi.fn()
const mockReplace = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
    refresh: vi.fn(),
  }),
}))

// Mock Supabase client with more realistic responses
const mockSupabaseClient = {
  auth: {
    signInWithPassword: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
    getSession: vi.fn(),
    onAuthStateChange: vi.fn(),
    resetPasswordForEmail: vi.fn(),
    updateUser: vi.fn(),
  },
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn(),
        maybeSingle: vi.fn(),
      })),
      order: vi.fn(() => Promise.resolve({ data: [], error: null })),
      insert: vi.fn(() => Promise.resolve({ data: null, error: null })),
      update: vi.fn(() => Promise.resolve({ data: null, error: null })),
    })),
  })),
}

vi.mock('@/lib/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

vi.mock('@/lib/supabase/server', () => ({
  createServerClient: () => mockSupabaseClient,
}))

// Mock auth helpers with realistic implementations
vi.mock('@/lib/supabase/auth', () => ({
  authClient: {
    signIn: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
    resetPassword: vi.fn(),
    updatePassword: vi.fn(),
  },
  isAdmin: vi.fn(),
  isStaff: vi.fn(),
  canManageUsers: vi.fn(),
  validateRoleCompanyAssociation: vi.fn(),
}))

// Import components after mocks
import LoginPage from '@/app/(auth)/login/page'
import RegisterPage from '@/app/(auth)/register/page'

describe('Authentication Flow Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Reset default mock implementations
    mockSupabaseClient.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    })
    
    mockSupabaseClient.from.mockReturnValue({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: null, error: null })),
          maybeSingle: vi.fn(() => Promise.resolve({ data: null, error: null })),
        })),
        order: vi.fn(() => Promise.resolve({ 
          data: [
            { id: '1', name: 'Test Company', company_type: 'customer' },
            { id: '2', name: 'Carrier Co', company_type: 'carrier' }
          ], 
          error: null 
        })),
      })),
      insert: vi.fn(() => Promise.resolve({ data: null, error: null })),
      update: vi.fn(() => Promise.resolve({ data: null, error: null })),
    })
  })

  describe('Complete Login Flow', () => {
    it('should handle successful login with valid credentials', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        user_metadata: {},
      }
      
      const mockSession = {
        access_token: 'access-token',
        refresh_token: 'refresh-token',
        user: mockUser,
        expires_at: Date.now() + 3600000,
      }

      const mockProfile = {
        user_id: 'user-123',
        email: '<EMAIL>',
        role: 'admin',
        company_id: null,
        is_active: true,
        first_name: 'Test',
        last_name: 'User',
      }

      // Mock successful sign in
      mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      })

      // Mock profile fetch
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: mockProfile,
        error: null,
      })

      // Mock auth client
      const { authClient } = require('@/lib/supabase/auth')
      authClient.signIn.mockResolvedValue({ user: mockUser, profile: mockProfile })

      // Mock useAuth hook to simulate authentication state change
      const mockUseAuth = vi.fn()
        .mockReturnValueOnce({ isAuthenticated: false, loading: false })
        .mockReturnValueOnce({ isAuthenticated: true, loading: false, user: mockProfile })

      vi.doMock('@/hooks/use-auth', () => ({
        useAuth: mockUseAuth,
      }))

      const { rerender } = render(<LoginPage />)

      // Fill in login form
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })

      // Submit form
      fireEvent.click(submitButton)

      // Wait for auth call
      await waitFor(() => {
        expect(authClient.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123')
      })

      // Simulate authentication state change by re-rendering
      mockUseAuth.mockReturnValue({ isAuthenticated: true, loading: false, user: mockProfile })
      rerender(<LoginPage />)

      // Should redirect to dashboard
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard')
      })
    })

    it('should handle login with invalid credentials', async () => {
      const authError = {
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid login credentials',
        timestamp: new Date(),
      }

      // Mock failed sign in
      const { authClient } = require('@/lib/supabase/auth')
      authClient.signIn.mockRejectedValue(authError)

      // Mock useAuth hook
      vi.doMock('@/hooks/use-auth', () => ({
        useAuth: () => ({ isAuthenticated: false, loading: false }),
      }))

      render(<LoginPage />)

      // Fill in login form with invalid credentials
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })

      // Submit form
      fireEvent.click(submitButton)

      // Wait for error to appear
      await waitFor(() => {
        expect(screen.getByText('Invalid login credentials')).toBeInTheDocument()
      })

      // Should not redirect
      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should handle deactivated account', async () => {
      const authError = {
        code: 'ACCOUNT_DEACTIVATED',
        message: 'Your account has been deactivated',
        timestamp: new Date(),
      }

      const { authClient } = require('@/lib/supabase/auth')
      authClient.signIn.mockRejectedValue(authError)

      vi.doMock('@/hooks/use-auth', () => ({
        useAuth: () => ({ isAuthenticated: false, loading: false }),
      }))

      render(<LoginPage />)

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })

      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Your account has been deactivated')).toBeInTheDocument()
        expect(screen.getByText('Contact your administrator or support team for assistance.')).toBeInTheDocument()
      })
    })
  })

  describe('Complete Registration Flow', () => {
    it('should handle successful user registration with role and company', async () => {
      const mockUser = {
        id: 'new-user-123',
        email: '<EMAIL>',
        user_metadata: {},
      }

      const registrationData = {
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        firstName: 'New',
        lastName: 'User',
        role: 'customer',
        companyId: '1',
        phoneNumber: '+**********',
      }

      // Mock successful registration
      const { authClient } = require('@/lib/supabase/auth')
      authClient.signUp.mockResolvedValue({ user: mockUser })

      render(<RegisterPage />)

      // Wait for companies to load
      await waitFor(() => {
        expect(screen.getByText('Test Company')).toBeInTheDocument()
      })

      // Fill registration form
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const confirmPasswordInput = screen.getByLabelText('Confirm Password')
      const firstNameInput = screen.getByLabelText('First Name')
      const lastNameInput = screen.getByLabelText('Last Name')
      const phoneInput = screen.getByLabelText('Phone Number')

      fireEvent.change(emailInput, { target: { value: registrationData.email } })
      fireEvent.change(passwordInput, { target: { value: registrationData.password } })
      fireEvent.change(confirmPasswordInput, { target: { value: registrationData.password } })
      fireEvent.change(firstNameInput, { target: { value: registrationData.firstName } })
      fireEvent.change(lastNameInput, { target: { value: registrationData.lastName } })
      fireEvent.change(phoneInput, { target: { value: registrationData.phoneNumber } })

      // Select company
      const companySelect = screen.getByRole('combobox')
      fireEvent.click(companySelect)
      
      await waitFor(() => {
        const companyOption = screen.getByText('Test Company')
        fireEvent.click(companyOption)
      })

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create account/i })
      fireEvent.click(submitButton)

      // Verify registration was called with correct data
      await waitFor(() => {
        expect(authClient.signUp).toHaveBeenCalledWith(
          expect.objectContaining({
            email: registrationData.email,
            password: registrationData.password,
            firstName: registrationData.firstName,
            lastName: registrationData.lastName,
            role: registrationData.role,
            companyId: registrationData.companyId,
            phoneNumber: registrationData.phoneNumber,
          })
        )
      })
    })

    it('should handle registration with staff role (no company required)', async () => {
      const registrationData = {
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
      }

      const { authClient } = require('@/lib/supabase/auth')
      authClient.signUp.mockResolvedValue({ user: { id: 'admin-123' } })

      render(<RegisterPage />)

      // Change role to admin
      const roleSelect = screen.getByRole('combobox')
      fireEvent.click(roleSelect)
      
      await waitFor(() => {
        const adminOption = screen.getByText('Administrator')
        fireEvent.click(adminOption)
      })

      // Fill basic information
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const confirmPasswordInput = screen.getByLabelText('Confirm Password')
      const firstNameInput = screen.getByLabelText('First Name')
      const lastNameInput = screen.getByLabelText('Last Name')

      fireEvent.change(emailInput, { target: { value: registrationData.email } })
      fireEvent.change(passwordInput, { target: { value: registrationData.password } })
      fireEvent.change(confirmPasswordInput, { target: { value: registrationData.password } })
      fireEvent.change(firstNameInput, { target: { value: registrationData.firstName } })
      fireEvent.change(lastNameInput, { target: { value: registrationData.lastName } })

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create account/i })
      fireEvent.click(submitButton)

      // Verify admin registration (no company required)
      await waitFor(() => {
        expect(authClient.signUp).toHaveBeenCalledWith(
          expect.objectContaining({
            role: 'admin',
            companyId: '', // Admin doesn't need company
          })
        )
      })
    })

    it('should handle registration errors', async () => {
      const registrationError = {
        code: 'EMAIL_EXISTS',
        message: 'User already registered',
        timestamp: new Date(),
      }

      const { authClient } = require('@/lib/supabase/auth')
      authClient.signUp.mockRejectedValue(registrationError)

      render(<RegisterPage />)

      // Fill minimal form data
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const confirmPasswordInput = screen.getByLabelText('Confirm Password')
      const firstNameInput = screen.getByLabelText('First Name')
      const lastNameInput = screen.getByLabelText('Last Name')

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'Password123!' } })
      fireEvent.change(confirmPasswordInput, { target: { value: 'Password123!' } })
      fireEvent.change(firstNameInput, { target: { value: 'Test' } })
      fireEvent.change(lastNameInput, { target: { value: 'User' } })

      const submitButton = screen.getByRole('button', { name: /create account/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('User already registered')).toBeInTheDocument()
      })
    })
  })

  describe('Password Reset Flow', () => {
    it('should handle forgot password request', async () => {
      // This would test the forgot password page when implemented
      const { authClient } = require('@/lib/supabase/auth')
      authClient.resetPassword.mockResolvedValue({ success: true })

      // Mock implementation for when forgot password page is created
      const mockResetRequest = vi.fn().mockResolvedValue({ success: true })
      
      expect(mockResetRequest).toBeDefined()
    })

    it('should handle password reset with token', async () => {
      // This would test the reset password page when implemented
      const { authClient } = require('@/lib/supabase/auth')
      authClient.updatePassword.mockResolvedValue({ success: true })

      // Mock implementation for when reset password page is created
      const mockPasswordUpdate = vi.fn().mockResolvedValue({ success: true })
      
      expect(mockPasswordUpdate).toBeDefined()
    })
  })

  describe('Session Management', () => {
    it('should handle session persistence across page reloads', async () => {
      const mockSession = {
        access_token: 'access-token',
        refresh_token: 'refresh-token',
        user: { id: 'user-123', email: '<EMAIL>' },
        expires_at: Date.now() + 3600000,
      }

      // Mock persisted session
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null,
      })

      // Mock profile fetch
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: {
          user_id: 'user-123',
          email: '<EMAIL>',
          role: 'admin',
          is_active: true,
        },
        error: null,
      })

      // This would be tested with the auth provider/store when component is ready
      expect(mockSupabaseClient.auth.getSession).toBeDefined()
    })

    it('should handle session expiration and refresh', async () => {
      const expiredSession = {
        access_token: 'expired-token',
        refresh_token: 'refresh-token',
        user: { id: 'user-123', email: '<EMAIL>' },
        expires_at: Date.now() - 1000, // Expired
      }

      const refreshedSession = {
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        user: { id: 'user-123', email: '<EMAIL>' },
        expires_at: Date.now() + 3600000,
      }

      mockSupabaseClient.auth.getSession
        .mockResolvedValueOnce({ data: { session: expiredSession }, error: null })
        .mockResolvedValueOnce({ data: { session: refreshedSession }, error: null })

      // This would be tested with the auth store/provider session refresh logic
      expect(mockSupabaseClient.auth.getSession).toBeDefined()
    })

    it('should handle logout and session cleanup', async () => {
      const { authClient } = require('@/lib/supabase/auth')
      authClient.signOut.mockResolvedValue({ error: null })

      mockSupabaseClient.auth.signOut.mockResolvedValue({ error: null })

      // Mock logout functionality
      await act(async () => {
        await authClient.signOut()
      })

      expect(authClient.signOut).toHaveBeenCalled()
    })
  })

  describe('Role-Based Authentication Context', () => {
    it('should load user profile with role information after login', async () => {
      const mockProfile = {
        user_id: 'user-123',
        email: '<EMAIL>',
        role: 'customer',
        company_id: 'company-123',
        is_active: true,
        first_name: 'Test',
        last_name: 'User',
      }

      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: mockProfile,
        error: null,
      })

      // This would be tested with the actual auth hooks/context
      const profileQuery = mockSupabaseClient.from('profiles')
        .select('*')
        .eq('user_id', 'user-123')
        .single()

      const result = await profileQuery
      expect(result.data).toEqual(mockProfile)
    })

    it('should validate role-company associations', async () => {
      const { validateRoleCompanyAssociation } = require('@/lib/supabase/auth')
      
      // Mock validation function
      validateRoleCompanyAssociation.mockImplementation((role: string, companyType?: string) => {
        const staffRoles = ['admin', 'cs', 'account']
        if (staffRoles.includes(role)) return true
        if (!companyType) return false
        return role === companyType
      })

      // Test staff roles (no company required)
      expect(validateRoleCompanyAssociation('admin')).toBe(true)
      expect(validateRoleCompanyAssociation('cs')).toBe(true)

      // Test role-company matching
      expect(validateRoleCompanyAssociation('customer', 'customer')).toBe(true)
      expect(validateRoleCompanyAssociation('customer', 'carrier')).toBe(false)
      expect(validateRoleCompanyAssociation('driver', 'carrier')).toBe(true)
    })
  })

  describe('Error Recovery and Retry Logic', () => {
    it('should retry authentication on network errors', async () => {
      const { authClient } = require('@/lib/supabase/auth')
      
      // First call fails with network error, second succeeds
      authClient.signIn
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ user: { id: 'user-123' } })

      // Simulate retry logic
      let result
      try {
        result = await authClient.signIn('<EMAIL>', 'password')
      } catch (error) {
        // Retry
        result = await authClient.signIn('<EMAIL>', 'password')
      }

      expect(authClient.signIn).toHaveBeenCalledTimes(2)
      expect(result).toBeDefined()
    })

    it('should handle rate limiting gracefully', async () => {
      const rateLimitError = {
        code: 'RATE_LIMITED',
        message: 'Too many login attempts. Please try again later.',
        timestamp: new Date(),
        details: { retryAfter: 60 },
      }

      const { authClient } = require('@/lib/supabase/auth')
      authClient.signIn.mockRejectedValue(rateLimitError)

      vi.doMock('@/hooks/use-auth', () => ({
        useAuth: () => ({ isAuthenticated: false, loading: false }),
      }))

      render(<LoginPage />)

      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Too many login attempts. Please try again later.')).toBeInTheDocument()
        expect(screen.getByText('This helps protect your account from unauthorized access attempts.')).toBeInTheDocument()
      })
    })
  })
})