'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useDocumentGeneration } from '@/hooks/use-document-generation'
import { useDocumentTemplates } from '@/hooks/use-document-templates'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { FileText, Download, Eye, AlertCircle, CheckCir<PERSON>, <PERSON>, Loader2 } from 'lucide-react'
import type { DocumentType } from '@/types/document-template'
import type { GeneratedDocument, GenerationProgress } from '@/types/document-generation'

interface DocumentGenerationInterfaceProps {
  shipmentId?: string
  onDocumentGenerated?: (document: GeneratedDocument) => void
}

/**
 * Main document generation interface component
 * Story 5.2: Automated Document Generation Engine - AC1, AC5
 */
export function DocumentGenerationInterface({ 
  shipmentId,
  onDocumentGenerated 
}: DocumentGenerationInterfaceProps) {
  const [selectedShipmentId, setSelectedShipmentId] = useState<string>(shipmentId || '')
  const [selectedTemplateIds, setSelectedTemplateIds] = useState<string[]>([])
  const [documentNumber, setDocumentNumber] = useState<string>('')
  const [notes, setNotes] = useState<string>('')
  const [mode, setMode] = useState<'single' | 'bulk'>('single')
  
  // Hooks
  const {
    generateDocument,
    bulkGenerateDocuments,
    isGenerating,
    progress,
    lastResult,
    error,
    clearError,
    reset
  } = useDocumentGeneration({
    onComplete: (result) => {
      if ('document' in result && result.document) {
        onDocumentGenerated?.(result.document)
      }
    }
  })

  const {
    templates,
    loading: templatesLoading,
    error: templatesError,
    loadTemplates
  } = useDocumentTemplates({
    filters: { is_active: true },
    sort: { field: 'template_name', direction: 'asc' },
    pageSize: 50
  })

  /**
   * Handle single document generation
   */
  const handleSingleGeneration = async () => {
    if (!selectedShipmentId || selectedTemplateIds.length === 0) {
      return
    }

    await generateDocument({
      shipmentId: selectedShipmentId,
      templateId: selectedTemplateIds[0],
      options: {
        documentNumber: documentNumber || undefined,
        additionalData: { notes }
      }
    })
  }

  /**
   * Handle bulk document generation
   */
  const handleBulkGeneration = async () => {
    if (!selectedShipmentId || selectedTemplateIds.length === 0) {
      return
    }

    await bulkGenerateDocuments({
      shipmentId: selectedShipmentId,
      templateIds: selectedTemplateIds,
      options: {
        additionalData: { notes }
      }
    })
  }

  /**
   * Handle template selection for single/bulk mode
   */
  const handleTemplateSelection = (templateId: string, checked: boolean) => {
    if (mode === 'single') {
      setSelectedTemplateIds(checked ? [templateId] : [])
    } else {
      setSelectedTemplateIds(prev => 
        checked 
          ? [...prev, templateId]
          : prev.filter(id => id !== templateId)
      )
    }
  }

  /**
   * Get document type display info
   */
  const getDocumentTypeInfo = (type: DocumentType) => {
    const typeMap = {
      booking_confirmation: { label: 'Booking Confirmation', icon: '📋', color: 'bg-blue-100 text-blue-800' },
      invoice_fob: { label: 'Invoice FOB', icon: '💰', color: 'bg-green-100 text-green-800' },
      invoice_cif: { label: 'Invoice CIF', icon: '💳', color: 'bg-green-100 text-green-800' },
      shipping_instruction: { label: 'Shipping Instruction', icon: '📦', color: 'bg-orange-100 text-orange-800' },
      bill_of_lading: { label: 'Bill of Lading', icon: '🚢', color: 'bg-blue-100 text-blue-800' },
      photo_upload: { label: 'Photo Upload', icon: '📸', color: 'bg-purple-100 text-purple-800' },
      other: { label: 'Other', icon: '📄', color: 'bg-gray-100 text-gray-800' }
    }
    return typeMap[type] || typeMap.other
  }

  /**
   * Handle document preview - opens document in new tab
   */
  const handlePreview = async (document: GeneratedDocument) => {
    try {
      const supabase = createClient()
      
      // Get signed URL for preview (expires in 1 hour)
      const { data, error } = await supabase.storage
        .from('documents')
        .createSignedUrl(document.file_path, 3600)
      
      if (error) {
        console.error('Error creating signed URL for preview:', error)
        alert('Failed to generate preview URL')
        return
      }
      
      // Open in new tab
      window.open(data.signedUrl, '_blank')
    } catch (error) {
      console.error('Error previewing document:', error)
      alert('Failed to preview document')
    }
  }

  /**
   * Handle document download
   */
  const handleDownload = async (document: GeneratedDocument) => {
    try {
      const supabase = createClient()
      
      // Get signed URL for download (expires in 1 hour)
      const { data, error } = await supabase.storage
        .from('documents')
        .createSignedUrl(document.file_path, 3600)
      
      if (error) {
        console.error('Error creating signed URL for download:', error)
        alert('Failed to generate download URL')
        return
      }
      
      // Use fetch to get the file and trigger download
      try {
        const response = await fetch(data.signedUrl)
        if (!response.ok) {
          throw new Error('Failed to fetch file')
        }
        
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        
        // Create download link and click it
        const link = window.document.createElement('a')
        link.href = url
        link.download = document.file_name
        link.style.display = 'none'
        
        window.document.body.appendChild(link)
        link.click()
        window.document.body.removeChild(link)
        
        // Clean up the object URL
        window.URL.revokeObjectURL(url)
      } catch (fetchError) {
        console.error('Error fetching file for download:', fetchError)
        // Fallback: open in new tab
        window.open(data.signedUrl, '_blank')
      }
    } catch (error) {
      console.error('Error downloading document:', error)
      alert('Failed to download document')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900 dark:text-white">Document Generation</h2>
          <p className="text-slate-600 dark:text-slate-400">
            Generate professional documents from shipment data
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant={mode === 'single' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setMode('single')}
            className={`text-sm ${
              mode === 'single' 
                ? 'bg-orange-500 hover:bg-orange-600 text-white' 
                : 'border-slate-400 hover:bg-slate-100 dark:hover:bg-slate-800'
            }`}
          >
            Single
          </Button>
          <Button
            variant={mode === 'bulk' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setMode('bulk')}
            className={`text-sm ${
              mode === 'bulk' 
                ? 'bg-orange-500 hover:bg-orange-600 text-white' 
                : 'border-slate-400 hover:bg-slate-100 dark:hover:bg-slate-800'
            }`}
          >
            Bulk
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Generation Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={clearError}
            className="mt-2 border-red-400 hover:bg-red-50 dark:hover:bg-red-950 text-red-600"
          >
            Dismiss
          </Button>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Generation Configuration
            </CardTitle>
            <CardDescription>
              Configure document generation settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Shipment Selection */}
            {!shipmentId && (
              <div className="space-y-2">
                <Label htmlFor="shipment-select">Shipment</Label>
                <Input
                  id="shipment-select"
                  placeholder="Enter shipment ID or number..."
                  value={selectedShipmentId}
                  onChange={(e) => setSelectedShipmentId(e.target.value)}
                />
              </div>
            )}

            {/* Document Number */}
            <div className="space-y-2">
              <Label htmlFor="document-number">Document Number (Optional)</Label>
              <Input
                id="document-number"
                placeholder="Auto-generated if not provided"
                value={documentNumber}
                onChange={(e) => setDocumentNumber(e.target.value)}
              />
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Additional Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Any additional information to include..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>

            {/* Template Selection */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Document Templates</Label>
                <Badge variant="secondary">
                  {selectedTemplateIds.length} selected
                </Badge>
              </div>

              {templatesLoading && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2 text-sm text-slate-600">Loading templates...</span>
                </div>
              )}

              {templatesError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{templatesError}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2 max-h-64 overflow-y-auto">
                {templates.map((template) => {
                  const typeInfo = getDocumentTypeInfo(template.document_type)
                  const isSelected = selectedTemplateIds.includes(template.id)
                  
                  return (
                    <div 
                      key={template.id}
                      className="flex items-center space-x-3 p-3 border rounded-lg bg-slate-700 hover:bg-slate-50 dark:hover:bg-blue-800"
                    >
                      <Checkbox
                        id={template.id}
                        checked={isSelected}
                        onCheckedChange={(checked) => 
                          handleTemplateSelection(template.id, !!checked)
                        }
                        disabled={mode === 'single' && selectedTemplateIds.length > 0 && !isSelected}
                        className="border-2 border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500 data-[state=checked]:text-white focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{typeInfo.icon}</span>
                          <span className="font-medium text-sm text-slate-900 dark:text-white">
                            {template.template_name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary" className={`text-xs ${typeInfo.color}`}>
                            {typeInfo.label}
                          </Badge>
                          {template.description && (
                            <span className="text-xs text-slate-500 truncate">
                              {template.description}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Generate Button */}
            <Button 
              onClick={mode === 'single' ? handleSingleGeneration : handleBulkGeneration}
              disabled={
                isGenerating || 
                !selectedShipmentId || 
                selectedTemplateIds.length === 0
              }
              className="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold disabled:bg-slate-300 disabled:text-slate-500 disabled:opacity-50"
            >
              {isGenerating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Generate {mode === 'single' ? 'Document' : `${selectedTemplateIds.length} Documents`}
            </Button>
          </CardContent>
        </Card>

        {/* Progress and Results Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Generation Status
            </CardTitle>
            <CardDescription>
              Track document generation progress and results
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Generation Progress */}
            {progress && (
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {progress.message}
                  </span>
                  <Badge 
                    variant={progress.status === 'error' ? 'destructive' : 'default'}
                    className="text-xs"
                  >
                    {progress.status}
                  </Badge>
                </div>
                
                <Progress value={progress.progress} className="h-2" />
                
                {progress.currentStep && (
                  <p className="text-xs text-slate-600 dark:text-slate-400">
                    {progress.currentStep}
                  </p>
                )}
              </div>
            )}

            {/* Results */}
            {lastResult && (
              <div className="space-y-4">
                {/* Single Result */}
                {'document' in lastResult && lastResult.success && lastResult.document && (
                  <div className="p-4 border rounded-lg bg-green-50 dark:bg-green-950">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span className="font-medium text-green-900 dark:text-green-100">
                        Document Generated Successfully
                      </span>
                    </div>
                    <div className="text-sm text-green-800 dark:text-green-200">
                      <p><strong>Document:</strong> {lastResult.document.document_name}</p>
                      <p><strong>Type:</strong> {getDocumentTypeInfo(lastResult.document.document_type).label}</p>
                      <p><strong>File:</strong> {lastResult.document.file_name}</p>
                      {lastResult.processingTime && (
                        <p><strong>Processing Time:</strong> {lastResult.processingTime}ms</p>
                      )}
                    </div>
                    <div className="flex gap-2 mt-3">
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handlePreview(lastResult.document)}
                        className="border-slate-400 hover:bg-slate-100 dark:hover:bg-slate-800"
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Preview
                      </Button>
                      <Button 
                        size="sm"
                        onClick={() => handleDownload(lastResult.document)}
                        className="bg-orange-500 hover:bg-orange-600 text-white"
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </div>
                  </div>
                )}

                {/* Bulk Results */}
                {'summary' in lastResult && (
                  <div className="space-y-3">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="h-5 w-5 text-blue-600" />
                        <span className="font-medium text-blue-900 dark:text-blue-100">
                          Bulk Generation Complete
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-slate-600 dark:text-slate-400">Total:</span>
                          <span className="ml-2 font-medium">{lastResult.summary.total}</span>
                        </div>
                        <div>
                          <span className="text-slate-600 dark:text-slate-400">Successful:</span>
                          <span className="ml-2 font-medium text-green-600">{lastResult.summary.successful}</span>
                        </div>
                        <div>
                          <span className="text-slate-600 dark:text-slate-400">Failed:</span>
                          <span className="ml-2 font-medium text-red-600">{lastResult.summary.failed}</span>
                        </div>
                        <div>
                          <span className="text-slate-600 dark:text-slate-400">Time:</span>
                          <span className="ml-2 font-medium">{lastResult.summary.totalProcessingTime}ms</span>
                        </div>
                      </div>
                    </div>

                    {/* Individual Results */}
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {lastResult.results.map((result, index) => {
                        const template = templates.find(t => t.id === result.templateId)
                        const typeInfo = template ? getDocumentTypeInfo(template.document_type) : null
                        
                        return (
                          <div 
                            key={index}
                            className={`p-3 border rounded-lg ${
                              result.success 
                                ? 'bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800' 
                                : 'bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-800'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                {typeInfo && <span className="text-sm">{typeInfo.icon}</span>}
                                <span className="text-sm font-medium">
                                  {template?.template_name || 'Unknown Template'}
                                </span>
                              </div>
                              <Badge 
                                variant={result.success ? 'default' : 'destructive'}
                                className="text-xs"
                              >
                                {result.success ? 'Success' : 'Failed'}
                              </Badge>
                            </div>
                            {result.error && (
                              <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                                {result.error}
                              </p>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Empty State */}
            {!isGenerating && !progress && !lastResult && (
              <div className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-slate-400" />
                <h3 className="mt-2 text-sm font-medium text-slate-900 dark:text-white">
                  Ready to Generate
                </h3>
                <p className="mt-1 text-sm text-slate-500">
                  Configure your settings and click generate to start
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}