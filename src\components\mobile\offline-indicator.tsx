'use client'

import { useState, useEffect } from 'react'
import { Wifi, WifiOff, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

export function OfflineIndicator() {
  const [isOnline, setIsOnline] = useState(true)
  const [showOfflineMessage, setShowOfflineMessage] = useState(false)
  const [isRetrying, setIsRetrying] = useState(false)

  useEffect(() => {
    // Set initial state
    setIsOnline(navigator.onLine)

    const handleOnline = () => {
      setIsOnline(true)
      setShowOfflineMessage(false)
      console.log('Network: Back online')
    }

    const handleOffline = () => {
      setIsOnline(false)
      setShowOfflineMessage(true)
      console.log('Network: Gone offline')
    }

    // Listen to network status changes
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Periodic connectivity check
    const checkConnectivity = async () => {
      try {
        // Try to fetch a small resource
        const response = await fetch('/manifest.json', {
          method: 'HEAD',
          cache: 'no-cache',
        })
        
        const isConnected = response.ok
        if (isConnected !== isOnline) {
          setIsOnline(isConnected)
          setShowOfflineMessage(!isConnected)
        }
      } catch (error) {
        // If fetch fails, we're likely offline
        if (isOnline) {
          setIsOnline(false)
          setShowOfflineMessage(true)
        }
      }
    }

    // Check connectivity every 30 seconds when offline
    const connectivityInterval = setInterval(() => {
      if (!navigator.onLine) {
        checkConnectivity()
      }
    }, 30000)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      clearInterval(connectivityInterval)
    }
  }, [isOnline])

  const handleRetry = async () => {
    setIsRetrying(true)
    
    try {
      // Try to fetch a small resource to test connectivity
      const response = await fetch('/manifest.json', {
        method: 'HEAD',
        cache: 'no-cache',
      })
      
      if (response.ok) {
        setIsOnline(true)
        setShowOfflineMessage(false)
        // Trigger a page refresh to sync data
        window.location.reload()
      } else {
        throw new Error('Connection test failed')
      }
    } catch (error) {
      console.log('Retry failed, still offline')
      // Keep showing offline state
    } finally {
      setIsRetrying(false)
    }
  }

  // Auto-hide the offline message after it's been shown
  useEffect(() => {
    if (showOfflineMessage) {
      const timer = setTimeout(() => {
        setShowOfflineMessage(false)
      }, 5000) // Hide after 5 seconds
      
      return () => clearTimeout(timer)
    }
  }, [showOfflineMessage])

  if (isOnline && !showOfflineMessage) {
    return null
  }

  return (
    <>
      {/* Persistent offline indicator in header */}
      {!isOnline && (
        <div className="bg-amber-600 text-white px-4 py-2 text-center text-sm font-medium">
          <div className="flex items-center justify-center space-x-2">
            <WifiOff className="w-4 h-4" />
            <span>Working Offline</span>
          </div>
        </div>
      )}

      {/* Dismissible offline toast */}
      {showOfflineMessage && (
        <div className="fixed top-4 left-4 right-4 z-50 animate-in slide-in-from-top-2">
          <div className="bg-slate-800 border border-amber-600 rounded-lg p-4 shadow-xl">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <WifiOff className="w-5 h-5 text-amber-500 mt-0.5" />
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-semibold text-white mb-1">
                  Connection Lost
                </h3>
                <p className="text-xs text-slate-300 mb-3">
                  You're now offline. Some features may be limited until you reconnect.
                </p>
                
                <div className="flex space-x-2">
                  <Button
                    onClick={handleRetry}
                    disabled={isRetrying}
                    size="sm"
                    variant="outline"
                    className="bg-transparent border-amber-600 text-amber-500 hover:bg-amber-600/10 text-xs h-7"
                  >
                    {isRetrying ? (
                      <>
                        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                        Checking...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-3 h-3 mr-1" />
                        Retry
                      </>
                    )}
                  </Button>
                  
                  <Button
                    onClick={() => setShowOfflineMessage(false)}
                    variant="ghost"
                    size="sm"
                    className="text-slate-400 hover:text-white hover:bg-slate-700 text-xs h-7"
                  >
                    Dismiss
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Connection restored notification */}
      {isOnline && showOfflineMessage && (
        <div className="fixed top-4 left-4 right-4 z-50 animate-in slide-in-from-top-2">
          <div className="bg-slate-800 border border-green-600 rounded-lg p-4 shadow-xl">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Wifi className="w-5 h-5 text-green-500" />
              </div>
              
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-white">
                  Connection Restored
                </h3>
                <p className="text-xs text-slate-300">
                  You're back online. Data will sync automatically.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}