/**
 * @file product-selector-cards.test.tsx
 * @description Unit tests for ProductSelectorCards component
 */

import { describe, test, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ProductSelectorCards } from '@/components/shipments/product-selector-cards'
import type { CustomerProductRelationship } from '@/hooks/use-shipment-relationships'

describe('ProductSelectorCards', () => {
  const mockOnProductSelect = vi.fn()
  const mockCustomerName = 'Test Customer'

  const mockSingleProduct: CustomerProductRelationship = {
    id: '1',
    customer_id: '123',
    product_id: 'prod-1',
    product: {
      id: 'prod-1',
      name: 'Organic Apple',
      code: 'APPLE001',
      unit_of_measure_id: 'kg',
    },
    is_default: true,
    is_active: true,
    unit_price_cif: 100,
    unit_price_fob: 90,
    currency_code: 'USD',
    quality_grade: 'Grade A',
    notes: null,
    created_at: '2023-01-01T00:00:00Z',
  }

  const mockMultipleProducts: CustomerProductRelationship[] = [
    {
      id: '1',
      customer_id: '123',
      product_id: 'prod-1',
      product: {
        id: 'prod-1',
        name: 'Organic Apple',
        code: 'APPLE001',
        unit_of_measure_id: 'kg',
      },
      is_default: true,
      is_active: true,
      unit_price_cif: 100,
      unit_price_fob: 90,
      currency_code: 'USD',
      quality_grade: 'Grade A',
      notes: null,
      created_at: '2023-01-01T00:00:00Z',
    },
    {
      id: '2',
      customer_id: '123',
      product_id: 'prod-2',
      product: {
        id: 'prod-2',
        name: 'Fresh Orange',
        code: 'ORANGE001',
        unit_of_measure_id: 'kg',
      },
      is_default: false,
      is_active: true,
      unit_price_cif: 80,
      unit_price_fob: 75,
      currency_code: 'USD',
      quality_grade: 'Grade B',
      notes: null,
      created_at: '2023-01-02T00:00:00Z',
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Single Product Mode', () => {
    test('renders single product with auto-selected state', () => {
      render(
        <ProductSelectorCards
          products={[mockSingleProduct]}
          selectedProductId="prod-1"
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      expect(screen.getByText('Product for Test Customer (Auto-selected)')).toBeInTheDocument()
      expect(screen.getByText('Organic Apple')).toBeInTheDocument()
      expect(screen.getByText('Code: APPLE001')).toBeInTheDocument()
      expect(screen.getByText('Auto-selected')).toBeInTheDocument()
      expect(screen.getByText('Default')).toBeInTheDocument()
      expect(screen.getByText('CIF: $100 USD')).toBeInTheDocument()
      expect(screen.getByText('FOB: $90 USD')).toBeInTheDocument()
      expect(screen.getByText('Quality: Grade A')).toBeInTheDocument()
    })

    test('auto-selects single product on mount', async () => {
      render(
        <ProductSelectorCards
          products={[mockSingleProduct]}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      await waitFor(() => {
        expect(mockOnProductSelect).toHaveBeenCalledWith('prod-1', mockSingleProduct)
      }, { timeout: 200 })
    })
  })

  describe('Multiple Products Mode', () => {
    test('renders multiple products with selection interface', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      expect(screen.getByText('Select Product for Test Customer')).toBeInTheDocument()
      expect(screen.getByText('Choose one product to include in this shipment.')).toBeInTheDocument()
      expect(screen.getByText('Organic Apple')).toBeInTheDocument()
      expect(screen.getByText('Fresh Orange')).toBeInTheDocument()
    })

    test('handles product selection correctly', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      const appleCard = screen.getByText('Organic Apple').closest('[role]') // Find closest card container
      if (appleCard) {
        fireEvent.click(appleCard)
        expect(mockOnProductSelect).toHaveBeenCalledWith('prod-1', mockMultipleProducts[0])
      }
    })

    test('shows selected state for chosen product', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId="prod-2"
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      expect(screen.getByText('✓ Selected for shipment')).toBeInTheDocument()
      expect(screen.getByText('Product selected: Fresh Orange')).toBeInTheDocument()
    })

    test('highlights default product badge', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      // Default badge should only appear for the first product
      const defaultBadges = screen.getAllByText('Default')
      expect(defaultBadges).toHaveLength(1)
    })
  })

  describe('Loading State', () => {
    test('renders loading placeholders', () => {
      render(
        <ProductSelectorCards
          products={[]}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
          loading={true}
        />
      )

      expect(screen.getByText('Loading products...')).toBeInTheDocument()
      // Should render 3 loading placeholder cards
      const loadingCards = screen.container.querySelectorAll('.animate-pulse')
      expect(loadingCards).toHaveLength(3)
    })
  })

  describe('Empty State', () => {
    test('renders empty state when no products available', () => {
      render(
        <ProductSelectorCards
          products={[]}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      expect(screen.getByText('No product relationships configured for this customer.')).toBeInTheDocument()
      expect(screen.getByText('Please set up customer-product relationships in Master Data.')).toBeInTheDocument()
    })
  })

  describe('Disabled State', () => {
    test('prevents selection when disabled', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
          disabled={true}
        />
      )

      const appleCard = screen.getByText('Organic Apple').closest('[role]')
      if (appleCard) {
        fireEvent.click(appleCard)
        expect(mockOnProductSelect).not.toHaveBeenCalled()
      }
    })
  })

  describe('Compact Mode', () => {
    test('renders in compact layout', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
          compact={true}
        />
      )

      const container = screen.container.querySelector('.grid')
      expect(container).toHaveClass('grid-cols-1', 'lg:grid-cols-2')
    })

    test('hides detailed pricing in compact mode', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
          compact={true}
        />
      )

      // In compact mode, detailed pricing should be hidden
      expect(screen.queryByText('CIF: $100 USD')).not.toBeInTheDocument()
      expect(screen.queryByText('FOB: $90 USD')).not.toBeInTheDocument()
    })
  })

  describe('Show Details', () => {
    test('shows product details when showDetails is true', () => {
      render(
        <ProductSelectorCards
          products={[mockSingleProduct]}
          selectedProductId="prod-1"
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
          showDetails={true}
        />
      )

      expect(screen.getByText('CIF: $100 USD')).toBeInTheDocument()
      expect(screen.getByText('FOB: $90 USD')).toBeInTheDocument()
      expect(screen.getByText('Quality: Grade A')).toBeInTheDocument()
    })

    test('hides product details when showDetails is false', () => {
      render(
        <ProductSelectorCards
          products={[mockSingleProduct]}
          selectedProductId="prod-1"
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
          showDetails={false}
        />
      )

      expect(screen.queryByText('CIF: $100 USD')).not.toBeInTheDocument()
      expect(screen.queryByText('FOB: $90 USD')).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    test('provides proper hover feedback', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId={null}
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      const appleCard = screen.getByText('Organic Apple').closest('[role]')
      if (appleCard) {
        fireEvent.mouseEnter(appleCard)
        // Card should have hover styling applied
        expect(appleCard).toHaveClass('cursor-pointer')
      }
    })

    test('shows visual selection indicators', () => {
      render(
        <ProductSelectorCards
          products={mockMultipleProducts}
          selectedProductId="prod-1"
          onProductSelect={mockOnProductSelect}
          customerName={mockCustomerName}
        />
      )

      // Selected card should have visual indicators
      const selectedCard = screen.getByText('Organic Apple').closest('[role]')
      expect(selectedCard).toHaveClass('border-orange-400')
    })
  })
})