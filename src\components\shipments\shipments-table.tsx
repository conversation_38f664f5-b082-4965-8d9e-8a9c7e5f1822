'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useShipmentStore } from '@/stores/shipment-store'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  Edit,
  MoreHorizontal,
} from 'lucide-react'
import type { ShipmentWithRelations, ShipmentStatus } from '@/lib/supabase/types'
import { StatusBadge } from '@/components/data-display/status-badge'
import { format } from 'date-fns'

interface ShipmentsTableProps {
  shipments: ShipmentWithRelations[]
  isLoading: boolean
  onSort: (field: keyof ShipmentWithRelations, direction: 'asc' | 'desc') => void
}

// Status badge colors
const getStatusColor = (status: string | null) => {
  switch (status) {
    case 'booking_confirmed':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'transport_assigned':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
    case 'driver_assigned':
      return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400'
    case 'loading_started':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'shipped':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
    case 'arrived':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'completed':
      return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400'
    case 'cancelled':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

// Format date helper
const formatDate = (date: string | null) => {
  if (!date) return '-'
  try {
    return format(new Date(date), 'MMM dd, yyyy')
  } catch {
    return '-'
  }
}

// Sortable column header component
interface SortableHeaderProps {
  field: keyof ShipmentWithRelations
  label: string
  currentSort: { field: keyof ShipmentWithRelations; direction: 'asc' | 'desc' }
  onSort: (field: keyof ShipmentWithRelations, direction: 'asc' | 'desc') => void
}

function SortableHeader({ field, label, currentSort, onSort }: SortableHeaderProps) {
  const isActive = currentSort.field === field
  const direction = isActive ? currentSort.direction : null

  const handleClick = () => {
    if (isActive) {
      onSort(field, direction === 'asc' ? 'desc' : 'asc')
    } else {
      onSort(field, 'asc')
    }
  }

  return (
    <button
      onClick={handleClick}
      className="flex items-center space-x-1 hover:text-white transition-colors"
    >
      <span>{label}</span>
      {isActive ? (
        direction === 'asc' ? (
          <ArrowUp className="h-4 w-4" />
        ) : (
          <ArrowDown className="h-4 w-4" />
        )
      ) : (
        <ArrowUpDown className="h-4 w-4 opacity-50" />
      )}
    </button>
  )
}

export function ShipmentsTable({ shipments, isLoading, onSort }: ShipmentsTableProps) {
  const router = useRouter()
  
  const {
    selectedShipments,
    pagination,
    sort,
    selectShipment,
    deselectShipment,
    selectAllShipments,
    deselectAllShipments,
    toggleShipmentSelection,
    setPage,
    setPageSize,
    nextPage,
    previousPage,
    setSort,
  } = useShipmentStore()

  const handleSort = (field: keyof ShipmentWithRelations, direction: 'asc' | 'desc') => {
    setSort(field, direction)
    onSort(field, direction)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      selectAllShipments()
    } else {
      deselectAllShipments()
    }
  }

  const isAllSelected = shipments.length > 0 && shipments.every(s => selectedShipments.has(s.id))
  const isIndeterminate = selectedShipments.size > 0 && !isAllSelected

  if (isLoading && shipments.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4" />
          <p className="text-slate-400">Loading shipments...</p>
        </div>
      </div>
    )
  }

  if (shipments.length === 0 && !isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="text-slate-500 mb-4">
            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-7 7-7-7" />
            </svg>
          </div>
          <p className="text-slate-300 text-lg font-medium">No shipments found</p>
          <p className="text-slate-500 mt-1">Try adjusting your search or filters</p>
          <Button 
            className="mt-4" 
            onClick={() => router.push('/shipments/create')}
          >
            Create New Shipment
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border border-slate-700 overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="border-slate-700 hover:bg-slate-800">
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  {...(isIndeterminate && { indeterminate: true })}
                  onCheckedChange={handleSelectAll}
                  className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
              </TableHead>
              <TableHead className="text-slate-300">
                <SortableHeader
                  field="shipment_number"
                  label="Shipment #"
                  currentSort={sort}
                  onSort={handleSort}
                />
              </TableHead>
              <TableHead className="text-slate-300">
                <SortableHeader
                  field="status"
                  label="Status"
                  currentSort={sort}
                  onSort={handleSort}
                />
              </TableHead>
              <TableHead className="text-slate-300">Customer</TableHead>
              <TableHead className="text-slate-300">Origin → Destination</TableHead>
              <TableHead className="text-slate-300">
                <SortableHeader
                  field="etd_date"
                  label="ETD"
                  currentSort={sort}
                  onSort={handleSort}
                />
              </TableHead>
              <TableHead className="text-slate-300">
                <SortableHeader
                  field="eta_date"
                  label="ETA"
                  currentSort={sort}
                  onSort={handleSort}
                />
              </TableHead>
              <TableHead className="text-slate-300">Transport</TableHead>
              <TableHead className="text-slate-300">Containers</TableHead>
              <TableHead className="text-slate-300 w-12">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {shipments.map((shipment) => {
              // Check if shipment was recently updated (within last 30 seconds)
              const isRecentlyUpdated = shipment.updated_at && 
                new Date().getTime() - new Date(shipment.updated_at).getTime() < 30000
              
              return (
                <TableRow
                  key={shipment.id}
                  className={`border-slate-700 hover:bg-slate-800 cursor-pointer ${
                    isRecentlyUpdated ? 'bg-blue-900/10 border-l-4 border-l-blue-500' : ''
                  }`}
                  onClick={() => router.push(`/shipments/${shipment.id}`)}
                >
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    checked={selectedShipments.has(shipment.id)}
                    onCheckedChange={() => toggleShipmentSelection(shipment.id)}
                    className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                  />
                </TableCell>
                
                <TableCell className="font-medium text-white">
                  {shipment.shipment_number}
                  {shipment.invoice_number && (
                    <div className="text-sm text-slate-400">
                      INV: {shipment.invoice_number}
                    </div>
                  )}
                </TableCell>
                
                <TableCell>
                  <StatusBadge 
                    status={shipment.status as ShipmentStatus} 
                    size="sm"
                    showIcon
                  />
                </TableCell>
                
                <TableCell className="text-slate-300">
                  {shipment.customer?.name || '-'}
                </TableCell>
                
                <TableCell className="text-slate-300">
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <span>{shipment.origin_port?.code || '---'}</span>
                      <span className="mx-2 text-slate-500">→</span>
                      <span>{shipment.destination_port?.code || '---'}</span>
                    </div>
                    {(shipment.origin_port?.name || shipment.destination_port?.name) && (
                      <div className="text-xs text-slate-500">
                        {shipment.origin_port?.name} → {shipment.destination_port?.name}
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell className="text-slate-300">
                  {formatDate(shipment.etd_date)}
                </TableCell>
                
                <TableCell className="text-slate-300">
                  {formatDate(shipment.eta_date)}
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    {shipment.transportation_mode?.toUpperCase() || 'N/A'}
                  </Badge>
                </TableCell>
                
                <TableCell className="text-slate-300">
                  <div className="flex items-center space-x-1">
                    <span className="text-sm">
                      {shipment.containers?.length || 0}
                    </span>
                    {shipment.containers && shipment.containers.length > 0 && (
                      <div className="flex -space-x-1">
                        {shipment.containers.slice(0, 2).map((container, index) => (
                          <div
                            key={container.id}
                            className="w-2 h-2 rounded-full bg-blue-500 border border-slate-800"
                            title={container.container_number || 'Container'}
                          />
                        ))}
                        {shipment.containers.length > 2 && (
                          <div className="w-2 h-2 rounded-full bg-slate-500 border border-slate-800" />
                        )}
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-slate-400 hover:text-white hover:bg-slate-700"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            {/* Page size selector and info */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-slate-300">Show</span>
                <Select
                  value={pagination.pageSize.toString()}
                  onValueChange={(value) => setPageSize(parseInt(value))}
                >
                  <SelectTrigger className="w-20 bg-slate-700 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-slate-300">per page</span>
              </div>
              
              <div className="text-sm text-slate-300">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of{' '}
                {pagination.totalCount} results
              </div>
            </div>

            {/* Pagination controls */}
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setPage(1)}
                disabled={pagination.page <= 1}
                className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:border-slate-600 disabled:text-slate-500 disabled:bg-slate-800/50"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={previousPage}
                disabled={pagination.page <= 1}
                className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:border-slate-600 disabled:text-slate-500 disabled:bg-slate-800/50"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <span className="text-sm text-slate-300 px-2">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              
              <Button
                size="sm"
                variant="outline"
                onClick={nextPage}
                disabled={pagination.page >= pagination.totalPages}
                className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:border-slate-600 disabled:text-slate-500 disabled:bg-slate-800/50"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={() => setPage(pagination.totalPages)}
                disabled={pagination.page >= pagination.totalPages}
                className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed disabled:border-slate-600 disabled:text-slate-500 disabled:bg-slate-800/50"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}