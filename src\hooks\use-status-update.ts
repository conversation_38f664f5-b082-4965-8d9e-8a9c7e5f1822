'use client'

import { useState, useCallback, useRef, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { 
  validatePhotosForStatus, 
  getPhotoRequirements,
  getStatusDisplayName 
} from '@/lib/utils/status-workflow'
import { LocationService } from '@/lib/utils/location-service'
import type { 
  ShipmentStatus, 
  StatusUpdateRequest, 
  GPSCoordinates,
  OfflineStatusUpdate 
} from '@/types/status-update'

interface UseStatusUpdateOptions {
  onSuccess?: (result: any) => void
  onError?: (error: string) => void
  enableOfflineQueue?: boolean
}

interface StatusUpdateState {
  isSubmitting: boolean
  isUploading: boolean
  uploadProgress: number
  error: string | null
  success: boolean
}

export function useStatusUpdate(options: UseStatusUpdateOptions = {}) {
  const [state, setState] = useState<StatusUpdateState>({
    isSubmitting: false,
    isUploading: false,
    uploadProgress: 0,
    error: null,
    success: false
  })

  const supabase = createClient()
  const abortControllerRef = useRef<AbortController | null>(null)
  
  // Memoize the options to prevent infinite re-renders
  const stableOptions = useMemo(() => options, [
    options.onSuccess,
    options.onError,
    options.enableOfflineQueue
  ])

  const validateStatusUpdate = useCallback((
    toStatus: ShipmentStatus,
    photos: File[],
    coordinates: GPSCoordinates | null
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    // Validate photo requirements
    const photoValidation = validatePhotosForStatus(toStatus, photos.length)
    if (!photoValidation.isValid) {
      errors.push(photoValidation.message)
    }

    // Validate GPS coordinates (required for all status updates)
    if (!coordinates) {
      errors.push('GPS location is required for status updates')
    }

    // Validate individual photos
    const maxFileSize = 10 * 1024 * 1024 // 10MB
    const acceptedFormats = ['image/jpeg', 'image/png', 'image/heic', 'image/webp']
    
    photos.forEach((photo, index) => {
      if (photo.size > maxFileSize) {
        errors.push(`Photo ${index + 1} is too large (max 10MB)`)
      }
      
      if (!acceptedFormats.includes(photo.type)) {
        errors.push(`Photo ${index + 1} has invalid format. Use JPEG, PNG, HEIC, or WebP`)
      }
    })

    return {
      isValid: errors.length === 0,
      errors
    }
  }, [])

  const uploadStatusPhotos = useCallback(async (
    statusHistoryId: string,
    shipmentId: string,
    photos: File[]
  ): Promise<any[]> => {
    const uploadResults = []
    let completedUploads = 0

    for (let i = 0; i < photos.length; i++) {
      const photo = photos[i]
      
      try {
        // Create unique filename
        const timestamp = Date.now()
        const randomId = Math.random().toString(36).substr(2, 9)
        const fileExtension = photo.name.split('.').pop() || 'jpg'
        const fileName = `${statusHistoryId}_${timestamp}_${randomId}.${fileExtension}`
        const filePath = `status-photos/${shipmentId}/${fileName}`

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('status-images')
          .upload(filePath, photo, {
            cacheControl: '3600',
            upsert: false
          })

        if (uploadError) {
          throw uploadError
        }

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('status-images')
          .getPublicUrl(filePath)

        // Create status_images record
        const { data: imageRecord, error: recordError } = await supabase
          .from('status_images')
          .insert({
            shipment_id: shipmentId,
            status_history_id: statusHistoryId,
            image_url: urlData.publicUrl,
            image_path: filePath,
            file_size: photo.size,
            mime_type: photo.type,
            metadata: {
              original_name: photo.name,
              uploaded_at: new Date().toISOString(),
              photo_order: i + 1
            }
          })
          .select()
          .single()

        if (recordError) {
          throw recordError
        }

        uploadResults.push(imageRecord)
        completedUploads++

        // Update progress
        setState(prev => ({
          ...prev,
          uploadProgress: Math.round((completedUploads / photos.length) * 100)
        }))
        
      } catch (error) {
        console.error(`Failed to upload photo ${i + 1}:`, error)
        throw new Error(`Failed to upload photo ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return uploadResults
  }, [supabase])

  const createStatusHistory = useCallback(async (
    shipmentId: string,
    statusUpdate: {
      status_to: ShipmentStatus
      status_from?: ShipmentStatus
      notes?: string
      coordinates: GPSCoordinates
      location?: string
    }
  ): Promise<any> => {
    const { data: user } = await supabase.auth.getUser()
    
    if (!user.user) {
      throw new Error('User not authenticated')
    }

    const { data: statusHistory, error } = await supabase
      .from('status_history')
      .insert({
        shipment_id: shipmentId,
        status_to: statusUpdate.status_to,
        status_from: statusUpdate.status_from,
        notes: statusUpdate.notes,
        latitude: statusUpdate.coordinates.latitude,
        longitude: statusUpdate.coordinates.longitude,
        gps_coordinates: LocationService.formatForDatabase(statusUpdate.coordinates),
        location: statusUpdate.location || LocationService.formatCoordinatesDisplay(
          statusUpdate.coordinates.latitude, 
          statusUpdate.coordinates.longitude
        ),
        updated_by: user.user.id
      })
      .select()
      .single()

    if (error) {
      throw error
    }

    return statusHistory
  }, [supabase])

  const updateShipmentStatus = useCallback(async (
    shipmentId: string,
    status: ShipmentStatus
  ): Promise<void> => {
    const { error } = await supabase
      .from('shipments')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', shipmentId)

    if (error) {
      throw error
    }
  }, [supabase])

  const queueOfflineUpdate = useCallback(async (
    shipmentId: string,
    statusUpdate: StatusUpdateRequest
  ): Promise<void> => {
    try {
      const offlineUpdate: OfflineStatusUpdate = {
        id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        shipment_id: shipmentId,
        status_update: {
          status_to: statusUpdate.status_to,
          status_from: statusUpdate.status_from,
          notes: statusUpdate.notes,
          latitude: statusUpdate.latitude,
          longitude: statusUpdate.longitude,
          location: statusUpdate.location
        },
        photos: statusUpdate.photos,
        timestamp: Date.now(),
        sync_status: 'pending',
        retry_count: 0
      }

      // Store in IndexedDB using OfflineStorageService
      const { OfflineStorageService } = await import('@/lib/utils/offline-storage')
      await OfflineStorageService.storeStatusUpdate(offlineUpdate)
      
      setState(prev => ({
        ...prev,
        success: true
      }))

      stableOptions.onSuccess?.(offlineUpdate)
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to queue offline update'
      
      setState(prev => ({
        ...prev,
        error: errorMessage
      }))

      stableOptions.onError?.(errorMessage)
    }
  }, [stableOptions])

  const submitStatusUpdate = useCallback(async (
    shipmentId: string,
    statusUpdate: StatusUpdateRequest
  ): Promise<void> => {
    // Create abort controller for this request
    const abortController = new AbortController()
    abortControllerRef.current = abortController

    try {
      setState(prev => ({
        ...prev,
        isSubmitting: true,
        isUploading: false,
        uploadProgress: 0,
        error: null,
        success: false
      }))

      // Validate the status update
      const validation = validateStatusUpdate(
        statusUpdate.status_to, 
        statusUpdate.photos,
        statusUpdate.coordinates || null
      )

      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      // Check if device is online
      const isOnline = navigator.onLine

      if (!isOnline || stableOptions.enableOfflineQueue) {
        // Queue update for offline sync
        await queueOfflineUpdate(shipmentId, statusUpdate)
        
        setState(prev => ({
          ...prev,
          isSubmitting: false,
          success: true
        }))
        
        return
      }

      // Check if request was aborted
      if (abortController.signal.aborted) {
        return
      }

      // Create status history record
      const statusHistory = await createStatusHistory(shipmentId, {
        status_to: statusUpdate.status_to,
        status_from: statusUpdate.status_from,
        notes: statusUpdate.notes,
        coordinates: statusUpdate.coordinates!,
        location: statusUpdate.location
      })

      // Check if request was aborted
      if (abortController.signal.aborted) {
        return
      }

      // Upload photos if any
      if (statusUpdate.photos.length > 0) {
        setState(prev => ({ ...prev, isUploading: true }))
        
        await uploadStatusPhotos(statusHistory.id, shipmentId, statusUpdate.photos)
        
        setState(prev => ({ ...prev, isUploading: false }))
      }

      // Check if request was aborted
      if (abortController.signal.aborted) {
        return
      }

      // Update shipment status
      await updateShipmentStatus(shipmentId, statusUpdate.status_to)

      setState(prev => ({
        ...prev,
        isSubmitting: false,
        success: true
      }))

      stableOptions.onSuccess?.(statusHistory)

    } catch (error) {
      if (abortController.signal.aborted) {
        return
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to update status'
      
      // If network error and offline queue is enabled, try to queue the update
      if (!navigator.onLine && stableOptions.enableOfflineQueue) {
        try {
          await queueOfflineUpdate(shipmentId, statusUpdate)
          
          setState(prev => ({
            ...prev,
            isSubmitting: false,
            success: true
          }))
          
          return
        } catch (queueError) {
          console.error('Failed to queue offline update:', queueError)
        }
      }
      
      setState(prev => ({
        ...prev,
        isSubmitting: false,
        isUploading: false,
        error: errorMessage
      }))

      stableOptions.onError?.(errorMessage)
    } finally {
      abortControllerRef.current = null
    }
  }, [
    validateStatusUpdate,
    createStatusHistory,
    uploadStatusPhotos,
    updateShipmentStatus,
    queueOfflineUpdate,
    stableOptions
  ])

  const cancelUpdate = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    
    setState(prev => ({
      ...prev,
      isSubmitting: false,
      isUploading: false,
      uploadProgress: 0,
      error: null
    }))
  }, [])

  const resetState = useCallback(() => {
    setState({
      isSubmitting: false,
      isUploading: false,
      uploadProgress: 0,
      error: null,
      success: false
    })
  }, [])

  return {
    ...state,
    submitStatusUpdate,
    queueOfflineUpdate,
    cancelUpdate,
    resetState,
    validateStatusUpdate
  }
}