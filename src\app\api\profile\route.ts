import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

// Create Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
)

// Create regular client for token verification
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization?.startsWith('Bearer ')) {
      console.error('Profile API: Missing or invalid authorization header')
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Missing or invalid authorization header' 
      }, { status: 401 })
    }

    const token = authorization.split(' ')[1]
    if (!token) {
      console.error('Profile API: Empty authorization token')
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Empty authorization token' 
      }, { status: 401 })
    }

    // Verify user is authenticated and get user info
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token)
    
    if (authError) {
      console.error('Profile API: Auth error when verifying token:', authError)
      return NextResponse.json({ 
        error: 'Invalid token', 
        details: authError.message 
      }, { status: 401 })
    }
    
    if (!user) {
      console.error('Profile API: No user found for token')
      return NextResponse.json({ 
        error: 'Invalid token', 
        details: 'No user found for provided token' 
      }, { status: 401 })
    }

    console.log('Profile API: Successfully verified user:', user.id)

    // Load profile using service role (bypasses RLS circular dependency)
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (profileError) {
      console.error('Error loading profile with service role:', profileError)
      
      // If profile doesn't exist, create a basic one
      if (profileError.code === 'PGRST116') { // No rows returned
        console.log('Profile API: No profile found, creating basic profile for user:', user.id)
        const { data: newProfile, error: createError } = await supabaseAdmin
          .from('profiles')
          .insert({
            user_id: user.id,
            email: user.email,
            first_name: user.user_metadata?.first_name || '',
            last_name: user.user_metadata?.last_name || '',
            role: 'customer', // Default role
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single()
          
        if (createError) {
          console.error('Error creating profile:', createError)
          return NextResponse.json({ 
            error: 'Profile creation failed', 
            details: createError.message 
          }, { status: 500 })
        }
        
        return NextResponse.json({ profile: newProfile })
      }
      
      return NextResponse.json({ 
        error: 'Profile not found', 
        details: profileError.message 
      }, { status: 404 })
    }

    console.log('Profile API: Successfully loaded profile for user:', user.id)
    return NextResponse.json({ profile })
  } catch (error) {
    console.error('Profile API Error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
