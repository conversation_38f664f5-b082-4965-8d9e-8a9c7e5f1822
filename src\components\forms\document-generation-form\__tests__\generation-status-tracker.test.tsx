/**
 * Generation Status Tracker Component Tests
 * Story 5.2: Automated Document Generation Engine
 */

import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { GenerationStatusTracker, MiniStatusIndicator } from '../generation-status-tracker'
import type { GenerationProgress, GenerationStatus } from '@/types/document-generation'

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  CheckCircle: () => <div data-testid="check-circle-icon">CheckCircle</div>,
  AlertCircle: () => <div data-testid="alert-circle-icon">AlertCircle</div>,
  Clock: () => <div data-testid="clock-icon">Clock</div>,
  Loader2: () => <div data-testid="loader2-icon">Loader2</div>,
  FileText: () => <div data-testid="file-text-icon">FileText</div>,
  Upload: () => <div data-testid="upload-icon">Upload</div>,
  Database: () => <div data-testid="database-icon">Database</div>
}))

describe('GenerationStatusTracker', () => {
  describe('Rendering', () => {
    it('should return null when no progress is provided', () => {
      const { container } = render(<GenerationStatusTracker progress={null} />)
      expect(container.firstChild).toBeNull()
    })

    it('should render status tracker with progress information', () => {
      const progress: GenerationProgress = {
        status: 'generating',
        progress: 50,
        message: 'Generating PDF document...',
        currentStep: 'Creating PDF structure'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByText('Generation Status')).toBeInTheDocument()
      expect(screen.getByText('Generating PDF document...')).toBeInTheDocument()
      expect(screen.getByText('Creating PDF structure')).toBeInTheDocument()
      expect(screen.getByText('50%')).toBeInTheDocument()
      expect(screen.getByText('generating')).toBeInTheDocument()
    })

    it('should apply custom className when provided', () => {
      const progress: GenerationProgress = {
        status: 'idle',
        progress: 0,
        message: 'Ready to generate'
      }

      const { container } = render(
        <GenerationStatusTracker progress={progress} className="custom-class" />
      )
      
      expect(container.firstChild).toHaveClass('custom-class')
    })
  })

  describe('Status Icons and Colors', () => {
    it('should show correct icon and colors for idle status', () => {
      const progress: GenerationProgress = {
        status: 'idle',
        progress: 0,
        message: 'Waiting to start'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByTestId('clock-icon')).toBeInTheDocument()
      expect(screen.getByText('idle')).toBeInTheDocument()
    })

    it('should show correct icon and colors for preparing status', () => {
      const progress: GenerationProgress = {
        status: 'preparing',
        progress: 10,
        message: 'Preparing document generation'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByTestId('database-icon')).toBeInTheDocument()
      expect(screen.getByText('preparing')).toBeInTheDocument()
    })

    it('should show correct icon and colors for generating status', () => {
      const progress: GenerationProgress = {
        status: 'generating',
        progress: 60,
        message: 'Generating PDF'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByTestId('file-text-icon')).toBeInTheDocument()
      expect(screen.getByText('generating')).toBeInTheDocument()
    })

    it('should show correct icon and colors for saving status', () => {
      const progress: GenerationProgress = {
        status: 'saving',
        progress: 90,
        message: 'Saving document to storage'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByTestId('upload-icon')).toBeInTheDocument()
      expect(screen.getByText('saving')).toBeInTheDocument()
    })

    it('should show correct icon and colors for complete status', () => {
      const progress: GenerationProgress = {
        status: 'complete',
        progress: 100,
        message: 'Document generated successfully'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByTestId('check-circle-icon')).toBeInTheDocument()
      expect(screen.getByText('complete')).toBeInTheDocument()
      expect(screen.getByText('Document generation completed successfully!')).toBeInTheDocument()
    })

    it('should show correct icon and colors for error status', () => {
      const progress: GenerationProgress = {
        status: 'error',
        progress: 0,
        message: 'Generation failed',
        error: 'Network connection error'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByTestId('alert-circle-icon')).toBeInTheDocument()
      expect(screen.getByText('error')).toBeInTheDocument()
      expect(screen.getByText('Error Details:')).toBeInTheDocument()
      expect(screen.getByText('Network connection error')).toBeInTheDocument()
    })
  })

  describe('Progress Display', () => {
    it('should display progress percentage correctly', () => {
      const progress: GenerationProgress = {
        status: 'generating',
        progress: 73.5,
        message: 'Processing...'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByText('74%')).toBeInTheDocument() // Rounded
    })

    it('should display current step when provided', () => {
      const progress: GenerationProgress = {
        status: 'generating',
        progress: 25,
        message: 'Processing template',
        currentStep: 'Populating template fields'
      }

      render(<GenerationStatusTracker progress={progress} />)
      
      expect(screen.getByText('Populating template fields')).toBeInTheDocument()
    })

    it('should not display current step when not provided', () => {
      const progress: GenerationProgress = {
        status: 'generating',
        progress: 25,
        message: 'Processing template'
      }

      const { container } = render(<GenerationStatusTracker progress={progress} />)
      
      // Should not have the current step indicator element
      expect(container.querySelector('.w-2.h-2.rounded-full')).not.toBeInTheDocument()
    })
  })

  describe('Processing Animation', () => {
    it('should show processing animation for active statuses', () => {
      const activeStatuses: GenerationStatus[] = ['preparing', 'generating', 'saving']

      activeStatuses.forEach(status => {
        const { container } = render(
          <GenerationStatusTracker 
            progress={{
              status,
              progress: 50,
              message: `Status: ${status}`
            }} 
          />
        )
        
        // Check for animation dots
        const animationDots = container.querySelectorAll('.animate-bounce')
        expect(animationDots).toHaveLength(3)
      })
    })

    it('should not show processing animation for idle status', () => {
      const { container } = render(
        <GenerationStatusTracker 
          progress={{
            status: 'idle',
            progress: 0,
            message: 'Ready'
          }} 
        />
      )
      
      const animationDots = container.querySelectorAll('.animate-bounce')
      expect(animationDots).toHaveLength(0)
    })

    it('should not show processing animation for complete status', () => {
      const { container } = render(
        <GenerationStatusTracker 
          progress={{
            status: 'complete',
            progress: 100,
            message: 'Done'
          }} 
        />
      )
      
      const animationDots = container.querySelectorAll('.animate-bounce')
      expect(animationDots).toHaveLength(0)
    })

    it('should not show processing animation for error status', () => {
      const { container } = render(
        <GenerationStatusTracker 
          progress={{
            status: 'error',
            progress: 0,
            message: 'Failed',
            error: 'Some error'
          }} 
        />
      )
      
      const animationDots = container.querySelectorAll('.animate-bounce')
      expect(animationDots).toHaveLength(0)
    })
  })
})

describe('MiniStatusIndicator', () => {
  describe('Rendering', () => {
    it('should render mini status indicator with basic information', () => {
      render(
        <MiniStatusIndicator 
          status="generating" 
          progress={75} 
        />
      )
      
      expect(screen.getByTestId('file-text-icon')).toBeInTheDocument()
      expect(screen.getByText('generating')).toBeInTheDocument()
      expect(screen.getByText('75%')).toBeInTheDocument()
    })

    it('should apply custom className when provided', () => {
      const { container } = render(
        <MiniStatusIndicator 
          status="complete" 
          progress={100} 
          className="custom-mini-class"
        />
      )
      
      expect(container.firstChild).toHaveClass('custom-mini-class')
    })
  })

  describe('Status Display', () => {
    it('should display different statuses correctly', () => {
      const statuses: Array<{ status: GenerationStatus; expectedText: string }> = [
        { status: 'idle', expectedText: 'idle' },
        { status: 'preparing', expectedText: 'preparing' },
        { status: 'generating', expectedText: 'generating' },
        { status: 'saving', expectedText: 'saving' },
        { status: 'complete', expectedText: 'complete' },
        { status: 'error', expectedText: 'error' }
      ]

      statuses.forEach(({ status, expectedText }) => {
        const { unmount } = render(
          <MiniStatusIndicator status={status} progress={50} />
        )
        
        expect(screen.getByText(expectedText)).toBeInTheDocument()
        unmount()
      })
    })

    it('should show correct icons for each status', () => {
      const statusIcons = [
        { status: 'idle' as GenerationStatus, icon: 'clock-icon' },
        { status: 'preparing' as GenerationStatus, icon: 'database-icon' },
        { status: 'generating' as GenerationStatus, icon: 'file-text-icon' },
        { status: 'saving' as GenerationStatus, icon: 'upload-icon' },
        { status: 'complete' as GenerationStatus, icon: 'check-circle-icon' },
        { status: 'error' as GenerationStatus, icon: 'alert-circle-icon' }
      ]

      statusIcons.forEach(({ status, icon }) => {
        const { unmount } = render(
          <MiniStatusIndicator status={status} progress={50} />
        )
        
        expect(screen.getByTestId(icon)).toBeInTheDocument()
        unmount()
      })
    })

    it('should apply correct color classes for each status', () => {
      const { container, rerender } = render(
        <MiniStatusIndicator status="complete" progress={100} />
      )
      
      let icon = container.querySelector('[data-testid="check-circle-icon"]')
      expect(icon).toHaveClass('text-green-500')
      
      rerender(<MiniStatusIndicator status="error" progress={0} />)
      
      icon = container.querySelector('[data-testid="alert-circle-icon"]')
      expect(icon).toHaveClass('text-red-500')
    })
  })

  describe('Animation', () => {
    it('should apply animation to active statuses', () => {
      const activeStatuses: GenerationStatus[] = ['preparing', 'generating', 'saving']

      activeStatuses.forEach(status => {
        const { container, unmount } = render(
          <MiniStatusIndicator status={status} progress={50} />
        )
        
        const iconElement = container.querySelector('[class*="animate-pulse"]')
        expect(iconElement).toBeInTheDocument()
        unmount()
      })
    })

    it('should not apply animation to inactive statuses', () => {
      const inactiveStatuses: GenerationStatus[] = ['idle', 'complete', 'error']

      inactiveStatuses.forEach(status => {
        const { container, unmount } = render(
          <MiniStatusIndicator status={status} progress={100} />
        )
        
        const iconElement = container.querySelector('[class*="animate-pulse"]')
        expect(iconElement).not.toBeInTheDocument()
        unmount()
      })
    })
  })

  describe('Progress Formatting', () => {
    it('should round progress to nearest integer', () => {
      render(<MiniStatusIndicator status="generating" progress={73.7} />)
      expect(screen.getByText('74%')).toBeInTheDocument()
    })

    it('should handle edge cases for progress values', () => {
      const { rerender } = render(
        <MiniStatusIndicator status="generating" progress={0} />
      )
      expect(screen.getByText('0%')).toBeInTheDocument()
      
      rerender(<MiniStatusIndicator status="complete" progress={100} />)
      expect(screen.getByText('100%')).toBeInTheDocument()
      
      rerender(<MiniStatusIndicator status="generating" progress={0.1} />)
      expect(screen.getByText('0%')).toBeInTheDocument()
      
      rerender(<MiniStatusIndicator status="generating" progress={99.9} />)
      expect(screen.getByText('100%')).toBeInTheDocument()
    })
  })
})