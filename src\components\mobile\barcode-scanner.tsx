'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Camera, X, AlertTriangle, CheckCircle, RotateCcw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface BarcodeScannerProps {
  onScanComplete: (value: string) => void
  onScanError: (error: string) => void
  placeholder?: string
  expectedFormat?: 'container' | 'seal'
  className?: string
}

// Mock barcode scanning for now - in production this would use a library like QuaggaJS or ZXing-js
const MOCK_BARCODES = {
  container: ['ABCD1234560', 'MSKU1234567', 'GESU1234561'],
  seal: ['123456789', 'AB123456', 'ABC123456789']
}

export function BarcodeScanner({
  onScanComplete,
  onScanError,
  placeholder = 'Point camera at barcode...',
  expectedFormat = 'container',
  className = ''
}: BarcodeScannerProps) {
  const [stream, setStream] = useState<MediaStream | null>(null)
  const [isScanning, setIsScanning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const [scanResult, setScanResult] = useState<string | null>(null)
  const [isInitializing, setIsInitializing] = useState(true)
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const scanTimeoutRef = useRef<NodeJS.Timeout>()
  const mockScanTimeoutRef = useRef<NodeJS.Timeout>()

  // Initialize camera
  const initializeCamera = useCallback(async () => {
    try {
      setIsInitializing(true)
      setError(null)

      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not supported on this device')
      }

      // Request camera permission
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      })

      setStream(mediaStream)
      setHasPermission(true)

      // Set video source
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
        await videoRef.current.play()
      }

      setIsScanning(true)
      
      // Mock scanning simulation - in production replace with real barcode detection
      startMockScanning()

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to access camera'
      setError(errorMessage)
      setHasPermission(false)
      onScanError(errorMessage)
    } finally {
      setIsInitializing(false)
    }
  }, [onScanError])

  // Mock scanning for demonstration - replace with real barcode detection
  const startMockScanning = useCallback(() => {
    // Simulate finding a barcode after 2-4 seconds
    const delay = Math.random() * 2000 + 2000
    
    mockScanTimeoutRef.current = setTimeout(() => {
      const mockBarcodes = MOCK_BARCODES[expectedFormat]
      const randomBarcode = mockBarcodes[Math.floor(Math.random() * mockBarcodes.length)]
      
      setScanResult(randomBarcode)
      
      // Auto-confirm after showing result briefly
      setTimeout(() => {
        onScanComplete(randomBarcode)
      }, 1000)
    }, delay)

    // Set overall scanning timeout (10 seconds)
    scanTimeoutRef.current = setTimeout(() => {
      onScanError('Scanning timeout - please try again or enter manually')
    }, 10000)
  }, [expectedFormat, onScanComplete, onScanError])

  // Cleanup camera and timers
  const cleanup = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
      setStream(null)
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
    
    if (scanTimeoutRef.current) {
      clearTimeout(scanTimeoutRef.current)
    }
    
    if (mockScanTimeoutRef.current) {
      clearTimeout(mockScanTimeoutRef.current)
    }
    
    setIsScanning(false)
    setScanResult(null)
  }, [stream])

  // Retry scanning
  const retryScanning = useCallback(() => {
    cleanup()
    setTimeout(() => {
      initializeCamera()
    }, 100)
  }, [cleanup, initializeCamera])

  // Initialize on mount
  useEffect(() => {
    initializeCamera()
    return cleanup
  }, [initializeCamera, cleanup])

  // Manual confirmation of scanned result
  const confirmScanResult = () => {
    if (scanResult) {
      onScanComplete(scanResult)
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Camera View */}
      <div className="relative bg-black rounded-lg overflow-hidden aspect-[4/3] max-h-[300px]">
        {isInitializing && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-800">
            <div className="text-center">
              <Camera className="w-8 h-8 text-slate-400 mx-auto mb-2 animate-pulse" />
              <p className="text-sm text-slate-400">Initializing camera...</p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-800">
            <div className="text-center p-4">
              <AlertTriangle className="w-8 h-8 text-red-400 mx-auto mb-2" />
              <p className="text-sm text-red-300 mb-3">{error}</p>
              <Button
                onClick={retryScanning}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <RotateCcw className="w-4 h-4 mr-1" />
                Retry
              </Button>
            </div>
          </div>
        )}

        {hasPermission && !error && (
          <>
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              playsInline
              muted
            />
            
            {/* Scanning overlay */}
            <div className="absolute inset-0">
              {/* Scan frame */}
              <div className="absolute inset-4 border-2 border-orange-500 rounded-lg">
                <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-orange-400"></div>
                <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-orange-400"></div>
                <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-orange-400"></div>
                <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-orange-400"></div>
                
                {/* Scanning line animation */}
                {isScanning && !scanResult && (
                  <div className="absolute top-0 left-0 right-0 h-0.5 bg-orange-400 animate-pulse"></div>
                )}
              </div>
              
              {/* Status text */}
              <div className="absolute bottom-4 left-4 right-4 text-center">
                <div className="bg-black/50 rounded px-3 py-2">
                  {scanResult ? (
                    <div className="flex items-center justify-center text-green-300">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      <span className="text-sm font-mono">{scanResult}</span>
                    </div>
                  ) : (
                    <p className="text-sm text-white">{placeholder}</p>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {/* Hidden canvas for frame processing */}
        <canvas
          ref={canvasRef}
          className="hidden"
          width="640"
          height="480"
        />
      </div>

      {/* Controls */}
      <div className="flex space-x-3">
        <Button
          onClick={retryScanning}
          disabled={isInitializing}
          variant="outline"
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          Retry
        </Button>
        
        {scanResult && (
          <Button
            onClick={confirmScanResult}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white"
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Use "{scanResult}"
          </Button>
        )}
      </div>

      {/* Info */}
      <Alert className="border-slate-600 bg-slate-800/50">
        <Camera className="h-4 w-4 text-slate-400" />
        <AlertDescription className="text-slate-400 text-sm">
          {expectedFormat === 'container' 
            ? 'Position the container number barcode within the frame. The camera will automatically detect and read the code.'
            : 'Position the seal number barcode within the frame. The camera will automatically detect and read the code.'
          }
        </AlertDescription>
      </Alert>
    </div>
  )
}