'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useShipmentStore } from '@/stores/shipment-store'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Search, X, Save, Loader2, Calendar, Building, Ship, Package, MapPin } from 'lucide-react'
import type { 
  ShipmentStatus, 
  TransportMode, 
  Company, 
  Port, 
  ShipmentFilters 
} from '@/lib/supabase/types'

interface AdvancedSearchModalProps {
  children: React.ReactNode
}

export function AdvancedSearchModal({ children }: AdvancedSearchModalProps) {
  const supabase = createClient()
  
  const {
    filters,
    updateFilters,
    clearFilters,
    savedSearches,
    saveSearch,
    loadSavedSearch,
    deleteSavedSearch,
  } = useShipmentStore()

  // Local state for advanced search form
  const [formData, setFormData] = useState<ShipmentFilters>(filters)
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [saveSearchName, setSaveSearchName] = useState('')
  const [showSaveDialog, setShowSaveDialog] = useState(false)

  // Reference data
  const [customers, setCustomers] = useState<Company[]>([])
  const [ports, setPorts] = useState<Port[]>([])
  const [loadingData, setLoadingData] = useState(true)

  // Status and transport mode options
  const statusOptions: { value: ShipmentStatus; label: string; color: string }[] = [
    { value: 'booking_confirmed', label: 'Booking Confirmed', color: 'bg-blue-500' },
    { value: 'transport_assigned', label: 'Transport Assigned', color: 'bg-purple-500' },
    { value: 'driver_assigned', label: 'Driver Assigned', color: 'bg-indigo-500' },
    { value: 'empty_container_picked', label: 'Empty Container Picked', color: 'bg-cyan-500' },
    { value: 'arrived_at_factory', label: 'Arrived at Factory', color: 'bg-teal-500' },
    { value: 'loading_started', label: 'Loading Started', color: 'bg-yellow-500' },
    { value: 'departed_factory', label: 'Departed Factory', color: 'bg-orange-500' },
    { value: 'container_returned', label: 'Container Returned', color: 'bg-amber-500' },
    { value: 'shipped', label: 'Shipped', color: 'bg-blue-600' },
    { value: 'arrived', label: 'Arrived', color: 'bg-green-500' },
    { value: 'completed', label: 'Completed', color: 'bg-emerald-500' },
    { value: 'cancelled', label: 'Cancelled', color: 'bg-red-500' },
  ]

  const transportModeOptions: { value: TransportMode; label: string; icon: React.ReactNode }[] = [
    { value: 'sea', label: 'Sea Freight', icon: <Ship className="h-4 w-4" /> },
    { value: 'air', label: 'Air Freight', icon: <Package className="h-4 w-4" /> },
    { value: 'road', label: 'Road Transport', icon: <Building className="h-4 w-4" /> },
    { value: 'rail', label: 'Rail Transport', icon: <MapPin className="h-4 w-4" /> },
  ]

  // Load reference data
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        setLoadingData(true)
        
        const [customersResult, portsResult] = await Promise.all([
          supabase
            .from('companies')
            .select('id, name, company_type')
            .in('company_type', ['customer', 'shipper', 'consignee'])
            .order('name'),
          supabase
            .from('ports')
            .select('id, name, code, country')
            .order('name')
        ])

        if (customersResult.data) setCustomers(customersResult.data)
        if (portsResult.data) setPorts(portsResult.data)
        
      } catch (error) {
        console.error('Error loading reference data:', error)
      } finally {
        setLoadingData(false)
      }
    }

    if (isOpen) {
      loadReferenceData()
    }
  }, [supabase, isOpen])

  // Update form data when filters change
  useEffect(() => {
    setFormData(filters)
  }, [filters])

  const handleApplySearch = () => {
    setIsLoading(true)
    updateFilters(formData)
    setTimeout(() => {
      setIsLoading(false)
      setIsOpen(false)
    }, 500)
  }

  const handleClearAll = () => {
    const clearedFilters: ShipmentFilters = {
      search: '',
      status: [],
      customer_id: [],
      transportation_mode: [],
      date_range: { start: null, end: null },
      origin_port_id: [],
      destination_port_id: [],
    }
    setFormData(clearedFilters)
  }

  const handleSaveSearch = () => {
    if (saveSearchName.trim()) {
      saveSearch(saveSearchName.trim())
      setSaveSearchName('')
      setShowSaveDialog(false)
    }
  }

  const toggleStatus = (status: ShipmentStatus) => {
    const newStatuses = formData.status.includes(status)
      ? formData.status.filter(s => s !== status)
      : [...formData.status, status]
    
    setFormData(prev => ({ ...prev, status: newStatuses }))
  }

  const toggleTransportMode = (mode: TransportMode) => {
    const newModes = formData.transportation_mode.includes(mode)
      ? formData.transportation_mode.filter(m => m !== mode)
      : [...formData.transportation_mode, mode]
    
    setFormData(prev => ({ ...prev, transportation_mode: newModes }))
  }

  const toggleCustomer = (customerId: string) => {
    const newCustomers = formData.customer_id.includes(customerId)
      ? formData.customer_id.filter(id => id !== customerId)
      : [...formData.customer_id, customerId]
    
    setFormData(prev => ({ ...prev, customer_id: newCustomers }))
  }

  const togglePort = (portId: string, type: 'origin' | 'destination') => {
    const field = type === 'origin' ? 'origin_port_id' : 'destination_port_id'
    const currentPorts = formData[field]
    const newPorts = currentPorts.includes(portId)
      ? currentPorts.filter(id => id !== portId)
      : [...currentPorts, portId]
    
    setFormData(prev => ({ ...prev, [field]: newPorts }))
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (formData.search) count++
    if (formData.status.length > 0) count++
    if (formData.customer_id.length > 0) count++
    if (formData.transportation_mode.length > 0) count++
    if (formData.date_range.start || formData.date_range.end) count++
    if (formData.origin_port_id.length > 0) count++
    if (formData.destination_port_id.length > 0) count++
    return count
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] bg-slate-800 border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>Advanced Search</span>
              {getActiveFiltersCount() > 0 && (
                <Badge variant="secondary" className="bg-blue-600 text-white">
                  {getActiveFiltersCount()} active
                </Badge>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 max-h-[70vh] overflow-y-auto">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-slate-700">
              <TabsTrigger value="basic" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                Basic Search
              </TabsTrigger>
              <TabsTrigger value="status" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                Status & Mode
              </TabsTrigger>
              <TabsTrigger value="companies" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                Companies
              </TabsTrigger>
              <TabsTrigger value="saved" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                Saved Searches
              </TabsTrigger>
            </TabsList>

            {/* Basic Search Tab */}
            <TabsContent value="basic" className="space-y-4">
              <Card className="bg-slate-700 border-slate-600">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white text-lg">Search Terms & Dates</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Search Input */}
                  <div>
                    <Label className="text-slate-300">Search Text</Label>
                    <Input
                      placeholder="Shipment number, invoice, vessel name, or booking number..."
                      value={formData.search}
                      onChange={(e) => setFormData(prev => ({ ...prev, search: e.target.value }))}
                      className="bg-slate-600 border-slate-500 text-white placeholder:text-slate-400"
                    />
                  </div>

                  {/* Date Range */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-slate-300 flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        ETD From
                      </Label>
                      <Input
                        type="date"
                        value={formData.date_range.start || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          date_range: { ...prev.date_range, start: e.target.value }
                        }))}
                        className="bg-slate-600 border-slate-500 text-white"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-slate-300 flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        ETD To
                      </Label>
                      <Input
                        type="date"
                        value={formData.date_range.end || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          date_range: { ...prev.date_range, end: e.target.value }
                        }))}
                        className="bg-slate-600 border-slate-500 text-white"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Status & Mode Tab */}
            <TabsContent value="status" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Status Selection */}
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white text-lg">Shipment Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
                      {statusOptions.map((status) => (
                        <label
                          key={status.value}
                          className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-slate-600"
                        >
                          <Checkbox
                            checked={formData.status.includes(status.value)}
                            onCheckedChange={() => toggleStatus(status.value)}
                            className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                          />
                          <div className="flex items-center space-x-2">
                            <div className={`w-3 h-3 rounded-full ${status.color}`} />
                            <span className="text-slate-300 text-sm">{status.label}</span>
                          </div>
                        </label>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Transportation Mode */}
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white text-lg">Transportation Mode</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {transportModeOptions.map((mode) => (
                        <label
                          key={mode.value}
                          className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-slate-600"
                        >
                          <Checkbox
                            checked={formData.transportation_mode.includes(mode.value)}
                            onCheckedChange={() => toggleTransportMode(mode.value)}
                            className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                          />
                          <div className="flex items-center space-x-2 text-slate-300">
                            {mode.icon}
                            <span className="text-sm">{mode.label}</span>
                          </div>
                        </label>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Companies Tab */}
            <TabsContent value="companies" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Customers */}
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white text-lg">Customers</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loadingData ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                      </div>
                    ) : (
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {customers.filter(c => c.company_type === 'customer').map((customer) => (
                          <label
                            key={customer.id}
                            className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-slate-600"
                          >
                            <Checkbox
                              checked={formData.customer_id.includes(customer.id)}
                              onCheckedChange={() => toggleCustomer(customer.id)}
                              className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                            />
                            <span className="text-slate-300 text-sm">{customer.name}</span>
                          </label>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Ports */}
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white text-lg">Ports</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loadingData ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                      </div>
                    ) : (
                      <Tabs defaultValue="origin" className="w-full">
                        <TabsList className="grid w-full grid-cols-2 bg-slate-600">
                          <TabsTrigger value="origin" className="text-slate-300 data-[state=active]:bg-slate-500">Origin</TabsTrigger>
                          <TabsTrigger value="destination" className="text-slate-300 data-[state=active]:bg-slate-500">Destination</TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="origin" className="space-y-2 max-h-48 overflow-y-auto mt-4">
                          {ports.map((port) => (
                            <label
                              key={`origin-${port.id}`}
                              className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-slate-600"
                            >
                              <Checkbox
                                checked={formData.origin_port_id.includes(port.id)}
                                onCheckedChange={() => togglePort(port.id, 'origin')}
                                className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                              />
                              <span className="text-slate-300 text-sm">
                                {port.name} ({port.code})
                              </span>
                            </label>
                          ))}
                        </TabsContent>
                        
                        <TabsContent value="destination" className="space-y-2 max-h-48 overflow-y-auto mt-4">
                          {ports.map((port) => (
                            <label
                              key={`destination-${port.id}`}
                              className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-slate-600"
                            >
                              <Checkbox
                                checked={formData.destination_port_id.includes(port.id)}
                                onCheckedChange={() => togglePort(port.id, 'destination')}
                                className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                              />
                              <span className="text-slate-300 text-sm">
                                {port.name} ({port.code})
                              </span>
                            </label>
                          ))}
                        </TabsContent>
                      </Tabs>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Saved Searches Tab */}
            <TabsContent value="saved" className="space-y-4">
              <Card className="bg-slate-700 border-slate-600">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white text-lg">Saved Searches</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Save Current Search */}
                  {getActiveFiltersCount() > 0 && (
                    <div className="border border-slate-600 rounded p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-white font-medium">Save Current Search</h4>
                        {!showSaveDialog && (
                          <Button
                            size="sm"
                            onClick={() => setShowSaveDialog(true)}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <Save className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                        )}
                      </div>
                      
                      {showSaveDialog && (
                        <div className="flex gap-2">
                          <Input
                            placeholder="Enter search name..."
                            value={saveSearchName}
                            onChange={(e) => setSaveSearchName(e.target.value)}
                            className="bg-slate-600 border-slate-500 text-white"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') handleSaveSearch()
                              if (e.key === 'Escape') setShowSaveDialog(false)
                            }}
                            autoFocus
                          />
                          <Button size="sm" onClick={handleSaveSearch} disabled={!saveSearchName.trim()}>
                            Save
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => setShowSaveDialog(false)}>
                            Cancel
                          </Button>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Saved Searches List */}
                  {savedSearches.length > 0 ? (
                    <div className="space-y-2">
                      {savedSearches.map((savedSearch) => (
                        <div key={savedSearch.id} className="flex items-center justify-between p-3 border border-slate-600 rounded">
                          <div>
                            <h4 className="text-white font-medium">{savedSearch.name}</h4>
                            <p className="text-sm text-slate-400">
                              Saved on {new Date(savedSearch.created_at).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                loadSavedSearch(savedSearch.id)
                                setFormData(savedSearch.filters)
                              }}
                              className="text-slate-300 border-slate-600 hover:bg-slate-600"
                            >
                              Load
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => deleteSavedSearch(savedSearch.id)}
                              className="text-red-400 border-red-600 hover:bg-red-600/20"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-slate-400">No saved searches yet</p>
                      <p className="text-sm text-slate-500 mt-1">
                        Set up some filters and save them for quick access
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-700">
          <Button
            variant="outline"
            onClick={handleClearAll}
            className="text-slate-300 border-slate-600 hover:bg-slate-700"
          >
            Clear All
          </Button>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="text-slate-300 border-slate-600 hover:bg-slate-700"
            >
              Cancel
            </Button>
            
            <Button
              onClick={handleApplySearch}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              Apply Search
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}