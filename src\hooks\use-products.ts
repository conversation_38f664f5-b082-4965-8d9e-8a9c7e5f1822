'use client'

import { useEffect, useCallback } from 'react'
import {
  useProductStore,
  useProducts,
  useProductsLoading,
  useProductsError,
  useProductActions,
} from '@/stores/product-store'
import type {
  Product,
  ProductInsert,
  ProductUpdate,
} from '@/lib/supabase/types'
import type { ProductFilter } from '@/lib/validations/products'

export function useProductsData() {
  const products = useProducts()
  const loading = useProductsLoading()
  const error = useProductsError()
  const {
    fetchProducts,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    clearError,
    subscribeToProducts,
  } = useProductActions()

  // Pagination and filtering state
  const currentPage = useProductStore(state => state.currentPage)
  const pageSize = useProductStore(state => state.pageSize)
  const totalCount = useProductStore(state => state.totalCount)
  const filter = useProductStore(state => state.filter)
  const searchTerm = useProductStore(state => state.searchTerm)
  const sortBy = useProductStore(state => state.sortBy)
  const sortOrder = useProductStore(state => state.sortOrder)

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / pageSize)
  const hasNextPage = currentPage < totalPages
  const hasPreviousPage = currentPage > 1

  // Load products on mount and setup real-time subscription
  useEffect(() => {
    fetchProducts()

    // Setup real-time subscription
    const unsubscribe = subscribeToProducts()

    return () => {
      unsubscribe()
    }
  }, [fetchProducts, subscribeToProducts])

  // Filtered products for dropdown/selection purposes
  const activeProducts = products.filter(product => product.is_active)
  const productsByCategory = products.reduce(
    (acc, product) => {
      const category = product.category || 'other'
      if (!acc[category]) acc[category] = []
      acc[category].push(product)
      return acc
    },
    {} as Record<string, typeof products>
  )

  // Unique categories for filter dropdown
  const categories = Array.from(
    new Set(products.map(p => p.category).filter(Boolean))
  ).sort()

  return {
    // Data
    products,
    activeProducts,
    productsByCategory,
    categories,
    loading,
    error,

    // Pagination
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    hasNextPage,
    hasPreviousPage,

    // Filters and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Actions
    fetchProducts,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage: (page: number) => setPage(Math.max(1, Math.min(page, totalPages))),
    nextPage: () => hasNextPage && setPage(currentPage + 1),
    previousPage: () => hasPreviousPage && setPage(currentPage - 1),
    clearError,

    // Utility functions
    refreshProducts: fetchProducts,
    getProductById: (id: string) => products.find(product => product.id === id),
    getProductByCode: (code: string) =>
      products.find(product => product.code === code),
  }
}

export function useProductCRUD() {
  const {
    createProduct,
    updateProduct,
    deleteProduct,
    deleteProducts,
    fetchProductById,
  } = useProductActions()

  const isCreating = useProductStore(state => state.isCreating)
  const isUpdating = useProductStore(state => state.isUpdating)
  const isDeleting = useProductStore(state => state.isDeleting)

  const handleCreate = useCallback(
    async (productData: ProductInsert): Promise<Product> => {
      try {
        const result = await createProduct(productData)
        return result
      } catch (error) {
        console.error('Failed to create product:', error)
        throw error
      }
    },
    [createProduct]
  )

  const handleUpdate = useCallback(
    async (id: string, updates: Partial<ProductUpdate>): Promise<Product> => {
      try {
        const result = await updateProduct(id, updates)
        return result
      } catch (error) {
        console.error('Failed to update product:', error)
        throw error
      }
    },
    [updateProduct]
  )

  const handleDelete = useCallback(
    async (id: string): Promise<void> => {
      try {
        await deleteProduct(id)
      } catch (error) {
        console.error('Failed to delete product:', error)
        throw error
      }
    },
    [deleteProduct]
  )

  const handleBulkDelete = useCallback(
    async (ids: string[]): Promise<void> => {
      try {
        await deleteProducts(ids)
      } catch (error) {
        console.error('Failed to delete products:', error)
        throw error
      }
    },
    [deleteProducts]
  )

  const handleFetchById = useCallback(
    async (id: string) => {
      try {
        return await fetchProductById(id)
      } catch (error) {
        console.error('Failed to fetch product:', error)
        throw error
      }
    },
    [fetchProductById]
  )

  return {
    // Loading states
    isCreating,
    isUpdating,
    isDeleting,

    // CRUD operations
    createProduct: handleCreate,
    updateProduct: handleUpdate,
    deleteProduct: handleDelete,
    bulkDeleteProducts: handleBulkDelete,
    fetchProductById: handleFetchById,

    // Utility
    isLoading: isCreating || isUpdating || isDeleting,
  }
}

export function useProductSelection() {
  const selectedProducts = useProductStore(state => state.selectedProducts)
  const { selectProduct, deselectProduct, clearSelection } = useProductActions()

  const selectAllProducts = useProductStore(state => state.selectAllProducts)
  const products = useProducts()

  const selectedIds = Array.from(selectedProducts)
  const selectedCount = selectedProducts.size
  const isSelected = (id: string) => selectedProducts.has(id)
  const isAllSelected =
    products.length > 0 && selectedProducts.size === products.length
  const isPartiallySelected =
    selectedProducts.size > 0 && selectedProducts.size < products.length

  const toggleProduct = useCallback(
    (id: string) => {
      if (selectedProducts.has(id)) {
        deselectProduct(id)
      } else {
        selectProduct(id)
      }
    },
    [selectedProducts, selectProduct, deselectProduct]
  )

  const toggleAll = useCallback(() => {
    if (isAllSelected) {
      clearSelection()
    } else {
      selectAllProducts()
    }
  }, [isAllSelected, clearSelection, selectAllProducts])

  return {
    selectedProducts: selectedIds,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,
    selectProduct,
    deselectProduct,
    toggleProduct,
    selectAllProducts,
    toggleAll,
    clearSelection,
  }
}

// Validation hooks
export function useProductValidation() {
  const products = useProducts()

  const validateUniqueCode = useCallback(
    (code: string, excludeId?: string) => {
      if (!code) return true // Code is optional
      const existing = products.find(
        product =>
          product.code?.toLowerCase() === code.toLowerCase() &&
          product.is_active &&
          product.id !== excludeId
      )
      return !existing
    },
    [products]
  )

  const validateHSCode = useCallback((hsCode: string) => {
    if (!hsCode) return null // HS code is optional

    // Basic HS code validation - should be numbers with optional dots
    const hsCodeRegex = /^[0-9.]+$/
    if (!hsCodeRegex.test(hsCode)) {
      return 'HS code must contain only numbers and dots'
    }

    // HS codes are typically 6-10 digits
    const digitCount = hsCode.replace(/\./g, '').length
    if (digitCount < 6 || digitCount > 10) {
      return 'HS code must have 6-10 digits'
    }

    return null
  }, [])

  return {
    validateUniqueCode,
    validateHSCode,
  }
}

// Product categories management
export function useProductCategories() {
  const { categories } = useProductsData()

  const createCategoryOptions = useCallback(() => {
    return categories.map(category => ({
      value: category,
      label: category,
    }))
  }, [categories])

  return {
    categories,
    categoryOptions: createCategoryOptions(),
  }
}

// Constants
export const DEFAULT_PRODUCT_FILTER: ProductFilter = {
  is_active: true,
}

// Common product statuses for display
export const PRODUCT_STATUSES = [
  { value: true, label: 'Active', variant: 'default' as const },
  { value: false, label: 'Inactive', variant: 'secondary' as const },
] as const

// Main hook that combines all functionality
export function useProductsManagement() {
  const data = useProductsData()
  const crud = useProductCRUD()
  const selection = useProductSelection()
  const validation = useProductValidation()
  const categories = useProductCategories()

  return {
    ...data,
    ...crud,
    ...selection,
    ...validation,
    ...categories,

    // Convenience methods
    resetFilters: () => data.setFilter(DEFAULT_PRODUCT_FILTER),
    clearSearch: () => data.setSearchTerm(''),
  }
}
