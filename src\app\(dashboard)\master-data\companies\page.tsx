'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Eye,
  X,
  Building,
  Mail,
  Phone,
  Navigation,
  Calendar,
  MapPin,
  Users,
  Truck,
  Factory as FactoryIcon,
  Ship,
} from 'lucide-react'
import { CompanyForm } from '@/components/forms/company-form/company-form'
import { useCompaniesManagement } from '@/hooks/use-companies'
import type { Company } from '@/stores/company-store'
import type { CompanyForm as CompanyFormData } from '@/lib/validations/companies'
import { formatDistanceToNow, format } from 'date-fns'

export default function CompaniesPage() {
  const {
    // Data
    companies,
    loading,
    error,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedCompanies,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createCompany,
    updateCompany,
    deleteCompany,
    bulkDeleteCompanies,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    toggleCompany,
    toggleAll,
    clearSelection,
    clearError,
    refreshCompanies,

    // Geographic utilities
    parseCoordinates,
    formatCoordinates,

    // Categories
    companyTypeOptions,
    getCompanyTypeLabel,
  } = useCompaniesManagement()

  // UI state
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingCompany, setEditingCompany] = useState<Company | null>(null)
  const [viewingCompany, setViewingCompany] = useState<Company | null>(null)
  const [deletingCompany, setDeletingCompany] = useState<Company | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // Handle create company
  const handleCreate = async (data: CompanyFormData) => {
    try {
      // Convert GPS coordinates object to string for database storage
      const processedData = {
        ...data,
        gps_coordinates: data.gps_coordinates
          ? JSON.stringify(data.gps_coordinates)
          : undefined,
        // Convert undefined to null for all info fields
        customer_info: data.customer_info
          ? {
              ...data.customer_info,
              incoterms: data.customer_info.incoterms ?? null,
              special_requirements: data.customer_info.special_requirements ?? null,
            }
          : data.customer_info,
        carrier_info: data.carrier_info
          ? {
              ...data.carrier_info,
              carrier_code: data.carrier_info.carrier_code ?? null,
              insurance_policy_no: data.carrier_info.insurance_policy_no ?? null,
              insurance_expiry_date: data.carrier_info.insurance_expiry_date ?? null,
            }
          : data.carrier_info,
        factory_info: data.factory_info
          ? {
              ...data.factory_info,
              factory_code: data.factory_info.factory_code ?? null,
            }
          : data.factory_info,
      }
      await createCompany(processedData)
      setShowCreateDialog(false)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle update company
  const handleUpdate = async (data: CompanyFormData) => {
    if (!editingCompany) return

    try {
      // Convert GPS coordinates object to string for database storage
      const processedData = {
        ...data,
        gps_coordinates: data.gps_coordinates
          ? JSON.stringify(data.gps_coordinates)
          : undefined,
      }
      await updateCompany(editingCompany.id, processedData)
      setEditingCompany(null)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle delete company
  const handleDelete = async (company: Company) => {
    try {
      await deleteCompany(company.id)
      setDeletingCompany(null)
    } catch (error) {
      // Error handling is done in the store
      console.error('Delete failed:', error)
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      await bulkDeleteCompanies(selectedCompanies)
      setShowBulkDeleteDialog(false)
      clearSelection()
    } catch (error) {
      // Error handling is done in the store
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle sorting
  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSorting(column, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSorting(column, 'asc')
    }
  }

  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  // Format coordinates for display
  const formatCompanyCoordinates = (gpsCoordinates: string | object | null) => {
    if (!gpsCoordinates) return 'No coordinates'

    const coords = parseCoordinates(gpsCoordinates)
    if (!coords) return 'Invalid coordinates'

    return formatCoordinates(coords.lat, coords.lng)
  }

  // Get company type icon
  const getCompanyTypeIcon = (type: string) => {
    switch (type) {
      case 'customer':
        return <Users className="h-4 w-4" />
      case 'carrier':
        return <Truck className="h-4 w-4" />
      case 'factory':
        return <FactoryIcon className="h-4 w-4" />
      case 'forwarder_agent':
        return <Ship className="h-4 w-4" />
      default:
        return <Building className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Companies</h1>
          <p className="text-slate-400 mt-1">
            Manage company stakeholders and their information
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Company
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">Create Company</DialogTitle>
              <DialogDescription className="text-slate-400">
                Add a new company with type-specific information
              </DialogDescription>
            </DialogHeader>
            <CompanyForm
              onSubmit={handleCreate}
              onCancel={() => setShowCreateDialog(false)}
              isLoading={isCreating}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Search */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {(searchTerm || filter.company_type) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFilter({})
                setSearchTerm('')
              }}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Search Companies
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search by name, tax ID, email, or contact..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
          </div>

          {/* Company Type Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Company Type
            </label>
            <Select
              value={filter.company_type || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  company_type: value === 'all' ? undefined : (value as any),
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Company Types" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Company Types
                </SelectItem>
                {companyTypeOptions.map(type => (
                  <SelectItem
                    key={type.value}
                    value={type.value}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {(searchTerm || filter.company_type) && (
          <div className="flex flex-wrap gap-2 pt-2">
            {searchTerm && (
              <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-2 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.company_type && (
              <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                Type: {getCompanyTypeLabel(filter.company_type)}
                <button
                  onClick={() =>
                    setFilter({ ...filter, company_type: undefined })
                  }
                  className="ml-2 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedCount} company{selectedCount !== 1 ? 'ies' : 'y'}{' '}
                selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowBulkDeleteDialog(true)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Companies Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Building className="h-5 w-5 text-orange-500" />
              Companies ({totalCount})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshCompanies}
              disabled={loading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {loading && companies.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-slate-300">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              <span className="ml-2">Loading companies...</span>
            </div>
          ) : companies.length === 0 ? (
            <div className="text-center py-8">
              <Building className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No companies found</p>
              <p className="text-sm text-slate-400 mt-1">
                Create your first company to get started
              </p>
            </div>
          ) : (
            <>
              <Table className="bg-slate-800">
                <TableHeader className="bg-slate-700">
                  <TableRow className="border-slate-600 hover:bg-slate-700">
                    <TableHead className="w-12 text-slate-200">
                      <Checkbox
                        checked={isAllSelected || isPartiallySelected}
                        onCheckedChange={toggleAll}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('name')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Name {getSortIcon('name')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('company_type')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Type {getSortIcon('company_type')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">Contact</TableHead>
                    <TableHead className="text-slate-200">Tax ID</TableHead>
                    <TableHead className="text-slate-200">Status</TableHead>
                    <TableHead className="w-32 text-slate-200">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-slate-800">
                  {companies.map(company => (
                    <TableRow
                      key={company.id}
                      className="border-slate-600 hover:bg-slate-700 text-slate-200"
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected(company.id)}
                          onCheckedChange={() => toggleCompany(company.id)}
                          className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          {getCompanyTypeIcon(company.company_type)}
                          <span>{company.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`${
                            company.company_type === 'customer'
                              ? 'border-blue-400 text-blue-200 bg-blue-500/20'
                              : company.company_type === 'carrier'
                                ? 'border-green-400 text-green-200 bg-green-500/20'
                                : company.company_type === 'factory'
                                  ? 'border-purple-400 text-purple-200 bg-purple-500/20'
                                  : company.company_type === 'shipper'
                                    ? 'border-yellow-400 text-yellow-200 bg-yellow-500/20'
                                    : company.company_type === 'consignee'
                                      ? 'border-orange-400 text-orange-200 bg-orange-500/20'
                                      : company.company_type === 'notify_party'
                                        ? 'border-pink-400 text-pink-200 bg-pink-500/20'
                                        : company.company_type ===
                                            'forwarder_agent'
                                          ? 'border-indigo-400 text-indigo-200 bg-indigo-500/20'
                                          : 'border-slate-400 text-slate-200 bg-slate-500/20'
                          }`}
                        >
                          {getCompanyTypeLabel(company.company_type)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          {company.contact_email && (
                            <div className="flex items-center space-x-1">
                              <Mail className="h-3 w-3 text-slate-400" />
                              <span className="text-slate-300">
                                {company.contact_email}
                              </span>
                            </div>
                          )}
                          {company.contact_phone && (
                            <div className="flex items-center space-x-1">
                              <Phone className="h-3 w-3 text-slate-400" />
                              <span className="text-slate-300">
                                {company.contact_phone}
                              </span>
                            </div>
                          )}
                          {!company.contact_email && !company.contact_phone && (
                            <span className="text-slate-500">
                              No contact info
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-slate-300 font-mono">
                          {company.tax_id || 'Not provided'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={company.is_active ? 'default' : 'secondary'}
                          className={
                            company.is_active
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                          }
                        >
                          {company.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setViewingCompany(company)}
                            className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingCompany(company)}
                            className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingCompany(company)}
                            className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-6 pb-6">
                  <div className="text-sm text-slate-300">
                    Page {currentPage} of {totalPages} ({totalCount} companies)
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(1)}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={previousPage}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* View Company Dialog */}
      {viewingCompany && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-slate-800 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">
                Company Details
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewingCompany(null)}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Company Avatar and Basic Info */}
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                  {getCompanyTypeIcon(viewingCompany.company_type)}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white">
                    {viewingCompany.name}
                  </h3>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge
                      variant="outline"
                      className={`${
                        viewingCompany.company_type === 'customer'
                          ? 'border-blue-400 text-blue-200 bg-blue-500/20'
                          : viewingCompany.company_type === 'carrier'
                            ? 'border-green-400 text-green-200 bg-green-500/20'
                            : viewingCompany.company_type === 'factory'
                              ? 'border-purple-400 text-purple-200 bg-purple-500/20'
                              : viewingCompany.company_type === 'shipper'
                                ? 'border-yellow-400 text-yellow-200 bg-yellow-500/20'
                                : viewingCompany.company_type === 'consignee'
                                  ? 'border-orange-400 text-orange-200 bg-orange-500/20'
                                  : viewingCompany.company_type ===
                                      'notify_party'
                                    ? 'border-pink-400 text-pink-200 bg-pink-500/20'
                                    : viewingCompany.company_type ===
                                        'forwarder_agent'
                                      ? 'border-indigo-400 text-indigo-200 bg-indigo-500/20'
                                      : 'border-slate-400 text-slate-200 bg-slate-500/20'
                      }`}
                    >
                      <Building className="h-3 w-3 mr-1" />
                      {getCompanyTypeLabel(viewingCompany.company_type)}
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <div
                        className={`w-2 h-2 rounded-full ${viewingCompany.is_active ? 'bg-green-400' : 'bg-red-400'}`}
                      />
                      <span
                        className={`text-sm ${viewingCompany.is_active ? 'text-green-300' : 'text-red-300'}`}
                      >
                        {viewingCompany.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Company Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Building className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Tax ID</span>
                  </div>
                  <p className="text-slate-300 font-mono">
                    {viewingCompany.tax_id || 'Not provided'}
                  </p>
                </div>

                {viewingCompany.contact_email && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Mail className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">Email</span>
                    </div>
                    <p className="text-slate-300">
                      {viewingCompany.contact_email}
                    </p>
                  </div>
                )}

                {viewingCompany.contact_phone && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Phone className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">Phone</span>
                    </div>
                    <p className="text-slate-300">
                      {viewingCompany.contact_phone}
                    </p>
                  </div>
                )}

                {(viewingCompany.contact_person_first_name ||
                  viewingCompany.contact_person_last_name) && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Users className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">
                        Contact Person
                      </span>
                    </div>
                    <p className="text-slate-300">
                      {[
                        viewingCompany.contact_person_first_name,
                        viewingCompany.contact_person_last_name,
                      ]
                        .filter(Boolean)
                        .join(' ')}
                    </p>
                  </div>
                )}
              </div>

              {/* GPS Coordinates */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Navigation className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">
                    GPS Coordinates
                  </span>
                </div>
                <p className="text-slate-300 font-mono text-sm">
                  {formatCompanyCoordinates(viewingCompany.gps_coordinates)}
                </p>
              </div>

              {/* Notes */}
              {viewingCompany.notes && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Notes</span>
                  </div>
                  <p className="text-slate-300">{viewingCompany.notes}</p>
                </div>
              )}

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Created</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingCompany.created_at
                      ? format(new Date(viewingCompany.created_at), 'PPP')
                      : 'Unknown'}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {viewingCompany.created_at
                      ? formatDistanceToNow(
                          new Date(viewingCompany.created_at),
                          {
                            addSuffix: true,
                          }
                        )
                      : ''}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Edit className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Last Updated</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingCompany.updated_at
                      ? format(new Date(viewingCompany.updated_at), 'PPP')
                      : 'Unknown'}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {viewingCompany.updated_at
                      ? formatDistanceToNow(
                          new Date(viewingCompany.updated_at),
                          {
                            addSuffix: true,
                          }
                        )
                      : ''}
                  </p>
                </div>
              </div>

              {/* Company ID */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Building className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Company ID</span>
                </div>
                <p className="text-slate-300 font-mono text-sm">
                  {viewingCompany.id}
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => setViewingCompany(null)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Company Dialog */}
      <Dialog
        open={!!editingCompany}
        onOpenChange={() => setEditingCompany(null)}
      >
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">Edit Company</DialogTitle>
            <DialogDescription className="text-slate-400">
              Update company information and type-specific details
            </DialogDescription>
          </DialogHeader>
          {editingCompany && (
            <CompanyForm
              company={editingCompany}
              onSubmit={handleUpdate}
              onCancel={() => setEditingCompany(null)}
              isLoading={isUpdating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Company Dialog */}
      <AlertDialog
        open={!!deletingCompany}
        onOpenChange={() => setDeletingCompany(null)}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Company
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete &quot;{deletingCompany?.name}
              &quot;? This action cannot be undone.
              {deletingCompany && (
                <div className="mt-2 p-2 bg-slate-700 rounded text-sm border border-slate-600">
                  <strong className="text-white">Name:</strong>{' '}
                  <span className="text-slate-200">{deletingCompany.name}</span>
                  <br />
                  <strong className="text-white">Type:</strong>{' '}
                  <span className="text-slate-200">
                    {getCompanyTypeLabel(deletingCompany.company_type)}
                  </span>
                  <br />
                  {deletingCompany.tax_id && (
                    <>
                      <strong className="text-white">Tax ID:</strong>{' '}
                      <span className="text-slate-200 font-mono">
                        {deletingCompany.tax_id}
                      </span>
                    </>
                  )}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setDeletingCompany(null)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deletingCompany && handleDelete(deletingCompany)}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Company
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Selected Companies
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete {selectedCount} selected compan
              {selectedCount !== 1 ? 'ies' : 'y'}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowBulkDeleteDialog(false)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Companies
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
