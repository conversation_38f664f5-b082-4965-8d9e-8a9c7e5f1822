'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Eye, EyeOff, Loader2 } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { registerSchema, type RegisterFormData } from '@/lib/validations/auth'
import { authClient, type UserRole } from '@/lib/supabase/auth'
import { createClient } from '@/lib/supabase/client'

interface Company {
  id: string
  name: string
  company_type: string
}

// Role display names
const roleDisplayNames: Record<UserRole, string> = {
  admin: 'Administrator',
  cs: 'Customer Service',
  account: 'Account Manager',
  customer: 'Customer',
  carrier: 'Carrier',
  driver: 'Driver',
  factory: 'Factory',
  shipper: 'Shipper',
  consignee: 'Consignee',
  notify_party: 'Notify Party',
  forwarder_agent: 'Forwarder Agent',
}

// Role descriptions
const roleDescriptions: Record<UserRole, string> = {
  admin: 'Full system access and user management',
  cs: 'Customer service and support access',
  account: 'Account management and client relations',
  customer: 'Customer portal access to shipments',
  carrier: 'Carrier company management access',
  driver: 'Mobile access for delivery updates',
  factory: 'Factory operations and inventory access',
  shipper: 'Shipper role for export processes',
  consignee: 'Consignee role for import processes',
  notify_party: 'Notification recipient access',
  forwarder_agent: 'Freight forwarding operations',
}

export default function RegisterPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [companies, setCompanies] = useState<Company[]>([])
  const [loadingCompanies, setLoadingCompanies] = useState(true)
  const router = useRouter()

  const form = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      role: 'customer',
      companyId: '',
    },
  })

  const selectedRole = form.watch('role')

  // Load companies on component mount
  useEffect(() => {
    async function loadCompanies() {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('companies')
          .select('id, name, company_type')
          .eq('is_active', true)
          .order('name')

        if (error) throw error
        setCompanies(data || [])
      } catch (error) {
        console.error('Error loading companies:', error)
        setError('Failed to load companies. Please refresh the page.')
      } finally {
        setLoadingCompanies(false)
      }
    }

    loadCompanies()
  }, [])

  // Filter companies based on selected role
  const getFilteredCompanies = () => {
    if (['admin', 'cs', 'account'].includes(selectedRole)) {
      return companies // Staff roles can be associated with any company
    }

    // Map roles to company types
    const roleToCompanyType: Record<string, string> = {
      customer: 'customer',
      carrier: 'carrier',
      driver: 'carrier',
      factory: 'factory',
      shipper: 'shipper',
      consignee: 'consignee',
      notify_party: 'notify_party',
      forwarder_agent: 'forwarder_agent',
    }

    const requiredCompanyType = roleToCompanyType[selectedRole]
    return companies.filter(
      company => company.company_type === requiredCompanyType
    )
  }

  // Reset company selection when role changes
  useEffect(() => {
    form.setValue('companyId', '')
  }, [selectedRole, form])

  async function onSubmit(data: RegisterFormData) {
    setIsLoading(true)
    setError(null)

    try {
      const userData = {
        first_name: data.firstName,
        last_name: data.lastName,
        phone_number: data.phoneNumber || undefined,
        role: data.role,
        company_id: data.companyId || undefined,
      }

      await authClient.signUp(data.email, data.password, userData)

      // Show success message and redirect to login
      router.push(
        '/login?message=Registration successful. Please check your email to verify your account.'
      )
    } catch (error) {
      console.error('Registration error:', error)
      setError(
        error instanceof Error
          ? error.message
          : 'An error occurred during registration. Please try again.'
      )
    } finally {
      setIsLoading(false)
    }
  }

  if (loadingCompanies) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2">Create account</h2>
          <p className="text-slate-300">Loading...</p>
        </div>
        <div className="flex justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Create account</h2>
        <p className="text-slate-300">Join DYY Trading Management platform</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">First Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter first name"
                      className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Last Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter last name"
                      className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    placeholder="Enter your email"
                    className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage className="text-red-300" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">
                  Phone Number (Optional)
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="tel"
                    placeholder="Enter phone number"
                    className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage className="text-red-300" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="role"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">Role</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isLoading}
                >
                  <FormControl>
                    <SelectTrigger className="bg-white/10 border-white/20 text-white focus:border-orange-500 focus:ring-orange-500">
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(roleDisplayNames).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{label}</span>
                          <span className="text-xs text-muted-foreground">
                            {roleDescriptions[value as UserRole]}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage className="text-red-300" />
              </FormItem>
            )}
          />

          {!['admin', 'cs', 'account'].includes(selectedRole) && (
            <FormField
              control={form.control}
              name="companyId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Company</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white/10 border-white/20 text-white focus:border-orange-500 focus:ring-orange-500">
                        <SelectValue placeholder="Select your company" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {getFilteredCompanies().map(company => (
                        <SelectItem key={company.id} value={company.id}>
                          {company.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Enter your password"
                      className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 pr-10"
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage className="text-red-300" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">Confirm Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder="Confirm your password"
                      className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 pr-10"
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                      disabled={isLoading}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage className="text-red-300" />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full bg-orange-500 hover:bg-orange-600 text-white"
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create account
          </Button>
        </form>
      </Form>

      <div className="text-center">
        <p className="text-slate-300 text-sm">
          Already have an account?{' '}
          <Link
            href="/login"
            className="text-orange-400 hover:text-orange-300 transition-colors font-medium"
          >
            Sign in
          </Link>
        </p>
      </div>
    </div>
  )
}
