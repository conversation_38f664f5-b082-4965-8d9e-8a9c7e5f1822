'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useDocumentTemplate, useDocumentTemplates, useTemplateVersions } from '@/hooks/use-document-templates'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Star,
  StarOff,
  ToggleLeft,
  Trash2,
  MoreHorizontal,
  Eye,
  FileText,
  Settings,
  History,
  Loader2,
  AlertCircle,
  CheckCircle,
  Calendar,
  User,
  Globe,
  Palette,
} from 'lucide-react'
import type { DocumentTemplate } from '@/types/document-template'

interface TemplateDetailPageProps {
  params: {
    id: string
  }
}

interface DeleteDialogState {
  isOpen: boolean
}

/**
 * Document Template Detail Page
 * Story 5.1: Document Template Management System
 * 
 * Page for viewing template details, version history, and performing
 * template actions with admin-only access controls.
 */
export default function TemplateDetailPage({ params }: TemplateDetailPageProps) {
  const router = useRouter()
  const [deleteDialog, setDeleteDialog] = useState<DeleteDialogState>({ isOpen: false })
  const [actionLoading, setActionLoading] = useState(false)
  const [actionError, setActionError] = useState<string | null>(null)
  const [actionSuccess, setActionSuccess] = useState<string | null>(null)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const { template, loading, error, refresh } = useDocumentTemplate(params.id)
  const { versions, loading: versionsLoading } = useTemplateVersions(template?.template_name || null)
  const {
    deleteTemplate,
    toggleTemplateStatus,
    setDefaultTemplate,
  } = useDocumentTemplates({ autoRefresh: false })

  const handleEdit = () => {
    router.push(`/documents/templates/${params.id}/edit`)
  }

  const handleClone = () => {
    // Navigate to clone page or open clone dialog
    console.log('Clone template:', template?.id)
  }

  const handleToggleStatus = async () => {
    if (!template) return
    
    setActionLoading(true)
    setActionError(null)
    
    const result = await toggleTemplateStatus(template.id, !template.is_active)
    
    if (result.success) {
      setActionSuccess(`Template ${template.is_active ? 'deactivated' : 'activated'} successfully`)
      refresh()
    } else {
      setActionError(result.error || 'Failed to toggle template status')
    }
    
    setActionLoading(false)
  }

  const handleSetDefault = async () => {
    if (!template) return
    
    setActionLoading(true)
    setActionError(null)
    
    const result = await setDefaultTemplate(template.id)
    
    if (result.success) {
      setActionSuccess('Template set as default successfully')
      refresh()
    } else {
      setActionError(result.error || 'Failed to set template as default')
    }
    
    setActionLoading(false)
  }

  const handleDeleteConfirm = async () => {
    if (!template) return
    
    setActionLoading(true)
    setActionError(null)
    
    const result = await deleteTemplate(template.id)
    
    if (result.success) {
      router.push('/documents/templates')
    } else {
      setActionError(result.error || 'Failed to delete template')
      setDeleteDialog({ isOpen: false })
    }
    
    setActionLoading(false)
  }

  const getStatusBadge = (template: DocumentTemplate) => {
    if (!template.is_active) {
      return <Badge variant="secondary">Inactive</Badge>
    }
    if (template.is_default) {
      return <Badge variant="default">Default</Badge>
    }
    return <Badge variant="outline">Active</Badge>
  }

  const getDocumentTypeLabel = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  // Auto-resize iframe to fit content
  useEffect(() => {
    const resizeIframe = () => {
      const iframe = iframeRef.current
      if (iframe && iframe.contentDocument) {
        const body = iframe.contentDocument.body
        if (body) {
          const height = Math.max(
            body.scrollHeight,
            body.offsetHeight,
            384 // minimum 384px
          )
          // Limit to 80% of viewport height to avoid excessive height
          const maxHeight = Math.min(height + 40, window.innerHeight * 0.8)
          iframe.style.height = `${maxHeight}px`
        }
      }
    }

    const iframe = iframeRef.current
    if (iframe) {
      iframe.onload = resizeIframe
      // Also try resize after a short delay in case content loads later
      const timer = setTimeout(resizeIframe, 200)
      return () => clearTimeout(timer)
    }
  }, [template?.template_content])

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin mr-4" />
            <span className="text-lg">Loading template...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Error state
  if (error || !template) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Template not found'}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Success message */}
      {actionSuccess && (
        <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertDescription className="text-green-800 dark:text-green-200">
            {actionSuccess}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActionSuccess(null)}
              className="ml-2 h-6 px-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Error message */}
      {actionError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {actionError}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActionError(null)}
              className="ml-2 h-6 px-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => router.push('/documents/templates')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
              {template.template_name}
            </h1>
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant="outline">
                {getDocumentTypeLabel(template.document_type)}
              </Badge>
              <Badge variant="secondary" className="font-mono">
                v{template.version}
              </Badge>
              {getStatusBadge(template)}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button onClick={handleEdit} className="bg-orange-600 hover:bg-orange-700">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" disabled={actionLoading}>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={handleClone}>
                <Copy className="h-4 w-4 mr-2" />
                Clone Template
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={handleToggleStatus}>
                <ToggleLeft className="h-4 w-4 mr-2" />
                {template.is_active ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>

              {template.is_active && !template.is_default && (
                <DropdownMenuItem onClick={handleSetDefault}>
                  <Star className="h-4 w-4 mr-2" />
                  Set as Default
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              <DropdownMenuItem
                onClick={() => setDeleteDialog({ isOpen: true })}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Template Details */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">
            <Eye className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="content">
            <FileText className="h-4 w-4 mr-2" />
            Content
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="versions">
            <History className="h-4 w-4 mr-2" />
            Versions
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              {/* Template Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Template Preview
                  </CardTitle>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Visual preview of how the template will appear when generated
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
                    {/* Preview iframe with auto-height */}
                    <div className="bg-white dark:bg-slate-900 min-h-96 max-h-screen overflow-auto">
                      <iframe
                        ref={iframeRef}
                        srcDoc={`
                          <!DOCTYPE html>
                          <html>
                          <head>
                            <meta charset="utf-8">
                            <title>Template Preview</title>
                            <style>
                              body {
                                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                                line-height: 1.6;
                                color: #333;
                                max-width: 100%;
                                margin: 0;
                                padding: 20px;
                                background: white;
                                overflow-x: hidden;
                              }
                              table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                              th { background-color: #f5f5f5; font-weight: bold; }
                              .placeholder { background-color: #fef3c7; padding: 2px 4px; border-radius: 2px; font-size: 0.85em; }
                              .loop-placeholder { background-color: #dbeafe; padding: 2px 4px; border-radius: 2px; font-size: 0.85em; }
                              ${template.template_styles || ''}
                            </style>
                          </head>
                          <body>
                            ${template.template_content
                              .replace(/\{\{([^}]+)\}\}/g, '<span class="placeholder">{{$1}}</span>')
                              .replace(/\{\{#each\s+([^}]+)\}\}/g, '<div class="loop-placeholder">{{#each $1}}</div>')
                              .replace(/\{\{\/each\}\}/g, '<div class="loop-placeholder">{{/each}}</div>')
                            }
                          </body>
                          </html>
                        `}
                        className="w-full border-0"
                        style={{ height: '384px' }} // Initial height, will be auto-adjusted
                        title="Template Preview"
                      />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-yellow-200 rounded border"></div>
                      <span className="text-sm text-slate-600 dark:text-slate-400">Regular Placeholders</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-blue-200 rounded border"></div>
                      <span className="text-sm text-slate-600 dark:text-slate-400">Loop Blocks</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Description */}
              {template.description && (
                <Card>
                  <CardHeader>
                    <CardTitle>Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-700 dark:text-slate-300">{template.description}</p>
                  </CardContent>
                </Card>
              )}

              {/* Usage Notes */}
              {template.usage_notes && (
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-700 dark:text-slate-300 whitespace-pre-wrap">
                      {template.usage_notes}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>

            <div className="space-y-6">
              {/* Template Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Template Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-slate-500" />
                    <div>
                      <p className="text-sm text-slate-500">Created</p>
                      <p className="font-medium">
                        {new Date(template.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-slate-500" />
                    <div>
                      <p className="text-sm text-slate-500">Last Updated</p>
                      <p className="font-medium">
                        {new Date(template.updated_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-slate-500" />
                    <div>
                      <p className="text-sm text-slate-500">Language</p>
                      <p className="font-medium">{template.language}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Palette className="h-4 w-4 text-slate-500" />
                    <div>
                      <p className="text-sm text-slate-500">Page Size</p>
                      <p className="font-medium">{template.page_size} - {template.page_orientation}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Format Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Format Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-500">Currency</span>
                    <span className="font-medium">{template.currency_format}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-500">Date Format</span>
                    <span className="font-medium">{template.date_format}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-500">Number Format</span>
                    <span className="font-medium">{template.number_format}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Content Tab */}
        <TabsContent value="content" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Content</CardTitle>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                Raw template content with placeholders
              </p>
            </CardHeader>
            <CardContent>
              <pre className="bg-slate-50 dark:bg-slate-800 dark:text-slate-200 rounded-lg p-4 text-sm overflow-x-auto">
                <code>{template.template_content}</code>
              </pre>
            </CardContent>
          </Card>

          {template.template_styles && (
            <Card>
              <CardHeader>
                <CardTitle>Custom Styles</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 text-sm overflow-x-auto">
                  <code>{template.template_styles}</code>
                </pre>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Page Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-slate-500">Page Size</span>
                  <span className="font-medium">{template.page_size}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-slate-500">Orientation</span>
                  <span className="font-medium capitalize">{template.page_orientation}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Margins (mm)</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-slate-500">Top</span>
                  <span className="font-medium">{template.margin_top}mm</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-slate-500">Bottom</span>
                  <span className="font-medium">{template.margin_bottom}mm</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-slate-500">Left</span>
                  <span className="font-medium">{template.margin_left}mm</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-slate-500">Right</span>
                  <span className="font-medium">{template.margin_right}mm</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Versions Tab */}
        <TabsContent value="versions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Version History</CardTitle>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                All versions of this template
              </p>
            </CardHeader>
            <CardContent>
              {versionsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  <span>Loading versions...</span>
                </div>
              ) : versions.length === 0 ? (
                <p className="text-center text-slate-500 py-8">No version history available</p>
              ) : (
                <div className="space-y-3">
                  {versions.map((version) => (
                    <div
                      key={version.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <Badge variant="secondary" className="font-mono">
                          v{version.version}
                        </Badge>
                        <div>
                          <p className="font-medium">{version.template_name}</p>
                          <p className="text-sm text-slate-500">
                            {new Date(version.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(version)}
                        {version.id !== template.id && (
                          <Link href={`/documents/templates/${version.id}`}>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.isOpen} onOpenChange={(open) => !open && setDeleteDialog({ isOpen: false })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Template</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{template.template_name}"? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({ isOpen: false })}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={actionLoading}
            >
              {actionLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}