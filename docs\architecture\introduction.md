# Introduction

This document outlines the complete fullstack architecture for DYY Trading Fruit Export Management System, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for the modern Next.js/Supabase fullstack application where these concerns are increasingly intertwined.

## Starter Template or Existing Project

**Decision**: Custom Next.js + Supabase configuration (not using existing starter template)

Based on the PRD, this is a greenfield project with specific technology choices already defined:
- **Frontend**: Next.js 14+ App Router with TypeScript and Tailwind CSS
- **Backend**: Supabase (PostgreSQL with Row Level Security)
- **UI Components**: ShadCN UI component library
- **Mobile**: Progressive Web App (PWA) capabilities

The T3 Stack provides an excellent foundation, but since Supabase is specified instead of tRPC/Prisma, we'll create a custom starter configuration that combines Next.js 14+ App Router, Supabase client configuration, ShadCN UI setup, and TypeScript configuration optimized for Supabase types.

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2024-08-13 | 1.0 | Initial architecture document creation | Claude Architect |
