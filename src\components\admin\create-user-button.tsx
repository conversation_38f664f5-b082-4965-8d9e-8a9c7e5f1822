'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { CreateUserDialog } from './create-user-dialog'

export function CreateUserButton() {
  const [showDialog, setShowDialog] = useState(false)

  return (
    <>
      <Button
        onClick={() => setShowDialog(true)}
        className="bg-orange-500 hover:bg-orange-600 text-white"
      >
        <Plus className="h-4 w-4 mr-2" />
        Create User
      </Button>

      <CreateUserDialog open={showDialog} onOpenChange={setShowDialog} />
    </>
  )
}
