'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Menu, X, LogOut } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/use-auth'
import type { UserProfile } from '@/lib/supabase/auth'
import {
  getNavigationForRole,
  type NavigationItem,
} from '@/lib/constants/routes'

interface MobileNavProps {
  className?: string
}

export function MobileNav({ className }: MobileNavProps) {
  const { profile, loading, signOut } = useAuth()
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    if (profile) {
      // Use mobile navigation for drivers, regular navigation for others
      const isMobile = profile.role === 'driver'
      setNavigationItems(getNavigationForRole(profile.role, isMobile))
    }
  }, [profile])

  // Close menu when route changes
  useEffect(() => {
    setIsOpen(false)
  }, [pathname])

  function isActive(href: string): boolean {
    if (href === '/dashboard/overview') {
      return pathname === '/dashboard' || pathname === '/dashboard/overview'
    }
    if (href === '/driver/dashboard') {
      return pathname === '/driver' || pathname === '/driver/dashboard'
    }
    return pathname === href || pathname.startsWith(href + '/')
  }

  async function handleSignOut() {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  if (loading || !profile) {
    return (
      <div className={cn('lg:hidden', className)}>
        <Button variant="ghost" size="icon" disabled>
          <Menu className="h-6 w-6" />
        </Button>
      </div>
    )
  }

  return (
    <div className={cn('lg:hidden', className)}>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className="text-white hover:bg-white/10"
      >
        {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Mobile menu overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setIsOpen(false)}
          />

          <div className="relative flex w-full max-w-sm flex-col bg-slate-900 p-6 h-full">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="h-8 w-8 rounded-lg bg-orange-500 flex items-center justify-center">
                  <span className="text-sm font-bold text-white">DYY</span>
                </div>
                <span className="text-white font-semibold">DYY Trading</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(false)}
                className="text-white hover:bg-white/10"
              >
                <X className="h-6 w-6" />
              </Button>
            </div>

            {/* User Info */}
            <div className="mb-6 p-4 bg-slate-800 rounded-lg">
              <p className="text-white font-medium">
                {profile.first_name} {profile.last_name}
              </p>
              <p className="text-slate-400 capitalize text-sm">
                {profile.role.replace('_', ' ')}
              </p>
              {profile.company_id && (
                <p className="text-slate-500 text-xs mt-1">
                  Company ID: {profile.company_id.slice(-8)}
                </p>
              )}
            </div>

            {/* Navigation */}
            <nav className="flex-1 space-y-2">
              {navigationItems.map(item => (
                <div key={item.id}>
                  {item.children ? (
                    // For mobile, we'll show children as separate items
                    <>
                      <div className="px-3 py-2 text-xs font-semibold text-slate-400 uppercase tracking-wider">
                        {item.title}
                      </div>
                      {item.children.map(child => (
                        <Link
                          key={child.id}
                          href={child.href}
                          className={cn(
                            'flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors',
                            isActive(child.href)
                              ? 'bg-orange-500/20 text-orange-400'
                              : 'text-slate-300 hover:text-white hover:bg-slate-800'
                          )}
                          onClick={() => setIsOpen(false)}
                        >
                          <child.icon className="h-5 w-5" />
                          <span>{child.title}</span>
                          {child.badge && (
                            <span className="ml-auto bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                              {child.badge}
                            </span>
                          )}
                        </Link>
                      ))}
                    </>
                  ) : (
                    // Single item
                    <Link
                      href={item.href}
                      className={cn(
                        'flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors',
                        isActive(item.href)
                          ? 'bg-orange-500/20 text-orange-400'
                          : 'text-slate-300 hover:text-white hover:bg-slate-800'
                      )}
                      onClick={() => setIsOpen(false)}
                    >
                      <item.icon className="h-5 w-5" />
                      <span>{item.title}</span>
                      {item.badge && (
                        <span className="ml-auto bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )}
                </div>
              ))}
            </nav>

            {/* Sign Out */}
            <div className="mt-6 pt-6 border-t border-slate-800">
              <Button
                onClick={handleSignOut}
                variant="ghost"
                className="w-full justify-start text-slate-300 hover:text-white hover:bg-slate-800"
              >
                <LogOut className="h-5 w-5 mr-3" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
