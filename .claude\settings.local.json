{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\src\\app\\(dashboard)\\shipments/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\src\\hooks/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\src\\stores/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\.bmad-core\\tasks/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\.bmad-core/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\stories/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\prd/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\prd/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\docs\\architecture/**)", "Read(/D:\\Mywork\\NodeProject\\DYY\\dyy-trading-management\\.bmad-core\\templates/**)", "Read(//d/**)", "Bash(ls:*)"], "deny": [], "ask": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}