create table public.document_templates (
  id uuid not null default gen_random_uuid (),
  template_name text not null,
  document_type public.document_type_enum not null,
  version text null default '1.0'::text,
  template_content text not null,
  template_data jsonb null,
  template_styles text null,
  page_size text null default 'A4'::text,
  page_orientation text null default 'portrait'::text,
  margin_top integer null default 20,
  margin_bottom integer null default 20,
  margin_left integer null default 20,
  margin_right integer null default 20,
  language text null default 'en'::text,
  currency_format text null default 'USD'::text,
  date_format text null default 'YYYY-MM-DD'::text,
  number_format text null default 'en-US'::text,
  description text null,
  usage_notes text null,
  required_fields text[] null,
  is_active boolean null default true,
  is_default boolean null default false,
  created_by uuid not null,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  constraint document_templates_pkey primary key (id),
  constraint document_templates_template_name_key unique (template_name),
  constraint document_templates_created_by_fkey foreign KEY (created_by) references profiles (user_id),
  constraint valid_page_size check (
    (
      page_size = any (
        array[
          'A4'::text,
          'A3'::text,
          'Letter'::text,
          'Legal'::text,
          'A5'::text
        ]
      )
    )
  ),
  constraint positive_margins check (
    (
      (margin_top >= 0)
      and (margin_bottom >= 0)
      and (margin_left >= 0)
      and (margin_right >= 0)
    )
  ),
  constraint valid_language check ((language ~* '^[a-z]{2}(-[A-Z]{2})?$'::text)),
  constraint valid_orientation check (
    (
      page_orientation = any (array['portrait'::text, 'landscape'::text])
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_document_templates_type on public.document_templates using btree (document_type) TABLESPACE pg_default;

create index IF not exists idx_document_templates_active on public.document_templates using btree (is_active) TABLESPACE pg_default;

create index IF not exists idx_document_templates_default on public.document_templates using btree (is_default) TABLESPACE pg_default;

create trigger update_document_templates_updated_at BEFORE
update on document_templates for EACH row
execute FUNCTION update_updated_at_column ();


create table public.documents (
  id uuid not null default gen_random_uuid (),
  shipment_id uuid not null,
  document_type public.document_type_enum not null,
  document_name text not null,
  document_number text null,
  file_path text not null,
  file_name text not null,
  file_size_bytes integer null,
  file_type text null,
  file_hash text null,
  description text null,
  version integer null default 1,
  is_original boolean null default true,
  language text null default 'en'::text,
  issued_date date null,
  valid_until date null,
  issued_by text null,
  is_public boolean null default false,
  access_level text null default 'shipment'::text,
  shared_with_customer boolean null default false,
  shared_with_carrier boolean null default false,
  is_verified boolean null default false,
  verification_notes text null,
  verified_by uuid null,
  verified_at timestamp with time zone null,
  uploaded_by uuid not null,
  upload_source text null,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  constraint documents_pkey primary key (id),
  constraint documents_shipment_id_fkey foreign KEY (shipment_id) references shipments (id) on delete CASCADE,
  constraint documents_uploaded_by_fkey foreign KEY (uploaded_by) references profiles (user_id),
  constraint documents_verified_by_fkey foreign KEY (verified_by) references profiles (user_id),
  constraint positive_file_size check (
    (
      (file_size_bytes is null)
      or (file_size_bytes > 0)
    )
  ),
  constraint positive_version check ((version > 0)),
  constraint valid_access_level check (
    (
      access_level = any (
        array[
          'shipment'::text,
          'company'::text,
          'internal'::text,
          'public'::text
        ]
      )
    )
  ),
  constraint valid_date_range check (
    (
      (valid_until is null)
      or (issued_date is null)
      or (valid_until >= issued_date)
    )
  ),
  constraint valid_language check ((language ~* '^[a-z]{2}(-[A-Z]{2})?$'::text))
) TABLESPACE pg_default;

create index IF not exists idx_documents_shipment on public.documents using btree (shipment_id) TABLESPACE pg_default;

create index IF not exists idx_documents_type on public.documents using btree (document_type) TABLESPACE pg_default;

create index IF not exists idx_documents_uploaded_by on public.documents using btree (uploaded_by) TABLESPACE pg_default;

create index IF not exists idx_documents_public on public.documents using btree (is_public) TABLESPACE pg_default;

create index IF not exists idx_documents_verified on public.documents using btree (is_verified) TABLESPACE pg_default;

create index IF not exists idx_documents_created on public.documents using btree (created_at) TABLESPACE pg_default;

create index IF not exists idx_documents_shipment_type on public.documents using btree (shipment_id, document_type) TABLESPACE pg_default;

create index IF not exists idx_documents_type_verification on public.documents using btree (document_type, is_verified) TABLESPACE pg_default;

create index IF not exists idx_documents_file_type on public.documents using btree (file_type) TABLESPACE pg_default;

create index IF not exists idx_documents_file_size on public.documents using btree (file_size_bytes) TABLESPACE pg_default
where
  (file_size_bytes is not null);

create index IF not exists idx_documents_version_original on public.documents using btree (shipment_id, version, is_original) TABLESPACE pg_default;

create index IF not exists idx_documents_issued_valid on public.documents using btree (issued_date, valid_until) TABLESPACE pg_default;

create index IF not exists idx_documents_access_public on public.documents using btree (access_level, is_public) TABLESPACE pg_default;

create index IF not exists idx_documents_sharing on public.documents using btree (shared_with_customer, shared_with_carrier) TABLESPACE pg_default;

create trigger update_documents_updated_at BEFORE
update on documents for EACH row
execute FUNCTION update_updated_at_column ();


