import { describe, it, expect } from 'vitest'
import {
  validateTemplateName,
  validateDocumentType,
  validateTemplateContent,
  validatePageConfiguration,
  validateFormatSettings,
  validateVersion,
  validateRequiredFields,
  validateTemplateInsert,
  validateTemplateUpdate,
  validateTemplatePlaceholders,
  getDocumentTypeRequiredFields,
  VALID_DOCUMENT_TYPES,
  VALID_PAGE_SIZES,
  VALID_PAGE_ORIENTATIONS,
  VALID_CURRENCY_FORMATS,
  VALID_DATE_FORMATS,
  VALID_NUMBER_FORMATS,
} from '../template-validation'
import type { DocumentTemplateInsert } from '@/types/document-template'

describe('Template Validation', () => {
  describe('validateTemplateName', () => {
    it('should pass valid template names', () => {
      const validNames = [
        'Invoice Template',
        'Standard_Contract_2024',
        'My-Template-v2.0',
        'Test Template 123',
      ]

      validNames.forEach(name => {
        const errors = validateTemplateName(name)
        expect(errors).toHaveLength(0)
      })
    })

    it('should reject empty or short names', () => {
      const invalidNames = ['', '  ', 'ab', '12']

      invalidNames.forEach(name => {
        const errors = validateTemplateName(name)
        expect(errors.length).toBeGreaterThan(0)
        expect(errors[0].code).toBe(name.trim().length === 0 ? 'REQUIRED' : 'MIN_LENGTH')
      })
    })

    it('should reject names that are too long', () => {
      const longName = 'a'.repeat(256)
      const errors = validateTemplateName(longName)
      
      expect(errors).toHaveLength(1)
      expect(errors[0].code).toBe('MAX_LENGTH')
    })

    it('should reject names with invalid characters', () => {
      const invalidNames = ['Template<script>', 'File\\Path', 'Name:Invalid', 'Bad*Name']

      invalidNames.forEach(name => {
        const errors = validateTemplateName(name)
        expect(errors.length).toBeGreaterThan(0)
        expect(errors.some(e => e.code === 'INVALID_CHARACTERS')).toBe(true)
      })
    })
  })

  describe('validateDocumentType', () => {
    it('should pass valid document types', () => {
      VALID_DOCUMENT_TYPES.forEach(type => {
        const errors = validateDocumentType(type)
        expect(errors).toHaveLength(0)
      })
    })

    it('should reject empty document type', () => {
      const errors = validateDocumentType('')
      expect(errors).toHaveLength(1)
      expect(errors[0].code).toBe('REQUIRED')
    })

    it('should reject invalid document types', () => {
      const invalidTypes = ['invalid_type', 'custom_document', 'not_supported']

      invalidTypes.forEach(type => {
        const errors = validateDocumentType(type)
        expect(errors).toHaveLength(1)
        expect(errors[0].code).toBe('INVALID_VALUE')
      })
    })
  })

  describe('validateTemplateContent', () => {
    it('should pass valid template content', () => {
      const validContent = [
        'Simple text content',
        '<h1>HTML Template</h1><p>Content with {{placeholder}}</p>',
        'Invoice #{{invoice_number}} for {{customer_name}}',
        'Complex template with multiple {{field1}} and {{field2}} placeholders',
      ]

      validContent.forEach(content => {
        const errors = validateTemplateContent(content)
        expect(errors).toHaveLength(0)
      })
    })

    it('should reject empty content', () => {
      const emptyContent = ['', '  ', '\n\t  ']

      emptyContent.forEach(content => {
        const errors = validateTemplateContent(content)
        expect(errors).toHaveLength(1)
        expect(errors[0].code).toBe('REQUIRED')
      })
    })

    it('should detect security risks', () => {
      const dangerousContent = [
        '<script>alert("xss")</script>',
        '<div onclick="malicious()">Click me</div>',
        '<img onerror="hack()" src="invalid">',
        '<iframe onload="steal()"></iframe>',
        'javascript:void(0)',
      ]

      dangerousContent.forEach(content => {
        const errors = validateTemplateContent(content)
        expect(errors.length).toBeGreaterThan(0)
        expect(errors.some(e => e.code === 'SECURITY_RISK')).toBe(true)
      })
    })

    it('should reject content that is too large', () => {
      const largeContent = 'x'.repeat(1000001) // > 1MB
      const errors = validateTemplateContent(largeContent)
      
      expect(errors).toHaveLength(1)
      expect(errors[0].code).toBe('MAX_SIZE')
    })
  })

  describe('validatePageConfiguration', () => {
    it('should pass valid page configurations', () => {
      const errors = validatePageConfiguration('A4', 'portrait', 20, 20, 20, 20)
      expect(errors).toHaveLength(0)
    })

    it('should reject invalid page sizes', () => {
      const errors = validatePageConfiguration('Invalid', 'portrait', 20, 20, 20, 20)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.field === 'page_size')).toBe(true)
    })

    it('should reject invalid page orientations', () => {
      const errors = validatePageConfiguration('A4', 'diagonal', 20, 20, 20, 20)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.field === 'page_orientation')).toBe(true)
    })

    it('should reject negative margins', () => {
      const errors = validatePageConfiguration('A4', 'portrait', -5, 20, 20, 20)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.field === 'margin_top' && e.code === 'NEGATIVE_VALUE')).toBe(true)
    })

    it('should reject margins that are too large', () => {
      const errors = validatePageConfiguration('A4', 'portrait', 250, 20, 20, 20)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.field === 'margin_top' && e.code === 'MAX_VALUE')).toBe(true)
    })

    it('should reject non-numeric margins', () => {
      const errors = validatePageConfiguration('A4', 'portrait', NaN, 20, 20, 20)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.field === 'margin_top' && e.code === 'INVALID_TYPE')).toBe(true)
    })
  })

  describe('validateFormatSettings', () => {
    it('should pass valid format settings', () => {
      const errors = validateFormatSettings('en', 'USD', 'YYYY-MM-DD', 'en-US')
      expect(errors).toHaveLength(0)
    })

    it('should pass extended language codes', () => {
      const errors = validateFormatSettings('en-US', 'USD', 'YYYY-MM-DD', 'en-US')
      expect(errors).toHaveLength(0)
    })

    it('should reject invalid language codes', () => {
      const invalidLanguages = ['eng', 'english', 'EN-us', '123', 'e']

      invalidLanguages.forEach(lang => {
        const errors = validateFormatSettings(lang, 'USD', 'YYYY-MM-DD', 'en-US')
        expect(errors.length).toBeGreaterThan(0)
        expect(errors.some(e => e.field === 'language')).toBe(true)
      })
    })

    it('should reject invalid currency formats', () => {
      const errors = validateFormatSettings('en', 'INVALID', 'YYYY-MM-DD', 'en-US')
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.field === 'currency_format')).toBe(true)
    })

    it('should reject invalid date formats', () => {
      const errors = validateFormatSettings('en', 'USD', 'INVALID-FORMAT', 'en-US')
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.field === 'date_format')).toBe(true)
    })

    it('should reject invalid number formats', () => {
      const errors = validateFormatSettings('en', 'USD', 'YYYY-MM-DD', 'invalid-locale')
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.field === 'number_format')).toBe(true)
    })
  })

  describe('validateVersion', () => {
    it('should pass valid semantic versions', () => {
      const validVersions = ['1.0', '2.1', '1.0.0', '2.3.1', '1.0-alpha', '2.1.3-beta.1']

      validVersions.forEach(version => {
        const errors = validateVersion(version)
        expect(errors).toHaveLength(0)
      })
    })

    it('should reject empty versions', () => {
      const emptyVersions = ['', '  ', '\t']

      emptyVersions.forEach(version => {
        const errors = validateVersion(version)
        expect(errors).toHaveLength(1)
        expect(errors[0].code).toBe('REQUIRED')
      })
    })

    it('should reject invalid version formats', () => {
      const invalidVersions = ['1', 'v1.0', '1.0.0.0', 'version-1', '1.0-', 'alpha']

      invalidVersions.forEach(version => {
        const errors = validateVersion(version)
        expect(errors.length).toBeGreaterThan(0)
        expect(errors[0].code).toBe('INVALID_FORMAT')
      })
    })
  })

  describe('validateRequiredFields', () => {
    it('should pass valid required fields arrays', () => {
      const validArrays = [
        null,
        [],
        ['field1', 'field2'],
        ['invoice_number', 'customer_name', 'amount'],
      ]

      validArrays.forEach(fields => {
        const errors = validateRequiredFields(fields)
        expect(errors).toHaveLength(0)
      })
    })

    it('should reject arrays with invalid items', () => {
      const invalidArrays = [
        ['field1', '', 'field3'], // Empty string
        ['field1', null as any, 'field3'], // Null item
        ['field1', 123 as any], // Non-string item
      ]

      invalidArrays.forEach(fields => {
        const errors = validateRequiredFields(fields)
        expect(errors.length).toBeGreaterThan(0)
        expect(errors.some(e => e.code === 'INVALID_ARRAY_ITEM')).toBe(true)
      })
    })

    it('should reject arrays with duplicates', () => {
      const duplicateArray = ['field1', 'field2', 'field1']
      const errors = validateRequiredFields(duplicateArray)
      
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.code === 'DUPLICATE_VALUES')).toBe(true)
    })
  })

  describe('validateTemplateInsert', () => {
    const validTemplate: DocumentTemplateInsert = {
      template_name: 'Test Template',
      document_type: 'invoice_fob',
      template_content: 'Invoice content with {{placeholder}}',
      version: '1.0',
    }

    it('should pass valid template insert', () => {
      const result = validateTemplateInsert(validTemplate)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should validate all required fields', () => {
      const invalidTemplate: DocumentTemplateInsert = {
        template_name: '',
        document_type: 'invalid_type' as any,
        template_content: '',
        version: 'invalid',
      }

      const result = validateTemplateInsert(invalidTemplate)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(3)
    })

    it('should apply defaults to optional fields', () => {
      const result = validateTemplateInsert(validTemplate)
      expect(result.isValid).toBe(true)
    })
  })

  describe('validateTemplatePlaceholders', () => {
    it('should pass content with valid placeholders', () => {
      const content = 'Invoice {{invoice_number}} for {{customer.name}} total {{amount}}'
      const result = validateTemplatePlaceholders(content)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should warn about content with no placeholders', () => {
      const content = 'Static content with no placeholders'
      const result = validateTemplatePlaceholders(content)
      
      expect(result.isValid).toBe(true)
      expect(result.warnings?.length).toBeGreaterThan(0)
      expect(result.warnings?.[0].code).toBe('NO_PLACEHOLDERS')
    })

    it('should reject content with empty placeholders', () => {
      const content = 'Content with {{}} empty placeholder'
      const result = validateTemplatePlaceholders(content)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'EMPTY_PLACEHOLDER')).toBe(true)
    })

    it('should reject content with invalid placeholder names', () => {
      const content = 'Content with {{123invalid}} and {{-invalid}} and {{in valid}}'
      const result = validateTemplatePlaceholders(content)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.every(e => e.code === 'INVALID_PLACEHOLDER_NAME')).toBe(true)
    })

    it('should pass content with valid complex placeholders', () => {
      const content = 'Content with {{shipment.number}} and {{customer_info.billing_address}}'
      const result = validateTemplatePlaceholders(content)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('getDocumentTypeRequiredFields', () => {
    it('should return correct fields for booking confirmation', () => {
      const fields = getDocumentTypeRequiredFields('booking_confirmation')
      expect(fields).toContain('booking_number')
      expect(fields).toContain('vessel_name')
      expect(fields).toContain('etd_date')
      expect(fields).toContain('eta_date')
    })

    it('should return correct fields for invoices', () => {
      const fobFields = getDocumentTypeRequiredFields('invoice_fob')
      const cifFields = getDocumentTypeRequiredFields('invoice_cif')
      
      expect(fobFields).toContain('invoice_number')
      expect(fobFields).toContain('customer_name')
      expect(cifFields).toContain('total_amount')
      expect(cifFields).toContain('currency')
    })

    it('should return correct fields for shipping instruction', () => {
      const fields = getDocumentTypeRequiredFields('shipping_instruction')
      expect(fields).toContain('shipment_number')
      expect(fields).toContain('vessel_name')
      expect(fields).toContain('port_of_loading')
      expect(fields).toContain('port_of_discharge')
    })

    it('should return correct fields for bill of lading', () => {
      const fields = getDocumentTypeRequiredFields('bill_of_lading')
      expect(fields).toContain('bl_number')
      expect(fields).toContain('vessel_name')
      expect(fields).toContain('voyage_number')
      expect(fields).toContain('shipper')
      expect(fields).toContain('consignee')
    })

    it('should return common fields for other document types', () => {
      const fields = getDocumentTypeRequiredFields('other')
      expect(fields).toContain('created_date')
      expect(fields).toContain('created_by')
      expect(fields.length).toBe(2) // Only common fields
    })

    it('should always include common fields', () => {
      VALID_DOCUMENT_TYPES.forEach(type => {
        const fields = getDocumentTypeRequiredFields(type)
        expect(fields).toContain('created_date')
        expect(fields).toContain('created_by')
      })
    })
  })

  describe('Constants validation', () => {
    it('should have valid document types', () => {
      expect(VALID_DOCUMENT_TYPES).toContain('booking_confirmation')
      expect(VALID_DOCUMENT_TYPES).toContain('invoice_fob')
      expect(VALID_DOCUMENT_TYPES).toContain('invoice_cif')
      expect(VALID_DOCUMENT_TYPES.length).toBeGreaterThan(0)
    })

    it('should have valid page sizes', () => {
      expect(VALID_PAGE_SIZES).toContain('A4')
      expect(VALID_PAGE_SIZES).toContain('Letter')
      expect(VALID_PAGE_SIZES.length).toBeGreaterThan(0)
    })

    it('should have valid currency formats', () => {
      expect(VALID_CURRENCY_FORMATS).toContain('USD')
      expect(VALID_CURRENCY_FORMATS).toContain('EUR')
      expect(VALID_CURRENCY_FORMATS.length).toBeGreaterThan(0)
    })
  })
})