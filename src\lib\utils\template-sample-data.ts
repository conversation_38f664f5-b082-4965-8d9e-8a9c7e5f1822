/**
 * Template Sample Data Utilities
 * Story 5.1: Document Template Management System
 * 
 * Provides sample data for template preview functionality
 */

import { format } from 'date-fns'

export interface SampleDataSet {
  // Shipment Data
  shipment_number: string
  transport_mode: string
  etd: string
  eta: string
  closing_time: string
  origin_port: string
  destination_port: string
  vessel_name: string
  voyage_number: string
  booking_reference: string
  container_type: string
  container_count: number
  total_gross_weight: number
  total_net_weight: number
  total_volume: number
  
  // Customer Data
  customer_name: string
  customer_code: string
  customer_address: string
  customer_phone: string
  customer_email: string
  
  // Shipper Data
  shipper_name: string
  shipper_address: string
  shipper_phone: string
  shipper_email: string
  
  // Consignee Data
  consignee_name: string
  consignee_address: string
  consignee_phone: string
  consignee_email: string
  
  // Notify Party Data
  notify_party_name: string
  notify_party_address: string
  notify_party_phone: string
  notify_party_email: string
  
  // Factory Data
  factory_name: string
  factory_address: string
  factory_phone: string
  
  // Forwarder Agent Data
  forwarder_agent_name: string
  forwarder_agent_address: string
  forwarder_agent_phone: string
  forwarder_agent_email: string
  
  // Product Data
  products: Array<{
    product_name: string
    variety: string
    grade: string
    quantity: number
    unit: string
    unit_price_fob: number
    unit_price_cif: number
    total_amount: number
    net_weight: number
    gross_weight: number
  }>
  
  // Financial Data
  total_amount_fob: number
  total_amount_cif: number
  currency: string
  payment_terms: string
  
  // Document Data
  invoice_number: string
  contract_number: string
  packing_list_number: string
  
  // Date placeholders
  current_date: string
  issue_date: string
  
  // Company Data
  company_name: string
  company_address: string
  company_phone: string
  company_email: string
  company_logo_url: string
}

/**
 * Generate comprehensive sample data for template preview
 */
export function generateSampleData(): SampleDataSet {
  const currentDate = new Date()
  const etdDate = new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000) // +7 days
  const etaDate = new Date(currentDate.getTime() + 21 * 24 * 60 * 60 * 1000) // +21 days
  
  // Define products first so we can calculate totals
  const products = [
    {
      product_name: 'Fresh Mango',
      variety: 'Nam Dok Mai',
      grade: 'Grade A',
      quantity: 800,
      unit: 'Cartons (5kg each)',
      unit_price_fob: 12.50,
      unit_price_cif: 14.25,
      total_amount: 10000,
      net_weight: 4000,
      gross_weight: 4400
    },
    {
      product_name: 'Fresh Pineapple',
      variety: 'Sweet Gold',
      grade: 'Premium',
      quantity: 1200,
      unit: 'Cartons (6 pieces each)',
      unit_price_fob: 8.75,
      unit_price_cif: 10.15,
      total_amount: 10500,
      net_weight: 7200,
      gross_weight: 8040
    },
    {
      product_name: 'Dried Coconut',
      variety: 'Organic',
      grade: 'Export Quality',
      quantity: 500,
      unit: 'Bags (25kg each)',
      unit_price_fob: 28.00,
      unit_price_cif: 31.50,
      total_amount: 14000,
      net_weight: 12500,
      gross_weight: 13000
    }
  ]

  // Calculate totals from products
  const totalNetWeight = products.reduce((sum, p) => sum + p.net_weight, 0)
  const totalGrossWeight = products.reduce((sum, p) => sum + p.gross_weight, 0)
  const totalAmountFob = products.reduce((sum, p) => sum + p.total_amount, 0)
  const totalAmountCif = totalAmountFob * 1.15 // CIF is typically 15% higher than FOB
  
  return {
    // Shipment Data
    shipment_number: 'EXSEA-BKK-241201-001',
    transport_mode: 'Sea',
    etd: format(etdDate, 'yyyy-MM-dd'),
    eta: format(etaDate, 'yyyy-MM-dd'),
    closing_time: format(new Date(etdDate.getTime() - 24 * 60 * 60 * 1000), 'yyyy-MM-dd HH:mm'),
    origin_port: 'Bangkok Port (THBKK)',
    destination_port: 'Los Angeles Port (USLAX)',
    vessel_name: 'MSC TOKYO VII',
    voyage_number: 'V2024-156',
    booking_reference: 'MSC-BKG-789456',
    container_type: '40HC',
    container_count: 2,
    total_gross_weight: totalGrossWeight,
    total_net_weight: totalNetWeight,
    total_volume: 67.6,
    
    // Customer Data
    customer_name: 'Global Fresh Import Co., Ltd.',
    customer_code: 'CUST-GFI-001',
    customer_address: '1234 Commerce Street, Los Angeles, CA 90015, USA',
    customer_phone: '******-555-0123',
    customer_email: '<EMAIL>',
    
    // Shipper Data
    shipper_name: 'DYY Trading Co., Ltd.',
    shipper_address: '123 Export Zone, Samut Prakan 10280, Thailand',
    shipper_phone: '+66-2-123-4567',
    shipper_email: '<EMAIL>',
    
    // Consignee Data
    consignee_name: 'Fresh Market Distribution LLC',
    consignee_address: '5678 Harbor Blvd, Long Beach, CA 90802, USA',
    consignee_phone: '******-555-0456',
    consignee_email: '<EMAIL>',
    
    // Notify Party Data
    notify_party_name: 'Pacific Logistics Services',
    notify_party_address: '9876 Terminal Island, San Pedro, CA 90731, USA',
    notify_party_phone: '******-555-0789',
    notify_party_email: '<EMAIL>',
    
    // Factory Data
    factory_name: 'Golden Fruit Processing Plant',
    factory_address: '456 Industrial Estate, Chonburi 20130, Thailand',
    factory_phone: '+66-38-123-456',
    
    // Forwarder Agent Data
    forwarder_agent_name: 'Bangkok Freight Solutions Ltd.',
    forwarder_agent_address: '789 Logistics Park, Bangkok 10100, Thailand',
    forwarder_agent_phone: '+66-2-987-6543',
    forwarder_agent_email: '<EMAIL>',
    
    // Product Data
    products,
    
    // Financial Data
    total_amount_fob: totalAmountFob,
    total_amount_cif: totalAmountCif,
    currency: 'USD',
    payment_terms: 'L/C at sight',
    
    // Document Data
    invoice_number: 'INV-2024-1156',
    contract_number: 'CTR-GFI-2024-089',
    packing_list_number: 'PL-2024-1156',
    
    // Date placeholders
    current_date: format(currentDate, 'yyyy-MM-dd'),
    issue_date: format(currentDate, 'dd/MM/yyyy'),
    
    // Company Data
    company_name: 'DYY Trading Co., Ltd.',
    company_address: '123 Export Zone, Samut Prakan 10280, Thailand\nTel: +66-2-123-4567 | Email: <EMAIL>',
    company_phone: '+66-2-123-4567',
    company_email: '<EMAIL>',
    company_logo_url: '/images/company-logo.png'
  }
}

/**
 * Replace placeholders in template content with sample data
 */
export function replacePlaceholders(templateContent: string, sampleData: SampleDataSet, expanded = false): string {
  let processedContent = templateContent

  // Replace simple placeholders
  Object.entries(sampleData).forEach(([key, value]) => {
    if (typeof value === 'string' || typeof value === 'number') {
      const placeholder = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
      processedContent = processedContent.replace(placeholder, String(value))
    }
  })

  // Handle product array placeholders with table generation
  if (processedContent.includes('{{products_table}}')) {
    const productsTable = generateProductsTable(sampleData.products, expanded)
    processedContent = processedContent.replace(/{{products_table}}/g, productsTable)
  }

  // Handle individual product placeholders if needed
  sampleData.products.forEach((product, index) => {
    Object.entries(product).forEach(([key, value]) => {
      const placeholder = new RegExp(`{{\\s*product_${index + 1}_${key}\\s*}}`, 'g')
      processedContent = processedContent.replace(placeholder, String(value))
    })
  })

  return processedContent
}

/**
 * Generate HTML table for products
 */
function generateProductsTable(products: SampleDataSet['products'], expanded = false): string {
  const tableRows = products.map((product, index) => `
    <tr>
      <td style="padding: 6px; border: 1px solid #cbd5e1; text-align: center; font-size: 12px;">${index + 1}</td>
      <td style="padding: 6px; border: 1px solid #cbd5e1; font-size: 12px;">${product.product_name}</td>
      <td style="padding: 6px; border: 1px solid #cbd5e1; font-size: 12px;">${product.variety}</td>
      <td style="padding: 6px; border: 1px solid #cbd5e1; text-align: center; font-size: 12px;">${product.quantity}</td>
      <td style="padding: 6px; border: 1px solid #cbd5e1; text-align: center; font-size: 12px;">${product.unit}</td>
      <td style="padding: 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 12px;">$${product.unit_price_fob.toFixed(2)}</td>
      <td style="padding: 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 12px;">$${product.total_amount.toLocaleString()}</td>
      <td style="padding: 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 12px;">${product.net_weight.toLocaleString()} kg</td>
    </tr>
  `).join('')

  const widthSettings = expanded ? {
    table: "width: 100%; min-width: 800px;",
    no: "width: 6%;",
    product: "width: 22%;", 
    variety: "width: 16%;",
    quantity: "width: 10%;",
    unit: "width: 14%;",
    unitPrice: "width: 12%;",
    totalAmount: "width: 12%;",
    netWeight: "width: 8%;"
  } : {
    table: "width: 100%;",
    no: "width: 8%;",
    product: "width: 20%;",
    variety: "width: 15%;", 
    quantity: "width: 10%;",
    unit: "width: 12%;",
    unitPrice: "width: 12%;",
    totalAmount: "width: 13%;",
    netWeight: "width: 10%;"
  }

  return `
    <table style="${widthSettings.table} border-collapse: collapse; table-layout: fixed; font-size: 14px; margin: 1em 0;">
      <thead>
        <tr style="background-color: #f1f5f9;">
          <th style="${widthSettings.no} padding: 8px 6px; border: 1px solid #cbd5e1; text-align: center; font-size: 13px;">No.</th>
          <th style="${widthSettings.product} padding: 8px 6px; border: 1px solid #cbd5e1; font-size: 13px;">Product Name</th>
          <th style="${widthSettings.variety} padding: 8px 6px; border: 1px solid #cbd5e1; font-size: 13px;">Variety</th>
          <th style="${widthSettings.quantity} padding: 8px 6px; border: 1px solid #cbd5e1; text-align: center; font-size: 13px;">Quantity</th>
          <th style="${widthSettings.unit} padding: 8px 6px; border: 1px solid #cbd5e1; text-align: center; font-size: 13px;">Unit</th>
          <th style="${widthSettings.unitPrice} padding: 8px 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 13px;">Unit Price (FOB)</th>
          <th style="${widthSettings.totalAmount} padding: 8px 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 13px;">Total Amount</th>
          <th style="${widthSettings.netWeight} padding: 8px 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 13px;">Net Weight</th>
        </tr>
      </thead>
      <tbody>
        ${tableRows}
        <tr style="background-color: #f8fafc; font-weight: 600;">
          <td colspan="6" style="padding: 8px 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 13px;">Total:</td>
          <td style="padding: 8px 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 13px;">$${products.reduce((sum, p) => sum + p.total_amount, 0).toLocaleString()}</td>
          <td style="padding: 8px 6px; border: 1px solid #cbd5e1; text-align: right; font-size: 13px;">${products.reduce((sum, p) => sum + p.net_weight, 0).toLocaleString()} kg</td>
        </tr>
      </tbody>
    </table>
  `
}

/**
 * Get available placeholder names for auto-complete
 */
export function getAvailablePlaceholders(): string[] {
  const sampleData = generateSampleData()
  const basicPlaceholders = Object.keys(sampleData).filter(key => 
    typeof sampleData[key as keyof SampleDataSet] === 'string' || 
    typeof sampleData[key as keyof SampleDataSet] === 'number'
  )
  
  const productPlaceholders = [
    'products_table',
    'product_1_product_name', 'product_1_variety', 'product_1_quantity',
    'product_2_product_name', 'product_2_variety', 'product_2_quantity',
    'product_3_product_name', 'product_3_variety', 'product_3_quantity'
  ]
  
  return [...basicPlaceholders, ...productPlaceholders].sort()
}