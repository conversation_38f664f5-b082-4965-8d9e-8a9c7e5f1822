'use client'

import { useState, useEffect, useMemo, useC<PERSON>back } from 'react'
import { <PERSON>Right, CheckCircle, AlertTriangle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { StatusSelector } from './status-selector'
import { PhotoCapture } from './photo-capture'
import { LocationCapture } from './location-capture'
import { useStatusUpdate } from '@/hooks/use-status-update'
import { getStatusDisplayName, getPhotoRequirements } from '@/lib/utils/status-workflow'
import { getLocalizedStatusDisplayName } from '@/lib/utils/status-workflow-i18n'
import { useLanguage } from '@/hooks/use-language'
import type { ShipmentStatus, GPSCoordinates } from '@/types/status-update'

interface StatusUpdateFormProps {
  shipmentId: string
  currentStatus: ShipmentStatus
  onUpdateComplete?: (result: any) => void
  onCancel?: () => void
  disabled?: boolean
}

interface FormData {
  selectedStatus: ShipmentStatus | null
  photos: File[]
  coordinates: GPSCoordinates | null
  notes: string
  locationDescription: string
}

export function StatusUpdateForm({
  shipmentId,
  currentStatus,
  onUpdateComplete,
  onCancel,
  disabled = false
}: StatusUpdateFormProps) {
  const { t, locale } = useLanguage()
  const [formData, setFormData] = useState<FormData>({
    selectedStatus: null,
    photos: [],
    coordinates: null,
    notes: '',
    locationDescription: ''
  })

  const [showConfirmation, setShowConfirmation] = useState(false)
  const [currentStep, setCurrentStep] = useState<'status' | 'photos' | 'location' | 'notes' | 'confirm'>('status')
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  // Memoize the options object to prevent infinite re-renders
  const statusUpdateOptions = useMemo(() => ({
    onSuccess: (result: any) => {
      onUpdateComplete?.(result)
    },
    onError: (error: string) => {
      console.error('Status update error:', error)
    }
  }), [onUpdateComplete])

  const {
    isSubmitting,
    isUploading,
    uploadProgress,
    error,
    success,
    submitStatusUpdate,
    resetState,
    validateStatusUpdate
  } = useStatusUpdate(statusUpdateOptions)

  const maxNotesLength = 500

  // Memoize callback functions to prevent infinite re-renders
  const handlePhotosChange = useCallback((photos: File[]) => {
    setFormData(prev => ({ ...prev, photos }))
  }, [])

  const handleLocationCaptured = useCallback((coords: GPSCoordinates) => {
    setFormData(prev => ({ ...prev, coordinates: coords }))
  }, [])

  const handleStatusSelect = useCallback((status: ShipmentStatus) => {
    setFormData(prev => ({ ...prev, selectedStatus: status }))
  }, [])

  // Reset form when success changes
  useEffect(() => {
    if (success) {
      // Reset form after a short delay to show success state
      const timer = setTimeout(() => {
        resetState()
        setFormData({
          selectedStatus: null,
          photos: [],
          coordinates: null,
          notes: '',
          locationDescription: ''
        })
        setCurrentStep('status')
        setShowConfirmation(false)
      }, 2000)

      return () => clearTimeout(timer)
    }
  }, [success, resetState])

  const getValidationErrors = (): string[] => {
    const errors: string[] = []

    switch (currentStep) {
      case 'status':
        if (!formData.selectedStatus) {
          errors.push(t('statusUpdate.selectStatus'))
        }
        break

      case 'photos':
        if (formData.selectedStatus) {
          const photoValidation = validateStatusUpdate(
            formData.selectedStatus,
            formData.photos,
            formData.coordinates
          )
          if (!photoValidation.isValid) {
            errors.push(...photoValidation.errors.filter(err => err.includes('photo')))
          }
        }
        break

      case 'location':
        if (!formData.coordinates) {
          errors.push(t('statusUpdate.gpsRequired'))
        }
        break

      case 'notes':
        if (formData.notes.length > maxNotesLength) {
          errors.push(t('statusUpdate.notesLimit'))
        }
        break

      case 'confirm':
        const fullValidation = formData.selectedStatus 
          ? validateStatusUpdate(formData.selectedStatus, formData.photos, formData.coordinates)
          : { isValid: false, errors: [t('statusUpdate.selectStatus')] }
        
        if (!fullValidation.isValid) {
          errors.push(...fullValidation.errors)
        }
        break
    }

    return errors
  }

  const validateCurrentStep = (): boolean => {
    const errors = getValidationErrors()
    setValidationErrors(errors)
    return errors.length === 0
  }

  const isCurrentStepValid = (): boolean => {
    const errors = getValidationErrors()
    return errors.length === 0
  }

  const handleNextStep = () => {
    if (!validateCurrentStep()) return

    const steps: Array<'status' | 'photos' | 'location' | 'notes' | 'confirm'> = 
      ['status', 'photos', 'location', 'notes', 'confirm']
    
    const currentIndex = steps.indexOf(currentStep)
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    } else {
      setShowConfirmation(true)
    }
  }

  const handlePreviousStep = () => {
    const steps: Array<'status' | 'photos' | 'location' | 'notes' | 'confirm'> = 
      ['status', 'photos', 'location', 'notes', 'confirm']
    
    const currentIndex = steps.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const handleSubmit = async () => {
    if (!formData.selectedStatus || !formData.coordinates) return

    try {
      await submitStatusUpdate(shipmentId, {
        status_to: formData.selectedStatus,
        status_from: currentStatus,
        notes: formData.notes.trim() || undefined,
        latitude: formData.coordinates.latitude,
        longitude: formData.coordinates.longitude,
        location: formData.locationDescription || undefined,
        photos: formData.photos,
        coordinates: formData.coordinates
      })
    } catch (error) {
      console.error('Submit error:', error)
    }
  }

  const getStepTitle = (step: typeof currentStep): string => {
    switch (step) {
      case 'status': return t('statusUpdate.selectStatusStep')
      case 'photos': return t('statusUpdate.addPhotosStep')
      case 'location': return t('statusUpdate.confirmLocationStep')
      case 'notes': return t('statusUpdate.addNotesStep')
      case 'confirm': return t('statusUpdate.confirmUpdateStep')
      default: return ''
    }
  }

  const getStepProgress = (): number => {
    const steps = ['status', 'photos', 'location', 'notes', 'confirm']
    const currentIndex = steps.indexOf(currentStep)
    return ((currentIndex + 1) / steps.length) * 100
  }

  if (success) {
    return (
      <div className="bg-green-900/20 rounded-lg border border-green-500/30 p-6 text-center">
        <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">
          {t('statusUpdate.statusUpdatedSuccessfully')}
        </h3>
        <p className="text-green-300 text-sm">
          {t('statusUpdate.shipmentStatusUpdatedTo')} {getLocalizedStatusDisplayName(formData.selectedStatus!, locale)}
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-medium text-white">
            {t('statusUpdate.title')}
          </h2>
          <span className="text-sm text-slate-400">
            {getStepTitle(currentStep)}
          </span>
        </div>
        
        <Progress value={getStepProgress()} className="h-2" />
        
        <div className="text-xs text-slate-500 mt-2">
          {t('statusUpdate.step')} {['status', 'photos', 'location', 'notes', 'confirm'].indexOf(currentStep) + 1} {t('statusUpdate.of')} 5
        </div>
      </div>

      {/* Error Display */}
      {(error || validationErrors.length > 0) && (
        <Alert className="border-red-500/30 bg-red-500/10">
          <AlertTriangle className="h-4 w-4 text-red-400" />
          <AlertDescription className="text-red-300">
            {error || validationErrors[0]}
          </AlertDescription>
        </Alert>
      )}

      {/* Step Content */}
      <div className="space-y-4">
        {currentStep === 'status' && (
          <StatusSelector
            currentStatus={currentStatus}
            selectedStatus={formData.selectedStatus || undefined}
            onStatusSelect={handleStatusSelect}
            disabled={disabled || isSubmitting}
          />
        )}

        {currentStep === 'photos' && formData.selectedStatus && (
          <div className="space-y-4">
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
              <h3 className="text-sm font-medium text-slate-300 mb-2">
                {t('statusUpdate.photoRequirements')}
              </h3>
              <div className="text-sm text-slate-400">
                {(() => {
                  const requirements = getPhotoRequirements(formData.selectedStatus)
                  return `${requirements.minPhotos}-${requirements.maxPhotos} ${t('statusUpdate.photosRequiredFor')} ${getLocalizedStatusDisplayName(formData.selectedStatus, locale)}`
                })()}
              </div>
            </div>
            
            <PhotoCapture
              maxPhotos={5}
              onPhotosChange={handlePhotosChange}
              disabled={disabled || isSubmitting}
            />
          </div>
        )}

        {currentStep === 'location' && (
          <LocationCapture
            onLocationCaptured={handleLocationCaptured}
            disabled={disabled || isSubmitting}
            showConfirmation={true}
          />
        )}

        {currentStep === 'notes' && (
          <div className="space-y-4">
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
              <label htmlFor="notes" className="block text-sm font-medium text-slate-300 mb-2">
                {t('statusUpdate.notesOptional')}
              </label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => 
                  setFormData(prev => ({ ...prev, notes: e.target.value }))
                }
                placeholder={t('statusUpdate.notesPlaceholder')}
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 resize-none"
                rows={4}
                maxLength={maxNotesLength}
                disabled={disabled || isSubmitting}
              />
              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-slate-500">
                  {t('statusUpdate.notesOptionalDesc')}
                </span>
                <span className={`text-xs ${
                  formData.notes.length > maxNotesLength * 0.8 
                    ? 'text-orange-400' 
                    : 'text-slate-500'
                }`}>
                  {formData.notes.length}/{maxNotesLength}
                </span>
              </div>
            </div>
          </div>
        )}

        {currentStep === 'confirm' && (
          <div className="space-y-4">
            <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
              <h3 className="text-sm font-medium text-slate-300 mb-4">
                {t('statusUpdate.confirmStatusUpdate')}
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-400">{t('statusUpdate.statusChange')}:</span>
                  <span className="text-white">
                    {getLocalizedStatusDisplayName(currentStatus, locale)} → {getLocalizedStatusDisplayName(formData.selectedStatus!, locale)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-slate-400">{t('statusUpdate.photos')}:</span>
                  <span className="text-white">
                    {formData.photos.length} {formData.photos.length === 1 ? t('statusUpdate.photo') : t('statusUpdate.photos')}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-slate-400">{t('statusUpdate.location')}:</span>
                  <span className="text-white text-xs">
                    {formData.coordinates 
                      ? `${formData.coordinates.latitude.toFixed(6)}, ${formData.coordinates.longitude.toFixed(6)}`
                      : t('statusUpdate.notCaptured')
                    }
                  </span>
                </div>
                
                {formData.notes && (
                  <div>
                    <span className="text-slate-400 block mb-1">{t('statusUpdate.notes')}:</span>
                    <span className="text-white text-sm">
                      {formData.notes}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-slate-300">{t('statusUpdate.uploadingPhotos')}</span>
            <span className="text-xs text-slate-500">{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="h-2" />
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex space-x-3">
        {currentStep !== 'status' && (
          <Button
            onClick={handlePreviousStep}
            disabled={disabled || isSubmitting}
            variant="outline"
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            {t('statusUpdate.previous')}
          </Button>
        )}
        
        <Button
          onClick={onCancel}
          disabled={isSubmitting}
          variant="outline"
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          <X className="w-4 h-4 mr-2" />
          {t('common.cancel')}
        </Button>
        
        <Button
          onClick={currentStep === 'confirm' ? handleSubmit : handleNextStep}
          disabled={disabled || isSubmitting || !isCurrentStepValid()}
          className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              {isUploading ? t('statusUpdate.uploading') : t('statusUpdate.updating')}
            </>
          ) : currentStep === 'confirm' ? (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              {t('statusUpdate.update')}
            </>
          ) : (
            <>
              {t('statusUpdate.next')}
              <ArrowRight className="w-4 h-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  )
}