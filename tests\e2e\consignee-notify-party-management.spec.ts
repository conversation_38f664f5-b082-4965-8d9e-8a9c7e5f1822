import { test, expect } from '@playwright/test'

test.describe('Consignee-Notify Party Relationship Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to consignee-notify party relationships page
    await page.goto('/master-data/notify-parties')
    
    // Wait for page to load
    await page.waitForSelector('[data-testid="relationships-table"]', { timeout: 10000 })
  })

  test('should display notify party relationships page correctly', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Consignee-Notify Party Relationships')
    
    // Check description
    await expect(page.locator('p')).toContainText('Manage consignee-notify party relationships with communication preferences')
    
    // Check Add Relationship button exists
    await expect(page.locator('button', { hasText: 'Add Relationship' })).toBeVisible()
    
    // Check filters section
    await expect(page.locator('h3', { hasText: 'Filters' })).toBeVisible()
    
    // Check search input
    await expect(page.locator('input[placeholder*="Search"]')).toBeVisible()
    
    // Check consignee filter dropdown
    await expect(page.locator('[data-testid="consignee-filter"]')).toBeVisible()
    
    // Check active status filter
    await expect(page.locator('[data-testid="active-status-filter"]')).toBeVisible()
  })

  test('should filter relationships by search term', async ({ page }) => {
    // Type in search box
    await page.fill('input[placeholder*="Search"]', 'Test Consignee')
    
    // Wait for debounced search
    await page.waitForTimeout(500)
    
    // Check that search filter appears
    await expect(page.locator('[data-testid="search-filter"]')).toContainText('Test Consignee')
    
    // Clear search
    await page.click('button[aria-label="Clear search"]')
    
    // Check that filter is removed
    await expect(page.locator('[data-testid="search-filter"]')).not.toBeVisible()
  })

  test('should filter relationships by consignee', async ({ page }) => {
    // Open consignee dropdown
    await page.click('[data-testid="consignee-filter"]')
    
    // Select a consignee
    await page.click('text=Test Consignee Co.')
    
    // Check that filter appears
    await expect(page.locator('[data-testid="consignee-filter-tag"]')).toContainText('Test Consignee Co.')
    
    // Clear filter
    await page.click('[data-testid="consignee-filter-tag"] button')
    
    // Check that filter is removed
    await expect(page.locator('[data-testid="consignee-filter-tag"]')).not.toBeVisible()
  })

  test('should filter relationships by active status', async ({ page }) => {
    // Open active status dropdown
    await page.click('[data-testid="active-status-filter"]')
    
    // Select Active only
    await page.click('text=Active Only')
    
    // Check that filter appears
    await expect(page.locator('[data-testid="active-status-filter-tag"]')).toContainText('Active')
    
    // Clear filter
    await page.click('[data-testid="active-status-filter-tag"] button')
    
    // Check that filter is removed
    await expect(page.locator('[data-testid="active-status-filter-tag"]')).not.toBeVisible()
  })

  test('should sort relationships by different columns', async ({ page }) => {
    // Sort by consignee name
    await page.click('th button:has-text("Consignee")')
    
    // Check that sort indicator appears
    await expect(page.locator('th button:has-text("Consignee") svg')).toBeVisible()
    
    // Sort by notify party
    await page.click('th button:has-text("Notify Party")')
    
    // Check that sort indicator appears
    await expect(page.locator('th button:has-text("Notify Party") svg')).toBeVisible()
    
    // Sort by priority order
    await page.click('th button:has-text("Priority")')
    
    // Check that sort indicator appears
    await expect(page.locator('th button:has-text("Priority") svg')).toBeVisible()
  })

  test('should create a new consignee-notify party relationship', async ({ page }) => {
    // Click Add Relationship button
    await page.click('button:has-text("Add Relationship")')
    
    // Check that dialog opens
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    await expect(page.locator('h2', { hasText: 'Create Relationship' })).toBeVisible()
    
    // Select consignee
    await page.click('[data-testid="consignee-select"]')
    await page.click('text=Test Consignee Co.')
    
    // Select notify party
    await page.click('[data-testid="notify-party-select"]')
    await page.click('text=Test Notify Party Ltd.')
    
    // Configure notification preferences
    await page.check('input[name="notification_preferences.email"]')
    await page.uncheck('input[name="notification_preferences.sms"]')
    await page.check('input[name="notification_preferences.line"]')
    await page.uncheck('input[name="notification_preferences.wechat"]')
    
    // Set priority order
    await page.fill('input[name="priority_order"]', '1')
    
    // Add special instructions
    await page.fill('textarea[name="special_instructions"]', 'Contact by email first, then LINE if urgent')
    
    // Add notes
    await page.fill('textarea[name="notes"]', 'Primary contact for shipment updates')
    
    // Submit form
    await page.click('button:has-text("Create Relationship")')
    
    // Check that dialog closes and success message appears
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    
    // Check that new relationship appears in table
    await expect(page.locator('td:has-text("Test Consignee Co.")')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('td:has-text("Test Notify Party Ltd.")')).toBeVisible({ timeout: 5000 })
  })

  test('should validate relationship form fields', async ({ page }) => {
    // Click Add Relationship button
    await page.click('button:has-text("Add Relationship")')
    
    // Try to submit empty form
    await page.click('button:has-text("Create Relationship")')
    
    // Check for validation errors
    await expect(page.locator('text=Consignee is required')).toBeVisible()
    await expect(page.locator('text=Notify party is required')).toBeVisible()
    
    // Select consignee and notify party
    await page.click('[data-testid="consignee-select"]')
    await page.click('text=Test Consignee Co.')
    
    await page.click('[data-testid="notify-party-select"]')
    await page.click('text=Test Notify Party Ltd.')
    
    // Uncheck all notification preferences to trigger validation
    await page.uncheck('input[name="notification_preferences.email"]')
    await page.uncheck('input[name="notification_preferences.sms"]')
    await page.uncheck('input[name="notification_preferences.line"]')
    await page.uncheck('input[name="notification_preferences.wechat"]')
    
    // Try to submit
    await page.click('button:has-text("Create Relationship")')
    
    // Check for notification preferences validation error
    await expect(page.locator('text=at least one notification channel')).toBeVisible()
    
    // Test invalid priority order
    await page.fill('input[name="priority_order"]', '0') // Should be at least 1
    await page.blur('input[name="priority_order"]')
    
    // Check for validation error
    await expect(page.locator('text=Priority order must be at least 1')).toBeVisible()
    
    // Test priority order too high
    await page.fill('input[name="priority_order"]', '1000') // Should be max 999
    await page.blur('input[name="priority_order"]')
    
    // Check for validation error
    await expect(page.locator('text=Priority order cannot exceed 999')).toBeVisible()
    
    // Cancel form
    await page.click('button:has-text("Cancel")')
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
  })

  test('should view relationship details', async ({ page }) => {
    // Find first relationship row and click view button
    await page.click('tbody tr:first-child button[aria-label="View relationship"]')
    
    // Check that view dialog opens
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    await expect(page.locator('h2', { hasText: 'Relationship Details' })).toBeVisible()
    
    // Check that relationship information is displayed
    await expect(page.locator('label:has-text("Consignee")')).toBeVisible()
    await expect(page.locator('label:has-text("Notify Party")')).toBeVisible()
    await expect(page.locator('label:has-text("Priority Order")')).toBeVisible()
    await expect(page.locator('label:has-text("Default Notify Party")')).toBeVisible()
    await expect(page.locator('label:has-text("Active Status")')).toBeVisible()
    await expect(page.locator('label:has-text("Notification Preferences")')).toBeVisible()
    await expect(page.locator('label:has-text("Special Instructions")')).toBeVisible()
    await expect(page.locator('label:has-text("Notes")')).toBeVisible()
    
    // Close dialog
    await page.press('[role="dialog"]', 'Escape')
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
  })

  test('should edit an existing relationship', async ({ page }) => {
    // Find first relationship row and click edit button
    await page.click('tbody tr:first-child button[aria-label="Edit relationship"]')
    
    // Check that edit dialog opens
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    await expect(page.locator('h2', { hasText: 'Edit Relationship' })).toBeVisible()
    
    // Modify notification preferences
    await page.check('input[name="notification_preferences.sms"]')
    await page.check('input[name="notification_preferences.wechat"]')
    
    // Modify priority order
    await page.fill('input[name="priority_order"]', '5')
    
    // Modify special instructions
    await page.fill('textarea[name="special_instructions"]', 'Updated instructions - contact via SMS for urgent matters')
    
    // Submit form
    await page.click('button:has-text("Update Relationship")')
    
    // Check that dialog closes
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    
    // Check that success message appears
    await expect(page.locator('text=Relationship updated successfully')).toBeVisible({ timeout: 5000 })
  })

  test('should handle default notify party designation', async ({ page }) => {
    // Click Add Relationship button
    await page.click('button:has-text("Add Relationship")')
    
    // Select consignee and notify party
    await page.click('[data-testid="consignee-select"]')
    await page.click('text=Test Consignee Co.')
    
    await page.click('[data-testid="notify-party-select"]')
    await page.click('text=Default Notify Party Inc.')
    
    // Enable notification preferences
    await page.check('input[name="notification_preferences.email"]')
    
    // Set as default
    await page.check('input[name="is_default"]')
    
    // Submit form
    await page.click('button:has-text("Create Relationship")')
    
    // Check that dialog closes
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    
    // Verify default designation appears in table
    await expect(page.locator('tbody tr:has(td:has-text("Default Notify Party Inc.")) td:has(svg[data-testid="default-icon"])')).toBeVisible({ timeout: 5000 })
  })

  test('should test notification preferences configuration', async ({ page }) => {
    // Click Add Relationship button
    await page.click('button:has-text("Add Relationship")')
    
    // Select consignee and notify party
    await page.click('[data-testid="consignee-select"]')
    await page.click('text=Test Consignee Co.')
    
    await page.click('[data-testid="notify-party-select"]')
    await page.click('text=Test Notify Party Ltd.')
    
    // Test different notification preference combinations
    
    // Email only
    await page.check('input[name="notification_preferences.email"]')
    await page.uncheck('input[name="notification_preferences.sms"]')
    await page.uncheck('input[name="notification_preferences.line"]')
    await page.uncheck('input[name="notification_preferences.wechat"]')
    
    // Verify at least one is enabled (form should be valid)
    await expect(page.locator('text=at least one notification channel')).not.toBeVisible()
    
    // All channels enabled
    await page.check('input[name="notification_preferences.email"]')
    await page.check('input[name="notification_preferences.sms"]')
    await page.check('input[name="notification_preferences.line"]')
    await page.check('input[name="notification_preferences.wechat"]')
    
    // Verify all are checked
    await expect(page.locator('input[name="notification_preferences.email"]')).toBeChecked()
    await expect(page.locator('input[name="notification_preferences.sms"]')).toBeChecked()
    await expect(page.locator('input[name="notification_preferences.line"]')).toBeChecked()
    await expect(page.locator('input[name="notification_preferences.wechat"]')).toBeChecked()
    
    // Cancel form
    await page.click('button:has-text("Cancel")')
  })

  test('should delete a relationship', async ({ page }) => {
    // Find first relationship row and click delete button
    await page.click('tbody tr:first-child button[aria-label="Delete relationship"]')
    
    // Check that delete confirmation dialog opens
    await expect(page.locator('[role="alertdialog"]')).toBeVisible()
    await expect(page.locator('h2', { hasText: 'Delete Relationship' })).toBeVisible()
    
    // Check warning message about deletion
    await expect(page.locator('text=This action cannot be undone')).toBeVisible()
    
    // Confirm deletion
    await page.click('button:has-text("Delete Relationship")')
    
    // Check that dialog closes
    await expect(page.locator('[role="alertdialog"]')).not.toBeVisible()
    
    // Check that success message appears
    await expect(page.locator('text=Relationship deleted successfully')).toBeVisible({ timeout: 5000 })
  })

  test('should handle bulk operations', async ({ page }) => {
    // Select multiple relationships
    await page.check('thead input[type="checkbox"]') // Select all
    
    // Check that bulk actions appear
    await expect(page.locator('text=selected')).toBeVisible()
    await expect(page.locator('button:has-text("Delete Selected")')).toBeVisible()
    
    // Clear selection
    await page.click('button:has-text("Clear Selection")')
    
    // Check that bulk actions disappear
    await expect(page.locator('text=selected')).not.toBeVisible()
    
    // Select individual relationships
    await page.check('tbody tr:first-child input[type="checkbox"]')
    await page.check('tbody tr:nth-child(2) input[type="checkbox"]')
    
    // Check that bulk actions appear with correct count
    await expect(page.locator('text=2 relationships selected')).toBeVisible()
    
    // Click bulk delete
    await page.click('button:has-text("Delete Selected")')
    
    // Check that confirmation dialog opens
    await expect(page.locator('[role="alertdialog"]')).toBeVisible()
    await expect(page.locator('text=delete 2 selected relationships')).toBeVisible()
    
    // Cancel bulk delete
    await page.click('button:has-text("Cancel")')
    await expect(page.locator('[role="alertdialog"]')).not.toBeVisible()
  })

  test('should handle pagination', async ({ page }) => {
    // Check if pagination is present (only if there are multiple pages)
    const paginationExists = await page.locator('[data-testid="pagination"]').isVisible()
    
    if (paginationExists) {
      // Test next page
      await page.click('button[aria-label="Next page"]')
      
      // Check that page number changes
      await expect(page.locator('text=Page 2')).toBeVisible()
      
      // Test previous page
      await page.click('button[aria-label="Previous page"]')
      
      // Check that page number changes back
      await expect(page.locator('text=Page 1')).toBeVisible()
      
      // Test first page button
      await page.click('button[aria-label="First page"]')
      await expect(page.locator('text=Page 1')).toBeVisible()
      
      // Test last page button
      await page.click('button[aria-label="Last page"]')
      // Page number will depend on total pages
    }
  })

  test('should refresh relationships data', async ({ page }) => {
    // Click refresh button
    await page.click('button:has-text("Refresh")')
    
    // Check that loading indicator appears briefly
    await expect(page.locator('[data-testid="loading-indicator"]')).toBeVisible({ timeout: 1000 })
    
    // Check that loading indicator disappears
    await expect(page.locator('[data-testid="loading-indicator"]')).not.toBeVisible({ timeout: 5000 })
  })

  test('should test priority ordering functionality', async ({ page }) => {
    // This test would involve drag-and-drop or priority editing
    // Click on a relationship to edit priority
    await page.click('tbody tr:first-child button[aria-label="Edit relationship"]')
    
    // Change priority order
    await page.fill('input[name="priority_order"]', '10')
    
    // Submit form
    await page.click('button:has-text("Update Relationship")')
    
    // Check that dialog closes
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    
    // Verify priority order appears in table
    await expect(page.locator('tbody tr:first-child td:has-text("10")')).toBeVisible({ timeout: 5000 })
  })

  test('should test relationship uniqueness validation', async ({ page }) => {
    // Try to create a duplicate relationship
    await page.click('button:has-text("Add Relationship")')
    
    // Select same consignee and notify party as an existing relationship
    await page.click('[data-testid="consignee-select"]')
    await page.click('text=Test Consignee Co.')
    
    await page.click('[data-testid="notify-party-select"]')
    await page.click('text=Test Notify Party Ltd.') // Assuming this combination already exists
    
    // Enable notification preferences
    await page.check('input[name="notification_preferences.email"]')
    
    // Submit form
    await page.click('button:has-text("Create Relationship")')
    
    // Check for duplicate relationship error
    await expect(page.locator('text=relationship already exists')).toBeVisible()
    
    // Cancel form
    await page.click('button:has-text("Cancel")')
  })

  test('should show proper error handling', async ({ page }) => {
    // Test form validation errors
    await page.click('button:has-text("Add Relationship")')
    
    // Test special instructions length limit
    const longInstructions = 'A'.repeat(1001) // Exceed 1000 character limit
    await page.fill('textarea[name="special_instructions"]', longInstructions)
    await page.blur('textarea[name="special_instructions"]')
    
    // Check that error message appears
    await expect(page.locator('text=1000 characters')).toBeVisible()
    
    // Test notes length limit
    const longNotes = 'N'.repeat(2001) // Exceed 2000 character limit
    await page.fill('textarea[name="notes"]', longNotes)
    await page.blur('textarea[name="notes"]')
    
    // Check that error message appears
    await expect(page.locator('text=2000 characters')).toBeVisible()
    
    // Cancel form
    await page.click('button:has-text("Cancel")')
  })

  test('should maintain filter state across operations', async ({ page }) => {
    // Apply filters
    await page.fill('input[placeholder*="Search"]', 'Test')
    await page.waitForTimeout(500)
    
    // Open consignee filter
    await page.click('[data-testid="consignee-filter"]')
    await page.click('text=Test Consignee Co.')
    
    // Check that filters are applied
    await expect(page.locator('[data-testid="search-filter"]')).toContainText('Test')
    await expect(page.locator('[data-testid="consignee-filter-tag"]')).toContainText('Test Consignee Co.')
    
    // Perform an operation (create new relationship)
    await page.click('button:has-text("Add Relationship")')
    await page.click('button:has-text("Cancel")')
    
    // Check that filters are still applied
    await expect(page.locator('[data-testid="search-filter"]')).toContainText('Test')
    await expect(page.locator('[data-testid="consignee-filter-tag"]')).toContainText('Test Consignee Co.')
  })

  test('should test shipment integration preview', async ({ page }) => {
    // Navigate to a hypothetical shipment creation page or component
    // This would test the integration component we created
    // For now, we'll test the relationship data availability
    
    // Get relationship details to verify they would be available for shipment integration
    await page.click('tbody tr:first-child button[aria-label="View relationship"]')
    
    // Verify all necessary fields are present for shipment integration
    await expect(page.locator('label:has-text("Notification Preferences")')).toBeVisible()
    await expect(page.locator('label:has-text("Special Instructions")')).toBeVisible()
    await expect(page.locator('label:has-text("Priority Order")')).toBeVisible()
    await expect(page.locator('label:has-text("Default Notify Party")')).toBeVisible()
    
    // These fields should be populated in shipment forms via the integration
    await page.press('[role="dialog"]', 'Escape')
  })
})