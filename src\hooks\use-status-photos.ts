'use client'

import { useEffect, useRef, useCallback, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { RealtimeChannel } from '@supabase/supabase-js'

// Status photo with related data
export interface StatusPhoto {
  id: string
  shipment_id: string
  status_history_id: string
  image_url: string
  image_path: string
  file_size: number | null
  mime_type: string | null
  metadata: any | null
  uploaded_by: string | null
  created_at: string
  signed_url?: string
  status_history?: {
    id: string
    status: string
    created_at: string
  }
  uploader_profile?: {
    user_id: string
    full_name: string | null
    email: string
  }
}

// Photos grouped by status
export interface StatusPhotoGroup {
  status: string
  status_date: string
  status_history_id: string
  photos: StatusPhoto[]
  total_count: number
}

interface UseStatusPhotosReturn {
  photoGroups: StatusPhotoGroup[]
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  getSignedUrl: (imagePath: string) => Promise<string | null>
}

export function useStatusPhotos(shipmentId: string): UseStatusPhotosReturn {
  const [photoGroups, setPhotoGroups] = useState<StatusPhotoGroup[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createClient()
  const channelRef = useRef<RealtimeChannel | null>(null)
  const urlCacheRef = useRef<Map<string, { url: string; expiry: number }>>(new Map())

  // Generate signed URL with caching to avoid race conditions
  const getSignedUrl = useCallback(async (imagePath: string): Promise<string | null> => {
    try {
      // Check cache first (valid for 45 minutes to avoid expiry issues)
      const cached = urlCacheRef.current.get(imagePath)
      const now = Date.now()
      
      if (cached && cached.expiry > now) {
        return cached.url
      }

      // Generate new signed URL
      const { data, error } = await supabase.storage
        .from('status-images')
        .createSignedUrl(imagePath, 3600) // 1 hour expiry

      if (error) {
        console.error('Error generating signed URL:', error)
        return null
      }

      // Cache the URL (expires in 45 minutes)
      urlCacheRef.current.set(imagePath, {
        url: data.signedUrl,
        expiry: now + (45 * 60 * 1000)
      })

      return data.signedUrl
    } catch (err) {
      console.error('Error in getSignedUrl:', err)
      return null
    }
  }, [supabase])

  // Fetch status photos with relations
  const fetchStatusPhotos = useCallback(async () => {
    if (!shipmentId) return

    try {
      setIsLoading(true)
      setError(null)

      const { data, error } = await supabase
        .from('status_images')
        .select(`
          *,
          status_history!inner(
            id,
            status_to,
            created_at
          ),
          uploader_profile:profiles(
            user_id,
            full_name,
            email
          )
        `)
        .eq('shipment_id', shipmentId)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Generate signed URLs for all photos
      const photosWithUrls = await Promise.all(
        (data || []).map(async (photo: any) => {
          const signedUrl = await getSignedUrl(photo.image_path)
          return {
            ...photo,
            signed_url: signedUrl,
          }
        })
      )

      // Group photos by status_history_id
      const groupsMap = new Map<string, StatusPhotoGroup>()

      photosWithUrls.forEach((photo) => {
        const statusHistoryId = photo.status_history_id
        const status = photo.status_history?.status_to || 'unknown'
        const statusDate = photo.status_history?.created_at || photo.created_at

        if (!groupsMap.has(statusHistoryId)) {
          groupsMap.set(statusHistoryId, {
            status,
            status_date: statusDate,
            status_history_id: statusHistoryId,
            photos: [],
            total_count: 0,
          })
        }

        const group = groupsMap.get(statusHistoryId)!
        group.photos.push(photo)
        group.total_count = group.photos.length
      })

      // Convert to array and sort by status date (newest first)
      const groupsArray = Array.from(groupsMap.values()).sort((a, b) => 
        new Date(b.status_date).getTime() - new Date(a.status_date).getTime()
      )

      setPhotoGroups(groupsArray)
    } catch (err) {
      console.error('Error fetching status photos:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch status photos')
    } finally {
      setIsLoading(false)
    }
  }, [supabase, shipmentId, getSignedUrl])

  // Handle real-time changes
  const handleStatusImageChange = useCallback(
    async (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload

      // Only process events for the specific shipment
      if (newRecord?.shipment_id !== shipmentId && oldRecord?.shipment_id !== shipmentId) {
        return
      }

      try {
        switch (eventType) {
          case 'INSERT':
            console.log('New status image uploaded:', newRecord)
            // Refetch to get complete data with relations
            await fetchStatusPhotos()
            break

          case 'UPDATE':
            console.log('Status image updated:', newRecord)
            // Refetch to ensure data consistency
            await fetchStatusPhotos()
            break

          case 'DELETE':
            console.log('Status image deleted:', oldRecord)
            // Remove from cache
            if (oldRecord?.image_path) {
              urlCacheRef.current.delete(oldRecord.image_path)
            }
            // Refetch to update groups
            await fetchStatusPhotos()
            break
        }
      } catch (err) {
        console.error('Error handling status image change:', err)
        setError(err instanceof Error ? err.message : 'Error processing real-time update')
      }
    },
    [shipmentId, fetchStatusPhotos]
  )

  // Setup real-time subscription
  useEffect(() => {
    if (!shipmentId) return

    // Initial fetch
    fetchStatusPhotos()

    // Setup real-time subscription
    const channel = supabase
      .channel(`status-images-${shipmentId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'status_images',
          filter: `shipment_id=eq.${shipmentId}`,
        },
        handleStatusImageChange
      )
      .subscribe((status) => {
        console.log('Status images subscription status:', status)
        
        if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          setError('Real-time connection error')
        }
      })

    channelRef.current = channel

    return () => {
      if (channelRef.current) {
        channelRef.current.unsubscribe()
      }
    }
  }, [shipmentId, fetchStatusPhotos, handleStatusImageChange, supabase])

  // Cleanup cache when component unmounts
  useEffect(() => {
    return () => {
      urlCacheRef.current.clear()
    }
  }, [])

  return {
    photoGroups,
    isLoading,
    error,
    refetch: fetchStatusPhotos,
    getSignedUrl,
  }
}