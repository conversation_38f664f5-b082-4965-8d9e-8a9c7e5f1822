import type { Metadata, Viewport } from 'next'
import { LanguageProvider } from '@/hooks/use-language'

export const metadata: Metadata = {
  title: 'Driver App | DYY Trading Management',
  description: 'Mobile driver interface for DYY Trading Management',
  manifest: '/manifest.json',
  appleWebApp: {
    statusBarStyle: 'black-translucent',
    title: 'DYY Driver',
  },
  formatDetection: {
    telephone: false,
  },
  other: {
    'mobile-web-app-capable': 'yes',
  },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
  themeColor: '#0f172a',
}

interface MobileDriverLayoutProps {
  children: React.ReactNode
}

export default function MobileDriverLayout({ children }: MobileDriverLayoutProps) {
  return (
    <>
      <LanguageProvider>
        <div className="min-h-screen bg-slate-900 text-white font-sans">
          {/* Main mobile container with safe area support */}
          <div className="min-h-screen flex flex-col">
            {children}
          </div>
        </div>
      </LanguageProvider>
      
      {/* Service Worker Registration */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('SW registered: ', registration);
                  })
                  .catch(function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                  });
              });
            }
          `,
        }}
      />
    </>
  )
}