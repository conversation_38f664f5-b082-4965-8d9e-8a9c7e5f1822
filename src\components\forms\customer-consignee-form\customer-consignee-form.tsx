'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { EmptyState } from '@/components/ui/empty-state'
import {
  Loader2,
  Info,
  Building,
  Users,
  Package,
  Star,
  AlertCircle,
  Link,
  CheckCircle,
  AlertTriangle,
} from 'lucide-react'
import {
  customerConsigneeFormSchema,
  type CustomerConsigneeForm,
} from '@/lib/validations/customer-consignees'
import { useCompanySelection } from '@/hooks/use-customer-consignees'
import type { Company } from '@/stores/company-store'
import type { CustomerConsignee } from '@/stores/customer-consignee-store'

interface CustomerConsigneeFormProps {
  relationship?: CustomerConsignee | null
  onSubmit: (data: CustomerConsigneeForm) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  className?: string
}

export function CustomerConsigneeForm({
  relationship,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: CustomerConsigneeFormProps) {
  const [error, setError] = useState<string | null>(null)
  const [customers, setCustomers] = useState<Company[]>([])
  const [consignees, setConsignees] = useState<Company[]>([])
  const [loadingOptions, setLoadingOptions] = useState(true)

  const { getCustomers, getConsignees } = useCompanySelection()

  // Load company options
  useEffect(() => {
    const loadOptions = async () => {
      try {
        setLoadingOptions(true)
        setError(null)
        console.log('Loading company options...')

        const [customerOptions, consigneeOptions] = await Promise.all([
          getCustomers(),
          getConsignees(),
        ])

        console.log('Loaded customers:', customerOptions)
        console.log('Loaded consignees:', consigneeOptions)

        setCustomers(customerOptions)
        setConsignees(consigneeOptions)
      } catch (error) {
        console.error('Error loading company options:', error)
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to load company options'
        setError(`Failed to load company options: ${errorMessage}`)
      } finally {
        setLoadingOptions(false)
      }
    }

    loadOptions()
  }, [getCustomers, getConsignees])

  const form = useForm<CustomerConsigneeForm>({
    resolver: zodResolver(customerConsigneeFormSchema),
    defaultValues: {
      customer_id: relationship?.customer_id || '',
      consignee_id: relationship?.consignee_id || '',
      is_default: relationship?.is_default || false,
      is_active: relationship?.is_active ?? true,
      notes: relationship?.notes || '',
    },
  })

  const selectedCustomerId = form.watch('customer_id')
  const selectedConsigneeId = form.watch('consignee_id')
  const isDefault = form.watch('is_default')
  const isActive = form.watch('is_active')

  // Get selected company details for display
  const selectedCustomer = customers.find(c => c.id === selectedCustomerId)
  const selectedConsignee = consignees.find(c => c.id === selectedConsigneeId)

  const onFormSubmit = async (data: CustomerConsigneeForm) => {
    try {
      setError(null)
      console.log('Form submitted with data:', data)

      await onSubmit(data)
    } catch (submitError) {
      console.error('Form submission error:', submitError)
      setError(
        submitError instanceof Error
          ? submitError.message
          : 'Failed to save relationship'
      )
    }
  }

  // Handle form errors
  useEffect(() => {
    if (Object.keys(form.formState.errors).length > 0) {
      console.log('Form validation errors:', form.formState.errors)

      const errorMessages = []
      if (form.formState.errors.customer_id)
        errorMessages.push('Customer selection is required')
      if (form.formState.errors.consignee_id)
        errorMessages.push('Consignee selection is required')
      if (form.formState.errors.root)
        errorMessages.push('Customer and consignee cannot be the same company')

      if (errorMessages.length > 0) {
        setError(`Please fix the following issues: ${errorMessages.join(', ')}`)
      }
    } else {
      // Clear validation errors
      // Only clear errors that were set by this form's validation logic,
      // not submission errors from the parent component.
      if (error && error.startsWith('Please fix the following issues:')) {
        setError(null)
      }
    }
  }, [form.formState.errors, error])

  if (loadingOptions) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
          <span className="ml-2 text-slate-300">
            Loading company options...
          </span>
        </div>
      </div>
    )
  }

  // Show error state if loading failed
  if (error && error.includes('Failed to load')) {
    return (
      <div className={className}>
        <EmptyState
          icon={AlertTriangle}
          title="Failed to Load Company Options"
          description={error}
          action={{
            label: 'Retry',
            onClick: () => window.location.reload(),
            variant: 'outline',
          }}
        >
          <div className="mt-4 p-4 bg-slate-800 border border-slate-600 rounded text-sm text-slate-300">
            <p className="font-medium mb-2">Possible solutions:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Check your network connection</li>
              <li>Ensure you have permission to access company data</li>
              <li>
                Verify that customer and consignee companies exist in the database
              </li>
              <li>Contact your system administrator if the problem persists</li>
            </ul>
          </div>
        </EmptyState>
      </div>
    )
  }

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Company Selection */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Link className="h-5 w-5 text-orange-500" />
                Company Selection
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Customer Selection */}
                <FormField
                  control={form.control}
                  name="customer_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-2">
                        <Users className="h-4 w-4 text-blue-500" />
                        Customer Company *
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select customer company" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                          {customers.length === 0 ? (
                            <div className="p-4 text-center text-slate-400">
                              No customer companies available
                            </div>
                          ) : (
                            customers.map(customer => (
                              <SelectItem
                                key={customer.id}
                                value={customer.id}
                                className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                              >
                                <div className="flex items-center space-x-2 w-full">
                                  <Users className="h-4 w-4 text-blue-500 flex-shrink-0" />
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium truncate">
                                      {customer.name}
                                    </div>
                                    {customer.contact_phone && (
                                      <div className="text-xs text-slate-400 truncate">
                                        {customer.contact_phone}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormDescription className="text-slate-400">
                        Select the customer company for this relationship
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Consignee Selection */}
                <FormField
                  control={form.control}
                  name="consignee_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-2">
                        <Package className="h-4 w-4 text-green-500" />
                        Consignee Company *
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select consignee company" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                          {consignees.length === 0 ? (
                            <div className="p-4 text-center text-slate-400">
                              No consignee companies available
                            </div>
                          ) : (
                            consignees
                              .filter(
                                consignee => consignee.id !== selectedCustomerId
                              ) // Prevent same company selection
                              .map(consignee => (
                                <SelectItem
                                  key={consignee.id}
                                  value={consignee.id}
                                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                                >
                                  <div className="flex items-center space-x-2 w-full">
                                    <Package className="h-4 w-4 text-green-500 flex-shrink-0" />
                                    <div className="flex-1 min-w-0">
                                      <div className="font-medium truncate">
                                        {consignee.name}
                                      </div>
                                      {consignee.contact_phone && (
                                        <div className="text-xs text-slate-400 truncate">
                                          {consignee.contact_phone}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </SelectItem>
                              ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormDescription className="text-slate-400">
                        Select the consignee company for this relationship
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Selection Preview */}
              {(selectedCustomer || selectedConsignee) && (
                <div className="bg-slate-700 rounded-lg p-4 mt-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium text-slate-200">
                      Selected Companies
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedCustomer && (
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <Users className="h-3 w-3 text-blue-500" />
                          <span className="text-xs font-medium text-blue-300">
                            Customer
                          </span>
                        </div>
                        <div className="text-sm text-slate-200 font-medium">
                          {selectedCustomer.name}
                        </div>
                        {selectedCustomer.contact_email && (
                          <div className="text-xs text-slate-400">
                            {selectedCustomer.contact_email}
                          </div>
                        )}
                      </div>
                    )}
                    {selectedConsignee && (
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <Package className="h-3 w-3 text-green-500" />
                          <span className="text-xs font-medium text-green-300">
                            Consignee
                          </span>
                        </div>
                        <div className="text-sm text-slate-200 font-medium">
                          {selectedConsignee.name}
                        </div>
                        {selectedConsignee.contact_email && (
                          <div className="text-xs text-slate-400">
                            {selectedConsignee.contact_email}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Relationship Configuration */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Building className="h-5 w-5 text-orange-500" />
                Relationship Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Default Consignee Toggle */}
                <FormField
                  control={form.control}
                  name="is_default"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4 bg-slate-700/30">
                      <div className="space-y-0.5">
                        <FormLabel className="text-white font-medium flex items-center gap-2">
                          <Star
                            className={`h-4 w-4 ${field.value ? 'text-yellow-500' : 'text-slate-400'}`}
                          />
                          Default Consignee
                        </FormLabel>
                        <div className="text-sm">
                          <span
                            className={
                              field.value ? 'text-yellow-400' : 'text-slate-400'
                            }
                          >
                            {field.value
                              ? 'This is the default consignee for the customer'
                              : 'Not set as default consignee'}
                          </span>
                        </div>
                        <FormDescription className="text-slate-400">
                          Only one default consignee per customer is allowed
                        </FormDescription>
                      </div>
                      <FormControl>
                        <div className="flex items-center space-x-2">
                          <span
                            className={`text-xs font-medium ${field.value ? 'text-yellow-400' : 'text-slate-400'}`}
                          >
                            {field.value ? 'DEFAULT' : 'STANDARD'}
                          </span>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-yellow-500 data-[state=unchecked]:bg-slate-600"
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Active Status Toggle */}
                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4 bg-slate-700/30">
                      <div className="space-y-0.5">
                        <FormLabel className="text-white font-medium">
                          Relationship Status
                        </FormLabel>
                        <div className="text-sm font-medium">
                          <span
                            className={
                              field.value ? 'text-green-400' : 'text-red-400'
                            }
                          >
                            {field.value
                              ? 'Active - Available for shipment creation'
                              : 'Inactive - Not available for shipment creation'}
                          </span>
                        </div>
                        <FormDescription className="text-slate-400">
                          Controls visibility in shipment workflows
                        </FormDescription>
                      </div>
                      <FormControl>
                        <div className="flex items-center space-x-2">
                          <span
                            className={`text-xs font-medium ${field.value ? 'text-green-400' : 'text-red-400'}`}
                          >
                            {field.value ? 'ON' : 'OFF'}
                          </span>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500"
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* Warning for default setting */}
              {isDefault && selectedCustomerId && (
                <Alert>
                  <Star className="h-4 w-4" />
                  <AlertDescription className="text-slate-600">
                    <strong>Note:</strong> Setting this as the default consignee
                    will automatically remove the default status from any other
                    consignee relationships for{' '}
                    {selectedCustomer?.name || 'this customer'}.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Notes */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Info className="h-5 w-5 text-orange-500" />
                Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional notes or special requirements for this relationship..."
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        rows={4}
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Optional notes about this customer-consignee relationship
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Summary Card */}
          {selectedCustomer && selectedConsignee && (
            <Card className="bg-gradient-to-r from-slate-800 to-slate-700 border-slate-600">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-white font-semibold mb-2">
                      Relationship Summary
                    </h3>
                    <div className="space-y-1 text-sm">
                      <div className="text-slate-300">
                        <span className="text-blue-300 font-medium">
                          {selectedCustomer.name}
                        </span>
                        <span className="text-slate-400 mx-2">→</span>
                        <span className="text-green-300 font-medium">
                          {selectedConsignee.name}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4">
                        <Badge
                          variant={isDefault ? 'default' : 'secondary'}
                          className={
                            isDefault
                              ? 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600'
                              : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                          }
                        >
                          {isDefault
                            ? 'Default Consignee'
                            : 'Standard Relationship'}
                        </Badge>
                        <Badge
                          variant={isActive ? 'default' : 'secondary'}
                          className={
                            isActive
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-red-600 hover:bg-red-700 text-white border-red-600'
                          }
                        >
                          {isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <Link className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {relationship ? 'Update Relationship' : 'Create Relationship'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}