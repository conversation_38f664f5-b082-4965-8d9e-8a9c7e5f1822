# Interface Specifications

## Admin Dashboard Layout

### Header Section
```tsx
<header className="bg-primary-900 border-b border-primary-700 px-6 py-4">
  <div className="flex items-center justify-between max-w-2xl mx-auto">
    <div className="flex items-center space-x-4">
      <img src="/logo.svg" alt="DYY Trading" className="h-8 w-auto" />
      <h1 className="text-xl font-semibold text-neutral-50">
        Admin Dashboard
      </h1>
    </div>
    <div className="flex items-center space-x-4">
      <NotificationButton />
      <UserMenu />
    </div>
  </div>
</header>
```

### Main Content Grid
```tsx
<main className="max-w-2xl mx-auto px-6 py-8">
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    
    {/* Quick Stats Cards */}
    <StatCard 
      title="Active Shipments" 
      value="127" 
      trend="+12%" 
      icon={<ShipIcon />}
      color="info"
    />
    <StatCard 
      title="Pending Documents" 
      value="8" 
      trend="-2" 
      icon={<DocumentIcon />}
      color="warning"
    />
    <StatCard 
      title="Drivers Online" 
      value="23" 
      trend="+5" 
      icon={<TruckIcon />}
      color="success"
    />
    
    {/* Master Data Management */}
    <Card className="md:col-span-2 lg:col-span-3">
      <CardHeader>
        <CardTitle>Master Data Management</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <MasterDataButton 
            icon={<BuildingIcon />}
            label="Companies"
            count="145"
            href="/master-data/companies"
          />
          <MasterDataButton 
            icon={<PackageIcon />}
            label="Products"
            count="89"
            href="/master-data/products"
          />
          <MasterDataButton 
            icon={<AnchorIcon />}
            label="Ports"
            count="56"
            href="/master-data/ports"
          />
          <MasterDataButton 
            icon={<UserIcon />}
            label="Drivers"
            count="34"
            href="/master-data/drivers"
          />
        </div>
      </CardContent>
    </Card>
    
    {/* Recent Activity */}
    <Card className="md:col-span-2 lg:col-span-2">
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <ActivityFeed items={recentActivity} />
      </CardContent>
    </Card>
    
    {/* System Status */}
    <Card>
      <CardHeader>
        <CardTitle>System Status</CardTitle>
      </CardHeader>
      <CardContent>
        <SystemStatusIndicators />
      </CardContent>
    </Card>
    
  </div>
</main>
```

## CS Shipment Management Interface

### Transportation Mode Selection Modal (Pre-Creation)
```tsx
{/* Transportation Mode Selection Modal */}
<Dialog open={transportModeModalOpen} onOpenChange={setTransportModeModalOpen}>
  <DialogContent className="bg-primary-800 border-primary-700 max-w-md">
    <DialogHeader>
      <DialogTitle className="text-neutral-50">Select Transportation Mode</DialogTitle>
      <DialogDescription className="text-neutral-300">
        Choose the transportation mode for this shipment. This will configure the appropriate workflow and required fields.
      </DialogDescription>
    </DialogHeader>
    
    <div className="space-y-3 py-4">
      {transportModes.map(mode => (
        <Button
          key={mode.value}
          variant={selectedTransportMode === mode.value ? "default" : "secondary"}
          className={`w-full justify-start p-4 h-auto ${
            selectedTransportMode === mode.value 
              ? 'bg-accent-500 hover:bg-accent-400' 
              : 'bg-primary-700 hover:bg-primary-600'
          }`}
          onClick={() => setSelectedTransportMode(mode.value)}
        >
          <div className="text-left">
            <div className="flex items-center space-x-2">
              <mode.icon className="w-5 h-5" />
              <span className="font-medium">{mode.label}</span>
            </div>
            <div className="text-xs opacity-75 mt-1">{mode.description}</div>
            <div className="text-xs opacity-60 mt-1">
              Required fields: {mode.requiredFields.join(', ')}
            </div>
          </div>
        </Button>
      ))}
    </div>
    
    <DialogFooter>
      <Button
        variant="secondary"
        onClick={() => setTransportModeModalOpen(false)}
        className="bg-primary-700 hover:bg-primary-600"
      >
        Cancel
      </Button>
      <Button
        onClick={handleTransportModeConfirm}
        disabled={!selectedTransportMode}
        className="bg-accent-500 hover:bg-accent-400"
      >
        Continue to Create Shipment
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### Shipment Creation Form with Cascading Selection
```tsx
<form className="space-y-6 max-w-4xl mx-auto p-6">
  
  {/* Section 1: Customer & Factory Information */}
  <Card>
    <CardHeader>
      <CardTitle>Customer & Factory Information</CardTitle>
      <CardDescription>
        Selecting a customer will automatically load associated shippers and products
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      
      {/* Customer and Factory Selection */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="customer">Customer *</Label>
          <Select onValueChange={handleCustomerSelection}>
            <SelectTrigger>
              <SelectValue placeholder="Search and select customer..." />
            </SelectTrigger>
            <SelectContent>
              {customers.map(customer => (
                <SelectItem key={customer.id} value={customer.id}>
                  <div className="flex items-center space-x-2">
                    <span>{customer.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {customer.activeShipments} active
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Mandatory Factory Selection */}
        <div>
          <Label htmlFor="factory">Factory *</Label>
          <Select onValueChange={setSelectedFactory} required>
            <SelectTrigger>
              <SelectValue placeholder="Select factory..." />
            </SelectTrigger>
            <SelectContent>
              {factories.map(factory => (
                <SelectItem key={factory.id} value={factory.id}>
                  <div>
                    <div className="font-medium">{factory.name}</div>
                    <div className="text-xs text-neutral-400">
                      {factory.location} • {factory.capacity}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Auto-populated Shipper */}
        <div>
          <Label htmlFor="shipper">Shipper (auto-loaded) *</Label>
          <Select 
            value={defaultShipper} 
            onValueChange={setSelectedShipper}
            disabled={!selectedCustomer}
          >
            <SelectTrigger className={!selectedCustomer ? 'opacity-50' : ''}>
              <SelectValue placeholder="Select shipper..." />
            </SelectTrigger>
            <SelectContent>
              {customerShippers.map(shipper => (
                <SelectItem key={shipper.id} value={shipper.id}>
                  <div className="flex items-center justify-between w-full">
                    <span>{shipper.name}</span>
                    {shipper.isDefault && (
                      <Badge className="bg-accent-500 text-xs">Default</Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Auto-populated Products with Pricing */}
      <div>
        <Label>Products (auto-loaded with pricing)</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          {customerProducts.map(product => (
            <Card key={product.id} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{product.name}</h4>
                  <p className="text-sm text-neutral-400">
                    {product.packaging} • {product.weightPerPackage}kg
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-mono text-sm">
                    {product.priceType}: ${product.pricePerKg}/kg
                  </p>
                  <Checkbox 
                    checked={product.isDefault}
                    onCheckedChange={() => toggleProductSelection(product.id)}
                  />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
      
    </CardContent>
  </Card>
  
  {/* Section 2: Forwarder Agent & Documentation */}
  <Card>
    <CardHeader>
      <CardTitle>Forwarder Agent & Booking Documentation</CardTitle>
      <CardDescription>
        Select forwarder agent and upload booking confirmation document
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Mandatory Forwarder Agent Selection */}
        <div>
          <Label htmlFor="forwarderAgent">Forwarder Agent *</Label>
          <Select onValueChange={setSelectedForwarderAgent} required>
            <SelectTrigger>
              <SelectValue placeholder="Select forwarder agent..." />
            </SelectTrigger>
            <SelectContent>
              {forwarderAgents.map(agent => (
                <SelectItem key={agent.id} value={agent.id}>
                  <div>
                    <div className="font-medium">{agent.name}</div>
                    <div className="text-xs text-neutral-400">
                      {agent.location} • {agent.services.join(', ')}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* Display selected agent details */}
          {selectedForwarderAgent && (
            <div className="mt-2 p-3 bg-primary-900 rounded-lg border border-primary-600">
              <div className="text-sm">
                <p className="text-neutral-200 font-medium">{selectedAgent.name}</p>
                <p className="text-neutral-400 text-xs">Contact: {selectedAgent.contact}</p>
                <p className="text-neutral-400 text-xs">Email: {selectedAgent.email}</p>
                <p className="text-neutral-400 text-xs">Services: {selectedAgent.services.join(', ')}</p>
              </div>
            </div>
          )}
        </div>
        
        {/* Booking Confirm Document Upload */}
        <div>
          <Label htmlFor="bookingDocument">Booking Confirmation Document *</Label>
          <div className="mt-2">
            <div className="border-2 border-dashed border-primary-600 rounded-lg p-4 text-center">
              <input
                type="file"
                id="bookingDocument"
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                onChange={handleBookingDocumentUpload}
                className="hidden"
                required
              />
              <label 
                htmlFor="bookingDocument" 
                className="cursor-pointer block"
              >
                <UploadIcon className="w-8 h-8 mx-auto text-neutral-400 mb-2" />
                <p className="text-sm text-neutral-300 mb-1">
                  Click to upload booking confirmation
                </p>
                <p className="text-xs text-neutral-400">
                  Supports PDF, JPG, PNG, DOC (max 10MB)
                </p>
              </label>
            </div>
            
            {/* Display uploaded document */}
            {bookingDocument && (
              <div className="mt-3 p-3 bg-primary-900 rounded-lg border border-primary-600">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <DocumentIcon className="w-4 h-4 text-accent-500" />
                    <div>
                      <p className="text-sm text-neutral-200">{bookingDocument.name}</p>
                      <p className="text-xs text-neutral-400">
                        {(bookingDocument.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button 
                      size="sm" 
                      variant="secondary"
                      onClick={() => previewDocument(bookingDocument)}
                      className="bg-primary-700 hover:bg-primary-600"
                    >
                      <EyeIcon className="w-3 h-3" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => removeBookingDocument()}
                    >
                      <XIcon className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Required Date & Time Information */}
      <div>
        <h4 className="text-sm font-medium text-neutral-50 mb-3">
          Logistics Schedule Information *
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          
          {/* ETD - Estimated Time of Departure */}
          <div>
            <Label htmlFor="etdDate">ETD (Estimated Time of Departure) *</Label>
            <Input
              type="datetime-local"
              id="etdDate"
              value={etdDate}
              onChange={(e) => setEtdDate(e.target.value)}
              className="bg-primary-900 border-primary-600 text-neutral-50"
              required
            />
            <p className="text-xs text-neutral-400 mt-1">
              When shipment is expected to depart from origin
            </p>
          </div>
          
          {/* ETA - Estimated Time of Arrival */}
          <div>
            <Label htmlFor="etaDate">ETA (Estimated Time of Arrival) *</Label>
            <Input
              type="datetime-local"
              id="etaDate"
              value={etaDate}
              onChange={(e) => setEtaDate(e.target.value)}
              className="bg-primary-900 border-primary-600 text-neutral-50"
              required
              min={etdDate} // ETA cannot be before ETD
            />
            <p className="text-xs text-neutral-400 mt-1">
              When shipment is expected to arrive at destination
            </p>
          </div>
          
          {/* Closing Time */}
          <div>
            <Label htmlFor="closingTime">Closing Time *</Label>
            <Input
              type="datetime-local"
              id="closingTime"
              value={closingTime}
              onChange={(e) => setClosingTime(e.target.value)}
              className="bg-primary-900 border-primary-600 text-neutral-50"
              required
              max={etdDate} // Closing time must be before ETD
            />
            <p className="text-xs text-neutral-400 mt-1">
              Last time to submit cargo for this shipment
            </p>
          </div>
          
        </div>
        
        {/* Date validation feedback */}
        {(etdDate && etaDate && etdDate >= etaDate) && (
          <Alert className="bg-warning-500/10 border-warning-500 mt-3">
            <AlertTriangleIcon className="w-4 h-4" />
            <AlertTitle>Date Validation</AlertTitle>
            <AlertDescription>
              ETA date must be after ETD date. Please adjust the dates accordingly.
            </AlertDescription>
          </Alert>
        )}
        
        {(closingTime && etdDate && closingTime >= etdDate) && (
          <Alert className="bg-warning-500/10 border-warning-500 mt-3">
            <AlertTriangleIcon className="w-4 h-4" />
            <AlertTitle>Date Validation</AlertTitle>
            <AlertDescription>
              Closing time must be before ETD. Please adjust the times accordingly.
            </AlertDescription>
          </Alert>
        )}
      </div>
      
    </CardContent>
  </Card>
  
  {/* Section 3: Consignee & Notify Party Cascading */}
  <Card>
    <CardHeader>
      <CardTitle>Destination Information</CardTitle>
      <CardDescription>
        Consignee selection will auto-load associated notify parties
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Consignee Selection - Triggers notify party loading */}
        <div>
          <Label htmlFor="consignee">Consignee *</Label>
          <Select onValueChange={handleConsigneeSelection}>
            <SelectTrigger>
              <SelectValue placeholder="Select consignee..." />
            </SelectTrigger>
            <SelectContent>
              {consignees.map(consignee => (
                <SelectItem key={consignee.id} value={consignee.id}>
                  <div>
                    <div className="font-medium">{consignee.name}</div>
                    <div className="text-xs text-neutral-400">
                      {consignee.city}, {consignee.country}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Auto-populated Notify Parties */}
        <div>
          <Label>Notify Parties (auto-loaded)</Label>
          <div className="space-y-2 mt-2">
            {consigneeNotifyParties.map(notifyParty => (
              <div key={notifyParty.id} className="flex items-center space-x-2">
                <Checkbox 
                  checked={notifyParty.isDefault}
                  onCheckedChange={() => toggleNotifyParty(notifyParty.id)}
                />
                <div className="flex-1">
                  <span className="text-sm">{notifyParty.name}</span>
                  <div className="text-xs text-neutral-400">
                    {notifyParty.preferredChannels.join(', ')}
                  </div>
                </div>
                {notifyParty.isDefault && (
                  <Badge className="bg-accent-500 text-xs">Default</Badge>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
      
    </CardContent>
  </Card>
  
  {/* Section 3: Transportation & Logistics */}
  <Card>
    <CardHeader>
      <CardTitle>Transportation Details</CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      
      {/* Transportation Mode Selection */}
      <div>
        <Label>Transportation Mode *</Label>
        <RadioGroup value={transportMode} onValueChange={setTransportMode}>
          <div className="flex space-x-6 mt-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="sea" id="sea" />
              <Label htmlFor="sea">Sea Freight</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="land" id="land" />
              <Label htmlFor="land">Land Transport</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="rail" id="rail" />
              <Label htmlFor="rail">Rail Transport</Label>
            </div>
          </div>
        </RadioGroup>
      </div>
      
      {/* Dynamic fields based on transport mode */}
      {transportMode === 'sea' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="vesselName">Vessel Name</Label>
            <Input id="vesselName" placeholder="Enter vessel name" />
          </div>
          <div>
            <Label htmlFor="voyageNumber">Voyage Number</Label>
            <Input id="voyageNumber" placeholder="Enter voyage #" />
          </div>
          <div>
            <Label htmlFor="bookingNumber">Booking Number</Label>
            <Input id="bookingNumber" placeholder="Enter booking #" />
          </div>
        </div>
      )}
      
      {/* Port Selection with Suggestions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="originPort">Origin Port</Label>
          <Select onValueChange={setSelectedOriginPort}>
            <SelectTrigger>
              <SelectValue placeholder="Select origin port..." />
            </SelectTrigger>
            <SelectContent>
              {originPorts.map(port => (
                <SelectItem key={port.id} value={port.id}>
                  <div>
                    <div className="font-medium">{port.name}</div>
                    <div className="text-xs text-neutral-400">
                      {port.code} • {port.country}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-neutral-400 mt-1">
            Suggested based on customer history
          </p>
        </div>
        
        <div>
          <Label htmlFor="destinationPort">Destination Port *</Label>
          <Select onValueChange={setSelectedDestinationPort} required>
            <SelectTrigger>
              <SelectValue placeholder="Select destination port..." />
            </SelectTrigger>
            <SelectContent>
              {destinationPorts.map(port => (
                <SelectItem key={port.id} value={port.id}>
                  <div>
                    <div className="font-medium">{port.name}</div>
                    <div className="text-xs text-neutral-400">
                      {port.code} • {port.country}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-neutral-400 mt-1">
            Required for shipment routing and documentation
          </p>
        </div>
      </div>
      
      {/* Optional Notes Field */}
      <div>
        <Label htmlFor="notes">Notes (Optional)</Label>
        <Textarea
          id="notes"
          placeholder="Enter any additional notes, special instructions, or comments for this shipment..."
          value={shipmentNotes}
          onChange={(e) => setShipmentNotes(e.target.value)}
          className="bg-primary-900 border-primary-600 text-neutral-50 min-h-[100px]"
          rows={4}
        />
        <p className="text-xs text-neutral-400 mt-1">
          These notes will be visible to all stakeholders and included in shipment documentation
        </p>
      </div>
      
      {/* Auto-Generation Notice */}
      <div className="space-y-3">
        <Alert className="bg-info-500/10 border-info-500">
          <InfoIcon className="w-4 h-4" />
          <AlertTitle>Automatic Shipment Number Generation</AlertTitle>
          <AlertDescription>
            Shipment number will be automatically generated when saved using format: 
            <code className="bg-primary-800 px-1 rounded text-accent-500 font-mono text-xs">
              EX[Mode]-[Port]-YYMMDD-[Running]
            </code>
            <br />
            <span className="text-xs">
              Mode: 1=Land, 2=Rail, 3=Sea • Running number resets monthly per mode/port combination
            </span>
          </AlertDescription>
        </Alert>
        
        <Alert className="bg-info-500/10 border-info-500">
          <InfoIcon className="w-4 h-4" />
          <AlertTitle>Automatic Container Management</AlertTitle>
          <AlertDescription>
            Containers will be automatically generated when the shipment is saved. 
            Products will be allocated to containers based on quantity and packaging specifications.
            You can manage container details after shipment creation.
          </AlertDescription>
        </Alert>
      </div>
      
    </CardContent>
  </Card>
  
  {/* Form Actions */}
  <div className="flex justify-end space-x-4 pt-6 border-t border-primary-700">
    <Button variant="secondary" type="button">
      Save as Draft
    </Button>
    <Button type="submit" className="bg-accent-500 hover:bg-accent-400">
      Create Shipment
    </Button>
  </div>
  
</form>
```

## Driver Mobile Interface

### Mobile Dashboard (Assignments)
```tsx
<div className="min-h-screen bg-primary-900 pb-16">
  
  {/* Mobile Header */}
  <header className="bg-primary-800 px-4 py-3 border-b border-primary-700">
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-lg font-semibold text-neutral-50">My Assignments</h1>
        <p className="text-sm text-neutral-400">
          {assignedShipments.length} active assignments
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <NetworkStatusIndicator />
        <NotificationBadge count={unreadNotifications} />
      </div>
    </div>
  </header>
  
  {/* Pull-to-refresh indicator */}
  <PullToRefresh onRefresh={refreshAssignments}>
    
    {/* Assignment Cards */}
    <div className="px-4 py-4 space-y-4">
      {assignedShipments.map(shipment => (
        <Card key={shipment.id} className="bg-primary-800 border-primary-700">
          <CardContent className="p-4">
            
            {/* Header with Status */}
            <div className="flex items-start justify-between mb-3">
              <div>
                <h3 className="font-mono text-sm text-accent-500">
                  {shipment.number}
                </h3>
                <p className="text-neutral-50 font-medium">
                  {shipment.customer.name}
                </p>
              </div>
              <StatusBadge status={shipment.status} />
            </div>
            
            {/* Location Information */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center text-sm text-neutral-200">
                <MapPinIcon className="w-4 h-4 mr-2 text-accent-500" />
                <span>Pickup: {shipment.pickupLocation}</span>
              </div>
              <div className="flex items-center text-sm text-neutral-200">
                <FlagIcon className="w-4 h-4 mr-2 text-accent-500" />
                <span>Delivery: {shipment.deliveryLocation}</span>
              </div>
              <div className="flex items-center text-sm text-neutral-400">
                <ClockIcon className="w-4 h-4 mr-2" />
                <span>ETA: {shipment.estimatedDelivery}</span>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex space-x-2">
              <Button 
                size="sm" 
                className="flex-1 bg-accent-500 hover:bg-accent-400 min-h-[44px]"
                onClick={() => navigateToUpdateStatus(shipment.id)}
              >
                <CameraIcon className="w-4 h-4 mr-2" />
                Update Status
              </Button>
              <Button 
                size="sm" 
                variant="secondary" 
                className="bg-primary-700 hover:bg-primary-600 min-h-[44px]"
                onClick={() => openNavigation(shipment.pickupLocation)}
              >
                <NavigationIcon className="w-4 h-4" />
              </Button>
            </div>
            
          </CardContent>
        </Card>
      ))}
    </div>
    
  </PullToRefresh>
  
  {/* Mobile Tab Navigation */}
  <nav className="fixed bottom-0 left-0 right-0 bg-primary-900 border-t border-primary-700">
    <div className="grid grid-cols-4">
      <TabButton 
        icon={<HomeIcon />} 
        label="Dashboard" 
        active 
        href="/driver/dashboard"
      />
      <TabButton 
        icon={<CameraIcon />} 
        label="Update" 
        href="/driver/update"
      />
      <TabButton 
        icon={<PhotoIcon />} 
        label="Photos" 
        href="/driver/photos"
      />
      <TabButton 
        icon={<HistoryIcon />} 
        label="History" 
        href="/driver/history"
      />
    </div>
  </nav>
  
</div>
```

### Mobile Status Update Interface
```tsx
<div className="min-h-screen bg-primary-900">
  
  {/* Header with Back Navigation */}
  <header className="bg-primary-800 px-4 py-3 border-b border-primary-700">
    <div className="flex items-center space-x-3">
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => router.back()}
        className="text-neutral-50"
      >
        <ArrowLeftIcon className="w-5 h-5" />
      </Button>
      <div>
        <h1 className="text-lg font-semibold text-neutral-50">Update Status</h1>
        <p className="text-sm text-neutral-400 font-mono">
          {shipment.number}
        </p>
      </div>
    </div>
  </header>
  
  <div className="px-4 py-6 space-y-6">
    
    {/* Current Status Display */}
    <Card className="bg-primary-800 border-primary-700">
      <CardContent className="p-4">
        <div className="text-center">
          <StatusIcon status={currentStatus} className="w-12 h-12 mx-auto mb-2" />
          <h2 className="text-lg font-medium text-neutral-50 mb-1">
            Current Status
          </h2>
          <p className="text-accent-500 font-medium">
            {getStatusDisplayName(currentStatus)}
          </p>
          <p className="text-xs text-neutral-400 mt-1">
            Last updated: {formatTimestamp(lastUpdate)}
          </p>
        </div>
      </CardContent>
    </Card>
    
    {/* Status Selection */}
    <Card className="bg-primary-800 border-primary-700">
      <CardHeader className="pb-3">
        <CardTitle className="text-neutral-50">Select New Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {availableStatuses.map(status => (
          <Button
            key={status.value}
            variant={selectedStatus === status.value ? "default" : "secondary"}
            className={`w-full justify-start min-h-[44px] ${
              selectedStatus === status.value 
                ? 'bg-accent-500 hover:bg-accent-400' 
                : 'bg-primary-700 hover:bg-primary-600'
            }`}
            onClick={() => setSelectedStatus(status.value)}
          >
            <status.icon className="w-5 h-5 mr-3" />
            <div className="text-left">
              <div className="font-medium">{status.label}</div>
              <div className="text-xs opacity-75">{status.description}</div>
            </div>
          </Button>
        ))}
      </CardContent>
    </Card>
    
    {/* Photo Capture Section */}
    <Card className="bg-primary-800 border-primary-700">
      <CardHeader className="pb-3">
        <CardTitle className="text-neutral-50">Documentation Required</CardTitle>
        <CardDescription>
          Take photos to document the status update
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        
        {/* Camera Interface */}
        <div className="relative">
          <div className="aspect-video bg-primary-900 rounded-lg border-2 border-dashed border-primary-600 flex items-center justify-center">
            {cameraActive ? (
              <CameraPreview onCapture={handlePhotoCapture} />
            ) : (
              <Button 
                onClick={activateCamera}
                className="bg-accent-500 hover:bg-accent-400 min-h-[44px]"
              >
                <CameraIcon className="w-5 h-5 mr-2" />
                Open Camera
              </Button>
            )}
          </div>
        </div>
        
        {/* Photo Gallery */}
        {capturedPhotos.length > 0 && (
          <div>
            <Label className="text-neutral-50 mb-2 block">
              Captured Photos ({capturedPhotos.length}/5)
            </Label>
            <div className="grid grid-cols-3 gap-2">
              {capturedPhotos.map((photo, index) => (
                <div key={index} className="relative">
                  <img 
                    src={photo.thumbnail} 
                    alt={`Photo ${index + 1}`}
                    className="aspect-square object-cover rounded-lg"
                  />
                  <Button
                    size="sm"
                    variant="destructive"
                    className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                    onClick={() => removePhoto(index)}
                  >
                    <XIcon className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
        
      </CardContent>
    </Card>
    
    {/* Location & Notes */}
    <Card className="bg-primary-800 border-primary-700">
      <CardContent className="p-4 space-y-4">
        
        {/* GPS Location */}
        <div>
          <Label className="text-neutral-50 mb-2 block">Location</Label>
          <div className="flex items-center space-x-2 p-3 bg-primary-900 rounded-lg">
            <MapPinIcon className="w-4 h-4 text-accent-500" />
            <div className="flex-1 text-sm">
              {currentLocation ? (
                <div>
                  <p className="text-neutral-50">{currentLocation.address}</p>
                  <p className="text-neutral-400 font-mono text-xs">
                    {currentLocation.coordinates}
                  </p>
                </div>
              ) : (
                <span className="text-neutral-400">Getting location...</span>
              )}
            </div>
            <Button 
              size="sm" 
              variant="secondary"
              onClick={refreshLocation}
              className="bg-primary-700"
            >
              <RefreshIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        {/* Optional Notes */}
        <div>
          <Label htmlFor="notes" className="text-neutral-50 mb-2 block">
            Notes (Optional)
          </Label>
          <Textarea
            id="notes"
            placeholder="Add any additional notes..."
            value={statusNotes}
            onChange={(e) => setStatusNotes(e.target.value)}
            className="bg-primary-900 border-primary-600 text-neutral-50"
            rows={3}
          />
        </div>
        
      </CardContent>
    </Card>
    
    {/* Submit Actions */}
    <div className="space-y-3 pb-6">
      
      {/* Offline Warning */}
      {!isOnline && (
        <Alert className="bg-warning-500/10 border-warning-500">
          <WifiOffIcon className="w-4 h-4" />
          <AlertTitle>Offline Mode</AlertTitle>
          <AlertDescription>
            Update will be queued and sent when connection is restored
          </AlertDescription>
        </Alert>
      )}
      
      {/* Action Buttons */}
      <div className="flex space-x-3">
        <Button 
          variant="secondary" 
          className="flex-1 min-h-[44px] bg-primary-700 hover:bg-primary-600"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button 
          className="flex-1 min-h-[44px] bg-accent-500 hover:bg-accent-400"
          onClick={handleStatusUpdate}
          disabled={!selectedStatus || (!isOnline && capturedPhotos.length === 0)}
        >
          {isOnline ? 'Update Status' : 'Queue Update'}
        </Button>
      </div>
      
    </div>
    
  </div>
  
</div>
```

## Shipment Viewing and Editing Interface

### Shipment Search and List View
```tsx
<div className="max-w-7xl mx-auto px-6 py-8">
  
  {/* Search and Filter Header */}
  <div className="mb-8">
    <div className="flex items-center justify-between mb-6">
      <h1 className="text-2xl font-semibold text-neutral-50">Shipment Management</h1>
      <div className="flex items-center space-x-4">
        <Button className="bg-accent-500 hover:bg-accent-400">
          <PlusIcon className="w-4 h-4 mr-2" />
          Create New Shipment
        </Button>
        <Button variant="secondary" className="bg-primary-700 hover:bg-primary-600">
          <DownloadIcon className="w-4 h-4 mr-2" />
          Export
        </Button>
      </div>
    </div>
    
    {/* Advanced Search Interface */}
    <Card className="bg-primary-800 border-primary-700">
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          
          {/* Quick Search */}
          <div className="lg:col-span-2">
            <Label htmlFor="quickSearch">Quick Search</Label>
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" />
              <Input
                id="quickSearch"
                placeholder="Shipment number, customer, container..."
                className="pl-10 bg-primary-900 border-primary-600"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          {/* Status Filter */}
          <div>
            <Label htmlFor="statusFilter">Status</Label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="bg-primary-900 border-primary-600">
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="booking_confirmed">Booking Confirmed</SelectItem>
                <SelectItem value="in_transit">In Transit</SelectItem>
                <SelectItem value="arrived">Arrived</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Transportation Mode Filter */}
          <div>
            <Label htmlFor="modeFilter">Mode</Label>
            <Select value={modeFilter} onValueChange={setModeFilter}>
              <SelectTrigger className="bg-primary-900 border-primary-600">
                <SelectValue placeholder="All modes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Modes</SelectItem>
                <SelectItem value="sea">Sea Freight</SelectItem>
                <SelectItem value="land">Land Transport</SelectItem>
                <SelectItem value="rail">Rail Transport</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
        </div>
        
        {/* Advanced Filters (Expandable) */}
        <Collapsible open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="text-neutral-300 hover:text-neutral-50 p-0">
              <ChevronDownIcon className={`w-4 h-4 mr-2 transition-transform ${showAdvancedFilters ? 'rotate-180' : ''}`} />
              Advanced Filters
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              
              <div>
                <Label htmlFor="customerFilter">Customer</Label>
                <Select value={customerFilter} onValueChange={setCustomerFilter}>
                  <SelectTrigger className="bg-primary-900 border-primary-600">
                    <SelectValue placeholder="Select customer" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.map(customer => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="dateRangeFrom">Date From</Label>
                <Input
                  type="date"
                  id="dateRangeFrom"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="bg-primary-900 border-primary-600"
                />
              </div>
              
              <div>
                <Label htmlFor="dateRangeTo">Date To</Label>
                <Input
                  type="date"
                  id="dateRangeTo"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="bg-primary-900 border-primary-600"
                />
              </div>
              
              <div className="flex items-end">
                <Button 
                  onClick={clearAllFilters}
                  variant="secondary"
                  className="bg-primary-700 hover:bg-primary-600"
                >
                  Clear Filters
                </Button>
              </div>
              
            </div>
          </CollapsibleContent>
        </Collapsible>
        
      </CardContent>
    </Card>
  </div>
  
  {/* Results Summary and Controls */}
  <div className="flex items-center justify-between mb-6">
    <div className="flex items-center space-x-4 text-neutral-300">
      <span>{filteredShipments.length} shipments found</span>
      {hasActiveFilters && (
        <Badge variant="secondary" className="bg-accent-500 text-black">
          Filtered
        </Badge>
      )}
    </div>
    
    <div className="flex items-center space-x-2">
      <Select value={sortBy} onValueChange={setSortBy}>
        <SelectTrigger className="w-40 bg-primary-800 border-primary-700">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="created_desc">Newest First</SelectItem>
          <SelectItem value="created_asc">Oldest First</SelectItem>
          <SelectItem value="status">Status</SelectItem>
          <SelectItem value="customer">Customer</SelectItem>
          <SelectItem value="eta">ETA</SelectItem>
        </SelectContent>
      </Select>
      
      <div className="flex border border-primary-700 rounded-lg">
        <Button
          variant={viewMode === 'list' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('list')}
          className={viewMode === 'list' ? 'bg-accent-500' : 'bg-transparent'}
        >
          <ListIcon className="w-4 h-4" />
        </Button>
        <Button
          variant={viewMode === 'grid' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('grid')}
          className={viewMode === 'grid' ? 'bg-accent-500' : 'bg-transparent'}
        >
          <GridIcon className="w-4 h-4" />
        </Button>
      </div>
    </div>
  </div>
  
  {/* Shipment Cards/List */}
  <div className={`gap-6 ${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'space-y-4'}`}>
    {filteredShipments.map(shipment => (
      <Card 
        key={shipment.id} 
        className="bg-primary-800 border-primary-700 hover:border-primary-600 transition-colors cursor-pointer"
        onClick={() => openShipmentDetail(shipment.id)}
      >
        <CardContent className="p-6">
          
          {/* Header with Status */}
          <div className="flex items-start justify-between mb-4">
            <div>
              <h3 className="font-mono text-accent-500 font-medium">
                {shipment.number}
              </h3>
              <p className="text-neutral-50 font-medium">{shipment.customer.name}</p>
            </div>
            <StatusBadge status={shipment.status} />
          </div>
          
          {/* Key Information */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center text-sm text-neutral-300">
              <TruckIcon className="w-4 h-4 mr-2 text-accent-500" />
              <span>{getTransportModeLabel(shipment.transportMode)}</span>
            </div>
            <div className="flex items-center text-sm text-neutral-300">
              <MapPinIcon className="w-4 h-4 mr-2 text-accent-500" />
              <span>{shipment.originPort} → {shipment.destinationPort}</span>
            </div>
            <div className="flex items-center text-sm text-neutral-300">
              <CalendarIcon className="w-4 h-4 mr-2 text-accent-500" />
              <span>ETA: {formatDate(shipment.eta)}</span>
            </div>
          </div>
          
          {/* Quick Actions */}
          <div className="flex space-x-2">
            <Button 
              size="sm" 
              variant="secondary"
              className="flex-1 bg-primary-700 hover:bg-primary-600"
              onClick={(e) => {
                e.stopPropagation();
                openShipmentDetail(shipment.id);
              }}
            >
              <EyeIcon className="w-3 h-3 mr-1" />
              View
            </Button>
            <Button 
              size="sm" 
              variant="secondary"
              className="flex-1 bg-primary-700 hover:bg-primary-600"
              onClick={(e) => {
                e.stopPropagation();
                editShipment(shipment.id);
              }}
            >
              <EditIcon className="w-3 h-3 mr-1" />
              Edit
            </Button>
            <Button 
              size="sm" 
              variant="secondary"
              className="bg-primary-700 hover:bg-primary-600"
              onClick={(e) => {
                e.stopPropagation();
                openQuickActions(shipment.id);
              }}
            >
              <MoreVerticalIcon className="w-3 h-3" />
            </Button>
          </div>
          
        </CardContent>
      </Card>
    ))}
  </div>
  
  {/* Pagination */}
  {totalPages > 1 && (
    <div className="flex items-center justify-center mt-8">
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              href="#" 
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              className={currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}
            />
          </PaginationItem>
          
          {[...Array(totalPages)].map((_, i) => (
            <PaginationItem key={i}>
              <PaginationLink 
                href="#" 
                onClick={() => setCurrentPage(i + 1)}
                isActive={currentPage === i + 1}
              >
                {i + 1}
              </PaginationLink>
            </PaginationItem>
          ))}
          
          <PaginationItem>
            <PaginationNext 
              href="#" 
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              className={currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  )}
  
</div>
```

### Shipment Detail View with Tabbed Interface
```tsx
<div className="max-w-7xl mx-auto px-6 py-8">
  
  {/* Header with Actions */}
  <div className="flex items-center justify-between mb-8">
    <div className="flex items-center space-x-4">
      <Button 
        variant="ghost" 
        onClick={() => router.back()}
        className="text-neutral-400 hover:text-neutral-50"
      >
        <ArrowLeftIcon className="w-5 h-5 mr-2" />
        Back to Shipments
      </Button>
      <div>
        <h1 className="text-2xl font-semibold text-neutral-50">{shipment.number}</h1>
        <p className="text-neutral-400">{shipment.customer.name}</p>
      </div>
    </div>
    
    <div className="flex items-center space-x-4">
      <StatusBadge status={shipment.status} size="lg" />
      <div className="flex space-x-2">
        <Button 
          variant="secondary"
          className="bg-primary-700 hover:bg-primary-600"
          onClick={() => setEditMode(!editMode)}
        >
          <EditIcon className="w-4 h-4 mr-2" />
          {editMode ? 'Cancel Edit' : 'Edit Shipment'}
        </Button>
        <Button className="bg-accent-500 hover:bg-accent-400">
          <DocumentIcon className="w-4 h-4 mr-2" />
          Generate Documents
        </Button>
      </div>
    </div>
  </div>
  
  {/* Edit Mode Warning */}
  {editMode && (
    <Alert className="mb-6 bg-warning-500/10 border-warning-500">
      <EditIcon className="w-4 h-4" />
      <AlertTitle>Edit Mode Active</AlertTitle>
      <AlertDescription>
        You are now editing this shipment. Changes will be automatically saved. 
        Click "Cancel Edit" to exit without saving unsaved changes.
      </AlertDescription>
    </Alert>
  )}
  
  {/* Tabbed Content */}
  <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
    
    {/* Tab Navigation */}
    <TabsList className="grid w-full grid-cols-6 bg-primary-800 border border-primary-700">
      <TabsTrigger value="overview" className="data-[state=active]:bg-accent-500">
        <HomeIcon className="w-4 h-4 mr-2" />
        Overview
      </TabsTrigger>
      <TabsTrigger value="logistics" className="data-[state=active]:bg-accent-500">
        <TruckIcon className="w-4 h-4 mr-2" />
        Logistics
      </TabsTrigger>
      <TabsTrigger value="products" className="data-[state=active]:bg-accent-500">
        <PackageIcon className="w-4 h-4 mr-2" />
        Products
      </TabsTrigger>
      <TabsTrigger value="documents" className="data-[state=active]:bg-accent-500">
        <DocumentIcon className="w-4 h-4 mr-2" />
        Documents
      </TabsTrigger>
      <TabsTrigger value="communication" className="data-[state=active]:bg-accent-500">
        <MessageSquareIcon className="w-4 h-4 mr-2" />
        Communication
      </TabsTrigger>
      <TabsTrigger value="audit" className="data-[state=active]:bg-accent-500">
        <ClockIcon className="w-4 h-4 mr-2" />
        Audit Trail
      </TabsTrigger>
    </TabsList>
    
    {/* Overview Tab */}
    <TabsContent value="overview" className="space-y-6">
      
      {/* Key Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        
        <Card className="bg-primary-800 border-primary-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-neutral-400">Transportation</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-semibold text-neutral-50">
              {getTransportModeLabel(shipment.transportMode)}
            </p>
            <p className="text-sm text-neutral-400">
              {shipment.originPort} → {shipment.destinationPort}
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-primary-800 border-primary-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-neutral-400">Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <p className="text-sm">
                <span className="text-neutral-400">ETD:</span>
                <span className="text-neutral-50 ml-2">{formatDateTime(shipment.etd)}</span>
              </p>
              <p className="text-sm">
                <span className="text-neutral-400">ETA:</span>
                <span className="text-neutral-50 ml-2">{formatDateTime(shipment.eta)}</span>
              </p>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-primary-800 border-primary-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-neutral-400">Containers</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-semibold text-neutral-50">
              {shipment.containers.length}
            </p>
            <p className="text-sm text-neutral-400">
              Total Weight: {shipment.totalWeight}kg
            </p>
          </CardContent>
        </Card>
        
        <Card className="bg-primary-800 border-primary-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-neutral-400">Value</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg font-semibold text-neutral-50">
              ${shipment.totalValue.toLocaleString()}
            </p>
            <p className="text-sm text-neutral-400">
              {shipment.products.length} product types
            </p>
          </CardContent>
        </Card>
        
      </div>
      
      {/* Status Timeline */}
      <Card className="bg-primary-800 border-primary-700">
        <CardHeader>
          <CardTitle className="text-neutral-50">Status Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {shipment.statusHistory.map((status, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className={`w-3 h-3 rounded-full ${
                    status.completed ? 'bg-accent-500' : 'bg-neutral-600'
                  }`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-neutral-50">
                    {getStatusDisplayName(status.status)}
                  </p>
                  <p className="text-xs text-neutral-400">
                    {formatDateTime(status.timestamp)} • {status.user}
                  </p>
                  {status.notes && (
                    <p className="text-xs text-neutral-300 mt-1">{status.notes}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
    </TabsContent>
    
    {/* Additional tabs would be implemented similarly... */}
    
  </Tabs>
  
</div>
```

---
