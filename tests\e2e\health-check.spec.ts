import { test, expect } from '@playwright/test'

test.describe('Health Check', () => {
  test('API health endpoint returns proper structure', async ({ request }) => {
    const response = await request.get('/api/health')
    const healthData = await response.json()

    // Check response structure
    expect(healthData).toHaveProperty('status')
    expect(healthData).toHaveProperty('timestamp')
    expect(healthData).toHaveProperty('services')
    expect(healthData.services).toHaveProperty('database')
    expect(healthData.services).toHaveProperty('authentication')
    expect(healthData.services).toHaveProperty('storage')
    expect(healthData.services).toHaveProperty('realtime')

    // Check service structure
    Object.values(healthData.services).forEach((service: any) => {
      expect(service).toHaveProperty('status')
      expect(service).toHaveProperty('details')
      expect(['healthy', 'unhealthy', 'unknown']).toContain(service.status)
    })
  })

  test('Health page loads and displays system status', async ({ page }) => {
    await page.goto('/health')

    // Check page title
    await expect(
      page.getByRole('heading', { name: /system health check/i })
    ).toBeVisible()

    // Check refresh button
    await expect(page.getByRole('button', { name: /refresh/i })).toBeVisible()

    // Wait for health check to complete
    await expect(page.getByText(/overall status/i)).toBeVisible()

    // Check services section
    await expect(page.getByText(/services/i)).toBeVisible()
    await expect(page.getByText(/database/i)).toBeVisible()
    await expect(page.getByText(/authentication/i)).toBeVisible()
    await expect(page.getByText(/storage/i)).toBeVisible()
    await expect(page.getByText(/realtime/i)).toBeVisible()
  })

  test('Home page loads successfully', async ({ page }) => {
    await page.goto('/')

    await expect(
      page.getByRole('heading', { name: /dyy trading management system/i })
    ).toBeVisible()
    await expect(page.getByText(/system status/i)).toBeVisible()
  })
})
