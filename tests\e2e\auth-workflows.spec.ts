import { test, expect, type Page } from '@playwright/test'

// Test user credentials for different roles
const testUsers = {
  admin: {
    email: '<EMAIL>',
    password: 'AdminPass123!',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
  },
  customer: {
    email: '<EMAIL>',
    password: 'CustomerPass123!',
    firstName: 'Customer',
    lastName: 'User',
    role: 'customer',
  },
  driver: {
    email: '<EMAIL>',
    password: 'DriverPass123!',
    firstName: 'Driver',
    lastName: 'User',
    role: 'driver',
  },
  newUser: {
    email: `newuser-${Date.now()}@example.com`,
    password: 'NewUserPass123!',
    firstName: 'New',
    lastName: 'User',
    role: 'customer',
  },
}

// Helper functions
async function loginUser(page: Page, userCredentials: typeof testUsers.admin) {
  await page.goto('/login')
  
  await page.fill('[data-testid="email-input"], input[type="email"]', userCredentials.email)
  await page.fill('[data-testid="password-input"], input[type="password"]', userCredentials.password)
  
  await page.click('button[type="submit"], button:has-text("Sign in")')
  
  // Wait for redirect to dashboard
  await page.waitForURL('**/dashboard**')
}

async function logout(page: Page) {
  // Click user menu
  await page.click('[data-testid="user-menu"], button:has-text("Profile"), .user-menu')
  
  // Click logout
  await page.click('button:has-text("Sign out"), button:has-text("Logout")')
  
  // Wait for redirect to login
  await page.waitForURL('**/login')
}

test.describe('Authentication Workflows E2E Tests', () => {
  test.describe('Login Flow', () => {
    test('should successfully login with valid credentials', async ({ page }) => {
      await page.goto('/login')
      
      // Verify login page elements
      await expect(page.getByText('Welcome back')).toBeVisible()
      await expect(page.getByText('Sign in to your account to continue')).toBeVisible()
      await expect(page.getByLabel('Email')).toBeVisible()
      await expect(page.getByLabel('Password')).toBeVisible()
      
      // Fill login form
      await page.fill('input[type="email"]', testUsers.admin.email)
      await page.fill('input[type="password"]', testUsers.admin.password)
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Should redirect to dashboard
      await expect(page).toHaveURL(/.*dashboard.*/)
      
      // Verify user is logged in (check for user profile or navigation)
      await expect(page.getByText(/dashboard/i)).toBeVisible()
    })

    test('should show error for invalid credentials', async ({ page }) => {
      await page.goto('/login')
      
      // Fill with invalid credentials
      await page.fill('input[type="email"]', '<EMAIL>')
      await page.fill('input[type="password"]', 'wrongpassword')
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Should show error message
      await expect(page.getByText(/invalid.*credentials|email.*password/i)).toBeVisible()
      
      // Should stay on login page
      await expect(page).toHaveURL(/.*login.*/)
    })

    test('should validate email format', async ({ page }) => {
      await page.goto('/login')
      
      // Fill with invalid email format
      await page.fill('input[type="email"]', 'invalid-email')
      await page.fill('input[type="password"]', 'somepassword')
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Should show validation error
      await expect(page.getByText(/invalid.*email|enter.*valid.*email/i)).toBeVisible()
    })

    test('should validate required fields', async ({ page }) => {
      await page.goto('/login')
      
      // Submit empty form
      await page.click('button[type="submit"]')
      
      // Should show validation errors
      await expect(page.getByText(/email.*required|password.*required/i)).toBeVisible()
    })

    test('should toggle password visibility', async ({ page }) => {
      await page.goto('/login')
      
      const passwordInput = page.locator('input[type="password"]')
      const toggleButton = page.locator('button:near(input[type="password"])')
      
      // Fill password
      await passwordInput.fill('testpassword')
      
      // Initially should be password type
      await expect(passwordInput).toHaveAttribute('type', 'password')
      
      // Click toggle button
      await toggleButton.click()
      
      // Should become text type
      await expect(passwordInput).toHaveAttribute('type', 'text')
      
      // Click again to hide
      await toggleButton.click()
      
      // Should become password type again
      await expect(passwordInput).toHaveAttribute('type', 'password')
    })

    test('should redirect authenticated users away from login page', async ({ page }) => {
      // First login
      await loginUser(page, testUsers.admin)
      
      // Try to access login page
      await page.goto('/login')
      
      // Should redirect to dashboard
      await expect(page).toHaveURL(/.*dashboard.*/)
    })
  })

  test.describe('Registration Flow', () => {
    test('should successfully register new user', async ({ page }) => {
      await page.goto('/register')
      
      // Verify registration page elements
      await expect(page.getByText('Create your account')).toBeVisible()
      await expect(page.getByLabel('Email')).toBeVisible()
      await expect(page.getByLabel('Password')).toBeVisible()
      await expect(page.getByLabel('Confirm Password')).toBeVisible()
      await expect(page.getByLabel('First Name')).toBeVisible()
      await expect(page.getByLabel('Last Name')).toBeVisible()
      
      // Fill registration form
      await page.fill('input[name="email"]', testUsers.newUser.email)
      await page.fill('input[name="password"]', testUsers.newUser.password)
      await page.fill('input[name="confirmPassword"]', testUsers.newUser.password)
      await page.fill('input[name="firstName"]', testUsers.newUser.firstName)
      await page.fill('input[name="lastName"]', testUsers.newUser.lastName)
      await page.fill('input[name="phoneNumber"]', '+**********')
      
      // Select role (default should be customer)
      await expect(page.getByText('Customer')).toBeVisible()
      
      // Select company if required for customer role
      const companySelect = page.locator('select[name="companyId"], [role="combobox"]:near(:text("Company"))')
      if (await companySelect.isVisible()) {
        await companySelect.click()
        await page.click('option:first-child, [role="option"]:first-child')
      }
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Should show success message or redirect
      await expect(page.getByText(/account.*created|registration.*successful|check.*email/i)).toBeVisible()
    })

    test('should validate password confirmation match', async ({ page }) => {
      await page.goto('/register')
      
      // Fill form with non-matching passwords
      await page.fill('input[name="email"]', '<EMAIL>')
      await page.fill('input[name="password"]', 'Password123!')
      await page.fill('input[name="confirmPassword"]', 'DifferentPassword123!')
      await page.fill('input[name="firstName"]', 'Test')
      await page.fill('input[name="lastName"]', 'User')
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Should show password mismatch error
      await expect(page.getByText(/passwords.*not.*match|passwords.*must.*match/i)).toBeVisible()
    })

    test('should show password strength indicator', async ({ page }) => {
      await page.goto('/register')
      
      const passwordInput = page.locator('input[name="password"]')
      
      // Type weak password
      await passwordInput.fill('weak')
      
      // Should show password strength indicator
      await expect(page.getByText(/password.*strength|weak|strong/i)).toBeVisible()
      
      // Type strong password
      await passwordInput.fill('StrongPassword123!')
      
      // Should show strong indicator
      await expect(page.getByText(/strong/i)).toBeVisible()
    })

    test('should handle role-based company requirements', async ({ page }) => {
      await page.goto('/register')
      
      // Fill basic form
      await page.fill('input[name="email"]', '<EMAIL>')
      await page.fill('input[name="firstName"]', 'Role')
      await page.fill('input[name="lastName"]', 'Test')
      
      // Select admin role (should not require company)
      await page.click('[role="combobox"]:near(:text("Role"))')
      await page.click('[role="option"]:has-text("Administrator")')
      
      // Company field should not be required or visible
      const companyField = page.locator(':text("Company")')
      if (await companyField.isVisible()) {
        // If visible, it should not be required
        await expect(companyField).not.toHaveAttribute('required')
      }
      
      // Switch to customer role (should require company)
      await page.click('[role="combobox"]:near(:text("Role"))')
      await page.click('[role="option"]:has-text("Customer")')
      
      // Company field should be visible and required
      await expect(page.getByText(/company/i)).toBeVisible()
    })

    test('should validate required fields', async ({ page }) => {
      await page.goto('/register')
      
      // Submit empty form
      await page.click('button[type="submit"]')
      
      // Should show validation errors for required fields
      await expect(page.getByText(/email.*required/i)).toBeVisible()
      await expect(page.getByText(/first.*name.*required/i)).toBeVisible()
      await expect(page.getByText(/last.*name.*required/i)).toBeVisible()
      await expect(page.getByText(/password.*required/i)).toBeVisible()
    })

    test('should show error for existing email', async ({ page }) => {
      await page.goto('/register')
      
      // Fill form with existing email
      await page.fill('input[name="email"]', testUsers.admin.email) // Use existing admin email
      await page.fill('input[name="password"]', 'NewPassword123!')
      await page.fill('input[name="confirmPassword"]', 'NewPassword123!')
      await page.fill('input[name="firstName"]', 'Test')
      await page.fill('input[name="lastName"]', 'User')
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Should show email exists error
      await expect(page.getByText(/email.*already.*exists|user.*already.*registered/i)).toBeVisible()
    })
  })

  test.describe('Role-Based Access Control', () => {
    test('admin should access admin dashboard', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      
      // Navigate to admin section
      await page.goto('/dashboard/admin')
      
      // Should be able to access admin dashboard
      await expect(page.getByText(/administration|admin.*dashboard|user.*management/i)).toBeVisible()
      
      // Should see admin-specific navigation
      await expect(page.getByText(/user.*management|system.*settings/i)).toBeVisible()
    })

    test('customer should not access admin dashboard', async ({ page }) => {
      await loginUser(page, testUsers.customer)
      
      // Try to navigate to admin section
      await page.goto('/dashboard/admin')
      
      // Should be redirected or show unauthorized
      await expect(page.getByText(/unauthorized|access.*denied|not.*authorized/i)).toBeVisible()
    })

    test('driver should access mobile interface', async ({ page }) => {
      await loginUser(page, testUsers.driver)
      
      // Should redirect to mobile dashboard
      await expect(page).toHaveURL(/.*mobile.*dashboard.*/)
      
      // Should see mobile-specific content
      await expect(page.getByText(/my.*deliveries|delivery.*status|mobile.*dashboard/i)).toBeVisible()
    })

    test('should show role-appropriate navigation', async ({ page }) => {
      // Test admin navigation
      await loginUser(page, testUsers.admin)
      
      // Should see all navigation items
      await expect(page.getByText(/shipments/i)).toBeVisible()
      await expect(page.getByText(/logistics/i)).toBeVisible()
      await expect(page.getByText(/inventory/i)).toBeVisible()
      await expect(page.getByText(/customers/i)).toBeVisible()
      await expect(page.getByText(/companies/i)).toBeVisible()
      await expect(page.getByText(/reports/i)).toBeVisible()
      await expect(page.getByText(/administration/i)).toBeVisible()
      
      await logout(page)
      
      // Test customer navigation
      await loginUser(page, testUsers.customer)
      
      // Should see limited navigation
      await expect(page.getByText(/shipments/i)).toBeVisible()
      await expect(page.getByText(/documents/i)).toBeVisible()
      await expect(page.getByText(/notifications/i)).toBeVisible()
      
      // Should not see admin/restricted items
      await expect(page.getByText(/administration/i)).not.toBeVisible()
      await expect(page.getByText(/logistics/i)).not.toBeVisible()
      await expect(page.getByText(/inventory/i)).not.toBeVisible()
    })
  })

  test.describe('Admin User Management', () => {
    test('admin should manage user accounts', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      
      // Navigate to user management
      await page.goto('/dashboard/admin/users')
      
      // Should see user management interface
      await expect(page.getByText(/user.*management|manage.*users/i)).toBeVisible()
      
      // Should see user table
      await expect(page.getByRole('table')).toBeVisible()
      
      // Should see create user button
      await expect(page.getByRole('button', { name: /create.*user|add.*user/i })).toBeVisible()
      
      // Should see filter options
      await expect(page.getByText(/filter|search|role|status/i)).toBeVisible()
    })

    test('admin should create new user', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      await page.goto('/dashboard/admin/users')
      
      // Click create user button
      await page.click('button:has-text("Create User"), button:has-text("Add User")')
      
      // Should open create user dialog
      await expect(page.getByText(/create.*user|add.*user.*account/i)).toBeVisible()
      
      // Fill user creation form
      const newUserEmail = `admintest-${Date.now()}@example.com`
      await page.fill('input[name="email"]', newUserEmail)
      await page.fill('input[name="firstName"]', 'Admin')
      await page.fill('input[name="lastName"]', 'Created')
      await page.fill('input[name="password"]', 'AdminCreated123!')
      
      // Select role
      await page.click('[role="combobox"]:near(:text("Role"))')
      await page.click('[role="option"]:has-text("Customer Service")')
      
      // Submit form
      await page.click('button[type="submit"]:has-text("Create")')
      
      // Should show success message
      await expect(page.getByText(/user.*created|account.*created.*successfully/i)).toBeVisible()
      
      // Should see new user in table
      await expect(page.getByText(newUserEmail)).toBeVisible()
    })

    test('admin should activate/deactivate users', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      await page.goto('/dashboard/admin/users')
      
      // Find a user row and click actions
      const userRow = page.locator('tr:has-text("<EMAIL>")').first()
      await userRow.locator('button:has-text("Actions"), [data-testid="user-actions"]').click()
      
      // Click deactivate option
      await page.click('button:has-text("Deactivate"), [role="menuitem"]:has-text("Deactivate")')
      
      // Should open deactivation dialog
      await expect(page.getByText(/deactivate.*user.*account/i)).toBeVisible()
      
      // Fill deactivation reason
      await page.fill('textarea', 'Test deactivation for E2E testing')
      
      // Confirm deactivation
      await page.click('button:has-text("Deactivate Account")')
      
      // Should show success message
      await expect(page.getByText(/user.*deactivated|account.*deactivated.*successfully/i)).toBeVisible()
      
      // User should show as inactive
      await expect(userRow.getByText(/inactive|deactivated/i)).toBeVisible()
    })

    test('admin should not deactivate last admin', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      await page.goto('/dashboard/admin/users')
      
      // Try to deactivate admin user (assuming it's the last admin)
      const adminRow = page.locator('tr:has-text("<EMAIL>")').first()
      await adminRow.locator('button:has-text("Actions")').click()
      await page.click('button:has-text("Deactivate")')
      
      // Fill reason and submit
      await page.fill('textarea', 'Test deactivation')
      await page.click('button:has-text("Deactivate Account")')
      
      // Should show error preventing deactivation
      await expect(page.getByText(/cannot.*deactivate.*last.*admin|must.*have.*admin/i)).toBeVisible()
    })

    test('admin should filter users by role and status', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      await page.goto('/dashboard/admin/users')
      
      // Filter by role
      await page.click('select[name="role"], [data-testid="role-filter"]')
      await page.click('option:has-text("Admin"), [role="option"]:has-text("Admin")')
      
      // Should show only admin users
      await expect(page.getByText(/admin.*role|administrator/i)).toBeVisible()
      
      // Filter by status
      await page.click('select[name="status"], [data-testid="status-filter"]')
      await page.click('option:has-text("Active"), [role="option"]:has-text("Active")')
      
      // Should show only active users
      await expect(page.getByText(/active.*status/i)).toBeVisible()
      
      // Clear filters
      await page.click('button:has-text("Clear"), button:has-text("Reset")')
      
      // Should show all users again
      await expect(page.getByRole('table')).toBeVisible()
    })
  })

  test.describe('Password Reset Flow', () => {
    test('should access forgot password page', async ({ page }) => {
      await page.goto('/login')
      
      // Click forgot password link
      await page.click('a:has-text("Forgot"), a:has-text("password")')
      
      // Should navigate to forgot password page
      await expect(page).toHaveURL(/.*forgot.*password.*/)
      await expect(page.getByText(/forgot.*password|reset.*password/i)).toBeVisible()
    })

    test('should submit forgot password request', async ({ page }) => {
      await page.goto('/forgot-password')
      
      // Fill email
      await page.fill('input[type="email"]', testUsers.customer.email)
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Should show success message
      await expect(page.getByText(/email.*sent|check.*email|reset.*link/i)).toBeVisible()
    })

    test('should validate email in forgot password', async ({ page }) => {
      await page.goto('/forgot-password')
      
      // Submit with invalid email
      await page.fill('input[type="email"]', 'invalid-email')
      await page.click('button[type="submit"]')
      
      // Should show validation error
      await expect(page.getByText(/invalid.*email|enter.*valid.*email/i)).toBeVisible()
    })
  })

  test.describe('Session Management', () => {
    test('should logout successfully', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      
      // Click logout (assuming it's in a user menu)
      await page.click('[data-testid="user-menu"], button:has-text("Profile")')
      await page.click('button:has-text("Sign out"), button:has-text("Logout")')
      
      // Should redirect to login page
      await expect(page).toHaveURL(/.*login.*/)
      await expect(page.getByText(/welcome.*back|sign.*in/i)).toBeVisible()
    })

    test('should maintain session across page reloads', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      
      // Verify logged in state
      await expect(page.getByText(/dashboard/i)).toBeVisible()
      
      // Reload page
      await page.reload()
      
      // Should still be logged in
      await expect(page.getByText(/dashboard/i)).toBeVisible()
      await expect(page).toHaveURL(/.*dashboard.*/)
    })

    test('should handle session expiration', async ({ page }) => {
      await loginUser(page, testUsers.admin)
      
      // Simulate session expiration by clearing storage
      await page.evaluate(() => {
        localStorage.clear()
        sessionStorage.clear()
      })
      
      // Try to access protected page
      await page.goto('/dashboard/admin')
      
      // Should redirect to login
      await expect(page).toHaveURL(/.*login.*/)
    })

    test('should prevent access to protected routes when not authenticated', async ({ page }) => {
      // Try to access protected routes without login
      const protectedRoutes = [
        '/dashboard',
        '/dashboard/admin',
        '/dashboard/shipments',
        '/dashboard/settings',
      ]
      
      for (const route of protectedRoutes) {
        await page.goto(route)
        
        // Should redirect to login
        await expect(page).toHaveURL(/.*login.*/)
      }
    })
  })

  test.describe('Cross-Browser Compatibility', () => {
    // These tests will run on all configured browsers
    test('login flow works across browsers', async ({ page, browserName }) => {
      console.log(`Testing login on ${browserName}`)
      
      await page.goto('/login')
      
      // Fill and submit login form
      await page.fill('input[type="email"]', testUsers.admin.email)
      await page.fill('input[type="password"]', testUsers.admin.password)
      await page.click('button[type="submit"]')
      
      // Should work on all browsers
      await expect(page).toHaveURL(/.*dashboard.*/)
    })
  })

  test.describe('Mobile Responsiveness', () => {
    test.use({ viewport: { width: 375, height: 667 } }) // iPhone size
    
    test('login form should be responsive on mobile', async ({ page }) => {
      await page.goto('/login')
      
      // Check form is visible and usable
      await expect(page.getByLabel('Email')).toBeVisible()
      await expect(page.getByLabel('Password')).toBeVisible()
      
      // Fill form on mobile
      await page.fill('input[type="email"]', testUsers.customer.email)
      await page.fill('input[type="password"]', testUsers.customer.password)
      
      // Submit should work
      await page.click('button[type="submit"]')
      
      // Should redirect successfully
      await expect(page).toHaveURL(/.*dashboard.*/)
    })

    test('navigation should work on mobile', async ({ page }) => {
      await loginUser(page, testUsers.customer)
      
      // Check for mobile navigation (hamburger menu)
      const mobileNav = page.locator('[data-testid="mobile-nav"], button:has-text("Menu")')
      if (await mobileNav.isVisible()) {
        await mobileNav.click()
        
        // Should show navigation items
        await expect(page.getByText(/shipments|documents|settings/i)).toBeVisible()
      }
    })
  })

  test.describe('Error Recovery', () => {
    test('should recover from network errors', async ({ page }) => {
      await page.goto('/login')
      
      // Simulate network failure
      await page.route('**/api/auth/**', route => route.abort())
      
      // Try to login
      await page.fill('input[type="email"]', testUsers.admin.email)
      await page.fill('input[type="password"]', testUsers.admin.password)
      await page.click('button[type="submit"]')
      
      // Should show network error
      await expect(page.getByText(/network.*error|connection.*failed/i)).toBeVisible()
      
      // Restore network
      await page.unroute('**/api/auth/**')
      
      // Retry should work
      if (await page.getByRole('button', { name: /try.*again|retry/i }).isVisible()) {
        await page.click('button:has-text("Try Again")')
        await expect(page).toHaveURL(/.*dashboard.*/)
      }
    })

    test('should handle server errors gracefully', async ({ page }) => {
      await page.goto('/login')
      
      // Simulate server error
      await page.route('**/api/auth/**', route => 
        route.fulfill({ status: 500, body: 'Internal Server Error' })
      )
      
      // Try to login
      await page.fill('input[type="email"]', testUsers.admin.email)
      await page.fill('input[type="password"]', testUsers.admin.password)
      await page.click('button[type="submit"]')
      
      // Should show error message
      await expect(page.getByText(/server.*error|something.*went.*wrong/i)).toBeVisible()
      
      // Should stay on login page
      await expect(page).toHaveURL(/.*login.*/)
    })
  })
})