# Customer-Consignee Relationship Management Implementation Plan

## Overview
Add a customer-consignee relationship management feature similar to the existing customer-shipper relationships, using the provided `customer_consignees` table schema.

## Architecture Pattern
Following the exact same pattern as customer-shipper relationships for consistency:
- Reuse existing UI components and patterns
- Follow same state management approach
- Apply same validation and filtering logic
- Integrate with shipment creation workflow

## Components to Create

### 1. Core Components
- **Store**: `src/stores/customer-consignee-store.ts`
  - Mirror customer-shipper store structure
  - Handle CRUD operations for customer_consignees table
  - Manage pagination, filtering, and sorting

- **Hook**: `src/hooks/use-customer-consignees.ts`
  - Provide customer-consignee management functionality
  - Include methods for getting consignees for customers
  - Handle default consignee logic

- **Validation**: `src/lib/validations/customer-consignees.ts`
  - Zod schema for customer-consignee relationships
  - Form validation rules matching table constraints

### 2. UI Components
- **Form Component**: `src/components/forms/customer-consignee-form/customer-consignee-form.tsx`
  - Customer selection (customer type only)
  - Consignee selection (consignee type only)
  - Default consignee toggle
  - Active status toggle
  - Notes field

- **Page Component**: Add customer-consignee management to relationships page or create new section
  - Data table with filtering and search
  - CRUD operations (Create, Read, Update, Delete)
  - Bulk operations support

### 3. Integration Points
- **Navigation**: Add menu item for customer-consignee relationships
- **Shipment Creation**: Integrate with shipment form for intelligent pre-population
- **Real-time Updates**: Subscribe to customer_consignees table changes

## Database Schema Mapping
The provided `customer_consignees` table has these key features:
- `customer_id` → companies with type 'customer'
- `consignee_id` → companies with type 'consignee'
- `is_default` → only one default per customer
- `is_active` → controls visibility in workflows
- `notes` → additional relationship information
- Unique constraint on (customer_id, consignee_id)
- Proper indexes for performance

## Implementation Steps

### Phase 1: Core Infrastructure
1. Create customer-consignee store with CRUD operations
2. Create validation schemas and TypeScript types
3. Create custom hook for relationship management
4. Implement company type filtering (customer/consignee only)

### Phase 2: UI Components
1. Create customer-consignee form component
2. Add relationship management page/section
3. Implement data table with search/filtering
4. Add CRUD dialogs and bulk operations

### Phase 3: Integration
1. Add navigation menu item
2. Integrate with shipment creation workflow
3. Implement real-time subscriptions
4. Add intelligent pre-population logic

### Phase 4: Testing & Polish
1. Unit tests for validation and store logic
2. Integration tests for CRUD operations
3. E2E tests for complete workflows
4. UI/UX improvements and accessibility

## Key Features to Implement
- ✅ Customer-consignee relationship CRUD
- ✅ Default consignee designation per customer
- ✅ Active/inactive relationship status
- ✅ Company type validation (customer + consignee only)
- ✅ Search and filtering capabilities
- ✅ Bulk import/export functionality
- ✅ Integration with shipment creation
- ✅ Real-time updates and subscriptions

## Files to Create/Modify
- **New**: `src/stores/customer-consignee-store.ts`
- **New**: `src/hooks/use-customer-consignees.ts`
- **New**: `src/lib/validations/customer-consignees.ts`
- **New**: `src/components/forms/customer-consignee-form/customer-consignee-form.tsx`
- **Modify**: Navigation component to add customer-consignee menu
- **Modify**: Relationships page or create new customer-consignee page
- **Modify**: Shipment creation form for consignee pre-population
- **New**: Tests for customer-consignee functionality

## Technical Details

### Store Implementation Pattern
```typescript
// Based on customer-shipper-store.ts structure
export interface CustomerConsignee {
  id: string
  customer_id: string
  consignee_id: string
  is_default: boolean | null
  is_active: boolean | null
  notes: string | null
  created_at: string | null
  updated_at: string | null
  // Joined company data
  customer?: Company | null
  consignee?: Company | null
}
```

### Hook Implementation Pattern
```typescript
// Based on use-customer-shippers.ts structure
export function useCustomerConsignees(customerId?: string) {
  // Filter relationships for specific customer
  // Get consignees for customer
  // Get default consignee for customer
  // CRUD operations
}
```

### Form Component Pattern
```typescript
// Based on customer-shipper-form.tsx structure
interface CustomerConsigneeFormProps {
  relationship?: CustomerConsignee | null
  onSubmit: (data: CustomerConsigneeFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}
```

### Database Access Pattern
```typescript
// Query customer-consignee relationships
const { data: relationships } = await supabase
  .from('customer_consignees')
  .select(`
    *,
    customer:companies!customer_id(name, company_type, contact_phone),
    consignee:companies!consignee_id(name, company_type, contact_phone, contact_email)
  `)
  .eq('is_active', true)
  .order('customer:companies(name)')
```

### Default Consignee Management
```typescript
// Reset previous default when setting new default
if (is_default) {
  await supabase
    .from('customer_consignees')
    .update({ is_default: false })
    .eq('customer_id', customer_id)
    .neq('id', relationship_id)
}
```

## Integration with Existing System

### Navigation Integration
Add to the master-data section in the sidebar navigation:
- "Customer-Consignee Relationships" menu item
- Place alongside existing "Customer-Shipper Relationships"

### Shipment Creation Integration
- When customer is selected, load available consignees
- Auto-populate default consignee if available
- Real-time updates when relationships change

### Company Type Filtering
- Customer dropdown: Filter companies where `company_type = 'customer'`
- Consignee dropdown: Filter companies where `company_type = 'consignee'`
- Use existing company type validation patterns

This plan leverages the existing customer-shipper implementation as a template, ensuring consistency in user experience and code patterns while implementing the customer-consignee relationship management system.