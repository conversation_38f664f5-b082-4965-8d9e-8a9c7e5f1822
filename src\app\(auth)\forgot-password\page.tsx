'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResol<PERSON> } from '@hookform/resolvers/zod'
import { ArrowLeft, Loader2, Mail } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  passwordResetRequestSchema,
  type PasswordResetRequestFormData,
} from '@/lib/validations/auth'
import { authClient, type AuthError } from '@/lib/supabase/auth'
import { AuthErrorDisplay } from '@/components/auth/auth-error-display'

export default function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<AuthError | null>(null)
  const [success, setSuccess] = useState(false)

  const form = useForm<PasswordResetRequestFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(passwordResetRequestSchema),
    defaultValues: {
      email: '',
    },
  })

  async function onSubmit(data: PasswordResetRequestFormData) {
    setIsLoading(true)
    setError(null)

    try {
      await authClient.resetPassword(data.email)
      setSuccess(true)
    } catch (error) {
      console.error('Password reset error:', error)
      if ((error as AuthError).code) {
        setError(error as AuthError)
      } else {
        setError({
          code: 'UNKNOWN_ERROR',
          message:
            error instanceof Error
              ? error.message
              : 'Failed to send reset email. Please try again.',
          timestamp: new Date(),
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  function handleRetry() {
    setError(null)
    form.handleSubmit(onSubmit)()
  }

  if (success) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
            <Mail className="h-8 w-8 text-green-400" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">
            Check your email
          </h2>
          <p className="text-slate-300">
            We've sent a password reset link to{' '}
            <strong>{form.getValues('email')}</strong>
          </p>
        </div>

        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
          <div className="text-center space-y-2">
            <p className="text-blue-300 text-sm">
              Didn't receive the email? Check your spam folder or try again.
            </p>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSuccess(false)}
              className="text-blue-400 hover:text-blue-300"
            >
              Send another email
            </Button>
          </div>
        </div>

        <div className="text-center">
          <Link
            href="/login"
            className="inline-flex items-center gap-2 text-sm text-orange-400 hover:text-orange-300 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to sign in
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">
          Forgot your password?
        </h2>
        <p className="text-slate-300">
          Enter your email address and we'll send you a link to reset your
          password.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <AuthErrorDisplay
            error={error}
            onRetry={handleRetry}
            className="mb-4"
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    placeholder="Enter your email address"
                    className="bg-white/10 border-white/20 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage className="text-red-300" />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full bg-orange-500 hover:bg-orange-600 text-white"
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Send reset link
          </Button>
        </form>
      </Form>

      <div className="text-center">
        <Link
          href="/login"
          className="inline-flex items-center gap-2 text-sm text-orange-400 hover:text-orange-300 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to sign in
        </Link>
      </div>
    </div>
  )
}
