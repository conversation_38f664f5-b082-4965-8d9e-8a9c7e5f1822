'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  Di<PERSON>Title, 
  DialogFooter 
} from '@/components/ui/dialog'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { 
  Edit3, 
  Calendar, 
  Ship, 
  Truck, 
  MapPin,
  CheckCircle, 
  X,
  Loader2,
  MoreHorizontal,
  Clock
} from 'lucide-react'
import { format } from 'date-fns'

interface QuickEditField {
  id: string
  label: string
  type: 'text' | 'date' | 'select' | 'datetime-local'
  icon: React.ReactNode
  currentValue: any
  options?: { value: string; label: string }[]
  placeholder?: string
  required?: boolean
}

interface QuickEditShortcutsProps {
  shipmentId: string
  fields: QuickEditField[]
  onSave: (fieldId: string, value: any) => Promise<void>
  isLoading?: boolean
}

export function QuickEditShortcuts({
  shipmentId,
  fields,
  onSave,
  isLoading = false
}: QuickEditShortcutsProps) {
  const [editingField, setEditingField] = useState<string | null>(null)
  const [editValue, setEditValue] = useState<any>('')
  const [showQuickEditDialog, setShowQuickEditDialog] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const openEditField = (field: QuickEditField) => {
    setEditingField(field.id)
    // Format the current value based on field type
    if (field.type === 'date' && field.currentValue) {
      try {
        setEditValue(format(new Date(field.currentValue), 'yyyy-MM-dd'))
      } catch {
        setEditValue('')
      }
    } else if (field.type === 'datetime-local' && field.currentValue) {
      try {
        setEditValue(format(new Date(field.currentValue), "yyyy-MM-dd'T'HH:mm"))
      } catch {
        setEditValue('')
      }
    } else {
      setEditValue(field.currentValue || '')
    }
    setShowQuickEditDialog(true)
  }

  const cancelEdit = () => {
    setEditingField(null)
    setEditValue('')
    setShowQuickEditDialog(false)
  }

  const saveEdit = async () => {
    if (!editingField) return
    
    try {
      setIsSaving(true)
      await onSave(editingField, editValue)
      cancelEdit()
    } catch (error) {
      console.error('Failed to save field:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const currentField = fields.find(f => f.id === editingField)

  return (
    <>
      {/* Quick Action Buttons */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            disabled={isLoading}
            className="border-green-500 bg-green-600/10 text-green-100 hover:bg-green-600/20 hover:border-green-400"
          >
            <Edit3 className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Quick Edit</span>
            <MoreHorizontal className="h-3 w-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
          {fields.map((field) => (
            <DropdownMenuItem
              key={field.id}
              onClick={() => openEditField(field)}
              className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 cursor-pointer"
            >
              <div className="flex items-center gap-2 w-full">
                {field.icon}
                <div className="flex-1">
                  <div className="font-medium">{field.label}</div>
                  <div className="text-xs text-slate-400 truncate">
                    {field.currentValue ? 
                      (field.type === 'date' && field.currentValue ? 
                        format(new Date(field.currentValue), 'MMM dd, yyyy') 
                        : field.currentValue.toString()
                      ) : 'Not set'
                    }
                  </div>
                </div>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Quick Edit Dialog */}
      <Dialog open={showQuickEditDialog} onOpenChange={(open) => !open && cancelEdit()}>
        <DialogContent className="sm:max-w-[450px] bg-slate-800 border-slate-700">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <Edit3 className="h-5 w-5" />
              Quick Edit - {currentField?.label}
            </DialogTitle>
          </DialogHeader>

          {currentField && (
            <div className="space-y-4">
              {/* Current Value Display */}
              <div className="p-3 bg-slate-900/50 rounded-lg border border-slate-600">
                <div className="flex items-center gap-2 text-sm">
                  {currentField.icon}
                  <span className="text-slate-400">Current Value:</span>
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    {currentField.currentValue ? 
                      (currentField.type === 'date' && currentField.currentValue ? 
                        format(new Date(currentField.currentValue), 'MMM dd, yyyy') 
                        : currentField.currentValue.toString()
                      ) : 'Not set'
                    }
                  </Badge>
                </div>
              </div>

              {/* Edit Input */}
              <div className="space-y-2">
                <label className="text-sm text-slate-300 font-medium">
                  New Value {currentField.required && <span className="text-red-400">*</span>}
                </label>
                
                {currentField.type === 'select' && currentField.options ? (
                  <select
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    disabled={isSaving}
                  >
                    <option value="">Select {currentField.label.toLowerCase()}</option>
                    {currentField.options.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : (
                  <Input
                    type={currentField.type}
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    placeholder={currentField.placeholder || `Enter ${currentField.label.toLowerCase()}`}
                    className="bg-slate-700 border-slate-600 text-white focus:border-blue-500"
                    disabled={isSaving}
                  />
                )}
              </div>

              {/* Validation Info */}
              {currentField.type === 'date' && (
                <div className="p-2 bg-blue-900/20 rounded border border-blue-500/20">
                  <div className="flex items-center gap-2 text-blue-300 text-sm">
                    <Calendar className="h-4 w-4" />
                    <span>Date format: YYYY-MM-DD</span>
                  </div>
                </div>
              )}

              {currentField.type === 'datetime-local' && (
                <div className="p-2 bg-blue-900/20 rounded border border-blue-500/20">
                  <div className="flex items-center gap-2 text-blue-300 text-sm">
                    <Clock className="h-4 w-4" />
                    <span>Include date and time</span>
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={cancelEdit}
              disabled={isSaving}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={saveEdit}
              disabled={!editValue || (currentField?.required && !editValue) || isSaving}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

// Helper component for inline field display with quick edit button
interface QuickEditFieldProps {
  label: string
  value: any
  type?: 'text' | 'date' | 'select'
  icon?: React.ReactNode
  onQuickEdit: () => void
  className?: string
  isStaff?: boolean
}

export function QuickEditField({ 
  label, 
  value, 
  type = 'text', 
  icon, 
  onQuickEdit, 
  className = "",
  isStaff = false 
}: QuickEditFieldProps) {
  const formatValue = (val: any) => {
    if (!val) return 'Not set'
    if (type === 'date') {
      try {
        return format(new Date(val), 'MMM dd, yyyy')
      } catch {
        return val
      }
    }
    return val.toString()
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className="flex-1">
        <label className="text-sm text-slate-400">{label}</label>
        <div className="flex items-center gap-2 mt-1">
          {icon}
          <span className="text-white">{formatValue(value)}</span>
        </div>
      </div>
      {isStaff && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onQuickEdit}
          className="h-6 px-2 text-xs text-slate-400 hover:text-white ml-2"
        >
          <Edit3 className="h-3 w-3" />
        </Button>
      )}
    </div>
  )
}