# Epic 3 Core Shipment Management

**Epic Goal:** Implement complete shipment lifecycle management from creation through delivery with intelligent cascading selection, status tracking across multiple transportation modes, container management, and stakeholder coordination to deliver the core business value of the fruit export management system.

## Story 3.1 Enhanced Intelligent Shipment Creation Interface

As a CS representative,  
I want to create shipments with Transportation Mode pre-selection, mandatory stakeholder fields, and intelligent pre-population based on relationships,  
so that I can efficiently initiate export processes with minimal data entry while ensuring all critical information is captured.

### Acceptance Criteria

**1:** Transportation Mode selection modal (Sea/Land/Rail) appears before shipment creation form and configures appropriate workflow fields and requirements.

**2:** Factory selection is mandatory with dropdown showing location and capacity information.

**3:** Forwarder Agent selection is mandatory with contact information and service details display.

**4:** ETD (Estimated Time of Departure), ETA (Estimated Time of Arrival), and Closing Time are mandatory datetime fields with validation ensuring logical sequence (Closing Time < ETD < ETA).

**5:** Destination Port selection is mandatory for routing and documentation, and require Origin Port with customer history suggestions.

**6:** Customer selection automatically loads associated shippers with default shipper pre-selected in cascading dropdown.

**7:** Customer selection automatically loads associated products with default product pre-selected and pricing auto-populated (CIF/FOB per KG).

**8:** Consignee selection automatically loads associated notify parties with default notify party pre-selected.

**9:** Booking Confirmation Document upload is mandatory with validation, preview, and secure storage capabilities.

**10:** Optional Notes field allows additional instructions visible to all stakeholders and included in shipment documentation.

**11:** Shipment number is automatically generated upon save using format EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running] where running number resets monthly per mode/port combination.

**12:** Form validation ensures data consistency, required field completion, and date/time logic validation before submission.

## Story 3.2 Automatic Container Generation and Product Management

As a CS representative,  
I want containers to be automatically generated and products allocated when shipments are saved,  
so that I can focus on shipment details without manual container entry while ensuring accurate cargo tracking.

### Acceptance Criteria

**1:** System automatically generates dummy containers when shipment is saved based on product quantities and packaging specifications.

**2:** Products are automatically allocated to containers based on quantity (number of packages), packaging specifications, and optimal container utilization.

**3:** Weight calculations automatically compute gross/net weights based on packaging-type quantities and per-package weights from customer-product relationships.

**4:** Container management interface allows post-creation editing of container details including type, size, seal number, and weight information.

**5:** Product details include shipping marks, manufacturing dates, expiration dates, lot numbers, and quality grades captured during shipment creation.

**6:** Container and product data validation ensures consistency with master data and customer-product specifications throughout the process.

## Story 3.3 Shipment Status Lifecycle Management

As a CS representative,  
I want to track and update shipment status throughout the export process,  
so that I can coordinate with stakeholders and maintain accurate records.

### Acceptance Criteria

**1:** Status workflow supports complete lifecycle: booking_confirmed → transport_assigned → driver_assigned → empty_container_picked → arrived_at_factory → loading_started → departed_factory → container_returned → shipped → arrived → completed.

**2:** Status updates create automatic audit trail with timestamp, user, location, and notes.

**3:** Status history displays complete timeline with all transitions and responsible parties.

**4:** Manual status updates include location information and optional notes for context.

**5:** Status changes trigger real-time updates using Supabase subscriptions for connected users.

## Story 3.4 Transportation Assignment Management

As a CS representative,  
I want to assign transportation resources to shipments,  
so that I can coordinate pickup, delivery, and logistics operations.

### Acceptance Criteria

**1:** Transportation assignment interface supports both company vehicles (with driver assignment) and external carrier companies.

**2:** Carrier selection is filtered to companies with carrier type and shows available drivers.

**3:** Assignment captures vehicle details (head/tail numbers), driver contact, pickup/delivery locations with GPS coordinates.

**4:** Transportation details include estimated distance and special handling instructions.

**5:** Assignment changes update shipment status automatically and notify relevant parties.

## Story 3.5 Shipment Search and Filtering

As a CS representative,  
I want to search and filter shipments efficiently,  
so that I can quickly locate specific shipments and manage my workload.

### Acceptance Criteria

**1:** Shipment list displays with search by shipment number, invoice number, customer name, or container number.

**2:** Filtering options include status, customer, carrier, date ranges, and transportation mode.

**3:** Sorting capabilities support multiple columns (date, status, customer, destination).

**4:** Pagination and infinite scroll support large shipment datasets efficiently.

**5:** Advanced search supports compound filters and saved search preferences.

## Story 3.6 Shipment Detail and Overview

As a CS representative,  
I want comprehensive shipment detail views,  
so that I can review all information and coordinate with stakeholders effectively.

### Acceptance Criteria

**1:** Shipment detail page displays complete information including stakeholders, containers, products, status history, and documents.

**2:** Editable sections allow updates to shipment details with proper validation and permission checks.

**3:** Related information shows customer-product pricing, shipper details, and notify party preferences automatically.

**4:** Action buttons provide quick access to common operations (status update, document generation, notifications).

**5:** Responsive design ensures detail view works effectively on tablets and mobile devices.

## Story 3.7 Multi-Modal Transportation Support

As a CS representative,  
I want to manage shipments across different transportation modes,  
so that I can handle sea, land, and rail export operations.

### Acceptance Criteria

**1:** Transportation mode selection (sea, land, rail) dynamically adjusts available fields and workflow options.

**2:** Sea transportation captures vessel name, voyage number, booking number, and port information.

**3:** Land transportation focuses on vehicle details, route information, and border crossing requirements.

**4:** Rail transportation includes rail line details, car numbers, and station information.

**5:** Status workflows adapt to transportation mode while maintaining consistent core progression.
