'use client'

import { useState, useEffect, useCallback } from 'react'
import { MapPin, Navigation, AlertCircle, CheckCircle, RotateCcw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useLanguage } from '@/hooks/use-language'
import type { GPSCoordinates } from '@/types/status-update'

interface LocationCaptureProps {
  onLocationCaptured: (coords: GPSCoordinates) => void
  showConfirmation?: boolean
  disabled?: boolean
  accuracyThreshold?: number // meters
  timeout?: number // milliseconds
  enableHighAccuracy?: boolean
}

interface LocationState {
  coordinates: GPSCoordinates | null
  isLoading: boolean
  error: string | null
  isSupported: boolean
  permissionStatus: 'prompt' | 'granted' | 'denied' | 'unavailable'
}

export function LocationCapture({
  onLocationCaptured,
  showConfirmation = true,
  disabled = false,
  accuracyThreshold = 50, // 50 meters
  timeout = 15000, // 15 seconds
  enableHighAccuracy = true
}: LocationCaptureProps) {
  const { t } = useLanguage()
  const [location, setLocation] = useState<LocationState>({
    coordinates: null,
    isLoading: false,
    error: null,
    isSupported: false,
    permissionStatus: 'prompt'
  })
  
  const [watchId, setWatchId] = useState<number | null>(null)
  const [isConfirmed, setIsConfirmed] = useState(false)

  // Check geolocation support on mount
  useEffect(() => {
    const checkSupport = async () => {
      if (!navigator.geolocation) {
        setLocation(prev => ({
          ...prev,
          isSupported: false,
          permissionStatus: 'unavailable',
          error: 'Geolocation not supported by this device'
        }))
        return
      }

      setLocation(prev => ({ ...prev, isSupported: true }))

      // Check permission status if available
      if ('permissions' in navigator) {
        try {
          const permission = await navigator.permissions.query({ name: 'geolocation' })
          setLocation(prev => ({ 
            ...prev, 
            permissionStatus: permission.state as 'prompt' | 'granted' | 'denied'
          }))
        } catch (error) {
          // Permission API might not be supported
          console.warn('Permission API not supported:', error)
        }
      }
    }

    checkSupport()
  }, [])

  const getLocationAccuracy = (accuracy: number): { level: string; color: string; message: string } => {
    if (accuracy <= 5) {
      return {
        level: 'Excellent',
        color: 'text-green-400',
        message: t('locationCapture.excellent')
      }
    } else if (accuracy <= 20) {
      return {
        level: 'Good',
        color: 'text-blue-400',
        message: t('locationCapture.good')
      }
    } else if (accuracy <= accuracyThreshold) {
      return {
        level: 'Fair',
        color: 'text-yellow-400',
        message: t('locationCapture.acceptable')
      }
    } else {
      return {
        level: 'Poor',
        color: 'text-red-400',
        message: t('locationCapture.low')
      }
    }
  }

  const formatCoordinates = (lat: number, lng: number): string => {
    const formatCoord = (coord: number, isLat: boolean): string => {
      const abs = Math.abs(coord)
      const deg = Math.floor(abs)
      const min = (abs - deg) * 60
      const direction = isLat 
        ? (coord >= 0 ? 'N' : 'S')
        : (coord >= 0 ? 'E' : 'W')
      return `${deg}°${min.toFixed(4)}'${direction}`
    }

    return `${formatCoord(lat, true)}, ${formatCoord(lng, false)}`
  }

  const formatForDatabase = (coords: { lat: number; lng: number }): string => {
    return `POINT(${coords.lng} ${coords.lat})`
  }

  const getCurrentPosition = useCallback((): Promise<GPSCoordinates> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation not supported'))
        return
      }

      const options: PositionOptions = {
        enableHighAccuracy,
        timeout,
        maximumAge: 30000 // 30 seconds cache
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const coords: GPSCoordinates = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          }
          resolve(coords)
        },
        (error) => {
          let message = 'Failed to get location'
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              message = t('locationCapture.accessDenied')
              break
            case error.POSITION_UNAVAILABLE:
              message = t('locationCapture.unavailable')
              break
            case error.TIMEOUT:
              message = t('locationCapture.timeout')
              break
            default:
              message = error.message || 'Unknown location error'
          }
          
          reject(new Error(message))
        },
        options
      )
    })
  }, [enableHighAccuracy, timeout])

  const captureLocation = async () => {
    if (disabled || !location.isSupported) return

    setLocation(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const coordinates = await getCurrentPosition()
      
      // Check accuracy threshold
      if (coordinates.accuracy > accuracyThreshold) {
        setLocation(prev => ({
          ...prev,
          coordinates,
          isLoading: false,
          error: t('locationCapture.lowAccuracyWarning').replace('{accuracy}', coordinates.accuracy.toFixed(0))
        }))
      } else {
        setLocation(prev => ({
          ...prev,
          coordinates,
          isLoading: false,
          error: null
        }))
      }
      
      // Auto-confirm if not showing confirmation dialog
      if (!showConfirmation) {
        onLocationCaptured(coordinates)
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to capture location'
      
      setLocation(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
        permissionStatus: errorMessage.includes('denied') ? 'denied' : prev.permissionStatus
      }))
    }
  }

  const confirmLocation = () => {
    if (location.coordinates) {
      onLocationCaptured(location.coordinates)
      setIsConfirmed(true)
    }
  }

  const retryCapture = () => {
    setLocation(prev => ({ 
      ...prev, 
      coordinates: null, 
      error: null 
    }))
    setIsConfirmed(false)
    captureLocation()
  }

  // Start watching position for real-time updates
  const startWatching = () => {
    if (!navigator.geolocation || disabled) return

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 5000
    }

    const id = navigator.geolocation.watchPosition(
      (position) => {
        const coords: GPSCoordinates = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp
        }
        
        setLocation(prev => ({ ...prev, coordinates: coords }))
      },
      (error) => {
        console.error('Watch position error:', error)
      },
      options
    )

    setWatchId(id)
  }

  const stopWatching = () => {
    if (watchId !== null) {
      navigator.geolocation.clearWatch(watchId)
      setWatchId(null)
    }
  }

  // Cleanup watch on unmount
  useEffect(() => {
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId)
      }
    }
  }, [watchId])

  if (!location.isSupported) {
    return (
      <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
        <div className="flex items-center space-x-3 text-slate-400">
          <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
          <div>
            <div className="text-sm font-medium text-red-400">
              Location Not Available
            </div>
            <div className="text-xs mt-1">
              This device does not support GPS location services
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Error Display */}
      {location.error && (
        <Alert className="border-red-500/30 bg-red-500/10">
          <AlertCircle className="h-4 w-4 text-red-400" />
          <AlertDescription className="text-red-300">
            {location.error}
          </AlertDescription>
        </Alert>
      )}

      {/* Location Capture Controls */}
      {!location.coordinates && (
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-slate-300">
              {t('locationCapture.gpsRequired')}
            </h3>
            <Badge variant="outline" className="text-xs border-slate-600 text-slate-400">
              {t('locationCapture.required')}
            </Badge>
          </div>
          
          <div className="space-y-3">
            <p className="text-sm text-slate-400">
              {t('locationCapture.locationDescription')}
            </p>
            
            <Button
              onClick={captureLocation}
              disabled={disabled || location.isLoading}
              className="w-full bg-orange-600 hover:bg-orange-700 text-white min-h-[48px]"
            >
              {location.isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  {t('locationCapture.gettingLocation')}
                </>
              ) : (
                <>
                  <Navigation className="w-5 h-5 mr-2" />
                  {t('locationCapture.captureCurrentLocation')}
                </>
              )}
            </Button>

            <div className="text-xs text-slate-500 text-center">
              {t('locationCapture.accuracyTarget')} {accuracyThreshold}m
            </div>
          </div>
        </div>
      )}

      {/* Location Display & Confirmation */}
      {location.coordinates && (
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-slate-300">
              {t('locationCapture.capturedLocation')}
            </h3>
            {isConfirmed ? (
              <Badge className="bg-green-600 text-white">
                <CheckCircle className="w-3 h-3 mr-1" />
                {t('locationCapture.confirmed')}
              </Badge>
            ) : (
              <Badge variant="outline" className="text-xs border-orange-500 text-orange-400">
                {t('locationCapture.pendingConfirmation')}
              </Badge>
            )}
          </div>

          <div className="space-y-3">
            {/* Coordinates Display */}
            <div className="bg-slate-700 rounded-lg p-3">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-orange-500 flex-shrink-0" />
                  <span className="text-sm font-mono text-white">
                    {location.coordinates.latitude.toFixed(6)}, {location.coordinates.longitude.toFixed(6)}
                  </span>
                </div>
                <Button
                  onClick={retryCapture}
                  disabled={disabled || location.isLoading}
                  size="sm"
                  variant="ghost"
                  className="text-slate-400 hover:text-white h-8 px-2"
                >
                  <RotateCcw className="w-3 h-3" />
                </Button>
              </div>

              <div className="text-xs text-slate-400">
                {formatCoordinates(location.coordinates.latitude, location.coordinates.longitude)}
              </div>
            </div>

            {/* Accuracy Information */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-xs text-slate-500">Accuracy:</span>
                <span className={`text-xs font-medium ${getLocationAccuracy(location.coordinates.accuracy).color}`}>
                  ±{location.coordinates.accuracy.toFixed(0)}m ({getLocationAccuracy(location.coordinates.accuracy).level})
                </span>
              </div>
              <div className="text-xs text-slate-500">
                {new Date(location.coordinates.timestamp).toLocaleTimeString()}
              </div>
            </div>

            {/* Confirmation Buttons */}
            {showConfirmation && !isConfirmed && (
              <div className="flex space-x-3 pt-2">
                <Button
                  onClick={confirmLocation}
                  disabled={disabled}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  {t('locationCapture.confirmLocation')}
                </Button>
                <Button
                  onClick={retryCapture}
                  disabled={disabled}
                  variant="outline"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}