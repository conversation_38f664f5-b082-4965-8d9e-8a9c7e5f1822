'use client'

import { useState } from 'react'
import { formatDistanceToNow, parseISO } from 'date-fns'
import { 
  Camera, 
  User, 
  Clock, 
  ChevronDown, 
  ChevronUp, 
  Eye,
  Download,
  AlertCircle,
  Loader2,
  Image as ImageIcon,
  X,
  Settings,
  Shield
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { StatusBadge } from './status-badge'
import { useStatusPhotos, type StatusPhotoGroup } from '@/hooks/use-status-photos'
import { useAuth } from '@/hooks/use-auth'
import { PhotoManagementPanel } from '@/components/forms/status-photos/photo-management-panel'
import type { ShipmentStatus } from '@/lib/supabase/types'

interface StatusPhotosGalleryProps {
  shipmentId: string
  className?: string
}

export function StatusPhotosGallery({ shipmentId, className = '' }: StatusPhotosGalleryProps) {
  const { photoGroups, isLoading, error, refetch } = useStatusPhotos(shipmentId)
  const { isStaff } = useAuth()
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())
  const [lightboxPhoto, setLightboxPhoto] = useState<string | null>(null)
  const [showManagement, setShowManagement] = useState(false)

  // Toggle group expansion
  const toggleGroup = (statusHistoryId: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev)
      if (newSet.has(statusHistoryId)) {
        newSet.delete(statusHistoryId)
      } else {
        newSet.add(statusHistoryId)
      }
      return newSet
    })
  }

  // Close lightbox
  const closeLightbox = () => {
    setLightboxPhoto(null)
  }

  // Download image
  const downloadImage = async (url: string, filename: string) => {
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    } catch (err) {
      console.error('Error downloading image:', err)
    }
  }

  if (isLoading) {
    return (
      <Card className={`bg-slate-800 border-slate-700 ${className}`}>
        <CardContent className="p-6">
          <div className="space-y-4">
            {[...Array(2)].map((_, i) => (
              <StatusPhotoGroupSkeleton key={i} />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={`bg-slate-800 border-slate-700 ${className}`}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  if (photoGroups.length === 0) {
    return (
      <Card className={`bg-card border-secondary ${className}`}>
        <CardHeader>
          <CardTitle className="text-slate-200 flex items-center gap-2">
            <Camera className="h-5 w-5 text-orange-500" />
            Status Documentation
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="text-center py-8">
            <Camera className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-200 mb-2">
              No Photos Available
            </h3>
            <p className="text-slate-400">
              Status photos will appear here when drivers upload them during status updates.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={`bg-card border-secondary ${className}`}>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex items-center gap-3">
              <CardTitle className="text-slate-200 flex items-center gap-2">
                <Camera className="h-5 w-5 text-orange-500" />
                Status Documentation
              </CardTitle>
              <Badge variant="secondary" className="bg-slate-700 text-slate-300 h-6 flex items-center">
                {photoGroups.reduce((total, group) => total + group.total_count, 0)} photos
              </Badge>
            </div>

            {/* Management Button for Staff */}
            {isStaff && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowManagement(!showManagement)}
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 h-8 flex items-center w-fit"
              >
                <Settings className="h-4 w-4 mr-2" />
                {showManagement ? 'Hide' : 'Manage'} Photos
                <Shield className="h-3 w-3 ml-1 text-blue-400" />
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {photoGroups.map((group) => (
              <StatusPhotoGroup
                key={group.status_history_id}
                group={group}
                isExpanded={expandedGroups.has(group.status_history_id)}
                onToggle={() => toggleGroup(group.status_history_id)}
                onPhotoClick={setLightboxPhoto}
                onDownload={downloadImage}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Photo Management Panel */}
      {isStaff && showManagement && (
        <PhotoManagementPanel
          shipmentId={shipmentId}
          photoGroups={photoGroups}
          onPhotosChanged={refetch}
          className="mt-4"
        />
      )}

      {/* Lightbox Modal */}
      {lightboxPhoto && (
        <Lightbox
          photoUrl={lightboxPhoto}
          onClose={closeLightbox}
          onDownload={(url) => downloadImage(url, 'status-photo.jpg')}
        />
      )}
    </>
  )
}

// Individual status photo group component
interface StatusPhotoGroupProps {
  group: StatusPhotoGroup
  isExpanded: boolean
  onToggle: () => void
  onPhotoClick: (url: string) => void
  onDownload: (url: string, filename: string) => void
}

function StatusPhotoGroup({
  group,
  isExpanded,
  onToggle,
  onPhotoClick,
  onDownload,
}: StatusPhotoGroupProps) {
  const statusDate = parseISO(group.status_date)
  const timeAgo = formatDistanceToNow(statusDate, { addSuffix: true })

  return (
    <div className="border border-slate-600 rounded-lg bg-slate-700/30">
      {/* Group Header */}
      <button
        onClick={onToggle}
        className="w-full p-4 flex items-center justify-between hover:bg-slate-700/50 transition-colors rounded-t-lg"
      >
        <div className="flex items-center space-x-3">
          <StatusBadge status={group.status as ShipmentStatus} size="default" showIcon />
          <Badge variant="secondary" className="bg-orange-500/20 text-orange-300 border-orange-400">
            {group.total_count} photo{group.total_count !== 1 ? 's' : ''}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 text-xs text-slate-400">
            <Clock className="h-3 w-3" />
            <span>{timeAgo}</span>
          </div>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 text-slate-400" />
          ) : (
            <ChevronDown className="h-4 w-4 text-slate-400" />
          )}
        </div>
      </button>

      {/* Photos Grid */}
      {isExpanded && (
        <div className="p-4 border-t border-slate-600">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 mb-4">
            {group.photos.map((photo) => (
              <PhotoThumbnail
                key={photo.id}
                photo={photo}
                onPhotoClick={onPhotoClick}
                onDownload={onDownload}
              />
            ))}
          </div>
          
          {/* Uploader Info */}
          {group.photos[0]?.uploader_profile && (
            <div className="flex items-center space-x-2 text-xs text-slate-400 pt-2 border-t border-slate-600">
              <User className="h-3 w-3" />
              <span>
                Uploaded by: {group.photos[0].uploader_profile.full_name || group.photos[0].uploader_profile.email}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Photo thumbnail component
interface PhotoThumbnailProps {
  photo: any
  onPhotoClick: (url: string) => void
  onDownload: (url: string, filename: string) => void
}

function PhotoThumbnail({ photo, onPhotoClick, onDownload }: PhotoThumbnailProps) {
  const [imageLoading, setImageLoading] = useState(true)
  const [imageError, setImageError] = useState(false)

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  const handleImageError = () => {
    setImageLoading(false)
    setImageError(true)
  }

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (photo.signed_url) {
      const filename = `status-photo-${photo.id}.jpg`
      onDownload(photo.signed_url, filename)
    }
  }

  if (imageError || !photo.signed_url) {
    return (
      <div className="aspect-square bg-slate-600 rounded-lg border border-slate-500 flex items-center justify-center">
        <ImageIcon className="h-6 w-6 text-slate-400" />
      </div>
    )
  }

  return (
    <div className="relative group aspect-square">
      {/* Loading Placeholder */}
      {imageLoading && (
        <div className="absolute inset-0 bg-slate-600 rounded-lg border border-slate-500 flex items-center justify-center">
          <Loader2 className="h-4 w-4 text-slate-400 animate-spin" />
        </div>
      )}

      {/* Image */}
      <img
        src={photo.signed_url}
        alt={`Status photo from ${photo.created_at}`}
        className={`w-full h-full object-cover rounded-lg border border-slate-500 cursor-pointer transition-all hover:ring-2 hover:ring-orange-500 hover:ring-offset-2 hover:ring-offset-slate-800 ${
          imageLoading ? 'opacity-0' : 'opacity-100'
        }`}
        onLoad={handleImageLoad}
        onError={handleImageError}
        onClick={() => onPhotoClick(photo.signed_url)}
      />

      {/* Overlay Actions */}
      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-2">
        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0 hover:bg-white/20"
          onClick={() => onPhotoClick(photo.signed_url)}
        >
          <Eye className="h-4 w-4 text-white" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0 hover:bg-white/20"
          onClick={handleDownload}
        >
          <Download className="h-4 w-4 text-white" />
        </Button>
      </div>
    </div>
  )
}

// Lightbox component
interface LightboxProps {
  photoUrl: string
  onClose: () => void
  onDownload: (url: string) => void
}

function Lightbox({ photoUrl, onClose, onDownload }: LightboxProps) {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const handleDownload = () => {
    onDownload(photoUrl)
  }

  return (
    <div
      className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div className="relative max-w-4xl max-h-full">
        {/* Close Button */}
        <Button
          size="sm"
          variant="ghost"
          className="absolute top-4 right-4 z-10 h-8 w-8 p-0 bg-black/50 hover:bg-black/70 text-white"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>

        {/* Download Button */}
        <Button
          size="sm"
          variant="ghost"
          className="absolute top-4 right-16 z-10 h-8 w-8 p-0 bg-black/50 hover:bg-black/70 text-white"
          onClick={handleDownload}
        >
          <Download className="h-4 w-4" />
        </Button>

        {/* Image */}
        <img
          src={photoUrl}
          alt="Status photo"
          className="max-w-full max-h-full object-contain rounded-lg"
        />
      </div>
    </div>
  )
}

// Skeleton loader for photo groups
function StatusPhotoGroupSkeleton() {
  return (
    <div className="border border-slate-600 rounded-lg bg-slate-700/30">
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-6 bg-slate-600 rounded animate-pulse w-24" />
          <div className="h-5 bg-slate-600 rounded animate-pulse w-16" />
        </div>
        <div className="h-4 bg-slate-600 rounded animate-pulse w-20" />
      </div>
    </div>
  )
}