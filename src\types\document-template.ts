/**
 * Document Template Types
 * Story 5.1: Document Template Management System
 */

// Document type enum matching the database enum
export type DocumentType = 
  | 'booking_confirmation' 
  | 'invoice_fob' 
  | 'invoice_cif' 
  | 'shipping_instruction' 
  | 'bill_of_lading' 
  | 'photo_upload' 
  | 'other'

// Page size options matching database constraints
export type PageSize = 'A4' | 'A3' | 'Letter' | 'Legal' | 'A5'

// Page orientation options matching database constraints
export type PageOrientation = 'portrait' | 'landscape'

// Access level for template visibility and permissions
export type TemplateAccessLevel = 'admin' | 'staff' | 'public'

/**
 * Core document template interface matching database schema
 */
export interface DocumentTemplate {
  id: string
  template_name: string
  document_type: DocumentType
  version: string
  template_content: string // HTML/text template content
  template_data: Record<string, any> | null // JSONB for template configuration
  template_styles: string | null // CSS styles for template
  page_size: PageSize
  page_orientation: PageOrientation
  margin_top: number
  margin_bottom: number
  margin_left: number
  margin_right: number
  language: string // ISO language code format (e.g., 'en', 'en-US')
  currency_format: string
  date_format: string
  number_format: string
  description: string | null
  usage_notes: string | null
  required_fields: string[] | null
  is_active: boolean
  is_default: boolean
  created_by: string // References profiles.user_id
  created_at: string
  updated_at: string
}

/**
 * Template creation/update payload
 */
export interface DocumentTemplateInsert {
  template_name: string
  document_type: DocumentType
  version?: string
  template_content: string
  template_data?: Record<string, any> | null
  template_styles?: string | null
  page_size?: PageSize
  page_orientation?: PageOrientation
  margin_top?: number
  margin_bottom?: number
  margin_left?: number
  margin_right?: number
  language?: string
  currency_format?: string
  date_format?: string
  number_format?: string
  description?: string | null
  usage_notes?: string | null
  required_fields?: string[] | null
  is_active?: boolean
  is_default?: boolean
}

/**
 * Template update payload (all fields optional except constraints)
 */
export interface DocumentTemplateUpdate {
  template_name?: string
  document_type?: DocumentType
  version?: string
  template_content?: string
  template_data?: Record<string, any> | null
  template_styles?: string | null
  page_size?: PageSize
  page_orientation?: PageOrientation
  margin_top?: number
  margin_bottom?: number
  margin_left?: number
  margin_right?: number
  language?: string
  currency_format?: string
  date_format?: string
  number_format?: string
  description?: string | null
  usage_notes?: string | null
  required_fields?: string[] | null
  is_active?: boolean
  is_default?: boolean
}

/**
 * Template filters for querying
 */
export interface DocumentTemplateFilters {
  document_type?: DocumentType[]
  is_active?: boolean
  is_default?: boolean
  search?: string
  created_by?: string
  language?: string
}

/**
 * Template sorting options
 */
export interface DocumentTemplateSortConfig {
  field: keyof DocumentTemplate
  direction: 'asc' | 'desc'
}

/**
 * Template query result with pagination
 */
export interface DocumentTemplateQueryResult {
  data: DocumentTemplate[]
  totalCount: number
  error?: string
}

/**
 * Template field placeholder for the editor
 */
export interface TemplateFieldPlaceholder {
  id: string
  label: string
  placeholder: string
  category: 'shipment' | 'customer' | 'product' | 'stakeholder' | 'system'
  dataPath: string // JSON path to the data (e.g., 'shipment.shipment_number')
  format?: 'text' | 'number' | 'date' | 'currency' | 'percentage'
  required?: boolean
  description?: string
}

/**
 * Template validation error
 */
export interface TemplateValidationError {
  field: string
  message: string
  code: string
}

/**
 * Template validation result
 */
export interface TemplateValidationResult {
  isValid: boolean
  errors: TemplateValidationError[]
  warnings?: TemplateValidationError[]
}

/**
 * Document type configuration for UI display
 */
export interface DocumentTypeConfig {
  type: DocumentType
  label: string
  description: string
  icon?: string
  category: 'commercial' | 'shipping' | 'compliance' | 'other'
  requiredFields: string[]
  suggestedPlaceholders: string[]
}

/**
 * Template preview options
 */
export interface TemplatePreviewOptions {
  templateId: string
  sampleData?: Record<string, any>
  format?: 'html' | 'pdf'
  includeStyles?: boolean
}

/**
 * Template activation/deactivation result
 */
export interface TemplateActivationResult {
  success: boolean
  message: string
  affectedTemplates?: DocumentTemplate[]
}