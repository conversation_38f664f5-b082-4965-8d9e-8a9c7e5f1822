import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { isStaff } from '@/lib/supabase/auth'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

// Create Supabase clients
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
)

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const containerId = params.id
    
    if (!containerId) {
      console.error('Container confirmation API: Missing container ID')
      return NextResponse.json({ 
        error: 'Bad Request', 
        details: 'Container ID is required' 
      }, { status: 400 })
    }

    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization?.startsWith('Bearer ')) {
      console.error('Container confirmation API: Missing or invalid authorization header')
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Missing or invalid authorization header' 
      }, { status: 401 })
    }

    const token = authorization.split(' ')[1]
    if (!token) {
      console.error('Container confirmation API: Empty authorization token')
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Empty authorization token' 
      }, { status: 401 })
    }

    // Verify user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.error('Container confirmation API: Auth error:', authError)
      return NextResponse.json({ 
        error: 'Invalid token', 
        details: authError?.message || 'No user found for provided token' 
      }, { status: 401 })
    }

    // Get user profile and check role
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role, first_name, last_name')
      .eq('user_id', user.id)
      .single()

    if (profileError) {
      console.error('Container confirmation API: Profile error:', profileError)
      return NextResponse.json({ 
        error: 'Profile not found', 
        details: profileError.message 
      }, { status: 404 })
    }

    // Check if user has permission to confirm (Admin or CS only)
    if (!isStaff(profile.role)) {
      console.error('Container confirmation API: Insufficient permissions for role:', profile.role)
      return NextResponse.json({ 
        error: 'Forbidden', 
        details: 'Only Admin and Customer Service can confirm container numbers' 
      }, { status: 403 })
    }

    // Parse request body
    const body = await request.json()
    const { confirm_container_number, confirm_seal_number, confirmation_notes } = body

    if (typeof confirm_container_number !== 'boolean' || typeof confirm_seal_number !== 'boolean') {
      return NextResponse.json({ 
        error: 'Bad Request', 
        details: 'confirm_container_number and confirm_seal_number must be boolean values' 
      }, { status: 400 })
    }

    // Get current container data
    const { data: container, error: containerError } = await supabaseAdmin
      .from('containers')
      .select('*')
      .eq('id', containerId)
      .single()

    if (containerError) {
      console.error('Container confirmation API: Container fetch error:', containerError)
      return NextResponse.json({ 
        error: 'Container not found', 
        details: containerError.message 
      }, { status: 404 })
    }

    // Build update object
    const updates: any = {}
    const confirmationTime = new Date().toISOString()

    if (confirm_container_number) {
      if (!container.container_number) {
        return NextResponse.json({ 
          error: 'Bad Request', 
          details: 'Cannot confirm empty container number' 
        }, { status: 400 })
      }
      updates.container_number_confirmed = true
      updates.container_number_confirmed_by = user.id
      updates.container_number_confirmed_at = confirmationTime
    }

    if (confirm_seal_number) {
      if (!container.seal_number) {
        return NextResponse.json({ 
          error: 'Bad Request', 
          details: 'Cannot confirm empty seal number' 
        }, { status: 400 })
      }
      updates.seal_number_confirmed = true
      updates.seal_number_confirmed_by = user.id
      updates.seal_number_confirmed_at = confirmationTime
    }

    if (Object.keys(updates).length === 0) {
      return NextResponse.json({ 
        error: 'Bad Request', 
        details: 'At least one number must be confirmed' 
      }, { status: 400 })
    }

    // Update container with confirmations using transaction
    const { data: updatedContainer, error: updateError } = await supabaseAdmin
      .from('containers')
      .update(updates)
      .eq('id', containerId)
      .select()
      .single()

    if (updateError) {
      console.error('Container confirmation API: Update error:', updateError)
      return NextResponse.json({ 
        error: 'Update failed', 
        details: updateError.message 
      }, { status: 500 })
    }

    // Log confirmation action with notes if provided
    if (confirmation_notes) {
      const logMessage = `Confirmation notes: ${confirmation_notes}`
      await supabaseAdmin
        .from('status_history')
        .insert({
          shipment_id: container.shipment_id,
          status_from: null,
          status_to: null,
          notes: logMessage,
          updated_by: user.id,
          action_type: 'confirmation_notes',
          created_at: confirmationTime
        })
    }

    console.log(`Container confirmation API: Successfully confirmed for user ${user.id}:`, {
      containerId,
      container_number_confirmed: confirm_container_number,
      seal_number_confirmed: confirm_seal_number,
      confirmedBy: `${profile.first_name} ${profile.last_name}`
    })

    return NextResponse.json({
      success: true,
      container: updatedContainer,
      confirmed_by: {
        user_id: user.id,
        name: `${profile.first_name} ${profile.last_name}`,
        role: profile.role
      },
      confirmed_at: confirmationTime
    })

  } catch (error) {
    console.error('Container confirmation API Error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}