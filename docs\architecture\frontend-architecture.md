# Frontend Architecture

## Component Architecture

### Component Organization

```text
src/
├── app/                          # Next.js App Router pages
│   ├── (auth)/                   # Auth route group
│   │   ├── login/               
│   │   └── register/            
│   ├── (dashboard)/              # Protected dashboard routes
│   │   ├── shipments/
│   │   │   ├── page.tsx         # Shipment list
│   │   │   ├── create/page.tsx  # Intelligent shipment creation
│   │   │   └── [id]/page.tsx    # Shipment details
│   │   ├── master-data/
│   │   │   ├── companies/
│   │   │   ├── products/
│   │   │   ├── drivers/
│   │   │   └── relationships/
│   │   └── documents/
│   ├── (mobile)/                 # Mobile-optimized routes
│   │   ├── driver/
│   │   │   ├── dashboard/
│   │   │   ├── shipments/
│   │   │   └── status-update/
│   │   └── customer/
│   ├── api/                      # API routes (minimal - mostly Supabase direct)
│   ├── layout.tsx               # Root layout with theme provider
│   └── page.tsx                 # Landing page
├── components/                   # Reusable UI components
│   ├── ui/                      # ShadCN UI components
│   ├── forms/                   # Domain-specific forms
│   │   ├── shipment-form/
│   │   ├── company-form/
│   │   └── driver-form/
│   ├── layout/                  # Layout components
│   ├── data-display/            # Data presentation components
│   └── mobile/                  # Mobile-specific components
├── hooks/                       # Custom React hooks
│   ├── use-supabase.ts         # Supabase client hook
│   ├── use-auth.ts             # Authentication state
│   ├── use-shipments.ts        # Shipment operations
│   ├── use-relationships.ts    # Relationship intelligence
│   └── use-real-time.ts        # Real-time subscriptions
├── lib/                        # Utility functions
│   ├── supabase/
│   │   ├── client.ts           # Supabase client configuration
│   │   ├── auth.ts             # Auth helpers
│   │   └── types.ts            # Generated types
│   └── utils.ts                # General utilities
├── stores/                     # Zustand state stores
│   ├── auth-store.ts           # Authentication state
│   ├── shipment-store.ts       # Shipment management
│   └── notification-store.ts   # Notifications
└── types/                      # TypeScript definitions
    ├── database.ts             # Supabase generated types
    └── shipment.ts             # Business entity types
```

## State Management Architecture

### State Structure

```typescript
// Zustand store for shipment management
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { Shipment, ShipmentStatus } from '@/types/database'
import { supabase } from '@/lib/supabase/client'

interface ShipmentState {
  // Data
  shipments: Shipment[]
  currentShipment: Shipment | null
  filters: {
    status?: ShipmentStatus
    customer_id?: string
    transportation_mode?: string
    date_range?: { start: string; end: string }
  }
  
  // UI state
  isLoading: boolean
  error: string | null
  
  // Real-time subscriptions
  subscriptions: Map<string, any>
  
  // Actions
  fetchShipments: (filters?: any) => Promise<void>
  createShipment: (data: any) => Promise<Shipment>
  updateShipmentStatus: (id: string, status: ShipmentStatus) => Promise<void>
  subscribeToShipment: (shipmentId: string) => () => void
}
```

### State Management Patterns

- **Zustand for Global State**: Lightweight state management with TypeScript support
- **React Query Alternative**: Supabase client handles caching and synchronization
- **Real-time Integration**: Direct Supabase subscription management within stores
- **Optimistic Updates**: Immediate UI updates with rollback on failure

## Routing Architecture

### Protected Route Pattern

```typescript
// middleware.ts - Route protection
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextRequest, NextResponse } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Protect dashboard routes
  if (req.nextUrl.pathname.startsWith('/dashboard') || 
      req.nextUrl.pathname.startsWith('/mobile')) {
    if (!session) {
      return NextResponse.redirect(new URL('/login', req.url))
    }

    // Get user profile for role-based access
    const { data: profile } = await supabase
      .from('profiles')
      .select('role, company_id')
      .eq('user_id', session.user.id)
      .single()

    if (!profile) {
      return NextResponse.redirect(new URL('/login', req.url))
    }

    // Role-based route protection
    if (req.nextUrl.pathname.startsWith('/mobile/driver') && 
        profile.role !== 'driver') {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }
  }

  return res
}
```

## Frontend Services Layer

### API Client Setup

```typescript
// lib/supabase/client.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Database } from '@/types/database'

export const supabase = createClientComponentClient<Database>()

// Enhanced client with error handling
export class SupabaseService {
  private client = supabase

  async withErrorHandling<T>(operation: () => Promise<{ data: T | null; error: any }>) {
    try {
      const result = await operation()
      if (result.error) {
        throw new Error(result.error.message)
      }
      return result.data
    } catch (error) {
      console.error('Supabase operation failed:', error)
      throw error
    }
  }

  // Real-time subscription helper
  subscribeToTable<T>(
    table: string,
    filter?: string,
    callback: (payload: any) => void
  ) {
    return this.client
      .channel(`${table}-changes`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table,
        filter
      }, callback)
      .subscribe()
  }
}
```
