export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string
          name: string
          code: string | null
          description: string | null
          category: string | null
          hs_code: string | null
          unit_of_measure_id: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          code?: string | null
          description?: string | null
          category?: string | null
          hs_code?: string | null
          unit_of_measure_id?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          code?: string | null
          description?: string | null
          category?: string | null
          hs_code?: string | null
          unit_of_measure_id?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'products_unit_of_measure_id_fkey'
            columns: ['unit_of_measure_id']
            isOneToOne: false
            referencedRelation: 'units_of_measure'
            referencedColumns: ['id']
          },
        ]
      }
      units_of_measure: {
        Row: {
          id: string
          code: string
          name: string
          category: string | null
          symbol: string | null
          conversion_factor: number | null
          base_unit_id: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          code: string
          name: string
          category?: string | null
          symbol?: string | null
          conversion_factor?: number | null
          base_unit_id?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          code?: string
          name?: string
          category?: string | null
          symbol?: string | null
          conversion_factor?: number | null
          base_unit_id?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'units_of_measure_base_unit_id_fkey'
            columns: ['base_unit_id']
            isOneToOne: false
            referencedRelation: 'units_of_measure'
            referencedColumns: ['id']
          },
        ]
      }
      shipments: {
        Row: {
          id: string
          shipment_number: string
          invoice_number: string | null
          customer_id: string | null
          shipper_id: string | null
          consignee_id: string | null
          notify_party_id: string | null
          factory_id: string | null
          forwarder_agent_id: string | null
          origin_port_id: string | null
          destination_port_id: string | null
          liner: string | null
          vessel_name: string | null
          voyage_number: string | null
          booking_number: string | null
          etd_date: string | null
          eta_date: string | null
          closing_time: string | null
          cy_date: string | null
          number_of_pallet: number | null
          pallet_description: string | null
          ephyto_refno: string | null
          currency_code: string | null
          total_weight: number | null
          total_volume: number | null
          status: string | null
          transportation_mode: string | null
          notes: string | null
          metadata: any | null
          created_by: string | null
          created_at: string | null
          updated_at: string | null
          total_value_cif: number | null
          total_value_fob: number | null
          total_quantity: number | null
          total_gross_weight: number | null
          total_net_weight: number | null
        }
        Insert: {
          id?: string
          shipment_number: string
          invoice_number?: string | null
          customer_id?: string | null
          shipper_id?: string | null
          consignee_id?: string | null
          notify_party_id?: string | null
          factory_id?: string | null
          forwarder_agent_id?: string | null
          origin_port_id?: string | null
          destination_port_id?: string | null
          liner?: string | null
          vessel_name?: string | null
          voyage_number?: string | null
          booking_number?: string | null
          etd_date?: string | null
          eta_date?: string | null
          closing_time?: string | null
          cy_date?: string | null
          number_of_pallet?: number | null
          pallet_description?: string | null
          ephyto_refno?: string | null
          currency_code?: string | null
          total_weight?: number | null
          total_volume?: number | null
          status?: string | null
          transportation_mode?: string | null
          notes?: string | null
          metadata?: any | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          total_value_cif?: number | null
          total_value_fob?: number | null
          total_quantity?: number | null
          total_gross_weight?: number | null
          total_net_weight?: number | null
        }
        Update: {
          id?: string
          shipment_number?: string
          invoice_number?: string | null
          customer_id?: string | null
          shipper_id?: string | null
          consignee_id?: string | null
          notify_party_id?: string | null
          factory_id?: string | null
          forwarder_agent_id?: string | null
          origin_port_id?: string | null
          destination_port_id?: string | null
          liner?: string | null
          vessel_name?: string | null
          voyage_number?: string | null
          booking_number?: string | null
          etd_date?: string | null
          eta_date?: string | null
          closing_time?: string | null
          cy_date?: string | null
          number_of_pallet?: number | null
          pallet_description?: string | null
          ephyto_refno?: string | null
          currency_code?: string | null
          total_weight?: number | null
          total_volume?: number | null
          status?: string | null
          transportation_mode?: string | null
          notes?: string | null
          metadata?: any | null
          created_by?: string | null
          created_at?: string | null
          updated_at?: string | null
          total_value_cif?: number | null
          total_value_fob?: number | null
          total_quantity?: number | null
          total_gross_weight?: number | null
          total_net_weight?: number | null
        }
        Relationships: []
      }
      companies: {
        Row: {
          id: string
          name: string
          company_type: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          company_type: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          company_type?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      ports: {
        Row: {
          id: string
          name: string
          code: string
          country: string
          port_type: string
          is_active: boolean | null
          gps_coordinates: unknown | null
          city: string | null
          timezone: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          code: string
          country: string
          port_type: string
          is_active?: boolean | null
          gps_coordinates?: unknown | null
          city?: string | null
          timezone?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          code?: string
          country?: string
          port_type?: string
          is_active?: boolean | null
          gps_coordinates?: unknown | null
          city?: string | null
          timezone?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      containers: {
        Row: {
          id: string
          shipment_id: string | null
          container_number: string | null
          container_type: string | null
          container_size: string | null
          seal_number: string | null
          tare_weight: number | null
          gross_weight: number | null
          volume: number | null
          temperature: string | null
          vent: string | null
          status: string | null
          created_at: string | null
          update_at: string | null
        }
        Insert: {
          id?: string
          shipment_id?: string | null
          container_number?: string | null
          container_type?: string | null
          container_size?: string | null
          seal_number?: string | null
          tare_weight?: number | null
          gross_weight?: number | null
          volume?: number | null
          temperature?: string | null
          vent?: string | null
          status?: string | null
          created_at?: string | null
          update_at?: string | null
        }
        Update: {
          id?: string
          shipment_id?: string | null
          container_number?: string | null
          container_type?: string | null
          container_size?: string | null
          seal_number?: string | null
          tare_weight?: number | null
          gross_weight?: number | null
          volume?: number | null
          temperature?: string | null
          vent?: string | null
          status?: string | null
          created_at?: string | null
          update_at?: string | null
        }
        Relationships: []
      }
      shipment_products: {
        Row: {
          id: string
          shipment_id: string | null
          container_id: string | null
          product_id: string | null
          product_description: string | null
          quantity: number
          unit_of_measure_id: string | null
          unit_price_cif: number
          unit_price_fob: number
          total_value_cif: number
          total_value_fob: number
          gross_weight: number
          net_weight: number
          shipping_mark: string | null
          mfg_date: string | null
          expire_date: string | null
          lot_number: string | null
          packaging_type: string
          quality_grade: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          shipment_id?: string | null
          container_id?: string | null
          product_id?: string | null
          product_description?: string | null
          quantity: number
          unit_of_measure_id?: string | null
          unit_price_cif: number
          unit_price_fob: number
          total_value_cif: number
          total_value_fob: number
          gross_weight?: number
          net_weight?: number
          shipping_mark?: string | null
          mfg_date?: string | null
          expire_date?: string | null
          lot_number?: string | null
          packaging_type: string
          quality_grade?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          shipment_id?: string | null
          container_id?: string | null
          product_id?: string | null
          product_description?: string | null
          quantity?: number
          unit_of_measure_id?: string | null
          unit_price_cif?: number
          unit_price_fob?: number
          total_value_cif?: number
          total_value_fob?: number
          gross_weight?: number
          net_weight?: number
          shipping_mark?: string | null
          mfg_date?: string | null
          expire_date?: string | null
          lot_number?: string | null
          packaging_type?: string
          quality_grade?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      unit_category: 'weight' | 'count' | 'volume' | 'length'
      shipment_status_enum: 'booking_confirmed' | 'transport_assigned' | 'driver_assigned' | 'empty_container_picked' | 'arrived_at_factory' | 'loading_started' | 'departed_factory' | 'container_returned' | 'shipped' | 'arrived' | 'completed' | 'cancelled'
      transport_mode_enum: 'sea' | 'air' | 'road' | 'rail'
      currency_enum: 'USD' | 'EUR' | 'GBP' | 'THB' | 'JPY'
      container_type_enum: 'dry' | 'refrigerated' | 'open_top' | 'flat_rack' | 'tank'
      container_size_enum: '20ft' | '40ft' | '40ft_hc' | '45ft'
      container_status_enum: 'empty' | 'loaded' | 'in_transit' | 'delivered'
      packaging_type_enum: 'box' | 'pallet' | 'bag' | 'drum' | 'bulk'
      port_type_enum: 'origin' | 'destination' | 'transit'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Convenient type aliases
export type Product = Database['public']['Tables']['products']['Row']
export type ProductInsert = Database['public']['Tables']['products']['Insert']
export type ProductUpdate = Database['public']['Tables']['products']['Update']

export type UnitOfMeasure =
  Database['public']['Tables']['units_of_measure']['Row']
export type UnitOfMeasureInsert =
  Database['public']['Tables']['units_of_measure']['Insert']
export type UnitOfMeasureUpdate =
  Database['public']['Tables']['units_of_measure']['Update']

export type Shipment = Database['public']['Tables']['shipments']['Row']
export type ShipmentInsert = Database['public']['Tables']['shipments']['Insert']
export type ShipmentUpdate = Database['public']['Tables']['shipments']['Update']

export type Company = Database['public']['Tables']['companies']['Row']
export type CompanyInsert = Database['public']['Tables']['companies']['Insert']
export type CompanyUpdate = Database['public']['Tables']['companies']['Update']

export type Port = Database['public']['Tables']['ports']['Row']
export type PortInsert = Database['public']['Tables']['ports']['Insert']
export type PortUpdate = Database['public']['Tables']['ports']['Update']

export type Container = Database['public']['Tables']['containers']['Row']
export type ContainerInsert = Database['public']['Tables']['containers']['Insert']
export type ContainerUpdate = Database['public']['Tables']['containers']['Update']

export type ShipmentProduct = Database['public']['Tables']['shipment_products']['Row']
export type ShipmentProductInsert = Database['public']['Tables']['shipment_products']['Insert']
export type ShipmentProductUpdate = Database['public']['Tables']['shipment_products']['Update']

// Extended types with relationships
export type ProductWithUnit = Product & {
  unit_of_measure: UnitOfMeasure | null
}

export type UnitOfMeasureWithBase = UnitOfMeasure & {
  base_unit: UnitOfMeasure | null
}

export type ShipmentProductWithRelations = ShipmentProduct & {
  product: Product | null
  unit_of_measure: UnitOfMeasure | null
}

export type ShipmentWithRelations = Shipment & {
  customer: Company | null
  shipper: Company | null
  consignee: Company | null
  notify_party: Company | null
  factory: Company | null
  forwarder_agent: Company | null
  origin_port: Port | null
  destination_port: Port | null
  containers: Container[]
  shipment_products: ShipmentProductWithRelations[]
}

// Enums
export type UnitCategory = Database['public']['Enums']['unit_category']
export type ShipmentStatus = Database['public']['Enums']['shipment_status_enum']
export type TransportMode = Database['public']['Enums']['transport_mode_enum']
export type CurrencyCode = Database['public']['Enums']['currency_enum']
export type ContainerType = Database['public']['Enums']['container_type_enum']
export type ContainerSize = Database['public']['Enums']['container_size_enum']
export type ContainerStatus = Database['public']['Enums']['container_status_enum']
export type PackagingType = Database['public']['Enums']['packaging_type_enum']
export type PortType = Database['public']['Enums']['port_type_enum']
