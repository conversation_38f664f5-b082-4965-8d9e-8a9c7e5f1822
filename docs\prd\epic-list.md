# Epic List

**Epic 1: Foundation & Authentication Infrastructure**  
Establish Next.js/Supabase project setup, authentication system, and basic user management with role-based access control.

**Epic 2: Master Data Management System**  
Create comprehensive CRUD operations for all master data entities including products, ports, companies (customers, factories, shippers, consignees, notify parties, forwarder agents, carriers), drivers, and relationship management with intelligent pre-population capabilities.

**Epic 3: Core Shipment Management**  
Implement complete shipment lifecycle management from creation through delivery with status tracking and stakeholder coordination.

**Epic 4: Mobile Driver Interface & Status Updates**  
Build mobile-optimized PWA for drivers with status updates, photo uploads, and offline capabilities.

**Epic 5: Document Generation & Management**  
Develop automated document generation system for export documents with centralized storage and distribution.

**Epic 6: Notification & Communication System**  
Implement multi-channel notification system supporting email, SMS, in-app, Line, and WeChat communications.
