/**
 * Document Generation Service Tests
 * Story 5.2: Automated Document Generation Engine
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { DocumentGenerationService } from '../document-generation-service'
import { PDFGenerationService } from '../pdf-generation-service'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/supabase/types'
import type { 
  DocumentGenerationRequest,
  ShipmentDataForGeneration 
} from '@/types/document-generation'
import type { DocumentTemplate } from '@/types/document-template'

// Mock the PDF generation service
vi.mock('../pdf-generation-service')

// Mock data
const mockShipmentData: ShipmentDataForGeneration = {
  shipment_number: 'EXSEA-BKK-240101-001',
  invoice_number: 'INV-2024-001',
  status: 'booking_confirmed',
  transportation_mode: 'sea',
  liner: 'Evergreen Line',
  vessel_name: 'Ever Given',
  voyage_number: 'V001',
  booking_number: 'BKG-001',
  etd_date: '2024-01-15T10:00:00Z',
  eta_date: '2024-02-01T14:00:00Z',
  closing_time: '2024-01-14T16:00:00Z',
  origin_port: {
    id: 'port-1',
    port_name: 'Bangkok Port',
    port_code: 'BKK',
    country: 'Thailand'
  },
  destination_port: {
    id: 'port-2',
    port_name: 'Los Angeles Port',
    port_code: 'LAX',
    country: 'United States'
  },
  customer: {
    id: 'customer-1',
    name: 'ABC Trading Co., Ltd.',
    type: 'customer',
    code: 'ABC001',
    address: '123 Business Street',
    city: 'Bangkok',
    country: 'Thailand',
    phone: '+66-2-123-4567',
    email: '<EMAIL>'
  },
  shipper: {
    id: 'shipper-1',
    name: 'XYZ Exports Ltd.',
    type: 'shipper',
    address: '456 Export Ave',
    city: 'Bangkok',
    country: 'Thailand'
  },
  consignee: {
    id: 'consignee-1',
    name: 'Import Partners LLC',
    type: 'consignee',
    address: '789 Import Blvd',
    city: 'Los Angeles',
    country: 'United States'
  },
  products: [
    {
      id: 'product-1',
      shipment_id: 'shipment-1',
      product_id: 'prod-1',
      product_description: 'Fresh Mangoes',
      quantity: 1000,
      unit_of_measure: {
        id: 'unit-1',
        name: 'Kilogram',
        abbreviation: 'kg'
      },
      unit_price_cif: 2.50,
      unit_price_fob: 2.20,
      total_value_cif: 2500.00,
      total_value_fob: 2200.00,
      gross_weight: 1200,
      net_weight: 1000,
      shipping_mark: 'Fresh Mangoes - Grade A',
      packaging_type: 'carton',
      quality_grade: 'A'
    }
  ],
  containers: [
    {
      id: 'container-1',
      shipment_id: 'shipment-1',
      container_number: 'ABCD1234567',
      container_type: 'dry',
      container_size: '20ft',
      seal_number: 'SEAL123',
      tare_weight: 2300,
      gross_weight: 22000,
      volume: 33.2,
      status: 'loaded'
    }
  ],
  total_weight: 22000,
  total_volume: 33.2,
  total_value_cif: 2500.00,
  total_value_fob: 2200.00,
  currency_code: 'USD',
  number_of_pallet: 40,
  pallet_description: 'Standard wooden pallets',
  notes: 'Handle with care - perishable goods',
  created_at: '2024-01-01T08:00:00Z',
  updated_at: '2024-01-01T08:00:00Z'
}

const mockTemplate: DocumentTemplate = {
  id: 'template-1',
  template_name: 'Booking Confirmation Template',
  document_type: 'booking_confirmation',
  version: '1.0',
  template_content: `
    <h1>Booking Confirmation</h1>
    <p>Shipment Number: {{shipment.shipment_number}}</p>
    <p>Customer: {{shipment.customer.name}}</p>
    <p>Vessel: {{shipment.vessel_name}}</p>
    <p>ETD: {{shipment.etd_date}}</p>
    <p>ETA: {{shipment.eta_date}}</p>
  `,
  template_data: {},
  template_styles: 'body { font-family: Arial, sans-serif; }',
  page_size: 'A4',
  page_orientation: 'portrait',
  margin_top: 20,
  margin_bottom: 20,
  margin_left: 20,
  margin_right: 20,
  language: 'en',
  currency_format: 'USD',
  date_format: 'YYYY-MM-DD',
  number_format: 'en-US',
  description: 'Standard booking confirmation template',
  usage_notes: null,
  required_fields: ['shipment_number', 'customer.name'],
  is_active: true,
  is_default: true,
  created_by: 'user-1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

const mockGeneratedDocument = {
  id: 'doc-1',
  shipment_id: 'shipment-1',
  document_type: 'booking_confirmation' as const,
  document_name: 'Booking Confirmation Template',
  file_path: 'documents/EXSEA-BKK-240101-001/booking_confirmation/booking_confirmation-EXSEA-BKK-240101-001-v1.pdf',
  file_name: 'booking_confirmation-EXSEA-BKK-240101-001-v1.pdf',
  file_size_bytes: 12345,
  file_type: 'application/pdf',
  version: 1,
  is_original: true,
  language: 'en',
  is_public: false,
  access_level: 'shipment' as const,
  shared_with_customer: false,
  shared_with_carrier: false,
  is_verified: false,
  uploaded_by: 'user-1',
  upload_source: 'automated_generation',
  created_at: '2024-01-01T08:00:00Z',
  updated_at: '2024-01-01T08:00:00Z'
}

describe('DocumentGenerationService', () => {
  let service: DocumentGenerationService
  let mockSupabase: any

  beforeEach(() => {
    // Create mock Supabase client
    mockSupabase = {
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn()
          }))
        })),
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn()
          }))
        }))
      })),
      storage: {
        from: vi.fn(() => ({
          upload: vi.fn(),
          getPublicUrl: vi.fn(() => ({
            data: { publicUrl: 'https://example.com/file.pdf' }
          }))
        }))
      },
      auth: {
        getUser: vi.fn(() => ({
          data: { user: { id: 'user-1' } }
        }))
      }
    }

    service = new DocumentGenerationService(mockSupabase as SupabaseClient<Database>)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('generateDocument', () => {
    it('should successfully generate a document', async () => {
      // Setup mocks
      const mockShipmentQuery = mockSupabase.from('shipments').select().eq().single
      mockShipmentQuery.mockResolvedValue({ data: { ...mockShipmentData, id: 'shipment-1' }, error: null })

      const mockTemplateQuery = mockSupabase.from('document_templates').select().eq().single
      mockTemplateQuery.mockResolvedValue({ data: mockTemplate, error: null })

      const mockUpload = mockSupabase.storage.from().upload
      mockUpload.mockResolvedValue({ data: { path: 'documents/test.pdf' }, error: null })

      const mockInsert = mockSupabase.from('documents').insert().select().single
      mockInsert.mockResolvedValue({ data: mockGeneratedDocument, error: null })

      // Mock PDF generation
      const mockPDFService = vi.mocked(PDFGenerationService)
      const mockPDFInstance = {
        validateRequirements: vi.fn(() => ({ isValid: true, errors: [] })),
        generatePDF: vi.fn(() => Promise.resolve(Buffer.from('mock pdf content')))
      }
      mockPDFService.mockImplementation(() => mockPDFInstance as any)

      // Test request
      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-1',
        templateId: 'template-1',
        options: {
          documentNumber: 'DOC-001',
          additionalData: { notes: 'Test generation' }
        }
      }

      // Execute
      const result = await service.generateDocument(request)

      // Assert
      expect(result.success).toBe(true)
      expect(result.document).toBeDefined()
      expect(result.templateId).toBe('template-1')
      expect(result.processingTime).toBeGreaterThan(0)

      // Verify service calls
      expect(mockShipmentQuery).toHaveBeenCalledWith('shipment-1')
      expect(mockTemplateQuery).toHaveBeenCalledWith('template-1')
      expect(mockPDFInstance.validateRequirements).toHaveBeenCalledWith(mockTemplate)
      expect(mockPDFInstance.generatePDF).toHaveBeenCalled()
      expect(mockUpload).toHaveBeenCalled()
      expect(mockInsert).toHaveBeenCalled()
    })

    it('should handle shipment not found error', async () => {
      // Setup mock to return no shipment
      const mockShipmentQuery = mockSupabase.from('shipments').select().eq().single
      mockShipmentQuery.mockResolvedValue({ data: null, error: { message: 'Not found' } })

      const request: DocumentGenerationRequest = {
        shipmentId: 'invalid-id',
        templateId: 'template-1'
      }

      const result = await service.generateDocument(request)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Shipment not found')
      expect(result.templateId).toBe('template-1')
    })

    it('should handle template not found error', async () => {
      // Setup shipment to exist but template not found
      const mockShipmentQuery = mockSupabase.from('shipments').select().eq().single
      mockShipmentQuery.mockResolvedValue({ data: { ...mockShipmentData, id: 'shipment-1' }, error: null })

      const mockTemplateQuery = mockSupabase.from('document_templates').select().eq().single
      mockTemplateQuery.mockResolvedValue({ data: null, error: { message: 'Not found' } })

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-1',
        templateId: 'invalid-template-id'
      }

      const result = await service.generateDocument(request)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Template not found')
    })

    it('should handle PDF generation validation errors', async () => {
      // Setup mocks
      const mockShipmentQuery = mockSupabase.from('shipments').select().eq().single
      mockShipmentQuery.mockResolvedValue({ data: { ...mockShipmentData, id: 'shipment-1' }, error: null })

      const mockTemplateQuery = mockSupabase.from('document_templates').select().eq().single
      mockTemplateQuery.mockResolvedValue({ data: mockTemplate, error: null })

      // Mock PDF validation failure
      const mockPDFService = vi.mocked(PDFGenerationService)
      const mockPDFInstance = {
        validateRequirements: vi.fn(() => ({ 
          isValid: false, 
          errors: ['Template content is required', 'Page size is invalid'] 
        })),
        generatePDF: vi.fn()
      }
      mockPDFService.mockImplementation(() => mockPDFInstance as any)

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-1',
        templateId: 'template-1'
      }

      const result = await service.generateDocument(request)

      expect(result.success).toBe(false)
      expect(result.error).toContain('PDF generation validation failed')
      expect(result.error).toContain('Template content is required')
      expect(mockPDFInstance.generatePDF).not.toHaveBeenCalled()
    })

    it('should handle storage upload errors', async () => {
      // Setup mocks
      const mockShipmentQuery = mockSupabase.from('shipments').select().eq().single
      mockShipmentQuery.mockResolvedValue({ data: { ...mockShipmentData, id: 'shipment-1' }, error: null })

      const mockTemplateQuery = mockSupabase.from('document_templates').select().eq().single
      mockTemplateQuery.mockResolvedValue({ data: mockTemplate, error: null })

      // Mock storage upload failure
      const mockUpload = mockSupabase.storage.from().upload
      mockUpload.mockResolvedValue({ data: null, error: { message: 'Storage error' } })

      // Mock PDF generation success
      const mockPDFService = vi.mocked(PDFGenerationService)
      const mockPDFInstance = {
        validateRequirements: vi.fn(() => ({ isValid: true, errors: [] })),
        generatePDF: vi.fn(() => Promise.resolve(Buffer.from('mock pdf content')))
      }
      mockPDFService.mockImplementation(() => mockPDFInstance as any)

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-1',
        templateId: 'template-1'
      }

      const result = await service.generateDocument(request)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Failed to store document')
    })
  })

  describe('bulkGenerateDocuments', () => {
    it('should successfully generate multiple documents', async () => {
      // Setup mocks for successful generation
      const mockShipmentQuery = mockSupabase.from('shipments').select().eq().single
      mockShipmentQuery.mockResolvedValue({ data: { ...mockShipmentData, id: 'shipment-1' }, error: null })

      const mockTemplateQuery = mockSupabase.from('document_templates').select().eq().single
      mockTemplateQuery.mockResolvedValue({ data: mockTemplate, error: null })

      const mockUpload = mockSupabase.storage.from().upload
      mockUpload.mockResolvedValue({ data: { path: 'documents/test.pdf' }, error: null })

      const mockInsert = mockSupabase.from('documents').insert().select().single
      mockInsert.mockResolvedValue({ data: mockGeneratedDocument, error: null })

      // Mock PDF generation
      const mockPDFService = vi.mocked(PDFGenerationService)
      const mockPDFInstance = {
        validateRequirements: vi.fn(() => ({ isValid: true, errors: [] })),
        generatePDF: vi.fn(() => Promise.resolve(Buffer.from('mock pdf content')))
      }
      mockPDFService.mockImplementation(() => mockPDFInstance as any)

      // Mock progress callback
      const mockOnProgress = vi.fn()

      const request = {
        shipmentId: 'shipment-1',
        templateIds: ['template-1', 'template-2'],
        onProgress: mockOnProgress
      }

      const result = await service.bulkGenerateDocuments(request)

      expect(result.summary.total).toBe(2)
      expect(result.summary.successful).toBe(2)
      expect(result.summary.failed).toBe(0)
      expect(result.results).toHaveLength(2)
      expect(mockOnProgress).toHaveBeenCalledWith(50) // After first document
      expect(mockOnProgress).toHaveBeenCalledWith(100) // After second document
    })

    it('should handle mixed success and failure results', async () => {
      // Setup shipment to exist
      const mockShipmentQuery = mockSupabase.from('shipments').select().eq().single
      mockShipmentQuery.mockResolvedValue({ data: { ...mockShipmentData, id: 'shipment-1' }, error: null })

      // Setup first template to succeed, second to fail
      const mockTemplateQuery = mockSupabase.from('document_templates').select().eq().single
      mockTemplateQuery
        .mockResolvedValueOnce({ data: mockTemplate, error: null }) // First call succeeds
        .mockResolvedValueOnce({ data: null, error: { message: 'Not found' } }) // Second call fails

      // Mock other operations for successful case
      const mockUpload = mockSupabase.storage.from().upload
      mockUpload.mockResolvedValue({ data: { path: 'documents/test.pdf' }, error: null })

      const mockInsert = mockSupabase.from('documents').insert().select().single
      mockInsert.mockResolvedValue({ data: mockGeneratedDocument, error: null })

      // Mock PDF generation
      const mockPDFService = vi.mocked(PDFGenerationService)
      const mockPDFInstance = {
        validateRequirements: vi.fn(() => ({ isValid: true, errors: [] })),
        generatePDF: vi.fn(() => Promise.resolve(Buffer.from('mock pdf content')))
      }
      mockPDFService.mockImplementation(() => mockPDFInstance as any)

      const request = {
        shipmentId: 'shipment-1',
        templateIds: ['template-1', 'template-2']
      }

      const result = await service.bulkGenerateDocuments(request)

      expect(result.summary.total).toBe(2)
      expect(result.summary.successful).toBe(1)
      expect(result.summary.failed).toBe(1)
      expect(result.results[0].success).toBe(true)
      expect(result.results[1].success).toBe(false)
      expect(result.results[1].error).toContain('Template not found')
    })
  })

  describe('template placeholder processing', () => {
    it('should correctly populate template placeholders', async () => {
      // This is tested indirectly through the generateDocument tests
      // but we could add more specific tests for the processPlaceholders method
      // if it were public or we exposed it for testing
      
      // For now, we verify through integration that placeholders work
      const mockShipmentQuery = mockSupabase.from('shipments').select().eq().single
      mockShipmentQuery.mockResolvedValue({ data: { ...mockShipmentData, id: 'shipment-1' }, error: null })

      const templateWithPlaceholders: DocumentTemplate = {
        ...mockTemplate,
        template_content: `
          <h1>Shipment: {{shipment.shipment_number}}</h1>
          <p>Customer: {{shipment.customer.name}}</p>
          <p>Total Weight: {{shipment.total_weight}} kg</p>
        `
      }

      const mockTemplateQuery = mockSupabase.from('document_templates').select().eq().single
      mockTemplateQuery.mockResolvedValue({ data: templateWithPlaceholders, error: null })

      const mockUpload = mockSupabase.storage.from().upload
      mockUpload.mockResolvedValue({ data: { path: 'documents/test.pdf' }, error: null })

      const mockInsert = mockSupabase.from('documents').insert().select().single
      mockInsert.mockResolvedValue({ data: mockGeneratedDocument, error: null })

      // Mock PDF generation to capture the processed content
      let processedContent = ''
      const mockPDFService = vi.mocked(PDFGenerationService)
      const mockPDFInstance = {
        validateRequirements: vi.fn(() => ({ isValid: true, errors: [] })),
        generatePDF: vi.fn((content: string) => {
          processedContent = content
          return Promise.resolve(Buffer.from('mock pdf content'))
        })
      }
      mockPDFService.mockImplementation(() => mockPDFInstance as any)

      const request: DocumentGenerationRequest = {
        shipmentId: 'shipment-1',
        templateId: 'template-1'
      }

      const result = await service.generateDocument(request)

      expect(result.success).toBe(true)
      expect(processedContent).toContain('EXSEA-BKK-240101-001') // shipment number
      expect(processedContent).toContain('ABC Trading Co., Ltd.') // customer name  
      expect(processedContent).toContain('22000') // total weight
    })
  })
})