'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { User, Settings, LogOut, Bell, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/hooks/use-auth'
import type { UserProfile } from '@/lib/supabase/auth'
import { createClient } from '@/lib/supabase/client'

interface Company {
  id: string
  name: string
  company_type: string
}

interface UserHeaderProps {
  className?: string
}

// Role display names
const roleDisplayNames: Record<string, string> = {
  admin: 'Administrator',
  cs: 'Customer Service',
  account: 'Account Manager',
  customer: 'Customer',
  carrier: 'Carrier',
  driver: 'Driver',
  factory: 'Factory',
  shipper: 'Shipper',
  consignee: 'Consignee',
  notify_party: 'Notify Party',
  forwarder_agent: 'Forwarder Agent',
}

// Role colors
const roleColors: Record<string, string> = {
  admin: 'bg-red-500',
  cs: 'bg-blue-500',
  account: 'bg-green-500',
  customer: 'bg-purple-500',
  carrier: 'bg-yellow-500',
  driver: 'bg-orange-500',
  factory: 'bg-teal-500',
  shipper: 'bg-indigo-500',
  consignee: 'bg-pink-500',
  notify_party: 'bg-cyan-500',
  forwarder_agent: 'bg-emerald-500',
}

export function UserHeader({ className }: UserHeaderProps) {
  const { profile, loading, signOut } = useAuth()
  const [company, setCompany] = useState<Company | null>(null)
  const [unreadNotifications, setUnreadNotifications] = useState(0)

  useEffect(() => {
    async function loadCompanyData() {
      if (profile?.company_id) {
        try {
          const supabase = createClient()
          const { data: companyData } = await supabase
            .from('companies')
            .select('id, name, company_type')
            .eq('id', profile.company_id)
            .single()

          if (companyData) {
            setCompany(companyData)
          }
        } catch (error) {
          console.error('Error loading company data:', error)
        }
      }

      // TODO: Load unread notifications count
      // This would typically come from a notifications table or API
      setUnreadNotifications(3) // Mock data
    }

    if (profile) {
      loadCompanyData()
    }
  }, [profile])

  async function handleSignOut() {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  if (loading) {
    return (
      <div className={cn('flex items-center space-x-4', className)}>
        <div className="animate-pulse flex items-center space-x-3">
          <div className="h-8 w-8 bg-slate-700 rounded-full"></div>
          <div className="space-y-1">
            <div className="h-4 w-24 bg-slate-700 rounded"></div>
            <div className="h-3 w-16 bg-slate-700 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return null
  }

  return (
    <div className={cn('flex items-center space-x-4', className)}>
      {/* Notifications */}
      <Button
        variant="ghost"
        size="icon"
        className="relative text-white hover:bg-white/10"
      >
        <Bell className="h-5 w-5" />
        {unreadNotifications > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
          >
            {unreadNotifications > 9 ? '9+' : unreadNotifications}
          </Badge>
        )}
      </Button>

      {/* User Menu */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex items-center space-x-3 text-white hover:bg-white/10 h-auto py-2 px-3"
          >
            {/* Avatar */}
            <div className="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>

            {/* User Info */}
            <div className="text-left">
              <p className="text-sm font-medium">
                {profile.first_name} {profile.last_name}
              </p>
              <div className="flex items-center space-x-2">
                <Badge
                  variant="secondary"
                  className={cn(
                    'text-white text-xs',
                    roleColors[profile.role] || 'bg-gray-500'
                  )}
                >
                  {roleDisplayNames[profile.role] || profile.role}
                </Badge>
                {company && (
                  <span className="text-xs text-slate-400">{company.name}</span>
                )}
              </div>
            </div>

            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel>
            <div className="space-y-1">
              <p className="font-medium">
                {profile.first_name} {profile.last_name}
              </p>
              <p className="text-sm text-muted-foreground">{profile.email}</p>
              <div className="flex items-center space-x-2 mt-2">
                <Badge
                  variant="secondary"
                  className={cn(
                    'text-white text-xs',
                    roleColors[profile.role] || 'bg-gray-500'
                  )}
                >
                  {roleDisplayNames[profile.role] || profile.role}
                </Badge>
                {company && (
                  <span className="text-xs text-muted-foreground">
                    {company.name}
                  </span>
                )}
              </div>
            </div>
          </DropdownMenuLabel>

          <DropdownMenuSeparator />

          <DropdownMenuItem asChild>
            <Link
              href="/dashboard/profile"
              className="flex items-center cursor-pointer"
            >
              <User className="mr-2 h-4 w-4" />
              Profile
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link
              href="/dashboard/settings"
              className="flex items-center cursor-pointer"
            >
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link
              href="/dashboard/notifications"
              className="flex items-center cursor-pointer"
            >
              <Bell className="mr-2 h-4 w-4" />
              Notifications
              {unreadNotifications > 0 && (
                <Badge variant="destructive" className="ml-auto text-xs">
                  {unreadNotifications}
                </Badge>
              )}
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={handleSignOut}
            className="flex items-center cursor-pointer text-red-600 focus:text-red-600"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
