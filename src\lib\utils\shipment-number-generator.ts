import { createClient } from '@/lib/supabase/client'
import type { TransportMode } from '@/lib/validations/shipment'

/**
 * Transport mode codes for shipment number generation
 */
export const TRANSPORT_MODE_CODES = {
  sea: 'SEA',
  land: 'LND',
  rail: 'RAL',
} as const

/**
 * Interface for shipment number generation parameters
 */
export interface ShipmentNumberParams {
  transportMode: TransportMode
  portCode: string
  date?: Date
}

/**
 * Interface for shipment number components
 */
export interface ShipmentNumberComponents {
  prefix: string
  modeCode: string
  portCode: string
  dateCode: string
  runningNumber: number
  fullNumber: string
}

/**
 * Interface for running number tracking
 */
export interface RunningNumberRecord {
  transport_mode: TransportMode
  port_code: string
  year_month: string
  current_number: number
  last_updated: string
}

/**
 * Generate shipment number with format: EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running]
 *
 * @param params - Parameters for shipment number generation
 * @returns Promise<ShipmentNumberComponents> - Generated shipment number components
 *
 * @example
 * ```typescript
 * const components = await generateShipmentNumber({
 *   transportMode: 'sea',
 *   portCode: 'THBKK',
 *   date: new Date('2024-03-15')
 * });
 * // Result: { fullNumber: 'EXSEA-THBKK-240315-001', ... }
 * ```
 */
export async function generateShipmentNumber(
  params: ShipmentNumberParams
): Promise<ShipmentNumberComponents> {
  const { transportMode, portCode, date = new Date() } = params

  // Validate inputs
  if (!transportMode || !portCode) {
    throw new Error(
      'Transport mode and port code are required for shipment number generation'
    )
  }

  if (!Object.keys(TRANSPORT_MODE_CODES).includes(transportMode)) {
    throw new Error(`Invalid transport mode: ${transportMode}`)
  }

  // Get transport mode code
  const modeCode = TRANSPORT_MODE_CODES[transportMode]

  // Format date as YYMMDD
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const dateCode = `${year}${month}${day}`

  // Get year-month for running number tracking
  const yearMonth = `${date.getFullYear()}-${month}`

  // Get next running number
  const runningNumber = await getNextRunningNumber(
    transportMode,
    portCode,
    yearMonth
  )

  // Build components
  const prefix = 'EX'
  const runningNumberStr = runningNumber.toString().padStart(3, '0')
  const fullNumber = `${prefix}${modeCode}-${portCode}-${dateCode}-${runningNumberStr}`

  return {
    prefix,
    modeCode,
    portCode,
    dateCode,
    runningNumber,
    fullNumber,
  }
}

/**
 * Get the next running number for a transport mode/port combination in a given month
 * Running numbers reset monthly per mode/port combination
 *
 * @param transportMode - Transport mode (sea, land, rail)
 * @param portCode - Port code (e.g., THBKK)
 * @param yearMonth - Year-month string (e.g., '2024-03')
 * @returns Promise<number> - Next running number
 */
async function getNextRunningNumber(
  transportMode: TransportMode,
  portCode: string,
  yearMonth: string
): Promise<number> {
  const supabase = createClient()

  try {
    // First, try to get existing running number record
    const { data: existingRecord, error: fetchError } = await supabase
      .from('shipment_running_numbers')
      .select('*')
      .eq('transport_mode', transportMode)
      .eq('port_code', portCode)
      .eq('year_month', yearMonth)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected for new combinations
      throw fetchError
    }

    if (existingRecord) {
      // Update existing record
      const nextNumber = existingRecord.current_number + 1

      const { error: updateError } = await supabase
        .from('shipment_running_numbers')
        .update({
          current_number: nextNumber,
          last_updated: new Date().toISOString(),
        })
        .eq('id', existingRecord.id)

      if (updateError) throw updateError

      return nextNumber
    } else {
      // Create new record starting from 1
      const { error: insertError } = await supabase
        .from('shipment_running_numbers')
        .insert({
          transport_mode: transportMode,
          port_code: portCode,
          year_month: yearMonth,
          current_number: 1,
          last_updated: new Date().toISOString(),
        })

      if (insertError) throw insertError

      return 1
    }
  } catch (error) {
    console.error('Error getting next running number:', error)
    // Fallback: generate based on current timestamp to avoid collisions
    const fallbackNumber = Math.floor(Math.random() * 900) + 100
    console.warn(`Using fallback running number: ${fallbackNumber}`)
    return fallbackNumber
  }
}

/**
 * Validate shipment number format
 *
 * @param shipmentNumber - Shipment number to validate
 * @returns boolean - Whether the format is valid
 */
export function validateShipmentNumberFormat(shipmentNumber: string): boolean {
  if (!shipmentNumber) return false

  // Pattern: EX[SEA|LND|RAL]-[PORT]-YYMMDD-[000]
  const pattern = /^EX(SEA|LND|RAL)-[A-Z]{2,10}-\d{6}-\d{3}$/
  return pattern.test(shipmentNumber)
}

/**
 * Parse shipment number into components
 *
 * @param shipmentNumber - Shipment number to parse
 * @returns ShipmentNumberComponents | null - Parsed components or null if invalid
 */
export function parseShipmentNumber(
  shipmentNumber: string
): ShipmentNumberComponents | null {
  if (!validateShipmentNumberFormat(shipmentNumber)) {
    return null
  }

  const parts = shipmentNumber.split('-')
  if (parts.length !== 4) return null

  const [prefixMode, portCode, dateCode, runningStr] = parts

  if (!prefixMode.startsWith('EX') || prefixMode.length < 5) return null

  const prefix = 'EX'
  const modeCode = prefixMode.substring(2) // Remove 'EX' prefix
  const runningNumber = parseInt(runningStr, 10)

  if (isNaN(runningNumber)) return null

  return {
    prefix,
    modeCode,
    portCode,
    dateCode,
    runningNumber,
    fullNumber: shipmentNumber,
  }
}

/**
 * Get transport mode from mode code
 *
 * @param modeCode - Mode code (SEA, LND, RAL)
 * @returns TransportMode | null - Transport mode or null if invalid
 */
export function getTransportModeFromCode(
  modeCode: string
): TransportMode | null {
  const modeMap: Record<string, TransportMode> = {
    SEA: 'sea',
    LND: 'land',
    RAL: 'rail',
  }

  return modeMap[modeCode] || null
}

/**
 * Extract date from shipment number
 *
 * @param shipmentNumber - Shipment number containing date
 * @returns Date | null - Extracted date or null if invalid
 */
export function extractDateFromShipmentNumber(
  shipmentNumber: string
): Date | null {
  const components = parseShipmentNumber(shipmentNumber)
  if (!components) return null

  const { dateCode } = components
  if (dateCode.length !== 6) return null

  const year = 2000 + parseInt(dateCode.substring(0, 2), 10)
  const month = parseInt(dateCode.substring(2, 4), 10) - 1 // Month is 0-indexed
  const day = parseInt(dateCode.substring(4, 6), 10)

  const date = new Date(year, month, day)

  // Validate the date is reasonable
  if (
    date.getFullYear() !== year ||
    date.getMonth() !== month ||
    date.getDate() !== day
  ) {
    return null
  }

  return date
}

/**
 * Check if a shipment number already exists in the database
 *
 * @param shipmentNumber - Shipment number to check
 * @returns Promise<boolean> - Whether the number exists
 */
export async function checkShipmentNumberExists(
  shipmentNumber: string
): Promise<boolean> {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('shipments')
      .select('id')
      .eq('shipment_number', shipmentNumber)
      .limit(1)

    if (error) throw error

    return data && data.length > 0
  } catch (error) {
    console.error('Error checking shipment number existence:', error)
    return false // Assume it doesn't exist on error to avoid blocking
  }
}

/**
 * Generate unique shipment number using Supabase stored function
 * Uses the generate_shipment_number(p_transport_mode, p_port_code) function in Supabase
 *
 * @param params - Parameters for shipment number generation
 * @returns Promise<ShipmentNumberComponents> - Generated unique shipment number
 */
export async function generateUniqueShipmentNumber(
  params: ShipmentNumberParams
): Promise<ShipmentNumberComponents> {
  const { transportMode, portCode } = params
  const supabase = createClient()

  try {
    console.log('Calling Supabase generate_shipment_number function with:', {
      transportMode,
      portCode,
    })

    // Call the Supabase stored function to generate shipment number
    const { data, error } = await supabase.rpc('generate_shipment_number', {
      p_transport_mode: transportMode,
      p_port_code: portCode,
    })

    if (error) {
      console.error('Error calling generate_shipment_number function:', error)
      throw new Error(`Failed to generate shipment number: ${error.message}`)
    }

    if (!data) {
      throw new Error('Supabase function returned no data')
    }

    console.log('Generated shipment number from Supabase function:', data)

    // Parse the returned shipment number
    const components = parseShipmentNumber(data)
    if (!components) {
      throw new Error(`Invalid shipment number format returned: ${data}`)
    }

    return components
  } catch (error) {
    console.error(
      'Error generating unique shipment number with Supabase function:',
      error
    )

    // Fallback to the original client-side generation if the function fails
    console.warn('Falling back to client-side shipment number generation...')
    return await generateShipmentNumberFallback(params)
  }
}

/**
 * Fallback shipment number generation (original client-side logic)
 */
async function generateShipmentNumberFallback(
  params: ShipmentNumberParams
): Promise<ShipmentNumberComponents> {
  const maxRetries = 3
  let attempts = 0

  while (attempts < maxRetries) {
    try {
      const components = await generateShipmentNumber(params)

      // Check if the generated number already exists
      const exists = await checkShipmentNumberExists(components.fullNumber)

      if (!exists) {
        return components
      }

      // If it exists, increment attempts and try again
      attempts++
      console.warn(
        `Shipment number collision detected: ${components.fullNumber}. Retrying (${attempts}/${maxRetries})...`
      )
    } catch (error) {
      console.error('Error generating unique shipment number:', error)
      throw error
    }
  }

  throw new Error(
    `Failed to generate unique shipment number after ${maxRetries} attempts`
  )
}

/**
 * Get running number statistics for a transport mode/port combination
 *
 * @param transportMode - Transport mode
 * @param portCode - Port code
 * @param yearMonth - Year-month string (optional, defaults to current month)
 * @returns Promise<RunningNumberRecord | null> - Running number record or null if not found
 */
export async function getRunningNumberStats(
  transportMode: TransportMode,
  portCode: string,
  yearMonth?: string
): Promise<RunningNumberRecord | null> {
  const supabase = createClient()

  if (!yearMonth) {
    const now = new Date()
    const year = now.getFullYear()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    yearMonth = `${year}-${month}`
  }

  try {
    const { data, error } = await supabase
      .from('shipment_running_numbers')
      .select('*')
      .eq('transport_mode', transportMode)
      .eq('port_code', portCode)
      .eq('year_month', yearMonth)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw error
    }

    return data || null
  } catch (error) {
    console.error('Error fetching running number stats:', error)
    return null
  }
}

/**
 * Reset running numbers for a new month (maintenance function)
 * This function is for administrative purposes and should be called at month boundaries
 *
 * @param previousYearMonth - Previous month to archive (e.g., '2024-02')
 * @returns Promise<void>
 */
export async function archiveMonthlyRunningNumbers(
  previousYearMonth: string
): Promise<void> {
  const supabase = createClient()

  try {
    // Archive previous month's records by moving them to an archive table
    const { error } = await supabase
      .from('shipment_running_numbers_archive')
      .insert(
        await supabase
          .from('shipment_running_numbers')
          .select('*')
          .eq('year_month', previousYearMonth)
          .then(({ data }) => data || [])
      )

    if (error) throw error

    // Delete archived records from active table
    const { error: deleteError } = await supabase
      .from('shipment_running_numbers')
      .delete()
      .eq('year_month', previousYearMonth)

    if (deleteError) throw deleteError

    console.log(`Archived running numbers for ${previousYearMonth}`)
  } catch (error) {
    console.error('Error archiving monthly running numbers:', error)
    throw error
  }
}

/**
 * Utility function for testing: generate multiple shipment numbers in sequence
 *
 * @param params - Base parameters for generation
 * @param count - Number of shipment numbers to generate
 * @returns Promise<ShipmentNumberComponents[]> - Array of generated numbers
 */
export async function generateShipmentNumberSequence(
  params: ShipmentNumberParams,
  count: number
): Promise<ShipmentNumberComponents[]> {
  const results: ShipmentNumberComponents[] = []

  for (let i = 0; i < count; i++) {
    try {
      const components = await generateShipmentNumber(params)
      results.push(components)
    } catch (error) {
      console.error(`Error generating shipment number ${i + 1}:`, error)
      throw error
    }
  }

  return results
}

// Export types for external use
export type {
  TransportMode,
  ShipmentNumberParams,
  ShipmentNumberComponents,
  RunningNumberRecord,
}
