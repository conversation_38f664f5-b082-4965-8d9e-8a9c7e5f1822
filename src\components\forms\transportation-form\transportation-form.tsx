'use client'

import { useState, useEffect } from 'react'
import * as React from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { SearchableSelect } from '@/components/ui/searchable-select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Loader2,
  Truck,
  User,
  Phone,
  MapPin,
  Calendar,
  Navigation,
  Calculator,
  FileText,
  AlertCircle,
  X,
  Plus,
} from 'lucide-react'
import {
  transportationFormSchema,
  type TransportationForm,
  type Coordinates,
  calculateDistance,
} from '@/lib/validations/transportation'
import { useCarriers, useDriversByCarrier, useCoordinateUtils } from '@/hooks/use-transportation'
import { GPSCoordinateInput } from '@/components/forms/company-form/gps-coordinate-input'
import { format } from 'date-fns'

interface TransportationFormProps {
  shipmentId: string
  initialData?: Partial<TransportationForm>
  onSubmit: (data: TransportationForm & { shipment_id: string }) => Promise<void>
  onCancel?: () => void
  isSubmitting?: boolean
  mode?: 'create' | 'edit'
  className?: string
}

export function TransportationForm({
  shipmentId,
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  mode = 'create',
  className,
}: TransportationFormProps) {
  const [error, setError] = useState<string | null>(null)
  const [showAdvancedGPS, setShowAdvancedGPS] = useState(false)
  const [estimatedDistance, setEstimatedDistance] = useState<number | null>(null)

  const { carriers, isLoading: isLoadingCarriers } = useCarriers()
  const { parseCoordinates, formatCoordinates } = useCoordinateUtils()

  // Ensure assignment_date is a valid ISO string
  const getValidAssignmentDate = (dateValue?: string): string => {
    try {
      if (!dateValue) return new Date().toISOString()
      const date = new Date(dateValue)
      if (isNaN(date.getTime())) return new Date().toISOString()
      return date.toISOString()
    } catch {
      return new Date().toISOString()
    }
  }

  const form = useForm<TransportationForm>({
    resolver: zodResolver(transportationFormSchema),
    defaultValues: {
      carrier_id: initialData?.carrier_id || '',
      driver_id: initialData?.driver_id || '',
      vehicle_head_number: initialData?.vehicle_head_number || '',
      vehicle_tail_number: initialData?.vehicle_tail_number || '',
      driver_phone: initialData?.driver_phone || '',
      assignment_date: getValidAssignmentDate(initialData?.assignment_date),
      pickup_container_location: initialData?.pickup_container_location || '',
      pickup_container_gps_coordinates: initialData?.pickup_container_gps_coordinates,
      pickup_product_location: initialData?.pickup_product_location || '',
      pickup_product_gps_coordinates: initialData?.pickup_product_gps_coordinates,
      delivery_location: initialData?.delivery_location || '',
      delivery_gps_coordinates: initialData?.delivery_gps_coordinates,
      estimated_distance: initialData?.estimated_distance,
      notes: initialData?.notes || '',
    },
  })

  const carrierId = useWatch({
    control: form.control,
    name: 'carrier_id',
  })

  const pickupProductCoords = useWatch({
    control: form.control,
    name: 'pickup_product_gps_coordinates',
  })

  const deliveryCoords = useWatch({
    control: form.control,
    name: 'delivery_gps_coordinates',
  })

  const { drivers, isLoading: isLoadingDrivers } = useDriversByCarrier(carrierId)

  // Prepare driver options for searchable select
  const driverOptions = React.useMemo(() => {
    const options = drivers.map(driver => ({
      value: driver.id,
      label: `${driver.driver_first_name} ${driver.driver_last_name}${driver.driver_code ? ` (${driver.driver_code})` : ''}`,
      searchTerms: [
        driver.driver_first_name.toLowerCase(),
        driver.driver_last_name.toLowerCase(),
        driver.driver_code?.toLowerCase() || '',
        `${driver.driver_first_name} ${driver.driver_last_name}`.toLowerCase(),
        driver.phone || '',
        driver.vehicle_head_number || '',
        driver.vehicle_tail_number || '',
      ].filter(Boolean)
    }))

    // Add "No driver assigned" option at the top
    return [
      { value: '__no_driver__', label: 'No driver assigned', searchTerms: ['no', 'none', 'unassigned'] },
      ...options
    ]
  }, [drivers])

  // Auto-calculate distance when coordinates change
  useEffect(() => {
    if (pickupProductCoords && deliveryCoords) {
      const distance = calculateDistance(
        pickupProductCoords.lat,
        pickupProductCoords.lng,
        deliveryCoords.lat,
        deliveryCoords.lng
      )
      setEstimatedDistance(distance)
      form.setValue('estimated_distance', distance)
    } else {
      setEstimatedDistance(null)
    }
  }, [pickupProductCoords, deliveryCoords, form])

  // Update driver phone and vehicle numbers when driver selection changes
  useEffect(() => {
    const selectedDriverId = form.getValues('driver_id')
    if (selectedDriverId && drivers.length > 0) {
      const selectedDriver = drivers.find(d => d.id === selectedDriverId)
      if (selectedDriver) {
        // Auto-populate phone if available
        if (selectedDriver.phone) {
          form.setValue('driver_phone', selectedDriver.phone)
        }
        // Auto-populate vehicle head number if available
        if (selectedDriver.vehicle_head_number) {
          form.setValue('vehicle_head_number', selectedDriver.vehicle_head_number)
        }
        // Auto-populate vehicle tail number if available
        if (selectedDriver.vehicle_tail_number) {
          form.setValue('vehicle_tail_number', selectedDriver.vehicle_tail_number)
        }
      }
    }
  }, [form.getValues('driver_id'), drivers, form])

  // Handle form submission
  const handleSubmit = async (data: TransportationForm) => {
    try {
      setError(null)
      await onSubmit({ ...data, shipment_id: shipmentId })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save transportation assignment')
    }
  }

  // Handle carrier selection change
  const handleCarrierChange = (value: string) => {
    // Only set valid carrier IDs, ignore placeholder values
    if (value && !value.startsWith('__')) {
      form.setValue('carrier_id', value)
      form.setValue('driver_id', '') // Reset driver when carrier changes
      form.setValue('driver_phone', '') // Reset driver phone
      form.setValue('vehicle_head_number', '') // Reset vehicle head number
      form.setValue('vehicle_tail_number', '') // Reset vehicle tail number
    }
  }

  // Handle driver selection change
  const handleDriverChange = (value: string) => {
    // Handle special "no driver" value
    if (value === '__no_driver__') {
      form.setValue('driver_id', '')
      form.setValue('driver_phone', '')
      form.setValue('vehicle_head_number', '')
      form.setValue('vehicle_tail_number', '')
      return
    }

    // Only set valid driver IDs, ignore placeholder values
    if (value && !value.startsWith('__')) {
      form.setValue('driver_id', value)

      // Auto-fill driver information from selected driver
      const selectedDriver = drivers.find(d => d.id === value)
      if (selectedDriver) {
        // Auto-populate phone if available
        if (selectedDriver.phone) {
          form.setValue('driver_phone', selectedDriver.phone)
        }
        // Auto-populate vehicle head number if available
        if (selectedDriver.vehicle_head_number) {
          form.setValue('vehicle_head_number', selectedDriver.vehicle_head_number)
        }
        // Auto-populate vehicle tail number if available
        if (selectedDriver.vehicle_tail_number) {
          form.setValue('vehicle_tail_number', selectedDriver.vehicle_tail_number)
        }
      }
    }
  }

  // Format assignment date for datetime-local input
  const formatDateTimeLocal = (dateString: string) => {
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return ''
      }
      return format(date, "yyyy-MM-dd'T'HH:mm")
    } catch {
      return ''
    }
  }

  // Convert datetime-local input to ISO string
  const convertToISOString = (dateTimeLocal: string) => {
    try {
      if (!dateTimeLocal) return new Date().toISOString()
      const date = new Date(dateTimeLocal)
      if (isNaN(date.getTime())) {
        return new Date().toISOString()
      }
      return date.toISOString()
    } catch {
      return new Date().toISOString()
    }
  }

  return (
    <Card className={`bg-slate-800 border-slate-700 ${className}`}>
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Truck className="h-5 w-5" />
          {mode === 'create' ? 'Assign Transportation' : 'Edit Transportation Assignment'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Carrier and Driver Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-blue-500" />
                <h3 className="text-lg font-medium text-white">Carrier & Driver Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Carrier Selection */}
                <FormField
                  control={form.control}
                  name="carrier_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Carrier Company *</FormLabel>
                      <Select onValueChange={handleCarrierChange} value={field.value || undefined}>
                        <FormControl>
                          <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                            <SelectValue placeholder="Select a carrier company" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingCarriers ? (
                            <SelectItem value="__loading__" disabled>
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              Loading carriers...
                            </SelectItem>
                          ) : carriers.length === 0 ? (
                            <SelectItem value="__no_carriers__" disabled>
                              No carrier companies found
                            </SelectItem>
                          ) : (
                            carriers.map((carrier) => (
                              <SelectItem key={carrier.id} value={carrier.id}>
                                {carrier.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Driver Selection */}
                <FormField
                  control={form.control}
                  name="driver_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Driver</FormLabel>
                      <FormControl>
                        <SearchableSelect
                          options={driverOptions}
                          value={field.value || undefined}
                          onValueChange={handleDriverChange}
                          disabled={!carrierId || isLoadingDrivers}
                          placeholder={
                            !carrierId
                              ? "Select carrier first"
                              : isLoadingDrivers
                              ? "Loading drivers..."
                              : "Search and select a driver"
                          }
                          searchPlaceholder="Search drivers by name, code, phone, or vehicle..."
                          emptyText={drivers.length === 0 ? "No drivers available for this carrier" : "No drivers found"}
                          className="w-full"
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        Optional: Assign a specific driver from the selected carrier
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Vehicle and Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="vehicle_head_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Vehicle Head Number</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="e.g., 12-3456"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vehicle_tail_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Vehicle Tail Number</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="e.g., 78-9012"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="driver_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Driver Phone</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            {...field}
                            placeholder="+66 XX XXX XXXX"
                            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 pl-10"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Assignment Date */}
              <FormField
                control={form.control}
                name="assignment_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">Assignment Date *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                        <Input
                          type="datetime-local"
                          {...field}
                          value={formatDateTimeLocal(field.value)}
                          onChange={(e) => {
                            const isoString = convertToISOString(e.target.value)
                            field.onChange(isoString)
                          }}
                          className="bg-slate-700 border-slate-600 text-white pl-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Location Information Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-blue-500" />
                  <h3 className="text-lg font-medium text-white">Location Information</h3>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAdvancedGPS(!showAdvancedGPS)}
                  className="border-yellow-500 bg-yellow-600/10 text-yellow-100 hover:bg-yellow-600/20 hover:border-yellow-400"
                >
                  <Navigation className="h-3 w-3 mr-1" />
                  {showAdvancedGPS ? 'Hide' : 'Show'} GPS Options
                </Button>
              </div>

              {/* Pickup Locations */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="pickup_container_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Pickup Container Location</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter container pickup location"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        Where to pick up empty containers
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pickup_product_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Pickup Product Location</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter product pickup location"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        Where to pick up products/cargo
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Delivery Location */}
              <FormField
                control={form.control}
                name="delivery_location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">Delivery Location *</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter delivery destination"
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* GPS Coordinates Section */}
              {showAdvancedGPS && (
                <div className="space-y-4 p-4 bg-slate-900/50 rounded-lg border border-slate-600">
                  <h4 className="text-md font-medium text-white">GPS Coordinates</h4>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Container Pickup GPS */}
                    <FormField
                      control={form.control}
                      name="pickup_container_gps_coordinates"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-200">Container Pickup GPS</FormLabel>
                          <FormControl>
                            <GPSCoordinateInput
                              value={field.value}
                              onChange={field.onChange}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Product Pickup GPS */}
                    <FormField
                      control={form.control}
                      name="pickup_product_gps_coordinates"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-200">Product Pickup GPS</FormLabel>
                          <FormControl>
                            <GPSCoordinateInput
                              value={field.value}
                              onChange={field.onChange}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Delivery GPS */}
                  <FormField
                    control={form.control}
                    name="delivery_gps_coordinates"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-slate-200">Delivery GPS</FormLabel>
                        <FormControl>
                          <GPSCoordinateInput
                            value={field.value}
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Distance and Notes Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Calculator className="h-4 w-4 text-blue-500" />
                <h3 className="text-lg font-medium text-white">Additional Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="estimated_distance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Estimated Distance (km)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Calculator className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            max="10000"
                            {...field}
                            value={field.value || ''}
                            onChange={(e) => {
                              const value = e.target.value ? parseFloat(e.target.value) : undefined
                              field.onChange(value)
                            }}
                            className="bg-slate-700 border-slate-600 text-white pl-10"
                            placeholder="0.00"
                          />
                        </div>
                      </FormControl>
                      {estimatedDistance && (
                        <FormDescription className="text-blue-400">
                          Auto-calculated: {estimatedDistance} km based on GPS coordinates
                        </FormDescription>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <Label className="text-slate-200">Transportation Status</Label>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className="bg-purple-500/20 text-purple-300 border-purple-400"
                    >
                      <Truck className="h-3 w-3 mr-1" />
                      Transport Assignment
                    </Badge>
                    <span className="text-sm text-slate-400">
                      Assignment will update shipment status
                    </span>
                  </div>
                </div>
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">Special Instructions & Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter any special handling instructions or notes for the transportation..."
                        rows={3}
                        maxLength={1000}
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 resize-none"
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      {field.value?.length || 0}/1000 characters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-slate-600">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  className="border-red-600 bg-red-600 hover:bg-red-700 text-white"
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              )}
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4 mr-2" />
                )}
                {mode === 'create' ? 'Assign Transportation' : 'Update Assignment'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}