'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Eye,
  X,
  Package,
  Users,
  Star,
  StarOff,
  Upload,
  DollarSign,
  Weight,
  Package2,
} from 'lucide-react'
import { CustomerProductForm } from '@/components/forms/customer-product-form/customer-product-form'
import { CustomerProductBulkImportForm } from '@/components/forms/customer-product-bulk-import-form/customer-product-bulk-import-form'
import {
  useCustomerProductsManagement,
  useCustomerOptions,
  useProductOptions,
  useProductCategories,
} from '@/hooks/use-customer-products'
import type { CustomerProduct } from '@/stores/customer-product-store'
import type { CustomerProductForm as CustomerProductFormData } from '@/lib/validations/customer-products'
import {
  CURRENCY_OPTIONS,
  PACKAGING_TYPE_OPTIONS,
  formatCurrency,
} from '@/lib/validations/customer-products'
import { formatDistanceToNow, format } from 'date-fns'

export default function ProductRelationshipsPage() {
  const {
    // Data
    customerProducts,
    loading,
    error,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedCustomerProducts,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createCustomerProduct,
    updateCustomerProduct,
    deleteCustomerProduct,
    bulkDeleteCustomerProducts,
    setDefaultProduct,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    toggleCustomerProduct,
    toggleAll,
    clearSelection,
    clearError,
    refreshCustomerProducts,
  } = useCustomerProductsManagement()

  const { customers } = useCustomerOptions()
  const { products } = useProductOptions()
  const { categories } = useProductCategories()

  // UI state
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingRelationship, setEditingRelationship] =
    useState<CustomerProduct | null>(null)
  const [viewingRelationship, setViewingRelationship] =
    useState<CustomerProduct | null>(null)
  const [deletingRelationship, setDeletingRelationship] =
    useState<CustomerProduct | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)
  const [showBulkImportDialog, setShowBulkImportDialog] = useState(false)

  // Handle create relationship
  const handleCreate = async (data: CustomerProductFormData) => {
    try {
      await createCustomerProduct(data)
      setShowCreateDialog(false)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle update relationship
  const handleUpdate = async (data: CustomerProductFormData) => {
    if (!editingRelationship) return

    try {
      await updateCustomerProduct(editingRelationship.id, data)
      setEditingRelationship(null)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle delete relationship
  const handleDelete = async (relationship: CustomerProduct) => {
    try {
      await deleteCustomerProduct(relationship.id)
      setDeletingRelationship(null)
    } catch (error) {
      console.error('Delete failed:', error)
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      await bulkDeleteCustomerProducts(Array.from(selectedCustomerProducts))
      setShowBulkDeleteDialog(false)
      clearSelection()
    } catch (error) {
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle set default
  const handleSetDefault = async (
    customerId: string,
    relationshipId: string
  ) => {
    try {
      await setDefaultProduct(customerId, relationshipId)
    } catch (error) {
      console.error('Set default failed:', error)
    }
  }

  // Handle sorting
  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSorting(column, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSorting(column, 'asc')
    }
  }

  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  // Format currency display
  const formatPrice = (price: number | null, currency: string | null) => {
    if (!price || !currency) return 'N/A'
    return formatCurrency(price, currency as any)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">
            Customer-Product Relationships
          </h1>
          <p className="text-slate-400 mt-1">
            Manage customer-product relationships with pricing and
            specifications
          </p>
        </div>
        <div className="flex space-x-2">
          <Dialog
            open={showBulkImportDialog}
            onOpenChange={setShowBulkImportDialog}
          >
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
              >
                <Upload className="h-4 w-4 mr-2" />
                Bulk Import
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
              <DialogHeader>
                <DialogTitle className="text-white">
                  Bulk Import Customer-Product Relationships
                </DialogTitle>
                <DialogDescription className="text-slate-400">
                  Import multiple customer-product relationships from a CSV file
                </DialogDescription>
              </DialogHeader>
              <CustomerProductBulkImportForm
                onClose={() => setShowBulkImportDialog(false)}
                onSuccess={() => {
                  refreshCustomerProducts()
                  // Don't auto-close dialog - let user see results and close manually
                }}
              />
            </DialogContent>
          </Dialog>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add Relationship
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
              <DialogHeader>
                <DialogTitle className="text-white">
                  Create Customer-Product Relationship
                </DialogTitle>
                <DialogDescription className="text-slate-400">
                  Associate a customer with a product and configure pricing and
                  specifications
                </DialogDescription>
              </DialogHeader>
              <CustomerProductForm
                onSubmit={handleCreate}
                onCancel={() => setShowCreateDialog(false)}
                isLoading={isCreating}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {(searchTerm ||
            filter.customer_id ||
            filter.category ||
            filter.currency_code ||
            filter.packaging_type) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchTerm('')
                setFilter({})
              }}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Search Relationships
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search customers, products, codes..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
          </div>

          {/* Customer Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Customer
            </label>
            <Select
              value={filter.customer_id || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  customer_id: value === 'all' ? undefined : value,
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Customers" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Customers
                </SelectItem>
                {customers.map(customer => (
                  <SelectItem
                    key={customer.id}
                    value={customer.id}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {customer.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Category Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Product Category
            </label>
            <Select
              value={filter.category || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  category: value === 'all' ? undefined : value,
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Categories
                </SelectItem>
                {categories.map(category => (
                  <SelectItem
                    key={category}
                    value={category}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Currency Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Currency
            </label>
            <Select
              value={filter.currency_code || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  currency_code: value === 'all' ? undefined : (value as any),
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Currencies" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Currencies
                </SelectItem>
                {CURRENCY_OPTIONS.map(option => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {(searchTerm ||
          filter.customer_id ||
          filter.category ||
          filter.currency_code) && (
          <div className="flex flex-wrap gap-2 pt-2">
            {searchTerm && (
              <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-2 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.customer_id && (
              <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                Customer:{' '}
                {customers.find(c => c.id === filter.customer_id)?.name ||
                  'Unknown'}
                <button
                  onClick={() =>
                    setFilter({ ...filter, customer_id: undefined })
                  }
                  className="ml-2 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.category && (
              <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
                Category: {filter.category}
                <button
                  onClick={() => setFilter({ ...filter, category: undefined })}
                  className="ml-2 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.currency_code && (
              <div className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm flex items-center">
                Currency: {filter.currency_code}
                <button
                  onClick={() =>
                    setFilter({ ...filter, currency_code: undefined })
                  }
                  className="ml-2 hover:text-purple-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedCount} relationship{selectedCount !== 1 ? 's' : ''}{' '}
                selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowBulkDeleteDialog(true)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Relationships Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Package className="h-5 w-5 text-orange-500" />
              Customer-Product Relationships ({totalCount})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshCustomerProducts}
              disabled={loading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {loading && customerProducts.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-slate-300">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              <span className="ml-2">Loading relationships...</span>
            </div>
          ) : customerProducts.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No relationships found</p>
              <p className="text-sm text-slate-400 mt-1">
                Create your first customer-product relationship to get started
              </p>
            </div>
          ) : (
            <>
              <Table className="bg-slate-800">
                <TableHeader className="bg-slate-700">
                  <TableRow className="border-slate-600 hover:bg-slate-700">
                    <TableHead className="w-12 text-slate-200">
                      <Checkbox
                        checked={isAllSelected || isPartiallySelected}
                        onCheckedChange={toggleAll}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('customer')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Customer {getSortIcon('customer')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('product')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Product {getSortIcon('product')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">Pricing</TableHead>
                    <TableHead className="text-slate-200">Packaging</TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('is_default')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Default {getSortIcon('is_default')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">Status</TableHead>
                    <TableHead className="w-32 text-slate-200">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-slate-800">
                  {customerProducts.map(relationship => (
                    <TableRow
                      key={relationship.id}
                      className="border-slate-600 hover:bg-slate-700 text-slate-200"
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected(relationship.id)}
                          onCheckedChange={() =>
                            toggleCustomerProduct(relationship.id)
                          }
                          className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-blue-500 flex-shrink-0" />
                          <div className="min-w-0">
                            <div className="font-medium text-slate-200 truncate">
                              {relationship.customer?.name ||
                                'Unknown Customer'}
                            </div>
                            {relationship.customer_product_code && (
                              <div className="text-xs text-slate-400 truncate">
                                Code: {relationship.customer_product_code}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Package2 className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <div className="min-w-0">
                            <div className="font-medium text-slate-200 truncate">
                              {relationship.product?.name || 'Unknown Product'}
                            </div>
                            <div className="text-xs text-slate-400 truncate flex items-center">
                              {relationship.product?.code && (
                                <span className="mr-2">
                                  Code: {relationship.product.code}
                                </span>
                              )}
                              {relationship.product?.category && (
                                <Badge
                                  variant="outline"
                                  className="border-slate-500 text-slate-300 text-xs px-1 py-0"
                                >
                                  {relationship.product.category}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {relationship.unit_price_cif && (
                            <div className="text-xs text-slate-300 flex items-center">
                              <DollarSign className="h-3 w-3 mr-1" />
                              CIF:{' '}
                              {formatPrice(
                                relationship.unit_price_cif,
                                relationship.currency_code
                              )}
                            </div>
                          )}
                          {relationship.unit_price_fob && (
                            <div className="text-xs text-slate-300 flex items-center">
                              <DollarSign className="h-3 w-3 mr-1" />
                              FOB:{' '}
                              {formatPrice(
                                relationship.unit_price_fob,
                                relationship.currency_code
                              )}
                            </div>
                          )}
                          {relationship.currency_code && (
                            <Badge
                              variant="outline"
                              className="border-slate-500 text-slate-300 text-xs"
                            >
                              {relationship.currency_code}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Badge
                            variant="outline"
                            className="border-slate-500 text-slate-300 text-xs"
                          >
                            {relationship.packaging_type}
                          </Badge>
                          {(relationship.gross_weight_per_package ||
                            relationship.net_weight_per_package) && (
                            <div className="text-xs text-slate-400 flex items-center">
                              <Weight className="h-3 w-3 mr-1" />
                              {relationship.gross_weight_per_package &&
                                `${relationship.gross_weight_per_package}kg`}
                              {relationship.gross_weight_per_package &&
                                relationship.net_weight_per_package &&
                                ' / '}
                              {relationship.net_weight_per_package &&
                                `${relationship.net_weight_per_package}kg net`}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {relationship.is_default ? (
                            <Star className="h-4 w-4 text-yellow-500" />
                          ) : (
                            <button
                              onClick={() =>
                                handleSetDefault(
                                  relationship.customer_id,
                                  relationship.id
                                )
                              }
                              className="opacity-50 hover:opacity-100"
                            >
                              <StarOff className="h-4 w-4 text-slate-500 hover:text-yellow-500" />
                            </button>
                          )}
                          <Badge
                            variant={
                              relationship.is_default ? 'default' : 'secondary'
                            }
                            className={
                              relationship.is_default
                                ? 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600'
                                : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                            }
                          >
                            {relationship.is_default ? 'Default' : 'Standard'}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            relationship.is_active ? 'default' : 'secondary'
                          }
                          className={
                            relationship.is_active
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-red-600 hover:bg-red-700 text-white border-red-600'
                          }
                        >
                          {relationship.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setViewingRelationship(relationship)}
                            className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingRelationship(relationship)}
                            className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              setDeletingRelationship(relationship)
                            }
                            className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-6 pb-6">
                  <div className="text-sm text-slate-300">
                    Page {currentPage} of {totalPages} ({totalCount}{' '}
                    relationships)
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(1)}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={previousPage}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Edit Relationship Dialog */}
      <Dialog
        open={!!editingRelationship}
        onOpenChange={() => setEditingRelationship(null)}
      >
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">
              Edit Customer-Product Relationship
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Update relationship configuration and pricing specifications
            </DialogDescription>
          </DialogHeader>
          {editingRelationship && (
            <CustomerProductForm
              customerProduct={editingRelationship}
              onSubmit={handleUpdate}
              onCancel={() => setEditingRelationship(null)}
              isLoading={isUpdating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Relationship Dialog */}
      <AlertDialog
        open={!!deletingRelationship}
        onOpenChange={() => setDeletingRelationship(null)}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Relationship
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete this customer-product
              relationship? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setDeletingRelationship(null)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                deletingRelationship && handleDelete(deletingRelationship)
              }
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Relationship
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Relationship Dialog */}
      <Dialog
        open={!!viewingRelationship}
        onOpenChange={() => setViewingRelationship(null)}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <Eye className="h-5 w-5 text-blue-500" />
              View Customer-Product Relationship Details
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Detailed view of the relationship configuration and specifications
            </DialogDescription>
          </DialogHeader>

          {viewingRelationship && (
            <div className="space-y-6 py-4">
              {/* Customer Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <Users className="h-5 w-5 text-blue-500" />
                      Customer Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        Customer Name
                      </label>
                      <p className="text-slate-200 font-medium">
                        {viewingRelationship.customer?.name ||
                          'Unknown Customer'}
                      </p>
                    </div>
                    {viewingRelationship.customer_product_code && (
                      <div>
                        <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Customer Product Code
                        </label>
                        <p className="text-slate-200 font-mono text-sm bg-slate-800 px-2 py-1 rounded">
                          {viewingRelationship.customer_product_code}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Product Information */}
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <Package2 className="h-5 w-5 text-green-500" />
                      Product Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        Product Name
                      </label>
                      <p className="text-slate-200 font-medium">
                        {viewingRelationship.product?.name || 'Unknown Product'}
                      </p>
                    </div>
                    {viewingRelationship.product?.code && (
                      <div>
                        <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Product Code
                        </label>
                        <p className="text-slate-200 font-mono text-sm bg-slate-800 px-2 py-1 rounded">
                          {viewingRelationship.product.code}
                        </p>
                      </div>
                    )}
                    {viewingRelationship.product?.category && (
                      <div>
                        <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Category
                        </label>
                        <p className="text-slate-200 font-medium">
                          {viewingRelationship.product.category}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Pricing Information */}
              <Card className="bg-slate-700 border-slate-600">
                <CardHeader className="pb-3">
                  <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                    <DollarSign className="h-5 w-5 text-yellow-500" />
                    Pricing Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {viewingRelationship.unit_price_cif && (
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        CIF Unit Price
                      </label>
                      <p className="text-slate-200 font-medium text-lg">
                        {formatPrice(
                          viewingRelationship.unit_price_cif,
                          viewingRelationship.currency_code
                        )}
                      </p>
                    </div>
                  )}
                  {viewingRelationship.unit_price_fob && (
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        FOB Unit Price
                      </label>
                      <p className="text-slate-200 font-medium text-lg">
                        {formatPrice(
                          viewingRelationship.unit_price_fob,
                          viewingRelationship.currency_code
                        )}
                      </p>
                    </div>
                  )}
                  {viewingRelationship.currency_code && (
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        Currency
                      </label>
                      <p className="text-slate-200 font-medium text-lg">
                        {viewingRelationship.currency_code}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Packaging Information */}
              <Card className="bg-slate-700 border-slate-600">
                <CardHeader className="pb-3">
                  <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                    <Package className="h-5 w-5 text-purple-500" />
                    Packaging Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                      Packaging Type
                    </label>
                    <p className="text-slate-200 font-medium flex items-center gap-1">
                      {viewingRelationship.packaging_type}
                    </p>
                  </div>
                  {viewingRelationship.gross_weight_per_package && (
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        Gross Weight
                      </label>
                      <p className="text-slate-200 font-medium flex items-center gap-1">
                        <Weight className="h-4 w-4" />
                        {viewingRelationship.gross_weight_per_package} kg
                      </p>
                    </div>
                  )}
                  {viewingRelationship.net_weight_per_package && (
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        Net Weight
                      </label>
                      <p className="text-slate-200 font-medium flex items-center gap-1">
                        <Weight className="h-4 w-4" />
                        {viewingRelationship.net_weight_per_package} kg
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Status and Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                      Status Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-slate-400">Default Product</span>
                      <div className="flex items-center gap-2">
                        {viewingRelationship.is_default ? (
                          <Star className="h-4 w-4 text-yellow-500" />
                        ) : (
                          <StarOff className="h-4 w-4 text-slate-500" />
                        )}
                        <Badge
                          variant={
                            viewingRelationship.is_default
                              ? 'default'
                              : 'secondary'
                          }
                          className={
                            viewingRelationship.is_default
                              ? 'bg-yellow-600 text-white'
                              : 'bg-slate-600 text-slate-200'
                          }
                        >
                          {viewingRelationship.is_default ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-slate-400">Active Status</span>
                      <Badge
                        variant={
                          viewingRelationship.is_active
                            ? 'default'
                            : 'secondary'
                        }
                        className={
                          viewingRelationship.is_active
                            ? 'bg-green-600 text-white'
                            : 'bg-red-600 text-white'
                        }
                      >
                        {viewingRelationship.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <AlertCircle className="h-5 w-5 text-slate-500" />
                      Timestamps
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {viewingRelationship.created_at && (
                      <div>
                        <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Created
                        </label>
                        <p className="text-slate-200">
                          {format(
                            new Date(viewingRelationship.created_at),
                            'PPp'
                          )}
                        </p>
                        <p className="text-xs text-slate-400">
                          {formatDistanceToNow(
                            new Date(viewingRelationship.created_at),
                            { addSuffix: true }
                          )}
                        </p>
                      </div>
                    )}
                    {viewingRelationship.updated_at && (
                      <div>
                        <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Last Updated
                        </label>
                        <p className="text-slate-200">
                          {format(
                            new Date(viewingRelationship.updated_at),
                            'PPp'
                          )}
                        </p>
                        <p className="text-xs text-slate-400">
                          {formatDistanceToNow(
                            new Date(viewingRelationship.updated_at),
                            { addSuffix: true }
                          )}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Additional Details */}
              {viewingRelationship.notes && (
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <AlertCircle className="h-5 w-5 text-slate-400" />
                      Notes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-200 whitespace-pre-wrap">
                      {viewingRelationship.notes}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-slate-600">
                <Button
                  variant="outline"
                  onClick={() => setViewingRelationship(null)}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setEditingRelationship(viewingRelationship)
                    setViewingRelationship(null)
                  }}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Relationship
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Selected Relationships
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete {selectedCount} selected
              relationship
              {selectedCount !== 1 ? 's' : ''}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowBulkDeleteDialog(false)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Relationships
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
