# Story 1.2: Database Schema & RLS Foundation

## Status
Done

## Story
**As a** System Administrator,  
**I want** the core database schema established with Row Level Security policies,  
**so that** data access is properly secured and the foundation is ready for master data operations.

## Acceptance Criteria

**1:** All master data tables are created (profiles, companies, products, ports, units_of_measure, drivers) with proper relationships and constraints.

**2:** Core business tables are created (shipments, containers, shipment_products, transportation, status_history) with audit trail support.

**3:** Row Level Security (RLS) is enabled on all tables with basic policies for role-based access control.

**4:** Database functions and triggers are implemented for automatic timestamp updates and data consistency.

**5:** Sample reference data is loaded (units of measure, basic port data) to support application testing.

## Tasks / Subtasks

- [x] Create core database enums and types (AC: 1, 2, 3)
  - [x] Create role_type enum with all 11 user roles
  - [x] Create company_type_enum for stakeholder types
  - [x] Create shipment_status_enum for status tracking
  - [x] Create transport_mode_enum, currency_enum, packaging_type_enum, port_type_enum, container_type_enum, container_size_enum, container_status_enum, notification_type_enum, notification_channel_enum, customer_type_enum, incoterms_enum
  - [x] Create document_type_enum for document management

- [x] Create user profiles table extending Supabase auth (AC: 1, 3)
  - [x] Create profiles table with proper references to auth.users
  - [x] Add role and company association fields
  - [x] Implement RLS policies for user profile access
  - [x] Add validation constraints for role-company relationships

- [x] Create master data tables with hybrid company design (AC: 1, 2)
  - [x] Create companies table with hybrid approach (base table + JSONB metadata)
  - [x] Create products table with unit of measure references
  - [x] Create ports table for origin/destination tracking
  - [x] Create units_of_measure table for standardized measurements
  - [x] Create drivers table linked to carrier companies
  - [x] Add proper foreign key constraints and indexes

- [x] Create company-specific info tables for complex types (AC: 1, 2)
  - [x] Create customer_info table with credit limits and incoterms
  - [x] Create carrier_info table with fleet management data
  - [x] Create factory_info table with production capacity and certifications
  - [x] Add proper constraints linking to companies table

- [x] Create relationship intelligence tables (AC: 1, 2)
  - [x] Create customer_shippers table for customer-shipper relationships
  - [x] Create consignee_notify_parties table for consignee-notify party relationships
  - [x] Add is_default flags and validation constraints
  - [x] Create indexes for intelligent pre-population queries

- [x] Create core business tables (AC: 2)
  - [x] Create shipments table with all stakeholder references
  - [x] Create containers table for shipment container tracking
  - [x] Create shipment_products table for product line items
  - [x] Create customer_products table for pricing and packaging specifications
  - [x] Create transportation table for logistics management
  - [x] Create status_history table with audit trail support
  - [x] Create status_images table for photo documentation

- [x] Create document management tables (AC: 2)
  - [x] Create documents table for shipment document storage
  - [x] Create document_templates table for PDF generation
  - [x] Add file path and metadata support
  - [x] Implement document type validation

- [x] Create notification system tables (AC: 2)
  - [x] Create notifications table for notification history
  - [x] Create notification_preferences table for user preferences
  - [x] Add support for multiple notification channels (email, SMS, Line, WeChat)
  - [x] Implement notification type validation

- [x] Implement Row Level Security policies (AC: 3)
  - [x] Enable RLS on all tables with proper policy definitions
  - [x] Create admin/staff full access policies
  - [x] Create role-based access policies for companies table
  - [x] Create shipment access policies based on stakeholder roles
  - [x] Create driver-specific policies for mobile access
  - [x] Test RLS policies with different user roles

- [x] Create database functions and triggers (AC: 4)
  - [x] Create updated_at trigger function for automatic timestamps
  - [x] Create validate_company_metadata function for hybrid design
  - [x] Create GPS coordinate sync trigger for companies table
  - [x] Create shipment number generation function
  - [x] Add triggers to all tables requiring automatic updates

- [x] Create performance indexes (AC: 1, 2)
  - [x] Add indexes for company type and status queries
  - [x] Add indexes for shipment filtering and sorting
  - [x] Add indexes for relationship intelligence queries
  - [x] Add GIS indexes for GPS coordinate queries
  - [x] Add composite indexes for multi-column searches

- [x] Load sample reference data (AC: 5)
  - [x] Create seed data for units of measure (KG, pieces, containers)
  - [x] Create seed data for basic port information
  - [x] Create seed data for currency codes
  - [x] Create sample company data for testing
  - [x] Verify data integrity and referential constraints

- [x] Create database migration files (AC: 1, 2, 3, 4, 5)
  - [x] Create initial schema migration file
  - [x] Create RLS policies migration file
  - [x] Create functions and triggers migration file
  - [x] Create performance indexes migration file
  - [x] Create seed data migration file
  - [x] Test migration sequence and rollback procedures

## Dev Notes

### Previous Story Insights
From Story 1.1: Supabase client configuration completed for browser and server, health check API implemented with proper error handling, project infrastructure established with TypeScript and Next.js App Router.

### Data Models
[Source: architecture/data-models.md#company]
**Company Entity (Hybrid Design):**
- Base table with common fields for all company types
- JSONB metadata field ONLY for simple types (shippers, consignees, notify parties, forwarder agents)
- Complex types (customers, carriers, factories) use separate info tables
- GPS coordinates stored as both JSONB and PostGIS point for efficient queries
- Company types: customer, carrier, factory, shipper, consignee, notify_party, forwarder_agent

[Source: architecture/data-models.md#product]
**Product Entity:**
- Standardized measurement units (base unit: KG)
- HS codes for customs compliance
- Product categories and specifications
- References to units_of_measure table

[Source: architecture/data-models.md#shipment]
**Shipment Entity:**
- Core business entity with auto-generated shipment numbers (EX[Mode]-[Port]-YYMMDD-[Running])
- Multiple stakeholder references (customer, shipper, consignee, notify_party, factory, forwarder_agent)
- Transportation details (ETD, ETA, ports, vessel information)
- Status tracking with enum values: booking_confirmed, transport_assigned, driver_assigned, empty_container_picked, arrived_at_factory, loading_started, departed_factory, container_returned, shipped, arrived, completed

[Source: architecture/data-models.md#customerproduct]
**CustomerProduct Relationship Entity:**
- Manages customer-specific pricing (CIF/FOB per KG)
- Packaging specifications (Bag, Plastic Basket, Carton)
- Intelligent pre-population support with is_default flag
- Gross/net weight per package specifications

[Source: architecture/data-models.md#driver]
**Driver Entity:**
- Linked to carrier companies only
- Mobile interface access support
- Line messaging app integration (line_id field)
- Photo documentation support (driver_picture_path)

### Database Schema Implementation
[Source: architecture/database-schema.md#core-schema-implementation]
[Source: docs/data-model-requirements.md]
**Required Extensions:**
- uuid-ossp for UUID generation
- postgis for GPS coordinate support

**Enum Definitions:**
- role_type: admin, cs, account, customer, carrier, driver, factory, shipper, consignee, notify_party, forwarder_agent
- company_type_enum: customer, carrier, factory, shipper, consignee, notify_party, forwarder_agent
- shipment_status_enum: booking_confirmed through completed (11 statuses total)
- transport_mode_enum: sea, land, rail
- currency_enum: THB, CNY, USD, EUR
- packaging_type_enum: Bag, Plastic Basket, Carton
- document_type_enum: booking_confirmation, invoice_fob, invoice_cif, shipping_instruction, bill_of_lading, photo_upload, other
- customer_type_enum: regular, premium, vip
- incoterms_enum: FOB, CIF, EXW, CFR
- port_type_enum: origin, destination, transit
- transport_mode_enum: sea, land, rail, air
- container_type_enum: dry, reefer, open_top, flat_rack, tank
- container_size_enum: 20ft, 40ft, 40hc, 45ft
- container_status_enum: empty, loaded, sealed,in_transit, delivered
- notification_type_enum: status_update, assignment, document_ready, delay_alert, system
- notification_channel_enum: email, sms, line, wechat, in_app

**Key Tables Structure:**
- profiles: Extends auth.users with role and company association
- companies: Hybrid design with JSONB metadata for simple types only
- customer_info: Credit limits, incoterms, special requirements for customer companies
- carrier_info: Fleet size, license types, coverage areas for carrier companies
- factory_info: Production capacity, certifications, license numbers for factory companies
- customer_shippers: Relationship table linking customers to their preferred shippers
- consignee_notify_parties: Relationship table linking consignees to notify parties
- shipments: All stakeholder references with date validation constraints
- customer_products: Pricing and packaging specifications with unique constraints
- documents: Shipment document storage with file paths and metadata
- document_templates: PDF generation templates with JSONB data
- notifications: Notification history with multiple channel support
- notification_preferences: User-specific notification channel preferences
- status_history: Audit trail with GPS coordinates
- status_images: Photo documentation support

[Source: architecture/database-schema.md#performance-indexes]
**Required Performance Indexes:**
- Company type and status filtering indexes
- Shipment customer and status indexes
- Customer-product relationship indexes for intelligent pre-population
- GIS indexes for GPS coordinate queries
- Status history tracking indexes

### Row Level Security Policies
[Source: architecture/backend-architecture.md#schema-design]
**RLS Policy Requirements:**
- Profiles: Users can only see/update their own profile
- Companies: Admins see all, users see affiliated companies only
- Shipments: Multi-role access based on stakeholder relationships
- Role-based filtering: admin/staff see all, customers see their shipments, drivers see assigned shipments

### API Integration Requirements
[Source: architecture/api-specification.md#core-api-patterns]
**Supabase Client Integration:**
- Auto-generated TypeScript types from database schema
- Real-time subscriptions for status updates
- Row Level Security integration for secure data access
- Support for complex joins and relationship queries

### File Locations for Database Code
[Source: architecture/unified-project-structure.md]
- Database migrations: `supabase/migrations/` with timestamped filenames
- Seed data: `supabase/seed.sql`
- Supabase configuration: `supabase/config.toml`
- Generated TypeScript types: `src/types/database.ts` (auto-generated)
- Validation schemas: `src/lib/validations/` (Zod schemas)

### Technical Constraints
[Source: architecture/tech-stack.md#technology-stack-table]
- PostgreSQL 15+ (Supabase managed) with PostGIS extension
- UUID primary keys using gen_random_uuid()
- Timestamptz for all date/time fields
- JSONB for flexible metadata storage
- Row Level Security for multi-tenant data access
- Supabase CLI for database migrations and type generation

### Validation Functions Required
- validate_company_metadata: Ensures complex company types have NULL metadata
- GPS coordinate synchronization between JSONB and PostGIS point columns
- Role-company association validation
- Date sequence validation for ETD/ETA constraints

### Sample Reference Data Requirements
[Source: architecture/data-models.md]
- Units of measure: KG (base unit), pieces, containers
- Currency codes: THB, CNY, USD, EUR with proper symbols
- Basic port data for major Asian export/import ports
- Sample company data representing all company types for testing

## Testing

### Testing Standards
**Test Location**: `tests/` directory with `integration/` subdirectory for database tests
**Testing Framework**: Vitest + Supabase CLI for database integration testing
**Database Testing Patterns**: 
- Local Supabase instance for testing
- Migration rollback testing
- RLS policy validation with test users
- Data integrity constraint testing
- Performance testing for indexed queries

**Specific Testing Requirements for This Story**:
- Test all RLS policies with different user roles
- Validate all foreign key constraints and referential integrity
- Test database functions and triggers with various scenarios
- Verify sample data loads correctly without constraint violations
- Test migration sequence including rollback procedures
- Performance testing for GPS coordinate queries and relationship intelligence

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-13 | 1.0 | Initial story creation with comprehensive architecture context | Scrum Master |

## Dev Agent Record

### Agent Model Used
Claude Sonnet 4 (claude-sonnet-4-20250514) - Full Stack Developer Agent

### Debug Log References
- Build validation: Next.js build completed successfully with TypeScript validation
- Test validation: All unit tests (9/9) and integration tests passed
- Linting validation: ESLint checks passed with no warnings or errors

### Completion Notes List
- Successfully created 9 comprehensive database migration files implementing the complete schema
- Implemented hybrid company design with JSONB metadata for simple types and dedicated tables for complex types
- Created comprehensive Row Level Security policies supporting 11 user roles with multi-tenant access control
- Implemented intelligent relationship tables for customer-shipper and consignee-notify party pre-population
- Added performance optimization with 50+ strategic indexes including GIS indexes for GPS coordinates
- Created complete sample reference data with 12 companies, 20 products, 16 ports, and relationship intelligence data
- All acceptance criteria validated through comprehensive constraint checking and data integrity verification

### File List
- `supabase/migrations/20250813000001_initial_schema_enums_types.sql` - Core enums and helper functions
- `supabase/migrations/20250813000002_master_data_tables.sql` - Profiles, companies, products, ports, units, drivers
- `supabase/migrations/20250813000003_company_info_tables.sql` - Customer, carrier, and factory info tables
- `supabase/migrations/20250813000004_relationship_intelligence_tables.sql` - Customer-shipper, consignee-notify party, customer-product relationships
- `supabase/migrations/20250813000005_core_business_tables.sql` - Shipments, containers, products, transportation, status history
- `supabase/migrations/20250813000006_document_notification_tables.sql` - Documents, templates, notifications, preferences
- `supabase/migrations/20250813000007_row_level_security_policies.sql` - Comprehensive RLS policies for all tables
- `supabase/migrations/20250813000008_performance_indexes.sql` - Performance optimization indexes
- `supabase/migrations/20250813000009_sample_reference_data.sql` - Sample data for testing and validation

## QA Results

*This section will be populated by the QA agent during review*