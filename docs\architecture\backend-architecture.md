# Backend Architecture

## Service Architecture

### Serverless Architecture

**Function Organization**

```text
supabase/
├── functions/
│   ├── notifications/
│   │   ├── email-notification/
│   │   │   ├── index.ts              # Email sending Edge Function
│   │   │   └── templates/            # Email templates
│   │   ├── sms-notification/
│   │   │   └── index.ts              # SMS/Line/WeChat notifications
│   │   └── in-app-notification/
│   │       └── index.ts              # Real-time in-app notifications
│   ├── document-processing/
│   │   ├── pdf-generator/
│   │   │   └── index.ts              # PDF generation for reports
│   │   └── file-upload-handler/
│   │       └── index.ts              # File upload processing
│   ├── data-sync/
│   │   ├── relationship-engine/
│   │   │   └── index.ts              # Relationship intelligence
│   │   └── external-api-sync/
│   │       └── index.ts              # Third-party integrations
│   └── mobile-sync/
│       ├── offline-sync/
│       │   └── index.ts              # PWA offline synchronization
│       └── status-update/
│           └── index.ts              # Driver status updates
├── migrations/                        # Database migrations
│   ├── 20240101000000_initial_schema.sql
│   ├── 20240102000000_companies_rls.sql
│   ├── 20240103000000_relationship_functions.sql
│   └── 20240104000000_performance_indexes.sql
└── seed.sql                          # Development seed data
```

**Function Template**

```typescript
// supabase/functions/notifications/email-notification/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface EmailRequest {
  to: string[]
  template: string
  data: Record<string, any>
  shipment_id?: string
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { to, template, data, shipment_id }: EmailRequest = await req.json()

    // Validate request
    if (!to || !template) {
      throw new Error('Missing required fields: to, template')
    }

    // Get email template
    const emailTemplate = await getEmailTemplate(template, data)
    
    // Send email via configured provider (SendGrid, AWS SES, etc.)
    const result = await sendEmail({
      to,
      subject: emailTemplate.subject,
      html: emailTemplate.html,
      text: emailTemplate.text
    })

    // Log notification activity
    if (shipment_id) {
      await supabase
        .from('notification_logs')
        .insert({
          shipment_id,
          notification_type: 'email',
          recipient: to.join(','),
          template,
          status: 'sent',
          provider_response: result
        })
    }

    return new Response(
      JSON.stringify({ success: true, message_id: result.messageId }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

async function getEmailTemplate(templateName: string, data: Record<string, any>) {
  // Load and process email template with data interpolation
  // Support for multiple languages based on user preferences
  return {
    subject: `Shipment Update: ${data.shipment_number}`,
    html: `<html>...</html>`,
    text: `Text version...`
  }
}

async function sendEmail(emailData: any) {
  // Integration with email service provider
  // Return provider response with message ID for tracking
  return { messageId: 'generated-id' }
}
```

## Database Architecture

### Schema Design

```sql
-- Row Level Security Policies for Multi-Tenant Access

-- Profiles RLS: Users can only see their own profile
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = user_id);

-- Companies RLS: Role-based access with company affiliation
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins see all companies" ON companies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE user_id = auth.uid() 
      AND role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Users see affiliated companies" ON companies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.user_id = auth.uid()
      AND (
        p.company_id = companies.id OR
        p.role IN ('admin', 'staff')
      )
    )
  );

-- Shipments RLS: Complex multi-role access control
ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Shipment access by role" ON shipments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.user_id = auth.uid()
      AND (
        -- Admins and staff see all
        p.role IN ('admin', 'staff') OR
        -- Customers see their shipments
        (p.role = 'customer' AND p.company_id = shipments.customer_id) OR
        -- Drivers see assigned shipments
        (p.role = 'driver' AND p.user_id IN (
          SELECT driver_id FROM shipment_drivers sd 
          WHERE sd.shipment_id = shipments.id
        )) OR
        -- Factory users see relevant shipments
        (p.role = 'factory_user' AND p.company_id = shipments.factory_id) OR
        -- Forwarders see their shipments
        (p.role = 'forwarder' AND p.company_id = shipments.forwarder_agent_id)
      )
    )
  );
```

### Data Access Layer

```typescript
// lib/repositories/shipment-repository.ts
import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/database'
import { SupabaseService } from '@/lib/supabase/client'

type Shipment = Database['public']['Tables']['shipments']['Row']
type ShipmentInsert = Database['public']['Tables']['shipments']['Insert']
type ShipmentUpdate = Database['public']['Tables']['shipments']['Update']

export class ShipmentRepository extends SupabaseService {
  
  async getShipments(filters?: {
    status?: string
    customer_id?: string
    date_range?: { start: string; end: string }
  }) {
    let query = this.client
      .from('shipments')
      .select(`
        *,
        customer:companies!customer_id(name, company_type),
        shipper:companies!shipper_id(name),
        consignee:companies!consignee_id(name),
        factory:companies!factory_id(name),
        origin_port:ports!origin_port_id(port_name, country),
        destination_port:ports!destination_port_id(port_name, country),
        status_history(status_to, created_at, notes),
        shipment_items(quantity, unit_price, product:products(name))
      `)
      .order('created_at', { ascending: false })

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    
    if (filters?.customer_id) {
      query = query.eq('customer_id', filters.customer_id)
    }
    
    if (filters?.date_range) {
      query = query
        .gte('created_at', filters.date_range.start)
        .lte('created_at', filters.date_range.end)
    }

    return this.withErrorHandling(() => query)
  }

  async createShipment(shipmentData: ShipmentInsert) {
    return this.withErrorHandling(async () => {
      // Create shipment with initial status
      const shipment = await this.client
        .from('shipments')
        .insert(shipmentData)
        .select()
        .single()

      // Create initial status history
      if (shipment.data) {
        await this.client
          .from('status_history')
          .insert({
            shipment_id: shipment.data.id,
            status_to: 'booking_confirmed',
            notes: 'Shipment created'
          })
      }

      return shipment
    })
  }

  async updateShipmentStatus(
    shipmentId: string, 
    newStatus: string, 
    notes?: string,
    location?: { latitude: number; longitude: number }
  ) {
    return this.withErrorHandling(async () => {
      // Get current status
      const { data: currentShipment } = await this.client
        .from('shipments')
        .select('status')
        .eq('id', shipmentId)
        .single()

      if (!currentShipment) {
        throw new Error('Shipment not found')
      }

      // Update shipment status
      const shipment = await this.client
        .from('shipments')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', shipmentId)
        .select()
        .single()

      // Record status history
      await this.client
        .from('status_history')
        .insert({
          shipment_id: shipmentId,
          status_from: currentShipment.status,
          status_to: newStatus,
          notes,
          latitude: location?.latitude,
          longitude: location?.longitude
        })

      // Trigger real-time notification
      await this.client.functions.invoke('notifications/status-change', {
        body: {
          shipment_id: shipmentId,
          old_status: currentShipment.status,
          new_status: newStatus,
          notes
        }
      })

      return shipment
    })
  }

  // Real-time subscription for shipment updates
  subscribeToShipmentUpdates(shipmentId: string, callback: (payload: any) => void) {
    return this.subscribeToTable(
      'shipments',
      `id=eq.${shipmentId}`,
      callback
    )
  }
}
```

## Authentication and Authorization

### Auth Flow

```mermaid
sequenceDiagram
    participant User
    participant NextJS
    participant Supabase
    participant Database
    
    User->>NextJS: Login Request
    NextJS->>Supabase: Auth.signIn()
    Supabase->>Database: Validate credentials
    Database->>Supabase: User data
    Supabase->>NextJS: Session + JWT
    NextJS->>Database: Get user profile
    Database->>NextJS: Profile with role/company
    NextJS->>User: Redirect to dashboard
    
    Note over User,Database: Subsequent requests
    User->>NextJS: Protected route access
    NextJS->>Supabase: Verify session
    Supabase->>NextJS: Valid session
    NextJS->>Database: Query with RLS
    Database->>NextJS: Filtered data
    NextJS->>User: Authorized content
```

### Middleware/Guards

```typescript
// lib/auth/auth-guard.ts
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

export async function requireAuth() {
  const supabase = createServerComponentClient({ cookies })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect('/login')
  }

  // Get user profile with role
  const { data: profile } = await supabase
    .from('profiles')
    .select('role, company_id, is_active')
    .eq('user_id', session.user.id)
    .single()

  if (!profile || !profile.is_active) {
    redirect('/login?error=inactive_account')
  }

  return { session, profile }
}

export async function requireRole(allowedRoles: string[]) {
  const { session, profile } = await requireAuth()
  
  if (!allowedRoles.includes(profile.role)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
  
  return { session, profile }
}

// Role-based component wrapper
export function withRole<T extends object>(
  Component: React.ComponentType<T>,
  allowedRoles: string[]
) {
  return async function ProtectedComponent(props: T) {
    await requireRole(allowedRoles)
    return <Component {...props} />
  }
}
```
