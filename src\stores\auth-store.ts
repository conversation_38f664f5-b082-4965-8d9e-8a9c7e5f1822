import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, Session } from '@supabase/supabase-js'
import type { UserProfile } from '@/lib/supabase/auth'

interface AuthState {
  user: User | null
  profile: UserProfile | null
  session: Session | null
  loading: boolean
  error: string | null
  lastError: string | null
  retryCount: number
  isRecovering: boolean
}

interface AuthActions {
  setAuth: (
    user: User | null,
    profile: UserProfile | null,
    session: Session | null
  ) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  updateProfile: (profile: UserProfile) => void
  clearAuth: () => void
  reset: () => void
  setRecovering: (isRecovering: boolean) => void
  incrementRetry: () => void
  resetRetry: () => void
}

type AuthStore = AuthState & AuthActions

const initialState: AuthState = {
  user: null,
  profile: null,
  session: null,
  loading: true,
  error: null,
  lastError: null,
  retryCount: 0,
  isRecovering: false,
}

export const useAuthStore = create<AuthStore>()((set, get) => ({
  ...initialState,

  setAuth: (user, profile, session) => {
    console.log('AuthStore: setAuth called with:', {
      user: !!user,
      profile: !!profile,
      session: !!session,
    })
    set({
      user,
      profile,
      session,
      loading: false,
      error: null,
    })
  },

  setLoading: loading => {
    console.log('AuthStore: setLoading called with:', loading)
    set({ loading })
  },

  setError: error => {
    console.log('AuthStore: setError called with:', error)
    set({ error, lastError: error, loading: false })
  },

  updateProfile: profile => {
    console.log('AuthStore: updateProfile called with:', profile)
    set({ profile })
  },

  clearAuth: () => {
    console.log('AuthStore: clearAuth called')
    set({
      user: null,
      profile: null,
      session: null,
      loading: false,
      error: null,
    })
  },

  reset: () => {
    console.log('AuthStore: reset called')
    set(initialState)
  },

  setRecovering: (isRecovering) => {
    console.log('AuthStore: setRecovering called with:', isRecovering)
    set({ isRecovering })
  },

  incrementRetry: () => {
    const { retryCount } = get()
    console.log('AuthStore: incrementRetry called, current count:', retryCount)
    set({ retryCount: retryCount + 1 })
  },

  resetRetry: () => {
    console.log('AuthStore: resetRetry called')
    set({ retryCount: 0 })
  },
}))

// Selectors for commonly used computed values
export const useIsAuthenticated = () => useAuthStore(state => !!state.user)
export const useIsAdmin = () =>
  useAuthStore(state => state.profile?.role === 'admin')
export const useIsStaff = () =>
  useAuthStore(
    state =>
      state.profile?.role &&
      ['admin', 'cs', 'account'].includes(state.profile.role)
  )
export const useUserRole = () => useAuthStore(state => state.profile?.role)
export const useUserProfile = () => useAuthStore(state => state.profile)
export const useAuthLoading = () => useAuthStore(state => state.loading)
export const useAuthError = () => useAuthStore(state => state.error)
