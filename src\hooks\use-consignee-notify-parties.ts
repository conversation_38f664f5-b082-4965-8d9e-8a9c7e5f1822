'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import {
  useConsigneeNotifyPartyStore,
  useConsigneeNotifyPartiesData,
  useConsigneeNotifyPartiesActions,
  useConsigneeNotifyPartiesSelection,
  type ConsigneeNotifyParty,
} from '@/stores/consignee-notify-party-store'
import type {
  ConsigneeNotifyPartyForm,
  ConsigneeNotifyPartyFilter,
} from '@/lib/validations/consignee-notify-parties'

// Main hook for consignee-notify parties management
export const useConsigneeNotifyPartiesManagement = () => {
  const data = useConsigneeNotifyPartiesData()
  const actions = useConsigneeNotifyPartiesActions()
  const selection = useConsigneeNotifyPartiesSelection()

  // Get filter and search state from store
  const filter = useConsigneeNotifyPartyStore(state => state.filter)
  const searchTerm = useConsigneeNotifyPartyStore(state => state.searchTerm)
  const sortBy = useConsigneeNotifyPartyStore(state => state.sortBy)
  const sortOrder = useConsigneeNotifyPartyStore(state => state.sortOrder)
  const isCreating = useConsigneeNotifyPartyStore(state => state.isCreating)
  const isUpdating = useConsigneeNotifyPartyStore(state => state.isUpdating)
  const isDeleting = useConsigneeNotifyPartyStore(state => state.isDeleting)

  // Initialize data on mount
  useEffect(() => {
    console.log('🚀 Initializing consignee-notify parties management...')
    actions.fetchConsigneeNotifyParties()
  }, []) // Remove actions from dependency array to prevent infinite loop

  return {
    // Data
    consigneeNotifyParties: data.consigneeNotifyParties,
    loading: data.loading,
    error: data.error,

    // Pagination
    currentPage: data.currentPage,
    totalPages: data.totalPages,
    totalCount: data.totalCount,
    hasNextPage: data.hasNextPage,
    hasPreviousPage: data.hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedConsigneeNotifyParties: selection.selectedConsigneeNotifyParties,
    selectedCount: selection.selectedCount,
    isSelected: selection.isSelected,
    isAllSelected: selection.isAllSelected,
    isPartiallySelected: selection.isPartiallySelected,

    // CRUD operations
    createConsigneeNotifyParty: actions.createConsigneeNotifyParty,
    updateConsigneeNotifyParty: actions.updateConsigneeNotifyParty,
    deleteConsigneeNotifyParty: actions.deleteConsigneeNotifyParty,
    bulkDeleteConsigneeNotifyParties: actions.bulkDeleteConsigneeNotifyParties,
    isCreating,
    isUpdating,
    isDeleting,

    // Default notify party management
    setDefaultNotifyParty: actions.setDefaultNotifyParty,
    getDefaultNotifyParty: actions.getDefaultNotifyParty,

    // Actions
    setFilter: actions.setFilter,
    setSearchTerm: actions.setSearchTerm,
    setSorting: actions.setSorting,
    clearFilters: actions.clearFilters,
    setPage: actions.setPage,
    nextPage: actions.nextPage,
    previousPage: actions.previousPage,
    toggleConsigneeNotifyParty: actions.toggleConsigneeNotifyParty,
    toggleAll: actions.toggleAll,
    clearSelection: actions.clearSelection,
    clearError: actions.clearError,
    refreshConsigneeNotifyParties: actions.refreshConsigneeNotifyParties,
  }
}

// Hook for consignee selection (filtered for consignees only)
export const useConsigneeOptions = () => {
  const [consignees, setConsignees] = useState<
    Array<{
      id: string
      name: string
      company_type: string
      contact_phone: string | null
    }>
  >([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchConsignees = async () => {
    console.log('🔍 Fetching consignees...')
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      console.log('📡 Supabase client created')

      const { data, error } = await supabase
        .from('companies')
        .select('id, name, company_type, contact_phone')
        .eq('company_type', 'consignee')
        .eq('is_active', true)
        .order('name')

      console.log('📊 Consignees query result:', { data, error })

      if (error) throw error

      setConsignees(data || [])
      console.log('✅ Consignees set:', data?.length || 0, 'items')
    } catch (error) {
      console.error('❌ Error fetching consignees:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to load consignees'
      )
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchConsignees()
  }, [])

  return { consignees, loading, error, refetch: fetchConsignees }
}

// Hook for notify party selection (filtered for notify parties only)
export const useNotifyPartyOptions = () => {
  const [notifyParties, setNotifyParties] = useState<
    Array<{
      id: string
      name: string
      company_type: string
      contact_phone: string | null
      contact_email: string | null
    }>
  >([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchNotifyParties = async () => {
    console.log('🔍 Fetching notify parties...')
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      console.log('📡 Supabase client created for notify parties')

      const { data, error } = await supabase
        .from('companies')
        .select('id, name, company_type, contact_phone, contact_email')
        .eq('company_type', 'notify_party')
        .eq('is_active', true)
        .order('name')

      console.log('📊 Notify parties query result:', { data, error })

      if (error) throw error

      setNotifyParties(data || [])
      console.log('✅ Notify parties set:', data?.length || 0, 'items')
    } catch (error) {
      console.error('❌ Error fetching notify parties:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to load notify parties'
      )
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchNotifyParties()
  }, [])

  return { notifyParties, loading, error, refetch: fetchNotifyParties }
}

// Hook for checking if consignee has existing relationships
export const useConsigneeNotifyPartyCheck = (consigneeId?: string) => {
  const [hasRelationships, setHasRelationships] = useState(false)
  const [defaultNotifyParty, setDefaultNotifyParty] =
    useState<ConsigneeNotifyParty | null>(null)
  const [relationshipCount, setRelationshipCount] = useState(0)
  const [loading, setLoading] = useState(false)

  const checkConsignee = async (id: string) => {
    setLoading(true)

    try {
      const supabase = createClient()

      // Check for any relationships
      const { count } = await supabase
        .from('consignee_notify_parties')
        .select('*', { count: 'exact', head: true })
        .eq('consignee_id', id)
        .eq('is_active', true)

      const relationshipCount = count || 0
      setHasRelationships(relationshipCount > 0)
      setRelationshipCount(relationshipCount)

      // Get default notify party if exists
      const { data: defaultRelationship, error: defaultError } = await supabase
        .from('consignee_notify_parties')
        .select('*')
        .eq('consignee_id', id)
        .eq('is_default', true)
        .eq('is_active', true)
        .single()

      if (defaultError && defaultError.code !== 'PGRST116') throw defaultError

      if (defaultRelationship) {
        // Get company data for this relationship
        const { data: companyData, error: companyError } = await supabase
          .from('companies')
          .select('id, name, company_type, contact_phone, contact_email')
          .in('id', [defaultRelationship.consignee_id, defaultRelationship.notify_party_id])

        if (companyError) throw companyError

        const companies = companyData || []
        const consignee = companies.find(c => c.id === defaultRelationship.consignee_id)
        const notifyParty = companies.find(c => c.id === defaultRelationship.notify_party_id)

        setDefaultNotifyParty({
          ...defaultRelationship,
          consignee,
          notify_party: notifyParty
        })
      } else {
        setDefaultNotifyParty(null)
      }
    } catch (error) {
      console.error('Error checking consignee relationships:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (consigneeId) {
      checkConsignee(consigneeId)
    } else {
      setHasRelationships(false)
      setDefaultNotifyParty(null)
      setRelationshipCount(0)
    }
  }, [consigneeId])

  return {
    hasRelationships,
    defaultNotifyParty,
    relationshipCount,
    loading,
    refetch: consigneeId ? () => checkConsignee(consigneeId) : undefined,
  }
}

// Hook for real-time consignee-notify party updates (for shipment integration)
export const useConsigneeNotifyPartiesRealtime = (consigneeId?: string) => {
  const [consigneeNotifyParties, setConsigneeNotifyParties] = useState<
    ConsigneeNotifyParty[]
  >([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!consigneeId) {
      setConsigneeNotifyParties([])
      return
    }

    setLoading(true)

    const supabase = createClient()

    // Initial fetch
    const fetchConsigneeNotifyParties = async () => {
      try {
        // First, get the basic consignee_notify_parties data
        const { data: relationshipData, error: relationshipError } = await supabase
          .from('consignee_notify_parties')
          .select('*')
          .eq('consignee_id', consigneeId)
          .eq('is_active', true)
          .order('is_default', { ascending: false })
          .order('priority_order', { ascending: true })

        if (relationshipError) throw relationshipError

        if (!relationshipData || relationshipData.length === 0) {
          setConsigneeNotifyParties([])
          return
        }

        // Get unique company IDs for batch fetching
        const consigneeIds = [...new Set(relationshipData.map(r => r.consignee_id))]
        const notifyPartyIds = [...new Set(relationshipData.map(r => r.notify_party_id))]
        const allCompanyIds = [...consigneeIds, ...notifyPartyIds]

        // Fetch all companies in one query
        const { data: companies, error: companiesError } = await supabase
          .from('companies')
          .select('id, name, company_type, contact_phone, contact_email')
          .in('id', allCompanyIds)

        if (companiesError) throw companiesError

        // Map companies to lookup object for faster access
        const companyLookup = companies?.reduce((acc, company) => {
          acc[company.id] = company
          return acc
        }, {} as Record<string, any>) || {}

        // Combine relationship data with company data
        const enrichedData = relationshipData.map(relationship => ({
          ...relationship,
          consignee: companyLookup[relationship.consignee_id] || null,
          notify_party: companyLookup[relationship.notify_party_id] || null
        }))

        setConsigneeNotifyParties(enrichedData)
      } catch (error) {
        console.error('Error fetching consignee notify parties:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchConsigneeNotifyParties()

    // Set up real-time subscription
    const subscription = supabase
      .channel(`consignee_notify_parties:${consigneeId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'consignee_notify_parties',
          filter: `consignee_id=eq.${consigneeId}`,
        },
        () => {
          // Refetch data when changes occur
          fetchConsigneeNotifyParties()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [consigneeId])

  return { consigneeNotifyParties, loading }
}

// Hook for relationship validation
export function useConsigneeNotifyPartyValidation() {
  const [loading, setLoading] = useState(false)

  // Validate unique relationship (no duplicate consignee-notify party combinations)
  const validateUniqueRelationship = async (
    consigneeId: string,
    notifyPartyId: string,
    excludeId?: string
  ): Promise<boolean> => {
    try {
      const supabase = createClient()

      let query = supabase
        .from('consignee_notify_parties')
        .select('id')
        .eq('consignee_id', consigneeId)
        .eq('notify_party_id', notifyPartyId)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      const { data, error } = await query.single()

      if (error && error.code !== 'PGRST116') throw error // PGRST116 = no rows returned

      return !data // Return true if no duplicate exists
    } catch (error) {
      console.error('Error validating relationship uniqueness:', error)
      return false
    }
  }

  // Validate company types
  const validateCompanyTypes = async (
    consigneeId: string,
    notifyPartyId: string
  ): Promise<{
    isValid: boolean
    consigneeType?: string
    notifyPartyType?: string
    error?: string
  }> => {
    setLoading(true)

    try {
      const supabase = createClient()

      // Get both companies
      const { data: companies, error } = await supabase
        .from('companies')
        .select('id, company_type')
        .in('id', [consigneeId, notifyPartyId])

      if (error) throw error

      const consignee = companies?.find(c => c.id === consigneeId)
      const notifyParty = companies?.find(c => c.id === notifyPartyId)

      if (!consignee) {
        return { isValid: false, error: 'Consignee company not found' }
      }

      if (!notifyParty) {
        return { isValid: false, error: 'Notify party company not found' }
      }

      const isValid =
        consignee.company_type === 'consignee' &&
        notifyParty.company_type === 'notify_party'

      return {
        isValid,
        consigneeType: consignee.company_type,
        notifyPartyType: notifyParty.company_type,
        error: isValid
          ? undefined
          : 'Companies must have correct types (consignee and notify_party)',
      }
    } catch (error) {
      console.error('Error validating company types:', error)
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Validation failed',
      }
    } finally {
      setLoading(false)
    }
  }

  // Check if setting as default would conflict with existing defaults
  const validateDefaultDesignation = async (
    consigneeId: string,
    relationshipId?: string
  ): Promise<{
    isValid: boolean
    existingDefault?: ConsigneeNotifyParty
    error?: string
  }> => {
    try {
      const supabase = createClient()

      let query = supabase
        .from('consignee_notify_parties')
        .select('*')
        .eq('consignee_id', consigneeId)
        .eq('is_default', true)
        .eq('is_active', true)

      if (relationshipId) {
        query = query.neq('id', relationshipId)
      }

      const { data: existingRelationship, error } = await query.single()

      if (error && error.code !== 'PGRST116') throw error

      if (existingRelationship) {
        // Get notify party company data
        const { data: notifyPartyData, error: companyError } = await supabase
          .from('companies')
          .select('id, name, company_type, contact_phone, contact_email')
          .eq('id', existingRelationship.notify_party_id)
          .single()

        if (companyError) throw companyError

        const enrichedDefault = {
          ...existingRelationship,
          notify_party: notifyPartyData
        }

        return {
          isValid: false,
          existingDefault: enrichedDefault,
          error: 'Another notify party is already set as default for this consignee',
        }
      }

      return {
        isValid: true,
        existingDefault: undefined,
        error: undefined,
      }
    } catch (error) {
      console.error('Error validating default designation:', error)
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Validation failed',
      }
    }
  }

  // Validate notification preferences (at least one channel enabled)
  const validateNotificationPreferences = (preferences: {
    email: boolean
    sms: boolean
    line: boolean
    wechat: boolean
  }): { isValid: boolean; error?: string } => {
    const hasChannel = Object.values(preferences).some(
      enabled => enabled === true
    )

    return {
      isValid: hasChannel,
      error: hasChannel
        ? undefined
        : 'At least one notification channel must be enabled',
    }
  }

  // Comprehensive validation for form submission
  const validateRelationshipForm = async (
    data: ConsigneeNotifyPartyForm,
    relationshipId?: string
  ): Promise<{
    isValid: boolean
    errors: string[]
  }> => {
    const errors: string[] = []

    // Check company types
    const companyValidation = await validateCompanyTypes(
      data.consignee_id,
      data.notify_party_id
    )
    if (!companyValidation.isValid) {
      errors.push(companyValidation.error || 'Invalid company types')
    }

    // Check unique relationship
    const isUniqueRelationship = await validateUniqueRelationship(
      data.consignee_id,
      data.notify_party_id,
      relationshipId
    )
    if (!isUniqueRelationship) {
      errors.push('A relationship between these companies already exists')
    }

    // Check notification preferences
    const prefValidation = validateNotificationPreferences(
      data.notification_preferences
    )
    if (!prefValidation.isValid) {
      errors.push(prefValidation.error || 'Invalid notification preferences')
    }

    // Check default designation if being set as default
    if (data.is_default) {
      const defaultValidation = await validateDefaultDesignation(
        data.consignee_id,
        relationshipId
      )
      if (!defaultValidation.isValid) {
        errors.push(defaultValidation.error || 'Cannot set as default')
      }
    }

    // Validate that consignee and notify party are not the same
    if (data.consignee_id === data.notify_party_id) {
      errors.push('Consignee and notify party cannot be the same company')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  return {
    validateUniqueRelationship,
    validateCompanyTypes,
    validateDefaultDesignation,
    validateNotificationPreferences,
    validateRelationshipForm,
    loading,
  }
}

// Hook for priority management utilities
export const useConsigneePriorityManagement = (consigneeId?: string) => {
  const [priorities, setPriorities] = useState<
    Array<{ priority_order: number; count: number }>
  >([])
  const [loading, setLoading] = useState(false)

  const fetchPriorityDistribution = async (id: string) => {
    setLoading(true)

    try {
      const supabase = createClient()

      const { data, error } = await supabase
        .from('consignee_notify_parties')
        .select('priority_order')
        .eq('consignee_id', id)
        .eq('is_active', true)

      if (error) throw error

      // Count occurrences of each priority
      const priorityCount = (data || []).reduce(
        (acc, item) => {
          const priority = item.priority_order || 1
          acc[priority] = (acc[priority] || 0) + 1
          return acc
        },
        {} as Record<number, number>
      )

      // Convert to array format
      const distribution = Object.entries(priorityCount)
        .map(([priority, count]) => ({
          priority_order: parseInt(priority),
          count: count as number,
        }))
        .sort((a, b) => a.priority_order - b.priority_order)

      setPriorities(distribution)
    } catch (error) {
      console.error('Error fetching priority distribution:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (consigneeId) {
      fetchPriorityDistribution(consigneeId)
    } else {
      setPriorities([])
    }
  }, [consigneeId])

  const getNextAvailablePriority = (): number => {
    if (priorities.length === 0) return 1

    // Find the first gap in priorities, or return next highest
    const sortedPriorities = priorities
      .map(p => p.priority_order)
      .sort((a, b) => a - b)

    for (let i = 1; i <= sortedPriorities.length + 1; i++) {
      if (!sortedPriorities.includes(i)) {
        return i
      }
    }

    return sortedPriorities.length + 1
  }

  const getSuggestedPriority = (): number => {
    // For new relationships, suggest the next logical priority
    return getNextAvailablePriority()
  }

  return {
    priorities,
    loading,
    getNextAvailablePriority,
    getSuggestedPriority,
    refetch: consigneeId
      ? () => fetchPriorityDistribution(consigneeId)
      : undefined,
  }
}
