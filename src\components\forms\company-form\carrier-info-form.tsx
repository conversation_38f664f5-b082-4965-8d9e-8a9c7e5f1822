'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Truck,
  Shield,
  Clock,
  Phone,
  MapPin,
  Package,
  Scale,
  Box,
  Plus,
  X,
  Navigation,
  Calendar,
} from 'lucide-react'
import type { CarrierInfo } from '@/lib/validations/companies'
import { LICENSE_TYPES, COVERAGE_AREAS } from '@/lib/validations/companies'

interface CarrierInfoFormProps {
  value?: Partial<CarrierInfo>
  onChange: (info: Partial<CarrierInfo>) => void
  errors?: any
}

export function CarrierInfoForm({
  value = {},
  onChange,
  errors = {},
}: CarrierInfoFormProps) {
  const [newLicenseType, setNewLicenseType] = useState('')
  const [newCoverageArea, setNewCoverageArea] = useState('')

  const updateField = <K extends keyof CarrierInfo>(
    field: K,
    fieldValue: CarrierInfo[K]
  ) => {
    onChange({ ...value, [field]: fieldValue })
  }

  const addLicenseType = (type: string) => {
    const current = value.license_types || []
    if (type && !current.includes(type)) {
      updateField('license_types', [...current, type])
    }
    setNewLicenseType('')
  }

  const removeLicenseType = (type: string) => {
    const current = value.license_types || []
    updateField(
      'license_types',
      current.filter(t => t !== type)
    )
  }

  const addCoverageArea = (area: string) => {
    const current = value.coverage_areas || []
    if (area && !current.includes(area)) {
      updateField('coverage_areas', [...current, area])
    }
    setNewCoverageArea('')
  }

  const removeCoverageArea = (area: string) => {
    const current = value.coverage_areas || []
    updateField(
      'coverage_areas',
      current.filter(a => a !== area)
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2 pb-2 border-b border-slate-600">
        <Truck className="h-5 w-5 text-green-500" />
        <h3 className="text-lg font-semibold text-white">Carrier Details</h3>
        <Badge
          variant="secondary"
          className="bg-green-500/20 text-green-300 border-green-400"
        >
          Fleet Information
        </Badge>
      </div>

      {/* Basic Carrier Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Carrier Code */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Package className="h-4 w-4 text-blue-500" />
            <span>Carrier Code</span>
          </Label>
          <Input
            placeholder="e.g., CAR001, TRANS-TH"
            value={value.carrier_code || ''}
            onChange={e => updateField('carrier_code', e.target.value)}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.carrier_code && (
            <p className="text-sm text-red-400">
              {errors.carrier_code.message}
            </p>
          )}
          <p className="text-xs text-slate-400">
            Unique identifier for this carrier
          </p>
        </div>

        {/* Fleet Size */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Truck className="h-4 w-4 text-green-500" />
            <span>Fleet Size</span>
          </Label>
          <Input
            type="number"
            placeholder="0"
            min="0"
            value={value.fleet_size || ''}
            onChange={e =>
              updateField('fleet_size', parseInt(e.target.value) || 0)
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-500 focus:ring-green-500"
          />
          {errors.fleet_size && (
            <p className="text-sm text-red-400">{errors.fleet_size.message}</p>
          )}
          <p className="text-xs text-slate-400">
            Total number of vehicles in fleet
          </p>
        </div>
      </div>

      {/* Capacity Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Max Weight Capacity */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Scale className="h-4 w-4 text-yellow-500" />
            <span>Max Weight Capacity (tons)</span>
          </Label>
          <Input
            type="number"
            placeholder="0.00"
            min="0"
            step="0.01"
            value={value.max_weight_capacity || ''}
            onChange={e =>
              updateField(
                'max_weight_capacity',
                parseFloat(e.target.value) || 0
              )
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-yellow-500 focus:ring-yellow-500"
          />
          {errors.max_weight_capacity && (
            <p className="text-sm text-red-400">
              {errors.max_weight_capacity.message}
            </p>
          )}
        </div>

        {/* Max Volume Capacity */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Box className="h-4 w-4 text-purple-500" />
            <span>Max Volume Capacity (m³)</span>
          </Label>
          <Input
            type="number"
            placeholder="0.00"
            min="0"
            step="0.01"
            value={value.max_volume_capacity || ''}
            onChange={e =>
              updateField(
                'max_volume_capacity',
                parseFloat(e.target.value) || 0
              )
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-purple-500"
          />
          {errors.max_volume_capacity && (
            <p className="text-sm text-red-400">
              {errors.max_volume_capacity.message}
            </p>
          )}
        </div>
      </div>

      {/* License Types */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Shield className="h-4 w-4 text-blue-500" />
          <span>License Types</span>
        </Label>

        {/* Current License Types */}
        {value.license_types && value.license_types.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {value.license_types.map(type => (
              <Badge
                key={type}
                variant="secondary"
                className="bg-blue-500/20 text-blue-300 border-blue-400 flex items-center space-x-1"
              >
                <span>{type}</span>
                <button
                  type="button"
                  onClick={() => removeLicenseType(type)}
                  className="ml-1 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Add License Type */}
        <div className="flex space-x-2">
          <Select value={newLicenseType} onValueChange={setNewLicenseType}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-blue-500 focus:ring-blue-500">
              <SelectValue placeholder="Select license type" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {LICENSE_TYPES.filter(
                type => !value.license_types?.includes(type)
              ).map(type => (
                <SelectItem
                  key={type}
                  value={type}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            onClick={() => addLicenseType(newLicenseType)}
            disabled={!newLicenseType}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Coverage Areas */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <MapPin className="h-4 w-4 text-green-500" />
          <span>Coverage Areas</span>
        </Label>

        {/* Current Coverage Areas */}
        {value.coverage_areas && value.coverage_areas.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {value.coverage_areas.map(area => (
              <Badge
                key={area}
                variant="secondary"
                className="bg-green-500/20 text-green-300 border-green-400 flex items-center space-x-1"
              >
                <span>{area}</span>
                <button
                  type="button"
                  onClick={() => removeCoverageArea(area)}
                  className="ml-1 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Add Coverage Area */}
        <div className="flex space-x-2">
          <Select value={newCoverageArea} onValueChange={setNewCoverageArea}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-green-500 focus:ring-green-500">
              <SelectValue placeholder="Select coverage area" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {COVERAGE_AREAS.filter(
                area => !value.coverage_areas?.includes(area)
              ).map(area => (
                <SelectItem
                  key={area}
                  value={area}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  {area}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            onClick={() => addCoverageArea(newCoverageArea)}
            disabled={!newCoverageArea}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Insurance Information */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Shield className="h-4 w-4 text-red-500" />
          <span>Insurance Information</span>
        </Label>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Insurance Policy Number */}
          <div className="space-y-2">
            <Label className="text-slate-200">Policy Number</Label>
            <Input
              placeholder="Insurance policy number"
              value={value.insurance_policy_no || ''}
              onChange={e => updateField('insurance_policy_no', e.target.value)}
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-red-500 focus:ring-red-500"
            />
          </div>

          {/* Insurance Coverage Amount */}
          <div className="space-y-2">
            <Label className="text-slate-200">Coverage Amount (THB)</Label>
            <Input
              type="number"
              placeholder="0.00"
              min="0"
              step="0.01"
              value={value.insurance_coverage_amount || ''}
              onChange={e =>
                updateField(
                  'insurance_coverage_amount',
                  parseFloat(e.target.value) || 0
                )
              }
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-red-500 focus:ring-red-500"
            />
          </div>

          {/* Insurance Expiry Date */}
          <div className="space-y-2">
            <Label className="text-slate-200 flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-yellow-500" />
              <span>Expiry Date</span>
            </Label>
            <Input
              type="date"
              value={value.insurance_expiry_date || ''}
              onChange={e =>
                updateField('insurance_expiry_date', e.target.value)
              }
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-yellow-500 focus:ring-yellow-500"
            />
          </div>
        </div>
      </div>

      {/* Contact and Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Emergency Contact Phone */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Phone className="h-4 w-4 text-orange-500" />
            <span>Emergency Contact</span>
          </Label>
          <Input
            placeholder="+66 xx xxx xxxx"
            value={value.emergency_contact_phone || ''}
            onChange={e =>
              updateField('emergency_contact_phone', e.target.value)
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
          />
          {errors.emergency_contact_phone && (
            <p className="text-sm text-red-400">
              {errors.emergency_contact_phone.message}
            </p>
          )}
          <p className="text-xs text-slate-400">
            24/7 emergency contact number
          </p>
        </div>

        {/* GPS Tracking Available */}
        <div className="space-y-2">
          <div className="flex items-center justify-between rounded-lg border border-slate-600 p-4">
            <div className="space-y-0.5">
              <Label className="text-base text-slate-200 flex items-center space-x-2">
                <Navigation className="h-4 w-4 text-blue-500" />
                <span>GPS Tracking</span>
              </Label>
              <p className="text-sm text-slate-400">
                Fleet equipped with GPS tracking
              </p>
            </div>
            <Switch
              checked={value.gps_tracking_available || false}
              onCheckedChange={checked =>
                updateField('gps_tracking_available', checked)
              }
            />
          </div>
        </div>
      </div>

      {/* Operating Hours */}
      <div className="space-y-2">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Clock className="h-4 w-4 text-purple-500" />
          <span>Operating Hours</span>
        </Label>
        <Textarea
          placeholder={`Enter operating hours information, e.g.:
Weekdays: 08:00-18:00
Weekends: 09:00-15:00
24/7 Emergency Service: Available`}
          value={
            typeof value.operating_hours === 'string'
              ? value.operating_hours
              : JSON.stringify(value.operating_hours || {}, null, 2)
          }
          onChange={e => {
            try {
              const parsed = JSON.parse(e.target.value)
              updateField('operating_hours', parsed)
            } catch {
              updateField('operating_hours', e.target.value as any)
            }
          }}
          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-purple-500"
          rows={4}
        />
        <p className="text-xs text-slate-400">
          Describe normal operating hours and availability
        </p>
      </div>

      {/* Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Truck className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-slate-200">
              Fleet Management
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Track vehicle capacity and availability
          </p>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Shield className="h-4 w-4 text-red-500" />
            <span className="text-sm font-medium text-slate-200">
              Insurance
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Coverage and liability protection
          </p>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <MapPin className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-slate-200">Coverage</span>
          </div>
          <p className="text-xs text-slate-400">
            Service areas and regional coverage
          </p>
        </div>
      </div>
    </div>
  )
}
