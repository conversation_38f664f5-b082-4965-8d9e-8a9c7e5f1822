'use client'

import { Badge } from '@/components/ui/badge'
import { SHIPMENT_STATUSES } from '@/lib/validations/shipment'
import { STATUS_METADATA } from '@/lib/validations/status-updates'
import type { ShipmentStatus } from '@/lib/supabase/types'

interface StatusBadgeProps {
  status: ShipmentStatus
  size?: 'sm' | 'default' | 'lg'
  showIcon?: boolean
  className?: string
}

export function StatusBadge({
  status,
  size = 'default',
  showIcon = false,
  className = '',
}: StatusBadgeProps) {
  // Get status configuration
  const statusConfig = SHIPMENT_STATUSES.find(s => s.value === status) || {
    label: status,
    variant: 'default' as const,
    color: '#0ea5e9',
  }

  const statusMetadata = STATUS_METADATA[status]

  // Size-based styles
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5 h-5',
    default: 'text-xs px-2.5 py-1 h-6',
    lg: 'text-sm px-3 py-1.5 h-7',
  }

  // Terminal status indicator
  const isTerminal = statusMetadata?.isTerminal || false

  return (
    <div className={`inline-flex items-center space-x-1 ${className}`}>
      <Badge
        variant={statusConfig.variant}
        className={`${sizeClasses[size]} font-medium border transition-colors duration-200`}
        style={{
          backgroundColor: statusConfig.color + '20',
          color: statusConfig.color,
          borderColor: statusConfig.color + '40',
        }}
      >
        <div className="flex items-center space-x-1">
          {showIcon && (
            <div
              className="w-1.5 h-1.5 rounded-full flex-shrink-0"
              style={{ backgroundColor: statusConfig.color }}
            />
          )}
          <span>{statusConfig.label}</span>
          {isTerminal && size !== 'sm' && (
            <span className="text-xs opacity-75">●</span>
          )}
        </div>
      </Badge>
    </div>
  )
}

// Bulk status badges component for lists
interface StatusBadgeListProps {
  statuses: ShipmentStatus[]
  maxDisplay?: number
  size?: 'sm' | 'default' | 'lg'
  className?: string
}

export function StatusBadgeList({
  statuses,
  maxDisplay = 3,
  size = 'sm',
  className = '',
}: StatusBadgeListProps) {
  const displayStatuses = statuses.slice(0, maxDisplay)
  const remainingCount = statuses.length - maxDisplay

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {displayStatuses.map((status, index) => (
        <StatusBadge
          key={`${status}-${index}`}
          status={status}
          size={size}
          showIcon
        />
      ))}
      {remainingCount > 0 && (
        <Badge
          variant="secondary"
          className={`${
            size === 'sm' ? 'text-xs px-2 py-0.5 h-5' : 'text-xs px-2.5 py-1 h-6'
          } bg-slate-600/40 text-slate-400 border-slate-500/40`}
        >
          +{remainingCount}
        </Badge>
      )}
    </div>
  )
}

// Status progress indicator
interface StatusProgressProps {
  currentStatus: ShipmentStatus
  className?: string
}

export function StatusProgress({
  currentStatus,
  className = '',
}: StatusProgressProps) {
  // Define status progression order
  const statusOrder: ShipmentStatus[] = [
    'booking_confirmed',
    'transport_assigned',
    'driver_assigned',
    'empty_container_picked',
    'arrived_at_factory',
    'loading_started',
    'departed_factory',
    'container_returned',
    'shipped',
    'arrived',
    'completed',
  ]

  const currentIndex = statusOrder.indexOf(currentStatus)
  const progressPercentage = currentIndex >= 0 ? ((currentIndex + 1) / statusOrder.length) * 100 : 0

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Progress Bar */}
      <div className="flex items-center space-x-2">
        <span className="text-xs text-slate-400 min-w-0">Progress</span>
        <div className="flex-1 h-2 bg-slate-700 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-orange-500 to-green-500 transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <span className="text-xs text-slate-400 min-w-fit">
          {Math.round(progressPercentage)}%
        </span>
      </div>

      {/* Current Status */}
      <StatusBadge
        status={currentStatus}
        size="default"
        showIcon
      />
    </div>
  )
}

// Status filter component
interface StatusFilterProps {
  selectedStatuses: ShipmentStatus[]
  onStatusToggle: (status: ShipmentStatus) => void
  className?: string
}

export function StatusFilter({
  selectedStatuses,
  onStatusToggle,
  className = '',
}: StatusFilterProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      <h4 className="text-sm font-medium text-slate-200">Filter by Status</h4>
      <div className="grid grid-cols-2 gap-2">
        {SHIPMENT_STATUSES.map(({ value, label, color }) => {
          const isSelected = selectedStatuses.includes(value)
          
          return (
            <button
              key={value}
              onClick={() => onStatusToggle(value)}
              className={`flex items-center space-x-2 p-2 rounded-lg border transition-colors duration-200 ${
                isSelected
                  ? 'bg-slate-700 border-slate-500'
                  : 'bg-slate-800 border-slate-600 hover:bg-slate-700'
              }`}
            >
              <div
                className={`w-3 h-3 rounded-full flex-shrink-0 ${
                  isSelected ? 'ring-2 ring-offset-2 ring-offset-slate-800' : ''
                }`}
                style={{
                  backgroundColor: isSelected ? color : color + '60',
                  ringColor: isSelected ? color + '80' : undefined,
                }}
              />
              <span className={`text-xs truncate ${
                isSelected ? 'text-slate-200' : 'text-slate-400'
              }`}>
                {label}
              </span>
            </button>
          )
        })}
      </div>
    </div>
  )
}