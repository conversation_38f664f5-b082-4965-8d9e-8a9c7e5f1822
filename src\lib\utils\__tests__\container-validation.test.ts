import { describe, it, expect } from 'vitest'
import {
  calculateCheckDigit,
  formatContainerNumber,
  validateContainerNumber,
  validateSealNumber,
  getContainerNumberExamples,
  getSealNumberExamples,
  validateContainerNumberRealTime,
  validateSealNumberRealTime
} from '../container-validation'

describe('Container Number Validation', () => {
  describe('calculateCheckDigit', () => {
    it('should calculate correct check digit for valid input', () => {
      expect(calculateCheckDigit('ABCD123456')).toBe(0)
      expect(calculateCheckDigit('MSKU123456')).toBe(5)
      expect(calculateCheckDigit('GESU123456')).toBe(4)
    })

    it('should throw error for invalid length', () => {
      expect(() => calculateCheckDigit('ABCD123')).toThrow('Owner code and serial must be exactly 10 characters')
      expect(() => calculateCheckDigit('ABCD12345678')).toThrow('Owner code and serial must be exactly 10 characters')
    })

    it('should throw error for invalid characters', () => {
      expect(() => calculateCheckDigit('AB1D123456')).toThrow('Invalid character')
      expect(() => calculateCheckDigit('ABCD12345A')).toThrow('Invalid digit')
    })
  })

  describe('formatContainerNumber', () => {
    it('should format container number with spaces', () => {
      expect(formatContainerNumber('ABCD1234567')).toBe('ABCD 123456 7')
      expect(formatContainerNumber('abcd1234567')).toBe('ABCD 123456 7')
    })

    it('should handle input with existing spaces and special characters', () => {
      expect(formatContainerNumber('ABCD-123456-7')).toBe('ABCD 123456 7')
      expect(formatContainerNumber('ABCD 123456 7')).toBe('ABCD 123456 7')
    })

    it('should return cleaned input for incomplete numbers', () => {
      expect(formatContainerNumber('ABCD123')).toBe('ABCD123')
      expect(formatContainerNumber('AB')).toBe('AB')
    })
  })

  describe('validateContainerNumber', () => {
    it('should validate correct container numbers', () => {
      const result = validateContainerNumber('ABCD1234560')
      expect(result.isValid).toBe(true)
      expect(result.checkDigitValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.formattedNumber).toBe('ABCD 123456 0')
    })

    it('should reject empty input', () => {
      const result = validateContainerNumber('')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Container number is required')
    })

    it('should reject incorrect length', () => {
      const result = validateContainerNumber('ABCD123456')
      expect(result.isValid).toBe(false)
      expect(result.errors[0]).toContain('must be 11 characters')
    })

    it('should reject invalid format', () => {
      const result = validateContainerNumber('ABC1234567A')
      expect(result.isValid).toBe(false)
      expect(result.errors[0]).toContain('must follow format: 4 letters + 7 digits')
    })

    it('should reject invalid check digit', () => {
      const result = validateContainerNumber('ABCD1234561') // Should be 0, not 1
      expect(result.isValid).toBe(false)
      expect(result.checkDigitValid).toBe(false)
      expect(result.errors[0]).toContain('Invalid check digit')
    })

    it('should handle input with spaces and formatting', () => {
      const result = validateContainerNumber('ABCD 123456 0')
      expect(result.isValid).toBe(true)
      expect(result.checkDigitValid).toBe(true)
    })
  })

  describe('validateSealNumber', () => {
    it('should validate numeric seal numbers', () => {
      const result = validateSealNumber('123456789')
      expect(result.isValid).toBe(true)
      expect(result.detectedFormat).toBe('numeric')
      expect(result.errors).toHaveLength(0)
    })

    it('should validate alphanumeric seal numbers', () => {
      const result = validateSealNumber('AB123456')
      expect(result.isValid).toBe(true)
      expect(result.detectedFormat).toBe('alphanumeric')
    })

    it('should validate ISO format seal numbers', () => {
      const result = validateSealNumber('ABC123456789')
      expect(result.isValid).toBe(true)
      expect(result.detectedFormat).toBe('iso')
    })

    it('should reject empty input', () => {
      const result = validateSealNumber('')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Seal number is required')
    })

    it('should reject too short seal numbers', () => {
      const result = validateSealNumber('12')
      expect(result.isValid).toBe(false)
      expect(result.errors[0]).toContain('must be between 3-15 characters')
    })

    it('should reject too long seal numbers', () => {
      const result = validateSealNumber('1234567890123456')
      expect(result.isValid).toBe(false)
      expect(result.errors[0]).toContain('must be between 3-15 characters')
    })

    it('should reject invalid characters', () => {
      const result = validateSealNumber('ABC123@#$')
      expect(result.isValid).toBe(false)
      expect(result.errors[0]).toContain('invalid characters')
    })

    it('should handle case insensitive input', () => {
      const result = validateSealNumber('ab123456')
      expect(result.isValid).toBe(true)
      expect(result.detectedFormat).toBe('alphanumeric')
    })
  })

  describe('getContainerNumberExamples', () => {
    it('should return valid example container numbers', () => {
      const examples = getContainerNumberExamples()
      expect(examples).toHaveLength(3)
      
      examples.forEach(example => {
        const validation = validateContainerNumber(example)
        expect(validation.isValid).toBe(true)
        expect(validation.checkDigitValid).toBe(true)
      })
    })
  })

  describe('getSealNumberExamples', () => {
    it('should return valid example seal numbers', () => {
      const examples = getSealNumberExamples()
      expect(examples).toHaveLength(3)
      
      examples.forEach(example => {
        const validation = validateSealNumber(example)
        expect(validation.isValid).toBe(true)
      })
    })
  })

  describe('validateContainerNumberRealTime', () => {
    it('should return validation when input changes', () => {
      const result = validateContainerNumberRealTime('ABCD1234560')
      expect(result).not.toBeNull()
      expect(result?.isValid).toBe(true)
    })

    it('should return null when validation is same as previous', () => {
      const previous = validateContainerNumber('ABCD1234560')
      const result = validateContainerNumberRealTime('ABCD1234560', previous)
      expect(result).toBeNull()
    })

    it('should return validation when errors change', () => {
      const previous = validateContainerNumber('ABCD1234560')
      const result = validateContainerNumberRealTime('ABCD1234561', previous)
      expect(result).not.toBeNull()
      expect(result?.isValid).toBe(false)
    })
  })

  describe('validateSealNumberRealTime', () => {
    it('should return validation when input changes', () => {
      const result = validateSealNumberRealTime('123456789')
      expect(result).not.toBeNull()
      expect(result?.isValid).toBe(true)
    })

    it('should return null when validation is same as previous', () => {
      const previous = validateSealNumber('123456789')
      const result = validateSealNumberRealTime('123456789', previous)
      expect(result).toBeNull()
    })

    it('should return validation when format changes', () => {
      const previous = validateSealNumber('123456789')
      const result = validateSealNumberRealTime('AB123456', previous)
      expect(result).not.toBeNull()
      expect(result?.detectedFormat).toBe('alphanumeric')
    })
  })
})