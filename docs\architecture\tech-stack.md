# Tech Stack

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.3+ | Type-safe development | Essential for Supabase type generation and large codebase maintainability |
| Frontend Framework | Next.js | 14.2+ | React-based full-stack framework | App Router for optimal performance, built-in API routes, excellent Vercel integration |
| UI Component Library | ShadCN UI | Latest | Accessible component system | Headless UI with Tailwind, customizable for dark blue theme, accessibility compliance |
| State Management | Zustand | 4.5+ | Lightweight state management | Simple API, TypeScript-first, perfect for Supabase real-time integration |
| Backend Language | TypeScript | 5.3+ | Unified language across stack | Shared types between frontend and Supabase Edge Functions |
| Backend Framework | Supabase | Latest | Backend-as-a-Service platform | PostgreSQL with RLS, real-time subscriptions, built-in auth, file storage |
| API Style | Supabase Client | Latest | Type-safe database access | Auto-generated TypeScript types, real-time subscriptions, RLS integration |
| Database | PostgreSQL | 15+ (Supabase managed) | Relational database with advanced features | JSONB support for hybrid companies design, PostGIS for GPS coordinates |
| Cache | Vercel Edge Cache | Built-in | Global edge caching | Automatic caching for static assets and API responses |
| File Storage | Supabase Storage | Latest | Secure file storage with CDN | Image uploads for drivers, document storage with access policies |
| Authentication | Supabase Auth | Latest | Role-based authentication | 11 user types support, JWT tokens, RLS integration |
| Frontend Testing | Vitest + Testing Library | Latest | Fast unit testing | Vite-based testing for Next.js, React component testing |
| Backend Testing | Supabase CLI + Jest | Latest | Database and Edge Function testing | Local Supabase instance, API endpoint testing |
| E2E Testing | Playwright | 1.40+ | Cross-browser testing | PWA testing capabilities, mobile device emulation |
| Build Tool | Next.js | 14.2+ | Integrated build system | Built-in bundling, optimization, deployment ready |
| Bundler | Webpack | 5+ (Next.js integrated) | Module bundling | Optimized for PWA, code splitting, tree shaking |
| IaC Tool | Supabase CLI | Latest | Database migrations and deployment | Schema versioning, Edge Function deployment |
| CI/CD | GitHub Actions + Vercel | Latest | Automated deployment pipeline | Git-based deployments, preview environments |
| Monitoring | Vercel Analytics + Sentry | Latest | Performance and error monitoring | Core Web Vitals tracking, error reporting |
| Logging | Vercel Functions Logs + Supabase Logs | Built-in | Centralized logging | Request tracing, database query logs |
| CSS Framework | Tailwind CSS | 3.4+ | Utility-first CSS framework | Dark blue theme implementation, responsive design, ShadCN compatibility |
