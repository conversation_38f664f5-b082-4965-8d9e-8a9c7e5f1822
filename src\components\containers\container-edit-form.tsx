'use client'

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { AlertTriangle, Save, X, Weight, Package, Lock, CheckCircle, Shield } from 'lucide-react'
import { containerEditFormSchema, type ContainerEditForm } from '@/lib/validations/container-product'
import { CONTAINER_TYPES, CONTAINER_SIZES } from '@/lib/validations/shipment'
import { useContainerCRUD } from '@/hooks/use-containers'
import { useContainerConfirmation } from '@/hooks/use-container-confirmation'
import { ContainerConfirmationDialog } from './container-confirmation-dialog'
import type { ContainerWithProducts } from '@/hooks/use-containers'

interface ContainerEditFormProps {
  container: ContainerWithProducts
  onSave: () => void
  onCancel: () => void
}

export function ContainerEditForm({ container, onSave, onCancel }: ContainerEditFormProps) {
  const { updateContainer, updating } = useContainerCRUD()
  const { checkPermissions, canEditField } = useContainerConfirmation()
  const [error, setError] = useState<string | null>(null)
  const [warnings, setWarnings] = useState<string[]>([])
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false)
  const [permissions, setPermissions] = useState({
    canConfirm: false,
    canEditAfterConfirmation: false,
    canEditBeforeConfirmation: false
  })

  const form = useForm<ContainerEditForm>({
    resolver: zodResolver(containerEditFormSchema),
    defaultValues: {
      id: container.id,
      container_number: container.container_number || '',
      container_type: container.container_type || 'dry',
      container_size: container.container_size || '40ft',
      seal_number: container.seal_number || '',
      tare_weight: container.tare_weight || undefined,
      gross_weight: container.gross_weight || undefined,
      volume: container.volume || undefined,
      temperature: container.temperature || '',
      vent: container.vent || '',
      status: container.status,
    },
  })

  // Load user permissions on component mount
  useEffect(() => {
    const loadPermissions = async () => {
      const userPermissions = await checkPermissions()
      setPermissions(userPermissions)
    }
    loadPermissions()
  }, [checkPermissions])

  // Helper functions for confirmation status
  const isContainerNumberConfirmed = container.container_number_confirmed || false
  const isSealNumberConfirmed = container.seal_number_confirmed || false
  
  const canEditContainerNumber = canEditField(isContainerNumberConfirmed, permissions)
  const canEditSealNumber = canEditField(isSealNumberConfirmed, permissions)
  
  const formatConfirmationDate = (dateString: string | null | undefined) => {
    if (!dateString) return null
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Calculate weight and volume capacity
  const getContainerCapacity = (size: string, type: string) => {
    const CAPACITY = {
      '20ft': {
        dry: { volume: 33.2, weight: 28200 },
        reefer: { volume: 28.3, weight: 27400 },
        open_top: { volume: 32.4, weight: 27600 },
        flat_rack: { volume: 0, weight: 30500 },
        tank: { volume: 26.0, weight: 30480 },
      },
      '40ft': {
        dry: { volume: 67.7, weight: 26700 },
        reefer: { volume: 59.3, weight: 27300 },
        open_top: { volume: 65.9, weight: 26500 },
        flat_rack: { volume: 0, weight: 30500 },
        tank: { volume: 50.0, weight: 30480 },
      },
      '40hc': {
        dry: { volume: 76.4, weight: 26580 },
        reefer: { volume: 67.3, weight: 27300 },
        open_top: { volume: 74.3, weight: 26200 },
        flat_rack: { volume: 0, weight: 30500 },
        tank: { volume: 58.0, weight: 30480 },
      },
      '45ft': {
        dry: { volume: 86.0, weight: 26000 },
        reefer: { volume: 76.0, weight: 26800 },
        open_top: { volume: 84.0, weight: 25800 },
        flat_rack: { volume: 0, weight: 30500 },
        tank: { volume: 65.0, weight: 30480 },
      },
    } as any

    return CAPACITY[size]?.[type] || { volume: 67.7, weight: 26700 }
  }

  // Watch for specific changes to validate capacity
  const containerType = form.watch('container_type')
  const containerSize = form.watch('container_size')
  const grossWeight = form.watch('gross_weight')
  
  useEffect(() => {
    const newWarnings: string[] = []
    
    if (containerType && containerSize) {
      const capacity = getContainerCapacity(containerSize, containerType)
      
      // Check if current products exceed new capacity
      if (container.total_weight > capacity.weight * 0.95) {
        newWarnings.push(
          `Current products (${container.total_weight}kg) exceed safe weight capacity (${(capacity.weight * 0.95).toFixed(0)}kg)`
        )
      }
      
      if (container.total_volume > capacity.volume * 0.9) {
        newWarnings.push(
          `Current products (${container.total_volume}m³) exceed safe volume capacity (${(capacity.volume * 0.9).toFixed(1)}m³)`
        )
      }
    }

    // Check gross weight vs products
    if (grossWeight && grossWeight < container.total_weight) {
      newWarnings.push('Gross weight is less than allocated product weight')
    }

    setWarnings(newWarnings)
  }, [containerType, containerSize, grossWeight, container.total_weight, container.total_volume])

  const onSubmit = async (data: ContainerEditForm) => {
    setError(null)
    
    try {
      // Remove empty strings and convert to proper types
      const updateData = {
        container_number: data.container_number || null,
        container_type: data.container_type,
        container_size: data.container_size,
        seal_number: data.seal_number || null,
        tare_weight: data.tare_weight || null,
        gross_weight: data.gross_weight || null,
        volume: data.volume || null,
        temperature: data.temperature || null,
        vent: data.vent || null,
        status: data.status,
      }

      await updateContainer(container.id, updateData)
      onSave()
    } catch (error: any) {
      setError(error.message || 'Failed to update container')
    }
  }

  const currentCapacity = getContainerCapacity(
    containerSize || '40ft',
    containerType || 'dry'
  )

  return (
    <div className="space-y-6">
      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Current Container Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Products Allocated:</span>
              <div className="font-medium">{container.total_products} items</div>
            </div>
            <div>
              <span className="text-muted-foreground">Total Weight:</span>
              <div className="font-medium">{container.total_weight.toFixed(1)} kg</div>
            </div>
            <div>
              <span className="text-muted-foreground">Total Volume:</span>
              <div className="font-medium">{container.total_volume.toFixed(2)} m³</div>
            </div>
            <div>
              <span className="text-muted-foreground">Weight Utilization:</span>
              <div className="font-medium">{(container.weight_utilization * 100).toFixed(1)}%</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Warnings */}
      {warnings.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Configuration Warnings:</div>
              {warnings.map((warning, index) => (
                <div key={index} className="text-sm">• {warning}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Edit Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Container Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Container Details
                </CardTitle>
                <CardDescription>
                  Basic container information and specifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="container_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center justify-between">
                        <span>Container Number</span>
                        {isContainerNumberConfirmed && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            <Lock className="h-3 w-3 mr-1" />
                            Confirmed
                          </Badge>
                        )}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input 
                            placeholder="e.g., ABCD1234567" 
                            disabled={!canEditContainerNumber}
                            className={`${!canEditContainerNumber ? 'bg-slate-50 text-slate-500' : ''}`}
                            {...field} 
                          />
                          {isContainerNumberConfirmed && (
                            <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                          )}
                        </div>
                      </FormControl>
                      {isContainerNumberConfirmed && container.container_number_confirmed_at && (
                        <p className="text-xs text-slate-500 mt-1">
                          Confirmed on {formatConfirmationDate(container.container_number_confirmed_at)}
                        </p>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="container_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Container Type</FormLabel>
                        <FormControl>
                          <select 
                            {...field}
                            className="w-full p-2 border rounded-md"
                          >
                            {CONTAINER_TYPES.map(type => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="container_size"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Container Size</FormLabel>
                        <FormControl>
                          <select 
                            {...field}
                            className="w-full p-2 border rounded-md"
                          >
                            {CONTAINER_SIZES.map(size => (
                              <option key={size.value} value={size.value}>
                                {size.label}
                              </option>
                            ))}
                          </select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="seal_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center justify-between">
                        <span>Seal Number</span>
                        {isSealNumberConfirmed && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            <Lock className="h-3 w-3 mr-1" />
                            Confirmed
                          </Badge>
                        )}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input 
                            placeholder="e.g., ABC123456"
                            disabled={!canEditSealNumber}
                            className={`${!canEditSealNumber ? 'bg-slate-50 text-slate-500' : ''}`}
                            {...field} 
                          />
                          {isSealNumberConfirmed && (
                            <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                          )}
                        </div>
                      </FormControl>
                      {isSealNumberConfirmed && container.seal_number_confirmed_at && (
                        <p className="text-xs text-slate-500 mt-1">
                          Confirmed on {formatConfirmationDate(container.seal_number_confirmed_at)}
                        </p>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <FormControl>
                        <select 
                          {...field}
                          className="w-full p-2 border rounded-md"
                        >
                          <option value="empty">Empty</option>
                          <option value="loaded">Loaded</option>
                          <option value="in_transit">In Transit</option>
                          <option value="delivered">Delivered</option>
                        </select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Weight and Volume */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Weight className="h-4 w-4" />
                  Weight & Volume
                </CardTitle>
                <CardDescription>
                  Container capacity and weight specifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-3 bg-muted rounded-md">
                  <div className="text-sm font-medium mb-2">Container Capacity</div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Max Weight:</span>
                      <div>{currentCapacity.weight.toLocaleString()} kg</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Max Volume:</span>
                      <div>{currentCapacity.volume} m³</div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="tare_weight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tare Weight (kg)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1"
                            placeholder="Empty weight"
                            {...field} 
                            onChange={e => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="gross_weight"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Gross Weight (kg)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.1"
                            placeholder="Total weight"
                            {...field} 
                            onChange={e => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="volume"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Volume (m³)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01"
                          placeholder="Container volume"
                          {...field} 
                          onChange={e => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="temperature"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Temperature</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="e.g., -18°C to -15°C"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vent"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ventilation</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Vent requirements"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <div className="flex gap-2">
              {permissions.canConfirm && (container.container_number || container.seal_number) && (
                (!container.container_number || !isContainerNumberConfirmed) || 
                (!container.seal_number || !isSealNumberConfirmed)
              ) && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowConfirmationDialog(true)}
                  disabled={updating}
                  className="border-orange-500 text-orange-600 hover:bg-orange-800"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Confirm Numbers
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onCancel}
                disabled={updating}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={updating || warnings.length > 0}
              >
                <Save className="h-4 w-4 mr-2" />
                {updating ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </form>
      </Form>

      {/* Confirmation Dialog */}
      <ContainerConfirmationDialog
        container={container}
        isOpen={showConfirmationDialog}
        onClose={() => setShowConfirmationDialog(false)}
        onConfirm={() => {
          // Refresh the container data after confirmation
          onSave()
        }}
      />
    </div>
  )
}