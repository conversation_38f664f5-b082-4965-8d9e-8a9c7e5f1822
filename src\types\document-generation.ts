/**
 * Document Generation Types
 * Story 5.2: Automated Document Generation Engine
 */

import { DocumentType, DocumentTemplate } from './document-template'

/**
 * Generated document interface matching documents table
 */
export interface GeneratedDocument {
  id: string
  shipment_id: string
  document_type: DocumentType
  document_name: string
  document_number?: string
  file_path: string
  file_name: string
  file_size_bytes?: number
  file_type?: string
  file_hash?: string
  description?: string
  version: number
  is_original: boolean
  language: string
  issued_date?: string
  valid_until?: string
  issued_by?: string
  is_public: boolean
  access_level: 'shipment' | 'company' | 'internal' | 'public'
  shared_with_customer: boolean
  shared_with_carrier: boolean
  is_verified: boolean
  verification_notes?: string
  verified_by?: string
  verified_at?: string
  uploaded_by: string
  upload_source?: string
  created_at: string
  updated_at: string
}

/**
 * Document generation options
 */
export interface DocumentGenerationOptions {
  documentNumber?: string
  additionalData?: Record<string, any>
  language?: string
  outputFormat?: 'pdf' | 'html'
  includeWatermark?: boolean
  customFileName?: string
}

/**
 * Document generation request
 */
export interface DocumentGenerationRequest {
  shipmentId: string
  templateId: string
  options?: DocumentGenerationOptions
}

/**
 * Bulk document generation request
 */
export interface BulkDocumentGenerationRequest {
  shipmentId: string
  templateIds: string[]
  options?: DocumentGenerationOptions
  onProgress?: (progress: number) => void
}

/**
 * Document generation result
 */
export interface DocumentGenerationResult {
  success: boolean
  document?: GeneratedDocument
  error?: string
  templateId: string
  processingTime?: number
}

/**
 * Bulk generation result
 */
export interface BulkGenerationResult {
  results: DocumentGenerationResult[]
  summary: {
    total: number
    successful: number
    failed: number
    totalProcessingTime: number
  }
}

/**
 * Generation progress status
 */
export type GenerationStatus = 'idle' | 'preparing' | 'generating' | 'saving' | 'complete' | 'error'

/**
 * Generation progress information
 */
export interface GenerationProgress {
  status: GenerationStatus
  progress: number // 0-100
  message: string
  currentStep?: string
  error?: string
}

/**
 * Complete shipment data for template population
 */
export interface ShipmentDataForGeneration {
  // Basic shipment info
  id: string // Shipment UUID
  shipment_number: string
  invoice_number?: string
  status: string
  transportation_mode: 'sea' | 'land' | 'rail'
  
  // Shipping details
  liner?: string
  vessel_name?: string
  voyage_number?: string
  booking_number?: string
  
  // Dates
  etd_date?: string
  eta_date?: string
  closing_time?: string
  cy_date?: string
  
  // Location details
  origin_port?: {
    id: string
    port_name: string
    port_code: string
    country: string
  }
  destination_port?: {
    id: string
    port_name: string
    port_code: string
    country: string
  }
  
  // Stakeholders
  customer?: Company
  shipper?: Company
  consignee?: Company
  notify_party?: Company
  factory?: Company
  forwarder_agent?: Company
  
  // Products and containers
  products?: ShipmentProduct[]
  containers?: Container[]
  
  // Totals
  total_weight?: number
  total_volume?: number
  total_value_cif?: number
  total_value_fob?: number
  currency_code: 'USD' | 'THB' | 'CNY' | 'EUR'
  
  // Additional data
  number_of_pallet?: number
  pallet_description?: string
  ephyto_refno?: string
  notes?: string
  
  // Audit info
  created_by?: string
  created_at: string
  updated_at: string
}

/**
 * Company information for template population
 */
export interface Company {
  id: string
  name: string
  type: string
  code?: string
  address?: string
  city?: string
  state?: string
  country?: string
  postal_code?: string
  phone?: string
  fax?: string
  email?: string
  contact_person?: string
  tax_id?: string
  registration_number?: string
}

/**
 * Shipment product information
 */
export interface ShipmentProduct {
  id: string
  shipment_id: string
  container_id?: string
  product_id: string
  product_description?: string
  quantity: number
  unit_of_measure_id?: string
  unit_of_measure?: {
    id: string
    name: string
    abbreviation: string
  }
  unit_price_cif: number
  unit_price_fob: number
  total_value_cif: number
  total_value_fob: number
  gross_weight: number
  net_weight: number
  total_gross_weight?: number
  total_net_weight?: number
  shipping_mark?: string
  mfg_date?: string
  expire_date?: string
  lot_number?: string
  packaging_type: string
  quality_grade?: string
}

/**
 * Container information
 */
export interface Container {
  id: string
  shipment_id: string
  container_number?: string
  container_type?: string
  container_size?: string
  seal_number?: string
  tare_weight?: number
  gross_weight?: number
  volume?: number
  temperature?: string
  vent?: string
  status: string
}

/**
 * Template placeholder processing result
 */
export interface PlaceholderProcessingResult {
  content: string
  missingFields: string[]
  processedFields: string[]
  errors: string[]
}

/**
 * PDF generation options
 */
export interface PDFGenerationOptions {
  template: DocumentTemplate
  content: string
  metadata?: {
    title?: string
    author?: string
    subject?: string
    creator?: string
  }
}

/**
 * File storage result
 */
export interface FileStorageResult {
  filePath: string
  fileName: string
  fileSize: number
  fileHash: string
  publicUrl: string
}

/**
 * Document generation statistics
 */
export interface GenerationStatistics {
  totalGenerated: number
  byDocumentType: Record<DocumentType, number>
  byStatus: Record<GenerationStatus, number>
  averageProcessingTime: number
  successRate: number
}