'use client'

import { useState } from 'react'
import { CheckCircle, Circle, ArrowRight } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  getNextStatusOptions, 
  getStatusDisplayName, 
  getStatusProgress, 
  getStatusColor,
  isValidStatusTransition 
} from '@/lib/utils/status-workflow'
import { getLocalizedStatusDisplayName } from '@/lib/utils/status-workflow-i18n'
import { useLanguage } from '@/hooks/use-language'
import type { ShipmentStatus } from '@/types/status-update'

interface StatusSelectorProps {
  currentStatus: ShipmentStatus
  availableStatuses?: ShipmentStatus[]
  selectedStatus?: ShipmentStatus
  onStatusSelect: (status: ShipmentStatus) => void
  disabled?: boolean
}

export function StatusSelector({ 
  currentStatus, 
  availableStatuses, 
  selectedStatus,
  onStatusSelect,
  disabled = false
}: StatusSelectorProps) {
  const { t, locale } = useLanguage()
  
  // Use provided statuses or derive from workflow
  const nextStatusOptions = availableStatuses || getNextStatusOptions(currentStatus)
  
  const handleStatusSelect = (status: ShipmentStatus) => {
    if (disabled) return
    
    // Validate transition
    if (isValidStatusTransition(currentStatus, status)) {
      onStatusSelect(status)
    }
  }

  return (
    <div className="space-y-4">
      {/* Current Status Display */}
      <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-slate-400 uppercase tracking-wide">
            {t('statusSelector.currentStatus')}
          </h3>
          <div className="text-xs text-slate-500">
            {getStatusProgress(currentStatus)}% {t('statusSelector.complete')}
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
          <div className="flex-1">
            <div className="text-white font-medium">
              {getLocalizedStatusDisplayName(currentStatus, locale)}
            </div>
            <div className="w-full bg-slate-700 rounded-full h-2 mt-2">
              <div 
                className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getStatusProgress(currentStatus)}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Available Next Status Options */}
      {nextStatusOptions.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <ArrowRight className="w-4 h-4 text-slate-400" />
            <h3 className="text-sm font-medium text-slate-400 uppercase tracking-wide">
              {t('statusSelector.updateTo')}
            </h3>
          </div>

          <div className="space-y-2">
            {nextStatusOptions.map((status) => (
              <button
                key={status}
                onClick={() => handleStatusSelect(status)}
                disabled={disabled}
                className={`w-full p-4 rounded-lg border transition-all duration-200 text-left ${
                  selectedStatus === status
                    ? 'bg-orange-500/20 border-orange-500/50 ring-2 ring-orange-500/30'
                    : 'bg-slate-800 border-slate-700 hover:border-slate-600 hover:bg-slate-700/50'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full border-2 flex-shrink-0 ${
                      selectedStatus === status 
                        ? 'border-orange-500 bg-orange-500' 
                        : 'border-slate-500'
                    }`}>
                      {selectedStatus === status && (
                        <CheckCircle className="w-3 h-3 text-white m-0.5" />
                      )}
                    </div>
                    <div>
                      <div className="text-white font-medium">
                        {getLocalizedStatusDisplayName(status, locale)}
                      </div>
                      <div className="text-xs text-slate-400 mt-1">
                        {t('statusSelector.progress')}: {getStatusProgress(status)}%
                      </div>
                    </div>
                  </div>
                  
                  <Badge
                    variant="outline"
                    className={`${
                      status === 'cancelled' 
                        ? 'bg-red-500/20 text-red-300 border-red-500/30'
                        : 'bg-blue-500/20 text-blue-300 border-blue-500/30'
                    } border font-medium px-2 py-1 text-xs`}
                  >
                    {status === 'cancelled' ? t('statusSelector.final') : t('statusSelector.next')}
                  </Badge>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* No Available Transitions (Final Status) */}
      {nextStatusOptions.length === 0 && (
        <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
          <div className="flex items-center space-x-3 text-slate-400">
            <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
            <div>
              <div className="text-sm font-medium">
                {currentStatus === 'completed' ? t('statusSelector.shipmentComplete') : t('statusSelector.finalStatus')}
              </div>
              <div className="text-xs mt-1">
                {t('statusSelector.noFurtherUpdates')}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}