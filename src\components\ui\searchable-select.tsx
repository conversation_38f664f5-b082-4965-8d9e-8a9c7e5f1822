'use client'

import * as React from 'react'
import { Check, ChevronDown, Search } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

export interface SearchableSelectOption {
  value: string
  label: string
  searchTerms?: string[]
}

interface SearchableSelectProps {
  options: SearchableSelectOption[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  emptyText?: string
  searchPlaceholder?: string
  disabled?: boolean
  className?: string
}

const SearchableSelect = React.forwardRef<HTMLButtonElement, SearchableSelectProps>(
  ({
    options,
    value,
    onValueChange,
    placeholder = "Select option...",
    emptyText = "No options found",
    searchPlaceholder = "Search...",
    disabled = false,
    className,
    ...props
  }, ref) => {
    const [open, setOpen] = React.useState(false)
    const [search, setSearch] = React.useState('')

    // Filter options based on search
    const filteredOptions = React.useMemo(() => {
      if (!search) return options

      const searchLower = search.toLowerCase()
      return options.filter(option => {
        // Search in label
        if (option.label.toLowerCase().includes(searchLower)) {
          return true
        }

        // Search in additional search terms if provided
        if (option.searchTerms) {
          return option.searchTerms.some(term =>
            term.toLowerCase().includes(searchLower)
          )
        }

        return false
      })
    }, [options, search])

    // Find selected option
    const selectedOption = options.find(option => option.value === value)

    // Handle selection
    const handleSelect = (selectedValue: string) => {
      onValueChange?.(selectedValue)
      setOpen(false)
      setSearch('')
    }

    // Reset search when closed
    React.useEffect(() => {
      if (!open) {
        setSearch('')
      }
    }, [open])

    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            ref={ref}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between bg-slate-700 border-slate-600 text-white hover:bg-slate-600",
              !selectedOption && "text-slate-400",
              className
            )}
            disabled={disabled}
            {...props}
          >
            {selectedOption ? selectedOption.label : placeholder}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0 bg-slate-800 border-slate-700" align="start">
          {/* Search Input */}
          <div className="flex items-center border-b border-slate-700 px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50 text-slate-400" />
            <Input
              placeholder={searchPlaceholder}
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="flex h-11 w-full border-0 bg-transparent py-3 text-sm text-white placeholder:text-slate-400 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 focus:ring-0 focus:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>

          {/* Options List */}
          <div className="max-h-60 overflow-auto p-1">
            {filteredOptions.length === 0 ? (
              <div className="px-2 py-3 text-sm text-slate-400 text-center">
                {emptyText}
              </div>
            ) : (
              filteredOptions.map((option) => (
                <div
                  key={option.value}
                  className={cn(
                    "relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm text-white outline-none hover:bg-slate-700 focus:bg-slate-700 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
                    value === option.value && "bg-slate-700"
                  )}
                  onClick={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {option.label}
                </div>
              ))
            )}
          </div>
        </PopoverContent>
      </Popover>
    )
  }
)

SearchableSelect.displayName = "SearchableSelect"

export { SearchableSelect, type SearchableSelectProps }