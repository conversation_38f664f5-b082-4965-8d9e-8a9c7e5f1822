'use client'

import { useState, useRef, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Download,
  FileText,
  Printer,
  Loader2,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Maximize2,
  Minimize2,
  Eye
} from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import type { DocumentTemplate } from '@/types/document-template'
import { generateSampleData, replacePlaceholders, getAvailablePlaceholders } from '@/lib/utils/template-sample-data'
import { validateTemplatePlaceholders } from '@/lib/utils/template-validation'

interface ValidationResult {
  isValid: boolean
  errors: Array<{ code: string; message: string }>
  warnings: Array<{ code: string; message: string }>
  placeholderCount: number
  missingPlaceholders: string[]
}

/**
 * Template Preview Component
 * Story 5.1: Document Template Management System - AC4
 * 
 * Provides template preview with sample data injection, PDF generation,
 * and validation checks before template activation.
 */
interface TemplatePreviewProps {
  template: Partial<DocumentTemplate>
  className?: string
  expanded?: boolean
}

export function TemplatePreview({ template, className, expanded = false }: TemplatePreviewProps) {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [validation, setValidation] = useState<ValidationResult | null>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [lastValidated, setLastValidated] = useState<Date | null>(null)
  const printRef = useRef<HTMLDivElement>(null)

  // Generate sample data and processed content
  const sampleData = generateSampleData()
  const processedContent = template.template_content 
    ? replacePlaceholders(template.template_content, sampleData, expanded)
    : ''

  // Validation function
  const validateTemplate = useCallback(() => {
    if (!template.template_content) {
      setValidation({
        isValid: false,
        errors: [{ code: 'MISSING_CONTENT', message: 'Template content is required' }],
        warnings: [],
        placeholderCount: 0,
        missingPlaceholders: []
      })
      return
    }

    const placeholderValidation = validateTemplatePlaceholders(template.template_content)
    const availablePlaceholders = getAvailablePlaceholders()
    
    // Extract placeholders from content
    const placeholderMatches = template.template_content.match(/{{[^}]+}}/g) || []
    const usedPlaceholders = placeholderMatches.map(match => 
      match.replace(/{{|}}/g, '').trim()
    )
    
    // Check for missing placeholders (used but not available in sample data)
    const missingPlaceholders = usedPlaceholders.filter(placeholder => 
      !availablePlaceholders.includes(placeholder)
    )
    
    const errors: Array<{ code: string; message: string }> = []
    const warnings: Array<{ code: string; message: string }> = []
    
    // Add validation errors
    if (placeholderValidation.errors) {
      errors.push(...placeholderValidation.errors.map(e => ({ code: e.code, message: e.message })))
    }
    
    // Add validation warnings
    if (placeholderValidation.warnings) {
      warnings.push(...placeholderValidation.warnings.map(w => ({ code: w.code, message: w.message })))
    }
    
    // Add missing placeholder warnings
    if (missingPlaceholders.length > 0) {
      warnings.push({
        code: 'UNKNOWN_PLACEHOLDERS',
        message: `Unknown placeholders: ${missingPlaceholders.join(', ')}`
      })
    }
    
    // Template activation readiness check
    if (!template.template_name) {
      errors.push({ code: 'MISSING_NAME', message: 'Template name is required for activation' })
    }
    
    if (!template.document_type) {
      errors.push({ code: 'MISSING_TYPE', message: 'Document type is required for activation' })
    }

    setValidation({
      isValid: errors.length === 0,
      errors,
      warnings,
      placeholderCount: usedPlaceholders.length,
      missingPlaceholders
    })
    
    setLastValidated(new Date())
  }, [template])

  // Print functionality
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: `${template.template_name || 'Template'} - Preview`,
    pageStyle: `
      @page {
        size: ${template.page_size || 'A4'} ${template.page_orientation || 'portrait'};
        margin: ${template.margin_top || 20}mm ${template.margin_right || 20}mm ${template.margin_bottom || 20}mm ${template.margin_left || 20}mm;
      }
      @media print {
        body { -webkit-print-color-adjust: exact; }
        .no-print { display: none !important; }
      }
    `
  })

  // PDF generation functionality
  const generatePDF = async () => {
    if (!printRef.current) return
    
    setIsGeneratingPDF(true)
    try {
      const element = printRef.current
      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })
      
      const imgData = canvas.toDataURL('image/png')
      const pageSize = template.page_size || 'A4'
      const orientation = template.page_orientation || 'portrait'
      
      // PDF dimensions based on page size
      const dimensions: Record<string, { width: number; height: number }> = {
        'A4': { width: 210, height: 297 },
        'A3': { width: 297, height: 420 },
        'Letter': { width: 216, height: 279 },
        'Legal': { width: 216, height: 356 },
        'A5': { width: 148, height: 210 }
      }
      
      const { width: pageWidth, height: pageHeight } = dimensions[pageSize] || dimensions['A4']
      const finalWidth = orientation === 'landscape' ? pageHeight : pageWidth
      const finalHeight = orientation === 'landscape' ? pageWidth : pageHeight
      
      const pdf = new jsPDF({
        orientation: orientation as 'portrait' | 'landscape',
        unit: 'mm',
        format: [finalWidth, finalHeight]
      })
      
      // Calculate image dimensions to fit the page
      const imgWidth = finalWidth - ((template.margin_left || 20) + (template.margin_right || 20))
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      
      pdf.addImage(imgData, 'PNG', template.margin_left || 20, template.margin_top || 20, imgWidth, imgHeight)
      
      const fileName = `${template.template_name || 'template'}-preview.pdf`
      pdf.save(fileName)
    } catch (error) {
      console.error('Error generating PDF:', error)
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  // Create preview styles
  const previewStyles = `
    ${template.template_styles || ''}
    
    /* Project color scheme - Dark blue theme with orange accents */
    .template-preview {
      font-family: system-ui, -apple-system, sans-serif;
      line-height: 1.5;
      color: #1e293b;
      background-color: #ffffff;
      max-width: 100%;
      margin: 0 auto;
      padding: 20px;
      box-sizing: border-box;
      min-width: ${expanded ? '850px' : 'auto'};
    }
    
    .template-preview * {
      box-sizing: border-box;
    }
    
    .template-preview h1, .template-preview h2, .template-preview h3 {
      color: #0f172a;
      margin-top: 0;
      margin-bottom: 0.5em;
      word-wrap: break-word;
    }
    
    .template-preview .accent {
      color: #f97316;
      font-weight: 600;
    }
    
    .template-preview table {
      border-collapse: collapse;
      width: 100%;
      margin: 1em 0;
      table-layout: fixed;
      overflow-wrap: break-word;
    }
    
    .template-preview th, .template-preview td {
      text-align: left;
      padding: 8px 10px;
      border: 1px solid #cbd5e1;
      word-wrap: break-word;
      overflow-wrap: break-word;
      vertical-align: top;
    }
    
    .template-preview th {
      background-color: #f1f5f9;
      font-weight: 600;
      color: #1e293b;
      font-size: 0.95em;
    }
    
    .template-preview td {
      font-size: 0.9em;
      vertical-align: top;
    }
    
    .template-preview .header-section {
      border-bottom: 2px solid #f97316;
      margin-bottom: 1em;
      padding-bottom: 0.5em;
      overflow: hidden;
    }
    
    .template-preview .footer-section {
      border-top: 1px solid #cbd5e1;
      margin-top: 2em;
      padding-top: 1em;
      font-size: 0.9em;
      color: #64748b;
    }
    
    /* Prevent overflow for all content */
    .template-preview div, .template-preview p, .template-preview span {
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
    
    /* Make sure images don't overflow */
    .template-preview img {
      max-width: 100%;
      height: auto;
    }
    
    /* Handle long text content */
    .template-preview pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      overflow: auto;
    }
  `

  return (
    <div className={expanded ? 'w-full max-w-none' : className}>
      {/* Preview Controls */}
      <div className="flex items-center justify-between mb-4 no-print">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-slate-600" />
          <span className="text-sm font-medium text-slate-700">Template Preview</span>
          {validation && (
            <Badge variant={validation.isValid ? "success" : "destructive"} className="text-xs">
              {validation.isValid ? (
                <>
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Valid
                </>
              ) : (
                <>
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Issues Found
                </>
              )}
            </Badge>
          )}
          {lastValidated && (
            <span className="text-xs text-slate-500">
              Last checked: {lastValidated.toLocaleTimeString()}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={validateTemplate}
            className="text-slate-600 hover:text-slate-700"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Validate
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="text-slate-600 hover:text-slate-700"
          >
            {isFullscreen ? (
              <Minimize2 className="h-3 w-3 mr-1" />
            ) : (
              <Maximize2 className="h-3 w-3 mr-1" />
            )}
            {isFullscreen ? 'Minimize' : 'Fullscreen'}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handlePrint}
            className="text-slate-600 hover:text-slate-700"
          >
            <Printer className="h-3 w-3 mr-1" />
            Print
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={generatePDF}
            disabled={isGeneratingPDF}
            className="text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300"
          >
            {isGeneratingPDF ? (
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            ) : (
              <Download className="h-3 w-3 mr-1" />
            )}
            PDF
          </Button>
        </div>
      </div>

      {/* Validation Results */}
      {validation && (
        <div className="space-y-2 mb-4 no-print">
          {validation.errors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <div className="font-medium">Template Activation Issues:</div>
                  {validation.errors.map((error, index) => (
                    <div key={index} className="text-sm">• {error.message}</div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}
          
          {validation.warnings.length > 0 && (
            <Alert>
              <Eye className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <div className="font-medium">Preview Warnings:</div>
                  {validation.warnings.map((warning, index) => (
                    <div key={index} className="text-sm">• {warning.message}</div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}
          
          <div className="flex items-center gap-4 text-sm text-slate-600">
            <span>Placeholders found: {validation.placeholderCount}</span>
            {validation.missingPlaceholders.length > 0 && (
              <span className="text-orange-600">
                Unknown: {validation.missingPlaceholders.length}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Preview Content */}
      {expanded && !isFullscreen ? (
        // Expanded layout without Card wrapper - matches Content tab layout
        <div className="space-y-6">
          {/* Template Info Header */}
          <div className="border-b border-slate-200 pb-4 no-print">
            <h3 className="text-lg font-semibold text-slate-800">
              {template.template_name || 'Untitled Template'} - {template.document_type || 'Unknown Type'}
            </h3>
            <div className="flex items-center gap-4 text-sm text-slate-600 mt-2">
              <span>Page: {template.page_size || 'A4'} ({template.page_orientation || 'portrait'})</span>
              <span>Language: {template.language || 'en'}</span>
              <span>Currency: {template.currency_format || 'USD'}</span>
            </div>
          </div>
          
          {/* Preview Area */}
          <div>
          {/* Inject custom styles */}
          <style dangerouslySetInnerHTML={{ __html: previewStyles }} />
          
          {/* Template preview area */}
          <div 
            ref={printRef}
            className="template-preview border border-slate-200 bg-white shadow-sm overflow-auto"
            style={{
              minHeight: expanded ? '900px' : '800px',
              maxHeight: expanded ? '1400px' : '1200px',
              width: '100%',
              minWidth: expanded ? '850px' : 'auto',
              padding: `${template.margin_top || 20}px ${template.margin_right || 20}px ${template.margin_bottom || 20}px ${template.margin_left || 20}px`
            }}
          >
            {processedContent ? (
              <div 
                className="h-full"
                dangerouslySetInnerHTML={{ __html: processedContent }} 
              />
            ) : (
              <div className="flex items-center justify-center h-64 text-slate-500">
                <div className="text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No template content to preview</p>
                  <p className="text-sm mt-1">Add content in the Content tab to see preview</p>
                </div>
              </div>
            )}
          </div>
          
          {/* Sample data info */}
          {processedContent && (
            <div className="mt-4 p-3 bg-slate-50 rounded-lg border text-sm text-slate-600 no-print">
              <div className="flex items-center gap-2 mb-2">
                <Eye className="h-4 w-4" />
                <span className="font-medium">Preview Information</span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <span>Sample data injected with realistic export scenarios</span>
                <span>All placeholders populated with test values</span>
                <span>Styling applied according to template configuration</span>
                <span>Ready for PDF generation and printing</span>
              </div>
            </div>
          )}
          </div>
        </div>
      ) : (
        // Card-based layout for non-expanded mode and fullscreen
        <Card className={isFullscreen ? "fixed inset-4 z-50 overflow-auto" : ""}>
          <CardHeader className="no-print">
            <CardTitle className="text-slate-800">
              {template.template_name || 'Untitled Template'} - {template.document_type || 'Unknown Type'}
            </CardTitle>
            <div className="flex items-center gap-4 text-sm text-slate-600">
              <span>Page: {template.page_size || 'A4'} ({template.page_orientation || 'portrait'})</span>
              <span>Language: {template.language || 'en'}</span>
              <span>Currency: {template.currency_format || 'USD'}</span>
            </div>
          </CardHeader>
          <CardContent>
            {/* Inject custom styles */}
            <style dangerouslySetInnerHTML={{ __html: previewStyles }} />
            
            {/* Template preview area */}
            <div 
              ref={printRef}
              className="template-preview border border-slate-200 bg-white shadow-sm overflow-auto"
              style={{
                minHeight: expanded ? '900px' : '800px',
                maxHeight: expanded ? '1400px' : '1200px',
                width: '100%',
                minWidth: expanded ? '850px' : 'auto',
                padding: `${template.margin_top || 20}px ${template.margin_right || 20}px ${template.margin_bottom || 20}px ${template.margin_left || 20}px`
              }}
            >
              {processedContent ? (
                <div 
                  className="h-full"
                  dangerouslySetInnerHTML={{ __html: processedContent }} 
                />
              ) : (
                <div className="flex items-center justify-center h-64 text-slate-500">
                  <div className="text-center">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No template content to preview</p>
                    <p className="text-sm mt-1">Add content in the Content tab to see preview</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* Sample data info */}
            {processedContent && (
              <div className="mt-4 p-3 bg-slate-50 rounded-lg border text-sm text-slate-600 no-print">
                <div className="flex items-center gap-2 mb-2">
                  <Eye className="h-4 w-4" />
                  <span className="font-medium">Preview Information</span>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <span>Sample data injected with realistic export scenarios</span>
                  <span>All placeholders populated with test values</span>
                  <span>Styling applied according to template configuration</span>
                  <span>Ready for PDF generation and printing</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}