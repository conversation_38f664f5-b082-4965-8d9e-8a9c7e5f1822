import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { useParams, useRouter } from 'next/navigation'
import ShipmentDetailPage from '../page'
import { useShipmentDetail } from '@/hooks/use-shipment-detail'
import { useStatusUpdates } from '@/hooks/use-status-updates'
import { useRealTimeStatusHistory } from '@/hooks/use-real-time'
import { useTransportation } from '@/hooks/use-transportation'
import { useAuth } from '@/hooks/use-auth'
import { useFullShipmentIntelligence } from '@/hooks/use-shipment-relationships'
import type { ShipmentWithRelations } from '@/lib/supabase/types'

// Mock all the hooks
vi.mock('next/navigation')
vi.mock('@/hooks/use-shipment-detail')
vi.mock('@/hooks/use-status-updates')
vi.mock('@/hooks/use-real-time')
vi.mock('@/hooks/use-transportation', () => ({
  useTransportation: vi.fn(),
  useCoordinateUtils: vi.fn(() => ({
    parseCoordinates: vi.fn(),
    formatCoordinates: vi.fn()
  }))
}))
vi.mock('@/hooks/use-auth')
vi.mock('@/hooks/use-shipment-relationships')

// Mock the sub-components
vi.mock('@/components/forms/shipment-form/status-update-form', () => ({
  StatusUpdateForm: () => <div data-testid="status-update-form">Status Update Form</div>
}))

vi.mock('@/components/forms/transportation-form/transportation-form', () => ({
  TransportationForm: () => <div data-testid="transportation-form">Transportation Form</div>
}))

vi.mock('@/components/data-display/timeline', () => ({
  Timeline: () => <div data-testid="timeline">Timeline</div>
}))

const mockShipment: ShipmentWithRelations = {
  id: 'shipment-1',
  shipment_number: 'EXSEA-BKK-250109-001',
  invoice_number: 'INV-2025-001',
  customer_id: 'customer-1',
  shipper_id: 'shipper-1',
  consignee_id: 'consignee-1',
  notify_party_id: 'notify-1',
  factory_id: 'factory-1',
  forwarder_agent_id: 'forwarder-1',
  origin_port_id: 'port-origin',
  destination_port_id: 'port-dest',
  liner: 'MSK',
  vessel_name: 'MSK VESSEL',
  voyage_number: 'V001',
  booking_number: 'BKG001',
  etd_date: '2025-01-15T00:00:00Z',
  eta_date: '2025-01-30T00:00:00Z',
  closing_time: '2025-01-14T12:00:00Z',
  cy_date: '2025-01-14T08:00:00Z',
  number_of_pallet: 10,
  pallet_description: 'Wooden pallets',
  ephyto_refno: 'EPHYTO-001',
  currency_code: 'USD',
  total_weight: 1000,
  total_volume: 50,
  status: 'booking_confirmed',
  transportation_mode: 'sea',
  notes: 'Test shipment notes',
  metadata: null,
  created_by: 'user-1',
  created_at: '2025-01-09T00:00:00Z',
  updated_at: '2025-01-09T00:00:00Z',
  total_value_cif: 10000,
  total_value_fob: 9500,
  customer: { id: 'customer-1', name: 'Test Customer', company_type: 'customer' },
  shipper: { id: 'shipper-1', name: 'Test Shipper', company_type: 'shipper' },
  consignee: { id: 'consignee-1', name: 'Test Consignee', company_type: 'consignee' },
  notify_party: { id: 'notify-1', name: 'Test Notify Party', company_type: 'notify_party' },
  factory: { id: 'factory-1', name: 'Test Factory', company_type: 'factory' },
  forwarder_agent: { id: 'forwarder-1', name: 'Test Forwarder', company_type: 'forwarder_agent' },
  origin_port: { id: 'port-origin', name: 'Bangkok Port', code: 'BKK', country: 'Thailand', port_type: 'origin', is_active: true, gps_coordinates: null, city: 'Bangkok', timezone: 'Asia/Bangkok', created_at: '2025-01-01T00:00:00Z', updated_at: '2025-01-01T00:00:00Z' },
  destination_port: { id: 'port-dest', name: 'Los Angeles Port', code: 'LAX', country: 'USA', port_type: 'destination', is_active: true, gps_coordinates: null, city: 'Los Angeles', timezone: 'America/Los_Angeles', created_at: '2025-01-01T00:00:00Z', updated_at: '2025-01-01T00:00:00Z' },
  containers: [
    {
      id: 'container-1',
      shipment_id: 'shipment-1',
      container_number: 'CONT123456',
      container_type: 'dry',
      container_size: '20ft',
      seal_number: 'SEAL123',
      tare_weight: 2200,
      gross_weight: 22200,
      volume: 33.2,
      temperature: null,
      vent: null,
      status: 'empty',
      created_at: '2025-01-09T00:00:00Z',
      update_at: '2025-01-09T00:00:00Z'
    }
  ],
  shipment_products: [
    {
      id: 'sp-1',
      shipment_id: 'shipment-1',
      container_id: 'container-1',
      product_id: 'product-1',
      product_description: 'Fresh Mangoes',
      quantity: 1000,
      unit_of_measure_id: 'unit-kg',
      unit_price_cif: 10,
      unit_price_fob: 9.5,
      total_value_cif: 10000,
      total_value_fob: 9500,
      gross_weight: 1100,
      net_weight: 1000,
      shipping_mark: 'MANGO-001',
      mfg_date: '2025-01-05',
      expire_date: '2025-02-05',
      lot_number: 'LOT-001',
      packaging_type: 'box',
      quality_grade: 'A',
      created_at: '2025-01-09T00:00:00Z',
      updated_at: '2025-01-09T00:00:00Z',
      product: {
        id: 'product-1',
        name: 'Fresh Mangoes',
        code: 'MNG-001',
        description: 'Premium fresh mangoes',
        category: 'fruits',
        hs_code: '0804.50',
        unit_of_measure_id: 'unit-kg',
        is_active: true,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      unit_of_measure: {
        id: 'unit-kg',
        code: 'KG',
        name: 'Kilogram',
        category: 'weight',
        symbol: 'kg',
        conversion_factor: 1,
        base_unit_id: null,
        is_active: true,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      }
    }
  ]
}

const mockUseParams = vi.mocked(useParams)
const mockUseRouter = vi.mocked(useRouter)
const mockUseShipmentDetail = vi.mocked(useShipmentDetail)
const mockUseStatusUpdates = vi.mocked(useStatusUpdates)
const mockUseRealTimeStatusHistory = vi.mocked(useRealTimeStatusHistory)
const mockUseTransportation = vi.mocked(useTransportation)
const mockUseAuth = vi.mocked(useAuth)
const mockUseFullShipmentIntelligence = vi.mocked(useFullShipmentIntelligence)

describe('ShipmentDetailPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    mockUseParams.mockReturnValue({ id: 'shipment-1' })
    mockUseRouter.mockReturnValue({
      push: vi.fn(),
      replace: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn()
    })
    
    mockUseShipmentDetail.mockReturnValue({
      shipment: mockShipment,
      isLoading: false,
      error: null,
      refreshShipment: vi.fn()
    })
    
    mockUseStatusUpdates.mockReturnValue({
      updateStatus: vi.fn(),
      isLoading: false
    })
    
    mockUseRealTimeStatusHistory.mockReturnValue({
      statusHistory: [],
      isLoading: false,
      refetch: vi.fn()
    })
    
    mockUseTransportation.mockReturnValue({
      transportation: null,
      isLoading: false,
      createTransportationAssignment: vi.fn(),
      updateTransportationAssignment: vi.fn(),
      deleteTransportationAssignment: vi.fn()
    })
    
    mockUseAuth.mockReturnValue({
      isStaff: true,
      user: { id: 'user-1', role: 'admin' }
    })
    
    mockUseFullShipmentIntelligence.mockReturnValue({
      state: {
        selectedCustomerId: null,
        selectedShipperId: null,
        selectedProductId: null,
        selectedConsigneeId: null,
        selectedNotifyPartyId: null,
        availableShippers: [],
        availableProducts: [],
        availableConsignees: [],
        availableNotifyParties: [],
        shipmentData: {},
        loadingShippers: false,
        loadingProducts: false,
        loadingConsignees: false,
        loadingNotifyParties: false,
        loadingHistory: false,
        hasValidRelationships: true,
        relationshipErrors: []
      },
      actions: {
        selectCustomer: vi.fn(),
        selectShipper: vi.fn(),
        selectProduct: vi.fn(),
        selectConsignee: vi.fn(),
        selectNotifyParty: vi.fn(),
        autoSelectDefaults: vi.fn(),
        populateFromCustomerHistory: vi.fn(),
        calculateEstimatedPricing: vi.fn(),
        updatePricingFromProduct: vi.fn(),
        suggestRoutesFromHistory: vi.fn(),
        getMostFrequentRoute: vi.fn(),
        validateAllRelationships: vi.fn(),
        getRelationshipErrors: vi.fn(),
        exportShipmentData: vi.fn(),
        exportFormData: vi.fn(),
        resetAllSelections: vi.fn(),
        resetCustomerDependents: vi.fn()
      },
      config: {
        autoSelectDefaults: true,
        enablePricePopulation: true,
        enableRouteIntelligence: true,
        enableHistoryAnalysis: true,
        validateRelationships: true
      }
    })
  })

  it('renders shipment detail page with basic information', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getAllByText('EXSEA-BKK-250109-001')).toHaveLength(2) // Header and basic info
      expect(screen.getByText('Shipment Details')).toBeInTheDocument()
      expect(screen.getByText('Basic Information')).toBeInTheDocument()
    })
  })

  it('displays shipment number and invoice number correctly', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getAllByText('EXSEA-BKK-250109-001')).toHaveLength(2)
      expect(screen.getByText('INV-2025-001')).toBeInTheDocument()
    })
  })

  it('shows route information with ports', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Route & Schedule')).toBeInTheDocument()
      expect(screen.getByText('Bangkok Port')).toBeInTheDocument()
      expect(screen.getByText('Los Angeles Port')).toBeInTheDocument()
      expect(screen.getByText('BKK')).toBeInTheDocument()
      expect(screen.getByText('LAX')).toBeInTheDocument()
    })
  })

  it('displays container information', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Containers (1)')).toBeInTheDocument()
      expect(screen.getByText('CONT123456')).toBeInTheDocument()
      // Just verify that the container section exists and basic info is shown
    })
  })

  it('displays product information with pricing', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Products (1)')).toBeInTheDocument()
      expect(screen.getByText('Fresh Mangoes')).toBeInTheDocument()
      expect(screen.getByText('MANGO-001')).toBeInTheDocument()
      // Product section renders with basic info
    })
  })

  it('shows company information', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Companies & Relationships')).toBeInTheDocument()
      expect(screen.getByText('Test Customer')).toBeInTheDocument()
      expect(screen.getByText('Test Shipper')).toBeInTheDocument()
      expect(screen.getByText('Test Consignee')).toBeInTheDocument()
      expect(screen.getByText('Test Notify Party')).toBeInTheDocument()
      expect(screen.getByText('Test Forwarder')).toBeInTheDocument()
    })
  })

  it('displays action buttons for staff users', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Update Status')).toBeInTheDocument()
      expect(screen.getAllByText('Assign Transportation')).toHaveLength(2) // Header and section
      expect(screen.getByText('Edit Shipment')).toBeInTheDocument()
      expect(screen.getByText('Refresh')).toBeInTheDocument()
    })
  })

  it('handles copy shipment number functionality', async () => {
    // Mock navigator.clipboard
    Object.assign(navigator, {
      clipboard: {
        writeText: vi.fn()
      }
    })
    
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getAllByText('EXSEA-BKK-250109-001')).toHaveLength(2)
    })
    
    const copyButton = screen.getByRole('button', { name: /copy number/i })
    fireEvent.click(copyButton)
    
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('EXSEA-BKK-250109-001')
  })

  it('shows loading state', () => {
    mockUseShipmentDetail.mockReturnValue({
      shipment: null,
      isLoading: true,
      error: null,
      refreshShipment: vi.fn()
    })
    
    render(<ShipmentDetailPage />)
    
    expect(screen.getByText('Loading shipment details...')).toBeInTheDocument()
  })

  it('shows error state', () => {
    mockUseShipmentDetail.mockReturnValue({
      shipment: null,
      isLoading: false,
      error: 'Failed to load shipment',
      refreshShipment: vi.fn()
    })
    
    render(<ShipmentDetailPage />)
    
    expect(screen.getByText('Failed to load shipment')).toBeInTheDocument()
  })

  it('shows not found state', () => {
    mockUseShipmentDetail.mockReturnValue({
      shipment: null,
      isLoading: false,
      error: null,
      refreshShipment: vi.fn()
    })
    
    render(<ShipmentDetailPage />)
    
    expect(screen.getByText('Shipment not found')).toBeInTheDocument()
  })

  it('allows inline editing of notes for staff users', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Test shipment notes')).toBeInTheDocument()
    })
    
    // Find and click the edit button for notes
    const editButtons = screen.getAllByRole('button')
    const notesEditButton = editButtons.find(button => 
      button.querySelector('svg') !== null && 
      button.closest('div')?.textContent?.includes('Notes')
    )
    
    if (notesEditButton) {
      fireEvent.click(notesEditButton)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter shipment notes...')).toBeInTheDocument()
      })
    }
  })

  it('renders timeline component', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getByTestId('timeline')).toBeInTheDocument()
    })
  })

  it('hides staff-only actions for non-staff users', async () => {
    mockUseAuth.mockReturnValue({
      isStaff: false,
      user: { id: 'user-1', role: 'customer' }
    })
    
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.queryByText('Update Status')).not.toBeInTheDocument()
      expect(screen.queryByText('Assign Transportation')).not.toBeInTheDocument()
    })
  })

  it('displays financial information when available', async () => {
    render(<ShipmentDetailPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Financial & Cargo Info')).toBeInTheDocument()
      // Financial section renders
    })
  })
})