'use client'

import { createClient } from '@/lib/supabase/client'

interface AuthRecoveryOptions {
  maxRetries?: number
  retryDelay?: number
  onRetry?: (attempt: number) => void
  onSuccess?: () => void
  onFailure?: (error: Error) => void
}

/**
 * Attempts to recover a failed authentication session
 * Useful for handling temporary network issues or token refresh problems
 */
export class AuthRecovery {
  private supabase = createClient()
  private options: Required<AuthRecoveryOptions>

  constructor(options: AuthRecoveryOptions = {}) {
    this.options = {
      maxRetries: 3,
      retryDelay: 1000,
      onRetry: () => {},
      onSuccess: () => {},
      onFailure: () => {},
      ...options
    }
  }

  /**
   * Attempt to refresh the current session
   */
  async refreshSession(): Promise<boolean> {
    try {
      console.log('AuthRecovery: Attempting session refresh')
      const { data, error } = await this.supabase.auth.refreshSession()
      
      if (error) {
        console.error('AuthRecovery: Session refresh failed:', error)
        return false
      }

      if (data.session) {
        console.log('AuthRecovery: Session refreshed successfully')
        return true
      }

      console.log('AuthRecovery: No session returned from refresh')
      return false
    } catch (err) {
      console.error('AuthRecovery: Exception during session refresh:', err)
      return false
    }
  }

  /**
   * Attempt to recover authentication with retries
   */
  async recoverAuthentication(): Promise<boolean> {
    console.log('AuthRecovery: Starting authentication recovery')
    
    for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
      console.log(`AuthRecovery: Attempt ${attempt}/${this.options.maxRetries}`)
      this.options.onRetry(attempt)

      // First try to refresh the session
      const refreshSuccess = await this.refreshSession()
      if (refreshSuccess) {
        console.log('AuthRecovery: Successfully recovered via session refresh')
        this.options.onSuccess()
        return true
      }

      // If refresh failed, try to get current session
      try {
        const { data: { session }, error } = await this.supabase.auth.getSession()
        if (!error && session) {
          console.log('AuthRecovery: Successfully recovered via getSession')
          this.options.onSuccess()
          return true
        }
      } catch (err) {
        console.error('AuthRecovery: Error getting session:', err)
      }

      // Wait before next attempt (except on last attempt)
      if (attempt < this.options.maxRetries) {
        console.log(`AuthRecovery: Waiting ${this.options.retryDelay}ms before next attempt`)
        await new Promise(resolve => setTimeout(resolve, this.options.retryDelay))
      }
    }

    console.log('AuthRecovery: All recovery attempts failed')
    const error = new Error(`Authentication recovery failed after ${this.options.maxRetries} attempts`)
    this.options.onFailure(error)
    return false
  }

  /**
   * Clear any invalid session data and redirect to login
   */
  async clearSessionAndRedirect(redirectUrl: string = '/login'): Promise<void> {
    try {
      console.log('AuthRecovery: Clearing session and redirecting to login')
      await this.supabase.auth.signOut()
      
      // Clear any stored auth data
      if (typeof window !== 'undefined') {
        localStorage.removeItem('supabase.auth.token')
        sessionStorage.clear()
        
        // Redirect after a short delay to ensure cleanup is complete
        setTimeout(() => {
          window.location.href = redirectUrl
        }, 100)
      }
    } catch (err) {
      console.error('AuthRecovery: Error during session cleanup:', err)
      // Force redirect even if cleanup failed
      if (typeof window !== 'undefined') {
        window.location.href = redirectUrl
      }
    }
  }

  /**
   * Check if the current session is valid
   */
  async isSessionValid(): Promise<boolean> {
    try {
      const { data: { session }, error } = await this.supabase.auth.getSession()
      
      if (error || !session) {
        console.log('AuthRecovery: No valid session found')
        return false
      }

      // Check if session is expired
      const now = Math.floor(Date.now() / 1000)
      if (session.expires_at && session.expires_at < now) {
        console.log('AuthRecovery: Session has expired')
        return false
      }

      // Try to verify the token by making a test API call
      try {
        const response = await fetch('/api/profile', {
          headers: {
            Authorization: `Bearer ${session.access_token}`,
          },
        })

        if (response.ok) {
          console.log('AuthRecovery: Session is valid')
          return true
        } else {
          console.log('AuthRecovery: Session token is invalid (API returned', response.status, ')')
          return false
        }
      } catch (apiError) {
        console.error('AuthRecovery: Error verifying session with API:', apiError)
        return false
      }
    } catch (err) {
      console.error('AuthRecovery: Error checking session validity:', err)
      return false
    }
  }
}

/**
 * Singleton instance for easy access
 */
export const authRecovery = new AuthRecovery({
  maxRetries: 3,
  retryDelay: 2000,
  onRetry: (attempt) => console.log(`🔄 Retrying authentication (${attempt}/3)...`),
  onSuccess: () => console.log('✅ Authentication recovered successfully'),
  onFailure: (error) => console.error('❌ Authentication recovery failed:', error.message)
})

export default AuthRecovery