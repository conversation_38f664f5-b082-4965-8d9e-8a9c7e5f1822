# Epic 2 Master Data Management System

**Epic Goal:** Implement comprehensive CRUD operations for all master data entities (Products, Ports, Units of Measure, Companies including Customers, Factories, Shippers, Consignees, Notify Parties, Forwarder Agents, Carriers, and Drivers) with intelligent relationship management enabling streamlined shipment creation through cascading selection and default pre-population workflows.

## Story 2.1 Products and Units of Measure Management

As a CS representative,  
I want to create and manage products with their units of measure,  
so that I can maintain accurate product catalogs with consistent measurement standards for shipment creation.

### Acceptance Criteria

**1:** Product management interface allows creating products with name, code, description, category, HS code, and unit of measure selection.

**2:** Units of measure management supports creating measurement units with conversion factors, categories (weight, count, volume, length), and base unit relationships.

**3:** Product list view displays all products with search, filtering by category, and sorting capabilities.

**4:** Edit and delete operations maintain data integrity with validation for products in use by existing shipments.

**5:** Interface follows ShadCN UI patterns with responsive design and proper error handling.

## Story 2.2 Ports and Location Data Management

As an Admin,  
I want to manage port information with location data,  
so that shipping routes can be properly configured for shipment planning.

### Acceptance Criteria

**1:** Port management interface supports creating ports with code, name, country, port type (origin, destination, transit), and GPS coordinates.

**2:** Location data entry includes address fields with bilingual support (Thai/English) and coordinate capture.

**3:** Port list displays with search by name/code/country and filtering by port type.

**4:** Hybrid GPS coordinate storage automatically syncs between JSONB address field and dedicated point column.

**5:** Geographic search capabilities allow finding ports within specified radius of coordinates.

## Story 2.3 Company Management with Type-Specific Data

As a CS representative,  
I want to manage all company types with their specific information requirements,  
so that I can maintain accurate stakeholder data for shipment coordination.

### Acceptance Criteria

**1:** Company creation interface dynamically shows appropriate fields based on company type selection (customer, carrier, factory, shipper, consignee, notify_party, forwarder_agent).

**2:** Complex company types (customer, carrier, factory) use dedicated info tables while simple types use JSONB metadata storage.

**3:** Customer info captures customer type, credit limit, incoterms, and special requirements.

**4:** Carrier info includes fleet data, license types, coverage areas, insurance details, and capacity information.

**5:** Factory info stores production capacity, certifications, operating hours, and quality control details.

## Story 2.4 Factory and Forwarder Agent Management

As a CS representative,  
I want to manage factory and forwarder agent information with detailed profiles,  
so that I can efficiently select appropriate factories and agents during shipment creation.

### Acceptance Criteria

**1:** Factory management interface creates factories with name, location, capacity, certifications, and operational details.

**2:** Factory profile captures production capacity, operating hours, quality control standards, and contact information.

**3:** Forwarder Agent management interface creates agents with company details, service offerings, and contact information.

**4:** Forwarder Agent profile captures name, location, services provided (sea/land/rail), contact details, and operational coverage areas.

**5:** Both Factory and Forwarder Agent profiles support active/inactive status for operational control during shipment creation.

## Story 2.5 Driver Management and Carrier Association

As a CS representative,  
I want to manage driver information linked to carrier companies,  
so that transportation assignments can be efficiently coordinated.

### Acceptance Criteria

**1:** Driver management interface creates drivers associated with specific carrier companies.

**2:** Driver profile captures name, code, phone, Line ID, photo upload, and availability status.

**3:** Carrier company selection is restricted to companies with carrier type only.

**4:** Driver list view shows carrier association with filtering and search capabilities.

**5:** Photo upload supports image compression and secure storage via Supabase Storage.

## Story 2.6 Customer-Shipper Relationship Management

As a CS representative,  
I want to configure customer-shipper relationships with default preferences,  
so that shipment creation is streamlined with intelligent pre-population.

### Acceptance Criteria

**1:** Customer-shipper relationship interface allows associating customers with multiple shippers.

**2:** Default shipper designation ensures only one default per customer with automatic toggle of previous defaults.

**3:** Relationship status (active/inactive) controls visibility in shipment creation workflows.

**4:** Bulk import functionality supports initial relationship setup from existing data.

**5:** Relationship changes trigger real-time updates in shipment creation interfaces.

## Story 2.7 Customer-Product Relationship with Pricing Management

As a CS representative,  
I want to manage customer-product relationships with detailed pricing and specifications,  
so that product information is consistent and automatically populated during shipment creation.

### Acceptance Criteria

**1:** Customer-product interface captures pricing (CIF/FOB per KG), packaging specifications, and weight details.

**2:** Product specifications include packaging type, gross/net weight per package, quality grade, and handling instructions.

**3:** Default product designation per customer supports streamlined shipment creation.

**4:** Currency selection (THB, CNY, USD, EUR) with validation and conversion capabilities.

**5:** Temperature and ventilation requirements are captured for specialized products.

## Story 2.8 Consignee-Notify Party Relationship Management

As a CS representative,  
I want to configure consignee-notify party relationships with communication preferences,  
so that notification workflows are properly established for shipment coordination.

### Acceptance Criteria

**1:** Consignee-notify party relationship interface supports multiple notify parties per consignee.

**2:** Notification preferences capture communication channels (email, SMS, Line, WeChat) and priority ordering.

**3:** Default notify party selection enables automatic population during shipment creation.

**4:** Special instructions field captures consignee-specific handling or communication requirements.

**5:** Relationship validation ensures both entities have appropriate company types.
