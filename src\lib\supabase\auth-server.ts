import { createClient as createServer<PERSON>lient } from './server'
import { redirect } from 'next/navigation'
import type { User } from '@supabase/supabase-js'
import type { UserRole, UserProfile } from './auth'

// Get user profile from server
export async function getUserProfile(
  userId?: string
): Promise<UserProfile | null> {
  const supabase = createServerClient()

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('user_id', userId || (await supabase.auth.getUser()).data.user?.id)
    .single()

  if (error || !profile) {
    return null
  }

  return profile as UserProfile
}

// Get current user with profile
export async function getCurrentUser(): Promise<{
  user: User
  profile: UserProfile
} | null> {
  const supabase = createServerClient()

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (userError || !user) {
    return null
  }

  const profile = await getUserProfile(user.id)

  if (!profile) {
    return null
  }

  return { user, profile }
}

// Require authentication - redirect if not authenticated
export async function requireAuth(): Promise<{
  user: User
  profile: UserProfile
}> {
  const result = await getCurrentUser()

  if (!result) {
    redirect('/login')
  }

  return result
}

// Require specific role - redirect if not authorized
export async function requireRole(
  allowedRoles: UserRole | UserRole[]
): Promise<{ user: User; profile: UserProfile }> {
  const result = await requireAuth()

  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles]

  if (!roles.includes(result.profile.role)) {
    redirect('/unauthorized')
  }

  return result
}
