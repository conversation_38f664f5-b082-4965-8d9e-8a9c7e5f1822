'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import type { UserRole } from '@/lib/supabase/auth'

// Higher-order component for role-based access control
export function withRole<P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles: UserRole | UserRole[],
  fallbackComponent?: React.ComponentType<P>
) {
  return function RoleProtectedComponent(props: P) {
    const { user, profile, loading } = useAuth()
    const [hasAccess, setHasAccess] = useState<boolean | null>(null)

    useEffect(() => {
      if (loading) return

      if (!user || !profile) {
        setHasAccess(false)
        return
      }

      const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles]
      setHasAccess(roles.includes(profile.role))
    }, [user, profile, loading])

    if (loading || hasAccess === null) {
      return (
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
        </div>
      )
    }

    if (!hasAccess) {
      if (fallbackComponent) {
        const FallbackComponent = fallbackComponent
        return <FallbackComponent {...props} />
      }

      return (
        <div className="text-center p-4">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Access Denied
          </h2>
          <p className="text-gray-600">
            You don&apos;t have permission to view this content.
          </p>
        </div>
      )
    }

    return <Component {...props} />
  }
}
