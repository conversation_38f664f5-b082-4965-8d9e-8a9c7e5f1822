'use client'

import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type { ConsigneeNotifyPartyFilter } from '@/lib/validations/consignee-notify-parties'

// Consignee-Notify Party types (based on database schema)
export interface ConsigneeNotifyParty {
  id: string
  consignee_id: string
  notify_party_id: string
  is_default: boolean | null
  is_active: boolean | null
  notification_preferences: {
    email: boolean
    sms: boolean
    line: boolean
    wechat: boolean
  } | null
  priority_order: number | null
  special_instructions: string | null
  notes: string | null
  created_at: string | null
  updated_at: string | null
  // Joined data
  consignee?: {
    id: string
    name: string
    company_type: string
    contact_phone: string | null
  } | null
  notify_party?: {
    id: string
    name: string
    company_type: string
    contact_phone: string | null
    contact_email: string | null
  } | null
}

interface ConsigneeNotifyPartyStore {
  // Data state
  consigneeNotifyParties: ConsigneeNotifyParty[]
  loading: boolean
  error: string | null

  // Pagination state
  currentPage: number
  pageSize: number
  totalCount: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean

  // Filter and search state
  filter: ConsigneeNotifyPartyFilter
  searchTerm: string
  sortBy: keyof ConsigneeNotifyParty
  sortOrder: 'asc' | 'desc'

  // Selection state
  selectedConsigneeNotifyParties: Set<string>

  // Loading states for operations
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean

  // Actions - Data fetching
  fetchConsigneeNotifyParties: () => Promise<void>
  refreshConsigneeNotifyParties: () => Promise<void>

  // Actions - CRUD operations
  createConsigneeNotifyParty: (data: any) => Promise<ConsigneeNotifyParty>
  updateConsigneeNotifyParty: (
    id: string,
    data: any
  ) => Promise<ConsigneeNotifyParty>
  deleteConsigneeNotifyParty: (id: string) => Promise<void>
  bulkDeleteConsigneeNotifyParties: (ids: string[]) => Promise<void>

  // Actions - Default notify party management
  setDefaultNotifyParty: (
    consigneeId: string,
    relationshipId: string
  ) => Promise<void>
  getDefaultNotifyParty: (
    consigneeId: string
  ) => Promise<ConsigneeNotifyParty | null>

  // Actions - Filter and search
  setFilter: (filter: Partial<ConsigneeNotifyPartyFilter>) => void
  setSearchTerm: (term: string) => void
  setSorting: (
    sortBy: keyof ConsigneeNotifyParty,
    sortOrder: 'asc' | 'desc'
  ) => void
  clearFilters: () => void

  // Actions - Pagination
  setPage: (page: number) => void
  nextPage: () => void
  previousPage: () => void

  // Actions - Selection
  toggleConsigneeNotifyParty: (id: string) => void
  toggleAll: () => void
  clearSelection: () => void

  // Actions - Error handling
  clearError: () => void
}

export const useConsigneeNotifyPartyStore = create<ConsigneeNotifyPartyStore>(
  (set, get) => ({
    // Initial state
    consigneeNotifyParties: [],
    loading: false,
    error: null,

    // Pagination state
    currentPage: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,

    // Filter and search state
    filter: {},
    searchTerm: '',
    sortBy: 'created_at',
    sortOrder: 'desc',

    // Selection state
    selectedConsigneeNotifyParties: new Set<string>(),

    // Loading states
    isCreating: false,
    isUpdating: false,
    isDeleting: false,

    // Data fetching
    fetchConsigneeNotifyParties: async () => {
      console.log('🔍 Fetching consignee-notify party relationships...')
      const state = get()
      set({ loading: true, error: null })

      try {
        const supabase = createClient()
        console.log('📡 Supabase client created for relationships')

        // Build the base query
        let query = supabase.from('consignee_notify_parties').select(`
          *,
          consignee:companies!consignee_id(id, name, company_type, contact_phone),
          notify_party:companies!notify_party_id(id, name, company_type, contact_phone, contact_email)
        `)

        // Apply filters
        if (state.filter.consignee_id) {
          query = query.eq('consignee_id', state.filter.consignee_id)
        }

        if (state.filter.notify_party_id) {
          query = query.eq('notify_party_id', state.filter.notify_party_id)
        }

        if (state.filter.is_default !== undefined) {
          query = query.eq('is_default', state.filter.is_default)
        }

        if (state.filter.is_active !== undefined) {
          query = query.eq('is_active', state.filter.is_active)
        } else {
          // Default to active relationships only
          query = query.eq('is_active', true)
        }

        if (state.filter.priority_order) {
          query = query.eq('priority_order', state.filter.priority_order)
        }

        // Get company IDs for search if needed
        let consigneeIds: string[] = []
        let notifyPartyIds: string[] = []
        let hasSearchResults = true

        if (state.searchTerm) {
          console.log('🔍 Searching for companies matching:', state.searchTerm)

          try {
            // Get consignee company IDs that match the search term
            const { data: matchingConsignees, error: consigneeError } =
              await supabase
                .from('companies')
                .select('id')
                .eq('company_type', 'consignee')
                .ilike('name', `%${state.searchTerm}%`)

            if (consigneeError) {
              console.warn('⚠️ Error searching consignees:', consigneeError)
            }

            consigneeIds = matchingConsignees?.map(c => c.id) || []
            console.log('👥 Matching consignees:', consigneeIds.length)

            // Get notify party company IDs that match the search term
            const { data: matchingNotifyParties, error: notifyPartyError } =
              await supabase
                .from('companies')
                .select('id')
                .eq('company_type', 'notify_party')
                .ilike('name', `%${state.searchTerm}%`)

            if (notifyPartyError) {
              console.warn(
                '⚠️ Error searching notify parties:',
                notifyPartyError
              )
            }

            notifyPartyIds = matchingNotifyParties?.map(n => n.id) || []
            console.log('🔔 Matching notify parties:', notifyPartyIds.length)

            // Check if we have any matching companies
            if (consigneeIds.length === 0 && notifyPartyIds.length === 0) {
              console.log('❌ No matching companies found for search term')
              hasSearchResults = false
            }
          } catch (searchError) {
            console.error('❌ Error during company search:', searchError)
            hasSearchResults = false
          }

          // Apply search conditions to main query
          if (
            hasSearchResults &&
            (consigneeIds.length > 0 || notifyPartyIds.length > 0)
          ) {
            const searchConditions = []
            if (consigneeIds.length > 0) {
              searchConditions.push(
                `consignee_id.in.(${consigneeIds.join(',')})`
              )
            }
            if (notifyPartyIds.length > 0) {
              searchConditions.push(
                `notify_party_id.in.(${notifyPartyIds.join(',')})`
              )
            }
            query = query.or(searchConditions.join(','))
            console.log('✅ Applied search conditions:', searchConditions)
          }
        }

        // Get total count
        let countQuery = supabase
          .from('consignee_notify_parties')
          .select('*', { count: 'exact', head: true })

        // Apply same filters for count
        if (state.filter.consignee_id) {
          countQuery = countQuery.eq('consignee_id', state.filter.consignee_id)
        }

        if (state.filter.notify_party_id) {
          countQuery = countQuery.eq(
            'notify_party_id',
            state.filter.notify_party_id
          )
        }

        if (state.filter.is_default !== undefined) {
          countQuery = countQuery.eq('is_default', state.filter.is_default)
        }

        if (state.filter.is_active !== undefined) {
          countQuery = countQuery.eq('is_active', state.filter.is_active)
        } else {
          countQuery = countQuery.eq('is_active', true)
        }

        if (state.filter.priority_order) {
          countQuery = countQuery.eq(
            'priority_order',
            state.filter.priority_order
          )
        }

        // Apply search to count query
        if (
          state.searchTerm &&
          hasSearchResults &&
          (consigneeIds.length > 0 || notifyPartyIds.length > 0)
        ) {
          const searchConditions = []
          if (consigneeIds.length > 0) {
            searchConditions.push(`consignee_id.in.(${consigneeIds.join(',')})`)
          }
          if (notifyPartyIds.length > 0) {
            searchConditions.push(
              `notify_party_id.in.(${notifyPartyIds.join(',')})`
            )
          }
          countQuery = countQuery.or(searchConditions.join(','))
        }

        // If search found no results, return empty data immediately
        if (state.searchTerm && !hasSearchResults) {
          console.log('📭 No search results, returning empty data')
          set({
            consigneeNotifyParties: [],
            totalCount: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
            loading: false,
          })
          console.log('✅ Search completed: 0 items')
          return
        }

        const { count } = await countQuery

        // Apply sorting
        const sortColumn =
          state.sortBy === 'consignee'
            ? 'consignee_id'
            : state.sortBy === 'notify_party'
              ? 'notify_party_id'
              : state.sortBy
        query = query.order(sortColumn, {
          ascending: state.sortOrder === 'asc',
        })

        // Apply pagination
        const from = (state.currentPage - 1) * state.pageSize
        const to = from + state.pageSize - 1
        query = query.range(from, to)

        const { data, error } = await query

        console.log('📊 Relationships query result:', { data, error, count })

        if (error) throw error

        const totalCount = count || 0
        const totalPages = Math.ceil(totalCount / state.pageSize)

        set({
          consigneeNotifyParties: data || [],
          totalCount,
          totalPages,
          hasNextPage: state.currentPage < totalPages,
          hasPreviousPage: state.currentPage > 1,
          loading: false,
        })

        console.log('✅ Relationships loaded:', data?.length || 0, 'items')
      } catch (error) {
        console.error('❌ Error fetching consignee-notify parties:', error)

        // Provide user-friendly error message
        let errorMessage = 'Failed to load consignee-notify party relationships'
        if (error instanceof Error) {
          // Handle specific Supabase errors
          if (error.message.includes('JWT')) {
            errorMessage =
              'Authentication error. Please refresh the page and try again.'
          } else if (error.message.includes('permission')) {
            errorMessage = 'Permission denied. Please check your access rights.'
          } else if (
            error.message.includes('network') ||
            error.message.includes('fetch')
          ) {
            errorMessage =
              'Network error. Please check your connection and try again.'
          } else {
            errorMessage = `Database error: ${error.message}`
          }
        }

        set({
          error: errorMessage,
          consigneeNotifyParties: [], // Clear data on error
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
          loading: false,
        })
      }
    },

    refreshConsigneeNotifyParties: async () => {
      await get().fetchConsigneeNotifyParties()
    },

    // CRUD operations
    createConsigneeNotifyParty: async data => {
      set({ isCreating: true, error: null })

      try {
        const supabase = createClient()

        // If setting as default, first reset other defaults for this consignee
        if (data.is_default) {
          await supabase
            .from('consignee_notify_parties')
            .update({ is_default: false })
            .eq('consignee_id', data.consignee_id)
        }

        const { data: created, error } = await supabase
          .from('consignee_notify_parties')
          .insert(data)
          .select(
            `
          *,
          consignee:companies!consignee_id(id, name, company_type, contact_phone),
          notify_party:companies!notify_party_id(id, name, company_type, contact_phone, contact_email)
        `
          )
          .single()

        if (error) throw error

        // Refresh the list
        await get().fetchConsigneeNotifyParties()

        set({ isCreating: false })
        return created
      } catch (error) {
        console.error(
          'Error creating consignee-notify party relationship:',
          error
        )
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to create consignee-notify party relationship',
          isCreating: false,
        })
        throw error
      }
    },

    updateConsigneeNotifyParty: async (id, data) => {
      set({ isUpdating: true, error: null })

      try {
        const supabase = createClient()

        // If setting as default, first reset other defaults for this consignee
        if (data.is_default) {
          await supabase
            .from('consignee_notify_parties')
            .update({ is_default: false })
            .eq('consignee_id', data.consignee_id)
            .neq('id', id)
        }

        const { data: updated, error } = await supabase
          .from('consignee_notify_parties')
          .update(data)
          .eq('id', id)
          .select(
            `
          *,
          consignee:companies!consignee_id(id, name, company_type, contact_phone),
          notify_party:companies!notify_party_id(id, name, company_type, contact_phone, contact_email)
        `
          )
          .single()

        if (error) throw error

        // Refresh the list
        await get().fetchConsigneeNotifyParties()

        set({ isUpdating: false })
        return updated
      } catch (error) {
        console.error(
          'Error updating consignee-notify party relationship:',
          error
        )
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to update consignee-notify party relationship',
          isUpdating: false,
        })
        throw error
      }
    },

    deleteConsigneeNotifyParty: async id => {
      set({ isDeleting: true, error: null })

      try {
        const supabase = createClient()

        const { error } = await supabase
          .from('consignee_notify_parties')
          .delete()
          .eq('id', id)

        if (error) throw error

        // Refresh the list
        await get().fetchConsigneeNotifyParties()

        set({ isDeleting: false })
      } catch (error) {
        console.error(
          'Error deleting consignee-notify party relationship:',
          error
        )
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to delete consignee-notify party relationship',
          isDeleting: false,
        })
        throw error
      }
    },

    bulkDeleteConsigneeNotifyParties: async ids => {
      set({ isDeleting: true, error: null })

      try {
        const supabase = createClient()

        const { error } = await supabase
          .from('consignee_notify_parties')
          .delete()
          .in('id', ids)

        if (error) throw error

        // Clear selection and refresh
        set({ selectedConsigneeNotifyParties: new Set() })
        await get().fetchConsigneeNotifyParties()

        set({ isDeleting: false })
      } catch (error) {
        console.error(
          'Error bulk deleting consignee-notify party relationships:',
          error
        )
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to delete consignee-notify party relationships',
          isDeleting: false,
        })
        throw error
      }
    },

    // Default notify party management
    setDefaultNotifyParty: async (consigneeId, relationshipId) => {
      set({ isUpdating: true, error: null })

      try {
        const supabase = createClient()

        // Reset all defaults for this consignee
        await supabase
          .from('consignee_notify_parties')
          .update({ is_default: false })
          .eq('consignee_id', consigneeId)

        // Set new default
        const { error } = await supabase
          .from('consignee_notify_parties')
          .update({ is_default: true })
          .eq('id', relationshipId)

        if (error) throw error

        // Refresh the list
        await get().fetchConsigneeNotifyParties()

        set({ isUpdating: false })
      } catch (error) {
        console.error('Error setting default notify party:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to set default notify party',
          isUpdating: false,
        })
        throw error
      }
    },

    getDefaultNotifyParty: async consigneeId => {
      try {
        const supabase = createClient()

        const { data, error } = await supabase
          .from('consignee_notify_parties')
          .select(
            `
          *,
          consignee:companies!consignee_id(id, name, company_type, contact_phone),
          notify_party:companies!notify_party_id(id, name, company_type, contact_phone, contact_email)
        `
          )
          .eq('consignee_id', consigneeId)
          .eq('is_default', true)
          .eq('is_active', true)
          .single()

        if (error && error.code !== 'PGRST116') throw error // PGRST116 = no rows returned

        return data || null
      } catch (error) {
        console.error('Error getting default notify party:', error)
        return null
      }
    },

    // Filter and search actions
    setFilter: newFilter => {
      set(state => ({
        filter: { ...state.filter, ...newFilter },
        currentPage: 1,
      }))
      get().fetchConsigneeNotifyParties()
    },

    setSearchTerm: term => {
      set({ searchTerm: term, currentPage: 1 })
      get().fetchConsigneeNotifyParties()
    },

    setSorting: (sortBy, sortOrder) => {
      set({ sortBy, sortOrder, currentPage: 1 })
      get().fetchConsigneeNotifyParties()
    },

    clearFilters: () => {
      set({
        filter: {},
        searchTerm: '',
        currentPage: 1,
      })
      get().fetchConsigneeNotifyParties()
    },

    // Pagination actions
    setPage: page => {
      set({ currentPage: page })
      get().fetchConsigneeNotifyParties()
    },

    nextPage: () => {
      const state = get()
      if (state.hasNextPage) {
        set({ currentPage: state.currentPage + 1 })
        get().fetchConsigneeNotifyParties()
      }
    },

    previousPage: () => {
      const state = get()
      if (state.hasPreviousPage) {
        set({ currentPage: state.currentPage - 1 })
        get().fetchConsigneeNotifyParties()
      }
    },

    // Selection actions
    toggleConsigneeNotifyParty: id => {
      set(state => {
        const newSelection = new Set(state.selectedConsigneeNotifyParties)
        if (newSelection.has(id)) {
          newSelection.delete(id)
        } else {
          newSelection.add(id)
        }
        return { selectedConsigneeNotifyParties: newSelection }
      })
    },

    toggleAll: () => {
      set(state => {
        const allIds = state.consigneeNotifyParties.map(cnp => cnp.id)
        const isAllSelected = allIds.every(id =>
          state.selectedConsigneeNotifyParties.has(id)
        )

        if (isAllSelected) {
          return { selectedConsigneeNotifyParties: new Set() }
        } else {
          return { selectedConsigneeNotifyParties: new Set(allIds) }
        }
      })
    },

    clearSelection: () => {
      set({ selectedConsigneeNotifyParties: new Set() })
    },

    // Error handling
    clearError: () => {
      set({ error: null })
    },
  })
)

// Computed selectors
export const useConsigneeNotifyPartiesData = () => {
  const store = useConsigneeNotifyPartyStore()
  return {
    consigneeNotifyParties: store.consigneeNotifyParties,
    loading: store.loading,
    error: store.error,
    totalCount: store.totalCount,
    currentPage: store.currentPage,
    totalPages: store.totalPages,
    hasNextPage: store.hasNextPage,
    hasPreviousPage: store.hasPreviousPage,
  }
}

export const useConsigneeNotifyPartiesActions = () => {
  const store = useConsigneeNotifyPartyStore()
  return {
    fetchConsigneeNotifyParties: store.fetchConsigneeNotifyParties,
    refreshConsigneeNotifyParties: store.refreshConsigneeNotifyParties,
    createConsigneeNotifyParty: store.createConsigneeNotifyParty,
    updateConsigneeNotifyParty: store.updateConsigneeNotifyParty,
    deleteConsigneeNotifyParty: store.deleteConsigneeNotifyParty,
    bulkDeleteConsigneeNotifyParties: store.bulkDeleteConsigneeNotifyParties,
    setDefaultNotifyParty: store.setDefaultNotifyParty,
    getDefaultNotifyParty: store.getDefaultNotifyParty,
    setFilter: store.setFilter,
    setSearchTerm: store.setSearchTerm,
    setSorting: store.setSorting,
    clearFilters: store.clearFilters,
    setPage: store.setPage,
    nextPage: store.nextPage,
    previousPage: store.previousPage,
    toggleConsigneeNotifyParty: store.toggleConsigneeNotifyParty,
    toggleAll: store.toggleAll,
    clearSelection: store.clearSelection,
    clearError: store.clearError,
  }
}

export const useConsigneeNotifyPartiesSelection = () => {
  const store = useConsigneeNotifyPartyStore()
  const selectedCount = store.selectedConsigneeNotifyParties.size
  const isAllSelected =
    store.consigneeNotifyParties.length > 0 &&
    store.consigneeNotifyParties.every(cnp =>
      store.selectedConsigneeNotifyParties.has(cnp.id)
    )
  const isPartiallySelected = selectedCount > 0 && !isAllSelected

  return {
    selectedConsigneeNotifyParties: store.selectedConsigneeNotifyParties,
    selectedCount,
    isSelected: (id: string) => store.selectedConsigneeNotifyParties.has(id),
    isAllSelected,
    isPartiallySelected,
  }
}
