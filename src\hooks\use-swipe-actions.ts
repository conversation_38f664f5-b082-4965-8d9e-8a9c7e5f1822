'use client'

import { useRef, useState, useCallback } from 'react'

interface SwipeConfig {
  threshold?: number
  velocity?: number
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onLongPress?: () => void
  longPressDelay?: number
}

interface TouchPosition {
  x: number
  y: number
  time: number
}

export function useSwipeActions({
  threshold = 50,
  velocity = 0.3,
  onSwipeLeft,
  onSwipeRight,
  onLongPress,
  longPressDelay = 500,
}: SwipeConfig = {}) {
  const [isPressed, setIsPressed] = useState(false)
  const [swipeDistance, setSwipeDistance] = useState(0)
  const startPos = useRef<TouchPosition | null>(null)
  const currentPos = useRef<TouchPosition | null>(null)
  const longPressTimer = useRef<NodeJS.Timeout | null>(null)

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0]
    const now = Date.now()
    
    startPos.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: now,
    }
    currentPos.current = startPos.current
    setIsPressed(true)
    setSwipeDistance(0)

    // Start long press timer
    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        onLongPress()
        setIsPressed(false)
      }, longPressDelay)
    }
  }, [onLongPress, longPressDelay])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!startPos.current) return

    const touch = e.touches[0]
    const now = Date.now()
    
    currentPos.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: now,
    }

    const deltaX = currentPos.current.x - startPos.current.x
    const deltaY = Math.abs(currentPos.current.y - startPos.current.y)
    
    // Only track horizontal swipes (ignore if too much vertical movement)
    if (deltaY < 50) {
      setSwipeDistance(deltaX)
    }

    // Cancel long press if user moves too much
    if (longPressTimer.current && (Math.abs(deltaX) > 10 || deltaY > 10)) {
      clearTimeout(longPressTimer.current)
      longPressTimer.current = null
    }
  }, [])

  const handleTouchEnd = useCallback(() => {
    if (!startPos.current || !currentPos.current) return

    const deltaX = currentPos.current.x - startPos.current.x
    const deltaY = Math.abs(currentPos.current.y - startPos.current.y)
    const deltaTime = currentPos.current.time - startPos.current.time
    const velocityX = Math.abs(deltaX) / deltaTime

    // Clear long press timer
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current)
      longPressTimer.current = null
    }

    // Only trigger swipe if it's primarily horizontal
    if (deltaY < 100 && (Math.abs(deltaX) > threshold || velocityX > velocity)) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight()
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft()
      }
    }

    // Reset state
    startPos.current = null
    currentPos.current = null
    setIsPressed(false)
    setSwipeDistance(0)
  }, [threshold, velocity, onSwipeLeft, onSwipeRight])

  const handleTouchCancel = useCallback(() => {
    // Clear timers and reset state
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current)
      longPressTimer.current = null
    }
    
    startPos.current = null
    currentPos.current = null
    setIsPressed(false)
    setSwipeDistance(0)
  }, [])

  // Mouse events for desktop testing
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    const now = Date.now()
    startPos.current = {
      x: e.clientX,
      y: e.clientY,
      time: now,
    }
    currentPos.current = startPos.current
    setIsPressed(true)
    setSwipeDistance(0)

    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        onLongPress()
        setIsPressed(false)
      }, longPressDelay)
    }
  }, [onLongPress, longPressDelay])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!startPos.current || !isPressed) return

    const now = Date.now()
    currentPos.current = {
      x: e.clientX,
      y: e.clientY,
      time: now,
    }

    const deltaX = currentPos.current.x - startPos.current.x
    const deltaY = Math.abs(currentPos.current.y - startPos.current.y)
    
    if (deltaY < 50) {
      setSwipeDistance(deltaX)
    }

    if (longPressTimer.current && (Math.abs(deltaX) > 10 || deltaY > 10)) {
      clearTimeout(longPressTimer.current)
      longPressTimer.current = null
    }
  }, [isPressed])

  const handleMouseUp = useCallback(() => {
    if (!startPos.current || !currentPos.current) return

    const deltaX = currentPos.current.x - startPos.current.x
    const deltaY = Math.abs(currentPos.current.y - startPos.current.y)
    const deltaTime = currentPos.current.time - startPos.current.time
    const velocityX = Math.abs(deltaX) / deltaTime

    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current)
      longPressTimer.current = null
    }

    if (deltaY < 100 && (Math.abs(deltaX) > threshold || velocityX > velocity)) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight()
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft()
      }
    }

    startPos.current = null
    currentPos.current = null
    setIsPressed(false)
    setSwipeDistance(0)
  }, [threshold, velocity, onSwipeLeft, onSwipeRight])

  return {
    // Touch handlers
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    onTouchCancel: handleTouchCancel,
    
    // Mouse handlers for desktop
    onMouseDown: handleMouseDown,
    onMouseMove: handleMouseMove,
    onMouseUp: handleMouseUp,
    
    // State
    isPressed,
    swipeDistance,
    
    // Utility function to get swipe style
    getSwipeStyle: () => ({
      transform: `translateX(${Math.max(-100, Math.min(100, swipeDistance * 0.5))}px)`,
      transition: isPressed ? 'none' : 'transform 0.3s ease-out',
    }),
  }
}