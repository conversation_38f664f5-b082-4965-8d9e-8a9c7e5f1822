-- Create storage bucket for driver photos
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'driver-photos',
  'driver-photos', 
  false,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp']::text[]
) ON CONFLICT (id) DO NOTHING;

-- Create RLS policy for driver photos storage
-- Allow authenticated users to upload driver photos
CREATE POLICY "Allow authenticated uploads" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'driver-photos');

-- Allow authenticated users to view driver photos
CREATE POLICY "Allow authenticated downloads" ON storage.objects
FOR SELECT TO authenticated
USING (bucket_id = 'driver-photos');

-- Allow authenticated users to update driver photos 
CREATE POLICY "Allow authenticated updates" ON storage.objects
FOR UPDATE TO authenticated
USING (bucket_id = 'driver-photos')
WITH CHECK (bucket_id = 'driver-photos');

-- Allow authenticated users to delete driver photos
CREATE POLICY "Allow authenticated deletions" ON storage.objects
FOR DELETE TO authenticated
USING (bucket_id = 'driver-photos');

-- Enable RLS on storage.objects table
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;