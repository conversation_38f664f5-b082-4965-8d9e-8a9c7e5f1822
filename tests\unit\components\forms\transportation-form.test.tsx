import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { TransportationForm } from '@/components/forms/transportation-form/transportation-form'
import * as transportationHooks from '@/hooks/use-transportation'

// Mock the hooks
vi.mock('@/hooks/use-transportation')
vi.mock('@/components/forms/company-form/gps-coordinate-input', () => ({
  GPSCoordinateInput: ({ value, onChange }: any) => (
    <input
      data-testid="gps-input"
      value={value ? `${value.lat},${value.lng}` : ''}
      onChange={(e) => {
        const [lat, lng] = e.target.value.split(',')
        if (lat && lng) {
          onChange({ lat: parseFloat(lat), lng: parseFloat(lng) })
        } else {
          onChange(undefined)
        }
      }}
    />
  ),
}))

// Mock data
const mockCarriers = [
  {
    id: 'carrier-1',
    name: 'Test Carrier 1',
    company_type: 'carrier',
    phone: '+66123456789',
    email: '<EMAIL>',
    is_active: true,
  },
  {
    id: 'carrier-2', 
    name: 'Test Carrier 2',
    company_type: 'carrier',
    phone: '+66987654321',
    email: '<EMAIL>',
    is_active: true,
  },
]

const mockDrivers = [
  {
    id: 'driver-1',
    carrier_id: 'carrier-1',
    driver_first_name: 'John',
    driver_last_name: 'Doe',
    driver_code: 'D001',
    phone: '+66111222333',
    line_id: 'johndoe',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'driver-2',
    carrier_id: 'carrier-1',
    driver_first_name: 'Jane',
    driver_last_name: 'Smith',
    driver_code: 'D002',
    phone: '+66444555666',
    line_id: 'janesmith',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
]

describe('TransportationForm', () => {
  const mockOnSubmit = vi.fn()
  const mockOnCancel = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock the hooks
    vi.mocked(transportationHooks.useCarriers).mockReturnValue({
      carriers: mockCarriers,
      isLoading: false,
      error: null,
      refetchCarriers: vi.fn(),
    })

    vi.mocked(transportationHooks.useDriversByCarrier).mockReturnValue({
      drivers: mockDrivers,
      isLoading: false,
      error: null,
      refetchDrivers: vi.fn(),
    })

    vi.mocked(transportationHooks.useCoordinateUtils).mockReturnValue({
      parseCoordinates: vi.fn((coords) => {
        if (!coords) return undefined
        // Mock parsing PostGIS format POINT(lng lat)
        const match = coords.toString().match(/POINT\(([-\d.]+)\s+([-\d.]+)\)/)
        if (match) {
          return { lat: parseFloat(match[2]), lng: parseFloat(match[1]) }
        }
        return undefined
      }),
      formatCoordinates: vi.fn((lat, lng) => `${lat.toFixed(6)}, ${lng.toFixed(6)}`),
    })
  })

  const defaultProps = {
    shipmentId: 'shipment-123',
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
    isSubmitting: false,
    mode: 'create' as const,
  }

  describe('Form Rendering', () => {
    it('renders create form with all required fields', () => {
      render(<TransportationForm {...defaultProps} />)
      
      // Check if main elements are rendered
      expect(screen.getByText('Assign Transportation')).toBeInTheDocument()
      expect(screen.getByText('Carrier & Driver Information')).toBeInTheDocument()
      expect(screen.getByText('Location Information')).toBeInTheDocument()
      
      // Check required fields
      expect(screen.getByLabelText(/Carrier Company/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Delivery Location/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Assignment Date/)).toBeInTheDocument()
    })

    it('renders edit form with correct title and initial data', () => {
      const initialData = {
        carrier_id: 'carrier-1',
        driver_id: 'driver-1',
        vehicle_head_number: 'HEAD-123',
        vehicle_tail_number: 'TAIL-456',
        driver_phone: '+66123456789',
        assignment_date: '2024-01-01T10:00:00Z',
        pickup_container_location: 'Container Terminal A',
        pickup_product_location: 'Factory B',
        delivery_location: 'Warehouse C',
        estimated_distance: 150.5,
        notes: 'Handle with care',
      }

      render(
        <TransportationForm
          {...defaultProps}
          mode="edit"
          initialData={initialData}
        />
      )
      
      expect(screen.getByText('Edit Transportation Assignment')).toBeInTheDocument()
      expect(screen.getByDisplayValue('HEAD-123')).toBeInTheDocument()
      expect(screen.getByDisplayValue('TAIL-456')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Container Terminal A')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Warehouse C')).toBeInTheDocument()
    })
  })

  describe('Carrier Selection', () => {
    it('displays available carriers in dropdown', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      const carrierSelect = screen.getByRole('combobox', { name: /Carrier Company/ })
      await userEvent.click(carrierSelect)
      
      await waitFor(() => {
        expect(screen.getByText('Test Carrier 1')).toBeInTheDocument()
        expect(screen.getByText('Test Carrier 2')).toBeInTheDocument()
      })
    })

    it('shows loading state when carriers are loading', () => {
      vi.mocked(transportationHooks.useCarriers).mockReturnValue({
        carriers: [],
        isLoading: true,
        error: null,
        refetchCarriers: vi.fn(),
      })

      render(<TransportationForm {...defaultProps} />)
      
      const carrierSelect = screen.getByRole('combobox', { name: /Carrier Company/ })
      expect(carrierSelect).toBeInTheDocument()
    })

    it('resets driver selection when carrier changes', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      // Select first carrier
      const carrierSelect = screen.getByRole('combobox', { name: /Carrier Company/ })
      await userEvent.click(carrierSelect)
      await userEvent.click(screen.getByText('Test Carrier 1'))
      
      // Select a driver
      const driverSelect = screen.getByRole('combobox', { name: /Driver/ })
      await userEvent.click(driverSelect)
      await userEvent.click(screen.getByText('John Doe (D001)'))
      
      // Change carrier - should reset driver
      await userEvent.click(carrierSelect)
      await userEvent.click(screen.getByText('Test Carrier 2'))
      
      // Driver selection should be reset
      expect(driverSelect).toHaveTextContent('Select a driver')
    })
  })

  describe('Driver Selection', () => {
    it('displays drivers for selected carrier', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      // First select a carrier
      const carrierSelect = screen.getByRole('combobox', { name: /Carrier Company/ })
      await userEvent.click(carrierSelect)
      await userEvent.click(screen.getByText('Test Carrier 1'))
      
      // Then check driver options
      const driverSelect = screen.getByRole('combobox', { name: /Driver/ })
      await userEvent.click(driverSelect)
      
      await waitFor(() => {
        expect(screen.getByText('John Doe (D001)')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith (D002)')).toBeInTheDocument()
        expect(screen.getByText('No driver assigned')).toBeInTheDocument()
      })
    })

    it('is disabled when no carrier is selected', () => {
      render(<TransportationForm {...defaultProps} />)
      
      const driverSelect = screen.getByRole('combobox', { name: /Driver/ })
      expect(driverSelect).toBeDisabled()
      expect(driverSelect).toHaveTextContent('Select carrier first')
    })

    it('auto-fills driver phone when driver is selected', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      // Select carrier first
      const carrierSelect = screen.getByRole('combobox', { name: /Carrier Company/ })
      await userEvent.click(carrierSelect)
      await userEvent.click(screen.getByText('Test Carrier 1'))
      
      // Select driver
      const driverSelect = screen.getByRole('combobox', { name: /Driver/ })
      await userEvent.click(driverSelect)
      await userEvent.click(screen.getByText('John Doe (D001)'))
      
      // Check if phone is auto-filled
      await waitFor(() => {
        const phoneInput = screen.getByLabelText(/Driver Phone/)
        expect(phoneInput).toHaveValue('+66111222333')
      })
    })
  })

  describe('GPS Location Integration', () => {
    it('shows GPS options when toggle is clicked', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      expect(screen.queryByText('GPS Coordinates')).not.toBeInTheDocument()
      
      const toggleButton = screen.getByText('Show GPS Options')
      await userEvent.click(toggleButton)
      
      expect(screen.getByText('GPS Coordinates')).toBeInTheDocument()
      expect(screen.getByText('Hide GPS Options')).toBeInTheDocument()
    })

    it('calculates distance when pickup and delivery coordinates are set', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      // Show GPS options
      await userEvent.click(screen.getByText('Show GPS Options'))
      
      // Set coordinates (mocked GPS inputs)
      const gpsInputs = screen.getAllByTestId('gps-input')
      await userEvent.type(gpsInputs[1], '13.7563,100.5018') // Product pickup
      await userEvent.type(gpsInputs[2], '13.7308,100.5618') // Delivery
      
      // Should show auto-calculated distance message
      await waitFor(() => {
        expect(screen.getByText(/Auto-calculated:/)).toBeInTheDocument()
      })
    })
  })

  describe('Form Validation', () => {
    it('shows validation errors for required fields', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      // Try to submit without filling required fields
      const submitButton = screen.getByText('Assign Transportation')
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Please select a valid carrier company/)).toBeInTheDocument()
        expect(screen.getByText(/Delivery location is required/)).toBeInTheDocument()
      })
    })

    it('validates phone number format', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      const phoneInput = screen.getByLabelText(/Driver Phone/)
      await userEvent.type(phoneInput, 'invalid-phone')
      
      // Trigger validation by trying to submit
      const submitButton = screen.getByText('Assign Transportation')
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Please enter a valid phone number/)).toBeInTheDocument()
      })
    })

    it('validates distance range', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      const distanceInput = screen.getByLabelText(/Estimated Distance/)
      await userEvent.type(distanceInput, '15000') // Over max limit
      
      const submitButton = screen.getByText('Assign Transportation')
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Distance must be less than 10,000 km/)).toBeInTheDocument()
      })
    })
  })

  describe('Form Submission', () => {
    it('submits form with valid data in create mode', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      // Fill required fields
      const carrierSelect = screen.getByRole('combobox', { name: /Carrier Company/ })
      await userEvent.click(carrierSelect)
      await userEvent.click(screen.getByText('Test Carrier 1'))
      
      const deliveryInput = screen.getByLabelText(/Delivery Location/)
      await userEvent.type(deliveryInput, 'Test Warehouse')
      
      const notesInput = screen.getByLabelText(/Special Instructions/)
      await userEvent.type(notesInput, 'Handle with care')
      
      // Submit form
      const submitButton = screen.getByText('Assign Transportation')
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            shipment_id: 'shipment-123',
            carrier_id: 'carrier-1',
            delivery_location: 'Test Warehouse',
            notes: 'Handle with care',
          })
        )
      })
    })

    it('shows loading state during submission', async () => {
      render(<TransportationForm {...defaultProps} isSubmitting={true} />)
      
      const submitButton = screen.getByRole('button', { name: /Assign Transportation/ })
      expect(submitButton).toBeDisabled()
      expect(screen.getByText('Assign Transportation')).toBeInTheDocument()
    })

    it('calls onCancel when cancel button is clicked', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      const cancelButton = screen.getByText('Cancel')
      await userEvent.click(cancelButton)
      
      expect(mockOnCancel).toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('displays error when carriers fail to load', () => {
      vi.mocked(transportationHooks.useCarriers).mockReturnValue({
        carriers: [],
        isLoading: false,
        error: 'Failed to load carriers',
        refetchCarriers: vi.fn(),
      })

      render(<TransportationForm {...defaultProps} />)
      
      const carrierSelect = screen.getByRole('combobox', { name: /Carrier Company/ })
      expect(carrierSelect).toBeInTheDocument()
    })

    it('displays error when drivers fail to load', () => {
      vi.mocked(transportationHooks.useDriversByCarrier).mockReturnValue({
        drivers: [],
        isLoading: false,
        error: 'Failed to load drivers',
        refetchDrivers: vi.fn(),
      })

      render(<TransportationForm {...defaultProps} />)
      
      // Should still render the form but drivers dropdown will be empty
      const driverSelect = screen.getByRole('combobox', { name: /Driver/ })
      expect(driverSelect).toBeInTheDocument()
    })
  })

  describe('Character Limits', () => {
    it('enforces character limits on text fields', async () => {
      render(<TransportationForm {...defaultProps} />)
      
      const notesInput = screen.getByLabelText(/Special Instructions/)
      const longText = 'a'.repeat(1001) // Over 1000 character limit
      
      await userEvent.type(notesInput, longText)
      
      // Should show character count
      expect(screen.getByText('1001/1000 characters')).toBeInTheDocument()
      
      // Trigger validation
      const submitButton = screen.getByText('Assign Transportation')
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Notes must be less than 1000 characters/)).toBeInTheDocument()
      })
    })
  })
})