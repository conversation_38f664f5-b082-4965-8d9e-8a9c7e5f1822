import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  generateShipmentNumber,
  generateUniqueShipmentNumber,
  validateShipmentNumberFormat,
  parseShipmentNumber,
  getTransportModeFromCode,
  extractDateFromShipmentNumber,
  checkShipmentNumberExists,
  TRANSPORT_MODE_CODES,
} from '../shipment-number-generator'
import { createClient } from '@/lib/supabase/client'

// Mock Supabase client
vi.mock('@/lib/supabase/client')

const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn(),
        limit: vi.fn(),
      })),
    })),
    update: vi.fn(() => ({
      eq: vi.fn(),
    })),
    insert: vi.fn(),
  })),
}

describe('Shipment Number Generator', () => {
  beforeEach(() => {
    vi.mocked(createClient).mockReturnValue(mockSupabase as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('TRANSPORT_MODE_CODES', () => {
    it('should have correct transport mode codes', () => {
      expect(TRANSPORT_MODE_CODES).toEqual({
        sea: 'SEA',
        land: 'LND',
        rail: 'RAL',
      })
    })
  })

  describe('generateShipmentNumber', () => {
    it('should generate correct shipment number format', async () => {
      // Mock successful running number retrieval
      const mockSelect = vi.fn().mockResolvedValue({
        data: { id: 1, current_number: 5 },
        error: null,
      })
      const mockUpdate = vi.fn().mockResolvedValue({ error: null })

      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            eq: () => ({
              eq: () => ({
                single: mockSelect,
              }),
            }),
          }),
        }),
        update: () => ({
          eq: mockUpdate,
        }),
      })

      const testDate = new Date('2024-03-15')
      const result = await generateShipmentNumber({
        transportMode: 'sea',
        portCode: 'THBKK',
        date: testDate,
      })

      expect(result).toEqual({
        prefix: 'EX',
        modeCode: 'SEA',
        portCode: 'THBKK',
        dateCode: '240315',
        runningNumber: 6,
        fullNumber: 'EXSEA-THBKK-240315-006',
      })
    })

    it('should create new record for first-time generation', async () => {
      // Mock no existing record found
      const mockSelect = vi.fn().mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      })
      const mockInsert = vi.fn().mockResolvedValue({ error: null })

      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            eq: () => ({
              eq: () => ({
                single: mockSelect,
              }),
            }),
          }),
        }),
        insert: mockInsert,
      })

      const result = await generateShipmentNumber({
        transportMode: 'land',
        portCode: 'THBKK',
      })

      expect(result.runningNumber).toBe(1)
      expect(result.fullNumber).toMatch(/^EXLND-THBKK-\d{6}-001$/)
      expect(mockInsert).toHaveBeenCalled()
    })

    it('should handle fallback on database error', async () => {
      // Mock database error
      const mockSelect = vi.fn().mockRejectedValue(new Error('Database error'))
      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            eq: () => ({
              eq: () => ({
                single: mockSelect,
              }),
            }),
          }),
        }),
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const result = await generateShipmentNumber({
        transportMode: 'rail',
        portCode: 'THBKK',
      })

      expect(result.runningNumber).toBeGreaterThanOrEqual(100)
      expect(result.runningNumber).toBeLessThanOrEqual(999)
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error getting next running number:',
        expect.any(Error)
      )
      expect(warnSpy).toHaveBeenCalledWith(
        `Using fallback running number: ${result.runningNumber}`
      )

      consoleSpy.mockRestore()
      warnSpy.mockRestore()
    })

    it('should throw error for invalid transport mode', async () => {
      await expect(
        generateShipmentNumber({
          transportMode: 'invalid' as any,
          portCode: 'THBKK',
        })
      ).rejects.toThrow('Invalid transport mode: invalid')
    })

    it('should throw error for missing required parameters', async () => {
      await expect(
        generateShipmentNumber({
          transportMode: '' as any,
          portCode: 'THBKK',
        })
      ).rejects.toThrow(
        'Transport mode and port code are required for shipment number generation'
      )
    })
  })

  describe('validateShipmentNumberFormat', () => {
    it('should validate correct shipment number formats', () => {
      expect(validateShipmentNumberFormat('EXSEA-THBKK-240315-001')).toBe(true)
      expect(validateShipmentNumberFormat('EXLND-HKHKG-231201-999')).toBe(true)
      expect(validateShipmentNumberFormat('EXRAL-SGSIN-240229-123')).toBe(true)
    })

    it('should reject invalid formats', () => {
      expect(validateShipmentNumberFormat('')).toBe(false)
      expect(validateShipmentNumberFormat('INVALID-FORMAT')).toBe(false)
      expect(validateShipmentNumberFormat('EX-THBKK-240315-001')).toBe(false)
      expect(validateShipmentNumberFormat('EXSEA-TH-240315-001')).toBe(false)
      expect(validateShipmentNumberFormat('EXSEA-THBKK-24031-001')).toBe(false)
      expect(validateShipmentNumberFormat('EXSEA-THBKK-240315-01')).toBe(false)
    })
  })

  describe('parseShipmentNumber', () => {
    it('should parse valid shipment numbers', () => {
      const result = parseShipmentNumber('EXSEA-THBKK-240315-123')
      expect(result).toEqual({
        prefix: 'EX',
        modeCode: 'SEA',
        portCode: 'THBKK',
        dateCode: '240315',
        runningNumber: 123,
        fullNumber: 'EXSEA-THBKK-240315-123',
      })
    })

    it('should return null for invalid shipment numbers', () => {
      expect(parseShipmentNumber('INVALID-FORMAT')).toBeNull()
      expect(parseShipmentNumber('')).toBeNull()
      expect(parseShipmentNumber('EX-THBKK-240315-001')).toBeNull()
    })
  })

  describe('getTransportModeFromCode', () => {
    it('should return correct transport modes', () => {
      expect(getTransportModeFromCode('SEA')).toBe('sea')
      expect(getTransportModeFromCode('LND')).toBe('land')
      expect(getTransportModeFromCode('RAL')).toBe('rail')
    })

    it('should return null for invalid codes', () => {
      expect(getTransportModeFromCode('INVALID')).toBeNull()
      expect(getTransportModeFromCode('')).toBeNull()
    })
  })

  describe('extractDateFromShipmentNumber', () => {
    it('should extract correct dates', () => {
      const date = extractDateFromShipmentNumber('EXSEA-THBKK-240315-001')
      expect(date).toEqual(new Date(2024, 2, 15)) // Month is 0-indexed
    })

    it('should return null for invalid shipment numbers', () => {
      expect(extractDateFromShipmentNumber('INVALID-FORMAT')).toBeNull()
      expect(extractDateFromShipmentNumber('')).toBeNull()
    })

    it('should return null for invalid dates', () => {
      expect(extractDateFromShipmentNumber('EXSEA-THBKK-241332-001')).toBeNull()
    })
  })

  describe('checkShipmentNumberExists', () => {
    it('should return true if shipment number exists', async () => {
      const mockSelect = vi.fn().mockResolvedValue({
        data: [{ id: 1 }],
        error: null,
      })

      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            limit: mockSelect,
          }),
        }),
      })

      const exists = await checkShipmentNumberExists('EXSEA-THBKK-240315-001')
      expect(exists).toBe(true)
    })

    it('should return false if shipment number does not exist', async () => {
      const mockSelect = vi.fn().mockResolvedValue({
        data: [],
        error: null,
      })

      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            limit: mockSelect,
          }),
        }),
      })

      const exists = await checkShipmentNumberExists('EXSEA-THBKK-240315-999')
      expect(exists).toBe(false)
    })

    it('should return false on database error', async () => {
      const mockSelect = vi.fn().mockRejectedValue(new Error('Database error'))
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            limit: mockSelect,
          }),
        }),
      })

      const exists = await checkShipmentNumberExists('EXSEA-THBKK-240315-001')
      expect(exists).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error checking shipment number existence:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe('generateUniqueShipmentNumber', () => {
    it('should generate unique shipment number on first try', async () => {
      // Mock successful number generation
      const mockSelect = vi.fn().mockResolvedValue({
        data: { id: 1, current_number: 5 },
        error: null,
      })
      const mockUpdate = vi.fn().mockResolvedValue({ error: null })
      const mockExists = vi.fn().mockResolvedValue({
        data: [],
        error: null,
      })

      mockSupabase.from.mockImplementation(table => {
        if (table === 'shipment_running_numbers') {
          return {
            select: () => ({
              eq: () => ({
                eq: () => ({
                  eq: () => ({
                    single: mockSelect,
                  }),
                }),
              }),
            }),
            update: () => ({
              eq: mockUpdate,
            }),
          }
        } else if (table === 'shipments') {
          return {
            select: () => ({
              eq: () => ({
                limit: mockExists,
              }),
            }),
          }
        }
      })

      const result = await generateUniqueShipmentNumber({
        transportMode: 'sea',
        portCode: 'THBKK',
      })

      expect(result.runningNumber).toBe(6)
      expect(result.fullNumber).toMatch(/^EXSEA-THBKK-\d{6}-006$/)
    })

    it('should retry on collision and eventually succeed', async () => {
      let callCount = 0
      const mockSelect = vi.fn().mockResolvedValue({
        data: { id: 1, current_number: 5 },
        error: null,
      })
      const mockUpdate = vi.fn().mockResolvedValue({ error: null })
      const mockExists = vi.fn().mockImplementation(() => {
        callCount++
        if (callCount === 1) {
          return Promise.resolve({ data: [{ id: 1 }], error: null }) // First call: exists
        }
        return Promise.resolve({ data: [], error: null }) // Second call: doesn't exist
      })

      mockSupabase.from.mockImplementation(table => {
        if (table === 'shipment_running_numbers') {
          return {
            select: () => ({
              eq: () => ({
                eq: () => ({
                  eq: () => ({
                    single: mockSelect,
                  }),
                }),
              }),
            }),
            update: () => ({
              eq: mockUpdate,
            }),
          }
        } else if (table === 'shipments') {
          return {
            select: () => ({
              eq: () => ({
                limit: mockExists,
              }),
            }),
          }
        }
      })

      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const result = await generateUniqueShipmentNumber({
        transportMode: 'sea',
        portCode: 'THBKK',
      })

      expect(result).toBeDefined()
      expect(warnSpy).toHaveBeenCalledWith(
        expect.stringContaining('Shipment number collision detected')
      )

      warnSpy.mockRestore()
    })

    it('should throw error after maximum retries', async () => {
      const mockSelect = vi.fn().mockResolvedValue({
        data: { id: 1, current_number: 5 },
        error: null,
      })
      const mockUpdate = vi.fn().mockResolvedValue({ error: null })
      const mockExists = vi.fn().mockResolvedValue({
        data: [{ id: 1 }], // Always exists (collision)
        error: null,
      })

      mockSupabase.from.mockImplementation(table => {
        if (table === 'shipment_running_numbers') {
          return {
            select: () => ({
              eq: () => ({
                eq: () => ({
                  eq: () => ({
                    single: mockSelect,
                  }),
                }),
              }),
            }),
            update: () => ({
              eq: mockUpdate,
            }),
          }
        } else if (table === 'shipments') {
          return {
            select: () => ({
              eq: () => ({
                limit: mockExists,
              }),
            }),
          }
        }
      })

      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      await expect(
        generateUniqueShipmentNumber({
          transportMode: 'sea',
          portCode: 'THBKK',
        })
      ).rejects.toThrow(
        'Failed to generate unique shipment number after 3 attempts'
      )

      warnSpy.mockRestore()
    })
  })
})
