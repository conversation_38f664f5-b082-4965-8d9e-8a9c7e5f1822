# Status-Based Photo Display Implementation Plan

## Overview
Add a new section to display photos uploaded by drivers during status updates, grouped by shipment status, with admin/CS management capabilities.

## Database & Storage Structure (Already Exists)
- **`status_images`** table with fields: `status_history_id`, `shipment_id`, `image_url`, `image_path`, `uploaded_by`, etc.
- **`status_history`** table linking status changes to shipments
- **Photos stored in Supabase Storage bucket: `status-images`**
- Storage path structure: `status-photos/{shipmentId}/{fileName}`

## Phase 1: Display Status Photos

### 1.1 Create Status Photos Hook
**File: `src/hooks/use-status-photos.ts`**
- Fetch photos from `status_images` table grouped by `status_history_id`
- Generate signed URLs from `status-images` bucket
- Real-time subscriptions for new photo uploads
- Error handling and loading states
- Cache management for performance

### 1.2 Create Status Photos Component
**File: `src/components/data-display/status-photos-gallery.tsx`**
- Gallery component with status-based grouping
- Thumbnail grid with lightbox functionality
- Photo metadata display (date, uploader, GPS coordinates)
- Status timeline integration
- Responsive design optimized for mobile

### 1.3 Integrate into Shipment Details Page
**Location: Between Status Timeline and Companies sections (around line 1535)**
- New "Status Documentation" section
- Group photos by status with collapsible accordions
- Show photo count badges per status
- Filter and search functionality
- Status type indicators

## Phase 2: Admin/CS Photo Management

### 2.1 Photo Management Components
**Files:**
- `src/components/forms/status-photos/photo-upload-form.tsx`
- `src/components/forms/status-photos/photo-management-panel.tsx`
- `src/components/forms/status-photos/photo-delete-dialog.tsx`

**Features:**
- Upload additional photos to existing status updates
- Delete photos with confirmation (admin/CS only)
- Bulk photo operations
- Role-based access control integration
- File validation and upload progress

### 2.2 Enhanced Gallery with Management
**Update: `status-photos-gallery.tsx`**
- Management toolbar for admin/CS users
- Photo selection and bulk actions
- Drag & drop upload functionality
- Status assignment for manually uploaded photos
- Audit trail for photo management actions

## Implementation Details

### Photo Display Structure
```
Status Documentation
├── 📦 Empty Container Picked (3 photos) [Expand/Collapse]
│   ├── [Photo Grid: 3 thumbnails]
│   └── 👤 Uploaded by: John Doe, 2 hours ago
├── 🏭 Arrived at Factory (5 photos) [Expand/Collapse]
│   ├── [Photo Grid: 5 thumbnails] 
│   └── 👤 Uploaded by: John Doe, 1 hour ago
└── 📸 Loading Started (2 photos) [Expand/Collapse]
    ├── [Photo Grid: 2 thumbnails]
    └── 👤 Uploaded by: Jane Smith, 30 minutes ago
```

### Recommended Page Location
**Position: Between Status Timeline and Companies sections**
- **Logical flow**: Status Timeline → Status Photos → Stakeholder Info
- **Visual balance**: Keeps status-related information grouped
- **Right column placement**: Utilizes available vertical space
- **Mobile responsive**: Stacks naturally on mobile devices

### Technical Implementation

#### Data Fetching Strategy
```typescript
// Hook structure
const useStatusPhotos = (shipmentId: string) => {
  // Fetch from status_images table
  // Join with status_history for status context
  // Generate signed URLs from 'status-images' bucket
  // Group by status_history_id
  // Real-time subscriptions
}
```

#### Storage Integration
- **Bucket**: `status_images` (Supabase Storage)
- **Path pattern**: `status-photos/{shipmentId}/{statusHistoryId}_{timestamp}_{fileName}`
- **Signed URLs**: Generate temporary access URLs for security
- **Thumbnail generation**: Auto-generate optimized thumbnails

### Features & User Experience

#### Display Features
- **Collapsible sections** by status for space efficiency
- **Photo count badges** with status icons
- **Thumbnail grid** (responsive: 2-3 mobile, 4-6 desktop)
- **Lightbox modal** for full-size viewing with metadata
- **Loading states** and skeleton placeholders
- **Empty states** for statuses without photos

#### Management Features (Admin/CS)
- **Upload button** per status section
- **Delete confirmation** with audit logging
- **Bulk selection** and operations
- **Photo metadata editing** (descriptions, tags)
- **Access control** based on user roles

#### Performance Optimizations
- **Lazy loading** for off-screen images
- **Image compression** and WebP format support
- **Caching strategy** for frequently accessed photos
- **Progressive loading** with blur-to-sharp transition
- **Virtual scrolling** for large photo collections

### Security & Access Control
- **Row Level Security (RLS)** policies for photo access
- **Role-based permissions** (admin/CS can manage, others view-only)
- **Signed URL expiration** for temporary access
- **File validation** (type, size, dimensions)
- **Upload rate limiting** to prevent abuse

## Phase 3: Advanced Features
- **Photo annotations** and comments system
- **Category tagging** (loading, container condition, product quality)
- **AI-powered analysis** (OCR for documents, quality assessment)
- **Stakeholder sharing** with controlled access
- **Mobile app integration** improvements
- **Export functionality** for reports and documentation

## Database Queries Required

### Main Query for Status Photos
```sql
SELECT 
  si.*,
  sh.status,
  sh.created_at as status_date,
  p.first_name || ' ' || p.last_name as uploader_name
FROM status_images si
JOIN status_history sh ON si.status_history_id = sh.id
LEFT JOIN profiles p ON si.uploaded_by = p.user_id
WHERE si.shipment_id = ?
ORDER BY sh.created_at DESC, si.created_at ASC;
```

### Query for Photo Management
```sql
-- Get photos with management metadata
SELECT 
  si.*,
  sh.status,
  sh.created_at as status_date,
  p.first_name || ' ' || p.last_name as uploader_name,
  p.role as uploader_role
FROM status_images si
JOIN status_history sh ON si.status_history_id = sh.id
LEFT JOIN profiles p ON si.uploaded_by = p.user_id
WHERE si.shipment_id = ?
AND (
  -- User can see photos they uploaded
  si.uploaded_by = auth.uid()
  OR
  -- Admin/CS can see all photos
  p.role IN ('admin', 'customer_service')
)
ORDER BY sh.created_at DESC, si.created_at ASC;
```

## UI/UX Considerations

### Mobile Experience
- **Touch-friendly** photo thumbnails (minimum 44px touch target)
- **Swipe gestures** for photo navigation in lightbox
- **Optimized loading** for mobile networks
- **Offline capability** for viewing cached photos

### Accessibility
- **Alt text** for all images
- **Keyboard navigation** support
- **Screen reader** compatibility
- **High contrast** mode support
- **Focus indicators** for interactive elements

### Performance Metrics
- **Initial load time**: < 2 seconds for photo thumbnails
- **Lightbox open time**: < 500ms
- **Upload progress**: Real-time feedback
- **Error recovery**: Graceful handling of failed uploads

## Testing Strategy

### Unit Tests
- Photo fetching and caching logic
- URL generation and validation
- Permission checking functions
- Image optimization utilities

### Integration Tests
- Photo upload workflow
- Status-photo associations
- Real-time updates
- Error handling scenarios

### E2E Tests
- Complete photo viewing workflow
- Admin photo management features
- Mobile responsive behavior
- Cross-browser compatibility

## Development Timeline

### Phase 1 (Week 1-2)
- [x] Create `use-status-photos` hook
- [x] Build basic status photos gallery component
- [x] Integrate into shipment details page
- [x] Implement basic lightbox functionality

### Phase 2 (Week 3-4)
- [x] Add admin/CS photo management features
- [x] Implement photo upload functionality
- [x] Add delete confirmation dialogs
- [x] Create audit trail for photo management

### Phase 3 (Week 5-6)
- [ ] Advanced features (annotations, categories)
- [ ] Performance optimizations
- [ ] Mobile app integration
- [ ] Testing and refinement

## Success Metrics
- **User engagement**: Time spent viewing photos increases by 40%
- **Admin efficiency**: Photo management tasks reduced by 60%
- **Error reduction**: Photo-related support tickets decrease by 50%
- **Performance**: Page load time remains under 3 seconds with photos