import { z } from 'zod'

// Coordinate validation schema (reused from companies)
export const coordinatesSchema = z.object({
  lat: z
    .number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90'),
  lng: z
    .number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180'),
})

// Transportation assignment form validation schema
export const transportationFormSchema = z.object({
  carrier_id: z.string().uuid('Please select a valid carrier company'),
  driver_id: z.string().uuid('Please select a valid driver').optional().or(z.literal('')),
  vehicle_head_number: z
    .string()
    .max(20, 'Vehicle head number must be less than 20 characters')
    .optional()
    .or(z.literal('')),
  vehicle_tail_number: z
    .string()
    .max(20, 'Vehicle tail number must be less than 20 characters')  
    .optional()
    .or(z.literal('')),
  driver_phone: z
    .string()
    .max(20, 'Driver phone must be less than 20 characters')
    .regex(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number')
    .optional()
    .or(z.literal('')),
  assignment_date: z.string().datetime('Please select a valid assignment date'),
  pickup_container_location: z
    .string()
    .max(255, 'Pickup container location must be less than 255 characters')
    .optional()
    .or(z.literal('')),
  pickup_container_gps_coordinates: coordinatesSchema.optional(),
  pickup_product_location: z
    .string()
    .max(255, 'Pickup product location must be less than 255 characters')
    .optional()
    .or(z.literal('')),
  pickup_product_gps_coordinates: coordinatesSchema.optional(),
  delivery_location: z
    .string()
    .max(255, 'Delivery location must be less than 255 characters')
    .min(1, 'Delivery location is required'),
  delivery_gps_coordinates: coordinatesSchema.optional(),
  estimated_distance: z
    .number()
    .min(0, 'Distance must be positive')
    .max(10000, 'Distance must be less than 10,000 km')
    .optional(),
  notes: z
    .string()
    .max(1000, 'Notes must be less than 1000 characters')
    .optional()
    .or(z.literal('')),
})

// Transportation assignment update schema (for editing existing assignments)
export const transportationUpdateSchema = transportationFormSchema.partial().extend({
  id: z.string().uuid('Invalid transportation assignment ID'),
})

// Transportation filter schema
export const transportationFilterSchema = z.object({
  shipment_id: z.string().uuid().optional(),
  carrier_id: z.string().uuid().optional(),
  driver_id: z.string().uuid().optional(),
  assignment_date_from: z.string().datetime().optional(),
  assignment_date_to: z.string().datetime().optional(),
})

// Transportation search schema
export const transportationSearchSchema = z.object({
  search_term: z
    .string()
    .max(100, 'Search term must be less than 100 characters')
    .optional(),
})

// Transportation sorting schema
export const transportationSortSchema = z.object({
  sort_by: z
    .enum([
      'assignment_date',
      'delivery_location',
      'estimated_distance',
      'created_at',
    ])
    .default('assignment_date'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
})

// Combined transportation query schema
export const transportationQuerySchema = transportationFilterSchema
  .merge(transportationSearchSchema)
  .merge(transportationSortSchema)
  .extend({
    page: z.number().int().min(1).default(1),
    page_size: z.number().int().min(1).max(100).default(20),
  })

// Type exports
export type TransportationForm = z.infer<typeof transportationFormSchema>
export type TransportationUpdate = z.infer<typeof transportationUpdateSchema>
export type TransportationFilter = z.infer<typeof transportationFilterSchema>
export type TransportationSearch = z.infer<typeof transportationSearchSchema>
export type TransportationSort = z.infer<typeof transportationSortSchema>
export type TransportationQuery = z.infer<typeof transportationQuerySchema>
export type Coordinates = z.infer<typeof coordinatesSchema>

// Validation functions
export const validateTransportationForm = (data: unknown) => {
  return transportationFormSchema.safeParse(data)
}

export const validateTransportationUpdate = (data: unknown) => {
  return transportationUpdateSchema.safeParse(data)
}

export const validateTransportationQuery = (data: unknown) => {
  return transportationQuerySchema.safeParse(data)
}

// Carrier validation helper - ensures only carrier companies are selectable
export const validateCarrierCompany = (companyType: string): boolean => {
  return companyType === 'carrier'
}

// Distance calculation helper (basic Haversine formula)
export const calculateDistance = (
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number => {
  const R = 6371 // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return Math.round(R * c * 100) / 100 // Round to 2 decimal places
}