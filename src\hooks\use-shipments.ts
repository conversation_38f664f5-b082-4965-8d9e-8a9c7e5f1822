'use client'

import { useEffect, useCallback, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useShipmentCreationStore } from '@/stores/shipment-creation-store'
import { generateUniqueShipmentNumber } from '@/lib/utils/shipment-number-generator'
import { updateShipmentTotals } from '@/lib/utils/shipment-totals'
import type {
  ShipmentCreation,
  ShipmentUpdate,
  ShipmentFilter,
  DocumentUpload,
  TransportMode,
} from '@/lib/validations/shipment'

// Types for Supabase database
export interface Shipment {
  id: string
  shipment_number: string
  transportation_mode: TransportMode
  customer_id: string
  shipper_id: string | null
  consignee_id: string | null
  notify_party_id: string | null
  factory_id: string
  forwarder_agent_id: string
  origin_port_id: string
  destination_port_id: string
  liner: string | null
  vessel_name: string | null
  voyage_number: string | null
  booking_number: string | null
  invoice_number: string | null
  etd_date: string
  eta_date: string
  closing_time: string
  cy_date: string | null
  number_of_pallet: number | null
  pallet_description: string | null
  ephyto_refno: string | null
  currency_code: string
  total_weight: number | null
  total_volume: number | null
  status: string
  transportation_mode: TransportMode
  notes: string | null
  metadata: Record<string, any> | null
  created_by: string | null
  created_at: string
  updated_at: string
  total_value_cif: number | null
  total_value_fob: number | null
}

export interface ShipmentWithRelations extends Shipment {
  customer: { id: string; name: string; company_type: string } | null
  shipper: { id: string; name: string } | null
  consignee: { id: string; name: string } | null
  notify_party: { id: string; name: string } | null
  factory: { id: string; name: string; contact_phone?: string } | null
  forwarder_agent: { id: string; name: string; contact_phone?: string } | null
  origin_port: {
    id: string
    port_name: string
    code: string
    country: string
  } | null
  destination_port: {
    id: string
    port_name: string
    code: string
    country: string
  } | null
}

export interface ShipmentInsert extends Omit<ShipmentCreation, 'id'> {}

// Document storage interface
export interface ShipmentDocument {
  id: string
  shipment_id: string
  file_name: string
  file_size: number
  file_type: string
  storage_path: string
  document_type: string
  uploaded_at: string
  uploaded_by: string
}

// Store state interface
interface ShipmentsState {
  shipments: Shipment[]
  selectedShipment: ShipmentWithRelations | null
  loading: boolean
  error: string | null
  creating: boolean
  updating: boolean
  deleting: boolean

  // Filters and pagination
  filter: ShipmentFilter | null
  searchTerm: string
  currentPage: number
  pageSize: number
  totalCount: number
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

// Hook for shipments data management
export function useShipmentsData() {
  const [state, setState] = useState<ShipmentsState>({
    shipments: [],
    selectedShipment: null,
    loading: false,
    error: null,
    creating: false,
    updating: false,
    deleting: false,
    filter: null,
    searchTerm: '',
    currentPage: 1,
    pageSize: 20,
    totalCount: 0,
    sortBy: 'created_at',
    sortOrder: 'desc',
  })

  const supabase = createClient()

  // Fetch shipments with filtering and pagination
  const fetchShipments = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      let query = supabase.from('shipments').select(`
          *,
          customer:companies!customer_id(id, name, company_type),
          shipper:companies!shipper_id(id, name),
          consignee:companies!consignee_id(id, name),
          notify_party:companies!notify_party_id(id, name),
          factory:companies!factory_id(id, name, contact_phone),
          forwarder_agent:companies!forwarder_agent_id(id, name, contact_phone),
          origin_port:ports!origin_port_id(id, port_name, code, country),
          destination_port:ports!destination_port_id(id, port_name, code, country)
        `)

      // Apply filters
      if (state.filter) {
        if (state.filter.transportation_mode) {
          query = query.eq(
            'transportation_mode',
            state.filter.transportation_mode
          )
        }
        if (state.filter.status) {
          query = query.eq('status', state.filter.status)
        }
        if (state.filter.customer_id) {
          query = query.eq('customer_id', state.filter.customer_id)
        }
        if (state.filter.factory_id) {
          query = query.eq('factory_id', state.filter.factory_id)
        }
        if (state.filter.origin_port_id) {
          query = query.eq('origin_port_id', state.filter.origin_port_id)
        }
        if (state.filter.destination_port_id) {
          query = query.eq(
            'destination_port_id',
            state.filter.destination_port_id
          )
        }
        if (state.filter.etd_date_from) {
          query = query.gte('etd_date', state.filter.etd_date_from)
        }
        if (state.filter.etd_date_to) {
          query = query.lte('etd_date', state.filter.etd_date_to)
        }
      }

      // Apply search
      if (state.searchTerm) {
        query = query.or(
          `shipment_number.ilike.%${state.searchTerm}%,booking_number.ilike.%${state.searchTerm}%,invoice_number.ilike.%${state.searchTerm}%`
        )
      }

      // Apply sorting
      query = query.order(state.sortBy, {
        ascending: state.sortOrder === 'asc',
      })

      // Apply pagination
      const from = (state.currentPage - 1) * state.pageSize
      const to = from + state.pageSize - 1
      query = query.range(from, to)

      const { data, error, count } = await query

      if (error) throw error

      setState(prev => ({
        ...prev,
        shipments: data || [],
        totalCount: count || 0,
        loading: false,
      }))
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message,
        loading: false,
      }))
    }
  }, [
    supabase,
    state.filter,
    state.searchTerm,
    state.currentPage,
    state.pageSize,
    state.sortBy,
    state.sortOrder,
  ])

  // Fetch single shipment by ID
  const fetchShipmentById = useCallback(
    async (id: string): Promise<ShipmentWithRelations | null> => {
      try {
        const { data, error } = await supabase
          .from('shipments')
          .select(
            `
          *,
          customer:companies!customer_id(id, name, company_type),
          shipper:companies!shipper_id(id, name),
          consignee:companies!consignee_id(id, name),
          notify_party:companies!notify_party_id(id, name),
          factory:companies!factory_id(id, name, contact_phone),
          forwarder_agent:companies!forwarder_agent_id(id, name, contact_phone),
          origin_port:ports!origin_port_id(id, port_name, code, country),
          destination_port:ports!destination_port_id(id, port_name, code, country)
        `
          )
          .eq('id', id)
          .single()

        if (error) throw error

        setState(prev => ({ ...prev, selectedShipment: data }))
        return data
      } catch (error: any) {
        console.error('Error fetching shipment:', error)
        return null
      }
    },
    [supabase]
  )

  // Setters for state
  const setFilter = useCallback((filter: ShipmentFilter | null) => {
    setState(prev => ({ ...prev, filter, currentPage: 1 }))
  }, [])

  const setSearchTerm = useCallback((searchTerm: string) => {
    setState(prev => ({ ...prev, searchTerm, currentPage: 1 }))
  }, [])

  const setSorting = useCallback(
    (sortBy: string, sortOrder: 'asc' | 'desc') => {
      setState(prev => ({ ...prev, sortBy, sortOrder, currentPage: 1 }))
    },
    []
  )

  const setPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }))
  }, [])

  // Subscribe to real-time updates
  useEffect(() => {
    const channel = supabase
      .channel('shipments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'shipments' },
        () => {
          fetchShipments()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [supabase, fetchShipments])

  // Load initial data
  useEffect(() => {
    fetchShipments()
  }, [fetchShipments])

  return {
    ...state,
    fetchShipments,
    fetchShipmentById,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
  }
}

// Hook for shipment CRUD operations
export function useShipmentCRUD() {
  const supabase = createClient()
  const [creating, setCreating] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [deleting, setDeleting] = useState(false)

  // Create new shipment with transaction-like rollback functionality
  const createShipment = useCallback(
    async (
      shipmentData: ShipmentCreation,
      documents?: Record<string, File>
    ): Promise<Shipment> => {
      setCreating(true)

      // Track created resources for rollback
      let createdShipmentId: string | null = null
      let createdContainerId: string | null = null
      let createdProductId: string | null = null
      let uploadedDocuments: string[] = []

      const rollbackOperations = async () => {
        console.log('Rolling back operations...')
        
        try {
          // Delete shipment_products
          if (createdProductId) {
            await supabase
              .from('shipment_products')
              .delete()
              .eq('id', createdProductId)
            console.log('Rolled back shipment_products')
          }

          // Delete containers
          if (createdContainerId) {
            await supabase
              .from('containers')
              .delete()
              .eq('id', createdContainerId)
            console.log('Rolled back containers')
          }

          // Delete uploaded documents
          if (uploadedDocuments.length > 0) {
            await supabase.storage
              .from('shipment-documents')
              .remove(uploadedDocuments)
            
            await supabase
              .from('shipment_documents')
              .delete()
              .in('storage_path', uploadedDocuments)
            console.log('Rolled back documents')
          }

          // Delete shipment_status_history
          if (createdShipmentId) {
            await supabase
              .from('status_history')
              .delete()
              .eq('shipment_id', createdShipmentId)
            console.log('Rolled back status history')
          }

          // Finally delete the shipment (CASCADE will handle related records)
          if (createdShipmentId) {
            await supabase
              .from('shipments')
              .delete()
              .eq('id', createdShipmentId)
            console.log('Rolled back shipment')
          }
        } catch (rollbackError) {
          console.error('Error during rollback (some data may remain):', rollbackError)
        }
      }

      try {
        // Step 1: Validate prerequisites
        const { data: originPort, error: portError } = await supabase
          .from('ports')
          .select('code')
          .eq('id', shipmentData.origin_port_id)
          .single()

        if (portError || !originPort) {
          throw new Error('Origin port not found or invalid. Please check the selected origin port.')
        }

        // Step 2: Generate unique shipment number
        let shipmentNumber
        try {
          shipmentNumber = await generateUniqueShipmentNumber({
            transportMode: shipmentData.transportation_mode,
            portCode: originPort.code,
            date: new Date(shipmentData.etd_date),
          })
        } catch (numberError) {
          throw new Error('Failed to generate shipment number. Please try again.')
        }

        // Step 3: Prepare and validate shipment data
        const insertData: ShipmentInsert = {
          ...shipmentData,
          shipment_number: shipmentNumber.fullNumber,
          invoice_number: shipmentNumber.fullNumber, // Set invoice_number = shipment_number
          status: 'booking_confirmed',
          created_by: (await supabase.auth.getUser()).data.user?.id || null,
        }

        // Step 4: Create shipment (main record)
        const { data: shipment, error: shipmentError } = await supabase
          .from('shipments')
          .insert(insertData)
          .select()
          .single()

        if (shipmentError) {
          throw new Error(`Failed to create shipment: ${shipmentError.message}`)
        }

        createdShipmentId = shipment.id
        console.log('✓ Created shipment:', shipment.shipment_number)

        // // Step 5: Create initial status tracking record
        // const { error: statusError } = await supabase
        //   .from('shipment_status_history')
        //   .insert({
        //     shipment_id: shipment.id,
        //     status: 'booking_confirmed',
        //     changed_by: insertData.created_by,
        //     changed_at: new Date().toISOString(),
        //     notes: 'Shipment created',
        //   })

        // if (statusError) {
        //   throw new Error(`Failed to create status history: ${statusError.message}`)
        // }

        //console.log('✓ Created status history')

        // Step 6: Upload documents if provided
        if (documents && Object.keys(documents).length > 0) {
          try {
            const uploadedDocs = await uploadShipmentDocuments(shipment.id, documents)
            uploadedDocuments = uploadedDocs.map(doc => doc.storage_path)
            console.log('✓ Uploaded documents:', uploadedDocuments.length)
          } catch (docError: any) {
            throw new Error(`Failed to upload documents: ${docError.message}`)
          }
        }

        // Step 7: Create container and product (if product selected)
        const selectedProductId = shipmentData.metadata?.selectedProductId;
        
        if (selectedProductId) {
          console.log('Creating container and product for shipment:', shipment.id)

          // Get selected product information with customer relationship data
          const { data: customerProduct, error: productError } = await supabase
            .from('customer_products')
            .select(`
              id,
              product_id,
              unit_price_cif,
              unit_price_fob,
              currency_code,
              packaging_type,
              quality_grade,
              standard_quantity,
              gross_weight_per_package,
              net_weight_per_package,
              products (
                id,
                name,
                code,
                description,
                unit_of_measure_id
              )
            `)
            .eq('customer_id', shipmentData.customer_id)
            .eq('product_id', selectedProductId)
            .eq('is_active', true)
            .single();

          if (productError || !customerProduct) {
            throw new Error(`Failed to fetch selected product information: ${productError?.message || 'Product not found'}`)
          }

          console.log('✓ Found customer product:', customerProduct.products?.name)
          console.log('📋 Customer product data:', {
            id: customerProduct.id,
            product_id: customerProduct.product_id,
            standard_quantity: customerProduct.standard_quantity,
            gross_weight_per_package: customerProduct.gross_weight_per_package,
            net_weight_per_package: customerProduct.net_weight_per_package,
            unit_price_cif: customerProduct.unit_price_cif,
            unit_price_fob: customerProduct.unit_price_fob,
            packaging_type: customerProduct.packaging_type,
            quality_grade: customerProduct.quality_grade,
          })

          // Create a single dummy container
          const dummyContainer = {
            shipment_id: shipment.id,
            container_number: `CONT-${shipment.shipment_number}-001`,
            container_type: 'dry' as const,
            container_size: '40ft' as const,
            seal_number: null,
            tare_weight: 3800, // Standard 40ft container tare weight
            gross_weight: 0, // Will be updated later
            volume: 67.7, // Standard 40ft container volume in CBM
            temperature: null,
            vent: null,
            status: 'empty' as const,
          };

          // Insert the dummy container
          const { data: createdContainer, error: containerError } = await supabase
            .from('containers')
            .insert(dummyContainer)
            .select()
            .single();

          if (containerError) {
            throw new Error(`Failed to create container: ${containerError.message}`)
          }

          createdContainerId = createdContainer.id
          console.log('✓ Created container:', createdContainer.container_number)

          // Create shipment product entry using customer_products values
          const quantity = customerProduct.standard_quantity || 1; // Use standard_quantity from customer_products
          const grossWeight = customerProduct.gross_weight_per_package || 100;
          const netWeight = customerProduct.net_weight_per_package || 95;
          
          console.log('📦 Using customer_products values:', {
            quantity: `${quantity} (from standard_quantity)`,
            grossWeight: `${grossWeight} kg (from gross_weight_per_package)`,
            netWeight: `${netWeight} kg (from net_weight_per_package)`,
            packaging: customerProduct.packaging_type,
            quality: customerProduct.quality_grade
          });
          
          const shipmentProduct = {
            shipment_id: shipment.id,
            container_id: createdContainer.id,
            product_id: selectedProductId,
            product_description: customerProduct.products?.name || 'Selected Product',
            quantity: quantity, // Use customer_products.standard_quantity
            unit_of_measure_id: customerProduct.products?.unit_of_measure_id || null, // Use actual product UOM
            unit_price_cif: customerProduct.unit_price_cif,
            unit_price_fob: customerProduct.unit_price_fob,
            total_value_cif: customerProduct.unit_price_cif * quantity * netWeight, // quantity * unit_price_cif * netWeight
            total_value_fob: customerProduct.unit_price_fob * quantity * netWeight, // quantity * unit_price_fob * netWeight
            gross_weight: grossWeight, // Use customer_products.gross_weight_per_package
            net_weight: netWeight, // Use customer_products.net_weight_per_package
            total_gross_weight: quantity * grossWeight, // quantity * gross_weight
            total_net_weight: quantity * netWeight, // quantity * net_weight
            shipping_mark: 'N/M',
            mfg_date: new Date().toISOString().split('T')[0], // Today's date as default
            expire_date: null, // Will be set later if needed
            lot_number: `LOT-${Date.now()}`, // Simple lot number
            packaging_type: customerProduct.packaging_type as 'Bag' | 'Plastic Basket' | 'Carton',
            quality_grade: customerProduct.quality_grade || 'Standard',
          };

          // Insert the shipment product
          const { data: createdProduct, error: productInsertError } = await supabase
            .from('shipment_products')
            .insert(shipmentProduct)
            .select()
            .single();

          if (productInsertError) {
            throw new Error(`Failed to create shipment product: ${productInsertError.message}`)
          }

          createdProductId = createdProduct.id
          console.log('✓ Created shipment product for:', customerProduct.products?.name)

          // Update container with basic weight (just the product weight)
          const { error: containerUpdateError } = await supabase
            .from('containers')
            .update({
              gross_weight: dummyContainer.tare_weight + shipmentProduct.total_gross_weight, // Tare + cargo
              status: 'loaded' as const,
            })
            .eq('id', createdContainer.id);

          if (containerUpdateError) {
            throw new Error(`Failed to update container weight: ${containerUpdateError.message}`)
          }

          // Update shipment totals by calculating from all products
          await updateShipmentTotals(supabase, shipment.id)
          console.log('✓ Updated shipment totals')
        } else {
          console.log('No product selected - skipping container and product creation')
        }

        console.log('✅ Shipment created successfully:', shipment.shipment_number)
        return shipment
        
      } catch (error: any) {
        console.error('❌ Error creating shipment:', error)
        
        // Perform rollback operations
        await rollbackOperations()
        
        // Create user-friendly error message
        let userMessage = 'Failed to create shipment. Please try again.'
        
        if (error.message.includes('Origin port not found')) {
          userMessage = 'Invalid origin port selected. Please check your port selection.'
        } else if (error.message.includes('shipment number')) {
          userMessage = 'Unable to generate shipment number. Please try again.'
        } else if (error.message.includes('Failed to create shipment:')) {
          userMessage = 'Failed to save shipment data. Please check your form data and try again.'
        } else if (error.message.includes('status history')) {
          userMessage = 'Shipment created but failed to initialize status tracking.'
        } else if (error.message.includes('upload documents')) {
          userMessage = 'Shipment created but failed to upload documents. You can add documents later.'
        } else if (error.message.includes('product information')) {
          userMessage = 'Invalid product selection. Please select a valid product for this customer.'
        } else if (error.message.includes('container')) {
          userMessage = 'Failed to create container. Please try again.'
        } else if (error.message.includes('shipment product')) {
          userMessage = 'Failed to assign product to shipment. Please try again.'
        } else if (error.message.includes('database')) {
          userMessage = 'Database connection error. Please check your internet connection and try again.'
        }
        
        // Throw error with user-friendly message
        const enhancedError = new Error(userMessage)
        enhancedError.cause = error
        throw enhancedError
        
      } finally {
        setCreating(false)
      }
    },
    [supabase]
  )

  // Update shipment
  const updateShipment = useCallback(
    async (id: string, updates: Partial<ShipmentUpdate>): Promise<Shipment> => {
      setUpdating(true)

      try {
        const { data, error } = await supabase
          .from('shipments')
          .update({
            ...updates,
            updated_at: new Date().toISOString(),
          })
          .eq('id', id)
          .select()
          .single()

        if (error) throw error

        // Track status changes
        if (updates.status) {
          await supabase.from('status_history').insert({
            shipment_id: id,
            status: updates.status,
            changed_by: (await supabase.auth.getUser()).data.user?.id || null,
            changed_at: new Date().toISOString(),
            notes: `Status changed to ${updates.status}`,
          })
        }

        return data
      } catch (error: any) {
        console.error('Error updating shipment:', error)
        throw error
      } finally {
        setUpdating(false)
      }
    },
    [supabase]
  )

  // Delete shipment
  const deleteShipment = useCallback(
    async (id: string): Promise<void> => {
      setDeleting(true)

      try {
        const { error } = await supabase.from('shipments').delete().eq('id', id)

        if (error) throw error
      } catch (error: any) {
        console.error('Error deleting shipment:', error)
        throw error
      } finally {
        setDeleting(false)
      }
    },
    [supabase]
  )

  return {
    createShipment,
    updateShipment,
    deleteShipment,
    creating,
    updating,
    deleting,
    isLoading: creating || updating || deleting,
  }
}

// Hook for document management
export function useShipmentDocuments(shipmentId?: string) {
  const [documents, setDocuments] = useState<ShipmentDocument[]>([])
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {}
  )

  const supabase = createClient()

  // Fetch documents for a shipment
  const fetchDocuments = useCallback(async () => {
    if (!shipmentId) return

    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('shipment_documents')
        .select('*')
        .eq('shipment_id', shipmentId)
        .order('uploaded_at', { ascending: false })

      if (error) throw error
      setDocuments(data || [])
    } catch (error: any) {
      console.error('Error fetching documents:', error)
    } finally {
      setLoading(false)
    }
  }, [supabase, shipmentId])

  // Upload document to Supabase Storage
  const uploadDocument = useCallback(
    async (
      file: File,
      documentType: string = 'other'
    ): Promise<ShipmentDocument> => {
      if (!shipmentId) throw new Error('Shipment ID is required')

      setUploading(true)

      try {
        // Generate unique file path
        const fileExtension = file.name.split('.').pop()
        const fileName = `${shipmentId}/${documentType}_${Date.now()}.${fileExtension}`

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('shipment-documents')
          .upload(fileName, file, {
            cacheControl: '3600',
            upsert: false,
          })

        if (uploadError) throw uploadError

        // Create document record
        const documentData: Omit<ShipmentDocument, 'id' | 'uploaded_at'> = {
          shipment_id: shipmentId,
          file_name: file.name,
          file_size: file.size,
          file_type: file.type,
          storage_path: uploadData.path,
          document_type: documentType,
          uploaded_by:
            (await supabase.auth.getUser()).data.user?.id || 'unknown',
        }

        const { data: document, error: dbError } = await supabase
          .from('shipment_documents')
          .insert(documentData)
          .select()
          .single()

        if (dbError) throw dbError

        // Refresh documents list
        await fetchDocuments()

        return document
      } catch (error: any) {
        console.error('Error uploading document:', error)
        throw error
      } finally {
        setUploading(false)
      }
    },
    [supabase, shipmentId, fetchDocuments]
  )

  // Delete document
  const deleteDocument = useCallback(
    async (documentId: string): Promise<void> => {
      try {
        // Get document info
        const { data: document, error: fetchError } = await supabase
          .from('shipment_documents')
          .select('storage_path')
          .eq('id', documentId)
          .single()

        if (fetchError) throw fetchError

        // Delete from storage
        const { error: storageError } = await supabase.storage
          .from('shipment-documents')
          .remove([document.storage_path])

        if (storageError) throw storageError

        // Delete from database
        const { error: dbError } = await supabase
          .from('shipment_documents')
          .delete()
          .eq('id', documentId)

        if (dbError) throw dbError

        // Refresh documents list
        await fetchDocuments()
      } catch (error: any) {
        console.error('Error deleting document:', error)
        throw error
      }
    },
    [supabase, fetchDocuments]
  )

  // Get download URL for document
  const getDocumentUrl = useCallback(
    async (storagePath: string): Promise<string> => {
      const { data } = await supabase.storage
        .from('shipment-documents')
        .createSignedUrl(storagePath, 3600) // 1 hour expiry

      return data?.signedUrl || ''
    },
    [supabase]
  )

  useEffect(() => {
    fetchDocuments()
  }, [fetchDocuments])

  return {
    documents,
    loading,
    uploading,
    uploadProgress,
    fetchDocuments,
    uploadDocument,
    deleteDocument,
    getDocumentUrl,
  }
}

// Utility function for uploading multiple documents
export async function uploadShipmentDocuments(
  shipmentId: string,
  documents: Record<string, File>
): Promise<ShipmentDocument[]> {
  const supabase = createClient()
  const uploadedDocuments: ShipmentDocument[] = []

  for (const [type, file] of Object.entries(documents)) {
    try {
      // Generate unique file path
      const fileExtension = file.name.split('.').pop()
      const fileName = `${shipmentId}/${type}_${Date.now()}.${fileExtension}`

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('shipment-documents')
        .upload(fileName, file)

      if (uploadError) throw uploadError

      // Create document record
      const documentData = {
        shipment_id: shipmentId,
        file_name: file.name,
        file_size: file.size,
        file_type: file.type,
        storage_path: uploadData.path,
        document_type: type,
        uploaded_by: (await supabase.auth.getUser()).data.user?.id || 'unknown',
      }

      const { data: document, error: dbError } = await supabase
        .from('shipment_documents')
        .insert(documentData)
        .select()
        .single()

      if (dbError) throw dbError

      uploadedDocuments.push(document)
    } catch (error) {
      console.error(`Error uploading document ${type}:`, error)
    }
  }

  return uploadedDocuments
}

// Enhanced hook that combines all shipment functionality
export function useShipmentsManagement() {
  const data = useShipmentsData()
  const crud = useShipmentCRUD()

  return {
    ...data,
    ...crud,

    // Convenience methods
    refreshShipments: data.fetchShipments,
    createShipmentWithDocuments: crud.createShipment,
  }
}

// Hook specifically for the creation form integration
export function useShipmentCreationIntegration() {
  const { createShipment, creating } = useShipmentCRUD()
  const store = useShipmentCreationStore()

  const submitShipment = useCallback(async () => {
    const formData = store.exportFormData()
    const uploadedFiles = store.documents.uploadedFiles

    return await createShipment(formData, uploadedFiles)
  }, [createShipment, store])

  return {
    submitShipment,
    creating,
    store,
  }
}
