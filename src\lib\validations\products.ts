import { z } from 'zod'

// Unit category enum
export const unitCategorySchema = z.enum([
  'weight',
  'count',
  'volume',
  'length',
])

// Unit of measure validation schema
export const unitOfMeasureSchema = z.object({
  code: z
    .string()
    .min(1, 'Unit code is required')
    .max(20, 'Unit code must be 20 characters or less')
    .regex(
      /^[A-Z0-9_-]+$/,
      'Unit code must contain only uppercase letters, numbers, hyphens, and underscores'
    ),
  name: z
    .string()
    .min(2, 'Unit name must be at least 2 characters')
    .max(100, 'Unit name must be 100 characters or less'),
  symbol: z
    .string()
    .min(1, 'Unit symbol is required')
    .max(10, 'Unit symbol must be 10 characters or less'),
  category: unitCategorySchema,
  conversion_factor: z
    .number()
    .positive('Conversion factor must be positive')
    .default(1.0),
  base_unit_id: z.string().uuid('Invalid base unit ID').optional().nullable(),
  is_active: z.boolean().default(true),
})

// Product validation schema
export const productSchema = z.object({
  name: z
    .string()
    .min(2, 'Product name must be at least 2 characters')
    .max(100, 'Product name must be 100 characters or less'),
  code: z
    .string()
    .max(50, 'Product code must be 50 characters or less')
    .regex(
      /^[A-Za-z0-9_-]*$/,
      'Product code must contain only letters, numbers, hyphens, and underscores'
    )
    .optional()
    .nullable(),
  description: z
    .string()
    .max(500, 'Description must be 500 characters or less')
    .optional()
    .nullable(),
  category: z
    .string()
    .max(100, 'Category must be 100 characters or less')
    .optional()
    .nullable(),
  hs_code: z
    .string()
    .max(20, 'HS code must be 20 characters or less')
    .regex(/^[0-9.]*$/, 'HS code must contain only numbers and dots')
    .optional()
    .nullable(),
  unit_of_measure_id: z
    .string()
    .uuid('Invalid unit of measure ID')
    .min(1, 'Unit of measure is required'),
  is_active: z.boolean().default(true),
})

// Form schemas (without database-generated fields)
export const unitOfMeasureFormSchema = unitOfMeasureSchema.omit({
  is_active: true,
})

export const productFormSchema = productSchema.omit({
  is_active: true,
})

// Update schemas (all fields optional except id)
export const unitOfMeasureUpdateSchema = unitOfMeasureSchema.partial().extend({
  id: z.string().uuid('Invalid unit ID'),
})

export const productUpdateSchema = productSchema.partial().extend({
  id: z.string().uuid('Invalid product ID'),
})

// Bulk operations schemas
export const bulkDeleteSchema = z.object({
  ids: z
    .array(z.string().uuid('Invalid ID'))
    .min(1, 'At least one item must be selected'),
})

// Search and filter schemas
export const unitFilterSchema = z.object({
  category: unitCategorySchema.optional(),
  is_active: z.boolean().optional(),
  search: z.string().optional(),
})

export const productFilterSchema = z.object({
  category: z.string().optional(),
  unit_of_measure_id: z.string().uuid().optional(),
  is_active: z.boolean().optional(),
  search: z.string().optional(),
})

// Type exports
export type UnitOfMeasureForm = z.infer<typeof unitOfMeasureFormSchema>
export type ProductForm = z.infer<typeof productFormSchema>
export type UnitOfMeasureUpdate = z.infer<typeof unitOfMeasureUpdateSchema>
export type ProductUpdate = z.infer<typeof productUpdateSchema>
export type UnitFilter = z.infer<typeof unitFilterSchema>
export type ProductFilter = z.infer<typeof productFilterSchema>
export type UnitCategory = z.infer<typeof unitCategorySchema>
