'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Ship,
  Package,
  Calendar,
  MapPin,
  FileText,
  AlertCircle,
  CheckCircle,
  Loader2,
  Star,
  Info,
  Bell,
  Mail,
  Smartphone,
  MessageSquare,
} from 'lucide-react'
import { ConsigneeNotifyPartyIntegration } from './consignee-notify-party-integration'
import { useShipmentFormIntegration } from '@/hooks/use-shipment-consignee-integration'

// Example shipment form schema
const shipmentFormSchema = z.object({
  // Basic shipment info
  shipmentNumber: z.string().min(1, 'Shipment number is required'),
  description: z.string().min(1, 'Description is required'),

  // Consignee and notify party (integrated fields)
  consigneeId: z.string().min(1, 'Consignee is required'),
  notifyPartyId: z.string().min(1, 'Notify party is required'),

  // Auto-populated from relationship
  notificationPreferences: z
    .object({
      email: z.boolean(),
      sms: z.boolean(),
      line: z.boolean(),
      wechat: z.boolean(),
    })
    .optional(),
  specialInstructions: z.string().optional(),
  relationshipNotes: z.string().optional(),

  // Additional shipment fields
  departureDate: z.string().min(1, 'Departure date is required'),
  arrivalDate: z.string().optional(),
  portOfLoading: z.string().min(1, 'Port of loading is required'),
  portOfDischarge: z.string().min(1, 'Port of discharge is required'),

  // Cargo details
  cargoDescription: z.string().min(1, 'Cargo description is required'),
  cargoWeight: z.number().min(0.1, 'Cargo weight must be greater than 0'),
  cargoValue: z.number().min(0, 'Cargo value must be non-negative').optional(),

  // Additional notes
  remarks: z.string().optional(),
})

type ShipmentFormData = z.infer<typeof shipmentFormSchema>

interface ShipmentFormExampleProps {
  onSubmit: (data: ShipmentFormData & { shipmentData?: any }) => Promise<void>
  onCancel?: () => void
  initialData?: Partial<ShipmentFormData>
  isLoading?: boolean
}

export function ShipmentFormExample({
  onSubmit,
  onCancel,
  initialData,
  isLoading = false,
}: ShipmentFormExampleProps) {
  const [error, setError] = useState<string | null>(null)

  // Integration hook for intelligent consignee-notify party management
  const { state, actions, config } = useShipmentFormIntegration()

  const form = useForm<ShipmentFormData>({
    resolver: zodResolver(shipmentFormSchema),
    defaultValues: {
      shipmentNumber: initialData?.shipmentNumber || '',
      description: initialData?.description || '',
      consigneeId: initialData?.consigneeId || '',
      notifyPartyId: initialData?.notifyPartyId || '',
      notificationPreferences: initialData?.notificationPreferences || {
        email: false,
        sms: false,
        line: false,
        wechat: false,
      },
      specialInstructions: initialData?.specialInstructions || '',
      relationshipNotes: initialData?.relationshipNotes || '',
      departureDate: initialData?.departureDate || '',
      arrivalDate: initialData?.arrivalDate || '',
      portOfLoading: initialData?.portOfLoading || '',
      portOfDischarge: initialData?.portOfDischarge || '',
      cargoDescription: initialData?.cargoDescription || '',
      cargoWeight: initialData?.cargoWeight || 0,
      cargoValue: initialData?.cargoValue || 0,
      remarks: initialData?.remarks || '',
    },
  })

  // Handle consignee selection with intelligent pre-population
  const handleConsigneeChange = (consigneeId: string, consigneeData?: any) => {
    actions.setConsignee(consigneeId)
    form.setValue('consigneeId', consigneeId)

    // Clear notify party when consignee changes
    form.setValue('notifyPartyId', '')
    form.setValue('notificationPreferences', {
      email: false,
      sms: false,
      line: false,
      wechat: false,
    })
    form.setValue('specialInstructions', '')
    form.setValue('relationshipNotes', '')
  }

  // Handle notify party selection with automatic field population
  const handleNotifyPartyChange = (
    notifyPartyId: string,
    relationship?: any
  ) => {
    actions.setNotifyParty(notifyPartyId)
    form.setValue('notifyPartyId', notifyPartyId)

    if (relationship && config.prePopulateRelatedFields) {
      // Auto-populate notification preferences
      if (relationship.notification_preferences) {
        form.setValue(
          'notificationPreferences',
          relationship.notification_preferences
        )
      }

      // Auto-populate special instructions
      if (relationship.special_instructions) {
        form.setValue('specialInstructions', relationship.special_instructions)
      }

      // Auto-populate relationship notes
      if (relationship.notes) {
        form.setValue('relationshipNotes', relationship.notes)
      }
    }
  }

  // Handle notification preferences change
  const handleNotificationPreferencesChange = (preferences: any) => {
    form.setValue('notificationPreferences', preferences)
  }

  // Handle special instructions change
  const handleSpecialInstructionsChange = (instructions: string) => {
    form.setValue('specialInstructions', instructions)
  }

  // Form submission with integrated data
  const handleSubmit = async (data: ShipmentFormData) => {
    try {
      setError(null)

      // Validate consignee-notify party relationship
      const isValid = await actions.validateSelection()
      if (!isValid) {
        const errors = actions.getValidationErrors()
        setError(errors.join('; '))
        return
      }

      // Export integrated shipment data
      const shipmentData = actions.exportShipmentData()

      // Submit with integrated data
      await onSubmit({
        ...data,
        shipmentData,
      })
    } catch (error) {
      console.error('Form submission error:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to create shipment'
      )
    }
  }

  // Get notification icons for display
  const getNotificationIcons = (preferences: any) => {
    if (!preferences) return []
    const icons = []
    if (preferences.email)
      icons.push(<Mail key="email" className="h-3 w-3 text-blue-400" />)
    if (preferences.sms)
      icons.push(<Smartphone key="sms" className="h-3 w-3 text-green-400" />)
    if (preferences.line)
      icons.push(
        <MessageSquare key="line" className="h-3 w-3 text-green-500" />
      )
    if (preferences.wechat)
      icons.push(
        <MessageSquare key="wechat" className="h-3 w-3 text-green-600" />
      )
    return icons
  }

  const watchedNotificationPreferences = form.watch('notificationPreferences')

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Integration Status Display */}
        {state.selectedConsigneeId && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span>
                  Intelligent pre-population:
                  {state.hasRelationships ? (
                    <span className="text-green-400 ml-1">
                      {state.relationshipCount} relationship
                      {state.relationshipCount !== 1 ? 's' : ''} available
                      {state.defaultNotifyParty && ' (default configured)'}
                    </span>
                  ) : (
                    <span className="text-amber-400 ml-1">
                      No relationships configured
                    </span>
                  )}
                </span>
              </div>
              {state.selectedRelationship?.is_default && (
                <Badge className="bg-yellow-600 text-white">
                  <Star className="h-3 w-3 mr-1" />
                  Default Used
                </Badge>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Basic Shipment Information */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Ship className="h-5 w-5 text-blue-500" />
              Shipment Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="shipmentNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Shipment Number *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="SH-2024-001"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Description *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Container shipment"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Intelligent Consignee-Notify Party Integration */}
        <ConsigneeNotifyPartyIntegration
          selectedConsigneeId={state.selectedConsigneeId || undefined}
          selectedNotifyPartyId={state.selectedNotifyPartyId || undefined}
          onConsigneeChange={handleConsigneeChange}
          onNotifyPartyChange={handleNotifyPartyChange}
          onNotificationPreferencesChange={handleNotificationPreferencesChange}
          onSpecialInstructionsChange={handleSpecialInstructionsChange}
          autoSelectDefault={config.autoSelectDefault}
          showRelationshipDetails={true}
          disabled={isLoading}
          showPriorityOrder={true}
        />

        {/* Auto-populated Notification Settings */}
        {state.selectedRelationship && (
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-yellow-200 flex items-center gap-2">
                <Bell className="h-5 w-5 text-yellow-500" />
                Notification Configuration
                <Badge className="bg-green-600 text-white text-xs">
                  Auto-populated
                </Badge>
              </CardTitle>
              <CardDescription className="text-slate-400">
                Automatically configured based on consignee-notify party
                relationship
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Display current notification preferences */}
              <div>
                <label className="text-slate-200 text-sm font-medium mb-2 block">
                  Active Notification Channels
                </label>
                <div className="flex items-center space-x-4 p-3 bg-slate-800 rounded border border-slate-600">
                  <div className="flex items-center space-x-1">
                    {getNotificationIcons(watchedNotificationPreferences)}
                  </div>
                  <div className="text-sm text-slate-300">
                    {Object.entries(watchedNotificationPreferences || {})
                      .filter(([_, enabled]) => enabled)
                      .map(([channel]) => channel.toUpperCase())
                      .join(', ') || 'None configured'}
                  </div>
                </div>
              </div>

              {/* Manual notification preferences override */}
              <div className="space-y-2">
                <label className="text-slate-200 text-sm font-medium">
                  Override Notification Preferences (optional)
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {(['email', 'sms', 'line', 'wechat'] as const).map(
                    channel => (
                      <FormField
                        key={channel}
                        control={form.control}
                        name={`notificationPreferences.${channel}`}
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-slate-600 p-3 bg-slate-800">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={isLoading}
                                className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="text-slate-200 capitalize">
                                {channel}
                              </FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                    )
                  )}
                </div>
              </div>

              {/* Special instructions display */}
              <FormField
                control={form.control}
                name="specialInstructions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Special Instructions
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Automatically populated from relationship..."
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[80px]"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Auto-populated from consignee-notify party relationship,
                      can be modified
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        )}

        {/* Route and Schedule Information */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-green-200 flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-500" />
              Route & Schedule
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="portOfLoading"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Port of Loading *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Port name"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="portOfDischarge"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Port of Discharge *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Port name"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="departureDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Departure Date *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="date"
                        className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="arrivalDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Expected Arrival Date
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="date"
                        className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Cargo Information */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-purple-200 flex items-center gap-2">
              <Package className="h-5 w-5 text-purple-500" />
              Cargo Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="cargoDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200">
                    Cargo Description *
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Detailed description of cargo..."
                      className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[80px]"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="cargoWeight"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Cargo Weight (KG) *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="1000.00"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : 0
                          )
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cargoValue"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Cargo Value (USD)
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="10000.00"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : 0
                          )
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Additional Remarks */}
        <Card className="bg-slate-700 border-slate-600">
          <CardContent className="pt-6">
            <FormField
              control={form.control}
              name="remarks"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200">
                    Additional Remarks
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Any additional information..."
                      className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[100px]"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Create Shipment
          </Button>
        </div>
      </form>
    </Form>
  )
}
