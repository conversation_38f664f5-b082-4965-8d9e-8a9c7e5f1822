import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { isStaff } from '@/lib/supabase/auth'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

// Create Supabase clients
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
)

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const containerId = params.id
    
    if (!containerId) {
      console.error('Container override API: Missing container ID')
      return NextResponse.json({ 
        error: 'Bad Request', 
        details: 'Container ID is required' 
      }, { status: 400 })
    }

    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization?.startsWith('Bearer ')) {
      console.error('Container override API: Missing or invalid authorization header')
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Missing or invalid authorization header' 
      }, { status: 401 })
    }

    const token = authorization.split(' ')[1]
    if (!token) {
      console.error('Container override API: Empty authorization token')
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Empty authorization token' 
      }, { status: 401 })
    }

    // Verify user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.error('Container override API: Auth error:', authError)
      return NextResponse.json({ 
        error: 'Invalid token', 
        details: authError?.message || 'No user found for provided token' 
      }, { status: 401 })
    }

    // Get user profile and check role
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role, first_name, last_name')
      .eq('user_id', user.id)
      .single()

    if (profileError) {
      console.error('Container override API: Profile error:', profileError)
      return NextResponse.json({ 
        error: 'Profile not found', 
        details: profileError.message 
      }, { status: 404 })
    }

    // Check if user has permission to override (Admin or CS only)
    if (!isStaff(profile.role)) {
      console.error('Container override API: Insufficient permissions for role:', profile.role)
      return NextResponse.json({ 
        error: 'Forbidden', 
        details: 'Only Admin and Customer Service can update confirmed container numbers' 
      }, { status: 403 })
    }

    // Parse request body
    const body = await request.json()
    const { container_number, seal_number, override_reason } = body

    if (!override_reason || typeof override_reason !== 'string' || override_reason.trim().length === 0) {
      return NextResponse.json({ 
        error: 'Bad Request', 
        details: 'Override reason is required for updating confirmed numbers' 
      }, { status: 400 })
    }

    if (!container_number && !seal_number) {
      return NextResponse.json({ 
        error: 'Bad Request', 
        details: 'At least one number (container_number or seal_number) must be provided' 
      }, { status: 400 })
    }

    // Get current container data
    const { data: currentContainer, error: containerError } = await supabaseAdmin
      .from('containers')
      .select('*')
      .eq('id', containerId)
      .single()

    if (containerError) {
      console.error('Container override API: Container fetch error:', containerError)
      return NextResponse.json({ 
        error: 'Container not found', 
        details: containerError.message 
      }, { status: 404 })
    }

    // Check if trying to update confirmed numbers
    const isContainerNumberOverride = container_number && 
      currentContainer.container_number_confirmed && 
      currentContainer.container_number !== container_number

    const isSealNumberOverride = seal_number && 
      currentContainer.seal_number_confirmed && 
      currentContainer.seal_number !== seal_number

    // Build update object
    const updates: any = {}
    
    if (container_number !== undefined) {
      updates.container_number = container_number
    }
    
    if (seal_number !== undefined) {
      updates.seal_number = seal_number
    }

    // Update container
    const { data: updatedContainer, error: updateError } = await supabaseAdmin
      .from('containers')
      .update(updates)
      .eq('id', containerId)
      .select()
      .single()

    if (updateError) {
      console.error('Container override API: Update error:', updateError)
      return NextResponse.json({ 
        error: 'Update failed', 
        details: updateError.message 
      }, { status: 500 })
    }

    // Log override actions if confirmed numbers were changed
    const overrideTime = new Date().toISOString()
    const historyEntries = []

    if (isContainerNumberOverride) {
      historyEntries.push({
        shipment_id: currentContainer.shipment_id,
        status_from: null,
        status_to: null,
        notes: `Container number override: ${currentContainer.container_number} → ${container_number}. Reason: ${override_reason}`,
        updated_by: user.id,
        action_type: 'confirmation_override',
        created_at: overrideTime
      })
    }

    if (isSealNumberOverride) {
      historyEntries.push({
        shipment_id: currentContainer.shipment_id,
        status_from: null,
        status_to: null,
        notes: `Seal number override: ${currentContainer.seal_number} → ${seal_number}. Reason: ${override_reason}`,
        updated_by: user.id,
        action_type: 'confirmation_override',
        created_at: overrideTime
      })
    }

    // Insert audit trail entries
    if (historyEntries.length > 0) {
      const { error: historyError } = await supabaseAdmin
        .from('status_history')
        .insert(historyEntries)

      if (historyError) {
        console.error('Container override API: History logging error:', historyError)
        // Don't fail the request for audit logging issues, just log the error
      }
    }

    console.log(`Container override API: Successfully updated container ${containerId} by user ${user.id}:`, {
      container_number_override: isContainerNumberOverride,
      seal_number_override: isSealNumberOverride,
      override_reason,
      updatedBy: `${profile.first_name} ${profile.last_name}`
    })

    return NextResponse.json({
      success: true,
      container: updatedContainer,
      overrides_applied: {
        container_number: isContainerNumberOverride,
        seal_number: isSealNumberOverride
      },
      override_reason,
      updated_by: {
        user_id: user.id,
        name: `${profile.first_name} ${profile.last_name}`,
        role: profile.role
      },
      updated_at: overrideTime
    })

  } catch (error) {
    console.error('Container override API Error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}