'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Factory as FactoryIcon,
  Award,
  Clock,
  Phone,
  Package,
  Thermometer,
  Scale,
  Truck,
  Timer,
  Plus,
  X,
  User,
  CheckCircle,
  Settings,
} from 'lucide-react'
import type { FactoryInfo } from '@/lib/validations/companies'
import { CERTIFICATIONS, SPECIALIZATIONS } from '@/lib/validations/companies'

interface FactoryInfoFormProps {
  value?: Partial<FactoryInfo>
  onChange: (info: Partial<FactoryInfo>) => void
  errors?: any
}

export function FactoryInfoForm({
  value = {},
  onChange,
  errors = {},
}: FactoryInfoFormProps) {
  const [newCertification, setNewCertification] = useState('')
  const [newSpecialization, setNewSpecialization] = useState('')

  const updateField = <K extends keyof FactoryInfo>(
    field: K,
    fieldValue: FactoryInfo[K]
  ) => {
    onChange({ ...value, [field]: fieldValue })
  }

  const addCertification = (cert: string) => {
    const current = value.certifications || []
    if (cert && !current.includes(cert)) {
      updateField('certifications', [...current, cert])
    }
    setNewCertification('')
  }

  const removeCertification = (cert: string) => {
    const current = value.certifications || []
    updateField(
      'certifications',
      current.filter(c => c !== cert)
    )
  }

  const addSpecialization = (spec: string) => {
    const current = value.specializations || []
    if (spec && !current.includes(spec)) {
      updateField('specializations', [...current, spec])
    }
    setNewSpecialization('')
  }

  const removeSpecialization = (spec: string) => {
    const current = value.specializations || []
    updateField(
      'specializations',
      current.filter(s => s !== spec)
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2 pb-2 border-b border-slate-600">
        <FactoryIcon className="h-5 w-5 text-purple-500" />
        <h3 className="text-lg font-semibold text-white">Factory Details</h3>
        <Badge
          variant="secondary"
          className="bg-purple-500/20 text-purple-300 border-purple-400"
        >
          Production Information
        </Badge>
      </div>

      {/* Basic Factory Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Factory Code */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Package className="h-4 w-4 text-blue-500" />
            <span>Factory Code *</span>
          </Label>
          <Input
            placeholder="e.g., FAC001, PROD-TH"
            value={value.factory_code || ''}
            onChange={e => updateField('factory_code', e.target.value)}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.factory_code && (
            <p className="text-sm text-red-400">
              {errors.factory_code.message}
            </p>
          )}
          <p className="text-xs text-slate-400">
            Unique identifier for this factory
          </p>
        </div>

        {/* License Number */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span>License Number *</span>
          </Label>
          <Input
            placeholder="Operating license number"
            value={value.license_no || ''}
            onChange={e => updateField('license_no', e.target.value)}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-500 focus:ring-green-500"
          />
          {errors.license_no && (
            <p className="text-sm text-red-400">{errors.license_no.message}</p>
          )}
          <p className="text-xs text-slate-400">
            Official operating license number
          </p>
        </div>
      </div>

      {/* Production Capacity */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Production Capacity */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Scale className="h-4 w-4 text-orange-500" />
            <span>Production Capacity (tons/day)</span>
          </Label>
          <Input
            type="number"
            placeholder="0"
            min="0"
            value={value.production_capacity_tons_per_day || ''}
            onChange={e =>
              updateField(
                'production_capacity_tons_per_day',
                parseInt(e.target.value) || 0
              )
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
          />
          {errors.production_capacity_tons_per_day && (
            <p className="text-sm text-red-400">
              {errors.production_capacity_tons_per_day.message}
            </p>
          )}
          <p className="text-xs text-slate-400">
            Daily production capacity in tons
          </p>
        </div>

        {/* Cold Storage Capacity */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Thermometer className="h-4 w-4 text-blue-500" />
            <span>Cold Storage (tons)</span>
          </Label>
          <Input
            type="number"
            placeholder="0"
            min="0"
            value={value.cold_storage_capacity_tons || ''}
            onChange={e =>
              updateField(
                'cold_storage_capacity_tons',
                parseInt(e.target.value) || 0
              )
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.cold_storage_capacity_tons && (
            <p className="text-sm text-red-400">
              {errors.cold_storage_capacity_tons.message}
            </p>
          )}
          <p className="text-xs text-slate-400">
            Cold storage capacity in tons
          </p>
        </div>
      </div>

      {/* Certifications */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Award className="h-4 w-4 text-yellow-500" />
          <span>Certifications</span>
        </Label>

        {/* Current Certifications */}
        {value.certifications && value.certifications.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {value.certifications.map(cert => (
              <Badge
                key={cert}
                variant="secondary"
                className="bg-yellow-500/20 text-yellow-300 border-yellow-400 flex items-center space-x-1"
              >
                <Award className="h-3 w-3" />
                <span>{cert}</span>
                <button
                  type="button"
                  onClick={() => removeCertification(cert)}
                  className="ml-1 hover:text-yellow-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Add Certification */}
        <div className="flex space-x-2">
          <Select value={newCertification} onValueChange={setNewCertification}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-yellow-500 focus:ring-yellow-500">
              <SelectValue placeholder="Select certification" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {CERTIFICATIONS.filter(
                cert => !value.certifications?.includes(cert)
              ).map(cert => (
                <SelectItem
                  key={cert}
                  value={cert}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  {cert}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            onClick={() => addCertification(newCertification)}
            disabled={!newCertification}
            className="bg-yellow-500 hover:bg-yellow-600 text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Specializations */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Package className="h-4 w-4 text-green-500" />
          <span>Product Specializations</span>
        </Label>

        {/* Current Specializations */}
        {value.specializations && value.specializations.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {value.specializations.map(spec => (
              <Badge
                key={spec}
                variant="secondary"
                className="bg-green-500/20 text-green-300 border-green-400 flex items-center space-x-1"
              >
                <Package className="h-3 w-3" />
                <span>{spec.replace('_', ' ')}</span>
                <button
                  type="button"
                  onClick={() => removeSpecialization(spec)}
                  className="ml-1 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Add Specialization */}
        <div className="flex space-x-2">
          <Select
            value={newSpecialization}
            onValueChange={setNewSpecialization}
          >
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-green-500 focus:ring-green-500">
              <SelectValue placeholder="Select specialization" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {SPECIALIZATIONS.filter(
                spec => !value.specializations?.includes(spec)
              ).map(spec => (
                <SelectItem
                  key={spec}
                  value={spec}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  {spec
                    .replace('_', ' ')
                    .replace(/\b\w/g, l => l.toUpperCase())}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            onClick={() => addSpecialization(newSpecialization)}
            disabled={!newSpecialization}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Quality Control Information */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <CheckCircle className="h-4 w-4 text-blue-500" />
          <span>Quality Control</span>
        </Label>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Quality Control Manager */}
          <div className="space-y-2">
            <Label className="text-slate-200 flex items-center space-x-2">
              <User className="h-4 w-4 text-purple-500" />
              <span>QC Manager</span>
            </Label>
            <Input
              placeholder="Quality Control Manager name"
              value={value.quality_control_manager || ''}
              onChange={e =>
                updateField('quality_control_manager', e.target.value)
              }
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-purple-500"
            />
          </div>

          {/* Quality Control Phone */}
          <div className="space-y-2">
            <Label className="text-slate-200 flex items-center space-x-2">
              <Phone className="h-4 w-4 text-orange-500" />
              <span>QC Contact Phone</span>
            </Label>
            <Input
              placeholder="+66 xx xxx xxxx"
              value={value.quality_control_phone || ''}
              onChange={e =>
                updateField('quality_control_phone', e.target.value)
              }
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
            />
            {errors.quality_control_phone && (
              <p className="text-sm text-red-400">
                {errors.quality_control_phone.message}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Operations Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Loading Dock Count */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Truck className="h-4 w-4 text-blue-500" />
            <span>Loading Docks</span>
          </Label>
          <Input
            type="number"
            placeholder="1"
            min="1"
            value={value.loading_dock_count || ''}
            onChange={e =>
              updateField('loading_dock_count', parseInt(e.target.value) || 1)
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.loading_dock_count && (
            <p className="text-sm text-red-400">
              {errors.loading_dock_count.message}
            </p>
          )}
          <p className="text-xs text-slate-400">Number of loading docks</p>
        </div>

        {/* Container Loading Time */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Timer className="h-4 w-4 text-yellow-500" />
            <span>Loading Time (min)</span>
          </Label>
          <Input
            type="number"
            placeholder="120"
            min="1"
            value={value.container_loading_time_minutes || ''}
            onChange={e =>
              updateField(
                'container_loading_time_minutes',
                parseInt(e.target.value) || 120
              )
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-yellow-500 focus:ring-yellow-500"
          />
          {errors.container_loading_time_minutes && (
            <p className="text-sm text-red-400">
              {errors.container_loading_time_minutes.message}
            </p>
          )}
          <p className="text-xs text-slate-400">
            Average container loading time
          </p>
        </div>

        {/* Advance Booking Required */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Clock className="h-4 w-4 text-green-500" />
            <span>Booking Required (hrs)</span>
          </Label>
          <Input
            type="number"
            placeholder="24"
            min="0"
            value={value.advance_booking_required_hours || ''}
            onChange={e =>
              updateField(
                'advance_booking_required_hours',
                parseInt(e.target.value) || 24
              )
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-500 focus:ring-green-500"
          />
          {errors.advance_booking_required_hours && (
            <p className="text-sm text-red-400">
              {errors.advance_booking_required_hours.message}
            </p>
          )}
          <p className="text-xs text-slate-400">
            Hours advance booking required
          </p>
        </div>
      </div>

      {/* Operating Hours */}
      <div className="space-y-2">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Clock className="h-4 w-4 text-purple-500" />
          <span>Operating Hours</span>
        </Label>
        <Textarea
          placeholder={`Enter operating hours information, e.g.:
Monday-Friday: 08:00-17:00
Saturday: 08:00-12:00
Sunday: Closed
Holiday Schedule: Contact QC Manager`}
          value={
            typeof value.operating_hours === 'string'
              ? value.operating_hours
              : JSON.stringify(value.operating_hours || {}, null, 2)
          }
          onChange={e => {
            try {
              const parsed = JSON.parse(e.target.value)
              updateField('operating_hours', parsed)
            } catch {
              updateField('operating_hours', e.target.value as any)
            }
          }}
          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-purple-500"
          rows={4}
        />
        <p className="text-xs text-slate-400">
          Describe factory operating hours and schedules
        </p>
      </div>

      {/* Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Scale className="h-4 w-4 text-orange-500" />
            <span className="text-sm font-medium text-slate-200">
              Production
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Daily capacity and storage capabilities
          </p>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Award className="h-4 w-4 text-yellow-500" />
            <span className="text-sm font-medium text-slate-200">Quality</span>
          </div>
          <p className="text-xs text-slate-400">
            Certifications and quality control
          </p>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Settings className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-slate-200">
              Operations
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Loading facilities and scheduling
          </p>
        </div>
      </div>
    </div>
  )
}
