'use client'

import { useAuth } from '@/hooks/use-auth'
import { useEffect } from 'react'

export default function DebugPage() {
  const { user, profile, loading, isAuthenticated, isAdmin } = useAuth()

  useEffect(() => {
    console.log('Debug Auth State:', {
      user,
      profile,
      loading,
      isAuthenticated,
      isAdmin,
    })
  }, [user, profile, loading, isAuthenticated, isAdmin])

  if (loading) {
    return <div className="p-6 text-white">Loading...</div>
  }

  return (
    <div className="p-6 bg-slate-900 min-h-screen text-white">
      <h1 className="text-2xl font-bold mb-6">Debug Authentication</h1>

      <div className="space-y-4">
        <div>
          <strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
        </div>

        <div>
          <strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}
        </div>

        {user && (
          <div>
            <strong>User:</strong>
            <pre className="bg-slate-800 p-4 rounded mt-2 overflow-auto">
              {JSON.stringify(user, null, 2)}
            </pre>
          </div>
        )}

        {profile && (
          <div>
            <strong>Profile:</strong>
            <pre className="bg-slate-800 p-4 rounded mt-2 overflow-auto">
              {JSON.stringify(profile, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
