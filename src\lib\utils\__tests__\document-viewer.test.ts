/**
 * Tests for Document Viewer Utilities
 */

import { getDocumentPreviewInfo } from '../document-viewer'

describe('document-viewer', () => {
  describe('getDocumentPreviewInfo', () => {
    test('should identify PDF files as previewable', () => {
      const result = getDocumentPreviewInfo('invoice.pdf')

      expect(result.canPreview).toBe(true)
      expect(result.previewType).toBe('pdf')
      expect(result.extension).toBe('pdf')
      expect(result.mimeType).toBe('application/pdf')
    })

    test('should identify image files as previewable', () => {
      const jpgResult = getDocumentPreviewInfo('photo.jpg')
      expect(jpgResult.canPreview).toBe(true)
      expect(jpgResult.previewType).toBe('image')
      expect(jpgResult.mimeType).toBe('image/jpeg')

      const pngResult = getDocumentPreviewInfo('screenshot.png')
      expect(pngResult.canPreview).toBe(true)
      expect(pngResult.previewType).toBe('image')
      expect(pngResult.mimeType).toBe('image/png')
    })

    test('should identify text files as previewable', () => {
      const txtResult = getDocumentPreviewInfo('readme.txt')
      expect(txtResult.canPreview).toBe(true)
      expect(txtResult.previewType).toBe('text')
      expect(txtResult.mimeType).toBe('text/plain')

      const csvResult = getDocumentPreviewInfo('data.csv')
      expect(csvResult.canPreview).toBe(true)
      expect(csvResult.previewType).toBe('text')
      expect(csvResult.mimeType).toBe('text/csv')
    })

    test('should identify non-previewable files', () => {
      const docResult = getDocumentPreviewInfo('document.doc')
      expect(docResult.canPreview).toBe(false)
      expect(docResult.previewType).toBe('unsupported')
      expect(docResult.mimeType).toBe('application/msword')

      const exeResult = getDocumentPreviewInfo('app.exe')
      expect(exeResult.canPreview).toBe(false)
      expect(exeResult.previewType).toBe('unsupported')
      expect(exeResult.mimeType).toBe('application/octet-stream')
    })

    test('should handle files without extensions', () => {
      const result = getDocumentPreviewInfo('filename')
      expect(result.canPreview).toBe(false)
      expect(result.previewType).toBe('unsupported')
      expect(result.extension).toBe('')
      expect(result.mimeType).toBe('application/octet-stream')
    })

    test('should handle case insensitive extensions', () => {
      const result = getDocumentPreviewInfo('INVOICE.PDF')
      expect(result.canPreview).toBe(true)
      expect(result.previewType).toBe('pdf')
      expect(result.extension).toBe('pdf')
      expect(result.mimeType).toBe('application/pdf')
    })
  })
})