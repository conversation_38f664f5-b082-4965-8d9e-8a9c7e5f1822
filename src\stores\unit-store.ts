'use client'

import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type {
  UnitOfMeasure,
  UnitOfMeasureInsert,
  UnitOfMeasureUpdate,
  UnitOfMeasureWithBase,
} from '@/lib/supabase/types'
import type { UnitFilter } from '@/lib/validations/products'

interface UnitState {
  // Data state
  units: UnitOfMeasureWithBase[]
  loading: boolean
  error: string | null

  // Filter and search state
  filter: UnitFilter
  searchTerm: string
  sortBy: 'name' | 'code' | 'category' | 'created_at'
  sortOrder: 'asc' | 'desc'

  // Pagination state
  currentPage: number
  pageSize: number
  totalCount: number

  // UI state
  selectedUnits: Set<string>
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean

  // Actions
  fetchUnits: () => Promise<void>
  fetchUnitById: (id: string) => Promise<UnitOfMeasureWithBase | null>
  createUnit: (unit: UnitOfMeasureInsert) => Promise<UnitOfMeasure>
  updateUnit: (
    id: string,
    updates: Partial<UnitOfMeasureUpdate>
  ) => Promise<UnitOfMeasure>
  deleteUnit: (id: string) => Promise<void>
  deleteUnits: (ids: string[]) => Promise<void>

  // Filter and search actions
  setFilter: (filter: Partial<UnitFilter>) => void
  setSearchTerm: (term: string) => void
  setSorting: (
    sortBy: UnitState['sortBy'],
    sortOrder: UnitState['sortOrder']
  ) => void

  // Pagination actions
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Selection actions
  selectUnit: (id: string) => void
  deselectUnit: (id: string) => void
  selectAllUnits: () => void
  clearSelection: () => void

  // Utility actions
  clearError: () => void
  reset: () => void

  // Real-time subscription management
  subscribeToUnits: () => () => void
}

const initialState = {
  units: [],
  loading: false,
  error: null,
  filter: {},
  searchTerm: '',
  sortBy: 'name' as const,
  sortOrder: 'asc' as const,
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  selectedUnits: new Set<string>(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
}

export const useUnitStore = create<UnitState>((set, get) => ({
  ...initialState,

  // Fetch units with filters, search, and pagination
  fetchUnits: async () => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()
      const { filter, searchTerm, sortBy, sortOrder, currentPage, pageSize } =
        get()

      let query = supabase.from('units_of_measure').select(
        `
          *,
          base_unit:base_unit_id (*)
        `,
        { count: 'exact' }
      )

      // Apply filters
      if (filter.category) {
        query = query.eq('category', filter.category)
      }

      if (filter.is_active !== undefined) {
        query = query.eq('is_active', filter.is_active)
      } else {
        // Default to show only active units
        query = query.eq('is_active', true)
      }

      // Apply search
      if (searchTerm.trim()) {
        query = query.or(
          `name.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%,symbol.ilike.%${searchTerm}%`
        )
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })

      // Apply pagination
      const from = (currentPage - 1) * pageSize
      const to = from + pageSize - 1
      query = query.range(from, to)

      const { data, count, error } = await query

      if (error) {
        throw new Error(error.message)
      }

      set({
        units: data || [],
        totalCount: count || 0,
        loading: false,
      })
    } catch (error) {
      console.error('Error fetching units:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch units',
        loading: false,
      })
    }
  },

  // Fetch single unit by ID
  fetchUnitById: async (id: string) => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('units_of_measure')
        .select(
          `
          *,
          base_unit:base_unit_id (*)
        `
        )
        .eq('id', id)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    } catch (error) {
      console.error('Error fetching unit:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch unit',
      })
      return null
    }
  },

  // Create new unit
  createUnit: async (unitData: UnitOfMeasureInsert) => {
    set({ isCreating: true, error: null })

    try {
      const supabase = createClient()

      // Check for duplicate code - race condition prevention
      const { data: existing } = await supabase
        .from('units_of_measure')
        .select('id')
        .eq('code', unitData.code)
        .eq('is_active', true)
        .single()

      if (existing) {
        throw new Error(`Unit with code "${unitData.code}" already exists`)
      }

      const { data, error } = await supabase
        .from('units_of_measure')
        .insert(unitData)
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      // Refresh the list to show the new unit
      await get().fetchUnits()

      set({ isCreating: false })
      return data
    } catch (error) {
      console.error('Error creating unit:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to create unit',
        isCreating: false,
      })
      throw error
    }
  },

  // Update existing unit
  updateUnit: async (id: string, updates: Partial<UnitOfMeasureUpdate>) => {
    set({ isUpdating: true, error: null })

    try {
      const supabase = createClient()

      // If updating code, check for duplicates - race condition prevention
      if (updates.code) {
        const { data: existing } = await supabase
          .from('units_of_measure')
          .select('id')
          .eq('code', updates.code)
          .eq('is_active', true)
          .neq('id', id)
          .single()

        if (existing) {
          throw new Error(`Unit with code "${updates.code}" already exists`)
        }
      }

      const { data, error } = await supabase
        .from('units_of_measure')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      // Update the unit in the local state
      set(state => ({
        units: state.units.map(unit =>
          unit.id === id ? { ...unit, ...data } : unit
        ),
        isUpdating: false,
      }))

      return data
    } catch (error) {
      console.error('Error updating unit:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to update unit',
        isUpdating: false,
      })
      throw error
    }
  },

  // Delete single unit
  deleteUnit: async (id: string) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Check if unit is being used by products
      const { data: products, error: checkError } = await supabase
        .from('products')
        .select('id')
        .eq('unit_of_measure_id', id)
        .eq('is_active', true)
        .limit(1)

      if (checkError) {
        throw new Error(checkError.message)
      }

      if (products && products.length > 0) {
        throw new Error(
          'Cannot delete unit of measure that is being used by products'
        )
      }

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('units_of_measure')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        units: state.units.filter(unit => unit.id !== id),
        selectedUnits: new Set(
          [...state.selectedUnits].filter(selectedId => selectedId !== id)
        ),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting unit:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to delete unit',
        isDeleting: false,
      })
      throw error
    }
  },

  // Delete multiple units
  deleteUnits: async (ids: string[]) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Check if any units are being used by products
      const { data: products, error: checkError } = await supabase
        .from('products')
        .select('id, unit_of_measure_id')
        .in('unit_of_measure_id', ids)
        .eq('is_active', true)

      if (checkError) {
        throw new Error(checkError.message)
      }

      if (products && products.length > 0) {
        const usedIds = [...new Set(products.map(p => p.unit_of_measure_id))]
        throw new Error(
          `Cannot delete units that are being used by products: ${usedIds.join(', ')}`
        )
      }

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('units_of_measure')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .in('id', ids)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        units: state.units.filter(unit => !ids.includes(unit.id)),
        selectedUnits: new Set(),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting units:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete units',
        isDeleting: false,
      })
      throw error
    }
  },

  // Filter and search actions
  setFilter: (newFilter: Partial<UnitFilter>) => {
    set(state => ({
      filter: { ...state.filter, ...newFilter },
      currentPage: 1, // Reset to first page when filtering
    }))
    get().fetchUnits()
  },

  setSearchTerm: (term: string) => {
    set({ searchTerm: term, currentPage: 1 })
    // Debounce the search - in a real app, use a debounce utility
    setTimeout(() => {
      if (get().searchTerm === term) {
        get().fetchUnits()
      }
    }, 300)
  },

  setSorting: (
    sortBy: UnitState['sortBy'],
    sortOrder: UnitState['sortOrder']
  ) => {
    set({ sortBy, sortOrder })
    get().fetchUnits()
  },

  // Pagination actions
  setPage: (page: number) => {
    set({ currentPage: page })
    get().fetchUnits()
  },

  setPageSize: (size: number) => {
    set({ pageSize: size, currentPage: 1 })
    get().fetchUnits()
  },

  // Selection actions
  selectUnit: (id: string) => {
    set(state => ({
      selectedUnits: new Set([...state.selectedUnits, id]),
    }))
  },

  deselectUnit: (id: string) => {
    set(state => {
      const newSelection = new Set(state.selectedUnits)
      newSelection.delete(id)
      return { selectedUnits: newSelection }
    })
  },

  selectAllUnits: () => {
    set(state => ({
      selectedUnits: new Set(state.units.map(unit => unit.id)),
    }))
  },

  clearSelection: () => {
    set({ selectedUnits: new Set() })
  },

  // Utility actions
  clearError: () => {
    set({ error: null })
  },

  reset: () => {
    set(initialState)
  },

  // Real-time subscription
  subscribeToUnits: () => {
    const supabase = createClient()

    const subscription = supabase
      .channel('units_of_measure_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'units_of_measure',
        },
        payload => {
          console.log('Unit change received:', payload)
          // Refresh the list when changes occur
          get().fetchUnits()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  },
}))

// Selector hooks for better performance
export const useUnits = () => useUnitStore(state => state.units)
export const useUnitsLoading = () => useUnitStore(state => state.loading)
export const useUnitsError = () => useUnitStore(state => state.error)
export const useUnitActions = () =>
  useUnitStore(state => ({
    fetchUnits: state.fetchUnits,
    fetchUnitById: state.fetchUnitById,
    createUnit: state.createUnit,
    updateUnit: state.updateUnit,
    deleteUnit: state.deleteUnit,
    deleteUnits: state.deleteUnits,
    setFilter: state.setFilter,
    setSearchTerm: state.setSearchTerm,
    setSorting: state.setSorting,
    setPage: state.setPage,
    selectUnit: state.selectUnit,
    deselectUnit: state.deselectUnit,
    clearSelection: state.clearSelection,
    clearError: state.clearError,
    subscribeToUnits: state.subscribeToUnits,
  }))
