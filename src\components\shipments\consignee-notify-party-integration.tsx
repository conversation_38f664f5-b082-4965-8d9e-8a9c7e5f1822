'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Bell,
  Building2,
  Star,
  StarOff,
  Hash,
  Info,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Mail,
  Smartphone,
  MessageSquare,
  Loader2,
} from 'lucide-react'
import {
  useConsigneeOptions,
  useConsigneeNotifyPartiesRealtime,
  useConsigneeNotifyPartyCheck,
} from '@/hooks/use-consignee-notify-parties'
import type { ConsigneeNotifyParty } from '@/stores/consignee-notify-party-store'

interface ConsigneeNotifyPartyIntegrationProps {
  // Form values from parent shipment form
  selectedConsigneeId?: string
  selectedNotifyPartyId?: string

  // Callbacks to update parent form
  onConsigneeChange?: (consigneeId: string, consigneeData?: any) => void
  onNotifyPartyChange?: (
    notifyPartyId: string,
    relationship?: ConsigneeNotifyParty
  ) => void
  onNotificationPreferencesChange?: (preferences: any) => void
  onSpecialInstructionsChange?: (instructions: string) => void

  // Configuration options
  autoSelectDefault?: boolean
  showRelationshipDetails?: boolean
  disabled?: boolean

  // Visual customization
  compact?: boolean
  showPriorityOrder?: boolean
}

export function ConsigneeNotifyPartyIntegration({
  selectedConsigneeId,
  selectedNotifyPartyId,
  onConsigneeChange,
  onNotifyPartyChange,
  onNotificationPreferencesChange,
  onSpecialInstructionsChange,
  autoSelectDefault = true,
  showRelationshipDetails = true,
  disabled = false,
  compact = false,
  showPriorityOrder = false,
}: ConsigneeNotifyPartyIntegrationProps) {
  const [isInitializing, setIsInitializing] = useState(false)

  // Fetch consignee options
  const { consignees, loading: loadingConsignees } = useConsigneeOptions()

  // Real-time notify party relationships for selected consignee
  const { consigneeNotifyParties, loading: loadingRelationships } =
    useConsigneeNotifyPartiesRealtime(selectedConsigneeId)

  // Check consignee relationships info
  const {
    hasRelationships,
    defaultNotifyParty,
    relationshipCount,
    loading: loadingCheck,
  } = useConsigneeNotifyPartyCheck(selectedConsigneeId)

  // Find selected relationship details
  const selectedRelationship = consigneeNotifyParties.find(
    rel => rel.notify_party_id === selectedNotifyPartyId
  )

  // Auto-select default notify party when consignee changes
  useEffect(() => {
    if (!selectedConsigneeId || !autoSelectDefault || isInitializing) return

    setIsInitializing(true)

    // Wait for relationships to load
    if (loadingRelationships) return

    // If there's a default notify party and no current selection, auto-select it
    if (defaultNotifyParty && !selectedNotifyPartyId) {
      onNotifyPartyChange?.(
        defaultNotifyParty.notify_party_id,
        defaultNotifyParty
      )

      // Also populate related data
      if (defaultNotifyParty.notification_preferences) {
        onNotificationPreferencesChange?.(
          defaultNotifyParty.notification_preferences
        )
      }

      if (defaultNotifyParty.special_instructions) {
        onSpecialInstructionsChange?.(defaultNotifyParty.special_instructions)
      }
    }

    setIsInitializing(false)
  }, [
    selectedConsigneeId,
    defaultNotifyParty,
    selectedNotifyPartyId,
    autoSelectDefault,
    loadingRelationships,
    onNotifyPartyChange,
    onNotificationPreferencesChange,
    onSpecialInstructionsChange,
    isInitializing,
  ])

  // Handle consignee selection
  const handleConsigneeChange = useCallback(
    (consigneeId: string) => {
      const consigneeData = consignees.find(c => c.id === consigneeId)
      onConsigneeChange?.(consigneeId, consigneeData)

      // Clear notify party selection when consignee changes
      onNotifyPartyChange?.('', undefined)
    },
    [consignees, onConsigneeChange, onNotifyPartyChange]
  )

  // Handle notify party selection
  const handleNotifyPartyChange = useCallback(
    (notifyPartyId: string) => {
      const relationship = consigneeNotifyParties.find(
        rel => rel.notify_party_id === notifyPartyId
      )

      onNotifyPartyChange?.(notifyPartyId, relationship)

      // Auto-populate related fields if relationship exists
      if (relationship) {
        if (relationship.notification_preferences) {
          onNotificationPreferencesChange?.(
            relationship.notification_preferences
          )
        }

        if (relationship.special_instructions) {
          onSpecialInstructionsChange?.(relationship.special_instructions)
        }
      }
    },
    [
      consigneeNotifyParties,
      onNotifyPartyChange,
      onNotificationPreferencesChange,
      onSpecialInstructionsChange,
    ]
  )

  // Get notification icons
  const getNotificationIcons = (preferences: any) => {
    if (!preferences) return []
    const icons = []
    if (preferences.email)
      icons.push(<Mail key="email" className="h-3 w-3 text-blue-400" />)
    if (preferences.sms)
      icons.push(<Smartphone key="sms" className="h-3 w-3 text-green-400" />)
    if (preferences.line)
      icons.push(
        <MessageSquare key="line" className="h-3 w-3 text-green-500" />
      )
    if (preferences.wechat)
      icons.push(
        <MessageSquare key="wechat" className="h-3 w-3 text-green-600" />
      )
    return icons
  }

  // Refresh relationships data
  const refreshRelationships = useCallback(() => {
    // This would trigger a refresh - functionality would be added to the hook
    console.log('Refreshing relationships for consignee:', selectedConsigneeId)
  }, [selectedConsigneeId])

  if (compact) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Compact Consignee Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-slate-200">
            Consignee *
          </label>
          <Select
            value={selectedConsigneeId || ''}
            onValueChange={handleConsigneeChange}
            disabled={disabled || loadingConsignees}
          >
            <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue placeholder="Select consignee" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
              {loadingConsignees ? (
                <div className="p-4 text-center text-slate-400">
                  <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                  Loading consignees...
                </div>
              ) : consignees.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  No consignees found
                </div>
              ) : (
                consignees.map(consignee => (
                  <SelectItem
                    key={consignee.id}
                    value={consignee.id}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-3 w-3 text-blue-500" />
                      <span>{consignee.name}</span>
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Compact Notify Party Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-slate-200">
            Notify Party *
          </label>
          <Select
            value={selectedNotifyPartyId || ''}
            onValueChange={handleNotifyPartyChange}
            disabled={disabled || !selectedConsigneeId || loadingRelationships}
          >
            <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue
                placeholder={
                  !selectedConsigneeId
                    ? 'Select consignee first'
                    : 'Select notify party'
                }
              />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
              {loadingRelationships ? (
                <div className="p-4 text-center text-slate-400">
                  <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                  Loading notify parties...
                </div>
              ) : consigneeNotifyParties.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  No notify parties configured for this consignee
                </div>
              ) : (
                consigneeNotifyParties.map(relationship => (
                  <SelectItem
                    key={relationship.notify_party_id}
                    value={relationship.notify_party_id}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center space-x-2">
                        <Bell className="h-3 w-3 text-green-500" />
                        <span>{relationship.notify_party?.name}</span>
                        {relationship.is_default && (
                          <Star className="h-3 w-3 text-yellow-500" />
                        )}
                      </div>
                      {showPriorityOrder && (
                        <Badge variant="outline" className="text-xs">
                          #{relationship.priority_order}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Consignee Selection */}
      <Card className="bg-slate-700 border-slate-600">
        <CardHeader>
          <CardTitle className="text-blue-200 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-blue-500" />
              Consignee Selection
            </div>
            {selectedConsigneeId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshRelationships}
                disabled={loadingCheck}
                className="text-slate-400 hover:text-white"
              >
                {loadingCheck ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            )}
          </CardTitle>
          <CardDescription className="text-slate-400">
            Select the consignee for this shipment
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Select
            value={selectedConsigneeId || ''}
            onValueChange={handleConsigneeChange}
            disabled={disabled || loadingConsignees}
          >
            <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue placeholder="Select consignee company" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
              {loadingConsignees ? (
                <div className="p-4 text-center text-slate-400">
                  <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                  Loading consignees...
                </div>
              ) : consignees.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  No consignee companies found
                </div>
              ) : (
                consignees.map(consignee => (
                  <SelectItem
                    key={consignee.id}
                    value={consignee.id}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-3 w-3 text-blue-500" />
                      <div>
                        <div>{consignee.name}</div>
                        {consignee.contact_phone && (
                          <div className="text-xs text-slate-400">
                            Phone: {consignee.contact_phone}
                          </div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>

          {/* Consignee relationship status */}
          {selectedConsigneeId && (
            <div className="flex items-center space-x-2 text-sm">
              {loadingCheck ? (
                <div className="flex items-center text-slate-400">
                  <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  Checking relationships...
                </div>
              ) : hasRelationships ? (
                <div className="flex items-center text-green-400">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  {relationshipCount} notify part
                  {relationshipCount !== 1 ? 'ies' : 'y'} configured
                  {defaultNotifyParty && (
                    <Badge className="ml-2 bg-yellow-600 text-white text-xs">
                      Default available
                    </Badge>
                  )}
                </div>
              ) : (
                <div className="flex items-center text-amber-400">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  No notify parties configured
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Notify Party Selection */}
      {selectedConsigneeId && (
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-green-200 flex items-center gap-2">
              <Bell className="h-5 w-5 text-green-500" />
              Notify Party Selection
            </CardTitle>
            <CardDescription className="text-slate-400">
              Select the notify party for this shipment (intelligently
              pre-populated based on consignee relationships)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {!hasRelationships ? (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className="text-slate-300">
                  No notify party relationships configured for this consignee.
                  You may need to set up relationships in Master Data →
                  Consignee Notify Party Relationships first.
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <Select
                  value={selectedNotifyPartyId || ''}
                  onValueChange={handleNotifyPartyChange}
                  disabled={disabled || loadingRelationships}
                >
                  <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                    <SelectValue placeholder="Select notify party" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                    {loadingRelationships ? (
                      <div className="p-4 text-center text-slate-400">
                        <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                        Loading notify parties...
                      </div>
                    ) : consigneeNotifyParties.length === 0 ? (
                      <div className="p-4 text-center text-slate-400">
                        No notify parties found for this consignee
                      </div>
                    ) : (
                      consigneeNotifyParties
                        .sort((a, b) => {
                          // Sort by default first, then by priority order, then by name
                          if (a.is_default && !b.is_default) return -1
                          if (!a.is_default && b.is_default) return 1

                          const priorityA = a.priority_order || 999
                          const priorityB = b.priority_order || 999
                          if (priorityA !== priorityB)
                            return priorityA - priorityB

                          // Finally sort by notify party name
                          const nameA = a.notify_party?.name || ''
                          const nameB = b.notify_party?.name || ''
                          return nameA.localeCompare(nameB)
                        })
                        .map(relationship => (
                          <SelectItem
                            key={relationship.notify_party_id}
                            value={relationship.notify_party_id}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center space-x-2">
                                <Bell className="h-3 w-3 text-green-500" />
                                <div>
                                  <div className="flex items-center gap-2">
                                    {relationship.notify_party?.name}
                                    {relationship.is_default && (
                                      <Star className="h-3 w-3 text-yellow-500" />
                                    )}
                                  </div>
                                  {relationship.notify_party?.contact_email && (
                                    <div className="text-xs text-slate-400">
                                      {relationship.notify_party.contact_email}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-1">
                                {showPriorityOrder && (
                                  <div className="flex items-center gap-1 text-xs text-slate-400">
                                    <Hash className="h-3 w-3" />
                                    {relationship.priority_order}
                                  </div>
                                )}
                                <div className="flex items-center gap-1">
                                  {getNotificationIcons(
                                    relationship.notification_preferences
                                  )}
                                </div>
                              </div>
                            </div>
                          </SelectItem>
                        ))
                    )}
                  </SelectContent>
                </Select>

                {/* Show selected relationship details */}
                {selectedRelationship && showRelationshipDetails && (
                  <Card className="bg-slate-800 border-slate-600">
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-slate-400">
                            Relationship Status
                          </span>
                          <div className="flex items-center gap-2">
                            {selectedRelationship.is_default && (
                              <Badge className="bg-yellow-600 text-white text-xs">
                                Default
                              </Badge>
                            )}
                            <Badge
                              variant={
                                selectedRelationship.is_active
                                  ? 'default'
                                  : 'secondary'
                              }
                              className={
                                selectedRelationship.is_active
                                  ? 'bg-green-600 text-white'
                                  : 'bg-red-600 text-white'
                              }
                            >
                              {selectedRelationship.is_active
                                ? 'Active'
                                : 'Inactive'}
                            </Badge>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-slate-400">
                            Notification Channels
                          </span>
                          <div className="flex items-center space-x-1">
                            {getNotificationIcons(
                              selectedRelationship.notification_preferences
                            )}
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-slate-400">
                            Priority Order
                          </span>
                          <div className="flex items-center gap-1 text-sm text-slate-200">
                            <Hash className="h-3 w-3" />
                            {selectedRelationship.priority_order}
                          </div>
                        </div>

                        {selectedRelationship.special_instructions && (
                          <div>
                            <span className="text-sm text-slate-400">
                              Special Instructions
                            </span>
                            <p className="text-sm text-slate-200 mt-1 p-2 bg-slate-700 rounded border border-slate-600">
                              {selectedRelationship.special_instructions}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
