'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { getDocumentTemplateService } from '@/lib/services/document-template-service'
import type {
  DocumentTemplate,
  DocumentTemplateInsert,
  DocumentTemplateUpdate,
  DocumentTemplateFilters,
  DocumentTemplateSortConfig,
  DocumentTemplateQueryResult,
  DocumentType
} from '@/types/document-template'

export interface UseDocumentTemplatesOptions {
  filters?: DocumentTemplateFilters
  sort?: DocumentTemplateSortConfig
  pageSize?: number
  autoRefresh?: boolean
}

/**
 * Hook for managing document templates
 * Story 5.1: Document Template Management System
 */
export function useDocumentTemplates(options: UseDocumentTemplatesOptions = {}) {
  const [templates, setTemplates] = useState<DocumentTemplate[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const templateService = getDocumentTemplateService(supabase)

  const {
    filters = {},
    sort = { field: 'updated_at', direction: 'desc' },
    pageSize = 20,
    autoRefresh = true
  } = options

  // Use useMemo to create stable objects
  const stableFilters = useMemo(() => filters, [JSON.stringify(filters)])
  const stableSort = useMemo(() => sort, [JSON.stringify(sort)])

  /**
   * Load templates with current filters and pagination
   */
  const loadTemplates = useCallback(async (page = 1) => {
    setLoading(true)
    setError(null)

    try {
      const result = await templateService.getTemplates({
        filters: stableFilters,
        sort: stableSort,
        pagination: { page, pageSize }
      })

      if (result.error) {
        setError(result.error)
      } else {
        setTemplates(result.data)
        setTotalCount(result.totalCount)
        setCurrentPage(page)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load templates'
      console.error('useDocumentTemplates.loadTemplates error:', err)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [templateService, stableFilters, stableSort, pageSize])

  /**
   * Create new template
   */
  const createTemplate = useCallback(async (
    template: DocumentTemplateInsert,
    userId: string
  ): Promise<{ success: boolean; data?: DocumentTemplate; error?: string }> => {
    setLoading(true)
    setError(null)

    try {
      const result = await templateService.createTemplate(template, userId)
      
      if (result.error) {
        setError(result.error)
        return { success: false, error: result.error }
      }

      // Refresh the list if successful
      if (autoRefresh) {
        await loadTemplates(currentPage)
      }

      return { success: true, data: result.data! }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create template'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [templateService, autoRefresh, loadTemplates, currentPage])

  /**
   * Update existing template
   */
  const updateTemplate = useCallback(async (
    id: string,
    updates: DocumentTemplateUpdate
  ): Promise<{ success: boolean; data?: DocumentTemplate; error?: string }> => {
    setLoading(true)
    setError(null)

    try {
      const result = await templateService.updateTemplate(id, updates)
      
      if (result.error) {
        setError(result.error)
        return { success: false, error: result.error }
      }

      // Refresh the list if successful
      if (autoRefresh) {
        await loadTemplates(currentPage)
      }

      return { success: true, data: result.data! }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update template'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [templateService, autoRefresh, loadTemplates, currentPage])

  /**
   * Delete template
   */
  const deleteTemplate = useCallback(async (
    id: string
  ): Promise<{ success: boolean; error?: string }> => {
    setLoading(true)
    setError(null)

    try {
      const result = await templateService.deleteTemplate(id)
      
      if (!result.success) {
        setError(result.error!)
        return { success: false, error: result.error! }
      }

      // Refresh the list if successful
      if (autoRefresh) {
        await loadTemplates(currentPage)
      }

      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete template'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [templateService, autoRefresh, loadTemplates, currentPage])

  /**
   * Toggle template active status
   */
  const toggleTemplateStatus = useCallback(async (
    id: string,
    isActive: boolean
  ): Promise<{ success: boolean; error?: string }> => {
    setLoading(true)
    setError(null)

    try {
      const result = await templateService.toggleTemplateStatus(id, isActive)
      
      if (!result.success) {
        setError(result.error!)
        return { success: false, error: result.error! }
      }

      // Refresh the list if successful
      if (autoRefresh) {
        await loadTemplates(currentPage)
      }

      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to toggle template status'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [templateService, autoRefresh, loadTemplates, currentPage])

  /**
   * Set template as default for its document type
   */
  const setDefaultTemplate = useCallback(async (
    id: string
  ): Promise<{ success: boolean; error?: string }> => {
    setLoading(true)
    setError(null)

    try {
      const result = await templateService.setDefaultTemplate(id)
      
      if (!result.success) {
        setError(result.error!)
        return { success: false, error: result.error! }
      }

      // Refresh the list if successful
      if (autoRefresh) {
        await loadTemplates(currentPage)
      }

      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to set default template'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [templateService, autoRefresh, loadTemplates, currentPage])

  /**
   * Clone template with new version
   */
  const cloneTemplate = useCallback(async (
    id: string,
    newVersion: string,
    userId: string
  ): Promise<{ success: boolean; data?: DocumentTemplate; error?: string }> => {
    setLoading(true)
    setError(null)

    try {
      const result = await templateService.cloneTemplate(id, newVersion, userId)
      
      if (result.error) {
        setError(result.error)
        return { success: false, error: result.error }
      }

      // Refresh the list if successful
      if (autoRefresh) {
        await loadTemplates(currentPage)
      }

      return { success: true, data: result.data! }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clone template'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [templateService, autoRefresh, loadTemplates, currentPage])

  /**
   * Load initial data and set up subscriptions
   */
  useEffect(() => {
    loadTemplates(1)
  }, [loadTemplates])

  /**
   * Subscribe to real-time updates if autoRefresh is enabled
   * TEMPORARILY DISABLED TO FIX INFINITE LOOP
   */
  useEffect(() => {
    // Real-time subscription temporarily disabled due to infinite loop issue
    /*
    if (!autoRefresh) return

    const subscription = templateService.subscribeToTemplateUpdates(
      (payload) => {
        console.log('Template update received:', payload)
        // Refresh data on any change
        loadTemplates(currentPage)
      },
      filters
    )

    return () => {
      subscription.unsubscribe()
    }
    */
  }, [])

  /**
   * Pagination helpers
   */
  const totalPages = Math.ceil(totalCount / pageSize)
  const hasNextPage = currentPage < totalPages
  const hasPreviousPage = currentPage > 1

  const goToNextPage = useCallback(() => {
    if (hasNextPage) {
      loadTemplates(currentPage + 1)
    }
  }, [hasNextPage, currentPage, loadTemplates])

  const goToPreviousPage = useCallback(() => {
    if (hasPreviousPage) {
      loadTemplates(currentPage - 1)
    }
  }, [hasPreviousPage, currentPage, loadTemplates])

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages) {
      loadTemplates(page)
    }
  }, [totalPages, loadTemplates])

  return {
    // Data
    templates,
    totalCount,
    currentPage,
    totalPages,
    loading,
    error,

    // Actions
    loadTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    toggleTemplateStatus,
    setDefaultTemplate,
    cloneTemplate,

    // Pagination
    hasNextPage,
    hasPreviousPage,
    goToNextPage,
    goToPreviousPage,
    goToPage,

    // Utils
    refresh: () => loadTemplates(currentPage),
    clearError: () => setError(null),
  }
}

/**
 * Hook for getting a single template by ID
 */
export function useDocumentTemplate(id: string | null) {
  const [template, setTemplate] = useState<DocumentTemplate | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const templateService = getDocumentTemplateService(supabase)

  const loadTemplate = useCallback(async () => {
    if (!id) {
      setTemplate(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await templateService.getTemplateById(id)
      setTemplate(result)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load template'
      setError(errorMessage)
      console.error('useDocumentTemplate.loadTemplate error:', err)
    } finally {
      setLoading(false)
    }
  }, [id, templateService])

  useEffect(() => {
    loadTemplate()
  }, [loadTemplate])

  return {
    template,
    loading,
    error,
    refresh: loadTemplate,
    clearError: () => setError(null),
  }
}

/**
 * Hook for getting default template by document type
 */
export function useDefaultTemplate(documentType: DocumentType | null) {
  const [template, setTemplate] = useState<DocumentTemplate | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const templateService = getDocumentTemplateService(supabase)

  const loadDefaultTemplate = useCallback(async () => {
    if (!documentType) {
      setTemplate(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await templateService.getDefaultTemplate(documentType)
      setTemplate(result)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load default template'
      setError(errorMessage)
      console.error('useDefaultTemplate.loadDefaultTemplate error:', err)
    } finally {
      setLoading(false)
    }
  }, [documentType, templateService])

  useEffect(() => {
    loadDefaultTemplate()
  }, [loadDefaultTemplate])

  return {
    template,
    loading,
    error,
    refresh: loadDefaultTemplate,
    clearError: () => setError(null),
  }
}

/**
 * Hook for template versions
 */
export function useTemplateVersions(templateName: string | null) {
  const [versions, setVersions] = useState<DocumentTemplate[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const templateService = getDocumentTemplateService(supabase)

  const loadVersions = useCallback(async () => {
    if (!templateName) {
      setVersions([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await templateService.getTemplateVersions(templateName)
      setVersions(result)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load template versions'
      setError(errorMessage)
      console.error('useTemplateVersions.loadVersions error:', err)
    } finally {
      setLoading(false)
    }
  }, [templateName, templateService])

  useEffect(() => {
    loadVersions()
  }, [loadVersions])

  return {
    versions,
    loading,
    error,
    refresh: loadVersions,
    clearError: () => setError(null),
  }
}