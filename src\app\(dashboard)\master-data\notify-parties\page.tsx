'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Eye,
  X,
  Link,
  Building2,
  Bell,
  Star,
  StarOff,
  Mail,
  MessageSquare,
  Smartphone,
  Hash,
} from 'lucide-react'
import { ConsigneeNotifyPartyForm } from '@/components/forms/consignee-notify-party-form/consignee-notify-party-form'
import {
  useConsigneeNotifyPartiesManagement,
  useConsigneeOptions,
  useNotifyPartyOptions,
} from '@/hooks/use-consignee-notify-parties'
import type { ConsigneeNotifyParty } from '@/stores/consignee-notify-party-store'
import type { ConsigneeNotifyPartyInsert } from '@/stores/consignee-notify-party-store'
import type { ConsigneeNotifyPartyForm } from '@/lib/validations/consignee-notify-parties'
import { formatDistanceToNow, format } from 'date-fns'

export default function NotifyPartiesPage() {
  const {
    // Data
    consigneeNotifyParties,
    loading,
    error,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedConsigneeNotifyParties,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createConsigneeNotifyParty,
    updateConsigneeNotifyParty,
    deleteConsigneeNotifyParty,
    bulkDeleteConsigneeNotifyParties,
    setDefaultNotifyParty,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    toggleConsigneeNotifyParty,
    toggleAll,
    clearSelection,
    clearError,
    refreshConsigneeNotifyParties,
  } = useConsigneeNotifyPartiesManagement()

  const { consignees } = useConsigneeOptions()
  const { notifyParties } = useNotifyPartyOptions()

  // Debug logging
  console.log('🏢 Consignees:', consignees)
  console.log('🔔 Notify Parties:', notifyParties)
  console.log('🔗 Relationships:', consigneeNotifyParties)

  // Initialize relationship data on component mount
  useEffect(() => {
    console.log('🚀 Initializing consignee-notify party relationships data...')
    refreshConsigneeNotifyParties()
  }, [refreshConsigneeNotifyParties])

  // UI state
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingRelationship, setEditingRelationship] =
    useState<ConsigneeNotifyParty | null>(null)
  const [viewingRelationship, setViewingRelationship] =
    useState<ConsigneeNotifyParty | null>(null)
  const [deletingRelationship, setDeletingRelationship] =
    useState<ConsigneeNotifyParty | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // Handle create relationship
  const handleCreate = async (data: ConsigneeNotifyPartyForm) => {
    try {
      await createConsigneeNotifyParty(data)
      setShowCreateDialog(false)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle update relationship
  const handleUpdate = async (data: ConsigneeNotifyPartyForm) => {
    if (!editingRelationship) return

    try {
      await updateConsigneeNotifyParty(editingRelationship.id, data)
      setEditingRelationship(null)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle delete relationship
  const handleDelete = async (relationship: ConsigneeNotifyParty) => {
    try {
      await deleteConsigneeNotifyParty(relationship.id)
      setDeletingRelationship(null)
    } catch (error) {
      console.error('Delete failed:', error)
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      await bulkDeleteConsigneeNotifyParties(
        Array.from(selectedConsigneeNotifyParties)
      )
      setShowBulkDeleteDialog(false)
      clearSelection()
    } catch (error) {
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle set default
  const handleSetDefault = async (
    consigneeId: string,
    relationshipId: string
  ) => {
    try {
      await setDefaultNotifyParty(consigneeId, relationshipId)
    } catch (error) {
      console.error('Set default failed:', error)
    }
  }

  // Handle sorting
  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSorting(column, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSorting(column, 'asc')
    }
  }

  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  // Format notification preferences
  const formatNotificationPreferences = (preferences: any) => {
    if (!preferences) return 'N/A'
    const enabled = Object.entries(preferences)
      .filter(([_, value]) => value)
      .map(([key]) => key)
    return enabled.length > 0 ? enabled.join(', ') : 'None'
  }

  // Get notification icons
  const getNotificationIcons = (preferences: any) => {
    if (!preferences) return []
    const icons = []
    if (preferences.email)
      icons.push(<Mail key="email" className="h-3 w-3 text-blue-400" />)
    if (preferences.sms)
      icons.push(<Smartphone key="sms" className="h-3 w-3 text-green-400" />)
    if (preferences.line)
      icons.push(
        <MessageSquare key="line" className="h-3 w-3 text-green-500" />
      )
    if (preferences.wechat)
      icons.push(
        <MessageSquare key="wechat" className="h-3 w-3 text-green-600" />
      )
    return icons
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">
            Consignee-Notify Party Relationships
          </h1>
          <p className="text-slate-400 mt-1">
            Manage consignee-notify party relationships with communication
            preferences
          </p>
        </div>
        <div className="flex space-x-2">
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add Relationship
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
              <DialogHeader>
                <DialogTitle className="text-white">
                  Create Consignee-Notify Party Relationship
                </DialogTitle>
                <DialogDescription className="text-slate-400">
                  Associate a consignee with a notify party and configure
                  communication preferences
                </DialogDescription>
              </DialogHeader>
              <ConsigneeNotifyPartyForm
                onSubmit={handleCreate}
                onCancel={() => setShowCreateDialog(false)}
                isLoading={isCreating}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {(searchTerm ||
            filter.consignee_id ||
            filter.is_active !== undefined) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchTerm('')
                setFilter({})
              }}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Search Relationships
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search consignees, notify parties..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
          </div>

          {/* Consignee Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Consignee
            </label>
            <Select
              value={filter.consignee_id || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  consignee_id: value === 'all' ? undefined : value,
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Consignees" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Consignees
                </SelectItem>
                {consignees.map(consignee => (
                  <SelectItem
                    key={consignee.id}
                    value={consignee.id}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {consignee.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">Status</label>
            <Select
              value={
                filter.is_active === true
                  ? 'active'
                  : filter.is_active === false
                    ? 'inactive'
                    : 'all'
              }
              onValueChange={value =>
                setFilter({
                  ...filter,
                  is_active: value === 'all' ? undefined : value === 'active',
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Status
                </SelectItem>
                <SelectItem
                  value="active"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  Active
                </SelectItem>
                <SelectItem
                  value="inactive"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  Inactive
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {(searchTerm ||
          filter.consignee_id ||
          filter.is_active !== undefined) && (
          <div className="flex flex-wrap gap-2 pt-2">
            {searchTerm && (
              <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-2 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.consignee_id && (
              <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                Consignee:{' '}
                {consignees.find(c => c.id === filter.consignee_id)?.name ||
                  'Unknown'}
                <button
                  onClick={() =>
                    setFilter({ ...filter, consignee_id: undefined })
                  }
                  className="ml-2 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.is_active !== undefined && (
              <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
                Status: {filter.is_active ? 'Active' : 'Inactive'}
                <button
                  onClick={() => setFilter({ ...filter, is_active: undefined })}
                  className="ml-2 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedCount} relationship{selectedCount !== 1 ? 's' : ''}{' '}
                selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowBulkDeleteDialog(true)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Relationships Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Link className="h-5 w-5 text-orange-500" />
              Consignee-Notify Party Relationships ({totalCount})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshConsigneeNotifyParties}
              disabled={loading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {loading && consigneeNotifyParties.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-slate-300">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              <span className="ml-2">Loading relationships...</span>
            </div>
          ) : consigneeNotifyParties.length === 0 ? (
            <div className="text-center py-8">
              <Link className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No relationships found</p>
              <p className="text-sm text-slate-400 mt-1">
                Create your first consignee-notify party relationship to get
                started
              </p>
            </div>
          ) : (
            <>
              <Table className="bg-slate-800">
                <TableHeader className="bg-slate-700">
                  <TableRow className="border-slate-600 hover:bg-slate-700">
                    <TableHead className="w-12 text-slate-200">
                      <Checkbox
                        checked={isAllSelected || isPartiallySelected}
                        onCheckedChange={toggleAll}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('consignee')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Consignee {getSortIcon('consignee')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('notify_party')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Notify Party {getSortIcon('notify_party')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      Notification Preferences
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('priority_order')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Priority {getSortIcon('priority_order')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('is_default')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Default {getSortIcon('is_default')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">Status</TableHead>
                    <TableHead className="w-32 text-slate-200">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-slate-800">
                  {consigneeNotifyParties.map(relationship => (
                    <TableRow
                      key={relationship.id}
                      className="border-slate-600 hover:bg-slate-700 text-slate-200"
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected(relationship.id)}
                          onCheckedChange={() =>
                            toggleConsigneeNotifyParty(relationship.id)
                          }
                          className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Building2 className="h-4 w-4 text-blue-500 flex-shrink-0" />
                          <div className="min-w-0">
                            <div className="font-medium text-slate-200 truncate">
                              {relationship.consignee?.name ||
                                'Unknown Consignee'}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Bell className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <div className="min-w-0">
                            <div className="font-medium text-slate-200 truncate">
                              {relationship.notify_party?.name ||
                                'Unknown Notify Party'}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          {getNotificationIcons(
                            relationship.notification_preferences
                          ).slice(0, 4)}
                          {getNotificationIcons(
                            relationship.notification_preferences
                          ).length > 4 && (
                            <span className="text-xs text-slate-400">
                              +
                              {getNotificationIcons(
                                relationship.notification_preferences
                              ).length - 4}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Hash className="h-3 w-3 text-slate-400" />
                          <span className="text-slate-200">
                            {relationship.priority_order}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {relationship.is_default ? (
                            <Star className="h-4 w-4 text-yellow-500" />
                          ) : (
                            <button
                              onClick={() =>
                                handleSetDefault(
                                  relationship.consignee_id,
                                  relationship.id
                                )
                              }
                              className="opacity-50 hover:opacity-100"
                            >
                              <StarOff className="h-4 w-4 text-slate-500 hover:text-yellow-500" />
                            </button>
                          )}
                          <Badge
                            variant={
                              relationship.is_default ? 'default' : 'secondary'
                            }
                            className={
                              relationship.is_default
                                ? 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600'
                                : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                            }
                          >
                            {relationship.is_default ? 'Default' : 'Standard'}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            relationship.is_active ? 'default' : 'secondary'
                          }
                          className={
                            relationship.is_active
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-red-600 hover:bg-red-700 text-white border-red-600'
                          }
                        >
                          {relationship.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setViewingRelationship(relationship)}
                            className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingRelationship(relationship)}
                            className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              setDeletingRelationship(relationship)
                            }
                            className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-6 pb-6">
                  <div className="text-sm text-slate-300">
                    Page {currentPage} of {totalPages} ({totalCount}{' '}
                    relationships)
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(1)}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={previousPage}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Edit Relationship Dialog */}
      <Dialog
        open={!!editingRelationship}
        onOpenChange={() => setEditingRelationship(null)}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">
              Edit Consignee-Notify Party Relationship
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Update relationship configuration and communication preferences
            </DialogDescription>
          </DialogHeader>
          {editingRelationship && (
            <ConsigneeNotifyPartyForm
              consigneeNotifyParty={editingRelationship}
              onSubmit={handleUpdate}
              onCancel={() => setEditingRelationship(null)}
              isLoading={isUpdating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Relationship Dialog */}
      <AlertDialog
        open={!!deletingRelationship}
        onOpenChange={() => setDeletingRelationship(null)}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Relationship
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete this consignee-notify party
              relationship? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setDeletingRelationship(null)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                deletingRelationship && handleDelete(deletingRelationship)
              }
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Relationship
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Relationship Dialog */}
      <Dialog
        open={!!viewingRelationship}
        onOpenChange={() => setViewingRelationship(null)}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <Eye className="h-5 w-5 text-blue-500" />
              View Consignee-Notify Party Relationship Details
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Detailed view of the relationship configuration and communication
              preferences
            </DialogDescription>
          </DialogHeader>

          {viewingRelationship && (
            <div className="space-y-6 py-4">
              {/* Company Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <Building2 className="h-5 w-5 text-blue-500" />
                      Consignee Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        Consignee Name
                      </label>
                      <p className="text-slate-200 font-medium">
                        {viewingRelationship.consignee?.name ||
                          'Unknown Consignee'}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <Bell className="h-5 w-5 text-green-500" />
                      Notify Party Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                        Notify Party Name
                      </label>
                      <p className="text-slate-200 font-medium">
                        {viewingRelationship.notify_party?.name ||
                          'Unknown Notify Party'}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Communication Preferences */}
              <Card className="bg-slate-700 border-slate-600">
                <CardHeader className="pb-3">
                  <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                    <Bell className="h-5 w-5 text-yellow-500" />
                    Communication Preferences
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                      Enabled Channels
                    </label>
                    <div className="flex items-center space-x-2 mt-1">
                      {getNotificationIcons(
                        viewingRelationship.notification_preferences
                      )}
                      <span className="text-slate-200 ml-2">
                        {formatNotificationPreferences(
                          viewingRelationship.notification_preferences
                        )}
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                      Priority Order
                    </label>
                    <p className="text-slate-200 font-medium flex items-center gap-1">
                      <Hash className="h-4 w-4" />
                      {viewingRelationship.priority_order}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Special Instructions */}
              {viewingRelationship.special_instructions && (
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                      Special Instructions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-200 whitespace-pre-wrap">
                      {viewingRelationship.special_instructions}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Status and Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                      Status Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-slate-400">
                        Default Notify Party
                      </span>
                      <div className="flex items-center gap-2">
                        {viewingRelationship.is_default ? (
                          <Star className="h-4 w-4 text-yellow-500" />
                        ) : (
                          <StarOff className="h-4 w-4 text-slate-500" />
                        )}
                        <Badge
                          variant={
                            viewingRelationship.is_default
                              ? 'default'
                              : 'secondary'
                          }
                          className={
                            viewingRelationship.is_default
                              ? 'bg-yellow-600 text-white'
                              : 'bg-slate-600 text-slate-200'
                          }
                        >
                          {viewingRelationship.is_default ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-slate-400">Active Status</span>
                      <Badge
                        variant={
                          viewingRelationship.is_active
                            ? 'default'
                            : 'secondary'
                        }
                        className={
                          viewingRelationship.is_active
                            ? 'bg-green-600 text-white'
                            : 'bg-red-600 text-white'
                        }
                      >
                        {viewingRelationship.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <AlertCircle className="h-5 w-5 text-slate-500" />
                      Timestamps
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {viewingRelationship.created_at && (
                      <div>
                        <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Created
                        </label>
                        <p className="text-slate-200">
                          {format(
                            new Date(viewingRelationship.created_at),
                            'PPp'
                          )}
                        </p>
                        <p className="text-xs text-slate-400">
                          {formatDistanceToNow(
                            new Date(viewingRelationship.created_at),
                            { addSuffix: true }
                          )}
                        </p>
                      </div>
                    )}
                    {viewingRelationship.updated_at && (
                      <div>
                        <label className="text-xs font-medium text-slate-400 uppercase tracking-wider">
                          Last Updated
                        </label>
                        <p className="text-slate-200">
                          {format(
                            new Date(viewingRelationship.updated_at),
                            'PPp'
                          )}
                        </p>
                        <p className="text-xs text-slate-400">
                          {formatDistanceToNow(
                            new Date(viewingRelationship.updated_at),
                            { addSuffix: true }
                          )}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Notes */}
              {viewingRelationship.notes && (
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-slate-200 flex items-center gap-2 text-lg">
                      <AlertCircle className="h-5 w-5 text-slate-400" />
                      Notes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-200 whitespace-pre-wrap">
                      {viewingRelationship.notes}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-slate-600">
                <Button
                  variant="outline"
                  onClick={() => setViewingRelationship(null)}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setEditingRelationship(viewingRelationship)
                    setViewingRelationship(null)
                  }}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Relationship
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Selected Relationships
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete {selectedCount} selected
              relationship
              {selectedCount !== 1 ? 's' : ''}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowBulkDeleteDialog(false)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Relationships
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
