'use client'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Search, Filter, X } from 'lucide-react'
import type { UserFilterState } from '@/app/(dashboard)/admin/users/page'

const roles = [
  { value: 'all', label: 'All Roles' },
  { value: 'admin', label: 'Admin' },
  { value: 'cs', label: 'Customer Service' },
  { value: 'account', label: 'Account Manager' },
  { value: 'customer', label: 'Customer' },
  { value: 'carrier', label: 'Carrier' },
  { value: 'driver', label: 'Driver' },
  { value: 'factory', label: 'Factory' },
  { value: 'shipper', label: 'Shipper' },
  { value: 'consignee', label: 'Consignee' },
  { value: 'notify_party', label: 'Notify Party' },
  { value: 'forwarder_agent', label: 'Forwarder Agent' },
]

const statuses = [
  { value: 'all', label: 'All Status' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
]

interface UserFiltersProps {
  filters: UserFilterState
  onFiltersChange: (filters: UserFilterState) => void
}

export function UserFilters({ filters, onFiltersChange }: UserFiltersProps) {
  const { searchTerm, selectedRole, selectedStatus } = filters

  const handleClearFilters = () => {
    onFiltersChange({
      searchTerm: '',
      selectedRole: 'all',
      selectedStatus: 'all',
    })
  }

  const updateFilter = (key: keyof UserFilterState, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    })
  }

  const hasActiveFilters =
    searchTerm || selectedRole !== 'all' || selectedStatus !== 'all'

  return (
    <div className="bg-slate-800 rounded-lg p-6 space-y-4">
      <div className="flex items-center space-x-2 mb-4">
        <Filter className="h-5 w-5 text-orange-500" />
        <h3 className="text-lg font-medium text-white">Filters</h3>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearFilters}
            className="text-slate-400 hover:text-white ml-auto"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search Input */}
        <div className="space-y-2">
          <Label htmlFor="search" className="text-slate-300">
            Search Users
          </Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              id="search"
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={e => updateFilter('searchTerm', e.target.value)}
              className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
            />
          </div>
        </div>

        {/* Role Filter */}
        <div className="space-y-2">
          <Label className="text-slate-300">Role</Label>
          <Select
            value={selectedRole}
            onValueChange={value => updateFilter('selectedRole', value)}
          >
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {roles.map(role => (
                <SelectItem
                  key={role.value}
                  value={role.value}
                  className="text-slate-300 hover:bg-slate-700"
                >
                  {role.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <Label className="text-slate-300">Status</Label>
          <Select
            value={selectedStatus}
            onValueChange={value => updateFilter('selectedStatus', value)}
          >
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {statuses.map(status => (
                <SelectItem
                  key={status.value}
                  value={status.value}
                  className="text-slate-300 hover:bg-slate-700"
                >
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 pt-2">
          {searchTerm && (
            <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
              Search: "{searchTerm}"
              <button
                onClick={() => updateFilter('searchTerm', '')}
                className="ml-2 hover:text-orange-200"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          )}
          {selectedRole !== 'all' && (
            <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
              Role: {roles.find(r => r.value === selectedRole)?.label}
              <button
                onClick={() => updateFilter('selectedRole', 'all')}
                className="ml-2 hover:text-blue-200"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          )}
          {selectedStatus !== 'all' && (
            <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
              Status: {statuses.find(s => s.value === selectedStatus)?.label}
              <button
                onClick={() => updateFilter('selectedStatus', 'all')}
                className="ml-2 hover:text-green-200"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
