import { z } from 'zod'

// Currency enum based on database schema
export const currencySchema = z.enum(['THB', 'CNY', 'USD', 'EUR'])

// Packaging type enum based on database schema
export const packagingTypeSchema = z.enum(['Bag', 'Plastic Basket', 'Carton'])

// Base customer-product validation schema
export const customerProductBaseSchema = z.object({
  customer_id: z.string().uuid('Invalid customer ID'),
  product_id: z.string().uuid('Invalid product ID'),
  customer_product_code: z
    .string()
    .max(50, 'Customer product code must be 50 characters or less')
    .optional()
    .nullable(),
  is_default: z.boolean().optional().default(false),
  is_active: z.boolean().optional().default(true),
  unit_price_cif: z
    .number()
    .min(0, 'CIF price must be non-negative')
    .max(99999999.9999, 'CIF price too large')
    .optional()
    .nullable(),
  unit_price_fob: z
    .number()
    .min(0, 'FOB price must be non-negative')
    .max(99999999.9999, 'FOB price too large')
    .optional()
    .nullable(),
  currency_code: currencySchema.optional().default('USD'),
  standard_quantity: z
    .number()
    .min(0, 'Standard quantity must be non-negative')
    .max(99999999.99, 'Standard quantity too large')
    .optional()
    .nullable(),
  unit_of_measure_id: z
    .string()
    .uuid('Invalid unit of measure ID')
    .optional()
    .nullable(),
  gross_weight_per_package: z
    .number()
    .min(0, 'Gross weight must be non-negative')
    .max(9999.9999, 'Gross weight too large')
    .optional()
    .nullable(),
  net_weight_per_package: z
    .number()
    .min(0, 'Net weight must be non-negative')
    .max(9999.9999, 'Net weight too large')
    .optional()
    .nullable(),
  quality_grade: z
    .string()
    .max(50, 'Quality grade must be 50 characters or less')
    .optional()
    .nullable(),
  packaging_type: packagingTypeSchema,
  packaging_specifications: z.record(z.string(), z.any()).optional().nullable(),
  handling_instructions: z.string().optional().nullable(),
  temperature_require: z.string().optional().nullable(),
  vent_require: z.string().optional().nullable(),
  shelf_life_days: z
    .number()
    .int('Shelf life must be a whole number')
    .min(0, 'Shelf life must be non-negative')
    .optional()
    .nullable(),
  notes: z.string().optional().nullable(),
})

// Customer-product form schema with additional validation
export const customerProductFormSchema = customerProductBaseSchema
  .refine(
    data => {
      // At least one price must be provided
      return data.unit_price_cif !== null || data.unit_price_fob !== null
    },
    {
      message: 'At least one price (CIF or FOB) must be provided',
    }
  )
  .refine(
    data => {
      // Net weight should not exceed gross weight
      if (data.gross_weight_per_package && data.net_weight_per_package) {
        return data.net_weight_per_package <= data.gross_weight_per_package
      }
      return true
    },
    {
      message: 'Net weight cannot exceed gross weight',
    }
  )

// Update schema (all fields optional except id)
export const customerProductUpdateSchema = customerProductFormSchema
  .partial()
  .extend({
    id: z.string().uuid('Invalid customer-product ID'),
  })

// Search and filter schemas
export const customerProductFilterSchema = z.object({
  customer_id: z.string().uuid().optional(),
  product_id: z.string().uuid().optional(),
  category: z.string().optional(),
  currency_code: currencySchema.optional(),
  packaging_type: packagingTypeSchema.optional(),
  is_default: z.boolean().optional(),
  is_active: z.boolean().optional(),
  search: z.string().optional(),
})

// Bulk operations schemas
export const bulkDeleteCustomerProductsSchema = z.object({
  ids: z
    .array(z.string().uuid('Invalid customer-product ID'))
    .min(1, 'At least one customer-product relationship must be selected'),
})

// Bulk import schema
export const customerProductBulkImportSchema = z.object({
  customer_name: z.string().min(1, 'Customer name is required'),
  product_name: z.string().min(1, 'Product name is required'),
  customer_product_code: z.string().optional(),
  unit_price_cif: z.number().min(0).optional(),
  unit_price_fob: z.number().min(0).optional(),
  currency_code: currencySchema.optional().default('USD'),
  standard_quantity: z.number().min(0).optional(),
  gross_weight_per_package: z.number().min(0).optional(),
  net_weight_per_package: z.number().min(0).optional(),
  quality_grade: z.string().optional(),
  packaging_type: packagingTypeSchema,
  handling_instructions: z.string().optional(),
  temperature_require: z.string().optional(),
  vent_require: z.string().optional(),
  shelf_life_days: z.number().int().min(0).optional(),
  is_default: z.boolean().optional().default(false),
  notes: z.string().optional(),
})

// Default product management validation
export const setDefaultProductSchema = z.object({
  customer_id: z.string().uuid('Invalid customer ID'),
  customer_product_id: z.string().uuid('Invalid customer-product ID'),
})

// Type exports
export type CustomerProductForm = z.infer<typeof customerProductFormSchema>
export type CustomerProductUpdate = z.infer<typeof customerProductUpdateSchema>
export type CustomerProductFilter = z.infer<typeof customerProductFilterSchema>
export type CustomerProductBulkImport = z.infer<
  typeof customerProductBulkImportSchema
>
export type CurrencyCode = z.infer<typeof currencySchema>
export type PackagingType = z.infer<typeof packagingTypeSchema>
export type SetDefaultProduct = z.infer<typeof setDefaultProductSchema>

// Constants for UI
export const CURRENCY_OPTIONS = [
  { value: 'THB', label: 'THB (Thai Baht)', symbol: '฿' },
  { value: 'CNY', label: 'CNY (Chinese Yuan)', symbol: '¥' },
  { value: 'USD', label: 'USD (US Dollar)', symbol: '$' },
  { value: 'EUR', label: 'EUR (Euro)', symbol: '€' },
] as const

export const PACKAGING_TYPE_OPTIONS = [
  { value: 'Bag', label: 'Bag' },
  { value: 'Plastic Basket', label: 'Plastic Basket' },
  { value: 'Carton', label: 'Carton' },
] as const

export const QUALITY_GRADES = [
  'Premium',
  'Grade A',
  'Grade B',
  'Grade C',
  'Standard',
  'Export Quality',
  'Organic',
  'Fair Trade',
] as const

// Validation helpers
export const validatePricing = (
  cifPrice?: number | null,
  fobPrice?: number | null
): boolean => {
  return (
    (cifPrice !== null && cifPrice !== undefined) ||
    (fobPrice !== null && fobPrice !== undefined)
  )
}

export const validateWeights = (
  grossWeight?: number | null,
  netWeight?: number | null
): boolean => {
  if (grossWeight && netWeight) {
    return netWeight <= grossWeight
  }
  return true
}

export const formatCurrency = (
  amount: number,
  currency: CurrencyCode
): string => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 4,
  })
  return formatter.format(amount)
}

export const formatWeight = (weight: number, unit: string = 'KG'): string => {
  return `${weight.toFixed(4)} ${unit}`
}

// Default values for form initialization
export const DEFAULT_CUSTOMER_PRODUCT = {
  customer_id: '',
  product_id: '',
  customer_product_code: null,
  is_default: false,
  is_active: true,
  unit_price_cif: null,
  unit_price_fob: null,
  currency_code: 'USD' as CurrencyCode,
  standard_quantity: null,
  unit_of_measure_id: null,
  gross_weight_per_package: null,
  net_weight_per_package: null,
  quality_grade: null,
  packaging_type: 'Bag' as PackagingType,
  packaging_specifications: null,
  handling_instructions: null,
  temperature_require: null,
  vent_require: null,
  shelf_life_days: null,
  notes: null,
} as const
