import { describe, it, expect } from 'vitest'
import { cn } from '@/lib/utils'

describe('utils', () => {
  describe('cn function', () => {
    it('should merge class names correctly', () => {
      const result = cn('px-4', 'py-2', 'bg-blue-500')
      expect(result).toBe('px-4 py-2 bg-blue-500')
    })

    it('should handle conditional class names', () => {
      const result = cn('px-4', false && 'py-2', 'bg-blue-500')
      expect(result).toBe('px-4 bg-blue-500')
    })

    it('should handle undefined and null values', () => {
      const result = cn('px-4', undefined, null, 'bg-blue-500')
      expect(result).toBe('px-4 bg-blue-500')
    })
  })
})
