import { test, expect } from '@playwright/test'

test.describe('Ports Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to ports page (assuming user is already authenticated)
    await page.goto('/master-data/ports')
    
    // Wait for page to load
    await page.waitForSelector('[data-testid="ports-table"]', { timeout: 10000 })
  })

  test('should display ports page correctly', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Ports')
    
    // Check description
    await expect(page.locator('p')).toContainText('Manage port locations and shipping routes')
    
    // Check Add Port button exists
    await expect(page.locator('button', { hasText: 'Add Port' })).toBeVisible()
    
    // Check filters section
    await expect(page.locator('h3', { hasText: 'Filters' })).toBeVisible()
    
    // Check search input
    await expect(page.locator('input[placeholder*="Search"]')).toBeVisible()
  })

  test('should filter ports by search term', async ({ page }) => {
    // Type in search box
    await page.fill('input[placeholder*="Search"]', 'Bangkok')
    
    // Wait for debounced search
    await page.waitForTimeout(500)
    
    // Check that search filter appears
    await expect(page.locator('[data-testid="search-filter"]')).toContainText('Bangkok')
    
    // Clear search
    await page.click('button[aria-label="Clear search"]')
    
    // Check that filter is removed
    await expect(page.locator('[data-testid="search-filter"]')).not.toBeVisible()
  })

  test('should filter ports by port type', async ({ page }) => {
    // Open port type dropdown
    await page.click('[data-testid="port-type-filter"]')
    
    // Select Origin
    await page.click('text=Origin Port')
    
    // Check that filter appears
    await expect(page.locator('[data-testid="port-type-filter-tag"]')).toContainText('Origin')
    
    // Clear filter
    await page.click('[data-testid="port-type-filter-tag"] button')
    
    // Check that filter is removed
    await expect(page.locator('[data-testid="port-type-filter-tag"]')).not.toBeVisible()
  })

  test('should filter ports by country', async ({ page }) => {
    // Open country dropdown
    await page.click('[data-testid="country-filter"]')
    
    // Select Thailand
    await page.click('text=Thailand')
    
    // Check that filter appears
    await expect(page.locator('[data-testid="country-filter-tag"]')).toContainText('Thailand')
    
    // Clear filter
    await page.click('[data-testid="country-filter-tag"] button')
    
    // Check that filter is removed
    await expect(page.locator('[data-testid="country-filter-tag"]')).not.toBeVisible()
  })

  test('should sort ports by different columns', async ({ page }) => {
    // Sort by code
    await page.click('th button:has-text("Code")')
    
    // Check that sort indicator appears
    await expect(page.locator('th button:has-text("Code") svg')).toBeVisible()
    
    // Sort by name
    await page.click('th button:has-text("Name")')
    
    // Check that sort indicator appears
    await expect(page.locator('th button:has-text("Name") svg')).toBeVisible()
    
    // Sort by port type
    await page.click('th button:has-text("Type")')
    
    // Check that sort indicator appears
    await expect(page.locator('th button:has-text("Type") svg')).toBeVisible()
  })

  test('should create a new port', async ({ page }) => {
    // Click Add Port button
    await page.click('button:has-text("Add Port")')
    
    // Check that dialog opens
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    await expect(page.locator('h2', { hasText: 'Create Port' })).toBeVisible()
    
    // Fill in port details
    await page.fill('input[name="code"]', 'TESTPT')
    await page.fill('input[name="name"]', 'Test Port')
    await page.fill('input[name="city"]', 'Test City')
    await page.fill('input[name="country"]', 'Test Country')
    
    // Select port type
    await page.click('[data-testid="port-type-select"]')
    await page.click('text=Origin Port')
    
    // Fill in timezone
    await page.fill('input[name="timezone"]', 'Asia/Bangkok')
    
    // Add GPS coordinates
    await page.fill('input[placeholder*="latitude"]', '13.7563')
    await page.fill('input[placeholder*="longitude"]', '100.5018')
    
    // Submit form
    await page.click('button:has-text("Create Port")')
    
    // Check that dialog closes and success message appears
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    
    // Check that new port appears in table (if visible on current page)
    await expect(page.locator('td:has-text("TESTPT")')).toBeVisible({ timeout: 5000 })
  })

  test('should validate port form fields', async ({ page }) => {
    // Click Add Port button
    await page.click('button:has-text("Add Port")')
    
    // Try to submit empty form
    await page.click('button:has-text("Create Port")')
    
    // Check for validation errors
    await expect(page.locator('text=Port code is required')).toBeVisible()
    await expect(page.locator('text=Port name is required')).toBeVisible()
    await expect(page.locator('text=City is required')).toBeVisible()
    await expect(page.locator('text=Country is required')).toBeVisible()
    
    // Test invalid port code
    await page.fill('input[name="code"]', 'invalid')
    await page.blur('input[name="code"]')
    
    // Check for validation error
    await expect(page.locator('text=2 uppercase letters')).toBeVisible()
    
    // Test invalid coordinates
    await page.fill('input[placeholder*="latitude"]', '95') // > 90
    await page.blur('input[placeholder*="latitude"]')
    
    // Check for validation error
    await expect(page.locator('text=between -90 and 90')).toBeVisible()
    
    // Cancel form
    await page.click('button:has-text("Cancel")')
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
  })

  test('should view port details', async ({ page }) => {
    // Find first port row and click view button
    await page.click('tbody tr:first-child button[aria-label="View port"]')
    
    // Check that view dialog opens
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    await expect(page.locator('h2', { hasText: 'Port Details' })).toBeVisible()
    
    // Check that port information is displayed
    await expect(page.locator('label:has-text("Port Code")')).toBeVisible()
    await expect(page.locator('label:has-text("Port Name")')).toBeVisible()
    await expect(page.locator('label:has-text("City")')).toBeVisible()
    await expect(page.locator('label:has-text("Country")')).toBeVisible()
    await expect(page.locator('label:has-text("Port Type")')).toBeVisible()
    await expect(page.locator('label:has-text("GPS Coordinates")')).toBeVisible()
    
    // Close dialog
    await page.press('[role="dialog"]', 'Escape')
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
  })

  test('should edit an existing port', async ({ page }) => {
    // Find first port row and click edit button
    await page.click('tbody tr:first-child button[aria-label="Edit port"]')
    
    // Check that edit dialog opens
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    await expect(page.locator('h2', { hasText: 'Edit Port' })).toBeVisible()
    
    // Modify port name
    await page.fill('input[name="name"]', 'Updated Port Name')
    
    // Submit form
    await page.click('button:has-text("Update Port")')
    
    // Check that dialog closes
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    
    // Check that updated name appears in table
    await expect(page.locator('td:has-text("Updated Port Name")')).toBeVisible({ timeout: 5000 })
  })

  test('should delete a port', async ({ page }) => {
    // Find first port row and click delete button
    await page.click('tbody tr:first-child button[aria-label="Delete port"]')
    
    // Check that delete confirmation dialog opens
    await expect(page.locator('[role="alertdialog"]')).toBeVisible()
    await expect(page.locator('h2', { hasText: 'Delete Port' })).toBeVisible()
    
    // Confirm deletion
    await page.click('button:has-text("Delete Port")')
    
    // Check that dialog closes
    await expect(page.locator('[role="alertdialog"]')).not.toBeVisible()
    
    // Check that port is removed from table or marked as inactive
    // (depending on implementation - soft delete vs hard delete)
  })

  test('should handle bulk operations', async ({ page }) => {
    // Select multiple ports
    await page.check('thead input[type="checkbox"]') // Select all
    
    // Check that bulk actions appear
    await expect(page.locator('text=selected')).toBeVisible()
    await expect(page.locator('button:has-text("Delete Selected")')).toBeVisible()
    
    // Clear selection
    await page.click('button:has-text("Clear Selection")')
    
    // Check that bulk actions disappear
    await expect(page.locator('text=selected')).not.toBeVisible()
    
    // Select individual ports
    await page.check('tbody tr:first-child input[type="checkbox"]')
    await page.check('tbody tr:nth-child(2) input[type="checkbox"]')
    
    // Check that bulk actions appear with correct count
    await expect(page.locator('text=2 ports selected')).toBeVisible()
    
    // Click bulk delete
    await page.click('button:has-text("Delete Selected")')
    
    // Check that confirmation dialog opens
    await expect(page.locator('[role="alertdialog"]')).toBeVisible()
    await expect(page.locator('text=delete 2 selected')).toBeVisible()
    
    // Cancel bulk delete
    await page.click('button:has-text("Cancel")')
    await expect(page.locator('[role="alertdialog"]')).not.toBeVisible()
  })

  test('should handle pagination', async ({ page }) => {
    // Check if pagination is present (only if there are multiple pages)
    const paginationExists = await page.locator('[data-testid="pagination"]').isVisible()
    
    if (paginationExists) {
      // Test next page
      await page.click('button[aria-label="Next page"]')
      
      // Check that page number changes
      await expect(page.locator('text=Page 2')).toBeVisible()
      
      // Test previous page
      await page.click('button[aria-label="Previous page"]')
      
      // Check that page number changes back
      await expect(page.locator('text=Page 1')).toBeVisible()
      
      // Test first page button
      await page.click('button[aria-label="First page"]')
      await expect(page.locator('text=Page 1')).toBeVisible()
      
      // Test last page button
      await page.click('button[aria-label="Last page"]')
      // Page number will depend on total pages
    }
  })

  test('should refresh ports data', async ({ page }) => {
    // Click refresh button
    await page.click('button:has-text("Refresh")')
    
    // Check that loading indicator appears briefly
    await expect(page.locator('[data-testid="loading-indicator"]')).toBeVisible({ timeout: 1000 })
    
    // Check that loading indicator disappears
    await expect(page.locator('[data-testid="loading-indicator"]')).not.toBeVisible({ timeout: 5000 })
  })

  test('should handle GPS coordinate input modes', async ({ page }) => {
    // Click Add Port button
    await page.click('button:has-text("Add Port")')
    
    // Check that separate input mode is default
    await expect(page.locator('input[placeholder*="latitude"]')).toBeVisible()
    await expect(page.locator('input[placeholder*="longitude"]')).toBeVisible()
    
    // Switch to combined input mode
    await page.click('button:has-text("Combined")')
    
    // Check that combined input appears
    await expect(page.locator('input[placeholder*="13.7563, 100.5018"]')).toBeVisible()
    
    // Test combined input
    await page.fill('input[placeholder*="13.7563, 100.5018"]', '13.7563, 100.5018')
    
    // Check that coordinates are parsed correctly
    await expect(page.locator('text=Valid Coordinates')).toBeVisible()
    
    // Test current location button (if geolocation is available)
    await page.click('button:has-text("Current Location")')
    
    // Note: This might fail in headless mode without geolocation
    // await expect(page.locator('text=Valid Coordinates')).toBeVisible({ timeout: 10000 })
    
    // Clear coordinates
    await page.click('button:has-text("Clear")')
    
    // Check that coordinates are cleared
    await expect(page.locator('text=Valid Coordinates')).not.toBeVisible()
    
    // Cancel form
    await page.click('button:has-text("Cancel")')
  })

  test('should show proper error handling', async ({ page }) => {
    // This test would require mocking API failures
    // For now, we'll test UI error states
    
    // Test form validation errors
    await page.click('button:has-text("Add Port")')
    await page.fill('input[name="code"]', 'A') // Too short
    await page.blur('input[name="code"]')
    
    // Check that error message appears
    await expect(page.locator('text=at least 3 characters')).toBeVisible()
    
    // Test invalid timezone format
    await page.fill('input[name="timezone"]', 'invalid-timezone')
    await page.blur('input[name="timezone"]')
    
    // Check that error message appears
    await expect(page.locator('text=Continent/City')).toBeVisible()
    
    // Cancel form
    await page.click('button:has-text("Cancel")')
  })

  test('should maintain filter state across navigation', async ({ page }) => {
    // Apply filters
    await page.fill('input[placeholder*="Search"]', 'Bangkok')
    await page.waitForTimeout(500)
    
    // Check that filter is applied
    await expect(page.locator('[data-testid="search-filter"]')).toContainText('Bangkok')
    
    // Navigate away and back (would require navigation setup)
    // For now, just refresh the page
    await page.reload()
    
    // Check that filters are cleared on refresh (expected behavior)
    await expect(page.locator('[data-testid="search-filter"]')).not.toBeVisible()
  })
})