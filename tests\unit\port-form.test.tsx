import { describe, expect, it, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { PortForm } from '@/components/forms/port-form/port-form'
import type { Port } from '@/stores/port-store'
import type { PortForm as PortFormData } from '@/lib/validations/ports'

// Mock the hooks
vi.mock('@/hooks/use-ports', () => ({
  usePortValidation: () => ({
    validateUniqueCode: vi.fn(() => ({ isValid: true })),
    validatePortCode: vi.fn(() => ({ isValid: true })),
    validateTimezone: vi.fn(() => ({ isValid: true })),
  }),
  usePortCategories: () => ({
    portTypeOptions: [
      { value: 'origin', label: 'Origin Port' },
      { value: 'destination', label: 'Destination Port' },
      { value: 'transit', label: 'Transit Port' },
    ],
    countryOptions: [
      { value: 'Thailand', label: 'Thailand' },
      { value: 'China', label: 'China' },
    ],
  }),
  useCoordinateUtils: () => ({
    parseCoordinatesFromString: vi.fn((str: string) => {
      const parts = str.split(',').map(s => s.trim())
      if (parts.length === 2) {
        const lat = parseFloat(parts[0])
        const lng = parseFloat(parts[1])
        if (!isNaN(lat) && !isNaN(lng)) {
          return { lat, lng }
        }
      }
      return null
    }),
  }),
}))

// Mock GPS Coordinate Input component
vi.mock('@/components/forms/port-form/gps-coordinate-input', () => ({
  GPSCoordinateInput: ({ value, onChange, className }: any) => (
    <div data-testid="gps-coordinate-input" className={className}>
      <input
        data-testid="lat-input"
        type="number"
        value={value?.lat || ''}
        onChange={(e) => {
          const lat = parseFloat(e.target.value)
          if (value) {
            onChange({ ...value, lat })
          } else {
            onChange({ lat, lng: 0 })
          }
        }}
        placeholder="Latitude"
      />
      <input
        data-testid="lng-input"
        type="number"
        value={value?.lng || ''}
        onChange={(e) => {
          const lng = parseFloat(e.target.value)
          if (value) {
            onChange({ ...value, lng })
          } else {
            onChange({ lat: 0, lng })
          }
        }}
        placeholder="Longitude"
      />
    </div>
  ),
}))

describe('PortForm', () => {
  const mockOnSubmit = vi.fn()
  const mockOnCancel = vi.fn()

  const defaultProps = {
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
    isLoading: false,
  }

  const mockPort: Port = {
    id: '1',
    code: 'THBKK',
    name: 'Bangkok Port',
    city: 'Bangkok',
    country: 'Thailand',
    port_type: 'origin',
    gps_coordinates: 'POINT(100.5018 13.7563)',
    timezone: 'Asia/Bangkok',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Rendering', () => {
    it('should render create form correctly', () => {
      render(<PortForm {...defaultProps} />)

      expect(screen.getByLabelText(/port code/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/port name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/city/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/country/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/port type/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/timezone/i)).toBeInTheDocument()
      expect(screen.getByTestId('gps-coordinate-input')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /create port/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument()
    })

    it('should render edit form correctly', () => {
      render(<PortForm {...defaultProps} port={mockPort} />)

      expect(screen.getByDisplayValue('THBKK')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Bangkok Port')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Bangkok')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Thailand')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Asia/Bangkok')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /update port/i })).toBeInTheDocument()
    })

    it('should show loading state', () => {
      render(<PortForm {...defaultProps} isLoading={true} />)

      const submitButton = screen.getByRole('button', { name: /create port/i })
      expect(submitButton).toBeDisabled()
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })

    it('should parse existing coordinates correctly', () => {
      render(<PortForm {...defaultProps} port={mockPort} />)

      // The GPS coordinate input should show the parsed coordinates
      expect(screen.getByDisplayValue('13.7563')).toBeInTheDocument()
      expect(screen.getByDisplayValue('100.5018')).toBeInTheDocument()
    })
  })

  describe('Form Validation', () => {
    it('should show validation errors for required fields', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const submitButton = screen.getByRole('button', { name: /create port/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/port code.*required/i)).toBeInTheDocument()
        expect(screen.getByText(/port name.*required/i)).toBeInTheDocument()
        expect(screen.getByText(/city.*required/i)).toBeInTheDocument()
        expect(screen.getByText(/country.*required/i)).toBeInTheDocument()
      })
    })

    it('should validate port code format', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const codeInput = screen.getByLabelText(/port code/i)
      await user.type(codeInput, 'invalid')
      await user.tab() // Trigger blur

      await waitFor(() => {
        expect(screen.getByText(/2 uppercase letters/i)).toBeInTheDocument()
      })
    })

    it('should auto-convert code to uppercase', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const codeInput = screen.getByLabelText(/port code/i)
      await user.type(codeInput, 'thbkk')

      expect(codeInput).toHaveValue('THBKK')
    })

    it('should validate timezone format', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const timezoneInput = screen.getByLabelText(/timezone/i)
      await user.type(timezoneInput, 'GMT+7')
      await user.tab() // Trigger blur

      await waitFor(() => {
        expect(screen.getByText(/continent\/city/i)).toBeInTheDocument()
      })
    })
  })

  describe('Auto-suggestions', () => {
    it('should auto-suggest timezone based on country', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const countryInput = screen.getByLabelText(/country/i)
      await user.type(countryInput, 'Thailand')
      await user.tab()

      await waitFor(() => {
        const timezoneInput = screen.getByLabelText(/timezone/i)
        expect(timezoneInput).toHaveValue('Asia/Bangkok')
      })
    })

    it('should not override existing timezone', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const timezoneInput = screen.getByLabelText(/timezone/i)
      await user.type(timezoneInput, 'Asia/Shanghai')

      const countryInput = screen.getByLabelText(/country/i)
      await user.type(countryInput, 'Thailand')
      await user.tab()

      // Should not override the manually entered timezone
      expect(timezoneInput).toHaveValue('Asia/Shanghai')
    })
  })

  describe('GPS Coordinates', () => {
    it('should handle coordinate input', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const latInput = screen.getByTestId('lat-input')
      const lngInput = screen.getByTestId('lng-input')

      await user.type(latInput, '13.7563')
      await user.type(lngInput, '100.5018')

      expect(latInput).toHaveValue(13.7563)
      expect(lngInput).toHaveValue(100.5018)
    })

    it('should show coordinate preview', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const latInput = screen.getByTestId('lat-input')
      const lngInput = screen.getByTestId('lng-input')

      await user.type(latInput, '13.7563')
      await user.type(lngInput, '100.5018')

      await waitFor(() => {
        expect(screen.getByText(/coordinate preview/i)).toBeInTheDocument()
        expect(screen.getByText(/13.756300, 100.501800/)).toBeInTheDocument()
        expect(screen.getByText(/POINT\(100.5018 13.7563\)/)).toBeInTheDocument()
      })
    })
  })

  describe('Form Submission', () => {
    it('should submit valid form data', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      // Fill in required fields
      await user.type(screen.getByLabelText(/port code/i), 'THBKK')
      await user.type(screen.getByLabelText(/port name/i), 'Bangkok Port')
      await user.type(screen.getByLabelText(/city/i), 'Bangkok')
      await user.type(screen.getByLabelText(/country/i), 'Thailand')

      // Select port type
      await user.click(screen.getByLabelText(/port type/i))
      await user.click(screen.getByText('Origin Port'))

      // Add coordinates
      await user.type(screen.getByTestId('lat-input'), '13.7563')
      await user.type(screen.getByTestId('lng-input'), '100.5018')

      // Submit form
      await user.click(screen.getByRole('button', { name: /create port/i }))

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            code: 'THBKK',
            name: 'Bangkok Port',
            city: 'Bangkok',
            country: 'Thailand',
            port_type: 'origin',
            gps_coordinates: JSON.stringify({ lat: 13.7563, lng: 100.5018 }),
          })
        )
      })
    })

    it('should handle submission errors', async () => {
      const user = userEvent.setup()
      const mockOnSubmitWithError = vi.fn().mockRejectedValue(new Error('Submission failed'))
      
      render(<PortForm {...defaultProps} onSubmit={mockOnSubmitWithError} />)

      // Fill in required fields
      await user.type(screen.getByLabelText(/port code/i), 'THBKK')
      await user.type(screen.getByLabelText(/port name/i), 'Bangkok Port')
      await user.type(screen.getByLabelText(/city/i), 'Bangkok')
      await user.type(screen.getByLabelText(/country/i), 'Thailand')

      // Submit form
      await user.click(screen.getByRole('button', { name: /create port/i }))

      await waitFor(() => {
        expect(screen.getByText(/submission failed/i)).toBeInTheDocument()
      })
    })

    it('should transform coordinates for submission', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      // Fill in required fields
      await user.type(screen.getByLabelText(/port code/i), 'THBKK')
      await user.type(screen.getByLabelText(/port name/i), 'Bangkok Port')
      await user.type(screen.getByLabelText(/city/i), 'Bangkok')
      await user.type(screen.getByLabelText(/country/i), 'Thailand')

      // Add coordinates
      await user.type(screen.getByTestId('lat-input'), '13.7563')
      await user.type(screen.getByTestId('lng-input'), '100.5018')

      // Submit form
      await user.click(screen.getByRole('button', { name: /create port/i }))

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            gps_coordinates: JSON.stringify({ lat: 13.7563, lng: 100.5018 }),
          })
        )
      })
    })
  })

  describe('Form Actions', () => {
    it('should handle cancel action', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      await user.click(screen.getByRole('button', { name: /cancel/i }))

      expect(mockOnCancel).toHaveBeenCalled()
    })

    it('should reset form on cancel', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      // Fill in some data
      await user.type(screen.getByLabelText(/port code/i), 'THBKK')
      await user.type(screen.getByLabelText(/port name/i), 'Bangkok Port')

      // Cancel
      await user.click(screen.getByRole('button', { name: /cancel/i }))

      // Form should be reset
      expect(screen.getByLabelText(/port code/i)).toHaveValue('')
      expect(screen.getByLabelText(/port name/i)).toHaveValue('')
    })

    it('should not show cancel button when onCancel is not provided', () => {
      const { onCancel, ...propsWithoutCancel } = defaultProps
      render(<PortForm {...propsWithoutCancel} />)

      expect(screen.queryByRole('button', { name: /cancel/i })).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper form labels', () => {
      render(<PortForm {...defaultProps} />)

      expect(screen.getByLabelText(/port code/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/port name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/city/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/country/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/port type/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/timezone/i)).toBeInTheDocument()
    })

    it('should have proper form descriptions', () => {
      render(<PortForm {...defaultProps} />)

      expect(screen.getByText(/unique port code/i)).toBeInTheDocument()
      expect(screen.getByText(/continent\/city/i)).toBeInTheDocument()
      expect(screen.getByText(/gps coordinates.*geographic search/i)).toBeInTheDocument()
    })

    it('should associate error messages with form fields', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      const submitButton = screen.getByRole('button', { name: /create port/i })
      await user.click(submitButton)

      await waitFor(() => {
        const codeInput = screen.getByLabelText(/port code/i)
        const errorMessage = screen.getByText(/port code.*required/i)
        
        expect(codeInput).toHaveAttribute('aria-describedby')
        expect(errorMessage).toHaveAttribute('id', expect.stringContaining('error'))
      })
    })
  })

  describe('Port Type Options', () => {
    it('should render all port type options', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      await user.click(screen.getByLabelText(/port type/i))

      expect(screen.getByText('Origin Port')).toBeInTheDocument()
      expect(screen.getByText('Destination Port')).toBeInTheDocument()
      expect(screen.getByText('Transit Port')).toBeInTheDocument()
    })

    it('should select port type correctly', async () => {
      const user = userEvent.setup()
      render(<PortForm {...defaultProps} />)

      await user.click(screen.getByLabelText(/port type/i))
      await user.click(screen.getByText('Destination Port'))

      // The select value should be updated (this depends on the Select component implementation)
      expect(screen.getByText('Destination Port')).toBeInTheDocument()
    })
  })
})