import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { AdminUserService, adminUserService } from '@/lib/services/admin-user-service'
import { UserActivationDialog } from '@/components/admin/user-activation-dialog'
import type { UserProfile } from '@/lib/supabase/auth'

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    getUser: vi.fn(),
  },
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn(),
        order: vi.fn(),
        limit: vi.fn(),
      })),
      or: vi.fn(() => ({
        order: vi.fn(() => ({
          limit: vi.fn(),
        })),
      })),
      order: vi.fn(),
      limit: vi.fn(),
    })),
    update: vi.fn(() => ({
      eq: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
    })),
    delete: vi.fn(() => ({
      eq: vi.fn(),
    })),
    insert: vi.fn(),
  })),
}

vi.mock('@/lib/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

// Mock AuthErrorDisplay component
vi.mock('@/components/auth/auth-error-display', () => ({
  AuthErrorDisplay: ({ error, onRetry }: any) => 
    error ? (
      <div data-testid="auth-error-display">
        <span>{error.message}</span>
        {onRetry && <button onClick={onRetry} data-testid="retry-button">Retry</button>}
      </div>
    ) : null,
}))

describe('Admin User Management Interface Tests', () => {
  const mockUserProfile: UserProfile = {
    user_id: 'user-123',
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User',
    phone_number: '+1234567890',
    line_id: null,
    wechat_id: null,
    role: 'customer',
    company_id: 'company-123',
    is_active: true,
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
  }

  const mockAdminProfile: UserProfile = {
    ...mockUserProfile,
    user_id: 'admin-123',
    email: '<EMAIL>',
    role: 'admin',
    company_id: null,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2023-01-01T00:00:00.000Z'))
    
    // Default auth user mock
    mockSupabaseClient.auth.getUser.mockResolvedValue({
      data: { user: { id: 'current-admin' } },
      error: null,
    })
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('AdminUserService', () => {
    describe('User Activation', () => {
      it('should successfully activate a user', async () => {
        const activatedUser = { ...mockUserProfile, is_active: true }
        
        mockSupabaseClient.from().update().eq().select().single.mockResolvedValue({
          data: activatedUser,
          error: null,
        })

        const service = new AdminUserService()
        const result = await service.activateUser('user-123', 'Account verification completed')

        expect(result).toEqual(activatedUser)
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('profiles')
        expect(mockSupabaseClient.from().update).toHaveBeenCalledWith({
          is_active: true,
          updated_at: '2023-01-01T00:00:00.000Z',
        })
      })

      it('should handle activation errors', async () => {
        mockSupabaseClient.from().update().eq().select().single.mockResolvedValue({
          data: null,
          error: { code: 'PGRST301', message: 'Row not found' },
        })

        const service = new AdminUserService()
        
        await expect(service.activateUser('non-existent-user'))
          .rejects.toMatchObject({
            code: 'USER_NOT_FOUND',
            message: 'User not found.',
          })
      })
    })

    describe('User Deactivation', () => {
      it('should successfully deactivate a non-admin user', async () => {
        const deactivatedUser = { ...mockUserProfile, is_active: false }
        
        // Mock user exists check
        mockSupabaseClient.from().select().eq().single.mockResolvedValueOnce({
          data: { user_id: 'user-123', role: 'customer', is_active: true },
          error: null,
        })

        // Mock deactivation update
        mockSupabaseClient.from().update().eq().select().single.mockResolvedValue({
          data: deactivatedUser,
          error: null,
        })

        const service = new AdminUserService()
        const result = await service.deactivateUser('user-123', 'Account suspended for violations')

        expect(result).toEqual(deactivatedUser)
        expect(mockSupabaseClient.from().update).toHaveBeenCalledWith({
          is_active: false,
          updated_at: '2023-01-01T00:00:00.000Z',
        })
      })

      it('should prevent deactivating the last admin', async () => {
        // Mock user exists check - admin user
        mockSupabaseClient.from().select().eq().single.mockResolvedValueOnce({
          data: { user_id: 'admin-123', role: 'admin', is_active: true },
          error: null,
        })

        // Mock admin count check - only 1 admin
        mockSupabaseClient.from().select.mockReturnValueOnce({
          eq: vi.fn(() => ({
            eq: vi.fn(() => Promise.resolve({
              data: [{ user_id: 'admin-123' }], // Only one admin
              error: null,
            })),
          })),
        })

        const service = new AdminUserService()
        
        await expect(service.deactivateUser('admin-123'))
          .rejects.toMatchObject({
            code: 'CANNOT_DEACTIVATE_LAST_ADMIN',
            message: 'Cannot deactivate the last active admin user.',
          })
      })

      it('should allow deactivating admin when multiple admins exist', async () => {
        const deactivatedAdmin = { ...mockAdminProfile, is_active: false }
        
        // Mock user exists check
        mockSupabaseClient.from().select().eq().single.mockResolvedValueOnce({
          data: { user_id: 'admin-123', role: 'admin', is_active: true },
          error: null,
        })

        // Mock admin count check - multiple admins
        mockSupabaseClient.from().select.mockReturnValueOnce({
          eq: vi.fn(() => ({
            eq: vi.fn(() => Promise.resolve({
              data: [
                { user_id: 'admin-123' },
                { user_id: 'admin-456' }
              ], // Multiple admins
              error: null,
            })),
          })),
        })

        // Mock deactivation update
        mockSupabaseClient.from().update().eq().select().single.mockResolvedValue({
          data: deactivatedAdmin,
          error: null,
        })

        const service = new AdminUserService()
        const result = await service.deactivateUser('admin-123', 'Admin role transfer')

        expect(result).toEqual(deactivatedAdmin)
      })
    })

    describe('User Deletion', () => {
      it('should successfully delete a non-admin user', async () => {
        // Mock user exists check
        mockSupabaseClient.from().select().eq().single.mockResolvedValue({
          data: { user_id: 'user-123', role: 'customer' },
          error: null,
        })

        // Mock deletion
        mockSupabaseClient.from().delete().eq.mockResolvedValue({
          data: null,
          error: null,
        })

        const service = new AdminUserService()
        await service.deleteUser('user-123', 'Account closure requested')

        expect(mockSupabaseClient.from().delete).toHaveBeenCalled()
      })

      it('should prevent deleting the last admin', async () => {
        // Mock user exists check - admin user
        mockSupabaseClient.from().select().eq().single.mockResolvedValue({
          data: { user_id: 'admin-123', role: 'admin' },
          error: null,
        })

        // Mock admin count check - only 1 admin
        mockSupabaseClient.from().select.mockReturnValueOnce({
          eq: vi.fn(() => ({
            eq: vi.fn(() => Promise.resolve({
              data: [{ user_id: 'admin-123' }],
              error: null,
            })),
          })),
        })

        const service = new AdminUserService()
        
        await expect(service.deleteUser('admin-123'))
          .rejects.toMatchObject({
            code: 'CANNOT_DELETE_LAST_ADMIN',
            message: 'Cannot delete the last admin user.',
          })
      })
    })

    describe('User Search and Retrieval', () => {
      it('should search users by query', async () => {
        const searchResults = [mockUserProfile, mockAdminProfile]
        
        mockSupabaseClient.from().select().or().order().limit.mockResolvedValue({
          data: searchResults,
          error: null,
        })

        const service = new AdminUserService()
        const result = await service.searchUsers('test')

        expect(result).toEqual(searchResults)
        expect(mockSupabaseClient.from().select().or).toHaveBeenCalledWith(
          'email.ilike.%test%,first_name.ilike.%test%,last_name.ilike.%test%'
        )
      })

      it('should get users by activation status', async () => {
        const activeUsers = [mockUserProfile]
        
        mockSupabaseClient.from().select().eq().order.mockResolvedValue({
          data: activeUsers,
          error: null,
        })

        const service = new AdminUserService()
        const result = await service.getUsersByStatus(true)

        expect(result).toEqual(activeUsers)
        expect(mockSupabaseClient.from().select().eq).toHaveBeenCalledWith('is_active', true)
      })

      it('should get user by ID with company details', async () => {
        const userWithCompany = {
          ...mockUserProfile,
          companies: {
            id: 'company-123',
            name: 'Test Company',
            company_type: 'customer',
          },
        }
        
        mockSupabaseClient.from().select().eq().single.mockResolvedValue({
          data: userWithCompany,
          error: null,
        })

        const service = new AdminUserService()
        const result = await service.getUserById('user-123')

        expect(result).toEqual(userWithCompany)
        expect(mockSupabaseClient.from().select).toHaveBeenCalledWith(
          expect.stringContaining('companies:company_id')
        )
      })
    })

    describe('Error Handling', () => {
      it('should map database constraint errors correctly', async () => {
        mockSupabaseClient.from().update().eq().select().single.mockResolvedValue({
          data: null,
          error: { code: '23505', message: 'Unique constraint violation' },
        })

        const service = new AdminUserService()
        
        await expect(service.activateUser('user-123'))
          .rejects.toMatchObject({
            code: 'UNIQUE_VIOLATION',
            message: 'A user with this email already exists.',
          })
      })

      it('should handle unknown errors', async () => {
        mockSupabaseClient.from().update().eq().select().single.mockResolvedValue({
          data: null,
          error: { code: 'UNKNOWN_CODE', message: 'Unknown error' },
        })

        const service = new AdminUserService()
        
        await expect(service.activateUser('user-123'))
          .rejects.toMatchObject({
            code: 'UNKNOWN_ERROR',
            message: 'An unexpected error occurred. Please try again.',
          })
      })
    })

    describe('Audit Logging', () => {
      it('should log user actions when auth user is available', async () => {
        const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
        
        mockSupabaseClient.from().update().eq().select().single.mockResolvedValue({
          data: { ...mockUserProfile, is_active: true },
          error: null,
        })

        const service = new AdminUserService()
        await service.activateUser('user-123', 'Test activation')

        expect(consoleSpy).toHaveBeenCalledWith('User ACTIVATED:', {
          targetUserId: 'user-123',
          actionBy: 'current-admin',
          action: 'ACTIVATED',
          reason: 'Test activation',
          timestamp: '2023-01-01T00:00:00.000Z',
        })

        consoleSpy.mockRestore()
      })

      it('should handle logging errors gracefully', async () => {
        const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
        
        mockSupabaseClient.auth.getUser.mockResolvedValue({
          data: { user: null },
          error: new Error('Auth error'),
        })

        mockSupabaseClient.from().update().eq().select().single.mockResolvedValue({
          data: { ...mockUserProfile, is_active: true },
          error: null,
        })

        const service = new AdminUserService()
        await service.activateUser('user-123')

        expect(consoleWarnSpy).toHaveBeenCalledWith('Could not get current user for logging')
        consoleWarnSpy.mockRestore()
      })
    })
  })

  describe('UserActivationDialog Component', () => {
    const mockOnOpenChange = vi.fn()
    const mockOnSuccess = vi.fn()

    beforeEach(() => {
      vi.clearAllMocks()
    })

    describe('Activation Mode', () => {
      it('should render activation dialog for inactive user', () => {
        const inactiveUser = { ...mockUserProfile, is_active: false }
        
        render(
          <UserActivationDialog
            user={inactiveUser}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        expect(screen.getByText('Activate User Account')).toBeInTheDocument()
        expect(screen.getByText(/Activate the account for/)).toBeInTheDocument()
        expect(screen.getByText('Test User')).toBeInTheDocument()
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
        expect(screen.getByText('Reason (Optional)')).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /activate account/i })).toBeInTheDocument()
      })

      it('should show correct placeholder for activation reason', () => {
        const inactiveUser = { ...mockUserProfile, is_active: false }
        
        render(
          <UserActivationDialog
            user={inactiveUser}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByPlaceholderText('Optional reason for activation...')
        expect(textarea).toBeInTheDocument()
      })
    })

    describe('Deactivation Mode', () => {
      it('should render deactivation dialog for active user', () => {
        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        expect(screen.getByText('Deactivate User Account')).toBeInTheDocument()
        expect(screen.getByText(/Deactivate the account for/)).toBeInTheDocument()
        expect(screen.getByText('Reason (Required)')).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /deactivate account/i })).toBeInTheDocument()
      })

      it('should show correct placeholder for deactivation reason', () => {
        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByPlaceholderText('Explain why this account is being deactivated...')
        expect(textarea).toBeInTheDocument()
      })

      it('should have destructive button variant for deactivation', () => {
        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const deactivateButton = screen.getByRole('button', { name: /deactivate account/i })
        expect(deactivateButton).toHaveClass('destructive')
      })
    })

    describe('Form Interactions', () => {
      it('should allow typing in reason field', async () => {
        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByRole('textbox')
        fireEvent.change(textarea, { target: { value: 'Security violation detected' } })

        await waitFor(() => {
          expect((textarea as HTMLTextAreaElement).value).toBe('Security violation detected')
        })
      })

      it('should call onOpenChange when cancel is clicked', () => {
        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const cancelButton = screen.getByRole('button', { name: /cancel/i })
        fireEvent.click(cancelButton)

        expect(mockOnOpenChange).toHaveBeenCalledWith(false)
      })

      it('should show loading state during submission', async () => {
        const mockToggleUserActivation = vi.spyOn(adminUserService, 'toggleUserActivation')
        mockToggleUserActivation.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByRole('textbox')
        const submitButton = screen.getByRole('button', { name: /deactivate account/i })

        fireEvent.change(textarea, { target: { value: 'Test reason' } })
        fireEvent.click(submitButton)

        await waitFor(() => {
          expect(submitButton).toBeDisabled()
          expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
        })

        mockToggleUserActivation.mockRestore()
      })
    })

    describe('Submission and Success', () => {
      it('should successfully activate user', async () => {
        const inactiveUser = { ...mockUserProfile, is_active: false }
        const activatedUser = { ...mockUserProfile, is_active: true }
        
        const mockToggleUserActivation = vi.spyOn(adminUserService, 'toggleUserActivation')
        mockToggleUserActivation.mockResolvedValue(activatedUser)

        render(
          <UserActivationDialog
            user={inactiveUser}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByRole('textbox')
        const submitButton = screen.getByRole('button', { name: /activate account/i })

        fireEvent.change(textarea, { target: { value: 'Account verification completed' } })
        fireEvent.click(submitButton)

        await waitFor(() => {
          expect(mockToggleUserActivation).toHaveBeenCalledWith({
            userId: 'user-123',
            isActive: true,
            reason: 'Account verification completed',
          })
          expect(mockOnSuccess).toHaveBeenCalledWith(activatedUser)
          expect(mockOnOpenChange).toHaveBeenCalledWith(false)
        })

        mockToggleUserActivation.mockRestore()
      })

      it('should successfully deactivate user', async () => {
        const deactivatedUser = { ...mockUserProfile, is_active: false }
        
        const mockToggleUserActivation = vi.spyOn(adminUserService, 'toggleUserActivation')
        mockToggleUserActivation.mockResolvedValue(deactivatedUser)

        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByRole('textbox')
        const submitButton = screen.getByRole('button', { name: /deactivate account/i })

        fireEvent.change(textarea, { target: { value: 'Policy violation' } })
        fireEvent.click(submitButton)

        await waitFor(() => {
          expect(mockToggleUserActivation).toHaveBeenCalledWith({
            userId: 'user-123',
            isActive: false,
            reason: 'Policy violation',
          })
          expect(mockOnSuccess).toHaveBeenCalledWith(deactivatedUser)
          expect(mockOnOpenChange).toHaveBeenCalledWith(false)
        })

        mockToggleUserActivation.mockRestore()
      })
    })

    describe('Error Handling', () => {
      it('should display error when activation fails', async () => {
        const activationError = {
          code: 'CANNOT_DEACTIVATE_LAST_ADMIN',
          message: 'Cannot deactivate the last active admin user.',
          timestamp: new Date(),
        }

        const mockToggleUserActivation = vi.spyOn(adminUserService, 'toggleUserActivation')
        mockToggleUserActivation.mockRejectedValue(activationError)

        render(
          <UserActivationDialog
            user={mockAdminProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByRole('textbox')
        const submitButton = screen.getByRole('button', { name: /deactivate account/i })

        fireEvent.change(textarea, { target: { value: 'Test reason' } })
        fireEvent.click(submitButton)

        await waitFor(() => {
          expect(screen.getByTestId('auth-error-display')).toBeInTheDocument()
          expect(screen.getByText('Cannot deactivate the last active admin user.')).toBeInTheDocument()
        })

        mockToggleUserActivation.mockRestore()
      })

      it('should allow retry after error', async () => {
        const activationError = {
          code: 'NETWORK_ERROR',
          message: 'Network connection failed',
          timestamp: new Date(),
        }

        const mockToggleUserActivation = vi.spyOn(adminUserService, 'toggleUserActivation')
        mockToggleUserActivation
          .mockRejectedValueOnce(activationError)
          .mockResolvedValueOnce({ ...mockUserProfile, is_active: false })

        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByRole('textbox')
        const submitButton = screen.getByRole('button', { name: /deactivate account/i })

        fireEvent.change(textarea, { target: { value: 'Test reason' } })
        fireEvent.click(submitButton)

        await waitFor(() => {
          expect(screen.getByTestId('auth-error-display')).toBeInTheDocument()
        })

        const retryButton = screen.getByTestId('retry-button')
        fireEvent.click(retryButton)

        await waitFor(() => {
          expect(mockOnSuccess).toHaveBeenCalled()
        })

        expect(mockToggleUserActivation).toHaveBeenCalledTimes(2)
        mockToggleUserActivation.mockRestore()
      })
    })

    describe('Edge Cases', () => {
      it('should not render when user is null', () => {
        const { container } = render(
          <UserActivationDialog
            user={null}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        expect(container.firstChild).toBeNull()
      })

      it('should disable form fields during loading', async () => {
        const mockToggleUserActivation = vi.spyOn(adminUserService, 'toggleUserActivation')
        mockToggleUserActivation.mockImplementation(() => new Promise(() => {})) // Never resolves

        render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByRole('textbox')
        const submitButton = screen.getByRole('button', { name: /deactivate account/i })
        const cancelButton = screen.getByRole('button', { name: /cancel/i })

        fireEvent.change(textarea, { target: { value: 'Test reason' } })
        fireEvent.click(submitButton)

        await waitFor(() => {
          expect(textarea).toBeDisabled()
          expect(submitButton).toBeDisabled()
          expect(cancelButton).toBeDisabled()
        })

        mockToggleUserActivation.mockRestore()
      })

      it('should reset form after successful submission', async () => {
        const activatedUser = { ...mockUserProfile, is_active: true }
        
        const mockToggleUserActivation = vi.spyOn(adminUserService, 'toggleUserActivation')
        mockToggleUserActivation.mockResolvedValue(activatedUser)

        const { rerender } = render(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const textarea = screen.getByRole('textbox')
        const submitButton = screen.getByRole('button', { name: /deactivate account/i })

        fireEvent.change(textarea, { target: { value: 'Test reason' } })
        fireEvent.click(submitButton)

        await waitFor(() => {
          expect(mockOnSuccess).toHaveBeenCalled()
        })

        // Reopen dialog (simulating new activation)
        rerender(
          <UserActivationDialog
            user={mockUserProfile}
            open={true}
            onOpenChange={mockOnOpenChange}
            onSuccess={mockOnSuccess}
          />
        )

        const newTextarea = screen.getByRole('textbox')
        expect((newTextarea as HTMLTextAreaElement).value).toBe('')

        mockToggleUserActivation.mockRestore()
      })
    })
  })
})