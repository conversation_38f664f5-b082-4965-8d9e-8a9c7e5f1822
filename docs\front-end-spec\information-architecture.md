# Information Architecture

## Navigation Hierarchy

```
DYY Trading Management
├── Dashboard (Role-specific landing)
├── Shipments
│   ├── Create New Shipment
│   ├── Active Shipments
│   ├── Shipment History
│   └── Status Updates
├── Master Data (Admin/CS only)
│   ├── Customer Management
│   ├── Factory Management
│   ├── Shipper Management
│   ├── Consignee Management
│   ├── Notify Party Management
│   ├── Forwarder Agents Management
│   ├── Carrier Management
│   ├── Driver Management
│   ├── Products
│   ├── Ports
│   └── Relationship Configuration
├── Documents
│   ├── Generate Documents
│   ├── Document Library
│   └── Templates (Admin only)
├── Communications
│   ├── Notification Center
│   ├── Channel Preferences
│   └── Message Templates (Admin only)
└── Account & Settings
    ├── User Profile
    ├── Preferences
    └── System Settings (Admin only)
```

## Mobile Navigation (Driver Interface)

```
Driver Mobile App
├── My Assignments (Dashboard)
├── Update Status
├── Photo Gallery
├── Work History
└── Profile & Settings
```

## Content Organization Principles

1. **Role-Based Information Hierarchy**
   - Most critical information at top level for each user type
   - Progressive disclosure prevents information overload
   - Context-sensitive navigation shows relevant sections only

2. **Task-Oriented Grouping**
   - Shipment lifecycle as primary organizing principle
   - Master data grouped by functional relationships
   - Communication tools consolidated for efficiency

3. **Mobile-First Information Prioritization**
   - Essential actions accessible within 2 taps on mobile
   - Status information prominently displayed
   - Secondary information available through clear navigation

---
