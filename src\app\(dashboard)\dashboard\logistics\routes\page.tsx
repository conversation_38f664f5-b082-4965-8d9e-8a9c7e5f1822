'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Filter,
  X,
  MapPin,
  Route,
  Clock,
  Truck,
  Navigation2,
  Eye,
  ArrowRight,
  Calendar,
  Timer,
  Fuel,
  DollarSign,
  Map,
} from 'lucide-react'

// Mock data for routes
const mockRoutes = [
  {
    id: '1',
    name: 'Bangkok - Laem Chabang Port',
    origin: 'Bangkok',
    destination: 'Laem Chabang Port',
    distance: '120 km',
    estimatedTime: '2h 30m',
    transportMode: 'Land',
    status: 'Active',
    frequency: 'Daily',
    cost: '$150',
    description: 'Main route for fruit export to Laem Chabang port',
    waypoints: ['Samut Prakan', 'Chonburi'],
    lastUsed: '2024-01-20',
  },
  {
    id: '2',
    name: 'Chiang Mai - Bangkok',
    origin: 'Chiang Mai',
    destination: 'Bangkok',
    distance: '700 km',
    estimatedTime: '9h 15m',
    transportMode: 'Land',
    status: 'Active',
    frequency: 'Weekly',
    cost: '$450',
    description: 'Northern fruit collection route',
    waypoints: ['Lampang', 'Nakhon Sawan', 'Ayutthaya'],
    lastUsed: '2024-01-19',
  },
  {
    id: '3',
    name: 'Surat Thani - Phuket Port',
    origin: 'Surat Thani',
    destination: 'Phuket Port',
    distance: '170 km',
    estimatedTime: '3h 45m',
    transportMode: 'Land',
    status: 'Active',
    frequency: 'Twice Weekly',
    cost: '$200',
    description: 'Southern fruit export route',
    waypoints: ['Phang Nga'],
    lastUsed: '2024-01-18',
  },
  {
    id: '4',
    name: 'Bangkok - Map Ta Phut Port',
    origin: 'Bangkok',
    destination: 'Map Ta Phut Port',
    distance: '200 km',
    estimatedTime: '3h 30m',
    transportMode: 'Land',
    status: 'Inactive',
    frequency: 'On Demand',
    cost: '$280',
    description: 'Alternative port route (currently not used)',
    waypoints: ['Chachoengsao', 'Rayong'],
    lastUsed: '2023-12-15',
  },
  {
    id: '5',
    name: 'Rayong - Laem Chabang Port',
    origin: 'Rayong',
    destination: 'Laem Chabang Port',
    distance: '45 km',
    estimatedTime: '1h 15m',
    transportMode: 'Land',
    status: 'Active',
    frequency: 'Daily',
    cost: '$80',
    description: 'Short haul port connection',
    waypoints: [],
    lastUsed: '2024-01-20',
  },
]

export default function RoutesPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRoutes, setSelectedRoutes] = useState<string[]>([])
  const [viewingRoute, setViewingRoute] = useState<any>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // Filter routes based on search term
  const filteredRoutes = mockRoutes.filter(route =>
    route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    route.origin.toLowerCase().includes(searchTerm.toLowerCase()) ||
    route.destination.toLowerCase().includes(searchTerm.toLowerCase()) ||
    route.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const toggleRoute = (routeId: string) => {
    setSelectedRoutes(prev =>
      prev.includes(routeId)
        ? prev.filter(id => id !== routeId)
        : [...prev, routeId]
    )
  }

  const toggleAll = () => {
    setSelectedRoutes(
      selectedRoutes.length === filteredRoutes.length
        ? []
        : filteredRoutes.map(route => route.id)
    )
  }

  const clearSelection = () => {
    setSelectedRoutes([])
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Routes Management</h1>
          <p className="text-slate-400 mt-1">
            Manage transportation routes and logistics planning
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Route
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">Create New Route</DialogTitle>
              <DialogDescription className="text-slate-400">
                Define a new transportation route with waypoints and logistics details
              </DialogDescription>
            </DialogHeader>
            <div className="text-center py-8 text-slate-400">
              <Route className="h-12 w-12 mx-auto mb-4" />
              <p>Route creation form would be implemented here</p>
              <p className="text-sm mt-2">
                This is a mockup for presentation purposes
              </p>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filters */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Search Routes</h3>
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchTerm('')}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-slate-300 text-sm font-medium">
            Search Routes
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search by route name, origin, destination, or description..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
            />
          </div>
        </div>

        {searchTerm && (
          <div className="flex flex-wrap gap-2 pt-2">
            <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
              Search: "{searchTerm}"
              <button
                onClick={() => setSearchTerm('')}
                className="ml-2 hover:text-orange-200"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Route Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-500/20">
                <Route className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-slate-400 text-sm">Total Routes</p>
                <p className="text-2xl font-bold text-white">{mockRoutes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-500/20">
                <Navigation2 className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-slate-400 text-sm">Active Routes</p>
                <p className="text-2xl font-bold text-white">
                  {mockRoutes.filter(r => r.status === 'Active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-500/20">
                <MapPin className="h-6 w-6 text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-slate-400 text-sm">Total Distance</p>
                <p className="text-2xl font-bold text-white">1,235 km</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-500/20">
                <DollarSign className="h-6 w-6 text-orange-400" />
              </div>
              <div className="ml-4">
                <p className="text-slate-400 text-sm">Avg. Cost</p>
                <p className="text-2xl font-bold text-white">$232</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      {selectedRoutes.length > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedRoutes.length} route{selectedRoutes.length !== 1 ? 's' : ''} selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  Deactivate Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Routes Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Route className="h-5 w-5 text-orange-500" />
              Transportation Routes ({filteredRoutes.length})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Refresh Routes
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {filteredRoutes.length === 0 ? (
            <div className="text-center py-8">
              <Route className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No routes found</p>
              <p className="text-sm text-slate-400 mt-1">
                {searchTerm ? 'Try adjusting your search criteria' : 'Create your first route to get started'}
              </p>
            </div>
          ) : (
            <Table className="bg-slate-800">
              <TableHeader className="bg-slate-700">
                <TableRow className="border-slate-600 hover:bg-slate-700">
                  <TableHead className="w-12 text-slate-200">
                    <Checkbox
                      checked={selectedRoutes.length === filteredRoutes.length}
                      onCheckedChange={toggleAll}
                      className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                    />
                  </TableHead>
                  <TableHead className="text-slate-200">Route Name</TableHead>
                  <TableHead className="text-slate-200">Route Details</TableHead>
                  <TableHead className="text-slate-200">Distance & Time</TableHead>
                  <TableHead className="text-slate-200">Cost & Frequency</TableHead>
                  <TableHead className="text-slate-200">Status</TableHead>
                  <TableHead className="w-32 text-slate-200">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="bg-slate-800">
                {filteredRoutes.map(route => (
                  <TableRow
                    key={route.id}
                    className="border-slate-600 hover:bg-slate-700 text-slate-200"
                  >
                    <TableCell>
                      <Checkbox
                        checked={selectedRoutes.includes(route.id)}
                        onCheckedChange={() => toggleRoute(route.id)}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableCell>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <Route className="h-4 w-4 text-orange-500" />
                        <div>
                          <span className="font-semibold">{route.name}</span>
                          <p className="text-xs text-slate-400 mt-1">
                            {route.description}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <MapPin className="h-3 w-3 text-green-400 mr-1" />
                          <span className="text-slate-300">{route.origin}</span>
                        </div>
                        <div className="flex items-center text-xs text-slate-400">
                          <ArrowRight className="h-3 w-3 mr-1" />
                          {route.waypoints.length > 0 && (
                            <span className="mr-2">via {route.waypoints.join(', ')}</span>
                          )}
                        </div>
                        <div className="flex items-center text-sm">
                          <MapPin className="h-3 w-3 text-red-400 mr-1" />
                          <span className="text-slate-300">{route.destination}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Navigation2 className="h-3 w-3 text-blue-400 mr-1" />
                          <span className="text-slate-300">{route.distance}</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Clock className="h-3 w-3 text-purple-400 mr-1" />
                          <span className="text-slate-300">{route.estimatedTime}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <DollarSign className="h-3 w-3 text-green-400 mr-1" />
                          <span className="text-slate-300">{route.cost}</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Timer className="h-3 w-3 text-orange-400 mr-1" />
                          <span className="text-slate-300">{route.frequency}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={route.status === 'Active' ? 'default' : 'secondary'}
                        className={
                          route.status === 'Active'
                            ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                            : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                        }
                      >
                        {route.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setViewingRoute(route)}
                          className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Route Details Modal */}
      {viewingRoute && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-slate-800 rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">
                Route Details
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewingRoute(null)}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Route Header */}
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <Route className="h-8 w-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white">
                    {viewingRoute.name}
                  </h3>
                  <p className="text-slate-400 mt-1">{viewingRoute.description}</p>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge
                      variant="outline"
                      className="border-orange-400 text-orange-200 bg-orange-500/20"
                    >
                      <Truck className="h-3 w-3 mr-1" />
                      {viewingRoute.transportMode}
                    </Badge>
                    <Badge
                      variant={viewingRoute.status === 'Active' ? 'default' : 'secondary'}
                      className={
                        viewingRoute.status === 'Active'
                          ? 'bg-green-600 text-white border-green-600'
                          : 'bg-slate-600 text-slate-200 border-slate-500'
                      }
                    >
                      {viewingRoute.status}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Route Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <MapPin className="h-4 w-4 text-green-500" />
                    <span className="text-white font-medium">Origin</span>
                  </div>
                  <p className="text-slate-300">{viewingRoute.origin}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <MapPin className="h-4 w-4 text-red-500" />
                    <span className="text-white font-medium">Destination</span>
                  </div>
                  <p className="text-slate-300">{viewingRoute.destination}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Navigation2 className="h-4 w-4 text-blue-500" />
                    <span className="text-white font-medium">Distance</span>
                  </div>
                  <p className="text-slate-300">{viewingRoute.distance}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Clock className="h-4 w-4 text-purple-500" />
                    <span className="text-white font-medium">Estimated Time</span>
                  </div>
                  <p className="text-slate-300">{viewingRoute.estimatedTime}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <DollarSign className="h-4 w-4 text-green-500" />
                    <span className="text-white font-medium">Cost</span>
                  </div>
                  <p className="text-slate-300">{viewingRoute.cost}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Timer className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Frequency</span>
                  </div>
                  <p className="text-slate-300">{viewingRoute.frequency}</p>
                </div>
              </div>

              {/* Waypoints */}
              {viewingRoute.waypoints && viewingRoute.waypoints.length > 0 && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Map className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Waypoints</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {viewingRoute.waypoints.map((waypoint: string, index: number) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="border-slate-400 text-slate-200"
                      >
                        {waypoint}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Last Used */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Calendar className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Last Used</span>
                </div>
                <p className="text-slate-300">{viewingRoute.lastUsed}</p>
              </div>

              {/* Route ID */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Navigation2 className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Route ID</span>
                </div>
                <p className="text-slate-300 font-mono text-sm">{viewingRoute.id}</p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end mt-6 space-x-2">
              <Button
                variant="outline"
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
              >
                Edit Route
              </Button>
              <Button
                onClick={() => setViewingRoute(null)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}