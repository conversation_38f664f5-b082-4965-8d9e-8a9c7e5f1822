# Responsive Design Implementation

## Breakpoint Strategy

### Device Categories
```css
/* Mobile First Approach */
/* Mobile (default): 0px - 767px */
.mobile-first { /* base styles */ }

/* Tablet: 768px - 1023px */
@media (min-width: 768px) {
  .tablet-up { /* tablet and desktop styles */ }
}

/* Desktop: 1024px - 1279px */
@media (min-width: 1024px) {
  .desktop-up { /* desktop styles */ }
}

/* Large Desktop: 1280px+ */
@media (min-width: 1280px) {
  .large-desktop { /* large screen styles */ }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi { /* retina/high-dpi styles */ }
}
```

### Touch Target Optimization
```css
/* Minimum Touch Target Requirements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
}

/* Mobile-Specific Interactions */
@media (max-width: 767px) {
  .mobile-touch {
    font-size: 16px; /* Prevent zoom on iOS */
    line-height: 1.5;
    letter-spacing: 0.01em;
  }
  
  .mobile-form-control {
    height: 44px;
    font-size: 16px;
    border-radius: 8px;
  }
  
  .mobile-button {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}
```

## Layout Adaptations

### Dashboard Grid Responsive Behavior
```tsx
{/* Admin Dashboard Grid - Responsive */}
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
  
  {/* Stats Cards - Full width on mobile, adaptive on larger screens */}
  <Card className="sm:col-span-2 lg:col-span-1">
    <StatCard title="Active Shipments" value="127" />
  </Card>
  
  {/* Master Data Section - Responsive column spanning */}
  <Card className="col-span-full lg:col-span-2">
    <MasterDataGrid />
  </Card>
  
  {/* Activity Feed - Adaptive positioning */}
  <Card className="col-span-full lg:col-span-2 xl:col-span-3">
    <ActivityFeed />
  </Card>
  
</div>
```

### Mobile Navigation Patterns
```tsx
{/* Desktop: Horizontal Navigation */}
<nav className="hidden md:flex space-x-8">
  <NavigationLink href="/dashboard">Dashboard</NavigationLink>
  <NavigationLink href="/shipments">Shipments</NavigationLink>
  <NavigationLink href="/master-data">Master Data</NavigationLink>
</nav>

{/* Mobile: Hamburger Menu */}
<div className="md:hidden">
  <Sheet>
    <SheetTrigger asChild>
      <Button variant="ghost" size="sm">
        <MenuIcon className="w-5 h-5" />
      </Button>
    </SheetTrigger>
    <SheetContent side="left" className="bg-primary-900 border-primary-700">
      <nav className="space-y-4 mt-8">
        <MobileNavigationLink href="/dashboard" icon={<HomeIcon />}>
          Dashboard
        </MobileNavigationLink>
        <MobileNavigationLink href="/shipments" icon={<TruckIcon />}>
          Shipments
        </MobileNavigationLink>
        <MobileNavigationLink href="/master-data" icon={<DatabaseIcon />}>
          Master Data
        </MobileNavigationLink>
      </nav>
    </SheetContent>
  </Sheet>
</div>

{/* Mobile: Bottom Tab Navigation (Driver Interface) */}
<div className="md:hidden fixed bottom-0 left-0 right-0 bg-primary-900 border-t border-primary-700 z-50">
  <div className="grid grid-cols-4 h-16">
    {tabs.map(tab => (
      <Link 
        key={tab.href}
        href={tab.href}
        className={`flex flex-col items-center justify-center space-y-1 ${
          pathname === tab.href ? 'text-accent-500' : 'text-neutral-400'
        }`}
      >
        <tab.icon className="w-5 h-5" />
        <span className="text-xs font-medium">{tab.label}</span>
      </Link>
    ))}
  </div>
</div>
```

### Form Layout Responsive Adaptations
```tsx
{/* Shipment Creation Form - Responsive Layout */}
<form className="space-y-6">
  
  {/* Customer Information - Responsive Grid */}
  <Card>
    <CardContent className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 p-6">
      
      {/* Customer Selection - Full width on mobile */}
      <div className="md:col-span-2 xl:col-span-1">
        <Label htmlFor="customer">Customer *</Label>
        <CustomerSelect />
      </div>
      
      {/* Shipper & Product - Stack on mobile, side-by-side on larger */}
      <div>
        <Label htmlFor="shipper">Shipper</Label>
        <ShipperSelect />
      </div>
      
      <div>
        <Label htmlFor="product">Product</Label>
        <ProductSelect />
      </div>
      
    </CardContent>
  </Card>
  
  {/* Product Details - Responsive Table/Cards */}
  <Card>
    <CardContent className="p-6">
      
      {/* Desktop: Data Table */}
      <div className="hidden md:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Unit Price</TableHead>
              <TableHead>Total Value</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.map(product => (
              <ProductTableRow key={product.id} product={product} />
            ))}
          </TableBody>
        </Table>
      </div>
      
      {/* Mobile: Card Layout */}
      <div className="md:hidden space-y-4">
        {products.map(product => (
          <Card key={product.id} className="bg-primary-800">
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-neutral-50">{product.name}</h4>
                <ProductActionMenu product={product} />
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-neutral-400">Quantity:</span>
                  <span className="ml-2 text-neutral-50">{product.quantity}</span>
                </div>
                <div>
                  <span className="text-neutral-400">Unit Price:</span>
                  <span className="ml-2 text-neutral-50 font-mono">${product.unitPrice}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-neutral-400">Total Value:</span>
                  <span className="ml-2 text-accent-500 font-mono font-medium">
                    ${product.totalValue}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
    </CardContent>
  </Card>
  
</form>
```

---
