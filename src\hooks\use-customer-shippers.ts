import { useEffect, useMemo, useCallback } from 'react'
import {
  useCustomerShipperStore,
  useCustomerShippersLoading,
  useCustomerShippersError,
  useCustomerShipperActions,
  type CustomerShipper,
  type CustomerShipperInsert,
  type CustomerShipperUpdate,
  type CustomerShipperBulkData,
  type CustomerShipperBulkResult,
} from '@/stores/customer-shipper-store'
import type { Company } from '@/stores/company-store'
import type { CustomerShipperFilter } from '@/lib/validations/customer-shippers'

// Main hook for customer-shipper management
// Hook for shipment relationship integration (compatible with useShipmentRelationships)
export function useCustomerShippers(customerId?: string) {
  const management = useCustomerShippersManagement()

  // Filter relationships for specific customer
  const customerShippers = useMemo(() => {
    if (!customerId) return []
    return management.relationships.filter(
      rel => rel.customer_id === customerId
    )
  }, [management.relationships, customerId])

  const getShippersForCustomer = useCallback(
    (customerIdParam: string) => {
      return management.relationships.filter(
        rel => rel.customer_id === customerIdParam
      )
    },
    [management.relationships]
  )

  const getDefaultShipperForCustomer = useCallback(
    (customerIdParam: string) => {
      return (
        management.relationships.find(
          rel =>
            rel.customer_id === customerIdParam &&
            rel.is_default &&
            rel.is_active
        ) || null
      )
    },
    [management.relationships]
  )

  return {
    customerShippers,
    loading: management.loading,
    error: management.error,
    getShippersForCustomer,
    getDefaultShipperForCustomer,
  }
}

export function useCustomerShippersManagement() {
  const store = useCustomerShipperStore()

  // Extract individual actions to avoid recreating on every render
  const fetchRelationships = useCustomerShipperStore(state => state.fetchRelationships)
  const fetchRelationshipById = useCustomerShipperStore(state => state.fetchRelationshipById)
  const createRelationshipAction = useCustomerShipperStore(state => state.createRelationship)
  const updateRelationshipAction = useCustomerShipperStore(state => state.updateRelationship)
  const deleteRelationshipAction = useCustomerShipperStore(state => state.deleteRelationship)
  const deleteRelationshipsAction = useCustomerShipperStore(state => state.deleteRelationships)
  const bulkImportRelationshipsAction = useCustomerShipperStore(state => state.bulkImportRelationships)
  const setFilterAction = useCustomerShipperStore(state => state.setFilter)
  const setSearchTermAction = useCustomerShipperStore(state => state.setSearchTerm)
  const setSortingAction = useCustomerShipperStore(state => state.setSorting)
  const setPageAction = useCustomerShipperStore(state => state.setPage)
  const setPageSizeAction = useCustomerShipperStore(state => state.setPageSize)
  const selectRelationshipAction = useCustomerShipperStore(state => state.selectRelationship)
  const deselectRelationshipAction = useCustomerShipperStore(state => state.deselectRelationship)
  const clearSelectionAction = useCustomerShipperStore(state => state.clearSelection)
  const clearErrorAction = useCustomerShipperStore(state => state.clearError)
  const subscribeToRelationshipsAction = useCustomerShipperStore(state => state.subscribeToRelationships)
  const getCustomerOptionsAction = useCustomerShipperStore(state => state.getCustomerOptions)
  const getShipperOptionsAction = useCustomerShipperStore(state => state.getShipperOptions)

  // Auto-fetch on mount and when filters change
  useEffect(() => {
    if (store.relationships.length === 0 && !store.loading) {
      fetchRelationships()
    }
  }, [fetchRelationships, store.relationships.length, store.loading])

  // Computed values for pagination
  const totalPages = useMemo(
    () => Math.ceil(store.totalCount / store.pageSize),
    [store.totalCount, store.pageSize]
  )

  const hasNextPage = useMemo(
    () => store.currentPage < totalPages,
    [store.currentPage, totalPages]
  )

  const hasPreviousPage = useMemo(
    () => store.currentPage > 1,
    [store.currentPage]
  )

  // Selection helpers
  const selectedCount = store.selectedRelationships.size

  const isSelected = (id: string) => store.selectedRelationships.has(id)

  const isAllSelected = useMemo(
    () =>
      store.relationships.length > 0 &&
      store.relationships.every(rel => store.selectedRelationships.has(rel.id)),
    [store.relationships, store.selectedRelationships]
  )

  const isPartiallySelected = useMemo(
    () => store.selectedRelationships.size > 0 && !isAllSelected,
    [store.selectedRelationships.size, isAllSelected]
  )

  // Enhanced actions with error handling
  const createRelationship = async (data: CustomerShipperInsert) => {
    try {
      return await createRelationshipAction(data)
    } catch (error) {
      console.error('Create relationship error:', error)
      throw error
    }
  }

  const updateRelationship = async (
    id: string,
    updates: Partial<CustomerShipperUpdate>
  ) => {
    try {
      return await updateRelationshipAction(id, updates)
    } catch (error) {
      console.error('Update relationship error:', error)
      throw error
    }
  }

  const deleteRelationship = async (id: string) => {
    try {
      await deleteRelationshipAction(id)
    } catch (error) {
      console.error('Delete relationship error:', error)
      throw error
    }
  }

  const bulkDeleteRelationships = async (ids: string[]) => {
    try {
      await deleteRelationshipsAction(ids)
    } catch (error) {
      console.error('Bulk delete relationships error:', error)
      throw error
    }
  }

  const bulkImportRelationships = async (
    data: CustomerShipperBulkData[]
  ): Promise<CustomerShipperBulkResult> => {
    try {
      return await bulkImportRelationshipsAction(data)
    } catch (error) {
      console.error('Bulk import relationships error:', error)
      throw error
    }
  }

  // Pagination helpers
  const nextPage = () => {
    if (hasNextPage) {
      setPageAction(store.currentPage + 1)
    }
  }

  const previousPage = () => {
    if (hasPreviousPage) {
      setPageAction(store.currentPage - 1)
    }
  }

  // Selection helpers
  const toggleRelationship = (id: string) => {
    if (store.selectedRelationships.has(id)) {
      deselectRelationshipAction(id)
    } else {
      selectRelationshipAction(id)
    }
  }

  const toggleAll = () => {
    if (isAllSelected) {
      clearSelectionAction()
    } else {
      // Select all current relationships
      store.relationships.forEach(rel => selectRelationshipAction(rel.id))
    }
  }

  // Data access
  const getRelationshipsByCustomer = (customerId: string) => {
    return store.relationships.filter(rel => rel.customer_id === customerId)
  }

  const getDefaultShipperForCustomer = (customerId: string) => {
    return store.relationships.find(
      rel =>
        rel.customer_id === customerId &&
        rel.is_default === true &&
        rel.is_active === true
    )
  }

  const getActiveRelationshipsForCustomer = (customerId: string) => {
    return store.relationships.filter(
      rel => rel.customer_id === customerId && rel.is_active === true
    )
  }

  return {
    // Data
    relationships: store.relationships,
    loading: store.loading,
    error: store.error,

    // Pagination
    currentPage: store.currentPage,
    pageSize: store.pageSize,
    totalCount: store.totalCount,
    totalPages,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter: store.filter,
    searchTerm: store.searchTerm,
    sortBy: store.sortBy,
    sortOrder: store.sortOrder,

    // Selection
    selectedRelationships: store.selectedRelationships,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createRelationship,
    updateRelationship,
    deleteRelationship,
    bulkDeleteRelationships,
    bulkImportRelationships,
    isCreating: store.isCreating,
    isUpdating: store.isUpdating,
    isDeleting: store.isDeleting,
    isBulkImporting: store.isBulkImporting,

    // Actions
    setFilter: setFilterAction,
    setSearchTerm: setSearchTermAction,
    setSorting: setSortingAction,
    setPage: setPageAction,
    setPageSize: setPageSizeAction,
    nextPage,
    previousPage,
    toggleRelationship,
    toggleAll,
    clearSelection: clearSelectionAction,
    clearError: clearErrorAction,
    refreshRelationships: fetchRelationships,

    // Utility methods
    getRelationshipsByCustomer,
    getDefaultShipperForCustomer,
    getActiveRelationshipsForCustomer,
    fetchRelationshipById: fetchRelationshipById,
    getCustomerOptions: getCustomerOptionsAction,
    getShipperOptions: getShipperOptionsAction,

    // Real-time
    subscribeToRelationships: subscribeToRelationshipsAction,
  }
}

// Hook for company selection (customers and shippers)
export function useCompanySelection() {
  // Extract specific functions to avoid dependency on the entire actions object
  const getCustomerOptions = useCustomerShipperStore(state => state.getCustomerOptions)
  const getShipperOptions = useCustomerShipperStore(state => state.getShipperOptions)

  const getCustomerCompanies = useCallback(async (): Promise<Company[]> => {
    return await getCustomerOptions()
  }, [getCustomerOptions])

  const getShipperCompanies = useCallback(async (): Promise<Company[]> => {
    return await getShipperOptions()
  }, [getShipperOptions])

  return {
    getCustomerCompanies,
    getShipperCompanies,
  }
}

// Hook for default shipper logic
export function useDefaultShipperLogic() {
  const getDefaultShipperForCustomer = useCustomerShipperStore(state => state.getDefaultShipperForCustomer)

  const getDefaultShipper = async (
    customerId: string
  ): Promise<CustomerShipper | null> => {
    return await getDefaultShipperForCustomer(customerId)
  }

  const hasDefaultShipper = async (customerId: string): Promise<boolean> => {
    const defaultShipper = await getDefaultShipper(customerId)
    return defaultShipper !== null
  }

  return {
    getDefaultShipper,
    hasDefaultShipper,
  }
}

// Hook for bulk import operations
export function useBulkImport() {
  const { bulkImportRelationships, isBulkImporting } =
    useCustomerShippersManagement()

  // Parse CSV line with proper handling of quoted fields
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    let i = 0

    while (i < line.length) {
      const char = line[i]
      const nextChar = line[i + 1]

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote inside quoted field
          current += '"'
          i += 2
          continue
        } else {
          // Start or end of quoted field
          inQuotes = !inQuotes
          i++
          continue
        }
      }

      if (char === ',' && !inQuotes) {
        // Field separator outside quotes
        result.push(current.trim())
        current = ''
        i++
        continue
      }

      // Regular character
      current += char
      i++
    }

    // Add the last field
    result.push(current.trim())
    return result
  }

  const importFromCSV = async (
    csvData: string
  ): Promise<CustomerShipperBulkResult> => {
    try {
      // Parse CSV data
      const lines = csvData.trim().split('\n')
      const headers = parseCSVLine(lines[0]).map(h =>
        h.toLowerCase().replace(/^["']|["']$/g, '')
      )

      // Validate headers
      const requiredHeaders = ['customer_name', 'shipper_name']
      const hasRequiredHeaders = requiredHeaders.every(required =>
        headers.includes(required)
      )

      if (!hasRequiredHeaders) {
        throw new Error('CSV must contain columns: customer_name, shipper_name')
      }

      // Parse rows
      const data: CustomerShipperBulkData[] = []
      for (let i = 1; i < lines.length; i++) {
        const values = parseCSVLine(lines[i]).map(v =>
          v.replace(/^["']|["']$/g, '')
        )
        if (values.length < 2 || !values.some(v => v)) continue // Skip empty rows

        const row: CustomerShipperBulkData = {
          customer_name: values[headers.indexOf('customer_name')] || '',
          shipper_name: values[headers.indexOf('shipper_name')] || '',
          is_default: false,
          notes: null,
        }

        // Parse optional columns
        const isDefaultIndex = headers.indexOf('is_default')
        if (isDefaultIndex >= 0 && values[isDefaultIndex]) {
          const defaultValue = values[isDefaultIndex].toLowerCase()
          row.is_default = ['true', '1', 'yes', 'y'].includes(defaultValue)
        }

        const notesIndex = headers.indexOf('notes')
        if (notesIndex >= 0 && values[notesIndex]) {
          row.notes = values[notesIndex]
        }

        data.push(row)
      }

      return await bulkImportRelationships(data)
    } catch (error) {
      console.error('CSV import error:', error)
      throw error
    }
  }

  const generateCSVTemplate = (): string => {
    const headers = ['customer_name', 'shipper_name', 'is_default', 'notes']
    const sampleRow = [
      'Customer Company Ltd',
      'Shipper Company Ltd',
      'false',
      'Optional notes',
    ]

    return [headers.join(','), sampleRow.join(',')].join('\n')
  }

  return {
    importFromCSV,
    generateCSVTemplate,
    isBulkImporting,
  }
}

// Hook for relationship validation
export function useRelationshipValidation() {
  const validateRelationship = (data: CustomerShipperInsert): string[] => {
    const errors: string[] = []

    if (!data.customer_id) {
      errors.push('Customer selection is required')
    }

    if (!data.shipper_id) {
      errors.push('Shipper selection is required')
    }

    if (data.customer_id === data.shipper_id) {
      errors.push('Customer and shipper cannot be the same company')
    }

    return errors
  }

  const validateBulkData = (
    data: CustomerShipperBulkData[]
  ): Array<{
    row: number
    errors: string[]
  }> => {
    return data
      .map((item, index) => {
        const errors: string[] = []

        if (!item.customer_name?.trim()) {
          errors.push('Customer name is required')
        }

        if (!item.shipper_name?.trim()) {
          errors.push('Shipper name is required')
        }

        if (item.customer_name === item.shipper_name) {
          errors.push('Customer and shipper cannot be the same')
        }

        return {
          row: index + 1,
          errors,
        }
      })
      .filter(result => result.errors.length > 0)
  }

  return {
    validateRelationship,
    validateBulkData,
  }
}
