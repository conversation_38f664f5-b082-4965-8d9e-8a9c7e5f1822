'use client'

import { Check, X } from 'lucide-react'
import { validatePassword } from '@/lib/validations/auth'

interface PasswordStrengthIndicatorProps {
  password: string
  className?: string
}

interface PasswordRequirement {
  test: (password: string) => boolean
  label: string
}

const requirements: PasswordRequirement[] = [
  {
    test: password => password.length >= 8,
    label: 'At least 8 characters',
  },
  {
    test: password => /(?=.*[a-z])/.test(password),
    label: 'One lowercase letter',
  },
  {
    test: password => /(?=.*[A-Z])/.test(password),
    label: 'One uppercase letter',
  },
  {
    test: password => /(?=.*\d)/.test(password),
    label: 'One number',
  },
  {
    test: password =>
      /(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(password),
    label: 'One special character',
  },
  {
    test: password => !/(.)\1{2,}/.test(password),
    label: 'No repeated characters',
  },
]

export function PasswordStrengthIndicator({
  password,
  className = '',
}: PasswordStrengthIndicatorProps) {
  if (!password) return null

  const metRequirements = requirements.filter(req => req.test(password))
  const strengthPercent = (metRequirements.length / requirements.length) * 100

  const getStrengthColor = () => {
    if (strengthPercent >= 100) return 'bg-green-500'
    if (strengthPercent >= 66) return 'bg-yellow-500'
    if (strengthPercent >= 33) return 'bg-orange-500'
    return 'bg-red-500'
  }

  const getStrengthText = () => {
    if (strengthPercent >= 100) return 'Strong'
    if (strengthPercent >= 66) return 'Good'
    if (strengthPercent >= 33) return 'Fair'
    return 'Weak'
  }

  const validation = validatePassword(password)

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Strength Bar */}
      <div className="space-y-1">
        <div className="flex justify-between text-xs">
          <span className="text-slate-400">Password strength</span>
          <span
            className={`font-medium ${validation.isValid ? 'text-green-400' : 'text-slate-400'}`}
          >
            {getStrengthText()}
          </span>
        </div>
        <div className="h-2 bg-slate-700 rounded-full overflow-hidden">
          <div
            className={`h-full transition-all duration-300 ${getStrengthColor()}`}
            style={{ width: `${strengthPercent}%` }}
          />
        </div>
      </div>

      {/* Requirements List */}
      <div className="space-y-2">
        {requirements.map((requirement, index) => {
          const isMet = requirement.test(password)
          return (
            <div key={index} className="flex items-center gap-2 text-xs">
              {isMet ? (
                <Check className="h-3 w-3 text-green-400 flex-shrink-0" />
              ) : (
                <X className="h-3 w-3 text-slate-500 flex-shrink-0" />
              )}
              <span className={isMet ? 'text-green-400' : 'text-slate-400'}>
                {requirement.label}
              </span>
            </div>
          )
        })}
      </div>

      {/* Additional Validation Errors */}
      {!validation.isValid && validation.error && password.length >= 8 && (
        <div className="flex items-start gap-2 text-xs">
          <X className="h-3 w-3 text-red-400 flex-shrink-0 mt-0.5" />
          <span className="text-red-400">{validation.error}</span>
        </div>
      )}
    </div>
  )
}
