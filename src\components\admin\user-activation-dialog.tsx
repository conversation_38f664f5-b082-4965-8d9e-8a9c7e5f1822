'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2, Shield, ShieldOff } from 'lucide-react'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  userActivationSchema,
  type UserActivationFormData,
} from '@/lib/validations/auth'
import {
  adminUserService,
  type UserManagementError,
} from '@/lib/services/admin-user-service'
import { AuthErrorDisplay } from '@/components/auth/auth-error-display'
import type { UserProfile } from '@/lib/supabase/auth'

interface UserActivationDialogProps {
  user: UserProfile | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: (updatedUser: UserProfile) => void
}

export function UserActivationDialog({
  user,
  open,
  onOpenChange,
  onSuccess,
}: UserActivationDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<UserManagementError | null>(null)

  const form = useForm<UserActivationFormData>({
    resolver: zodResolver(userActivationSchema),
    defaultValues: {
      userId: user?.user_id || '',
      isActive: !user?.is_active, // Toggle the current state
      reason: '',
    },
  })

  const isActivating = !user?.is_active

  async function onSubmit(data: UserActivationFormData) {
    if (!user) return

    setIsLoading(true)
    setError(null)

    try {
      const updatedUser = await adminUserService.toggleUserActivation({
        ...data,
        userId: user.user_id,
      })

      onSuccess(updatedUser)
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error('User activation error:', error)
      setError(error as UserManagementError)
    } finally {
      setIsLoading(false)
    }
  }

  function handleRetry() {
    setError(null)
    form.handleSubmit(onSubmit)()
  }

  if (!user) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isActivating ? (
              <Shield className="h-5 w-5 text-green-500" />
            ) : (
              <ShieldOff className="h-5 w-5 text-red-500" />
            )}
            {isActivating ? 'Activate' : 'Deactivate'} User Account
          </DialogTitle>
          <DialogDescription>
            {isActivating ? (
              <>
                Activate the account for{' '}
                <strong>
                  {user.first_name} {user.last_name}
                </strong>{' '}
                ({user.email}). The user will be able to sign in and access the
                system.
              </>
            ) : (
              <>
                Deactivate the account for{' '}
                <strong>
                  {user.first_name} {user.last_name}
                </strong>{' '}
                ({user.email}). The user will be signed out and unable to access
                the system.
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <AuthErrorDisplay
              error={error}
              onRetry={handleRetry}
              className="mb-4"
            />

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Reason {isActivating ? '(Optional)' : '(Required)'}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder={
                        isActivating
                          ? 'Optional reason for activation...'
                          : 'Explain why this account is being deactivated...'
                      }
                      className="min-h-[80px]"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant={isActivating ? 'default' : 'destructive'}
                disabled={isLoading}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isActivating ? 'Activate Account' : 'Deactivate Account'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
