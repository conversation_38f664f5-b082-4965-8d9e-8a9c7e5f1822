'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { StatusBadge } from '@/components/data-display/status-badge'
import {
  Eye,
  Edit,
  ChevronDown,
  ChevronUp,
  Truck,
  Ship,
  Train,
  Calendar,
  MapPin,
  Package,
  Building,
  MoreVertical,
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { CollapsibleContent } from '@/components/ui/collapsible-content'
import { useSwipeActions } from '@/hooks/use-swipe-actions'
import type { ShipmentWithRelations, ShipmentStatus } from '@/lib/supabase/types'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'

interface ShipmentCardProps {
  shipment: ShipmentWithRelations
  variant?: 'compact' | 'standard' | 'expanded' | 'priority'
  isSelected?: boolean
  isExpanded?: boolean
  onToggleSelection?: () => void
  onToggleExpansion?: () => void
  className?: string
}

// Format date helper
const formatDate = (date: string | null, short = false, includeTime = false) => {
  if (!date) return '-'
  try {
    if (includeTime) {
      return format(new Date(date), short ? 'MMM dd HH:mm' : 'MMM dd, yyyy HH:mm')
    }
    return format(new Date(date), short ? 'MMM dd' : 'MMM dd, yyyy')
  } catch {
    return '-'
  }
}

// Get transport mode icon
const getTransportIcon = (mode: string | null) => {
  switch (mode?.toLowerCase()) {
    case 'sea':
      return <Ship className="h-4 w-4" />
    case 'land':
      return <Truck className="h-4 w-4" />
    case 'rail':
      return <Train className="h-4 w-4" />
    default:
      return <Package className="h-4 w-4" />
  }
}

export function ShipmentCard({
  shipment,
  variant = 'standard',
  isSelected = false,
  isExpanded = false,
  onToggleSelection,
  onToggleExpansion,
  className,
}: ShipmentCardProps) {
  const router = useRouter()
  
  // Check if shipment was recently updated (within last 30 seconds)
  const isRecentlyUpdated = shipment.updated_at && 
    new Date().getTime() - new Date(shipment.updated_at).getTime() < 30000

  // Setup swipe actions
  const swipeActions = useSwipeActions({
    onSwipeLeft: () => {
      // Quick action: Toggle selection or mark as priority
      if (onToggleSelection) {
        onToggleSelection()
      }
    },
    onSwipeRight: () => {
      // Quick action: View details
      handleViewDetails()
    },
    onLongPress: () => {
      // Long press: Toggle expansion
      if (onToggleExpansion) {
        onToggleExpansion()
      }
    },
    threshold: 60,
    velocity: 0.4,
  })

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on interactive elements
    const target = e.target as HTMLElement
    if (target.closest('button') || target.closest('[role="button"]') || target.closest('input')) {
      return
    }
    
    if (onToggleExpansion) {
      onToggleExpansion()
    } else {
      router.push(`/shipments/${shipment.id}`)
    }
  }

  const handleViewDetails = (e?: React.MouseEvent) => {
    e?.preventDefault()
    e?.stopPropagation()
    router.push(`/shipments/${shipment.id}`)
  }

  const handleEdit = (e?: React.MouseEvent) => {
    e?.preventDefault()
    e?.stopPropagation()
    router.push(`/shipments/${shipment.id}/edit`)
  }

  // Priority variant styling
  const isPriority = variant === 'priority'
  const isCompact = variant === 'compact'

  return (
    <div className="relative">
      {/* Swipe Action Indicators */}
      {swipeActions.isPressed && (
        <>
          {/* Left swipe indicator - Selection */}
          <div 
            className={cn(
              'absolute left-0 top-0 h-full w-16 bg-blue-600 flex items-center justify-center transition-opacity z-0',
              swipeActions.swipeDistance < -30 ? 'opacity-100' : 'opacity-50'
            )}
          >
            <div className="text-white">
              {isSelected ? '✕' : '✓'}
            </div>
          </div>
          
          {/* Right swipe indicator - View Details */}
          <div 
            className={cn(
              'absolute right-0 top-0 h-full w-16 bg-green-600 flex items-center justify-center transition-opacity z-0',
              swipeActions.swipeDistance > 30 ? 'opacity-100' : 'opacity-50'
            )}
          >
            <Eye className="h-4 w-4 text-white" />
          </div>
        </>
      )}
      
      <Card
        className={cn(
          'relative z-10 transition-all duration-200 cursor-pointer select-none', // Added select-none for better touch
          'bg-slate-800 border-slate-700',
          // Enhanced mobile touch interactions
          'hover:shadow-lg hover:border-slate-600',
          'active:scale-[0.98] active:bg-slate-750', // Touch feedback
          'min-h-[120px]', // Minimum touch target size
          isRecentlyUpdated && 'border-l-4 border-l-blue-500 bg-blue-900/10',
          isPriority && 'ring-2 ring-orange-500/50 border-orange-500/50',
          isSelected && 'ring-2 ring-blue-500 border-blue-500',
          swipeActions.isPressed && 'cursor-grabbing',
          className
        )}
        onClick={handleCardClick}
        style={swipeActions.getSwipeStyle()}
        onTouchStart={swipeActions.onTouchStart}
        onTouchMove={swipeActions.onTouchMove}
        onTouchEnd={swipeActions.onTouchEnd}
        onTouchCancel={swipeActions.onTouchCancel}
        onMouseDown={swipeActions.onMouseDown}
        onMouseMove={swipeActions.onMouseMove}
        onMouseUp={swipeActions.onMouseUp}
      >
      <CardContent className="p-4">
        {/* Card Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-start space-x-3">
            {/* Selection Checkbox */}
            {onToggleSelection && (
              <div className="min-w-[44px] min-h-[44px] flex items-center justify-center">
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={onToggleSelection}
                  onClick={(e) => e.stopPropagation()}
                  className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
              </div>
            )}
            
            {/* Shipment Number and Status */}
            <div className="min-w-0 flex-1">
              <div className="flex items-start flex-col space-y-1 mb-1">
                <h3 className="font-semibold text-white text-sm break-all">
                  {shipment.shipment_number}
                </h3>
                <StatusBadge 
                  status={shipment.status as ShipmentStatus} 
                  size="sm"
                  showIcon
                />
              </div>
              
              {/* Customer Name */}
              <p className="text-slate-400 text-xs truncate">
                {shipment.customer?.name || 'No customer'}
              </p>
            </div>
          </div>

          {/* Actions Menu */}
          <div className="flex items-center space-x-2">
            {/* Transport Mode Icon */}
            <div className="text-slate-400 flex items-center">
              {getTransportIcon(shipment.transportation_mode)}
            </div>
            
            {/* More Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                <Button
                  size="sm"
                  variant="ghost"
                  className="min-w-[44px] min-h-[44px] p-0 text-slate-400 hover:text-white hover:bg-slate-700"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
                <DropdownMenuItem 
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleViewDetails(e)
                  }} 
                  className="text-slate-200"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleEdit(e)
                  }} 
                  className="text-slate-200"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Expand/Collapse Button */}
            {onToggleExpansion && (
              <Button
                size="sm"
                variant="ghost"
                data-testid="expand-toggle-button"
                onClick={(e) => {
                  e.stopPropagation()
                  onToggleExpansion()
                }}
                className="min-w-[44px] min-h-[44px] p-0 text-slate-400 hover:text-white hover:bg-slate-700"
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-3">
          {/* Route Information */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2 min-w-0 flex-1">
              <MapPin className="h-4 w-4 text-slate-400 flex-shrink-0" />
              <div className="flex items-center space-x-2 min-w-0">
                <span className="text-slate-300 font-medium truncate">
                  {shipment.origin_port?.code || '---'}
                </span>
                <span className="text-slate-500">→</span>
                <span className="text-slate-300 font-medium truncate">
                  {shipment.destination_port?.code || '---'}
                </span>
              </div>
            </div>
            
            {/* Container Count */}
            <div className="flex items-center space-x-1 text-slate-400 flex-shrink-0">
              <Package className="h-4 w-4" />
              <span className="text-xs">
                {shipment.containers?.length || 0}
              </span>
            </div>
          </div>

          {/* Key Dates */}
          {!isCompact && (
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="flex items-center space-x-2">
                <Calendar className="h-3 w-3 text-slate-400 flex-shrink-0" />
                <div>
                  <span className="text-slate-500">ETD:</span>
                  <span className="text-slate-300 ml-1">
                    {formatDate(shipment.etd_date)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="h-3 w-3 text-slate-400 flex-shrink-0" />
                <div>
                  <span className="text-slate-500">ETA:</span>
                  <span className="text-slate-300 ml-1">
                    {formatDate(shipment.eta_date)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Expanded Content */}
        {!isCompact && (
          <CollapsibleContent isOpen={isExpanded} duration={300}>
            <div className="mt-4 pt-3 border-t border-slate-700 space-y-3">
              {/* Additional Details */}
              <div className="grid grid-cols-1 gap-2 text-xs">
              {shipment.invoice_number && (
                <div className="flex justify-between">
                  <span className="text-slate-500">Invoice:</span>
                  <span className="text-slate-300">{shipment.invoice_number}</span>
                </div>
              )}
              
              {shipment.booking_number && (
                <div className="flex justify-between">
                  <span className="text-slate-500">Booking No.:</span>
                  <span className="text-slate-300">{shipment.booking_number}</span>
                </div>
              )}

              {/*(shipment.origin_port?.name || shipment.destination_port?.name) && (
                <div className="flex flex-col space-y-1">
                  <span className="text-slate-500">Route:</span>
                  <span className="text-slate-300 text-xs">
                    {shipment.origin_port?.name || 'Unknown origin'} → {shipment.destination_port?.name || 'Unknown destination'}
                  </span>
                </div>
              )*/}
              
              {shipment.transportation_mode && (
                <div className="flex justify-between">
                  <span className="text-slate-500">Transport:</span>
                  <Badge variant="outline" className="border-slate-600 text-slate-300 text-xs">
                    {shipment.transportation_mode.toUpperCase()}
                  </Badge>
                </div>
              )}
              
              {shipment.factory && (
                <div className="flex justify-between">
                  <span className="text-slate-500">Factory:</span>
                  <span className="text-slate-300">{shipment.factory.name}</span>
                </div>
              )}

              {shipment.vessel_name && (
                <div className="flex justify-between">
                  <span className="text-slate-500">Vessel Name:</span>
                  <span className="text-slate-300">{shipment.vessel_name}</span>
                </div>
              )}

              {/* Full Dates */}
              <div className="grid grid-cols-1 gap-1 mt-2">
                {shipment.closing_time && (
                  <div className="flex justify-between">
                    <span className="text-slate-500">Closing:</span>
                    <span className="text-slate-300">{formatDate(shipment.closing_time, false, true)}</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Container Details */}
            {shipment.containers && shipment.containers.length > 0 && (
              <div className="space-y-3">
                <span className="text-slate-500 text-xs">Containers:</span>
                <div className="space-y-2">
                  {shipment.containers.slice(0, 3).map((container, index) => (
                    <div
                      key={container.id}
                      className="bg-slate-700/50 rounded p-2 space-y-1"
                    >
                      <div className="flex items-center justify-between">
                        <Badge
                          variant="outline"
                          className="border-slate-600 text-slate-300 text-xs"
                        >
                          {container.container_number || `CONT-${index + 1}`}
                        </Badge>
                        {container.container_type && (
                          <span className="text-slate-400 text-xs">
                            {container.container_type}
                            {container.container_size && ` ${container.container_size}`}
                          </span>
                        )}
                      </div>
                      
                      {/* Container Details Grid */}
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        {container.seal_number && (
                          <div className="flex flex-col">
                            <span className="text-slate-500">Seal:</span>
                            <span className="text-slate-300 truncate">{container.seal_number}</span>
                          </div>
                        )}
                        
                        {container.temperature && (
                          <div className="flex flex-col">
                            <span className="text-slate-500">Temp:</span>
                            <span className="text-slate-300">{container.temperature}</span>
                          </div>
                        )}
                        
                        {container.vent && (
                          <div className="flex flex-col">
                            <span className="text-slate-500">Vent:</span>
                            <span className="text-slate-300">{container.vent}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  {shipment.containers.length > 3 && (
                    <div className="text-center">
                      <Badge
                        variant="outline"
                        className="border-slate-600 text-slate-400 text-xs px-2 py-0.5"
                      >
                        +{shipment.containers.length - 3} more containers
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="flex space-x-2 pt-2">
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation()
                  handleViewDetails(e)
                }}
                className="flex-1 border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 h-8"
              >
                <Eye className="h-3 w-3 mr-2" />
                View
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(e)
                }}
                className="flex-1 border-slate-600 bg-slate-700/50 text-slate-300 hover:bg-slate-700 hover:border-slate-500 h-8"
              >
                <Edit className="h-3 w-3 mr-2" />
                Edit
              </Button>
            </div>
            </div>
          </CollapsibleContent>
        )}
      </CardContent>
      </Card>
    </div>
  )
}

// Shipment Card List Component for easy rendering of multiple cards
interface ShipmentCardListProps {
  shipments: ShipmentWithRelations[]
  selectedShipments: Set<string>
  expandedCards: string[]
  variant?: 'compact' | 'standard' | 'expanded' | 'priority'
  onToggleSelection?: (id: string) => void
  onToggleExpansion?: (id: string) => void
  className?: string
}

export function ShipmentCardList({
  shipments,
  selectedShipments,
  expandedCards,
  variant = 'standard',
  onToggleSelection,
  onToggleExpansion,
  className,
}: ShipmentCardListProps) {
  if (shipments.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="text-slate-500 mb-4">
            <Package className="h-12 w-12 mx-auto" />
          </div>
          <p className="text-slate-300 text-lg font-medium">No shipments found</p>
          <p className="text-slate-500 mt-1">Try adjusting your search or filters</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      // Responsive grid layout following the specification
      'grid gap-4',
      'grid-cols-1', // Mobile: Single column stack
      'md:grid-cols-2', // Tablet: Two-column grid  
      'lg:grid-cols-1 xl:grid-cols-2', // Desktop: Flexible based on sidebar
      '2xl:grid-cols-3', // Wide: Three-column grid
      className
    )}>
      {shipments.map((shipment) => (
        <ShipmentCard
          key={shipment.id}
          shipment={shipment}
          variant={variant}
          isSelected={selectedShipments.has(shipment.id)}
          isExpanded={expandedCards.includes(shipment.id)}
          onToggleSelection={onToggleSelection ? () => onToggleSelection(shipment.id) : undefined}
          onToggleExpansion={onToggleExpansion ? () => onToggleExpansion(shipment.id) : undefined}
        />
      ))}
    </div>
  )
}