'use client'

import React, { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Eye,
  X,
  Building,
  Mail,
  Phone,
  Navigation,
  Calendar,
  MapPin,
  Users,
} from 'lucide-react'
import { CompanyForm } from '@/components/forms/company-form/company-form'
import { useCompaniesManagement } from '@/hooks/use-companies'
import type { Company } from '@/stores/company-store'
import type { CompanyForm as CompanyFormData } from '@/lib/validations/companies'
import { formatDistanceToNow, format } from 'date-fns'

export default function CustomersPage() {
  const {
    // Data
    companies,
    loading,
    error,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedCompanies,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createCompany,
    updateCompany,
    deleteCompany,
    bulkDeleteCompanies,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    toggleCompany,
    toggleAll,
    clearSelection,
    clearError,
    refreshCompanies,

    // Geographic utilities
    parseCoordinates,
    formatCoordinates,
  } = useCompaniesManagement()

  // Filter companies to show only customers
  const customers = useMemo(() => {
    return companies.filter(company => company.company_type === 'customer')
  }, [companies])

  // Calculate customer-specific pagination info
  const customerTotalCount = customers.length
  const customersOnPage = customers.slice(
    (currentPage - 1) * 10,
    currentPage * 10
  )

  // UI state
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Company | null>(null)
  const [viewingCustomer, setViewingCustomer] = useState<Company | null>(null)
  const [deletingCustomer, setDeletingCustomer] = useState<Company | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // Force filter to customer type when component mounts
  React.useEffect(() => {
    if (!filter.company_type || filter.company_type !== 'customer') {
      setFilter({ ...filter, company_type: 'customer' })
    }
  }, [filter, setFilter])

  // Handle create customer (force customer type)
  const handleCreate = async (data: CompanyFormData) => {
    try {
      // Force company type to customer and process data
      const processedData = {
        ...data,
        company_type: 'customer' as const,
        gps_coordinates: data.gps_coordinates
          ? JSON.stringify(data.gps_coordinates)
          : undefined,
        customer_info: data.customer_info
          ? {
              ...data.customer_info,
              incoterms: data.customer_info.incoterms ?? null,
              special_requirements: data.customer_info.special_requirements ?? null,
            }
          : data.customer_info,
        carrier_info: undefined,
        factory_info: undefined,
        forwarder_agent_info: undefined,
      }
      await createCompany(processedData)
      setShowCreateDialog(false)
    } catch (error) {
      throw error
    }
  }

  // Handle update customer
  const handleUpdate = async (data: CompanyFormData) => {
    if (!editingCustomer) return

    try {
      // Force company type to customer and process data
      const processedData = {
        ...data,
        company_type: 'customer' as const,
        gps_coordinates: data.gps_coordinates
          ? JSON.stringify(data.gps_coordinates)
          : undefined,
      }
      await updateCompany(editingCustomer.id, processedData)
      setEditingCustomer(null)
    } catch (error) {
      throw error
    }
  }

  // Handle delete customer
  const handleDelete = async (customer: Company) => {
    try {
      await deleteCompany(customer.id)
      setDeletingCustomer(null)
    } catch (error) {
      console.error('Delete failed:', error)
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      // Only delete selected customers
      const selectedCustomerIds = selectedCompanies.filter(id =>
        customers.some(customer => customer.id === id)
      )
      await bulkDeleteCompanies(selectedCustomerIds)
      setShowBulkDeleteDialog(false)
      clearSelection()
    } catch (error) {
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle sorting
  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSorting(column, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSorting(column, 'asc')
    }
  }

  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  // Format coordinates for display
  const formatCustomerCoordinates = (gpsCoordinates: string | object | null) => {
    if (!gpsCoordinates) return 'No coordinates'

    const coords = parseCoordinates(gpsCoordinates)
    if (!coords) return 'Invalid coordinates'

    return formatCoordinates(coords.lat, coords.lng)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Customers</h1>
          <p className="text-slate-400 mt-1">
            Manage customer information and relationships
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Customer
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">Create Customer</DialogTitle>
              <DialogDescription className="text-slate-400">
                Add a new customer with their information
              </DialogDescription>
            </DialogHeader>
            <CompanyForm
              onSubmit={handleCreate}
              onCancel={() => setShowCreateDialog(false)}
              isLoading={isCreating}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Search */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Search Customers</h3>
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchTerm('')}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-slate-300 text-sm font-medium">
            Search Customers
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search by name, tax ID, email, or contact..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
            />
          </div>
        </div>

        {searchTerm && (
          <div className="flex flex-wrap gap-2 pt-2">
            <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
              Search: "{searchTerm}"
              <button
                onClick={() => setSearchTerm('')}
                className="ml-2 hover:text-orange-200"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedCount} customer{selectedCount !== 1 ? 's' : ''} selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowBulkDeleteDialog(true)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Customers Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-500" />
              Customers ({customerTotalCount})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshCompanies}
              disabled={loading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {loading && customers.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-slate-300">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              <span className="ml-2">Loading customers...</span>
            </div>
          ) : customers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No customers found</p>
              <p className="text-sm text-slate-400 mt-1">
                Create your first customer to get started
              </p>
            </div>
          ) : (
            <>
              <Table className="bg-slate-800">
                <TableHeader className="bg-slate-700">
                  <TableRow className="border-slate-600 hover:bg-slate-700">
                    <TableHead className="w-12 text-slate-200">
                      <Checkbox
                        checked={isAllSelected || isPartiallySelected}
                        onCheckedChange={toggleAll}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('name')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Customer Name {getSortIcon('name')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">Contact</TableHead>
                    <TableHead className="text-slate-200">Tax ID</TableHead>
                    <TableHead className="text-slate-200">Status</TableHead>
                    <TableHead className="w-32 text-slate-200">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-slate-800">
                  {customersOnPage.map(customer => (
                    <TableRow
                      key={customer.id}
                      className="border-slate-600 hover:bg-slate-700 text-slate-200"
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected(customer.id)}
                          onCheckedChange={() => toggleCompany(customer.id)}
                          className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4" />
                          <span>{customer.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          {customer.contact_email && (
                            <div className="flex items-center space-x-1">
                              <Mail className="h-3 w-3 text-slate-400" />
                              <span className="text-slate-300">
                                {customer.contact_email}
                              </span>
                            </div>
                          )}
                          {customer.contact_phone && (
                            <div className="flex items-center space-x-1">
                              <Phone className="h-3 w-3 text-slate-400" />
                              <span className="text-slate-300">
                                {customer.contact_phone}
                              </span>
                            </div>
                          )}
                          {!customer.contact_email && !customer.contact_phone && (
                            <span className="text-slate-500">No contact info</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-slate-300 font-mono">
                          {customer.tax_id || 'Not provided'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={customer.is_active ? 'default' : 'secondary'}
                          className={
                            customer.is_active
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                          }
                        >
                          {customer.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setViewingCustomer(customer)}
                            className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingCustomer(customer)}
                            className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingCustomer(customer)}
                            className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-6 pb-6">
                  <div className="text-sm text-slate-300">
                    Page {currentPage} of {totalPages} ({customerTotalCount} customers)
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(1)}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={previousPage}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* View Customer Dialog */}
      {viewingCustomer && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-slate-800 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">
                Customer Details
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewingCustomer(null)}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Customer Avatar and Basic Info */}
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white">
                    {viewingCustomer.name}
                  </h3>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge
                      variant="outline"
                      className="border-blue-400 text-blue-200 bg-blue-500/20"
                    >
                      <Users className="h-3 w-3 mr-1" />
                      Customer
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <div
                        className={`w-2 h-2 rounded-full ${viewingCustomer.is_active ? 'bg-green-400' : 'bg-red-400'}`}
                      />
                      <span
                        className={`text-sm ${viewingCustomer.is_active ? 'text-green-300' : 'text-red-300'}`}
                      >
                        {viewingCustomer.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Building className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Tax ID</span>
                  </div>
                  <p className="text-slate-300 font-mono">
                    {viewingCustomer.tax_id || 'Not provided'}
                  </p>
                </div>

                {viewingCustomer.contact_email && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Mail className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">Email</span>
                    </div>
                    <p className="text-slate-300">
                      {viewingCustomer.contact_email}
                    </p>
                  </div>
                )}

                {viewingCustomer.contact_phone && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Phone className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">Phone</span>
                    </div>
                    <p className="text-slate-300">
                      {viewingCustomer.contact_phone}
                    </p>
                  </div>
                )}

                {(viewingCustomer.contact_person_first_name ||
                  viewingCustomer.contact_person_last_name) && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Users className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">
                        Contact Person
                      </span>
                    </div>
                    <p className="text-slate-300">
                      {[
                        viewingCustomer.contact_person_first_name,
                        viewingCustomer.contact_person_last_name,
                      ]
                        .filter(Boolean)
                        .join(' ')}
                    </p>
                  </div>
                )}
              </div>

              {/* Customer Specific Information */}
              {viewingCustomer.customer_info && (
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-white border-b border-slate-600 pb-2">
                    Customer Information
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {viewingCustomer.customer_info.incoterms && (
                      <div className="bg-slate-700 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <Building className="h-4 w-4 text-orange-500" />
                          <span className="text-white font-medium">Incoterms</span>
                        </div>
                        <p className="text-slate-300">
                          {viewingCustomer.customer_info.incoterms}
                        </p>
                      </div>
                    )}
                    
                    {viewingCustomer.customer_info.special_requirements && (
                      <div className="bg-slate-700 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <AlertCircle className="h-4 w-4 text-orange-500" />
                          <span className="text-white font-medium">Special Requirements</span>
                        </div>
                        <p className="text-slate-300">
                          {viewingCustomer.customer_info.special_requirements}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* GPS Coordinates */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Navigation className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">GPS Coordinates</span>
                </div>
                <p className="text-slate-300 font-mono text-sm">
                  {formatCustomerCoordinates(viewingCustomer.gps_coordinates)}
                </p>
              </div>

              {/* Notes */}
              {viewingCustomer.notes && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Notes</span>
                  </div>
                  <p className="text-slate-300">{viewingCustomer.notes}</p>
                </div>
              )}

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Created</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingCustomer.created_at
                      ? format(new Date(viewingCustomer.created_at), 'PPP')
                      : 'Unknown'}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {viewingCustomer.created_at
                      ? formatDistanceToNow(
                          new Date(viewingCustomer.created_at),
                          {
                            addSuffix: true,
                          }
                        )
                      : ''}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Edit className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Last Updated</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingCustomer.updated_at
                      ? format(new Date(viewingCustomer.updated_at), 'PPP')
                      : 'Unknown'}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {viewingCustomer.updated_at
                      ? formatDistanceToNow(
                          new Date(viewingCustomer.updated_at),
                          {
                            addSuffix: true,
                          }
                        )
                      : ''}
                  </p>
                </div>
              </div>

              {/* Customer ID */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Building className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Customer ID</span>
                </div>
                <p className="text-slate-300 font-mono text-sm">
                  {viewingCustomer.id}
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => setViewingCustomer(null)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Customer Dialog */}
      <Dialog
        open={!!editingCustomer}
        onOpenChange={() => setEditingCustomer(null)}
      >
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">Edit Customer</DialogTitle>
            <DialogDescription className="text-slate-400">
              Update customer information
            </DialogDescription>
          </DialogHeader>
          {editingCustomer && (
            <CompanyForm
              company={editingCustomer}
              onSubmit={handleUpdate}
              onCancel={() => setEditingCustomer(null)}
              isLoading={isUpdating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Customer Dialog */}
      <AlertDialog
        open={!!deletingCustomer}
        onOpenChange={() => setDeletingCustomer(null)}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Customer
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete &quot;{deletingCustomer?.name}
              &quot;? This action cannot be undone.
              {deletingCustomer && (
                <div className="mt-2 p-2 bg-slate-700 rounded text-sm border border-slate-600">
                  <strong className="text-white">Name:</strong>{' '}
                  <span className="text-slate-200">{deletingCustomer.name}</span>
                  <br />
                  <strong className="text-white">Type:</strong>{' '}
                  <span className="text-slate-200">Customer</span>
                  <br />
                  {deletingCustomer.tax_id && (
                    <>
                      <strong className="text-white">Tax ID:</strong>{' '}
                      <span className="text-slate-200 font-mono">
                        {deletingCustomer.tax_id}
                      </span>
                    </>
                  )}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setDeletingCustomer(null)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deletingCustomer && handleDelete(deletingCustomer)}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Customer
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Selected Customers
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete {selectedCount} selected customer
              {selectedCount !== 1 ? 's' : ''}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowBulkDeleteDialog(false)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Customers
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}