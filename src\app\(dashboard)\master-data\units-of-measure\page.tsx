'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Settings,
  Eye,
  X,
} from 'lucide-react'
import { UnitForm } from '@/components/forms/unit-form/unit-form'
import { useUnitsManagement, UNIT_CATEGORIES } from '@/hooks/use-units'
import type { UnitOfMeasure } from '@/lib/supabase/types'
import type { UnitOfMeasureForm } from '@/lib/validations/products'

export default function UnitsOfMeasurePage() {
  const {
    // Data
    units,
    loading,
    error,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedUnits,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createUnit,
    updateUnit,
    deleteUnit,
    bulkDeleteUnits,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    toggleUnit,
    toggleAll,
    clearSelection,
    clearError,
    refreshUnits,
  } = useUnitsManagement()

  // UI state
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingUnit, setEditingUnit] = useState<UnitOfMeasure | null>(null)
  const [deletingUnit, setDeletingUnit] = useState<UnitOfMeasure | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // Handle create unit
  const handleCreate = async (data: UnitOfMeasureForm) => {
    try {
      await createUnit(data)
      setShowCreateDialog(false)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle update unit
  const handleUpdate = async (data: UnitOfMeasureForm) => {
    if (!editingUnit) return

    try {
      await updateUnit(editingUnit.id, data)
      setEditingUnit(null)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle delete unit
  const handleDelete = async (unit: UnitOfMeasure) => {
    try {
      await deleteUnit(unit.id)
      setDeletingUnit(null)
    } catch (error) {
      // Error handling is done in the store
      console.error('Delete failed:', error)
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      await bulkDeleteUnits(selectedUnits)
      setShowBulkDeleteDialog(false)
      clearSelection()
    } catch (error) {
      // Error handling is done in the store
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle sorting
  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSorting(column, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSorting(column, 'asc')
    }
  }

  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  // Format conversion factor display
  const formatConversionFactor = (unit: UnitOfMeasure) => {
    if (!unit.base_unit_id) return '1.0 (base)'
    return `${unit.conversion_factor || 1}`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Units of Measure</h1>
          <p className="text-slate-400 mt-1">
            Manage measurement units and conversion factors
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Unit
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">
                Create Unit of Measure
              </DialogTitle>
              <DialogDescription className="text-slate-400">
                Add a new unit of measure with conversion factors
              </DialogDescription>
            </DialogHeader>
            <UnitForm
              onSubmit={handleCreate}
              onCancel={() => setShowCreateDialog(false)}
              isLoading={isCreating}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Search */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {(searchTerm ||
            filter.category ||
            filter.is_active !== undefined) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFilter({})
                setSearchTerm('')
              }}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Search Units
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search by name or code..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Category
            </label>
            <Select
              value={filter.category || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  category: value === 'all' ? undefined : (value as any),
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Categories
                </SelectItem>
                {UNIT_CATEGORIES.map(category => (
                  <SelectItem
                    key={category.value}
                    value={category.value}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">Status</label>
            <Select
              value={
                filter.is_active === undefined
                  ? 'all'
                  : filter.is_active
                    ? 'active'
                    : 'inactive'
              }
              onValueChange={value =>
                setFilter({
                  ...filter,
                  is_active: value === 'all' ? undefined : value === 'active',
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Status
                </SelectItem>
                <SelectItem
                  value="active"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  Active
                </SelectItem>
                <SelectItem
                  value="inactive"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  Inactive
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {(searchTerm || filter.category || filter.is_active !== undefined) && (
          <div className="flex flex-wrap gap-2 pt-2">
            {searchTerm && (
              <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-2 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.category && (
              <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                Category:{' '}
                {UNIT_CATEGORIES.find(c => c.value === filter.category)
                  ?.label || filter.category}
                <button
                  onClick={() => setFilter({ ...filter, category: undefined })}
                  className="ml-2 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.is_active !== undefined && (
              <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
                Status: {filter.is_active ? 'Active' : 'Inactive'}
                <button
                  onClick={() => setFilter({ ...filter, is_active: undefined })}
                  className="ml-2 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedCount} unit{selectedCount !== 1 ? 's' : ''} selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowBulkDeleteDialog(true)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Units Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Settings className="h-5 w-5 text-orange-500" />
              Units ({totalCount})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshUnits}
              disabled={loading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {loading && units.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-slate-300">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              <span className="ml-2">Loading units...</span>
            </div>
          ) : units.length === 0 ? (
            <div className="text-center py-8">
              <Settings className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No units found</p>
              <p className="text-sm text-slate-400 mt-1">
                Create your first unit of measure to get started
              </p>
            </div>
          ) : (
            <>
              <Table className="bg-slate-800">
                <TableHeader className="bg-slate-700">
                  <TableRow className="border-slate-600 hover:bg-slate-700">
                    <TableHead className="w-12 text-slate-200">
                      <Checkbox
                        checked={isAllSelected || isPartiallySelected}
                        onCheckedChange={toggleAll}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('code')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Code {getSortIcon('code')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('name')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Name {getSortIcon('name')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">Symbol</TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('category')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Category {getSortIcon('category')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      Conversion Factor
                    </TableHead>
                    <TableHead className="text-slate-200">Base Unit</TableHead>
                    <TableHead className="text-slate-200">Status</TableHead>
                    <TableHead className="w-24 text-slate-200">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-slate-800">
                  {units.map(unit => (
                    <TableRow
                      key={unit.id}
                      className="border-slate-600 hover:bg-slate-700 text-slate-200"
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected(unit.id)}
                          onCheckedChange={() => toggleUnit(unit.id)}
                          className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                      </TableCell>
                      <TableCell className="font-medium">{unit.code}</TableCell>
                      <TableCell>{unit.name}</TableCell>
                      <TableCell>{unit.symbol}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className="capitalize border-slate-400 text-slate-200 bg-slate-700 hover:bg-slate-600"
                        >
                          {unit.category}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatConversionFactor(unit)}</TableCell>
                      <TableCell>
                        {unit.base_unit ? (
                          <span className="text-sm text-slate-300">
                            {unit.base_unit.name} ({unit.base_unit.symbol})
                          </span>
                        ) : (
                          <span className="text-sm text-slate-400">
                            Base unit
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={unit.is_active ? 'default' : 'secondary'}
                          className={
                            unit.is_active
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                          }
                        >
                          {unit.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingUnit(unit)}
                            className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingUnit(unit)}
                            className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-6 pb-6">
                  <div className="text-sm text-slate-300">
                    Page {currentPage} of {totalPages} ({totalCount} units)
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(1)}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={previousPage}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Edit Unit Dialog */}
      <Dialog open={!!editingUnit} onOpenChange={() => setEditingUnit(null)}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">
              Edit Unit of Measure
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Update unit information and conversion factors
            </DialogDescription>
          </DialogHeader>
          {editingUnit && (
            <UnitForm
              unit={editingUnit}
              onSubmit={handleUpdate}
              onCancel={() => setEditingUnit(null)}
              isLoading={isUpdating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Unit Dialog */}
      <AlertDialog
        open={!!deletingUnit}
        onOpenChange={() => setDeletingUnit(null)}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Unit of Measure
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete &quot;{deletingUnit?.name}&quot;?
              This action cannot be undone.
              {deletingUnit && (
                <div className="mt-2 p-2 bg-slate-700 rounded text-sm border border-slate-600">
                  <strong className="text-white">Code:</strong>{' '}
                  <span className="text-slate-200">{deletingUnit.code}</span>
                  <br />
                  <strong className="text-white">Symbol:</strong>{' '}
                  <span className="text-slate-200">{deletingUnit.symbol}</span>
                  <br />
                  <strong className="text-white">Category:</strong>{' '}
                  <span className="text-slate-200">
                    {deletingUnit.category}
                  </span>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setDeletingUnit(null)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deletingUnit && handleDelete(deletingUnit)}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Unit
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Selected Units
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete {selectedCount} selected unit
              {selectedCount !== 1 ? 's' : ''}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowBulkDeleteDialog(false)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Units
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
