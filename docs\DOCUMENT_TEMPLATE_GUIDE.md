# Document Template Guide

This guide explains how to create and use document templates in the DYY Trading Management System for generating dynamic invoices, packing lists, and other shipping documents.

## Table of Contents
- [Overview](#overview)
- [Template Structure](#template-structure)
- [Basic Placeholders](#basic-placeholders)
- [Company Information](#company-information)
- [Address Formatting](#address-formatting)
- [Metadata and USCI Codes](#metadata-and-usci-codes)
- [Dynamic Product Rows](#dynamic-product-rows)
- [Container Information](#container-information)
- [Template Examples](#template-examples)
- [Advanced Features](#advanced-features)

## Overview

The document generation system uses HTML templates with special placeholders that get replaced with actual shipment data. Templates support:

- **Static content** - Fixed text and HTML structure
- **Dynamic placeholders** - Data from shipments, companies, products
- **Loop structures** - Repeating rows for products and containers
- **Address parsing** - Automatic formatting of JSON address data
- **Metadata extraction** - USCI codes and business registration info

## Template Structure

Templates are standard HTML documents with special placeholder syntax:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{shipment.invoice_number}} - Invoice</title>
    <style>
        /* Your CSS styles here */
    </style>
</head>
<body>
    <div class="container">
        <!-- Your template content with placeholders -->
    </div>
</body>
</html>
```

## Basic Placeholders

Use double curly braces `{{}}` to insert dynamic data:

### Shipment Information
```html
{{shipment.shipment_number}}       <!-- Shipment number -->
{{shipment.invoice_number}}        <!-- Invoice number -->
{{shipment.status}}                <!-- Shipment status -->
{{shipment.transportation_mode}}   <!-- sea, land, rail -->
{{shipment.vessel_name}}           <!-- Vessel name -->
{{shipment.voyage_number}}         <!-- Voyage number -->
{{shipment.booking_number}}        <!-- Booking number -->
{{shipment.etd_date}}              <!-- Estimated time of departure -->
{{shipment.eta_date}}              <!-- Estimated time of arrival -->
{{shipment.closing_time}}          <!-- Port closing time -->
{{shipment.origin_port}}           <!-- Origin port -->
{{shipment.destination_port}}      <!-- Destination port -->
```

### Financial Information
```html
{{shipment.total_value_cif}}       <!-- Total CIF value -->
{{shipment.total_value_fob}}       <!-- Total FOB value -->
{{shipment.currency_code}}         <!-- Currency (USD, THB, etc.) -->
{{shipment.total_weight}}          <!-- Total weight -->
{{shipment.total_volume}}          <!-- Total volume -->
```

### Dates and Times
```html
{{shipment.created_at}}            <!-- Creation date -->
{{shipment.updated_at}}            <!-- Last update date -->
```

## Company Information

Access company data for all parties involved in the shipment:

### Available Company Types
- `customer` - The customer who placed the order
- `shipper` - The company shipping the goods
- `consignee` - The company receiving the goods
- `notify_party` - Party to be notified
- `factory` - Manufacturing facility
- `forwarder_agent` - Forwarding agent

### Company Fields
```html
{{shipment.consignee.name}}                    <!-- Company name -->
{{shipment.consignee.company_type}}            <!-- Company type -->
{{shipment.consignee.contact_phone}}           <!-- Phone number -->
{{shipment.consignee.contact_fax}}             <!-- Fax number -->
{{shipment.consignee.contact_email}}           <!-- Email address -->
{{shipment.consignee.contact_person_first_name}} <!-- Contact first name -->
{{shipment.consignee.contact_person_last_name}}  <!-- Contact last name -->
{{shipment.consignee.tax_id}}                  <!-- Tax ID -->
```

## Address Formatting

Addresses are stored as JSON and automatically parsed into multiple formats:

### JSON Address Structure
```json
{
  "street": {"en": "Rm801,Floor 8, HeungKong Financial Center,No.3046 Xing Hai Avenue"},
  "district": {"en": "Nanshan"},
  "province": {"en": "ShenZhen"},
  "country": {"en": "China"},
  "postal_code": {"en": "518000"}
}
```

### Individual Address Components
```html
{{shipment.consignee.address.street}}          <!-- Street address -->
{{shipment.consignee.address.city}}            <!-- City -->
{{shipment.consignee.address.district}}        <!-- District -->
{{shipment.consignee.address.province}}        <!-- Province/State -->
{{shipment.consignee.address.country}}         <!-- Country -->
{{shipment.consignee.address.postal_code}}     <!-- Postal code -->
```

### Formatted Full Addresses
```html
{{shipment.consignee.address.full}}            <!-- Full address (English, comma-separated) -->
{{shipment.consignee.address.full_en}}         <!-- Full address (English) -->
{{shipment.consignee.address.full_th}}         <!-- Full address (Thai) -->
```

### Line-by-Line for Invoices
```html
{{shipment.consignee.address.line1}}           <!-- Street address -->
{{shipment.consignee.address.line2}}           <!-- District, Province -->
{{shipment.consignee.address.line3}}           <!-- Country, Postal Code -->
```

### Address Usage Example
```html
<div class="consignee">
    <h4>CONSIGNEES:</h4>
    <p>{{shipment.consignee.name}}</p>
    <p>{{shipment.consignee.address.line1}}</p>
    <p>{{shipment.consignee.address.line2}}</p>
    <p>{{shipment.consignee.address.line3}}</p>
    <p>TEL: {{shipment.consignee.contact_phone}}</p>
    <p>EMAIL: {{shipment.consignee.contact_email}}</p>
    <p>ATTN: {{shipment.consignee.contact_person_first_name}} {{shipment.consignee.contact_person_last_name}}</p>
</div>
```

## Metadata and USCI Codes

Companies can have additional metadata stored as JSON, including business registration codes:

### JSON Metadata Structure
```json
{
  "usci": "91440300MA5EKLR82L",
  "business_license": "12345678",
  "tax_registration": "TAX123456"
}
```

### USCI Code Access
```html
{{shipment.consignee.metadata.usci}}           <!-- USCI code (primary field) -->
{{shipment.consignee.metadata.usci_code}}      <!-- USCI code (alternative) -->
{{shipment.consignee.metadata.USCI}}           <!-- USCI code (uppercase) -->
```

### Other Business Registration Fields
```html
{{shipment.consignee.metadata.business_license}}      <!-- Business license number -->
{{shipment.consignee.metadata.tax_registration}}      <!-- Tax registration number -->
{{shipment.consignee.metadata.registration_number}}   <!-- General registration number -->
```

### Metadata Usage Example
```html
<div class="consignee">
    <h4>CONSIGNEES:</h4>
    <p>{{shipment.consignee.name}}</p>
    <p>{{shipment.consignee.address.full}}</p>
    <p>USCI CODE: {{shipment.consignee.metadata.usci}}</p>
    <p>TEL: {{shipment.consignee.contact_phone}}</p>
    <p>EMAIL: {{shipment.consignee.contact_email}}</p>
</div>
```

## Dynamic Product Rows

Create tables that automatically adjust to the number of products in a shipment using loop structures.

### Loop Syntax
```html
{{#each shipment.products}}
    <!-- Content for each product -->
{{/each}}
```

### Product Table Example
```html
<table>
    <thead>
        <tr>
            <th>MARK & NO</th>
            <th>QUANTITY<br>CARTONS</th>
            <th>PACKING</th>
            <th>DESCRIPTION</th>
            <th>N.W.<br>(KG)</th>
            <th>G.W.<br>(KG)</th>
            <th>UNIT PRICE<br>(THB/KG)</th>
            <th>AMOUNT<br>(THB)</th>
        </tr>
    </thead>
    <tbody>
        {{#each shipment.products}}
        <tr>
            <td>{{this.shipping_mark}}</td>
            <td>{{this.quantity}}</td>
            <td>{{this.packaging_type}}</td>
            <td class="item-description">{{this.product_description}}</td>
            <td>{{this.net_weight}}</td>
            <td>{{this.gross_weight}}</td>
            <td>{{this.unit_price_cif}}</td>
            <td>{{this.total_value_cif}}</td>
        </tr>
        {{/each}}
        
        <!-- Total row -->
        <tr>
            <td><strong>TOTAL</strong></td>
            <td><strong>{{shipment.total_quantity}}</strong></td>
            <td><strong>CARTONS</strong></td>
            <td></td>
            <td><strong>{{shipment.total_net_weight}}</strong></td>
            <td><strong>{{shipment.total_gross_weight}}</strong></td>
            <td></td>
            <td><strong>{{shipment.total_value_cif}}</strong></td>
        </tr>
    </tbody>
</table>
```

### Available Product Fields
```html
{{this.product_description}}       <!-- Product description -->
{{this.quantity}}                  <!-- Quantity -->
{{this.unit_of_measure_id}}        <!-- Unit of measure -->
{{this.unit_price_cif}}            <!-- CIF price per unit -->
{{this.unit_price_fob}}            <!-- FOB price per unit -->
{{this.total_value_cif}}           <!-- Total CIF value -->
{{this.total_value_fob}}           <!-- Total FOB value -->
{{this.gross_weight}}              <!-- Gross weight -->
{{this.net_weight}}                <!-- Net weight -->
{{this.total_gross_weight}}        <!-- Total gross weight -->
{{this.total_net_weight}}          <!-- Total net weight -->
{{this.shipping_mark}}             <!-- Shipping mark -->
{{this.mfg_date}}                  <!-- Manufacturing date -->
{{this.expire_date}}               <!-- Expiry date -->
{{this.lot_number}}                <!-- Lot number -->
{{this.packaging_type}}            <!-- Packaging type -->
{{this.quality_grade}}             <!-- Quality grade -->
```

### Loop Helper Variables
```html
{{@index}}                         <!-- 0-based index (0, 1, 2, ...) -->
{{@index1}}                        <!-- 1-based index (1, 2, 3, ...) -->
```

### Example with Row Numbers
```html
{{#each shipment.products}}
<tr>
    <td>{{@index1}}</td>                              <!-- Row number -->
    <td>{{this.shipping_mark}}</td>
    <td>{{this.quantity}}</td>
    <td>{{this.product_description}}</td>
    <td>{{this.total_value_cif}}</td>
</tr>
{{/each}}
```

## Container Information

Loop through shipping containers:

```html
<h3>Container Information</h3>
{{#each shipment.containers}}
<div class="container-info">
    <p><strong>Container {{@index1}}:</strong> {{this.container_number}}</p>
    <p>Type: {{this.container_type}} {{this.container_size}}</p>
    <p>Seal: {{this.seal_number}}</p>
    <p>Tare Weight: {{this.tare_weight}} kg</p>
    <p>Gross Weight: {{this.gross_weight}} kg</p>
    <p>Volume: {{this.volume}} m³</p>
    <p>Temperature: {{this.temperature}}°C, Vent: {{this.vent}}%</p>
    <p>Status: {{this.status}}</p>
</div>
{{/each}}
```

## Template Examples

### Complete Invoice Template
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice / Packing List</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 11px;
        }
        .container {
            width: 210mm;
            margin: 0 auto;
            border: 1px solid black;
            padding: 10px;
            box-sizing: border-box;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .consignee, .notify-party {
            border: 1px solid black;
            padding: 5px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        table, th, td {
            border: 1px solid black;
        }
        th, td {
            padding: 4px;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
            font-size: 10px;
        }
        .item-description {
            text-align: left;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h3>{{shipment.shipper.name}}</h3>
        <p>{{shipment.shipper.address.full}}</p>
        <p>TEL: {{shipment.shipper.contact_phone}} FAX: {{shipment.shipper.contact_fax}}</p>
    </div>

    <div class="invoice-title">
        <p>INVOICE / PACKING LIST</p>
    </div>

    <div class="invoice-details">
        <div></div>
        <div>
            <p><strong>INVOICE NO.:</strong> {{shipment.invoice_number}}</p>
            <p><strong>DATE:</strong> {{shipment.created_at}}</p>
            <p><strong>JOB NO.:</strong> {{shipment.shipment_number}}</p>
        </div>
    </div>

    <div class="consignee">
        <h4>CONSIGNEES:</h4>
        <p>{{shipment.consignee.name}}</p>
        <p>{{shipment.consignee.address.line1}}</p>
        <p>{{shipment.consignee.address.line2}}</p>
        <p>{{shipment.consignee.address.line3}}</p>
        <p>USCI CODE: {{shipment.consignee.metadata.usci}}</p>
        <p>TEL: {{shipment.consignee.contact_phone}}</p>
        <p>EMAIL: {{shipment.consignee.contact_email}}</p>
        <p>ATTN: {{shipment.consignee.contact_person_first_name}} {{shipment.consignee.contact_person_last_name}}</p>
    </div>

    <div class="notify-party">
        <h4>NOTIFY PARTY:</h4>
        <p>{{shipment.notify_party.name}}</p>
        <p>{{shipment.notify_party.address.full}}</p>
        <p>USCI: {{shipment.notify_party.metadata.usci}}</p>
        <p>TEL: {{shipment.notify_party.contact_phone}}</p>
        <p>EMAIL: {{shipment.notify_party.contact_email}}</p>
        <p>ATTN: {{shipment.notify_party.contact_person_first_name}} {{shipment.notify_party.contact_person_last_name}}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>MARK & NO</th>
                <th>QUANTITY<br>CARTONS</th>
                <th>PACKING</th>
                <th>DESCRIPTION</th>
                <th>N.W.<br>(KG)</th>
                <th>G.W.<br>(KG)</th>
                <th>UNIT PRICE<br>(THB/KG)</th>
                <th>AMOUNT<br>(THB)</th>
            </tr>
        </thead>
        <tbody>
            {{#each shipment.products}}
            <tr>
                <td>{{this.shipping_mark}}</td>
                <td>{{this.quantity}}</td>
                <td>{{this.packaging_type}}</td>
                <td class="item-description">{{this.product_description}}</td>
                <td>{{this.net_weight}}</td>
                <td>{{this.gross_weight}}</td>
                <td>{{this.unit_price_cif}}</td>
                <td>{{this.total_value_cif}}</td>
            </tr>
            {{/each}}
            
            <tr>
                <td><strong>TOTAL</strong></td>
                <td><strong>{{shipment.total_quantity}}</strong></td>
                <td><strong>CARTONS</strong></td>
                <td></td>
                <td><strong>{{shipment.total_net_weight}}</strong></td>
                <td><strong>{{shipment.total_gross_weight}}</strong></td>
                <td></td>
                <td><strong>{{shipment.total_value_cif}}</strong></td>
            </tr>
        </tbody>
    </table>

    <div class="shipping-details">
        <div>
            <p><strong>VESSEL NAME:</strong> {{shipment.vessel_name}}</p>
            <p><strong>F.O.B.</strong> {{shipment.origin_port}}</p>
            <p><strong>FROM {{shipment.origin_port}} TO</strong> {{shipment.destination_port}}</p>
            {{#each shipment.containers}}
            <p><strong>CONTAINER NO.</strong> {{this.container_number}}</p>
            {{/each}}
        </div>
        <div>
            <p><strong>ETD:</strong> {{shipment.etd_date}}</p>
        </div>
    </div>
    
    <div class="totals">
        <p><strong>TOTAL</strong> {{shipment.total_quantity}} CARTONS</p>
        <p><strong>TOTAL N.W.</strong> {{shipment.total_net_weight}} KGM</p>
        <p><strong>TOTAL G.W.</strong> {{shipment.total_gross_weight}} KGM</p>
    </div>
    
    {{#each shipment.containers}}
    <div class="container-specs">
        <p><strong>TEMP.</strong> {{this.temperature}}°C</p>
        <p><strong>VENT</strong> {{this.vent}}%</p>
    </div>
    {{/each}}
    
    <div class="notes">
        <p><strong>Notes:</strong></p>
        <p>{{shipment.notes}}</p>
    </div>
</div>

</body>
</html>
```

## Advanced Features

### Raw Data Access
For complex scenarios, access raw JSON data:
```html
{{shipment.consignee.address_raw}}              <!-- Original address JSON -->
{{shipment.consignee.metadata_raw}}             <!-- Original metadata JSON -->
```

### All Company Types
Replace `consignee` with any of these company types:
- `customer`
- `shipper`
- `consignee`
- `notify_party`
- `factory`
- `forwarder_agent`

### Multiple Loops
Use multiple loop structures in the same template:
```html
<!-- Products loop -->
{{#each shipment.products}}
<div>Product: {{this.product_description}}</div>
{{/each}}

<!-- Containers loop -->
{{#each shipment.containers}}
<div>Container: {{this.container_number}}</div>
{{/each}}
```

### Conditional Content
While not fully implemented, you can use empty value handling:
```html
{{#if this.lot_number}}
<p>Lot Number: {{this.lot_number}}</p>
{{/if}}
```

## Best Practices

1. **Use semantic HTML** - Structure your documents with proper HTML elements
2. **Include CSS styles** - Embed styles in the template for consistent formatting
3. **Test with real data** - Always test templates with actual shipment data
4. **Keep it simple** - Start with basic placeholders and add complexity gradually
5. **Use meaningful names** - Name your templates clearly (e.g., "CIF_Invoice_Template")
6. **Document custom fields** - If you add custom metadata, document what fields are available
7. **Responsive design** - Consider how templates will look when printed or viewed on different devices

## Troubleshooting

### Common Issues
- **Missing data shows as `[field.name]`** - The field doesn't exist in the data
- **Loops don't work** - Check array path and use `{{#each}}` syntax correctly  
- **Formatting issues** - Verify HTML structure and CSS styles
- **Address not showing** - Ensure address data exists and is properly formatted JSON

### Testing Templates
1. Create a simple template first
2. Test with known shipment data
3. Check the document generation logs for errors
4. Verify all required fields are available in your test data

This guide covers all the essential features for creating dynamic document templates in the DYY Trading Management System. For additional support or custom requirements, refer to the development team.