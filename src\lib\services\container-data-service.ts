'use client'

import { createClient } from '@/lib/supabase/client'
import type { Container, ContainerUpdateData } from '@/types/container'

export class ContainerDataService {
  private client = createClient()

  /**
   * Update container with number and seal data
   * If the container doesn't exist, it will be created
   */
  async updateContainer(
    containerId: string,
    containerData: ContainerUpdateData
  ): Promise<Container> {
    try {
      // First check if the container exists
      const existingContainer = await this.getContainer(containerId)
      
      if (!existingContainer) {
        console.warn(`Container with ID ${containerId} not found, cannot update without shipmentId`)
        throw new Error(`Container with ID ${containerId} not found. Please navigate from a valid shipment.`)
      }

      const { data, error } = await this.client
        .from('containers')
        .update({
          container_number: containerData.container_number,
          seal_number: containerData.seal_number,
          container_type: containerData.container_type,
          container_size: containerData.container_size,
          updated_at: new Date().toISOString()
        })
        .eq('id', containerId)
        .select()

      if (error) {
        console.error('Container update error:', error)
        throw new Error(`Failed to update container: ${error.message}`)
      }

      if (!data || data.length === 0) {
        throw new Error(`No rows were updated. Container ${containerId} may not exist or you may not have permission to update it.`)
      }

      const updatedContainer = data[0]
      return updatedContainer as Container
    } catch (error) {
      console.error('Container service error:', error)
      throw error instanceof Error ? error : new Error('Unknown error occurred')
    }
  }

  /**
   * Update container with number and seal data, with shipment context
   * If the container doesn't exist, it will be created for the specified shipment
   */
  async upsertContainer(
    containerId: string,
    shipmentId: string,
    containerData: ContainerUpdateData
  ): Promise<Container> {
    try {
      // First try to update the existing container
      const existingContainer = await this.getContainer(containerId)
      
      if (existingContainer) {
        // Container exists, update it
        return await this.updateContainer(containerId, containerData)
      } else {
        // Container doesn't exist, create it with the specified ID
        const { data, error } = await this.client
          .from('containers')
          .insert({
            id: containerId,
            shipment_id: shipmentId,
            container_number: containerData.container_number,
            seal_number: containerData.seal_number,
            container_type: containerData.container_type,
            container_size: containerData.container_size,
            status: 'empty' as const,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single()

        if (error) {
          console.error('Container creation error:', error)
          throw new Error(`Failed to create container: ${error.message}`)
        }

        if (!data) {
          throw new Error('No data returned from container creation')
        }

        return data as Container
      }
    } catch (error) {
      console.error('Container upsert error:', error)
      throw error instanceof Error ? error : new Error('Unknown error occurred')
    }
  }

  /**
   * Validate container number uniqueness within shipment
   */
  async validateContainerNumberUniqueness(
    containerNumber: string, 
    shipmentId: string, 
    excludeContainerId?: string
  ): Promise<boolean> {
    try {
      let query = this.client
        .from('containers')
        .select('id, container_number')
        .eq('shipment_id', shipmentId)
        .eq('container_number', containerNumber)

      if (excludeContainerId) {
        query = query.neq('id', excludeContainerId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Container uniqueness validation error:', error)
        throw new Error(`Failed to validate container number uniqueness: ${error.message}`)
      }

      return data.length === 0
    } catch (error) {
      console.error('Container uniqueness validation error:', error)
      throw error instanceof Error ? error : new Error('Unknown error occurred')
    }
  }

  /**
   * Get container by ID
   */
  async getContainer(containerId: string): Promise<Container | null> {
    try {
      const { data, error } = await this.client
        .from('containers')
        .select('*')
        .eq('id', containerId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows found
          return null
        }
        console.error('Get container error:', error)
        throw new Error(`Failed to get container: ${error.message}`)
      }

      return data as Container
    } catch (error) {
      console.error('Get container error:', error)
      throw error instanceof Error ? error : new Error('Unknown error occurred')
    }
  }

  /**
   * Get all containers for a shipment
   */
  async getContainersByShipment(shipmentId: string): Promise<Container[]> {
    try {
      const { data, error } = await this.client
        .from('containers')
        .select('*')
        .eq('shipment_id', shipmentId)
        .order('created_at', { ascending: true })

      if (error) {
        console.error('Get containers by shipment error:', error)
        throw new Error(`Failed to get containers: ${error.message}`)
      }

      return (data || []) as Container[]
    } catch (error) {
      console.error('Get containers by shipment error:', error)
      throw error instanceof Error ? error : new Error('Unknown error occurred')
    }
  }

  /**
   * Create a new container for a shipment
   */
  async createContainer(
    shipmentId: string,
    containerData: Partial<ContainerUpdateData>
  ): Promise<Container> {
    try {
      const { data, error } = await this.client
        .from('containers')
        .insert({
          shipment_id: shipmentId,
          container_number: containerData.container_number,
          seal_number: containerData.seal_number,
          container_type: containerData.container_type,
          container_size: containerData.container_size,
          status: 'empty' as const,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('Container creation error:', error)
        throw new Error(`Failed to create container: ${error.message}`)
      }

      if (!data) {
        throw new Error('No data returned from container creation')
      }

      return data as Container
    } catch (error) {
      console.error('Container creation error:', error)
      throw error instanceof Error ? error : new Error('Unknown error occurred')
    }
  }

  /**
   * Check if container number exists in any shipment (global check)
   */
  async isContainerNumberInUse(containerNumber: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from('containers')
        .select('id')
        .eq('container_number', containerNumber)
        .limit(1)

      if (error) {
        console.error('Container number check error:', error)
        throw new Error(`Failed to check container number: ${error.message}`)
      }

      return data.length > 0
    } catch (error) {
      console.error('Container number check error:', error)
      throw error instanceof Error ? error : new Error('Unknown error occurred')
    }
  }
}