'use client'

import { useState, useRef, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { createClient } from '@/lib/supabase/client'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import {
  Upload,
  X,
  Image as ImageIcon,
  AlertCircle,
  CheckCircle,
  Loader2,
  FileImage,
  Plus
} from 'lucide-react'

// Photo upload validation schema
const photoUploadSchema = z.object({
  files: z.array(z.instanceof(File)).min(1, 'At least one photo is required'),
  notes: z.string().optional(),
})

type PhotoUploadForm = z.infer<typeof photoUploadSchema>

interface PhotoUploadFormProps {
  shipmentId: string
  statusHistoryId: string
  statusName: string
  onSuccess?: (uploadedPhotos: any[]) => void
  onCancel?: () => void
  className?: string
}

interface UploadProgress {
  fileName: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
  url?: string
}

export function PhotoUploadForm({
  shipmentId,
  statusHistoryId,
  statusName,
  onSuccess,
  onCancel,
  className = '',
}: PhotoUploadFormProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = createClient()

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<PhotoUploadForm>({
    resolver: zodResolver(photoUploadSchema),
    defaultValues: {
      files: [],
      notes: '',
    },
  })

  // File validation
  const validateFile = (file: File): string | null => {
    const maxSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    
    if (!allowedTypes.includes(file.type)) {
      return 'Only JPEG, PNG, and WebP images are allowed'
    }
    
    if (file.size > maxSize) {
      return 'File size must be less than 10MB'
    }
    
    return null
  }

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files)
    const validFiles: File[] = []
    const errors: string[] = []

    fileArray.forEach(file => {
      const error = validateFile(file)
      if (error) {
        errors.push(`${file.name}: ${error}`)
      } else {
        validFiles.push(file)
      }
    })

    if (errors.length > 0) {
      alert(`File validation errors:\n${errors.join('\n')}`)
    }

    if (validFiles.length > 0) {
      const newFiles = [...selectedFiles, ...validFiles]
      setSelectedFiles(newFiles)
      setValue('files', newFiles)
      
      // Initialize upload progress
      const newProgress = validFiles.map(file => ({
        fileName: file.name,
        progress: 0,
        status: 'pending' as const,
      }))
      setUploadProgress(prev => [...prev, ...newProgress])
    }
  }, [selectedFiles, setValue])

  // Handle file input change
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      handleFileSelect(files)
    }
  }

  // Handle drag and drop
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)
    
    const files = event.dataTransfer.files
    if (files) {
      handleFileSelect(files)
    }
  }

  // Remove file
  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index)
    setSelectedFiles(newFiles)
    setValue('files', newFiles)
    
    const newProgress = uploadProgress.filter((_, i) => i !== index)
    setUploadProgress(newProgress)
  }

  // Upload single file to Supabase Storage
  const uploadFile = async (file: File, index: number): Promise<string | null> => {
    try {
      // Update progress to uploading
      setUploadProgress(prev => prev.map((item, i) => 
        i === index ? { ...item, status: 'uploading', progress: 0 } : item
      ))

      // Generate unique file name
      const timestamp = Date.now()
      const fileExtension = file.name.split('.').pop()
      const fileName = `${statusHistoryId}_${timestamp}_${index}.${fileExtension}`
      const filePath = `status-photos/${shipmentId}/${fileName}`

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('status-images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
        })

      if (error) {
        throw error
      }

      // Update progress to success
      setUploadProgress(prev => prev.map((item, i) => 
        i === index ? { 
          ...item, 
          status: 'success', 
          progress: 100,
          url: data.path 
        } : item
      ))

      return data.path
    } catch (error) {
      console.error('Error uploading file:', error)
      
      // Update progress to error
      setUploadProgress(prev => prev.map((item, i) => 
        i === index ? { 
          ...item, 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Upload failed'
        } : item
      ))
      
      return null
    }
  }

  // Insert photo record into database
  const insertPhotoRecord = async (filePath: string, notes?: string) => {
    try {
      const { data, error } = await supabase
        .from('status_images')
        .insert({
          shipment_id: shipmentId,
          status_history_id: statusHistoryId,
          image_path: filePath,
          image_url: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/status-images/${filePath}`,
          file_size: null, // Could be calculated if needed
          mime_type: null, // Could be stored if needed
          metadata: notes ? { notes } : null,
          uploaded_by: (await supabase.auth.getUser()).data.user?.id || null,
        })
        .select('*')
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error inserting photo record:', error)
      throw error
    }
  }

  // Form submission handler
  const onSubmit = async (data: PhotoUploadForm) => {
    if (isUploading) return

    setIsUploading(true)
    const uploadedPhotos: any[] = []

    try {
      // Upload all files
      const uploadPromises = selectedFiles.map((file, index) => uploadFile(file, index))
      const uploadResults = await Promise.all(uploadPromises)

      // Insert successful uploads into database
      for (let i = 0; i < uploadResults.length; i++) {
        const filePath = uploadResults[i]
        if (filePath) {
          try {
            const photoRecord = await insertPhotoRecord(filePath, data.notes)
            uploadedPhotos.push(photoRecord)
          } catch (error) {
            console.error('Error inserting photo record:', error)
          }
        }
      }

      // Call success callback
      if (onSuccess) {
        onSuccess(uploadedPhotos)
      }

      // Reset form
      reset()
      setSelectedFiles([])
      setUploadProgress([])
      
    } catch (error) {
      console.error('Error during upload:', error)
    } finally {
      setIsUploading(false)
    }
  }

  const hasErrors = uploadProgress.some(item => item.status === 'error')
  const allComplete = uploadProgress.length > 0 && uploadProgress.every(item => item.status === 'success')

  return (
    <Card className={`bg-slate-800 border-slate-700 ${className}`}>
      <CardHeader>
        <CardTitle className="text-slate-200 flex items-center gap-2">
          <Upload className="h-5 w-5 text-orange-500" />
          Upload Photos
          <Badge variant="secondary" className="bg-slate-700 text-slate-300">
            {statusName}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* File Upload Area */}
          <div className="space-y-4">
            <Label className="text-slate-200">Photos *</Label>
            
            {/* Drag and Drop Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
                dragOver
                  ? 'border-orange-500 bg-orange-500/10'
                  : 'border-slate-600 hover:border-slate-500'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="flex flex-col items-center space-y-2">
                <Upload className="h-12 w-12 text-slate-400" />
                <div className="space-y-1">
                  <p className="text-slate-200">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-sm text-slate-400">
                    JPEG, PNG, WebP up to 10MB each
                  </p>
                </div>
              </div>
            </div>

            <Input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/jpeg,image/jpg,image/png,image/webp"
              onChange={handleFileInputChange}
              className="hidden"
            />

            {errors.files && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errors.files.message}</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="space-y-4">
              <Label className="text-slate-200">Selected Photos ({selectedFiles.length})</Label>
              <div className="space-y-2">
                {selectedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-3 p-3 bg-slate-700/50 rounded-lg"
                  >
                    <FileImage className="h-8 w-8 text-orange-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-slate-200 truncate">{file.name}</p>
                      <p className="text-xs text-slate-400">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      
                      {/* Upload Progress */}
                      {uploadProgress[index] && (
                        <div className="mt-2">
                          {uploadProgress[index].status === 'uploading' && (
                            <div className="space-y-1">
                              <Progress 
                                value={uploadProgress[index].progress} 
                                className="h-2"
                              />
                              <p className="text-xs text-slate-400">
                                Uploading... {uploadProgress[index].progress}%
                              </p>
                            </div>
                          )}
                          {uploadProgress[index].status === 'success' && (
                            <div className="flex items-center space-x-1 text-green-400">
                              <CheckCircle className="h-3 w-3" />
                              <span className="text-xs">Uploaded successfully</span>
                            </div>
                          )}
                          {uploadProgress[index].status === 'error' && (
                            <div className="flex items-center space-x-1 text-red-400">
                              <AlertCircle className="h-3 w-3" />
                              <span className="text-xs">
                                {uploadProgress[index].error || 'Upload failed'}
                              </span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {!isUploading && uploadProgress[index]?.status !== 'uploading' && (
                      <Button
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={() => removeFile(index)}
                        className="h-8 w-8 p-0 hover:bg-red-500/20 text-red-400"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label className="text-slate-200">Notes (Optional)</Label>
            <Textarea
              {...register('notes')}
              placeholder="Add any additional notes about these photos..."
              rows={3}
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
            />
            {errors.notes && (
              <p className="text-sm text-red-400">{errors.notes.message}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex space-x-3 pt-4">
            <Button
              type="submit"
              disabled={isUploading || selectedFiles.length === 0}
              className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Photos
                </>
              )}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isUploading}
                className="bg-slate-600 border-slate-500 text-slate-200 hover:bg-slate-500"
              >
                Cancel
              </Button>
            )}
          </div>

          {/* Status Messages */}
          {hasErrors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Some photos failed to upload. Please try again.
              </AlertDescription>
            </Alert>
          )}

          {allComplete && (
            <Alert className="bg-green-500/20 border-green-400">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <AlertDescription className="text-green-300">
                All photos uploaded successfully!
              </AlertDescription>
            </Alert>
          )}
        </form>
      </CardContent>
    </Card>
  )
}