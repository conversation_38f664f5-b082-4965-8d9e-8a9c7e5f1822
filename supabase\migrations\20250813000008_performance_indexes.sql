-- Performance Indexes
-- Story 1.2: Database Schema & RLS Foundation
-- This migration creates additional performance indexes for optimized queries

-- ============================================================================
-- SHIPMENTS TABLE ADDITIONAL INDEXES
-- ============================================================================

-- Core business query indexes
CREATE INDEX idx_shipments_customer_status ON shipments(customer_id, status);
CREATE INDEX idx_shipments_carrier_status ON shipments(carrier_id, status) WHERE carrier_id IS NOT NULL;
CREATE INDEX idx_shipments_factory_status ON shipments(factory_id, status) WHERE factory_id IS NOT NULL;
CREATE INDEX idx_shipments_status_updated ON shipments(status, status_updated_at);

-- Date range query indexes
CREATE INDEX idx_shipments_etd ON shipments(etd) WHERE etd IS NOT NULL;
CREATE INDEX idx_shipments_eta ON shipments(eta) WHERE eta IS NOT NULL;
CREATE INDEX idx_shipments_created_date ON shipments(DATE(created_at));

-- Multi-column search indexes
CREATE INDEX idx_shipments_customer_dates ON shipments(customer_id, etd, eta);
CREATE INDEX idx_shipments_carrier_dates ON shipments(carrier_id, etd, eta) WHERE carrier_id IS NOT NULL;

-- Shipment number pattern search
CREATE INDEX idx_shipments_number_pattern ON shipments(shipment_number text_pattern_ops);

-- Transport mode and route indexes
CREATE INDEX idx_shipments_transport_mode ON shipments(transport_mode);
CREATE INDEX idx_shipments_origin_destination ON shipments(origin_port_id, destination_port_id);

-- Financial queries
CREATE INDEX idx_shipments_value_currency ON shipments(total_value, value_currency) WHERE total_value IS NOT NULL;

-- ============================================================================
-- STATUS HISTORY TABLE INDEXES
-- ============================================================================

-- Status tracking and timeline queries
CREATE INDEX idx_status_history_shipment_timestamp ON status_history(shipment_id, timestamp);
CREATE INDEX idx_status_history_status_timestamp ON status_history(status, timestamp);
CREATE INDEX idx_status_history_updated_by ON status_history(updated_by) WHERE updated_by IS NOT NULL;

-- Recent status changes
CREATE INDEX idx_status_history_recent ON status_history(timestamp DESC) WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days';

-- System vs manual updates
CREATE INDEX idx_status_history_system_generated ON status_history(system_generated, timestamp);

-- Location-based queries
CREATE INDEX idx_status_history_gps ON status_history USING GIST(gps_coordinates) WHERE gps_coordinates IS NOT NULL;

-- ============================================================================
-- CONTAINERS TABLE INDEXES
-- ============================================================================

-- Container tracking
CREATE INDEX idx_containers_number ON containers(container_number);
CREATE INDEX idx_containers_status ON containers(container_status);
CREATE INDEX idx_containers_shipment_status ON containers(shipment_id, container_status);

-- Container specifications
CREATE INDEX idx_containers_type_size ON containers(container_type, container_size);

-- Weight and capacity queries
CREATE INDEX idx_containers_gross_weight ON containers(gross_weight_kg) WHERE gross_weight_kg IS NOT NULL;

-- Current location tracking
CREATE INDEX idx_containers_current_gps ON containers USING GIST(current_coordinates) WHERE current_coordinates IS NOT NULL;

-- Date-based container tracking
CREATE INDEX idx_containers_pickup_delivery ON containers(pickup_date, delivery_date);

-- ============================================================================
-- SHIPMENT PRODUCTS TABLE INDEXES
-- ============================================================================

-- Product analysis
CREATE INDEX idx_shipment_products_product_quantity ON shipment_products(product_id, quantity);
CREATE INDEX idx_shipment_products_shipment_product ON shipment_products(shipment_id, product_id);

-- Weight calculations
CREATE INDEX idx_shipment_products_net_weight ON shipment_products(net_weight_kg);

-- Container assignment
CREATE INDEX idx_shipment_products_container ON shipment_products(container_id) WHERE container_id IS NOT NULL;

-- Packaging analysis
CREATE INDEX idx_shipment_products_packaging ON shipment_products(packaging_type, total_packages);

-- ============================================================================
-- TRANSPORTATION TABLE INDEXES
-- ============================================================================

-- Transportation assignment
CREATE INDEX idx_transportation_carrier_driver ON transportation(carrier_id, driver_id);
CREATE INDEX idx_transportation_shipment_status ON transportation(shipment_id, transportation_status);

-- Schedule optimization
CREATE INDEX idx_transportation_scheduled_pickup ON transportation(scheduled_pickup_time) WHERE scheduled_pickup_time IS NOT NULL;
CREATE INDEX idx_transportation_scheduled_delivery ON transportation(scheduled_delivery_time) WHERE scheduled_delivery_time IS NOT NULL;

-- Location-based queries
CREATE INDEX idx_transportation_pickup_gps ON transportation USING GIST(pickup_coordinates) WHERE pickup_coordinates IS NOT NULL;
CREATE INDEX idx_transportation_delivery_gps ON transportation USING GIST(delivery_coordinates) WHERE delivery_coordinates IS NOT NULL;

-- Vehicle tracking
CREATE INDEX idx_transportation_vehicle ON transportation(vehicle_number) WHERE vehicle_number IS NOT NULL;

-- ============================================================================
-- DOCUMENTS TABLE INDEXES
-- ============================================================================

-- Document retrieval
CREATE INDEX idx_documents_shipment_type ON documents(shipment_id, document_type);
CREATE INDEX idx_documents_type_verification ON documents(document_type, is_verified);

-- File management
CREATE INDEX idx_documents_file_type ON documents(file_type);
CREATE INDEX idx_documents_file_size ON documents(file_size_bytes) WHERE file_size_bytes IS NOT NULL;

-- Version control
CREATE INDEX idx_documents_version_original ON documents(shipment_id, version, is_original);

-- Date-based queries
CREATE INDEX idx_documents_issued_valid ON documents(issued_date, valid_until);

-- Access control
CREATE INDEX idx_documents_access_public ON documents(access_level, is_public);
CREATE INDEX idx_documents_sharing ON documents(shared_with_customer, shared_with_carrier);

-- ============================================================================
-- NOTIFICATIONS TABLE INDEXES
-- ============================================================================

-- User notification queries
CREATE INDEX idx_notifications_recipient_type ON notifications(recipient_id, notification_type);
CREATE INDEX idx_notifications_recipient_priority ON notifications(recipient_id, priority DESC, created_at DESC);

-- Unread notifications
CREATE INDEX idx_notifications_recipient_unread ON notifications(recipient_id, in_app_read) WHERE in_app_read = false;

-- Notification delivery status
CREATE INDEX idx_notifications_send_status ON notifications(is_sent, scheduled_for);
CREATE INDEX idx_notifications_send_attempts ON notifications(send_attempts, last_attempt_at) WHERE send_attempts > 0;

-- Channel-specific delivery
CREATE INDEX idx_notifications_email_sent ON notifications(email_sent, email_sent_at);
CREATE INDEX idx_notifications_sms_sent ON notifications(sms_sent, sms_sent_at);

-- Shipment-related notifications
CREATE INDEX idx_notifications_shipment_type ON notifications(shipment_id, notification_type) WHERE shipment_id IS NOT NULL;

-- ============================================================================
-- RELATIONSHIP INTELLIGENCE INDEXES
-- ============================================================================

-- Customer shipper relationship optimization
CREATE INDEX idx_customer_shippers_frequency_used ON customer_shippers(customer_id, frequency_score DESC, last_used_date DESC);
CREATE INDEX idx_customer_shippers_default_active ON customer_shippers(customer_id, is_default DESC, is_active) WHERE is_active = true;

-- Consignee notify party relationship optimization
CREATE INDEX idx_consignee_notify_priority_frequency ON consignee_notify_parties(consignee_id, notification_priority, frequency_score DESC);
CREATE INDEX idx_consignee_notify_default_active ON consignee_notify_parties(consignee_id, is_default DESC, is_active) WHERE is_active = true;

-- Customer product relationship optimization
CREATE INDEX idx_customer_products_frequency_ordered ON customer_products(customer_id, frequency_score DESC, last_ordered_date DESC);
CREATE INDEX idx_customer_products_default_active ON customer_products(customer_id, is_default DESC, is_active) WHERE is_active = true;
CREATE INDEX idx_customer_products_pricing_current ON customer_products(customer_id, product_id, price_valid_from DESC) WHERE price_valid_to IS NULL OR price_valid_to >= CURRENT_DATE;

-- ============================================================================
-- DRIVER AND CARRIER OPTIMIZATION INDEXES
-- ============================================================================

-- Driver assignment optimization
CREATE INDEX idx_drivers_carrier_active ON drivers(carrier_id, is_active) WHERE is_active = true;
CREATE INDEX idx_drivers_phone_lookup ON drivers(phone);
CREATE INDEX idx_drivers_line_id ON drivers(line_id) WHERE line_id IS NOT NULL;

-- Carrier capacity planning
CREATE INDEX idx_carrier_info_fleet_capacity ON carrier_info(fleet_size, max_capacity_tons) WHERE is_active = true;
CREATE INDEX idx_carrier_info_coverage ON carrier_info(coverage_areas) WHERE is_active = true;

-- ============================================================================
-- FACTORY AND PRODUCTION INDEXES
-- ============================================================================

-- Production capacity planning
CREATE INDEX idx_factory_info_capacity_unit ON factory_info(production_capacity_monthly, production_unit_id) WHERE is_active = true;
CREATE INDEX idx_factory_info_lead_time ON factory_info(lead_time_days) WHERE is_active = true;

-- ============================================================================
-- CUSTOMER ANALYSIS INDEXES
-- ============================================================================

-- Customer segmentation
CREATE INDEX idx_customer_info_type_volume ON customer_info(customer_type, annual_volume) WHERE is_active = true;
CREATE INDEX idx_customer_info_credit_limit ON customer_info(credit_limit_amount, credit_limit_currency) WHERE is_active = true;

-- ============================================================================
-- PORTS AND GEOGRAPHIC INDEXES
-- ============================================================================

-- Geographic queries optimization
CREATE INDEX idx_ports_country_type ON ports(country_code, port_type) WHERE is_active = true;
CREATE INDEX idx_ports_name_search ON ports(name text_pattern_ops) WHERE is_active = true;

-- Company geographic distribution
CREATE INDEX idx_companies_country_type ON companies(country_code, company_type) WHERE is_active = true;

-- ============================================================================
-- STATUS IMAGES OPTIMIZATION
-- ============================================================================

-- Image retrieval by shipment and category
CREATE INDEX idx_status_images_shipment_category ON status_images(shipment_id, image_category, taken_at DESC);
CREATE INDEX idx_status_images_status_history ON status_images(status_history_id);

-- Image processing workflow
CREATE INDEX idx_status_images_processing ON status_images(is_processed, taken_at) WHERE is_processed = false;

-- Image metadata
CREATE INDEX idx_status_images_taken_by ON status_images(taken_by) WHERE taken_by IS NOT NULL;

-- ============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- ============================================================================

-- Shipment dashboard queries
CREATE INDEX idx_shipments_dashboard_customer ON shipments(customer_id, status, etd DESC, created_at DESC);
CREATE INDEX idx_shipments_dashboard_carrier ON shipments(carrier_id, status, etd DESC, created_at DESC) WHERE carrier_id IS NOT NULL;

-- Multi-stakeholder shipment views
CREATE INDEX idx_shipments_all_stakeholders ON shipments(customer_id, shipper_id, consignee_id, carrier_id) WHERE carrier_id IS NOT NULL;

-- Time-based analysis
CREATE INDEX idx_shipments_monthly_analysis ON shipments(DATE_TRUNC('month', created_at), customer_id, status);

-- Financial reporting
CREATE INDEX idx_shipments_financial_summary ON shipments(customer_id, value_currency, DATE_TRUNC('month', created_at)) WHERE total_value IS NOT NULL;

-- ============================================================================
-- PARTIAL INDEXES FOR PERFORMANCE
-- ============================================================================

-- Active records only
CREATE INDEX idx_companies_active_by_type ON companies(company_type, name) WHERE is_active = true;
CREATE INDEX idx_products_active_by_category ON products(category, name) WHERE is_active = true;
CREATE INDEX idx_profiles_active_by_role ON profiles(role, company_id) WHERE is_active = true;

-- Pending/incomplete shipments
CREATE INDEX idx_shipments_incomplete ON shipments(status, etd) WHERE status NOT IN ('completed', 'cancelled');

-- Recent activity (last 90 days)
CREATE INDEX idx_shipments_recent_activity ON shipments(customer_id, status, created_at) WHERE created_at >= CURRENT_DATE - INTERVAL '90 days';

-- High-value shipments
CREATE INDEX idx_shipments_high_value ON shipments(total_value DESC, customer_id) WHERE total_value >= 50000;

-- ============================================================================
-- EXPRESSION INDEXES FOR CALCULATED FIELDS
-- ============================================================================

-- Calculated shipping duration
CREATE INDEX idx_shipments_duration ON shipments((eta - etd)) WHERE eta IS NOT NULL AND etd IS NOT NULL;

-- Days since shipment creation
CREATE INDEX idx_shipments_age ON shipments((CURRENT_DATE - DATE(created_at)));

-- Container utilization
CREATE INDEX idx_containers_utilization ON containers((net_weight_kg / NULLIF(gross_weight_kg, 0))) WHERE gross_weight_kg > 0;

-- ============================================================================
-- FULL TEXT SEARCH INDEXES
-- ============================================================================

-- Company name search
CREATE INDEX idx_companies_name_fts ON companies USING gin(to_tsvector('english', name));

-- Product name and description search
CREATE INDEX idx_products_search_fts ON products USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Shipment notes search
CREATE INDEX idx_shipments_notes_fts ON shipments USING gin(to_tsvector('english', COALESCE(special_instructions, '') || ' ' || COALESCE(internal_notes, '')));

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON INDEX idx_shipments_customer_status IS 'Optimizes customer dashboard queries filtering by status';
COMMENT ON INDEX idx_status_history_shipment_timestamp IS 'Optimizes shipment timeline and audit trail queries';
COMMENT ON INDEX idx_customer_shippers_frequency_used IS 'Optimizes intelligent pre-population for customer-shipper relationships';
COMMENT ON INDEX idx_notifications_recipient_unread IS 'Optimizes unread notification counts for user dashboards';
COMMENT ON INDEX idx_shipments_dashboard_customer IS 'Composite index for customer dashboard performance';
COMMENT ON INDEX idx_companies_name_fts IS 'Full-text search index for company name lookups';

-- Summary comment
COMMENT ON SCHEMA public IS 'Performance indexes implemented for efficient querying across all business entities and relationships';