-- Row Level Security Policies
-- Story 1.2: Database Schema & RLS Foundation
-- This migration enables RLS and creates comprehensive security policies

-- ============================================================================
-- ENABLE ROW LEVEL SECURITY ON ALL TABLES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE carrier_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE factory_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_shippers ENABLE ROW LEVEL SECURITY;
ALTER TABLE consignee_notify_parties ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE units_of_measure ENABLE ROW LEVEL SECURITY;
ALTER TABLE ports ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;
ALTER TABLE containers ENABLE ROW LEVEL SECURITY;
ALTER TABLE shipment_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE transportation ENABLE ROW LEVEL SECURITY;
ALTER TABLE status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE status_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- HELPER FUNCTIONS FOR RLS POLICIES
-- ============================================================================

-- Function to check if current user is admin or staff
CREATE OR REPLACE FUNCTION is_admin_or_staff()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND role IN ('admin', 'cs', 'account')
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's company ID
CREATE OR REPLACE FUNCTION get_user_company_id()
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT company_id 
        FROM profiles 
        WHERE id = auth.uid() 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS role_type AS $$
BEGIN
    RETURN (
        SELECT role 
        FROM profiles 
        WHERE id = auth.uid() 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has access to a shipment
CREATE OR REPLACE FUNCTION has_shipment_access(shipment_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_company_id UUID;
    user_role role_type;
    shipment_record RECORD;
BEGIN
    -- Admin/staff have access to everything
    IF is_admin_or_staff() THEN
        RETURN true;
    END IF;
    
    user_company_id := get_user_company_id();
    user_role := get_user_role();
    
    -- No company association
    IF user_company_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- Get shipment details
    SELECT * INTO shipment_record
    FROM shipments 
    WHERE id = shipment_id_param;
    
    -- Check if user's company is involved in the shipment
    RETURN (
        user_company_id IN (
            shipment_record.customer_id,
            shipment_record.carrier_id,
            shipment_record.factory_id,
            shipment_record.shipper_id,
            shipment_record.consignee_id,
            shipment_record.notify_party_id,
            shipment_record.forwarder_agent_id
        ) OR 
        -- Drivers can see shipments assigned to them
        (user_role = 'driver' AND EXISTS (
            SELECT 1 FROM drivers d 
            WHERE d.id = shipment_record.driver_id 
            AND d.carrier_id = user_company_id
        ))
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- PROFILES TABLE POLICIES
-- ============================================================================

-- Users can only see/update their own profile
CREATE POLICY "users_can_view_own_profile" ON profiles
    FOR SELECT
    USING (id = auth.uid());

CREATE POLICY "users_can_update_own_profile" ON profiles
    FOR UPDATE
    USING (id = auth.uid());

-- Admin/staff can see all profiles
CREATE POLICY "admin_staff_can_view_all_profiles" ON profiles
    FOR SELECT
    USING (is_admin_or_staff());

-- Admin can insert new profiles
CREATE POLICY "admin_can_insert_profiles" ON profiles
    FOR INSERT
    WITH CHECK (is_admin_or_staff());

-- Admin can update any profile
CREATE POLICY "admin_can_update_any_profile" ON profiles
    FOR UPDATE
    USING (is_admin_or_staff());

-- ============================================================================
-- COMPANIES TABLE POLICIES
-- ============================================================================

-- Admin/staff can see all companies
CREATE POLICY "admin_staff_can_view_all_companies" ON companies
    FOR SELECT
    USING (is_admin_or_staff());

-- Users can see their own company and companies they do business with
CREATE POLICY "users_can_view_related_companies" ON companies
    FOR SELECT
    USING (
        id = get_user_company_id() OR
        -- Companies involved in shipments with user's company
        EXISTS (
            SELECT 1 FROM shipments s
            WHERE get_user_company_id() IN (
                s.customer_id, s.carrier_id, s.factory_id,
                s.shipper_id, s.consignee_id, s.notify_party_id, s.forwarder_agent_id
            ) AND id IN (
                s.customer_id, s.carrier_id, s.factory_id,
                s.shipper_id, s.consignee_id, s.notify_party_id, s.forwarder_agent_id
            )
        ) OR
        -- Relationship intelligence allows viewing
        EXISTS (
            SELECT 1 FROM customer_shippers cs
            WHERE (cs.customer_id = get_user_company_id() AND cs.shipper_id = id)
            OR (cs.shipper_id = get_user_company_id() AND cs.customer_id = id)
        )
    );

-- Admin can manage companies
CREATE POLICY "admin_can_manage_companies" ON companies
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- ============================================================================
-- COMPANY INFO TABLES POLICIES
-- ============================================================================

-- Customer info policies
CREATE POLICY "admin_can_view_all_customer_info" ON customer_info
    FOR SELECT
    USING (is_admin_or_staff());

CREATE POLICY "customers_can_view_own_info" ON customer_info
    FOR SELECT
    USING (company_id = get_user_company_id());

CREATE POLICY "admin_can_manage_customer_info" ON customer_info
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- Carrier info policies
CREATE POLICY "admin_can_view_all_carrier_info" ON carrier_info
    FOR SELECT
    USING (is_admin_or_staff());

CREATE POLICY "carriers_can_view_own_info" ON carrier_info
    FOR SELECT
    USING (company_id = get_user_company_id());

CREATE POLICY "admin_can_manage_carrier_info" ON carrier_info
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- Factory info policies
CREATE POLICY "admin_can_view_all_factory_info" ON factory_info
    FOR SELECT
    USING (is_admin_or_staff());

CREATE POLICY "factories_can_view_own_info" ON factory_info
    FOR SELECT
    USING (company_id = get_user_company_id());

CREATE POLICY "admin_can_manage_factory_info" ON factory_info
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- ============================================================================
-- RELATIONSHIP TABLES POLICIES
-- ============================================================================

-- Customer shippers policies
CREATE POLICY "admin_can_view_all_customer_shippers" ON customer_shippers
    FOR SELECT
    USING (is_admin_or_staff());

CREATE POLICY "parties_can_view_own_relationships" ON customer_shippers
    FOR SELECT
    USING (customer_id = get_user_company_id() OR shipper_id = get_user_company_id());

CREATE POLICY "admin_can_manage_customer_shippers" ON customer_shippers
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- Consignee notify parties policies
CREATE POLICY "admin_can_view_all_consignee_notify_parties" ON consignee_notify_parties
    FOR SELECT
    USING (is_admin_or_staff());

CREATE POLICY "parties_can_view_notify_relationships" ON consignee_notify_parties
    FOR SELECT
    USING (consignee_id = get_user_company_id() OR notify_party_id = get_user_company_id());

CREATE POLICY "admin_can_manage_consignee_notify_parties" ON consignee_notify_parties
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- Customer products policies
CREATE POLICY "admin_can_view_all_customer_products" ON customer_products
    FOR SELECT
    USING (is_admin_or_staff());

CREATE POLICY "customers_can_view_own_products" ON customer_products
    FOR SELECT
    USING (customer_id = get_user_company_id());

CREATE POLICY "admin_can_manage_customer_products" ON customer_products
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- ============================================================================
-- MASTER DATA POLICIES (READ-ONLY FOR MOST USERS)
-- ============================================================================

-- Units of measure, ports, products - readable by all authenticated users
CREATE POLICY "authenticated_can_view_units_of_measure" ON units_of_measure
    FOR SELECT
    USING (auth.role() = 'authenticated');

CREATE POLICY "authenticated_can_view_ports" ON ports
    FOR SELECT
    USING (auth.role() = 'authenticated');

CREATE POLICY "authenticated_can_view_products" ON products
    FOR SELECT
    USING (auth.role() = 'authenticated');

-- Admin can manage master data
CREATE POLICY "admin_can_manage_units_of_measure" ON units_of_measure
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

CREATE POLICY "admin_can_manage_ports" ON ports
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

CREATE POLICY "admin_can_manage_products" ON products
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- ============================================================================
-- DRIVERS POLICIES
-- ============================================================================

-- Admin/staff can see all drivers
CREATE POLICY "admin_staff_can_view_all_drivers" ON drivers
    FOR SELECT
    USING (is_admin_or_staff());

-- Carriers can see their own drivers
CREATE POLICY "carriers_can_view_own_drivers" ON drivers
    FOR SELECT
    USING (carrier_id = get_user_company_id());

-- Drivers can see their own record
CREATE POLICY "drivers_can_view_own_record" ON drivers
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'driver'
            AND drivers.carrier_id = p.company_id
        )
    );

-- Admin and carriers can manage drivers
CREATE POLICY "admin_carriers_can_manage_drivers" ON drivers
    FOR ALL
    USING (is_admin_or_staff() OR get_user_role() = 'carrier')
    WITH CHECK (is_admin_or_staff() OR (get_user_role() = 'carrier' AND carrier_id = get_user_company_id()));

-- ============================================================================
-- SHIPMENTS POLICIES
-- ============================================================================

-- Admin/staff can see all shipments
CREATE POLICY "admin_staff_can_view_all_shipments" ON shipments
    FOR SELECT
    USING (is_admin_or_staff());

-- Users can see shipments they're involved in
CREATE POLICY "users_can_view_related_shipments" ON shipments
    FOR SELECT
    USING (has_shipment_access(id));

-- Admin/staff can manage all shipments
CREATE POLICY "admin_staff_can_manage_shipments" ON shipments
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- Customers can create shipments
CREATE POLICY "customers_can_create_shipments" ON shipments
    FOR INSERT
    WITH CHECK (
        get_user_role() = 'customer' AND 
        customer_id = get_user_company_id()
    );

-- Stakeholders can update shipments they're involved in (limited fields)
CREATE POLICY "stakeholders_can_update_related_shipments" ON shipments
    FOR UPDATE
    USING (has_shipment_access(id))
    WITH CHECK (has_shipment_access(id));

-- ============================================================================
-- CONTAINERS AND SHIPMENT PRODUCTS POLICIES
-- ============================================================================

-- Follow shipment access rules
CREATE POLICY "users_can_view_containers_for_accessible_shipments" ON containers
    FOR SELECT
    USING (has_shipment_access(shipment_id));

CREATE POLICY "users_can_view_shipment_products_for_accessible_shipments" ON shipment_products
    FOR SELECT
    USING (has_shipment_access(shipment_id));

-- Admin can manage containers and shipment products
CREATE POLICY "admin_can_manage_containers" ON containers
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

CREATE POLICY "admin_can_manage_shipment_products" ON shipment_products
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- ============================================================================
-- TRANSPORTATION POLICIES
-- ============================================================================

-- Follow shipment access rules
CREATE POLICY "users_can_view_transportation_for_accessible_shipments" ON transportation
    FOR SELECT
    USING (has_shipment_access(shipment_id));

-- Admin and carriers can manage transportation
CREATE POLICY "admin_carriers_can_manage_transportation" ON transportation
    FOR ALL
    USING (is_admin_or_staff() OR get_user_role() = 'carrier')
    WITH CHECK (is_admin_or_staff() OR get_user_role() = 'carrier');

-- Drivers can update their assigned transportation
CREATE POLICY "drivers_can_update_assigned_transportation" ON transportation
    FOR UPDATE
    USING (
        get_user_role() = 'driver' AND
        EXISTS (
            SELECT 1 FROM drivers d
            WHERE d.id = driver_id 
            AND d.carrier_id = get_user_company_id()
        )
    );

-- ============================================================================
-- STATUS HISTORY AND IMAGES POLICIES
-- ============================================================================

-- Follow shipment access rules
CREATE POLICY "users_can_view_status_history_for_accessible_shipments" ON status_history
    FOR SELECT
    USING (has_shipment_access(shipment_id));

CREATE POLICY "users_can_view_status_images_for_accessible_shipments" ON status_images
    FOR SELECT
    USING (has_shipment_access(shipment_id));

-- Users can create status updates for shipments they have access to
CREATE POLICY "users_can_create_status_updates" ON status_history
    FOR INSERT
    WITH CHECK (has_shipment_access(shipment_id));

CREATE POLICY "users_can_upload_status_images" ON status_images
    FOR INSERT
    WITH CHECK (has_shipment_access(shipment_id));

-- Admin can manage all status records
CREATE POLICY "admin_can_manage_status_history" ON status_history
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

CREATE POLICY "admin_can_manage_status_images" ON status_images
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- ============================================================================
-- DOCUMENTS POLICIES
-- ============================================================================

-- Users can view documents for shipments they have access to
CREATE POLICY "users_can_view_documents_for_accessible_shipments" ON documents
    FOR SELECT
    USING (
        has_shipment_access(shipment_id) OR
        is_public = true OR
        (access_level = 'public')
    );

-- Users can upload documents for shipments they have access to
CREATE POLICY "users_can_upload_documents" ON documents
    FOR INSERT
    WITH CHECK (has_shipment_access(shipment_id));

-- Admin can manage all documents
CREATE POLICY "admin_can_manage_documents" ON documents
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- ============================================================================
-- DOCUMENT TEMPLATES POLICIES
-- ============================================================================

-- All authenticated users can view active templates
CREATE POLICY "authenticated_can_view_active_templates" ON document_templates
    FOR SELECT
    USING (auth.role() = 'authenticated' AND is_active = true);

-- Admin can manage templates
CREATE POLICY "admin_can_manage_templates" ON document_templates
    FOR ALL
    USING (is_admin_or_staff())
    WITH CHECK (is_admin_or_staff());

-- ============================================================================
-- NOTIFICATIONS POLICIES
-- ============================================================================

-- Users can only see their own notifications
CREATE POLICY "users_can_view_own_notifications" ON notifications
    FOR SELECT
    USING (recipient_id = auth.uid());

-- Users can mark their own notifications as read
CREATE POLICY "users_can_update_own_notifications" ON notifications
    FOR UPDATE
    USING (recipient_id = auth.uid())
    WITH CHECK (recipient_id = auth.uid());

-- Admin can view all notifications
CREATE POLICY "admin_can_view_all_notifications" ON notifications
    FOR SELECT
    USING (is_admin_or_staff());

-- System can create notifications
CREATE POLICY "system_can_create_notifications" ON notifications
    FOR INSERT
    WITH CHECK (true); -- This will be restricted by application logic

-- ============================================================================
-- NOTIFICATION PREFERENCES POLICIES
-- ============================================================================

-- Users can only see/update their own preferences
CREATE POLICY "users_can_manage_own_preferences" ON notification_preferences
    FOR ALL
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Admin can view all preferences
CREATE POLICY "admin_can_view_all_preferences" ON notification_preferences
    FOR SELECT
    USING (is_admin_or_staff());

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION is_admin_or_staff() IS 'Security function to check if current user has admin or staff privileges';
COMMENT ON FUNCTION get_user_company_id() IS 'Security function to get the current user''s company ID';
COMMENT ON FUNCTION get_user_role() IS 'Security function to get the current user''s role';
COMMENT ON FUNCTION has_shipment_access(UUID) IS 'Security function to check if user has access to a specific shipment';

-- Summary comment
COMMENT ON SCHEMA public IS 'Row Level Security policies implemented for multi-tenant access control with role-based permissions';