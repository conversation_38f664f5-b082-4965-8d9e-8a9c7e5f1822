import { useEffect, useMemo, useCallback } from 'react'
import {
  useCustomerConsigneeStore,
  useCustomerConsigneesLoading,
  useCustomerConsigneesError,
  useCustomerConsigneeActions,
  type CustomerConsignee,
  type CustomerConsigneeInsert,
  type CustomerConsigneeUpdate,
  type CustomerConsigneeBulkData,
  type CustomerConsigneeBulkResult,
} from '@/stores/customer-consignee-store'
import type { Company } from '@/stores/company-store'
import type { CustomerConsigneeFilter } from '@/lib/validations/customer-consignees'

// Main hook for customer-consignee management
// Hook for shipment relationship integration (compatible with useShipmentRelationships)
export function useCustomerConsignees(customerId?: string) {
  const management = useCustomerConsigneesManagement()

  // Filter relationships for specific customer
  const customerConsignees = useMemo(() => {
    if (!customerId) return []
    return management.relationships.filter(
      rel => rel.customer_id === customerId
    )
  }, [management.relationships, customerId])

  const getConsigneesForCustomer = useCallback(
    (customerIdParam: string) => {
      return management.relationships.filter(
        rel => rel.customer_id === customerIdParam
      )
    },
    [management.relationships]
  )

  const getDefaultConsigneeForCustomer = useCallback(
    (customerIdParam: string) => {
      return (
        management.relationships.find(
          rel =>
            rel.customer_id === customerIdParam &&
            rel.is_default &&
            rel.is_active
        ) || null
      )
    },
    [management.relationships]
  )

  return {
    customerConsignees,
    loading: management.loading,
    error: management.error,
    getConsigneesForCustomer,
    getDefaultConsigneeForCustomer,
  }
}

export function useCustomerConsigneesManagement() {
  const store = useCustomerConsigneeStore()

  // Extract individual actions to avoid recreating on every render
  const fetchRelationships = useCustomerConsigneeStore(state => state.fetchRelationships)
  const createRelationshipAction = useCustomerConsigneeStore(state => state.createRelationship)
  const updateRelationshipAction = useCustomerConsigneeStore(state => state.updateRelationship)
  const deleteRelationshipAction = useCustomerConsigneeStore(state => state.deleteRelationship)
  const deleteRelationshipsAction = useCustomerConsigneeStore(state => state.deleteRelationships)
  const bulkImportRelationshipsAction = useCustomerConsigneeStore(state => state.bulkImportRelationships)
  const setFilterAction = useCustomerConsigneeStore(state => state.setFilter)
  const setSearchTermAction = useCustomerConsigneeStore(state => state.setSearchTerm)
  const setSortingAction = useCustomerConsigneeStore(state => state.setSorting)
  const setPageAction = useCustomerConsigneeStore(state => state.setPage)
  const selectRelationshipAction = useCustomerConsigneeStore(state => state.selectRelationship)
  const deselectRelationshipAction = useCustomerConsigneeStore(state => state.deselectRelationship)
  const clearSelectionAction = useCustomerConsigneeStore(state => state.clearSelection)
  const clearErrorAction = useCustomerConsigneeStore(state => state.clearError)
  const subscribeToRelationshipsAction = useCustomerConsigneeStore(state => state.subscribeToRelationships)
  const getCustomerOptionsAction = useCustomerConsigneeStore(state => state.getCustomerOptions)
  const getConsigneeOptionsAction = useCustomerConsigneeStore(state => state.getConsigneeOptions)

  // Auto-fetch on mount and when filters change
  useEffect(() => {
    if (store.relationships.length === 0 && !store.loading) {
      fetchRelationships()
    }
  }, [fetchRelationships, store.relationships.length, store.loading])

  // Computed values for pagination
  const totalPages = useMemo(
    () => Math.ceil(store.totalCount / store.pageSize),
    [store.totalCount, store.pageSize]
  )

  const hasNextPage = useMemo(
    () => store.currentPage < totalPages,
    [store.currentPage, totalPages]
  )

  const hasPreviousPage = useMemo(
    () => store.currentPage > 1,
    [store.currentPage]
  )

  // Selection helpers
  const selectedCount = store.selectedRelationships.size

  const isSelected = (id: string) => store.selectedRelationships.has(id)

  const isAllSelected = useMemo(
    () =>
      store.relationships.length > 0 &&
      store.relationships.every(rel => store.selectedRelationships.has(rel.id)),
    [store.relationships, store.selectedRelationships]
  )

  const isPartiallySelected = useMemo(
    () =>
      selectedCount > 0 &&
      selectedCount < store.relationships.length &&
      !isAllSelected,
    [selectedCount, store.relationships.length, isAllSelected]
  )

  // Enhanced actions with optimistic updates and error recovery
  const createRelationship = useCallback(
    async (data: CustomerConsigneeInsert) => {
      try {
        return await createRelationshipAction(data)
      } catch (error) {
        // Error is already handled in the store
        throw error
      }
    },
    [createRelationshipAction]
  )

  const updateRelationship = useCallback(
    async (id: string, data: Partial<CustomerConsigneeUpdate>) => {
      try {
        return await updateRelationshipAction(id, data)
      } catch (error) {
        // Error is already handled in the store
        throw error
      }
    },
    [updateRelationshipAction]
  )

  const deleteRelationship = useCallback(
    async (id: string) => {
      try {
        await deleteRelationshipAction(id)
      } catch (error) {
        // Error is already handled in the store
        throw error
      }
    },
    [deleteRelationshipAction]
  )

  const bulkDeleteRelationships = useCallback(
    async (ids: string[]) => {
      try {
        await deleteRelationshipsAction(ids)
      } catch (error) {
        // Error is already handled in the store
        throw error
      }
    },
    [deleteRelationshipsAction]
  )

  const bulkImportRelationships = useCallback(
    async (data: CustomerConsigneeBulkData[]) => {
      try {
        return await bulkImportRelationshipsAction(data)
      } catch (error) {
        // Error is already handled in the store
        throw error
      }
    },
    [bulkImportRelationshipsAction]
  )

  // Filter and search actions
  const setFilter = useCallback(
    (filter: Partial<CustomerConsigneeFilter>) => {
      setFilterAction(filter)
    },
    [setFilterAction]
  )

  const setSearchTerm = useCallback(
    (term: string) => {
      setSearchTermAction(term)
    },
    [setSearchTermAction]
  )

  const setSorting = useCallback(
    (
      sortBy: 'customer_name' | 'consignee_name' | 'is_default' | 'is_active' | 'created_at',
      sortOrder: 'asc' | 'desc'
    ) => {
      setSortingAction(sortBy, sortOrder)
    },
    [setSortingAction]
  )

  // Pagination actions
  const setPage = useCallback(
    (page: number) => {
      setPageAction(page)
    },
    [setPageAction]
  )

  const nextPage = useCallback(() => {
    if (hasNextPage) {
      setPageAction(store.currentPage + 1)
    }
  }, [hasNextPage, store.currentPage, setPageAction])

  const previousPage = useCallback(() => {
    if (hasPreviousPage) {
      setPageAction(store.currentPage - 1)
    }
  }, [hasPreviousPage, store.currentPage, setPageAction])

  // Selection actions
  const toggleRelationship = useCallback(
    (id: string) => {
      if (isSelected(id)) {
        deselectRelationshipAction(id)
      } else {
        selectRelationshipAction(id)
      }
    },
    [isSelected, deselectRelationshipAction, selectRelationshipAction]
  )

  const toggleAll = useCallback(() => {
    if (isAllSelected) {
      clearSelectionAction()
    } else {
      store.relationships.forEach(rel => selectRelationshipAction(rel.id))
    }
  }, [isAllSelected, store.relationships, clearSelectionAction, selectRelationshipAction])

  const clearSelection = useCallback(() => {
    clearSelectionAction()
  }, [clearSelectionAction])

  // Utility actions
  const clearError = useCallback(() => {
    clearErrorAction()
  }, [clearErrorAction])

  const refreshRelationships = useCallback(() => {
    fetchRelationships()
  }, [fetchRelationships])

  // Company options helpers
  const getCustomerOptions = useCallback(async () => {
    try {
      return await getCustomerOptionsAction()
    } catch (error) {
      console.error('Error getting customer options:', error)
      return []
    }
  }, [getCustomerOptionsAction])

  const getConsigneeOptions = useCallback(async () => {
    try {
      return await getConsigneeOptionsAction()
    } catch (error) {
      console.error('Error getting consignee options:', error)
      return []
    }
  }, [getConsigneeOptionsAction])

  return {
    // Data
    relationships: store.relationships,
    loading: store.loading,
    error: store.error,

    // Pagination
    currentPage: store.currentPage,
    totalPages,
    totalCount: store.totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter: store.filter,
    searchTerm: store.searchTerm,
    sortBy: store.sortBy,
    sortOrder: store.sortOrder,

    // Selection
    selectedRelationships: store.selectedRelationships,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createRelationship,
    updateRelationship,
    deleteRelationship,
    bulkDeleteRelationships,
    bulkImportRelationships,
    isCreating: store.isCreating,
    isUpdating: store.isUpdating,
    isDeleting: store.isDeleting,
    isBulkImporting: store.isBulkImporting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    toggleRelationship,
    toggleAll,
    clearSelection,
    clearError,
    refreshRelationships,
    getCustomerOptions,
    getConsigneeOptions,
  }
}

// Specialized hook for company selection (dropdown data)
export function useCompanySelection() {
  // Extract specific functions to avoid dependency on the entire actions object
  const getCustomerOptions = useCustomerConsigneeStore(state => state.getCustomerOptions)
  const getConsigneeOptions = useCustomerConsigneeStore(state => state.getConsigneeOptions)

  const getCustomers = useCallback(async (): Promise<Company[]> => {
    try {
      return await getCustomerOptions()
    } catch (error) {
      console.error('Error fetching customers:', error)
      return []
    }
  }, [getCustomerOptions])

  const getConsignees = useCallback(async (): Promise<Company[]> => {
    try {
      return await getConsigneeOptions()
    } catch (error) {
      console.error('Error fetching consignees:', error)
      return []
    }
  }, [getConsigneeOptions])

  return {
    getCustomers,
    getConsignees,
  }
}

// Hook for real-time subscription management
export function useCustomerConsigneeSubscription() {
  const subscribeToRelationships = useCustomerConsigneeStore(state => state.subscribeToRelationships)

  useEffect(() => {
    console.log('Setting up customer-consignee subscription...')
    const unsubscribe = subscribeToRelationships()

    return () => {
      console.log('Cleaning up customer-consignee subscription...')
      unsubscribe()
    }
  }, [subscribeToRelationships])
}

// Hook for getting default consignee for customer (for shipment forms)
export function useDefaultConsignee(customerId?: string) {
  const getDefaultConsigneeForCustomer = useCustomerConsigneeStore(state => state.getDefaultConsigneeForCustomer)

  const getDefaultConsignee = useCallback(async () => {
    if (!customerId) return null
    try {
      return await getDefaultConsigneeForCustomer(customerId)
    } catch (error) {
      console.error('Error getting default consignee:', error)
      return null
    }
  }, [customerId, getDefaultConsigneeForCustomer])

  return {
    getDefaultConsignee,
  }
}