'use client'

import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type { CompanyFilter } from '@/lib/validations/companies'

// Company types (these should match database types)
export interface Company {
  id: string
  name: string
  company_type:
    | 'customer'
    | 'carrier'
    | 'factory'
    | 'shipper'
    | 'consignee'
    | 'notify_party'
    | 'forwarder_agent'
  tax_id: string | null
  contact_email: string | null
  contact_phone: string | null
  contact_fax: string | null
  contact_person_first_name: string | null
  contact_person_last_name: string | null
  address: any | null // JSONB
  gps_coordinates: string | object | null // PostGIS point as string or JSON object
  metadata: any | null // JSONB
  notes: string | null
  is_active: boolean | null
  created_at: string | null
  updated_at: string | null
  // Joined data from info tables
  customer_info?: CustomerInfo | null
  carrier_info?: CarrierInfo | null
  factory_info?: FactoryInfo | null
}

export interface CustomerInfo {
  company_id: string
  customer_type: 'regular' | 'premium' | 'vip'
  credit_limit: number | null
  incoterms: 'FOB' | 'CIF' | 'EXW' | 'CFR' | null
  special_requirements: string | null
  created_at: string | null
  updated_at: string | null
}

export interface CarrierInfo {
  company_id: string
  carrier_code: string | null
  fleet_size: number | null
  license_types: string[] | null
  coverage_areas: string[] | null
  insurance_policy_no: string | null
  insurance_expiry_date: string | null
  insurance_coverage_amount: number | null
  max_weight_capacity: number | null
  max_volume_capacity: number | null
  operating_hours: any | null // JSONB
  emergency_contact_phone: string | null
  gps_tracking_available: boolean | null
  created_at: string | null
  updated_at: string | null
}

export interface FactoryInfo {
  id: string // <-- Add this line
  company_id: string
  factory_code: string
  license_no: string
  certifications: string[] | null
  production_capacity_tons_per_day: number | null
  cold_storage_capacity_tons: number | null
  operating_hours: any | null // JSONB
  specializations: string[] | null
  quality_control_manager: string | null
  quality_control_phone: string | null
  loading_dock_count: number | null
  container_loading_time_minutes: number | null
  advance_booking_required_hours: number | null
  created_at: string | null
  updated_at: string | null
}

export interface CompanyInsert {
  id?: string
  name: string
  company_type: Company['company_type']
  tax_id?: string | null
  contact_email?: string | null
  contact_phone?: string | null
  contact_fax?: string | null
  contact_person_first_name?: string | null
  contact_person_last_name?: string | null
  address?: any | null
  gps_coordinates?: string | null
  metadata?: any | null
  notes?: string | null
  is_active?: boolean | null
  // Type-specific info
  customer_info?: Omit<CustomerInfo, 'company_id' | 'created_at' | 'updated_at'>
  carrier_info?: Omit<CarrierInfo, 'company_id' | 'created_at' | 'updated_at'>
  factory_info?: Omit<FactoryInfo, 'company_id' | 'created_at' | 'updated_at'>
}

export interface CompanyUpdate {
  id?: string
  name?: string
  company_type?: Company['company_type']
  tax_id?: string | null
  contact_email?: string | null
  contact_phone?: string | null
  contact_fax?: string | null
  contact_person_first_name?: string | null
  contact_person_last_name?: string | null
  address?: any | null
  gps_coordinates?: string | null
  metadata?: any | null
  notes?: string | null
  is_active?: boolean | null
  // Type-specific info
  customer_info?: Partial<
    Omit<CustomerInfo, 'company_id' | 'created_at' | 'updated_at'>
  >
  carrier_info?: Partial<
    Omit<CarrierInfo, 'company_id' | 'created_at' | 'updated_at'>
  >
  factory_info?: Partial<
    Omit<FactoryInfo, 'company_id' | 'created_at' | 'updated_at'>
  >
}

interface CompanyState {
  // Data state
  companies: Company[]
  loading: boolean
  error: string | null
  isAllCompaniesLoaded: boolean // Flag to track if all companies are loaded (not paginated)

  // Filter and search state
  filter: CompanyFilter
  searchTerm: string
  sortBy: 'name' | 'company_type' | 'created_at'
  sortOrder: 'asc' | 'desc'

  // Pagination state
  currentPage: number
  pageSize: number
  totalCount: number

  // UI state
  selectedCompanies: Set<string>
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean

  // Actions
  fetchCompanies: () => Promise<void>
  fetchAllCompaniesForDropdown: () => Promise<void>
  fetchCompanyById: (id: string) => Promise<Company | null>
  createCompany: (company: CompanyInsert) => Promise<Company>
  updateCompany: (
    id: string,
    updates: Partial<CompanyUpdate>
  ) => Promise<Company>
  deleteCompany: (id: string) => Promise<void>
  deleteCompanies: (ids: string[]) => Promise<void>

  // Filter and search actions
  setFilter: (filter: Partial<CompanyFilter>) => void
  setSearchTerm: (term: string) => void
  setSorting: (
    sortBy: CompanyState['sortBy'],
    sortOrder: CompanyState['sortOrder']
  ) => void

  // Pagination actions
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Selection actions
  selectCompany: (id: string) => void
  deselectCompany: (id: string) => void
  selectAllCompanies: () => void
  clearSelection: () => void

  // Utility actions
  clearError: () => void
  reset: () => void

  // Real-time subscription management
  subscribeToCompanies: () => () => void
}

const initialState = {
  companies: [],
  loading: false,
  error: null,
  isAllCompaniesLoaded: false,
  filter: {},
  searchTerm: '',
  sortBy: 'name' as const,
  sortOrder: 'asc' as const,
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  selectedCompanies: new Set<string>(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
}

export const useCompanyStore = create<CompanyState>((set, get) => ({
  ...initialState,

  // Fetch companies with filters, search, and pagination
  fetchCompanies: async () => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()
      const { filter, searchTerm, sortBy, sortOrder, currentPage, pageSize } =
        get()

      let query = supabase.from('companies').select(
        `
          *,
          customer_info(*),
          carrier_info(*),
          factory_info(*)
        `,
        { count: 'exact' }
      )

      // Apply filters
      if (filter.company_type) {
        query = query.eq('company_type', filter.company_type)
      }

      if (filter.is_active !== undefined) {
        query = query.eq('is_active', filter.is_active)
      } else {
        // Default to show only active companies
        query = query.eq('is_active', true)
      }

      // Apply search
      if (searchTerm.trim()) {
        query = query.or(
          `name.ilike.%${searchTerm}%,tax_id.ilike.%${searchTerm}%,contact_email.ilike.%${searchTerm}%,contact_person_first_name.ilike.%${searchTerm}%,contact_person_last_name.ilike.%${searchTerm}%`
        )
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })

      // Apply pagination
      const from = (currentPage - 1) * pageSize
      const to = from + pageSize - 1
      query = query.range(from, to)

      const { data, count, error } = await query

      if (error) {
        throw new Error(error.message)
      }


      set({
        companies: data || [],
        totalCount: count || 0,
        loading: false,
        isAllCompaniesLoaded: false, // This is paginated data
      })
    } catch (error) {
      console.error('Error fetching companies:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch companies',
        loading: false,
      })
    }
  },

  // Fetch all companies for dropdown (no pagination)
  fetchAllCompaniesForDropdown: async () => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()

      let query = supabase.from('companies').select(
        `
          *,
          customer_info(*),
          carrier_info(*),
          factory_info(*)
        `
      )

      // Only fetch active companies for dropdowns
      query = query.eq('is_active', true)

      // Apply sorting by name for consistent dropdown order
      query = query.order('name', { ascending: true })

      // No pagination - fetch all active companies
      const { data, error } = await query

      if (error) {
        throw new Error(error.message)
      }


      set({
        companies: data || [],
        totalCount: data?.length || 0,
        loading: false,
        isAllCompaniesLoaded: true, // This is all companies data
      })
    } catch (error) {
      console.error('Error fetching all companies for dropdown:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch companies',
        loading: false,
        isAllCompaniesLoaded: false, // Reset flag on error
      })
    }
  },

  // Fetch single company by ID
  fetchCompanyById: async (id: string) => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('companies')
        .select(
          `
          *,
          customer_info(*),
          carrier_info(*),
          factory_info(*)
        `
        )
        .eq('id', id)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    } catch (error) {
      console.error('Error fetching company:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch company',
      })
      return null
    }
  },

  // Create new company
  createCompany: async (companyData: CompanyInsert) => {
    set({ isCreating: true, error: null })

    try {
      const supabase = createClient()
      const { customer_info, carrier_info, factory_info, ...baseData } =
        companyData

      // Check for duplicate name within same company type - race condition prevention
      const { data: existing } = await supabase
        .from('companies')
        .select('id')
        .ilike('name', baseData.name)
        .eq('company_type', baseData.company_type)
        .eq('is_active', true)
        .single()

      if (existing) {
        throw new Error(`Company with name "${baseData.name}" already exists as ${baseData.company_type}`)
      }

      // Convert coordinates to PostGIS point if provided
      let gpsCoordinates = null
      if (baseData.gps_coordinates) {
        try {
          const coords = JSON.parse(baseData.gps_coordinates)
          if (coords.lat && coords.lng) {
            gpsCoordinates = `SRID=4326;POINT(${coords.lng} ${coords.lat})`
          }
        } catch {
          gpsCoordinates = baseData.gps_coordinates
        }
      }

      // Start transaction for company and info table inserts
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .insert({
          ...baseData,
          gps_coordinates: gpsCoordinates,
        })
        .select()
        .single()

      if (companyError) {
        throw new Error(companyError.message)
      }

      // Insert type-specific info based on company type
      if (company) {
        if (baseData.company_type === 'customer' && customer_info) {
          const { error: customerError } = await supabase
            .from('customer_info')
            .insert({
              company_id: company.id,
              ...customer_info,
            })

          if (customerError) {
            // Cleanup: delete the company record
            await supabase.from('companies').delete().eq('id', company.id)
            throw new Error(customerError.message)
          }
        } else if (baseData.company_type === 'carrier' && carrier_info) {
          const { error: carrierError } = await supabase
            .from('carrier_info')
            .insert({
              company_id: company.id,
              ...carrier_info,
            })

          if (carrierError) {
            await supabase.from('companies').delete().eq('id', company.id)
            throw new Error(carrierError.message)
          }
        } else if (baseData.company_type === 'factory' && factory_info) {
          const { error: factoryError } = await supabase
            .from('factory_info')
            .insert({
              company_id: company.id,
              ...factory_info,
            })

          if (factoryError) {
            await supabase.from('companies').delete().eq('id', company.id)
            throw new Error(factoryError.message)
          }
        }
      }

      // Refresh the list to show the new company
      await get().fetchCompanies()

      set({ isCreating: false })
      return company
    } catch (error) {
      console.error('Error creating company:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to create company',
        isCreating: false,
      })
      throw error
    }
  },

  // Update existing company
  updateCompany: async (id: string, updates: Partial<CompanyUpdate>) => {
    set({ isUpdating: true, error: null })

    try {
      const supabase = createClient()
      const { customer_info, carrier_info, factory_info, ...baseUpdates } =
        updates

      // If updating name, check for duplicates within same company type - race condition prevention
      if (baseUpdates.name) {
        // Get the company type to check against (either from updates or current record)
        let companyTypeToCheck = baseUpdates.company_type
        if (!companyTypeToCheck) {
          const { data: currentCompany } = await supabase
            .from('companies')
            .select('company_type')
            .eq('id', id)
            .single()
          companyTypeToCheck = currentCompany?.company_type
        }

        const { data: existing } = await supabase
          .from('companies')
          .select('id, company_type')
          .ilike('name', baseUpdates.name)
          .eq('company_type', companyTypeToCheck)
          .eq('is_active', true)
          .neq('id', id)
          .single()

        if (existing) {
          throw new Error(
            `Company with name "${baseUpdates.name}" already exists as ${existing.company_type}`
          )
        }
      }

      // Convert coordinates to PostGIS point if updating
      const updateData = { ...baseUpdates }
      if (baseUpdates.gps_coordinates) {
        try {
          const coords = JSON.parse(baseUpdates.gps_coordinates)
          if (coords.lat && coords.lng) {
            updateData.gps_coordinates = `SRID=4326;POINT(${coords.lng} ${coords.lat})`
          }
        } catch {
          updateData.gps_coordinates = baseUpdates.gps_coordinates
        }
      }

      // Update main company record
      const { error: updateError } = await supabase
        .from('companies')
        .update({ ...updateData, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (updateError) {
        throw new Error(updateError.message)
      }

      // Update type-specific info if provided
      if (customer_info) {
        // Rebuild the object to ensure only valid columns are included, removing any rogue 'id' or other fields.
        const {
          id: _id,
          company_id: _company_id,
          created_at: _created_at,
          updated_at: _updated_at,
          ...cleanCustomerInfo
        } = customer_info as any

        const { error: customerError } = await supabase
          .from('customer_info')
          .upsert(
            {
              company_id: id,
              ...cleanCustomerInfo,
              updated_at: new Date().toISOString(),
            },
            {
              onConflict: 'company_id',
            }
          )

        if (customerError) {
          throw new Error(customerError.message)
        }
      }

      if (carrier_info) {
        // Rebuild the object to ensure only valid columns are included.
        const {
          id: _id,
          company_id: _company_id,
          created_at: _created_at,
          updated_at: _updated_at,
          ...cleanCarrierInfo
        } = carrier_info as any

        const { error: carrierError } = await supabase
          .from('carrier_info')
          .upsert(
            {
              company_id: id,
              ...cleanCarrierInfo,
              updated_at: new Date().toISOString(),
            },
            {
              onConflict: 'company_id',
            }
          )

        if (carrierError) {
          throw new Error(carrierError.message)
        }
      }

      if (factory_info) {
        // // Rebuild the object to ensure only valid columns are included.
        // const {
        //   id: _id,
        //   company_id: _company_id,
        //   created_at: _created_at,
        //   updated_at: _updated_at,
        //   ...cleanFactoryInfo
        // } = factory_info as any

        // Remove system fields that shouldn't be updated
        const cleanFactoryInfo = { ...factory_info }
        delete (cleanFactoryInfo as any).company_id
        delete (cleanFactoryInfo as any).created_at
        delete (cleanFactoryInfo as any).updated_at

        const { error: factoryError } = await supabase
          .from('factory_info')
          .upsert(
            {
              company_id: id,
              ...cleanFactoryInfo,
              updated_at: new Date().toISOString(),
            },
            {
              onConflict: 'company_id',
            }
          )

        if (factoryError) {
          throw new Error(factoryError.message)
        }
      }

      // Fetch the updated company with all relations
      const { data: updatedCompany, error: fetchError } = await supabase
        .from('companies')
        .select(
          `
          *,
          customer_info(*),
          carrier_info(*),
          factory_info(*)
        `
        )
        .eq('id', id)
        .single()

      if (fetchError) {
        throw new Error(fetchError.message)
      }

      // Update the company in local state
      set(state => ({
        companies: state.companies.map(company =>
          company.id === id ? { ...company, ...updatedCompany } : company
        ),
        isUpdating: false,
      }))

      return updatedCompany
    } catch (error) {
      console.error('Error updating company:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to update company',
        isUpdating: false,
      })
      throw error
    }
  },

  // Delete single company
  deleteCompany: async (id: string) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Check if company is being used by shipments
      // NOTE: This check would be implemented once shipment tables are available

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('companies')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        companies: state.companies.filter(company => company.id !== id),
        selectedCompanies: new Set(
          [...state.selectedCompanies].filter(selectedId => selectedId !== id)
        ),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting company:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete company',
        isDeleting: false,
      })
      throw error
    }
  },

  // Delete multiple companies
  deleteCompanies: async (ids: string[]) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('companies')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .in('id', ids)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        companies: state.companies.filter(company => !ids.includes(company.id)),
        selectedCompanies: new Set(),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting companies:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete companies',
        isDeleting: false,
      })
      throw error
    }
  },

  // Filter and search actions
  setFilter: (newFilter: Partial<CompanyFilter>) => {
    set(state => ({
      filter: { ...state.filter, ...newFilter },
      currentPage: 1, // Reset to first page when filtering
    }))
    get().fetchCompanies()
  },

  setSearchTerm: (term: string) => {
    set({ searchTerm: term, currentPage: 1 })
    // Debounce the search
    setTimeout(() => {
      if (get().searchTerm === term) {
        get().fetchCompanies()
      }
    }, 300)
  },

  setSorting: (
    sortBy: CompanyState['sortBy'],
    sortOrder: CompanyState['sortOrder']
  ) => {
    set({ sortBy, sortOrder })
    get().fetchCompanies()
  },

  // Pagination actions
  setPage: (page: number) => {
    set({ currentPage: page })
    get().fetchCompanies()
  },

  setPageSize: (size: number) => {
    set({ pageSize: size, currentPage: 1 })
    get().fetchCompanies()
  },

  // Selection actions
  selectCompany: (id: string) => {
    set(state => ({
      selectedCompanies: new Set([...state.selectedCompanies, id]),
    }))
  },

  deselectCompany: (id: string) => {
    set(state => {
      const newSelection = new Set(state.selectedCompanies)
      newSelection.delete(id)
      return { selectedCompanies: newSelection }
    })
  },

  selectAllCompanies: () => {
    set(state => ({
      selectedCompanies: new Set(state.companies.map(company => company.id)),
    }))
  },

  clearSelection: () => {
    set({ selectedCompanies: new Set() })
  },

  // Utility actions
  clearError: () => {
    set({ error: null })
  },

  reset: () => {
    set(initialState)
  },

  // Real-time subscription
  subscribeToCompanies: () => {
    const supabase = createClient()

    const subscription = supabase
      .channel('companies_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'companies',
        },
        payload => {
          console.log('Company change received:', payload)
          // Refresh the list when changes occur
          get().fetchCompanies()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  },
}))

// Selector hooks for better performance
export const useCompanies = () => useCompanyStore(state => state.companies)
export const useCompaniesLoading = () => useCompanyStore(state => state.loading)
export const useCompaniesError = () => useCompanyStore(state => state.error)
export const useCompanyActions = () =>
  useCompanyStore(state => ({
    fetchCompanies: state.fetchCompanies,
    fetchAllCompaniesForDropdown: state.fetchAllCompaniesForDropdown,
    fetchCompanyById: state.fetchCompanyById,
    createCompany: state.createCompany,
    updateCompany: state.updateCompany,
    deleteCompany: state.deleteCompany,
    deleteCompanies: state.deleteCompanies,
    setFilter: state.setFilter,
    setSearchTerm: state.setSearchTerm,
    setSorting: state.setSorting,
    setPage: state.setPage,
    selectCompany: state.selectCompany,
    deselectCompany: state.deselectCompany,
    clearSelection: state.clearSelection,
    clearError: state.clearError,
    subscribeToCompanies: state.subscribeToCompanies,
  }))
