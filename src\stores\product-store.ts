'use client'

import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type {
  Product,
  ProductInsert,
  ProductUpdate,
  ProductWithUnit,
} from '@/lib/supabase/types'
import type { ProductFilter } from '@/lib/validations/products'

interface ProductState {
  // Data state
  products: ProductWithUnit[]
  loading: boolean
  error: string | null

  // Filter and search state
  filter: ProductFilter
  searchTerm: string
  sortBy: 'name' | 'code' | 'category' | 'created_at'
  sortOrder: 'asc' | 'desc'

  // Pagination state
  currentPage: number
  pageSize: number
  totalCount: number

  // UI state
  selectedProducts: Set<string>
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean

  // Actions
  fetchProducts: () => Promise<void>
  fetchProductById: (id: string) => Promise<ProductWithUnit | null>
  createProduct: (product: ProductInsert) => Promise<Product>
  updateProduct: (
    id: string,
    updates: Partial<ProductUpdate>
  ) => Promise<Product>
  deleteProduct: (id: string) => Promise<void>
  deleteProducts: (ids: string[]) => Promise<void>

  // Filter and search actions
  setFilter: (filter: Partial<ProductFilter>) => void
  setSearchTerm: (term: string) => void
  setSorting: (
    sortBy: ProductState['sortBy'],
    sortOrder: ProductState['sortOrder']
  ) => void

  // Pagination actions
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Selection actions
  selectProduct: (id: string) => void
  deselectProduct: (id: string) => void
  selectAllProducts: () => void
  clearSelection: () => void

  // Utility actions
  clearError: () => void
  reset: () => void

  // Real-time subscription management
  subscribeToProducts: () => () => void
}

const initialState = {
  products: [],
  loading: false,
  error: null,
  filter: {},
  searchTerm: '',
  sortBy: 'name' as const,
  sortOrder: 'asc' as const,
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  selectedProducts: new Set<string>(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
}

export const useProductStore = create<ProductState>((set, get) => ({
  ...initialState,

  // Fetch products with filters, search, and pagination
  fetchProducts: async () => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()
      const { filter, searchTerm, sortBy, sortOrder, currentPage, pageSize } =
        get()

      let query = supabase.from('products').select(
        `
          *,
          unit_of_measure:unit_of_measure_id (*)
        `,
        { count: 'exact' }
      )

      // Apply filters
      if (filter.category) {
        query = query.eq('category', filter.category)
      }

      if (filter.unit_of_measure_id) {
        query = query.eq('unit_of_measure_id', filter.unit_of_measure_id)
      }

      if (filter.is_active !== undefined) {
        query = query.eq('is_active', filter.is_active)
      } else {
        // Default to show only active products
        query = query.eq('is_active', true)
      }

      // Apply search
      if (searchTerm.trim()) {
        query = query.or(
          `name.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,hs_code.ilike.%${searchTerm}%`
        )
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })

      // Apply pagination
      const from = (currentPage - 1) * pageSize
      const to = from + pageSize - 1
      query = query.range(from, to)

      const { data, count, error } = await query

      if (error) {
        throw new Error(error.message)
      }

      set({
        products: data || [],
        totalCount: count || 0,
        loading: false,
      })
    } catch (error) {
      console.error('Error fetching products:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch products',
        loading: false,
      })
    }
  },

  // Fetch single product by ID
  fetchProductById: async (id: string) => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('products')
        .select(
          `
          *,
          unit_of_measure:unit_of_measure_id (*)
        `
        )
        .eq('id', id)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    } catch (error) {
      console.error('Error fetching product:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch product',
      })
      return null
    }
  },

  // Create new product
  createProduct: async (productData: ProductInsert) => {
    set({ isCreating: true, error: null })

    try {
      const supabase = createClient()

      // Check for duplicate code if provided - race condition prevention
      if (productData.code) {
        const { data: existing } = await supabase
          .from('products')
          .select('id')
          .eq('code', productData.code)
          .eq('is_active', true)
          .single()

        if (existing) {
          throw new Error(
            `Product with code "${productData.code}" already exists`
          )
        }
      }

      // Validate unit of measure exists
      if (productData.unit_of_measure_id) {
        const { data: unit, error: unitError } = await supabase
          .from('units_of_measure')
          .select('id')
          .eq('id', productData.unit_of_measure_id)
          .eq('is_active', true)
          .single()

        if (unitError || !unit) {
          throw new Error('Selected unit of measure is not valid')
        }
      }

      const { data, error } = await supabase
        .from('products')
        .insert(productData)
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      // Refresh the list to show the new product
      await get().fetchProducts()

      set({ isCreating: false })
      return data
    } catch (error) {
      console.error('Error creating product:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to create product',
        isCreating: false,
      })
      throw error
    }
  },

  // Update existing product
  updateProduct: async (id: string, updates: Partial<ProductUpdate>) => {
    set({ isUpdating: true, error: null })

    try {
      const supabase = createClient()

      // If updating code, check for duplicates - race condition prevention
      if (updates.code) {
        const { data: existing } = await supabase
          .from('products')
          .select('id')
          .eq('code', updates.code)
          .eq('is_active', true)
          .neq('id', id)
          .single()

        if (existing) {
          throw new Error(`Product with code "${updates.code}" already exists`)
        }
      }

      // Validate unit of measure exists if updating
      if (updates.unit_of_measure_id) {
        const { data: unit, error: unitError } = await supabase
          .from('units_of_measure')
          .select('id')
          .eq('id', updates.unit_of_measure_id)
          .eq('is_active', true)
          .single()

        if (unitError || !unit) {
          throw new Error('Selected unit of measure is not valid')
        }
      }

      const { data, error } = await supabase
        .from('products')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      // Update the product in the local state
      set(state => ({
        products: state.products.map(product =>
          product.id === id ? { ...product, ...data } : product
        ),
        isUpdating: false,
      }))

      return data
    } catch (error) {
      console.error('Error updating product:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to update product',
        isUpdating: false,
      })
      throw error
    }
  },

  // Delete single product
  deleteProduct: async (id: string) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Check if product is being used by shipments
      // NOTE: This check would be implemented once shipment tables are available
      // For now, we'll proceed with soft delete

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('products')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        products: state.products.filter(product => product.id !== id),
        selectedProducts: new Set(
          [...state.selectedProducts].filter(selectedId => selectedId !== id)
        ),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting product:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete product',
        isDeleting: false,
      })
      throw error
    }
  },

  // Delete multiple products
  deleteProducts: async (ids: string[]) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Check if any products are being used by shipments
      // NOTE: This check would be implemented once shipment tables are available
      // For now, we'll proceed with soft delete

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('products')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .in('id', ids)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        products: state.products.filter(product => !ids.includes(product.id)),
        selectedProducts: new Set(),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting products:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete products',
        isDeleting: false,
      })
      throw error
    }
  },

  // Filter and search actions
  setFilter: (newFilter: Partial<ProductFilter>) => {
    set(state => ({
      filter: { ...state.filter, ...newFilter },
      currentPage: 1, // Reset to first page when filtering
    }))
    get().fetchProducts()
  },

  setSearchTerm: (term: string) => {
    set({ searchTerm: term, currentPage: 1 })
    // Debounce the search - in a real app, use a debounce utility
    setTimeout(() => {
      if (get().searchTerm === term) {
        get().fetchProducts()
      }
    }, 300)
  },

  setSorting: (
    sortBy: ProductState['sortBy'],
    sortOrder: ProductState['sortOrder']
  ) => {
    set({ sortBy, sortOrder })
    get().fetchProducts()
  },

  // Pagination actions
  setPage: (page: number) => {
    set({ currentPage: page })
    get().fetchProducts()
  },

  setPageSize: (size: number) => {
    set({ pageSize: size, currentPage: 1 })
    get().fetchProducts()
  },

  // Selection actions
  selectProduct: (id: string) => {
    set(state => ({
      selectedProducts: new Set([...state.selectedProducts, id]),
    }))
  },

  deselectProduct: (id: string) => {
    set(state => {
      const newSelection = new Set(state.selectedProducts)
      newSelection.delete(id)
      return { selectedProducts: newSelection }
    })
  },

  selectAllProducts: () => {
    set(state => ({
      selectedProducts: new Set(state.products.map(product => product.id)),
    }))
  },

  clearSelection: () => {
    set({ selectedProducts: new Set() })
  },

  // Utility actions
  clearError: () => {
    set({ error: null })
  },

  reset: () => {
    set(initialState)
  },

  // Real-time subscription
  subscribeToProducts: () => {
    const supabase = createClient()

    const subscription = supabase
      .channel('products_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'products',
        },
        payload => {
          console.log('Product change received:', payload)
          // Refresh the list when changes occur
          get().fetchProducts()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  },
}))

// Selector hooks for better performance
export const useProducts = () => useProductStore(state => state.products)
export const useProductsLoading = () => useProductStore(state => state.loading)
export const useProductsError = () => useProductStore(state => state.error)
export const useProductActions = () =>
  useProductStore(state => ({
    fetchProducts: state.fetchProducts,
    fetchProductById: state.fetchProductById,
    createProduct: state.createProduct,
    updateProduct: state.updateProduct,
    deleteProduct: state.deleteProduct,
    deleteProducts: state.deleteProducts,
    setFilter: state.setFilter,
    setSearchTerm: state.setSearchTerm,
    setSorting: state.setSorting,
    setPage: state.setPage,
    selectProduct: state.selectProduct,
    deselectProduct: state.deselectProduct,
    clearSelection: state.clearSelection,
    clearError: state.clearError,
    subscribeToProducts: state.subscribeToProducts,
  }))
