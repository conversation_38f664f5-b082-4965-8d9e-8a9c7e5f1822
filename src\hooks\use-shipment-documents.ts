/**
 * Hook for fetching and managing shipment documents
 */

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'

export interface ShipmentDocument {
  id: string
  shipment_id: string
  document_type: string
  document_name: string
  document_number?: string
  file_path: string // storage_path in shipment_documents table
  file_name: string
  file_size_bytes?: number // file_size in shipment_documents table
  file_type?: string
  description?: string
  version: number
  is_original: boolean
  language: string
  issued_date?: string
  is_public: boolean
  access_level: string
  shared_with_customer: boolean
  shared_with_carrier: boolean
  is_verified: boolean
  created_at: string
  updated_at: string
  // Fields specific to shipment_documents table
  uploaded_by?: string // Reference to auth.users.id
  // Bucket information for downloads
  storage_bucket?: string // Which Supabase storage bucket the file is in
}

export interface UseShipmentDocumentsReturn {
  documents: ShipmentDocument[]
  isLoading: boolean
  error: string | null
  refreshDocuments: () => Promise<void>
  getDocumentsByCategory: (category: string) => ShipmentDocument[]
  getDocumentCount: () => number
  getDocumentCountByCategory: (category: string) => number
}

/**
 * Hook to fetch and manage documents for a shipment
 */
export function useShipmentDocuments(shipmentId: string): UseShipmentDocumentsReturn {
  const [documents, setDocuments] = useState<ShipmentDocument[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createClient()

  const fetchDocuments = useCallback(async () => {
    if (!shipmentId) return

    setIsLoading(true)
    setError(null)

    try {
      // Fetch from both tables
      const [documentsResult, shipmentDocumentsResult] = await Promise.allSettled([
        supabase
          .from('documents')
          .select('*')
          .eq('shipment_id', shipmentId)
          .order('created_at', { ascending: false }),
        supabase
          .from('shipment_documents')
          .select('*')
          .eq('shipment_id', shipmentId)
          .order('created_at', { ascending: false })
      ])

      const allDocuments: ShipmentDocument[] = []

      // Process documents table results
      if (documentsResult.status === 'fulfilled' && documentsResult.value.data) {
        // Add bucket information to documents from the documents table
        const documentsWithBucket = documentsResult.value.data.map((doc: any) => ({
          ...doc,
          storage_bucket: 'documents'
        }))
        allDocuments.push(...documentsWithBucket)
      } else if (documentsResult.status === 'rejected') {
        const error = documentsResult.reason
        if (!error.message?.includes('does not exist') && !error.message?.includes('relation')) {
          console.error('Error fetching from documents table:', error)
        }
      }

      // Process shipment_documents table results
      if (shipmentDocumentsResult.status === 'fulfilled' && shipmentDocumentsResult.value.data) {
        // Transform shipment_documents to match ShipmentDocument interface
        const transformedShipmentDocs = shipmentDocumentsResult.value.data.map((doc: any) => ({
          id: doc.id,
          shipment_id: doc.shipment_id,
          document_type: doc.document_type,
          document_name: doc.file_name, // Use file_name as document_name
          document_number: undefined,
          file_path: doc.storage_path, // Use storage_path as file_path
          file_name: doc.file_name,
          file_size_bytes: doc.file_size,
          file_type: doc.file_type,
          description: undefined,
          version: 1, // Default version for shipment_documents
          is_original: true, // Default to true for shipment_documents
          language: 'en', // Default language
          issued_date: undefined,
          is_public: false, // Default to false for security
          access_level: 'internal', // Default access level
          shared_with_customer: false, // Default sharing settings
          shared_with_carrier: false,
          is_verified: false, // Default verification status
          created_at: doc.created_at,
          updated_at: doc.updated_at,
          uploaded_by: doc.uploaded_by, // Include uploaded_by from shipment_documents
          storage_bucket: 'shipment-documents' // Specify the correct bucket for shipment documents
        }))
        allDocuments.push(...transformedShipmentDocs)
      } else if (shipmentDocumentsResult.status === 'rejected') {
        const error = shipmentDocumentsResult.reason
        if (!error.message?.includes('does not exist') && !error.message?.includes('relation')) {
          console.error('Error fetching from shipment_documents table:', error)
        }
      }

      // Sort by created_at descending
      allDocuments.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

      setDocuments(allDocuments)
    } catch (err) {
      console.error('Error fetching shipment documents:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch documents')
      setDocuments([])
    } finally {
      setIsLoading(false)
    }
  }, [shipmentId, supabase])

  // Fetch documents on mount and when shipmentId changes
  useEffect(() => {
    fetchDocuments()
  }, [fetchDocuments])

  const getDocumentsByCategory = useCallback((category: string) => {
    const categoryMap: Record<string, string[]> = {
      shipping: ['bill_of_lading', 'invoice_fob', 'invoice_cif', 'shipping_instruction', 'booking_confirmation'],
      certificates: ['phytosanitary_certificate', 'quality_certificate', 'fumigation_certificate', 'certificate_origin'],
      photos: ['photo_upload', 'loading_photo', 'product_photo', 'container_photo', 'quality_inspection']
    }
    
    const categoryTypes = categoryMap[category] || []
    return documents.filter(doc => categoryTypes.includes(doc.document_type))
  }, [documents])

  const getDocumentCount = useCallback(() => {
    return documents.length
  }, [documents])

  const getDocumentCountByCategory = useCallback((category: string) => {
    return getDocumentsByCategory(category).length
  }, [getDocumentsByCategory])

  return {
    documents,
    isLoading,
    error,
    refreshDocuments: fetchDocuments,
    getDocumentsByCategory,
    getDocumentCount,
    getDocumentCountByCategory
  }
}