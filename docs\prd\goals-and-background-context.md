# Goals and Background Context

## Goals
- Streamline fruit export operations from manual to digital processes
- Reduce coordination time by 50% through automated workflows
- Achieve 90% real-time status visibility across all shipments
- Improve document accuracy to 95% through automated generation
- Enable mobile-first field operations for drivers and stakeholders
- Establish comprehensive audit trail compliance (100%)
- Create intelligent relationship management for streamlined shipment creation
- Achieve 60% reduction in data entry through pre-population workflows

## Background Context

DYY TRADING INTL Co.,Ltd currently manages complex fruit export operations manually across multiple stakeholders including customers, factories, carriers, and shipping agents. This manual process creates communication gaps, status tracking difficulties, and documentation errors. The company needs a modern, integrated platform that supports sea, land, and rail transportation with real-time coordination.

The system will serve internal operations staff (Admin, CS, Account) as primary users while providing specialized interfaces for external stakeholders (customers, carriers, drivers, factory personnel). The solution leverages Next.js and Supabase for modern web performance with mobile accessibility for field operations.

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2024-12-XX | 1.0 | Initial PRD creation from existing specifications | Product Manager |
| 2024-12-XX | 1.1 | Added Transportation Mode pre-selection, mandatory Factory/Forwarder Agent fields, ETD/ETA/Closing Time requirements, mandatory Destination Port, automatic shipment number generation, and enhanced master data structure | Product Manager |
