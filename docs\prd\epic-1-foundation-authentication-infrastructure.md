# Epic 1 Foundation & Authentication Infrastructure

**Epic Goal:** Establish the technical foundation with Next.js/Supabase project setup, implement role-based authentication system supporting all 11 user types, and deliver basic user management capabilities with a functional health check interface to validate the infrastructure is operational and ready for business functionality development.

## Story 1.1 Project Infrastructure Setup

As a Developer,  
I want to establish the Next.js/Supabase project foundation with ShadCN UI,  
so that the development team has a working application infrastructure with proper tooling and deployment pipeline.

### Acceptance Criteria

**1:** Project is initialized with Next.js 14+ App Router, TypeScript, Tailwind CSS, and ShadCN UI component library configured with dark blue theme colors (#1e293b, #0f172a, #334155, #f97316).

**2:** Supabase project is configured with PostgreSQL database, authentication, storage, and real-time subscriptions enabled.

**3:** Development environment includes ESLint, Prettier, and Jest testing framework with initial test suite.

**4:** CI/CD pipeline is configured for automated testing and deployment to staging environment.

**5:** Application renders a basic health check page confirming all integrations are functional.

## Story 1.2 Database Schema & RLS Foundation

As a System Administrator,  
I want the core database schema established with Row Level Security policies,  
so that data access is properly secured and the foundation is ready for master data operations.

### Acceptance Criteria

**1:** All master data tables are created (profiles, companies, products, ports, units_of_measure, drivers) with proper relationships and constraints.

**2:** Core business tables are created (shipments, containers, shipment_products, transportation, status_history) with audit trail support.

**3:** Row Level Security (RLS) is enabled on all tables with basic policies for role-based access control.

**4:** Database functions and triggers are implemented for automatic timestamp updates and data consistency.

**5:** Sample reference data is loaded (units of measure, basic port data) to support application testing.

## Story 1.3 Authentication System Implementation

As an Admin,  
I want to manage user accounts with role-based access control,  
so that I can control system access and ensure proper security for all user types.

### Acceptance Criteria

**1:** Supabase Auth is configured to support all 11 user types (admin, cs, account, customer, carrier, driver, factory, shipper, consignee, notify_party, forwarder_agent).

**2:** User registration and login flows are implemented with email/password authentication.

**3:** Custom user metadata stores role information with proper validation and constraints.

**4:** Role-based navigation displays appropriate menu options based on user permissions.

**5:** Admin interface allows creating, editing, and managing user accounts with role assignment.

## Story 1.4 Basic User Management Interface

As an Admin,  
I want to view and manage user profiles through a web interface,  
so that I can perform day-to-day user administration tasks efficiently.

### Acceptance Criteria

**1:** User management dashboard displays all users with filtering by role and status.

**2:** Create user form captures all required profile information with role selection and company association.

**3:** Edit user functionality allows updating profile information, role changes, and account activation/deactivation.

**4:** User detail view shows complete profile information, role permissions, and audit trail.

**5:** Interface follows ShadCN UI patterns with dark blue theme and responsive design.
