import { z } from 'zod'

// Base customer-shipper relationship schema
export const customerShipperSchema = z.object({
  customer_id: z.string().uuid('Invalid customer ID format'),
  shipper_id: z.string().uuid('Invalid shipper ID format'),
  is_default: z.boolean().default(false),
  is_active: z.boolean().default(true),
  notes: z.string().optional().nullable(),
})

// Form schema for creating/updating relationships
export const customerShipperFormSchema = z
  .object({
    customer_id: z
      .string({
        required_error: 'Customer selection is required',
      })
      .uuid('Invalid customer ID format'),

    shipper_id: z
      .string({
        required_error: 'Shipper selection is required',
      })
      .uuid('Invalid shipper ID format'),

    is_default: z.boolean().default(false),

    is_active: z.boolean().default(true),

    notes: z
      .string()
      .optional()
      .nullable()
      .transform(val => (val === '' ? null : val)),
  })
  .refine(data => data.customer_id !== data.shipper_id, {
    message: 'Customer and shipper cannot be the same company',
    path: ['shipper_id'],
  })

// Update schema (all fields optional except IDs when provided)
export const customerShipperUpdateSchema = customerShipperFormSchema
  .partial()
  .extend({
    id: z.string().uuid().optional(),
  })

// Filter schema for search and filtering
export const customerShipperFilterSchema = z
  .object({
    customer_id: z.string().uuid().optional(),
    shipper_id: z.string().uuid().optional(),
    is_active: z.boolean().optional(),
    is_default: z.boolean().optional(),
    search: z.string().optional(),
  })
  .partial()

// Bulk import schema
export const customerShipperBulkImportSchema = z.object({
  customer_name: z.string().min(1, 'Customer name is required'),
  shipper_name: z.string().min(1, 'Shipper name is required'),
  is_default: z
    .union([
      z.boolean(),
      z.string().transform(val => {
        const lower = val.toLowerCase().trim()
        return (
          lower === 'true' || lower === '1' || lower === 'yes' || lower === 'y'
        )
      }),
    ])
    .default(false),
  notes: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val === '' ? null : val)),
})

export const customerShipperBulkImportArraySchema = z.array(
  customerShipperBulkImportSchema
)

// Sort options
export const customerShipperSortSchema = z
  .enum([
    'customer_name',
    'shipper_name',
    'is_default',
    'is_active',
    'created_at',
    'updated_at',
  ])
  .default('customer_name')

// Sort order
export const sortOrderSchema = z.enum(['asc', 'desc']).default('asc')

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
})

// Combined query schema for API endpoints
export const customerShipperQuerySchema = customerShipperFilterSchema
  .extend({
    sort: customerShipperSortSchema,
    order: sortOrderSchema,
  })
  .merge(paginationSchema)

// Type exports
export type CustomerShipperForm = z.infer<typeof customerShipperFormSchema>
export type CustomerShipperUpdate = z.infer<typeof customerShipperUpdateSchema>
export type CustomerShipperFilter = z.infer<typeof customerShipperFilterSchema>
export type CustomerShipperBulkImport = z.infer<
  typeof customerShipperBulkImportSchema
>
export type CustomerShipperSort = z.infer<typeof customerShipperSortSchema>
export type CustomerShipperQuery = z.infer<typeof customerShipperQuerySchema>

// Validation functions
export function validateCustomerShipperForm(
  data: unknown
): CustomerShipperForm {
  return customerShipperFormSchema.parse(data)
}

export function validateCustomerShipperFilter(
  data: unknown
): CustomerShipperFilter {
  return customerShipperFilterSchema.parse(data)
}

export function validateBulkImportData(
  data: unknown[]
): CustomerShipperBulkImport[] {
  return customerShipperBulkImportArraySchema.parse(data)
}

// Helper function to validate CSV headers
export function validateCSVHeaders(headers: string[]): boolean {
  const requiredHeaders = ['customer_name', 'shipper_name']
  const optionalHeaders = ['is_default', 'notes']
  const allowedHeaders = [...requiredHeaders, ...optionalHeaders]

  // Check if all required headers are present
  const hasAllRequired = requiredHeaders.every(header =>
    headers.some(h => h.toLowerCase().trim() === header.toLowerCase())
  )

  // Check if all headers are valid
  const hasOnlyValid = headers.every(header =>
    allowedHeaders.some(
      allowed => allowed.toLowerCase() === header.toLowerCase().trim()
    )
  )

  return hasAllRequired && hasOnlyValid
}

// Error messages
export const CustomerShipperErrorMessages = {
  DUPLICATE_RELATIONSHIP: 'This customer-shipper relationship already exists',
  CUSTOMER_NOT_FOUND:
    'Selected customer company not found or is not a customer type',
  SHIPPER_NOT_FOUND:
    'Selected shipper company not found or is not a shipper type',
  INVALID_COMPANY_TYPE: 'Invalid company type for this relationship',
  SAME_COMPANY: 'Customer and shipper cannot be the same company',
  DEFAULT_CONFLICT:
    'Another relationship is already set as default for this customer',
  BULK_IMPORT_INVALID: 'Invalid bulk import data format',
  CSV_HEADERS_INVALID:
    'CSV file must contain valid headers: customer_name, shipper_name, and optionally is_default, notes',
} as const
