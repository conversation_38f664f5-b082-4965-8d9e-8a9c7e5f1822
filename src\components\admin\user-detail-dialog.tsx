'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { UserProfile } from '@/lib/supabase/auth'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  X,
  User,
  Mail,
  Phone,
  Building,
  Calendar,
  Shield,
  Activity,
  Edit,
  MessageSquare,
  Smartphone,
} from 'lucide-react'
import { formatDistanceToNow, format } from 'date-fns'

interface UserDetailDialogProps {
  user: UserProfile | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface ExtendedUserProfile extends UserProfile {
  company_name?: string
  company_type?: string
}

export function UserDetailDialog({
  user,
  open,
  onOpenChange,
}: UserDetailDialogProps) {
  const [userDetails, setUserDetails] = useState<ExtendedUserProfile | null>(
    null
  )
  const [loading, setLoading] = useState(false)

  const supabase = createClient()

  useEffect(() => {
    if (open && user) {
      loadUserDetails()
    }
  }, [open, user])

  async function loadUserDetails() {
    if (!user) return

    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(
          `
          *,
          companies:company_id (
            name,
            company_type
          )
        `
        )
        .eq('user_id', user.user_id)
        .single()

      if (error) throw error

      const userWithCompany = {
        ...data,
        company_name: data.companies?.name,
        company_type: data.companies?.company_type,
      }

      setUserDetails(userWithCompany)
    } catch (err) {
      console.error('Error loading user details:', err)
    } finally {
      setLoading(false)
    }
  }

  function getRoleBadgeColor(role: string) {
    const colors = {
      admin: 'bg-red-500/20 text-red-300 border-red-500/30',
      cs: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      account: 'bg-green-500/20 text-green-300 border-green-500/30',
      customer: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
      carrier: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30',
      driver: 'bg-orange-500/20 text-orange-300 border-orange-500/30',
      factory: 'bg-indigo-500/20 text-indigo-300 border-indigo-500/30',
      shipper: 'bg-pink-500/20 text-pink-300 border-pink-500/30',
      consignee: 'bg-teal-500/20 text-teal-300 border-teal-500/30',
      notify_party: 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30',
      forwarder_agent: 'bg-amber-500/20 text-amber-300 border-amber-500/30',
    }
    return (
      colors[role as keyof typeof colors] ||
      'bg-slate-500/20 text-slate-300 border-slate-500/30'
    )
  }

  function getRoleDescription(role: string) {
    const descriptions = {
      admin: 'Full system access and user management',
      cs: 'Customer service and support operations',
      account: 'Account management and client relations',
      customer: 'Customer portal access for shipment tracking',
      carrier: 'Carrier operations and logistics management',
      driver: 'Mobile access for delivery updates',
      factory: 'Factory operations and production management',
      shipper: 'Export process and shipping operations',
      consignee: 'Import process and receiving operations',
      notify_party: 'Notification recipient for shipments',
      forwarder_agent: 'Freight forwarding and agent operations',
    }
    return descriptions[role as keyof typeof descriptions] || 'Unknown role'
  }

  if (!open || !user) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-slate-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">User Details</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="text-slate-400 hover:text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {loading ? (
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="h-4 bg-slate-700 rounded animate-pulse"
              ></div>
            ))}
          </div>
        ) : userDetails ? (
          <div className="space-y-6">
            {/* User Avatar and Basic Info */}
            <div className="flex items-start space-x-4">
              <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xl font-medium">
                  {(
                    userDetails.first_name?.[0] || userDetails.email[0]
                  ).toUpperCase()}
                </span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white">
                  {userDetails.first_name || userDetails.last_name
                    ? `${userDetails.first_name || ''} ${userDetails.last_name || ''}`.trim()
                    : userDetails.email.split('@')[0]}
                </h3>
                <p className="text-slate-400">{userDetails.email}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <Badge className={getRoleBadgeColor(userDetails.role)}>
                    <Shield className="h-3 w-3 mr-1" />
                    {userDetails.role.replace('_', ' ')}
                  </Badge>
                  <div className="flex items-center space-x-1">
                    <div
                      className={`w-2 h-2 rounded-full ${userDetails.is_active ? 'bg-green-400' : 'bg-red-400'}`}
                    />
                    <span
                      className={`text-sm ${userDetails.is_active ? 'text-green-300' : 'text-red-300'}`}
                    >
                      {userDetails.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Role Description */}
            <div className="bg-slate-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Activity className="h-4 w-4 text-orange-500" />
                <span className="text-white font-medium">Role Permissions</span>
              </div>
              <p className="text-slate-300 text-sm">
                {getRoleDescription(userDetails.role)}
              </p>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Mail className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Email</span>
                </div>
                <p className="text-slate-300">{userDetails.email}</p>
              </div>

              {userDetails.phone_number && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Phone className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Phone</span>
                  </div>
                  <p className="text-slate-300">{userDetails.phone_number}</p>
                </div>
              )}

              {userDetails.line_id && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <MessageSquare className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Line ID</span>
                  </div>
                  <p className="text-slate-300">{userDetails.line_id}</p>
                </div>
              )}

              {userDetails.wechat_id && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Smartphone className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">WeChat ID</span>
                  </div>
                  <p className="text-slate-300">{userDetails.wechat_id}</p>
                </div>
              )}
            </div>

            {/* Company Information */}
            {userDetails.company_name && (
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Building className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Company</span>
                </div>
                <p className="text-slate-300 font-medium">
                  {userDetails.company_name}
                </p>
                {userDetails.company_type && (
                  <p className="text-slate-400 text-sm mt-1">
                    Type: {userDetails.company_type.replace('_', ' ')}
                  </p>
                )}
              </div>
            )}

            {/* Account Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Calendar className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Created</span>
                </div>
                <p className="text-slate-300">
                  {format(new Date(userDetails.created_at), 'PPP')}
                </p>
                <p className="text-slate-400 text-sm">
                  {formatDistanceToNow(new Date(userDetails.created_at), {
                    addSuffix: true,
                  })}
                </p>
              </div>

              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Edit className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Last Updated</span>
                </div>
                <p className="text-slate-300">
                  {format(new Date(userDetails.updated_at), 'PPP')}
                </p>
                <p className="text-slate-400 text-sm">
                  {formatDistanceToNow(new Date(userDetails.updated_at), {
                    addSuffix: true,
                  })}
                </p>
              </div>
            </div>

            {/* Account ID */}
            <div className="bg-slate-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <User className="h-4 w-4 text-orange-500" />
                <span className="text-white font-medium">Account ID</span>
              </div>
              <p className="text-slate-300 font-mono text-sm">
                {userDetails.user_id}
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-slate-400">Failed to load user details</p>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end mt-6">
          <Button
            onClick={() => onOpenChange(false)}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  )
}
