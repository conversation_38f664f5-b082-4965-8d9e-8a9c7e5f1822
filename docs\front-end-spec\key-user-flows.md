# Key User Flows

## Flow 1: Intelligent Shipment Creation (CS Representative)

**Objective:** Create new shipment with minimal data entry through cascading selection and automatic container generation

**Flow Steps:**
1. **Transportation Mode Selection (Pre-Creation)**
   - Transportation mode selection modal before entering creation form
   - Options: Sea, Land, Rail
   - Each mode adjusts available fields and workflow requirements
   - Confirmation leads to main creation form

2. **Customer & Factory Selection**
   - Type-ahead search with customer suggestions
   - Customer selection triggers automatic loading of:
     - Associated shippers (default pre-selected)
     - Associated products with pricing (default pre-selected)
     - Previous shipment patterns for auto-completion
   - **Mandatory Factory Selection**
     - Factory dropdown (required field)
     - Factory-specific information and requirements

3. **Shipper & Product Refinement**
   - Cascaded shipper dropdown (pre-filtered to customer's associates)
   - Product selection shows customer-specific pricing (CIF/FOB per KG)
   - Packaging specifications auto-populate from customer-product relationships

4. **Forwarder Agent & Documentation**
   - **Mandatory Forwarder Agent Selection**
     - Forwarder agent dropdown (required field)
     - Agent contact information and service details
   - **Required Date & Time Information**
     - ETD (Estimated Time of Departure) date entry (required)
     - ETA (Estimated Time of Arrival) date entry (required)
     - Closing Time entry (required)
   - **Booking Confirm Document Upload**
     - File upload interface for booking confirmation
     - Document validation and storage
     - Preview and replacement capabilities

5. **Consignee & Notification Setup**
   - Consignee selection triggers automatic loading of:
     - Associated notify parties (default pre-selected)
     - Communication preferences by channel
     - Special handling instructions

6. **Shipment Details Completion**
   - **Mandatory Destination Port** - User must select destination port (required field)
   - Origin port information with suggestions based on customer history
   - **Optional Notes Field** - User can enter additional notes or special instructions
   - Document generation preferences
   - **No Container Entry Required** - System generates dummy containers automatically upon save
   - **Automatic Shipment Number Generation** - System generates shipment number upon save with format: EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running]
     - Transportation Mode Code: 1 (Land), 2 (Rail), 3 (Sea)
     - Running number based on Transportation Mode and Port, resets to 1 every month

**Key UX Elements:**
- **Transportation Mode Modal:** Clear selection interface before form entry (Sea/Land/Rail)
- **Factory Selection:** Mandatory field with location and capacity information  
- **Forwarder Agent Selection:** Required with contact details and service offerings
- **ETD/ETA/Closing Time:** Required date and time fields for logistics coordination
- **Destination Port:** Mandatory port selection for shipment routing
- **Document Upload:** Drag-and-drop interface with preview and validation
- **Notes Field:** Optional text area for additional instructions or comments
- **Automatic Shipment Number:** System generates unique number with transportation mode, port, and date
- **Automatic Container Generation:** Clear indication that containers are auto-created
- Real-time form validation with clear error messaging
- Progress indicators showing completion status
- Save-as-draft functionality for complex shipments
- Confirmation screen with summary before submission

## Flow 2: Mobile Driver Status Update

**Objective:** Update shipment status with photo documentation on mobile device

**Flow Steps:**
1. **Assignment Dashboard**
   - Swipe-based navigation through assigned shipments
   - Status cards with clear visual hierarchy
   - GPS-based sorting (nearest first)

2. **Status Update Selection**
   - Current status clearly displayed
   - Available next statuses as large touch targets
   - Status transition rules enforced automatically

3. **Photo Documentation**
   - Camera interface with guidelines overlay
   - Multiple photo capture (up to 5 per update)
   - Automatic GPS coordinate capture
   - Thumbnail preview with retake option

4. **Confirmation & Sync**
   - Review screen with all captured information
   - Offline queue indication if network unavailable
   - Success feedback with next assignment suggestion

**Key UX Elements:**
- Gesture-based navigation optimized for one-handed use
- High contrast interface for outdoor visibility
- Immediate feedback for all user actions
- Graceful offline/online state management

## Flow 3: Cascading Product Selection (Customer Portal)

**Objective:** Enable customers to easily track products across shipments

**Flow Steps:**
1. **Product-Based Navigation**
   - Product categories with visual indicators
   - Search with auto-complete for product codes
   - Filter by shipment status and date ranges

2. **Shipment Association Discovery**
   - Products linked to active shipments
   - Status progression visualization
   - Document availability indicators

3. **Real-Time Status Monitoring**
   - Live status updates via websocket connection
   - Notification preferences easily accessible
   - Historical tracking with trend analysis

**Key UX Elements:**
- Responsive design adapting to device capabilities
- Rich data visualization for complex logistics information
- Contextual help and onboarding flows

## Flow 4: Shipment Viewing and Editing (CS Representative)

**Objective:** Enable CS representatives to efficiently search, view, and edit existing shipments with comprehensive management capabilities

**Flow Steps:**
1. **Shipment Search and Discovery**
   - Advanced search interface with multiple filter criteria
   - Search by shipment number, customer, container number, or invoice number
   - Filter by status, transportation mode, date ranges, and destination
   - Saved search preferences for frequently used filters
   - Real-time search results with pagination and sorting

2. **Shipment List View with Quick Actions**
   - Condensed shipment cards showing key information
   - Status indicators with color coding and progress visualization
   - Quick action buttons for common operations (view, edit, duplicate)
   - Bulk operations for multiple shipment management
   - Export functionality for reporting and analysis

3. **Comprehensive Shipment Detail View**
   - Tabbed interface organizing information by category:
     - **Overview:** Key details, status timeline, stakeholder summary
     - **Logistics:** Transportation details, ports, dates, container information
     - **Products:** Product details, quantities, pricing, specifications
     - **Documents:** Generated documents, uploaded files, document history
     - **Communication:** Notification history, stakeholder messages, notes
     - **Audit Trail:** Complete change history with user attribution

4. **Inline Editing with Smart Validation**
   - Edit mode toggle for individual sections or entire shipment
   - Real-time validation maintaining business rule compliance
   - Field-level permissions based on shipment status and user role
   - Unsaved changes indicator with auto-save functionality
   - Conflict resolution for concurrent editing scenarios

5. **Container Management Post-Creation**
   - Container details editing (type, size, seal numbers, weights)
   - Product allocation adjustments between containers
   - Container addition/removal with automatic weight recalculation
   - Container status tracking and photo documentation
   - Seal verification and security management

6. **Status Management and Progression**
   - Status update interface with dropdown of valid next statuses
   - Mandatory fields for specific status transitions
   - Automatic stakeholder notifications on status changes
   - Status rollback capability with approval workflow
   - Custom status notes and location information

7. **Stakeholder Communication Hub**
   - Integrated messaging interface for shipment-specific communication
   - Contact information display for all shipment stakeholders
   - Communication history timeline with message threading
   - Quick notification sending with template selection
   - Stakeholder preference management and channel selection

**Key UX Elements:**
- **Comprehensive Search:** Advanced filtering with saved preferences and real-time results
- **Tabbed Information Architecture:** Organized data presentation preventing information overload
- **Contextual Editing:** Inline editing with smart validation and permission awareness
- **Visual Status Tracking:** Clear progress indicators and timeline visualization
- **Integrated Communication:** Seamless stakeholder coordination within shipment context
- **Audit Transparency:** Complete change history with user attribution and timestamps
- **Responsive Design:** Optimized for desktop primary use with tablet compatibility
- **Keyboard Shortcuts:** Power user efficiency with keyboard navigation support

---
