'use client'

import { useState, useEffect } from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Loader2,
  Info,
  Building,
  Users,
  Truck,
  Factory as FactoryIcon,
  Mail,
  Phone,
  MapPin,
  AlertCircle,
  Globe,
  Ship,
} from 'lucide-react'
import {
  companyFormSchema,
  type CompanyForm,
  COMPANY_TYPES,
} from '@/lib/validations/companies'
import {
  useCompanyValidation,
  useCompanyCategories,
  useCoordinateUtils,
} from '@/hooks/use-companies'
import type { Company } from '@/stores/company-store'
import { GPSCoordinateInput } from './gps-coordinate-input'
import { CustomerInfoForm } from './customer-info-form'
import { CarrierInfoForm } from './carrier-info-form'
import { FactoryInfoForm } from './factory-info-form'
import { ForwarderAgentInfoForm } from './forwarder-agent-info-form'

interface CompanyFormProps {
  company?: Company | null
  onSubmit: (data: CompanyForm) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  className?: string
}

export function CompanyForm({
  company,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: CompanyFormProps) {
  const [error, setError] = useState<string | null>(null)
  const [usci, setUsci] = useState<string>('')
  const [usciError, setUsciError] = useState<string | null>(null)
  const {
    validateUniqueName,
    validateCompanyName,
    validateTaxId,
    validateEmail,
    validatePhoneNumber,
  } = useCompanyValidation()
  const { companyTypeOptions, getCompanyTypeLabel, isComplexCompanyType } =
    useCompanyCategories()
  const { parseCoordinatesFromString } = useCoordinateUtils()

  // Parse existing coordinates if editing
  const existingCoords = company?.gps_coordinates
    ? (() => {
        try {
          // Handle different PostGIS geography formats that Supabase might return
          const coordString = company.gps_coordinates.toString()

          // ST_AsText returns format: POINT(lng lat)
          const pointMatch = coordString.match(
            /POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
          )
          if (pointMatch) {
            return {
              lat: parseFloat(pointMatch[2]),
              lng: parseFloat(pointMatch[1]),
            }
          }

          // Handle WKT format with SRID: SRID=4326;POINT(lng lat)
          const sridMatch = coordString.match(
            /SRID=\d+;POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
          )
          if (sridMatch) {
            return {
              lat: parseFloat(sridMatch[2]),
              lng: parseFloat(sridMatch[1]),
            }
          }

          // Supabase might return as JSON: {"type":"Point","coordinates":[lng,lat]}
          const jsonMatch = coordString.match(/^\{.*\}$/)
          if (jsonMatch) {
            const parsed = JSON.parse(coordString)
            if (
              parsed.type === 'Point' &&
              parsed.coordinates &&
              parsed.coordinates.length === 2
            ) {
              return {
                lat: parsed.coordinates[1],
                lng: parsed.coordinates[0],
              }
            }
          }

          return null
        } catch {
          return null
        }
      })()
    : null

  // Extract USCI from metadata if editing
  const existingUsci = company?.metadata
    ? (() => {
        try {
          const metadata =
            typeof company.metadata === 'string'
              ? JSON.parse(company.metadata)
              : company.metadata
          return metadata?.usci || ''
        } catch {
          return ''
        }
      })()
    : ''

  const form = useForm<CompanyForm>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      name: company?.name || '',
      company_type: company?.company_type || 'customer',
      tax_id: company?.tax_id || '',
      contact_email: company?.contact_email || '',
      contact_phone: company?.contact_phone || '',
      contact_fax: company?.contact_fax || '',
      contact_person_first_name: company?.contact_person_first_name || '',
      contact_person_last_name: company?.contact_person_last_name || '',
      gps_coordinates: existingCoords || undefined,
      notes: company?.notes || '',
      is_active: company?.is_active ?? true,
      // Address information
      address: {
        street: {
          en: company?.address?.street?.en || '',
          th: company?.address?.street?.th || '',
        },
        city: {
          en: company?.address?.city?.en || '',
          th: company?.address?.city?.th || '',
        },
        province: {
          en: company?.address?.province?.en || '',
          th: company?.address?.province?.th || '',
        },
        postal_code: company?.address?.postal_code || '',
        country: {
          en: company?.address?.country?.en || '',
          th: company?.address?.country?.th || '',
        },
      },
      // Type-specific info
      customer_info: company?.customer_info || undefined,
      carrier_info: company?.carrier_info || undefined,
      factory_info: company?.factory_info || undefined,
      metadata: company?.metadata || undefined,
    },
  })

  // Watch company type to show/hide type-specific sections
  const watchedCompanyType = useWatch({
    control: form.control,
    name: 'company_type',
  })

  // Initialize complex company type info when type changes
  useEffect(() => {
    if (watchedCompanyType === 'customer' && !form.getValues('customer_info')) {
      form.setValue('customer_info', {
        customer_type: 'regular',
        credit_limit: 0,
      })
    } else if (
      watchedCompanyType === 'carrier' &&
      !form.getValues('carrier_info')
    ) {
      form.setValue('carrier_info', {
        fleet_size: 0,
        license_types: [],
        coverage_areas: [],
        gps_tracking_available: false,
      })
    } else if (
      watchedCompanyType === 'factory' &&
      !form.getValues('factory_info')
    ) {
      form.setValue('factory_info', {
        factory_code: '',
        license_no: '',
        certifications: [],
        production_capacity_tons_per_day: 0,
        cold_storage_capacity_tons: 0,
        specializations: [],
        loading_dock_count: 1,
        container_loading_time_minutes: 120,
        advance_booking_required_hours: 24,
      })
    } else if (
      watchedCompanyType === 'forwarder_agent' &&
      !form.getValues('metadata')
    ) {
      form.setValue('metadata', {
        services_provided: [],
        coverage_areas: [],
        equipment_types: [],
        service_specializations: [],
      })
    }
    // Clear info objects when switching to other simple types
    else if (
      ['shipper', 'consignee', 'notify_party'].includes(watchedCompanyType)
    ) {
      form.setValue('customer_info', undefined)
      form.setValue('carrier_info', undefined)
      form.setValue('factory_info', undefined)
      // Keep metadata for these types as they might use it for USCI
    }
    // Clear info objects when switching to complex types
    else if (['customer', 'carrier', 'factory'].includes(watchedCompanyType)) {
      form.setValue('metadata', undefined)
    }
  }, [watchedCompanyType, form])

  // Initialize USCI from existing metadata when editing
  useEffect(() => {
    if (company && existingUsci) {
      setUsci(existingUsci)
    }
  }, [company, existingUsci])

  // Validate USCI format
  const validateUsci = (usciValue: string) => {
    if (!usciValue) {
      setUsciError(null)
      return true
    }

    // USCI should be 18 characters
    if (usciValue.length !== 18) {
      setUsciError('USCI must be exactly 18 characters')
      return false
    }

    // USCI should contain only alphanumeric characters
    if (!/^[A-Z0-9]{18}$/.test(usciValue)) {
      setUsciError('USCI must contain only uppercase letters and numbers')
      return false
    }

    setUsciError(null)
    return true
  }

  // Handle USCI input change with validation
  const handleUsciChange = (value: string) => {
    const upperValue = value.toUpperCase()
    setUsci(upperValue)
    validateUsci(upperValue)
  }

  // Get the icon for the current company type
  const getCompanyTypeIcon = (type: string) => {
    switch (type) {
      case 'customer':
        return <Users className="h-4 w-4" />
      case 'carrier':
        return <Truck className="h-4 w-4" />
      case 'factory':
        return <FactoryIcon className="h-4 w-4" />
      case 'forwarder_agent':
        return <Ship className="h-4 w-4" />
      default:
        return <Building className="h-4 w-4" />
    }
  }

  const onFormSubmit = async (data: CompanyForm) => {
    try {
      setError(null)
      console.log('Form submitted with data:', data)

      // Convert coordinates to string if provided
      if (data.gps_coordinates) {
        data.gps_coordinates = JSON.stringify(data.gps_coordinates) as any
      }

      // Clean up address fields - remove completely empty address object
      if (data.address) {
        const cleanAddress: any = {}

        // Only include bilingual fields if at least one language is provided
        if (
          data.address.street?.en?.trim() ||
          data.address.street?.th?.trim()
        ) {
          cleanAddress.street = {}
          if (data.address.street.en?.trim())
            cleanAddress.street.en = data.address.street.en.trim()
          if (data.address.street.th?.trim())
            cleanAddress.street.th = data.address.street.th.trim()
        }

        if (data.address.city?.en?.trim() || data.address.city?.th?.trim()) {
          cleanAddress.city = {}
          if (data.address.city.en?.trim())
            cleanAddress.city.en = data.address.city.en.trim()
          if (data.address.city.th?.trim())
            cleanAddress.city.th = data.address.city.th.trim()
        }

        if (
          data.address.province?.en?.trim() ||
          data.address.province?.th?.trim()
        ) {
          cleanAddress.province = {}
          if (data.address.province.en?.trim())
            cleanAddress.province.en = data.address.province.en.trim()
          if (data.address.province.th?.trim())
            cleanAddress.province.th = data.address.province.th.trim()
        }

        if (
          data.address.country?.en?.trim() ||
          data.address.country?.th?.trim()
        ) {
          cleanAddress.country = {}
          if (data.address.country.en?.trim())
            cleanAddress.country.en = data.address.country.en.trim()
          if (data.address.country.th?.trim())
            cleanAddress.country.th = data.address.country.th.trim()
        }

        if (data.address.postal_code?.trim()) {
          cleanAddress.postal_code = data.address.postal_code.trim()
        }

        // Only include address if there are actual meaningful values
        if (Object.keys(cleanAddress).length > 0) {
          data.address = cleanAddress
        } else {
          data.address = null // Set to null instead of undefined for database
        }
      } else {
        // If no address provided at all, set to null
        data.address = null
      }

      // Handle USCI for consignee and notify_party types
      if (
        data.company_type === 'consignee' ||
        data.company_type === 'notify_party'
      ) {
        if (usci) {
          // Validate USCI before submission
          if (!validateUsci(usci)) {
            return
          }

          // Add USCI to metadata
          const existingMetadata = data.metadata || {}
          const metadataObject =
            typeof existingMetadata === 'string'
              ? JSON.parse(existingMetadata)
              : existingMetadata

          data.metadata = {
            ...metadataObject,
            usci: usci,
          }
        }
      }

      // Validate unique name (only if creating or name changed)
      if (!company || company.name !== data.name) {
        const nameValidation = validateUniqueName(data.name, data.company_type, company?.id)
        if (!nameValidation.isValid) {
          form.setError('name', { message: nameValidation.message })
          return
        }
      }

      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to save company'
      )
    }
  }

  // Log form errors for debugging and show user-friendly error
  useEffect(() => {
    if (Object.keys(form.formState.errors).length > 0) {
      console.log('Form validation errors:', form.formState.errors)

      // Set a user-friendly error message when validation fails
      const errorMessages = []
      if (form.formState.errors.name)
        errorMessages.push('Company name is required')
      if (form.formState.errors.company_type)
        errorMessages.push('Company type is required')
      if (form.formState.errors.address) {
        errorMessages.push('Address information has validation errors')
      }
      if (form.formState.errors.customer_info)
        errorMessages.push('Customer information is incomplete')
      if (form.formState.errors.carrier_info)
        errorMessages.push('Carrier information is incomplete')
      if (form.formState.errors.factory_info)
        errorMessages.push('Factory information is incomplete')

      if (errorMessages.length > 0) {
        setError(`Please fix the following issues: ${errorMessages.join(', ')}`)
      }
    } else {
      // Clear error when form is valid
      if (error && !error.includes('Failed to')) {
        setError(null)
      }
    }
  }, [form.formState.errors, error])

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Basic Company Information */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Building className="h-5 w-5 text-orange-500" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Company Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Company Name *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter company name"
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Company Type */}
              <FormField
                control={form.control}
                name="company_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Company Type *
                    </FormLabel>
                    {company ? (
                      // Show read-only company type when editing
                      <div className="flex items-center space-x-2 p-3 bg-slate-700 border border-slate-600 rounded-md">
                        {getCompanyTypeIcon(field.value)}
                        <span className="text-white font-medium">
                          {getCompanyTypeLabel(field.value)}
                        </span>
                        <Badge
                          variant="secondary"
                          className="text-xs bg-slate-600 text-slate-300 border-slate-500"
                        >
                          Cannot be changed after creation
                        </Badge>
                      </div>
                    ) : (
                      // Show editable select when creating
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select company type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {companyTypeOptions.map(type => (
                            <SelectItem
                              key={type.value}
                              value={type.value}
                              className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                            >
                              <div className="flex items-center space-x-2">
                                {getCompanyTypeIcon(type.value)}
                                <span>{type.label}</span>
                                {type.complex && (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs bg-orange-500/20 text-orange-300 border-orange-400"
                                  >
                                    Complex
                                  </Badge>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                    <FormDescription className="text-slate-400">
                      {company
                        ? 'Company type cannot be changed after creation to maintain data integrity'
                        : isComplexCompanyType(watchedCompanyType)
                          ? 'This type requires additional detailed information'
                          : 'This type uses simple metadata storage'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Tax ID */}
                <FormField
                  control={form.control}
                  name="tax_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">Tax ID</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter tax identification number"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Active Status */}
                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4 bg-slate-700/30">
                      <div className="space-y-0.5">
                        <FormLabel className="text-white font-medium">
                          Company Status
                        </FormLabel>
                        <div className="text-sm font-medium">
                          <span
                            className={
                              field.value ? 'text-green-400' : 'text-red-400'
                            }
                          >
                            {field.value
                              ? 'Active - Company is available for operations'
                              : 'Inactive - Company is not available for operations'}
                          </span>
                        </div>
                      </div>
                      <FormControl>
                        <div className="flex items-center space-x-2">
                          <span
                            className={`text-xs font-medium ${field.value ? 'text-green-400' : 'text-red-400'}`}
                          >
                            {field.value ? 'ON' : 'OFF'}
                          </span>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500"
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Mail className="h-5 w-5 text-orange-500" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Contact Email */}
                <FormField
                  control={form.control}
                  name="contact_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Email Address
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contact Phone */}
                <FormField
                  control={form.control}
                  name="contact_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Phone Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="+66 2 123 4567"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contact Fax */}
                <FormField
                  control={form.control}
                  name="contact_fax"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Fax Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="+66 2 123 4568"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Contact Person First Name */}
                <FormField
                  control={form.control}
                  name="contact_person_first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Contact Person First Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="John"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contact Person Last Name */}
                <FormField
                  control={form.control}
                  name="contact_person_last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Contact Person Last Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Doe"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <MapPin className="h-5 w-5 text-orange-500" />
                Address Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Street Address */}
              <div className="space-y-3">
                <label className="text-slate-200 text-sm font-medium">
                  Street Address
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-1">
                    <label className="text-slate-300 text-xs">English</label>
                    <FormField
                      control={form.control}
                      name="address.street.en"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="123 Main Street"
                              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="space-y-1">
                    <label className="text-slate-300 text-xs">Thai (ไทย)</label>
                    <FormField
                      control={form.control}
                      name="address.street.th"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="123 ถนนหลัก"
                              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* City */}
                <div className="space-y-3">
                  <label className="text-slate-200 text-sm font-medium">
                    City
                  </label>
                  <div className="space-y-2">
                    <FormField
                      control={form.control}
                      name="address.city.en"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Bangkok (EN)"
                              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="address.city.th"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="กรุงเทพฯ (TH)"
                              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Province */}
                <div className="space-y-3">
                  <label className="text-slate-200 text-sm font-medium">
                    Province/State
                  </label>
                  <div className="space-y-2">
                    <FormField
                      control={form.control}
                      name="address.province.en"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Bangkok (EN)"
                              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="address.province.th"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="กรุงเทพมหานคร (TH)"
                              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Postal Code */}
                <FormField
                  control={form.control}
                  name="address.postal_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Postal Code
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="10110"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Country */}
                <div className="space-y-3">
                  <label className="text-slate-200 text-sm font-medium flex items-center gap-2">
                    <Globe className="h-4 w-4 text-blue-500" />
                    Country
                  </label>
                  <div className="space-y-2">
                    <FormField
                      control={form.control}
                      name="address.country.en"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Thailand (EN)"
                              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="address.country.th"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="ประเทศไทย (TH)"
                              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Address Information Card */}
              <div className="bg-slate-700 rounded-lg p-3 border border-slate-600 mt-4">
                <div className="flex items-center space-x-2 mb-2">
                  <MapPin className="h-4 w-4 text-orange-500" />
                  <span className="text-sm font-medium text-slate-200">
                    Address Guidelines
                  </span>
                </div>
                <p className="text-xs text-slate-400">
                  Provide address information in both English and Thai for
                  better international compatibility. All fields are optional.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* GPS Coordinates */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <MapPin className="h-5 w-5 text-orange-500" />
                Location & Coordinates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <GPSCoordinateInput
                value={form.watch('gps_coordinates')}
                onChange={coords => form.setValue('gps_coordinates', coords)}
                error={form.formState.errors.gps_coordinates?.message}
              />
            </CardContent>
          </Card>

          {/* Type-Specific Information */}
          {isComplexCompanyType(watchedCompanyType) && (
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  {getCompanyTypeIcon(watchedCompanyType)}
                  {getCompanyTypeLabel(watchedCompanyType)} Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="w-full space-y-4">
                  <div className="text-center pb-4">
                    <Badge
                      variant="secondary"
                      className="bg-orange-500/20 text-orange-300 border-orange-400 px-4 py-2 text-sm font-medium"
                    >
                      {getCompanyTypeLabel(watchedCompanyType)} Details
                    </Badge>
                  </div>

                  {watchedCompanyType === 'customer' && (
                    <CustomerInfoForm
                      value={form.watch('customer_info')}
                      onChange={info => form.setValue('customer_info', info)}
                      errors={form.formState.errors.customer_info}
                    />
                  )}

                  {watchedCompanyType === 'carrier' && (
                    <CarrierInfoForm
                      value={form.watch('carrier_info')}
                      onChange={info => form.setValue('carrier_info', info)}
                      errors={form.formState.errors.carrier_info}
                    />
                  )}

                  {watchedCompanyType === 'factory' && (
                    <FactoryInfoForm
                      value={form.watch('factory_info')}
                      onChange={info => form.setValue('factory_info', info)}
                      errors={form.formState.errors.factory_info}
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* USCI Information for Consignee and Notify Party */}
          {(watchedCompanyType === 'consignee' ||
            watchedCompanyType === 'notify_party') && (
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  {getCompanyTypeIcon(watchedCompanyType)}
                  {getCompanyTypeLabel(watchedCompanyType)} Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="w-full space-y-4">
                  <div className="text-center pb-4">
                    <Badge
                      variant="secondary"
                      className="bg-blue-500/20 text-blue-300 border-blue-400 px-4 py-2 text-sm font-medium"
                    >
                      Registration Details
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <label className="text-slate-200 text-sm font-medium flex items-center gap-2">
                      <Building className="h-4 w-4 text-blue-500" />
                      USCI (Unified Social Credit Identifier)
                    </label>
                    <Input
                      placeholder="e.g. 91440300MA5GX8K77W"
                      value={usci}
                      onChange={e => handleUsciChange(e.target.value)}
                      className={`bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-blue-500 ${
                        usciError
                          ? 'border-red-500 focus:border-red-500'
                          : 'focus:border-blue-500'
                      }`}
                      maxLength={18}
                    />
                    {usciError && (
                      <p className="text-sm text-red-400">{usciError}</p>
                    )}
                    <p className="text-xs text-slate-400">
                      Enter the 18-character Unified Social Credit Identifier
                      for Chinese companies
                    </p>
                  </div>

                  {/* Information Card */}
                  <div className="bg-slate-700 rounded-lg p-3 border border-slate-600 mt-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Building className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium text-slate-200">
                        USCI Information
                      </span>
                    </div>
                    <p className="text-xs text-slate-400">
                      Required registration identifier for companies operating
                      in China
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Forwarder Agent Information */}
          {watchedCompanyType === 'forwarder_agent' && (
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Ship className="h-5 w-5 text-indigo-500" />
                  Forwarder Agent Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="w-full space-y-4">
                  <div className="text-center pb-4">
                    <Badge
                      variant="secondary"
                      className="bg-indigo-500/20 text-indigo-300 border-indigo-400 px-4 py-2 text-sm font-medium"
                    >
                      Service Details
                    </Badge>
                  </div>

                  <ForwarderAgentInfoForm
                    value={form.watch('metadata') || {}}
                    onChange={info => form.setValue('metadata', info)}
                    errors={form.formState.errors.metadata}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Info className="h-5 w-5 text-orange-500" />
                Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional notes or special requirements..."
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        rows={4}
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Optional notes about the company
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-orange-500 hover:bg-orange-600 text-white"
              onClick={() => console.log('Submit button clicked')}
            >
              {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {company ? 'Update Company' : 'Create Company'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
