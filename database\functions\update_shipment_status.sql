-- Function to update shipment status with validation and audit trail
-- This function handles the complete status update workflow including:
-- 1. Status transition validation
-- 2. Shipment status update
-- 3. Status history record creation
-- 4. GPS coordinate conversion

CREATE OR REPLACE FUNCTION public.update_shipment_status(
  p_shipment_id UUID,
  p_status_from shipment_status_enum,
  p_status_to shipment_status_enum,
  p_notes TEXT DEFAULT NULL,
  p_location TEXT DEFAULT NULL,
  p_latitude NUMERIC DEFAULT NULL,
  p_longitude NUMERIC DEFAULT NULL,
  p_updated_by UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_status shipment_status_enum;
  v_shipment_exists BOOLEAN;
  v_status_history_id UUID;
  v_gps_point GEOGRAPHY;
  v_result JSON;
BEGIN
  -- Check if shipment exists and get current status
  SELECT 
    status,
    TRUE
  INTO 
    v_current_status,
    v_shipment_exists
  FROM shipments 
  WHERE id = p_shipment_id;
  
  -- Validate shipment exists
  IF NOT v_shipment_exists THEN
    RAISE EXCEPTION 'Shipment with ID % not found', p_shipment_id;
  END IF;
  
  -- Validate current status matches expected status_from
  IF v_current_status != p_status_from THEN
    RAISE EXCEPTION 'Status transition validation failed: Expected current status %, but found %', 
      p_status_from, v_current_status;
  END IF;
  
  -- Validate status transition is allowed
  -- Basic validation - in production, this should reference a status_transitions table
  CASE p_status_from
    WHEN 'booking_confirmed' THEN
      IF p_status_to NOT IN ('transport_assigned', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'transport_assigned' THEN
      IF p_status_to NOT IN ('driver_assigned', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'driver_assigned' THEN
      IF p_status_to NOT IN ('empty_container_picked', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'empty_container_picked' THEN
      IF p_status_to NOT IN ('arrived_at_factory', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'arrived_at_factory' THEN
      IF p_status_to NOT IN ('loading_started', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'loading_started' THEN
      IF p_status_to NOT IN ('departed_factory', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'departed_factory' THEN
      IF p_status_to NOT IN ('container_returned', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'container_returned' THEN
      IF p_status_to NOT IN ('shipped', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'shipped' THEN
      IF p_status_to NOT IN ('arrived', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'arrived' THEN
      IF p_status_to NOT IN ('completed') THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', p_status_from, p_status_to;
      END IF;
    WHEN 'completed' THEN
      RAISE EXCEPTION 'Cannot change status from completed state';
    WHEN 'cancelled' THEN
      RAISE EXCEPTION 'Cannot change status from cancelled state';
    ELSE
      RAISE EXCEPTION 'Unknown status: %', p_status_from;
  END CASE;
  
  -- Create GPS point if coordinates provided
  IF p_latitude IS NOT NULL AND p_longitude IS NOT NULL THEN
    v_gps_point := ST_SetSRID(ST_MakePoint(p_longitude, p_latitude), 4326)::GEOGRAPHY;
  END IF;
  
  -- Start transaction block
  BEGIN
    -- Update shipment status
    UPDATE shipments 
    SET 
      status = p_status_to,
      updated_at = NOW()
    WHERE id = p_shipment_id;
    
    -- Create status history record
    INSERT INTO status_history (
      shipment_id,
      status_from,
      status_to,
      notes,
      location,
      latitude,
      longitude,
      gps_coordinates,
      updated_by,
      created_at
    ) VALUES (
      p_shipment_id,
      p_status_from,
      p_status_to,
      p_notes,
      p_location,
      p_latitude,
      p_longitude,
      v_gps_point,
      p_updated_by,
      NOW()
    ) RETURNING id INTO v_status_history_id;
    
    -- Prepare success result
    v_result := json_build_object(
      'success', true,
      'shipment_id', p_shipment_id,
      'status_from', p_status_from,
      'status_to', p_status_to,
      'status_history_id', v_status_history_id,
      'updated_at', NOW()
    );
    
    RETURN v_result;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- Rollback will happen automatically
      RAISE EXCEPTION 'Status update failed: %', SQLERRM;
  END;
  
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.update_shipment_status(UUID, shipment_status_enum, shipment_status_enum, TEXT, TEXT, NUMERIC, NUMERIC, UUID) TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION public.update_shipment_status IS 'Updates shipment status with validation and creates audit trail in status_history table';