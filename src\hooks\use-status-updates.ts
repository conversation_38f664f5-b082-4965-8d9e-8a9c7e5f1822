'use client'

import { useState, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from './use-auth'
import { useShipmentStore } from '@/stores/shipment-store'
import {
  statusUpdateSchema,
  type StatusUpdate,
  type StatusHistory,
  type ShipmentStatus,
  isValidStatusTransition,
} from '@/lib/validations/status-updates'
import type { Database } from '@/lib/supabase/types'

interface UseStatusUpdatesReturn {
  updateStatus: (data: StatusUpdateRequest) => Promise<void>
  getStatusHistory: (shipmentId: string) => Promise<StatusHistory[]>
  isLoading: boolean
  error: string | null
  clearError: () => void
}

interface StatusUpdateRequest {
  shipment_id: string
  status_to: ShipmentStatus
  notes?: string
  location?: string
  coordinates?: {
    lat: number
    lng: number
  }
}

export function useStatusUpdates(): UseStatusUpdatesReturn {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const { user, profile } = useAuth()
  const { updateShipment } = useShipmentStore()

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Get current shipment to validate status transitions
  const getShipmentCurrentStatus = useCallback(
    async (shipmentId: string): Promise<ShipmentStatus | null> => {
      try {
        const { data, error } = await supabase
          .from('shipments')
          .select('status')
          .eq('id', shipmentId)
          .single()

        if (error) throw error
        return data?.status as ShipmentStatus
      } catch (err) {
        console.error('Error fetching shipment status:', err)
        return null
      }
    },
    [supabase]
  )

  // Update shipment status
  const updateStatus = useCallback(
    async (data: StatusUpdateRequest): Promise<void> => {
      if (!user || !profile) {
        throw new Error('User authentication required')
      }

      setIsLoading(true)
      setError(null)

      try {
        // Get current status for validation
        const currentStatus = await getShipmentCurrentStatus(data.shipment_id)
        if (!currentStatus) {
          throw new Error('Unable to fetch current shipment status')
        }

        // Validate status transition
        if (!isValidStatusTransition(currentStatus, data.status_to)) {
          throw new Error(
            `Invalid status transition from ${currentStatus} to ${data.status_to}`
          )
        }

        // Prepare status update data
        const statusUpdateData: StatusUpdate = {
          shipment_id: data.shipment_id,
          status_from: currentStatus,
          status_to: data.status_to,
          notes: data.notes,
          location: data.location,
          latitude: data.coordinates?.lat,
          longitude: data.coordinates?.lng,
        }

        // Validate the data
        const validatedData = statusUpdateSchema.parse(statusUpdateData)

        // Start a database transaction
        const { error: transactionError } = await supabase.rpc('update_shipment_status', {
          p_shipment_id: validatedData.shipment_id,
          p_status_from: validatedData.status_from,
          p_status_to: validatedData.status_to,
          p_updated_by: user.id,
          p_notes: validatedData.notes,
          p_location: validatedData.location,
          p_latitude: validatedData.latitude,
          p_longitude: validatedData.longitude,
        })

        if (transactionError) {
          throw new Error(`Status update failed: ${transactionError.message}`)
        }

        // Update local store
        updateShipment(data.shipment_id, {
          status: data.status_to,
          updated_at: new Date().toISOString(),
        })

        console.log('Status updated successfully:', {
          shipment: data.shipment_id,
          from: currentStatus,
          to: data.status_to,
        })
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update status'
        setError(errorMessage)
        console.error('Status update error:', err)
        throw new Error(errorMessage)
      } finally {
        setIsLoading(false)
      }
    },
    [user, profile, supabase, getShipmentCurrentStatus, updateShipment]
  )

  // Get status history for a shipment
  const getStatusHistory = useCallback(
    async (shipmentId: string): Promise<StatusHistory[]> => {
      setIsLoading(true)
      setError(null)

      try {
        const { data, error } = await supabase
          .from('status_history')
          .select(`
            *,
            updated_by_profile:profiles!updated_by(
              user_id,
              full_name,
              email
            )
          `)
          .eq('shipment_id', shipmentId)
          .order('created_at', { ascending: false })

        if (error) throw error

        return data as StatusHistory[]
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch status history'
        setError(errorMessage)
        console.error('Status history fetch error:', err)
        return []
      } finally {
        setIsLoading(false)
      }
    },
    [supabase]
  )

  return {
    updateStatus,
    getStatusHistory,
    isLoading,
    error,
    clearError,
  }
}

// Hook for bulk status operations
interface UseBulkStatusUpdatesReturn {
  bulkUpdateStatus: (shipmentIds: string[], status: ShipmentStatus, notes?: string) => Promise<void>
  isLoading: boolean
  error: string | null
  clearError: () => void
}

export function useBulkStatusUpdates(): UseBulkStatusUpdatesReturn {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const { user, profile } = useAuth()

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const bulkUpdateStatus = useCallback(
    async (shipmentIds: string[], status: ShipmentStatus, notes?: string): Promise<void> => {
      if (!user || !profile) {
        throw new Error('User authentication required')
      }

      if (shipmentIds.length === 0) {
        throw new Error('No shipments selected for update')
      }

      setIsLoading(true)
      setError(null)

      try {
        // Use RPC for bulk operations to maintain data consistency
        const { error: bulkError } = await supabase.rpc('bulk_update_shipment_status', {
          p_shipment_ids: shipmentIds,
          p_new_status: status,
          p_notes: notes,
          p_updated_by: user.id,
        })

        if (bulkError) {
          throw new Error(`Bulk status update failed: ${bulkError.message}`)
        }

        console.log('Bulk status update successful:', {
          count: shipmentIds.length,
          status,
          notes,
        })
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to bulk update status'
        setError(errorMessage)
        console.error('Bulk status update error:', err)
        throw new Error(errorMessage)
      } finally {
        setIsLoading(false)
      }
    },
    [user, profile, supabase]
  )

  return {
    bulkUpdateStatus,
    isLoading,
    error,
    clearError,
  }
}

// Hook for status validation
interface UseStatusValidationReturn {
  validateTransition: (currentStatus: ShipmentStatus, nextStatus: ShipmentStatus) => boolean
  getValidNextStatuses: (currentStatus: ShipmentStatus) => ShipmentStatus[]
  isTerminalStatus: (status: ShipmentStatus) => boolean
}

export function useStatusValidation(): UseStatusValidationReturn {
  const validateTransition = useCallback(
    (currentStatus: ShipmentStatus, nextStatus: ShipmentStatus): boolean => {
      return isValidStatusTransition(currentStatus, nextStatus)
    },
    []
  )

  const getValidNextStatuses = useCallback(
    (currentStatus: ShipmentStatus): ShipmentStatus[] => {
      // Import here to avoid circular dependencies
      const { getNextValidStatuses } = require('@/lib/validations/status-updates')
      return getNextValidStatuses(currentStatus)
    },
    []
  )

  const isTerminalStatus = useCallback(
    (status: ShipmentStatus): boolean => {
      return status === 'completed' || status === 'cancelled'
    },
    []
  )

  return {
    validateTransition,
    getValidNextStatuses,
    isTerminalStatus,
  }
}

// Hook for status analytics
interface StatusAnalytics {
  totalUpdates: number
  averageTimePerStatus: Record<string, number>
  statusDistribution: Record<string, number>
  recentActivity: StatusHistory[]
}

interface UseStatusAnalyticsReturn {
  getAnalytics: (shipmentIds: string[], days?: number) => Promise<StatusAnalytics>
  isLoading: boolean
  error: string | null
}

export function useStatusAnalytics(): UseStatusAnalyticsReturn {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  const getAnalytics = useCallback(
    async (shipmentIds: string[], days: number = 30): Promise<StatusAnalytics> => {
      setIsLoading(true)
      setError(null)

      try {
        const startDate = new Date()
        startDate.setDate(startDate.getDate() - days)

        const { data, error } = await supabase
          .from('status_history')
          .select(`
            *,
            updated_by_profile:profiles!updated_by(
              user_id,
              full_name,
              email
            )
          `)
          .in('shipment_id', shipmentIds)
          .gte('created_at', startDate.toISOString())
          .order('created_at', { ascending: false })

        if (error) throw error

        const statusHistory = data as StatusHistory[]

        // Calculate analytics
        const analytics: StatusAnalytics = {
          totalUpdates: statusHistory.length,
          averageTimePerStatus: {},
          statusDistribution: {},
          recentActivity: statusHistory.slice(0, 10),
        }

        // Calculate status distribution
        statusHistory.forEach(item => {
          analytics.statusDistribution[item.status_to] =
            (analytics.statusDistribution[item.status_to] || 0) + 1
        })

        // TODO: Calculate average time per status (requires more complex logic)
        // This would involve grouping by shipment and calculating time differences

        return analytics
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch analytics'
        setError(errorMessage)
        console.error('Status analytics error:', err)
        return {
          totalUpdates: 0,
          averageTimePerStatus: {},
          statusDistribution: {},
          recentActivity: [],
        }
      } finally {
        setIsLoading(false)
      }
    },
    [supabase]
  )

  return {
    getAnalytics,
    isLoading,
    error,
  }
}