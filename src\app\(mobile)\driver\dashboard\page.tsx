'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { User, RefreshCw, Truck, MapPin, Clock, AlertCircle, ChevronDown } from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'
import { createClient } from '@/lib/supabase/client'
import { usePullToRefresh, useOfflineStatus } from '@/hooks/use-mobile'

import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ShipmentCard } from '@/components/mobile/shipment-card'
import { OfflineIndicator } from '@/components/mobile/offline-indicator'
import { PWAInstallPrompt } from '@/components/mobile/pwa-install-prompt'
import { LanguageSelector } from '@/components/mobile/language-selector'
import { useLanguage } from '@/hooks/use-language'

interface DriverAssignment {
  id: string
  shipment: {
    id: string
    shipment_number: string
    status: string
    customer?: {
      name: string
    }
    factory?: {
      name: string
    }
    origin_port?: {
      name: string
    }
    destination_port?: {
      name: string
    }
    etd_date?: string
    eta_date?: string
    products?: Array<{
      product_description?: string
      quantity: number
      packaging_type: string
      unit_of_measure?: {
        name: string
      }
      product?: {
        name: string
      }
    }>
    containers?: Array<{
      id: string
      container_number?: string
      seal_number?: string
      container_type?: string
      container_size?: string
      status?: string
      container_number_confirmed?: boolean
      container_number_confirmed_by?: string | null
      container_number_confirmed_at?: string | null
      seal_number_confirmed?: boolean
      seal_number_confirmed_by?: string | null
      seal_number_confirmed_at?: string | null
    }>
    status_updates?: Array<{
      status_to: string
      created_at: string
    }>
  }
  pickup_container_location?: string
  pickup_product_location?: string
  delivery_location?: string
  assignment_date?: string
  estimated_distance?: number
}

export default function DriverDashboardPage() {
  const [assignments, setAssignments] = useState<DriverAssignment[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [driverId, setDriverId] = useState<string | null>(null)
  const { user, profile, loading, isAuthenticated, signOut } = useAuth()
  const { isOnline } = useOfflineStatus()
  const { t } = useLanguage()
  const router = useRouter()
  const supabase = createClient()
  const loadingRef = useRef(false) // Prevent race conditions

  // Redirect if not authenticated or not a driver
  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        router.push('/driver/login')
        return
      }
      
      if (profile?.role !== 'driver') {
        router.push('/dashboard') // Redirect non-drivers to main app
        return
      }
    }
  }, [isAuthenticated, loading, profile, router])

  // Load driver assignments with race condition prevention
  const loadAssignments = async (forceRefresh = false) => {
    if (!profile?.user_id || loadingRef.current) return

    loadingRef.current = true
    setError(null)

    // Only show loading indicator on initial load or force refresh
    if (forceRefresh || assignments.length === 0) {
      setIsLoading(true)
    }

    try {
      // First get or use cached driver info
      let currentDriverId = driverId

      if (!currentDriverId) {
        const { data: driverData, error: driverError } = await supabase
          .from('drivers')
          .select('id, carrier_id')
          .eq('user_id', profile.user_id)
          .single()

        if (driverError) {
          throw new Error('Driver profile not found. Please contact your dispatcher.')
        }

        currentDriverId = driverData.id
        setDriverId(currentDriverId)
      }

      // Get transportation assignments for this driver
      const { data, error } = await supabase
        .from('transportation')
        .select(`
          id,
          pickup_container_location,
          pickup_product_location,
          delivery_location,
          assignment_date,
          estimated_distance,
          shipment:shipments (
            id,
            shipment_number,
            status,
            etd_date,
            eta_date,
            customer:customer_id (
              name
            ),
            factory:factory_id (
              name
            ),
            origin_port:origin_port_id (
              name
            ),
            destination_port:destination_port_id (
              name
            ),
            products:shipment_products (
              product_description,
              quantity,
              packaging_type,
              unit_of_measure:unit_of_measure_id (
                name
              ),
              product:product_id (
                name
              )
            ),
            containers:containers (
              id,
              container_number,
              seal_number,
              container_type,
              container_size,
              status,
              container_number_confirmed,
              container_number_confirmed_by,
              container_number_confirmed_at,
              seal_number_confirmed,
              seal_number_confirmed_by,
              seal_number_confirmed_at
            ),
            status_updates:status_history (
              status_to,
              created_at
            )
          )
        `)
        .eq('driver_id', currentDriverId)
        .order('assignment_date', { ascending: false })
        .limit(20)

      if (error) {
        throw new Error('Failed to load assignments')
      }

      setAssignments(data || [])
    } catch (err) {
      console.error('Error loading assignments:', err)
      setError(err instanceof Error ? err.message : 'Failed to load assignments')
    } finally {
      setIsLoading(false)
      loadingRef.current = false
    }
  }

  // Real-time subscriptions for assignment updates
  useEffect(() => {
    if (!driverId || !profile?.role) return

    console.log('Setting up real-time subscription for driver:', driverId)
    
    const subscription = supabase
      .channel('driver-assignments')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'transportation',
          filter: `driver_id=eq.${driverId}`,
        },
        (payload) => {
          console.log('Real-time update received:', payload)
          // Reload assignments when transportation data changes
          loadAssignments(false)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'shipments',
        },
        (payload) => {
          console.log('Shipment status update received:', payload)
          // Check if this shipment affects current assignments
          setAssignments(current =>
            current.map(assignment => {
              if (assignment.shipment.id === payload.new.id) {
                return {
                  ...assignment,
                  shipment: { ...assignment.shipment, ...payload.new }
                }
              }
              return assignment
            })
          )
        }
      )
      .subscribe()

    return () => {
      console.log('Cleaning up real-time subscription')
      supabase.removeChannel(subscription)
    }
  }, [driverId, profile?.role, supabase])

  // Handle online/offline sync
  useEffect(() => {
    const handleOnlineSync = () => {
      console.log('Back online - syncing data')
      loadAssignments(true)
    }

    window.addEventListener('online-sync', handleOnlineSync)
    return () => window.removeEventListener('online-sync', handleOnlineSync)
  }, [])

  // Initial load
  useEffect(() => {
    if (profile?.role === 'driver') {
      loadAssignments(true)
    }
  }, [profile])

  // Pull-to-refresh functionality
  const { containerRef, isPulling, isRefreshing, pullDistance, pullProgress } = usePullToRefresh({
    onRefresh: () => loadAssignments(true),
    threshold: 80,
    resistance: 2.5
  })

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut()
      router.push('/driver/login')
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  if (loading || !profile) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="flex items-center space-x-2 text-white">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span className="text-lg">{t('common.loading')}</span>
        </div>
      </div>
    )
  }

  return (
    <div ref={containerRef} className="min-h-screen bg-slate-900 text-white relative">
      {/* Offline Indicator */}
      <OfflineIndicator />

      {/* Pull-to-refresh indicator */}
      {(isPulling || isRefreshing) && (
        <div 
          className="fixed top-0 left-0 right-0 z-40 flex justify-center pt-4"
          style={{
            transform: `translateY(${Math.min(pullDistance * 0.5, 40)}px)`,
            opacity: Math.min(pullProgress * 2, 1)
          }}
        >
          <div className="bg-slate-800 rounded-full px-4 py-2 border border-slate-700 flex items-center space-x-2">
            <RefreshCw className={`w-4 h-4 text-orange-500 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span className="text-sm text-white">
              {isRefreshing ? t('dashboard.refreshing') : isPulling ? t('dashboard.releaseToRefresh') : t('dashboard.pullToRefresh')}
            </span>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="bg-gradient-to-r from-slate-800 to-slate-900 border-b border-slate-700">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-white">
                  {t('dashboard.welcome')}, {profile.first_name}
                </h1>
                <p className="text-sm text-slate-400">
                  {t('dashboard.driver')} {!isOnline && t('dashboard.offline')}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <LanguageSelector />
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="text-slate-400 hover:text-white hover:bg-slate-700"
              >
                {t('dashboard.signOut')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="px-4 py-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-slate-800 rounded-xl p-4 border border-slate-700">
            <div className="flex items-center space-x-2">
              <Truck className="w-5 h-5 text-orange-500" />
              <span className="text-slate-400 text-sm">{t('dashboard.activeJobs')}</span>
            </div>
            <p className="text-2xl font-bold text-white mt-1">
              {assignments.length}
            </p>
          </div>
          <div className="bg-slate-800 rounded-xl p-4 border border-slate-700">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-500" />
              <span className="text-slate-400 text-sm">{t('dashboard.thisWeek')}</span>
            </div>
            <p className="text-2xl font-bold text-white mt-1">
              {assignments.filter(a => {
                if (!a.assignment_date) return false
                const assignmentDate = new Date(a.assignment_date)
                const weekAgo = new Date()
                weekAgo.setDate(weekAgo.getDate() - 7)
                return assignmentDate >= weekAgo
              }).length}
            </p>
          </div>
        </div>
      </div>

      {/* Manual Refresh Button (fallback for devices without pull-to-refresh) */}
      <div className="px-4 pb-4">
        <Button
          onClick={() => loadAssignments(true)}
          disabled={loadingRef.current}
          variant="outline"
          className="w-full h-12 bg-slate-800 border-slate-700 text-white hover:bg-slate-700 disabled:opacity-50"
        >
          {loadingRef.current ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              {t('dashboard.refreshing')}
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4 mr-2" />
              {t('dashboard.refresh')}
            </>
          )}
        </Button>
        {!isOnline && (
          <p className="text-xs text-amber-400 mt-2 text-center">
            {t('dashboard.offlineCached')}
          </p>
        )}
      </div>

      <Separator className="bg-slate-700" />

      {/* Assignments List */}
      <div className="px-4 py-4">
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
          <MapPin className="w-5 h-5 mr-2 text-orange-500" />
          {t('dashboard.yourAssignments')}
        </h2>

        {error && (
          <div className="mb-4 p-4 bg-red-900/20 border border-red-800 rounded-lg flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        )}

        {isLoading && !isRefreshing ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-slate-800 rounded-xl p-4 border border-slate-700 animate-pulse">
                <div className="h-4 bg-slate-700 rounded mb-2"></div>
                <div className="h-3 bg-slate-700 rounded w-2/3 mb-2"></div>
                <div className="h-3 bg-slate-700 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : assignments.length === 0 ? (
          <div className="text-center py-12">
            <Truck className="w-16 h-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-300 mb-2">
              {t('dashboard.noAssignments')}
            </h3>
            <p className="text-slate-400 text-sm">
              {t('dashboard.noAssignmentsDesc')}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {assignments.map((assignment) => (
              <ShipmentCard key={assignment.id} assignment={assignment} />
            ))}
          </div>
        )}
      </div>

      {/* PWA Install Prompt */}
      <PWAInstallPrompt />
    </div>
  )
}