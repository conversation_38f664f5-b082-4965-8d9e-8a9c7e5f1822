# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Next.js
.next/
out/
build/
dist/
.vercel/

# Environment variables
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE & Editors
.vscode/
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
coverage/
*.lcov
playwright-report/
test-results/
.nyc_output

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.db
*.sqlite
*.sqlite3

# Cache & temporary files
.cache/
.temp/
.tmp/
*.pid
*.seed
*.pid.lock

# Runtime data
pids/
lib-cov/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ESLint cache
.eslintcache

# Stylelint cache
.stylelintcache

# Storybook build outputs
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# Local Netlify folder
.netlify

# Sentry Config File
.sentryclirc

# Trading/Finance specific (if applicable)
# Add any trading data, API keys, or sensitive financial data patterns
trading-data/
market-data/
*.key
*.pem
*.p12
*.pfx

# Backup files
*.bak
*.backup
*~

# Lock files (choose one based on your package manager)
# package-lock.json  # Uncomment if using yarn exclusively
# yarn.lock         # Uncomment if using npm exclusively