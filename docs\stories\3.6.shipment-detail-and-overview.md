# Story 3.6: Shipment Detail and Overview

## Status
Approved

## Story
**As a** CS representative,  
**I want** comprehensive shipment detail views,  
**so that** I can review all information and coordinate with stakeholders effectively.

## Acceptance Criteria

**1:** Shipment detail page displays complete information including stakeholders, containers, products, status history, and documents.

**2:** Editable sections allow updates to shipment details with proper validation and permission checks.

**3:** Related information shows customer-product pricing, shipper details, and notify party preferences automatically.

**4:** Action buttons provide quick access to common operations (status update, document generation, notifications).

**5:** Responsive design ensures detail view works effectively on tablets and mobile devices.

**6:** Container and product editing allows modification of quantities, weights, pricing, and other detailed attributes with proper validation and automatic calculation of total values (CIF/FOB) based on quantity, net weight, and unit prices.

## Tasks / Subtasks

- [ ] Create shipment detail page component (AC: 1)
  - [ ] Design responsive layout structure for shipment details
  - [ ] Implement stakeholder information display section
  - [ ] Add container details section with weight/volume summaries
  - [ ] Create products section with pricing and packaging details
  - [ ] Build status history timeline component
  - [ ] Add documents section with upload/preview capabilities

- [ ] Implement editable sections with validation (AC: 2)
  - [ ] Create inline edit components for shipment fields
  - [ ] Add form validation using Zod schemas from lib/validations/shipment.ts
  - [ ] Implement permission checks based on user roles
  - [ ] Add save/cancel functionality with optimistic updates
  - [ ] Handle edit mode state management with Zustand
  - [ ] Create shipment edit page with full form editing capabilities
  - [ ] Add containers editing section with capacity validation
  - [ ] Add shipment products editing with pricing calculations
  - [x] Implement add/remove functionality for containers and products
  - [x] Add product selection modal with dropdown of available products
  - [x] Create addProductToShipment function to insert new shipment_products records
  - [x] Integrate Add Product functionality with existing edit page layout

- [x] Build relationship intelligence displays (AC: 3)
  - [x] Fetch and display customer-product pricing information
  - [x] Show shipper contact details and preferences
  - [x] Display notify party communication preferences
  - [x] Implement auto-refresh for related data changes

- [x] Add action buttons and operations (AC: 4)
  - [x] Create status update modal/drawer
  - [x] Add document generation trigger buttons
  - [x] Implement stakeholder notification actions
  - [x] Add quick edit shortcuts for common operations

- [ ] Ensure mobile responsiveness (AC: 5)
  - [ ] Implement mobile-first responsive design with Tailwind CSS
  - [ ] Create collapsible sections for mobile view
  - [ ] Optimize touch targets and form inputs for tablets
  - [ ] Test across different screen sizes and orientations

- [ ] Container and product editing functionality (AC: 6)
  - [ ] Create container editing forms with validation (type, size, weight, volume)
  - [ ] Implement product editing forms with pricing calculations
  - [ ] Add container capacity validation and warnings
  - [ ] Implement automatic calculation of total values (CIF/FOB)
    - [ ] Total CIF Value = Quantity × Net Weight × Unit Price CIF
    - [ ] Total FOB Value = Quantity × Net Weight × Unit Price FOB
    - [ ] Real-time calculation on field changes (quantity, net weight, unit prices)
    - [ ] Visual indicators for auto-calculated fields
    - [ ] Formula display for user understanding
  - [ ] Add validation for weight consistency (gross ≥ net weight)
  - [ ] Create add/remove functionality for containers and products
  - [ ] Implement date validation for manufacturing and expiry dates
  - [ ] Add packaging compatibility checks

- [ ] Unit and Integration Testing
  - [ ] Write unit tests for shipment detail component
  - [ ] Test edit functionality and validation
  - [ ] Test responsive behavior across breakpoints
  - [ ] Integration test with real-time updates
  - [ ] Test container and product editing validation
  - [x] Test add/remove container and product functionality
  - [x] Test Add Product modal functionality and product selection
  - [x] Test product insertion into shipment_products table
  - [ ] Test automatic pricing calculations (CIF/FOB totals)
  - [ ] Test calculation accuracy with edge cases (decimal precision, zero values)
  - [ ] Test real-time calculation updates on field changes

## Dev Notes

### Previous Story Insights
No previous story insights available - this is the first story in the development cycle.

### Data Models [Source: architecture/data-models.md#shipment]
- **Shipment Interface**: Complete shipment entity with stakeholder references, dates, status, and transportation mode
- **Related Entities**: Customer, shipper, consignee, notify_party, factory, forwarder_agent companies; origin/destination ports; containers; products; status_history
- **Key Relationships**: Many-to-one with Company (multiple roles), one-to-many with Container/ShipmentProduct/StatusHistory

### API Specifications [Source: architecture/frontend-architecture.md#api-client-setup]
- **Supabase Client**: Use createClientComponentClient with Database types from @/types/database
- **Real-time Integration**: Supabase subscriptions for status updates using subscribeToTable helper
- **Error Handling**: SupabaseService.withErrorHandling for consistent error management

### Component Specifications [Source: architecture/frontend-architecture.md#component-organization]
- **Location**: Place in src/app/(dashboard)/shipments/[id]/page.tsx
- **UI Components**: Use ShadCN UI components from components/ui/ (Button, Card, Badge, Sheet, Form)
- **Data Display**: Use components/data-display/ for shipment-card.tsx, status-badge.tsx, timeline.tsx
- **State Management**: Zustand store in stores/shipment-store.ts with real-time subscription management

### File Locations [Source: architecture/unified-project-structure.md]
- **Main Page**: src/app/(dashboard)/shipments/[id]/page.tsx
- **Edit Page**: src/app/(dashboard)/shipments/[id]/edit/page.tsx
- **Components**: src/components/data-display/shipment-detail.tsx
- **Edit Components**: src/components/forms/shipment-form/shipment-edit-inline.tsx
- **Stores**: src/stores/shipment-store.ts (existing)
- **Types**: src/types/shipment.ts for extended shipment types
- **Hooks**: src/hooks/use-shipments.ts for CRUD operations
- **Validations**: src/lib/validations/container-product.ts for container and product validation schemas

### Testing Requirements
No specific testing guidance found in architecture docs - implement following standard practices:
- Unit tests for components in tests/unit/components/
- Integration tests in tests/integration/
- Use Vitest + Testing Library for component testing
- Playwright for E2E testing of complete shipment detail workflow

### Technical Constraints [Source: architecture/tech-stack.md]
- **Framework**: Next.js 14.2+ App Router with TypeScript 5.3+
- **UI Library**: ShadCN UI with Tailwind CSS dark blue theme (#1e293b, #0f172a) and orange accents (#f97316)
- **State Management**: Zustand 4.5+ with TypeScript-first approach
- **Database**: Supabase client with auto-generated TypeScript types
- **Mobile Support**: Progressive Web App (PWA) capabilities required

### Project Structure Notes [Source: architecture/unified-project-structure.md]
- Uses App Router pattern with (dashboard) route group
- Shipment routes follow pattern: shipments/[id]/page.tsx for details, shipments/[id]/edit/page.tsx for editing
- Components organized by domain: forms/, data-display/, ui/
- Real-time subscriptions managed in hooks/use-real-time.ts

### Testing
- **Test File Location**: tests/unit/components/shipment-detail.test.tsx
- **Testing Frameworks**: Vitest + Testing Library for unit tests, Playwright for E2E
- **Test Standards**: 80% coverage target, test component rendering, user interactions, and data fetching
- **Specific Requirements**: Test responsive behavior, edit functionality, real-time updates integration

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-09 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

*This section is populated by the development agent during implementation*

### Agent Model Used
*To be populated by dev agent*

### Debug Log References
*To be populated by dev agent*

### Completion Notes List
**2025-01-11**: Added Add Product functionality to Edit Shipment page
- Implemented product selection modal with dropdown of available products
- Created addProductToShipment function to insert new shipment_products records
- Integrated add product functionality with existing edit page layout
- Added Dialog UI component for product selection with proper loading states
- Products are inserted with default values and can be edited using existing edit functionality
- Added comprehensive error handling and user feedback
- Added optional container selection to associate products with specific containers
- Container dropdown shows available containers in the shipment with details (number, type, size, status)
- Products can be added without container selection (container_id will be null)

**2025-01-11**: Implemented Task AC:3 - Build relationship intelligence displays
- Enhanced customer-product pricing information display with CIF/FOB prices and available products list
- Improved shipper contact details display with phone, email, and default status indicators
- Added comprehensive notify party communication preferences display showing email, SMS, LINE, WeChat options
- Implemented special instructions display for notify party relationships
- Added auto-refresh functionality that updates relationship intelligence every 30 seconds
- Enhanced manual refresh button to also refresh relationship data
- Added visual indicators for auto-refresh functionality on intelligence cards

**2025-01-11**: Implemented Task AC:4 - Add action buttons and operations
- ✅ Status update modal/drawer was already implemented using StatusUpdateForm component
- ✅ Created comprehensive DocumentGenerationModal with document categories (shipping, certificates, financial, internal)
- ✅ Implemented StakeholderNotificationModal with multi-channel support (Email, SMS, LINE, WeChat)
- ✅ Added QuickEditShortcuts component for common operations (ETD, ETA, transportation mode, closing time)
- ✅ Integrated all new modals and components into shipment detail page with proper state management
- Enhanced action button section with functional document generation and stakeholder notification triggers
- Added quick edit dropdown with field-specific editing for staff users
- All components include proper loading states, error handling, and user feedback
- Document generation supports multiple categories with availability status and batch selection
- Stakeholder notifications intelligently populate recipients from shipment relationships with preferred channels
- Quick edit shortcuts provide inline editing for common fields with validation

### File List
**Modified Files:**
- `src/app/(dashboard)/shipments/[id]/page.tsx` - Enhanced relationship intelligence displays with auto-refresh functionality and integrated new action button modals

**Created Files:**
- `src/components/forms/shipment-form/document-generation-modal.tsx` - Comprehensive document generation modal with categorized documents
- `src/components/forms/shipment-form/stakeholder-notification-modal.tsx` - Multi-channel stakeholder notification system
- `src/components/forms/shipment-form/quick-edit-shortcuts.tsx` - Quick edit component for common shipment fields

## QA Results

*Results from QA Agent QA review of the completed story implementation*