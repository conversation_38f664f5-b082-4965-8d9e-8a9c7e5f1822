'use client'

import { useEffect, useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Ship, 
  FileText, 
  Truck, 
  Building, 
  Package, 
  Anchor, 
  User,
  Plus,
  TrendingUp,
  TrendingDown,
  Activity,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react'
import { useShipmentsData } from '@/hooks/use-shipments'
import { useCompaniesData } from '@/hooks/use-companies'
import { useProductsData } from '@/hooks/use-products'
import { usePortsData } from '@/hooks/use-ports'
import { useDriversData } from '@/hooks/use-drivers'
import { createClient } from '@/lib/supabase/client'


function StatCard({ title, value, trend, icon: Icon, color }: {
  title: string
  value: string  
  trend: string
  icon: any
  color: string
}) {
  const isPositive = trend.startsWith('+')
  const isNegative = trend.startsWith('-')
  
  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-slate-400 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-white mt-1">{value}</p>
            <div className="flex items-center mt-2">
              {isPositive && <TrendingUp className="w-3 h-3 text-green-500 mr-1" />}
              {isNegative && <TrendingDown className="w-3 h-3 text-red-500 mr-1" />}
              <span className={`text-xs font-medium ${
                isPositive ? 'text-green-500' : isNegative ? 'text-red-500' : 'text-slate-400'
              }`}>
                {trend}
              </span>
            </div>
          </div>
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
      </CardContent>
    </Card>
  )
}

function MasterDataButton({ icon: Icon, label, count, href }: {
  icon: any
  label: string
  count: string
  href: string
}) {
  return (
    <Button 
      variant="ghost"
      className="h-auto p-4 flex flex-col items-center space-y-2 bg-slate-800 hover:bg-slate-700 border border-slate-700"
      onClick={() => window.location.href = href}
    >
      <Icon className="w-6 h-6 text-orange-500" />
      <div className="text-center">
        <p className="text-sm font-medium text-white">{label}</p>
        <p className="text-xs text-slate-400">{count} total</p>
      </div>
    </Button>
  )
}

function ActivityItem({ activity, formatTime }: { activity: ActivityItem; formatTime: (date: string) => string }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />
      case 'info':
        return <Activity className="w-4 h-4 text-blue-500" />
      default:
        return <Clock className="w-4 h-4 text-slate-400" />
    }
  }

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-slate-700 rounded-lg transition-colors">
      <div className="flex-shrink-0 mt-0.5">
        {getStatusIcon(activity.status)}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm text-white">{activity.message}</p>
        <p className="text-xs text-slate-400 mt-1">
          {activity.user_name || 'System'} • {formatTime(activity.created_at)}
        </p>
      </div>
    </div>
  )
}

function SystemStatusIndicator({ service, status, uptime }: {
  service: string
  status: string
  uptime: string
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return 'bg-green-500'
      case 'degraded':
        return 'bg-yellow-500'
      case 'outage':
        return 'bg-red-500'
      default:
        return 'bg-slate-500'
    }
  }

  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${getStatusColor(status)}`} />
        <span className="text-sm text-white">{service}</span>
      </div>
      <div className="text-right">
        <span className="text-xs text-slate-400">{uptime}</span>
      </div>
    </div>
  )
}

interface DashboardStats {
  activeShipments: number
  totalCompanies: number
  totalProducts: number
  totalPorts: number
  totalDrivers: number
  onlineDrivers: number
}

interface ActivityItem {
  id: string
  type: string
  message: string
  user_name?: string
  created_at: string
  status?: string
}

export default function DashboardPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState<DashboardStats>({
    activeShipments: 0,
    totalCompanies: 0,
    totalProducts: 0,
    totalPorts: 0,
    totalDrivers: 0,
    onlineDrivers: 0
  })
  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([])
  
  const { totalCount: shipmentsCount, loading: shipmentsLoading } = useShipmentsData()
  const { totalCount: companiesCount, loading: companiesLoading } = useCompaniesData()
  const { totalCount: productsCount, loading: productsLoading } = useProductsData()
  const { totalCount: portsCount, loading: portsLoading } = usePortsData()
  const { totalCount: driversCount, loading: driversLoading } = useDriversData()
  
  const supabase = createClient()
  
  // Fetch dashboard statistics
  const fetchStats = useCallback(async () => {
    try {
      // Get active shipments count
      const { count: activeShipmentsCount } = await supabase
        .from('shipments')
        .select('*', { count: 'exact', head: true })
        .in('status', ['booking_confirmed', 'in_transit', 'customs_clearance'])
      
      // Get online drivers count (mock for now)
      const onlineDriversCount = Math.floor(driversCount * 0.7)
      
      setStats({
        activeShipments: activeShipmentsCount || 0,
        totalCompanies: companiesCount,
        totalProducts: productsCount,
        totalPorts: portsCount,
        totalDrivers: driversCount,
        onlineDrivers: onlineDriversCount
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    }
  }, [supabase, companiesCount, productsCount, portsCount, driversCount])
  
  // Fetch recent activity
  const fetchRecentActivity = useCallback(async () => {
    try {
      // Get recent shipments with customer info
      const { data: recentShipments } = await supabase
        .from('shipments')
        .select(`
          id,
          shipment_number,
          status,
          created_at,
          updated_at,
          customer:companies!customer_id(name)
        `)
        .order('created_at', { ascending: false })
        .limit(10)
      
      // Transform to activity items
      const activities: ActivityItem[] = []
      
      if (recentShipments) {
        recentShipments.forEach((shipment) => {
          activities.push({
            id: `shipment-${shipment.id}`,
            type: 'shipment_created',
            message: `New shipment ${shipment.shipment_number} created${shipment.customer ? ` for ${shipment.customer.name}` : ''}`,
            user_name: 'System',
            created_at: shipment.created_at,
            status: 'success'
          })
        })
      }
      
      setRecentActivity(activities.slice(0, 5))
    } catch (error) {
      console.error('Error fetching recent activity:', error)
    }
  }, [supabase])
  
  // Load data
  useEffect(() => {
    const loadDashboardData = async () => {
      setIsLoading(true)
      await Promise.all([
        fetchStats(),
        fetchRecentActivity()
      ])
      setIsLoading(false)
    }
    
    // Only load when all counts are available
    if (!shipmentsLoading && !companiesLoading && !productsLoading && !portsLoading && !driversLoading) {
      loadDashboardData()
    }
  }, [fetchStats, fetchRecentActivity, shipmentsLoading, companiesLoading, productsLoading, portsLoading, driversLoading])
  
  // Format relative time
  const formatRelativeTime = useCallback((dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffMinutes < 1) return 'Just now'
    if (diffMinutes < 60) return `${diffMinutes} minutes ago`
    if (diffHours < 24) return `${diffHours} hours ago`
    return `${diffDays} days ago`
  }, [])
  
  // Calculate trends (mock calculation)
  const calculateTrend = useCallback((current: number, type: string) => {
    // Mock trend calculation - in real app this would compare with previous period
    const trends = {
      activeShipments: Math.random() > 0.5 ? '+' : '-',
      totalCompanies: '+',
      onlineDrivers: Math.random() > 0.3 ? '+' : '-'
    }
    const percentage = Math.floor(Math.random() * 15) + 1
    return `${trends[type as keyof typeof trends] || '+'}${percentage}%`
  }, [])
  
  // Generate dynamic stats data
  const statsData = [
    {
      title: "Active Shipments",
      value: stats.activeShipments.toString(),
      trend: calculateTrend(stats.activeShipments, 'activeShipments'),
      icon: Ship,
      color: "text-blue-500"
    },
    {
      title: "Total Companies",
      value: stats.totalCompanies.toString(),
      trend: calculateTrend(stats.totalCompanies, 'totalCompanies'),
      icon: Building,
      color: "text-green-500"
    },
    {
      title: "Drivers Online",
      value: `${stats.onlineDrivers}/${stats.totalDrivers}`,
      trend: calculateTrend(stats.onlineDrivers, 'onlineDrivers'),
      icon: Truck,
      color: "text-orange-500"
    }
  ]
  
  // Generate dynamic master data buttons
  const masterDataButtons = [
    {
      icon: Building,
      label: "Companies",
      count: stats.totalCompanies.toString(),
      href: "/master-data/companies"
    },
    {
      icon: Package,
      label: "Products",
      count: stats.totalProducts.toString(),
      href: "/master-data/products"
    },
    {
      icon: Anchor,
      label: "Ports",
      count: stats.totalPorts.toString(),
      href: "/master-data/ports"
    },
    {
      icon: User,
      label: "Drivers",
      count: stats.totalDrivers.toString(),
      href: "/master-data/drivers"
    }
  ]

  // Generate dynamic system status
  const systemStatus = [
    { service: "Database", status: "operational", uptime: "99.9%" },
    { service: "API Services", status: "operational", uptime: "99.8%" },
    { service: "File Storage", status: "operational", uptime: "100%" },
    { service: "Supabase Auth", status: "operational", uptime: "99.7%" }
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
          <p className="text-slate-300 mt-4">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
        <p className="text-slate-400">Welcome back! Here's what's happening with your trading operations.</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {statsData.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Master Data Management */}
        <Card className="lg:col-span-2 bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center justify-between">
              Master Data Management
              <Button 
                size="sm" 
                className="bg-orange-500 hover:bg-orange-600 text-white"
                onClick={() => window.location.href = '/master-data/companies'}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add New
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {masterDataButtons.map((button, index) => (
                <MasterDataButton key={index} {...button} />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Status */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">System Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            {systemStatus.map((item, index) => (
              <SystemStatusIndicator key={index} {...item} />
            ))}
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="lg:col-span-2 bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <div className="max-h-80 overflow-y-auto">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity) => (
                  <ActivityItem key={activity.id} activity={activity} formatTime={formatRelativeTime} />
                ))
              ) : (
                <div className="text-center text-slate-400 py-8">
                  <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No recent activity</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              className="w-full bg-orange-500 hover:bg-orange-600 text-white justify-start"
              onClick={() => window.location.href = '/shipments/create'}
            >
              <Ship className="w-4 h-4 mr-2" />
              Create New Shipment
            </Button>
            <Button 
              variant="secondary"
              className="w-full bg-slate-700 hover:bg-slate-600 text-white justify-start"
              onClick={() => window.location.href = '/master-data/companies'}
            >
              <Building className="w-4 h-4 mr-2" />
              Manage Companies
            </Button>
            <Button 
              variant="secondary"
              className="w-full bg-slate-700 hover:bg-slate-600 text-white justify-start"
              onClick={() => window.location.href = '/admin/users'}
            >
              <User className="w-4 h-4 mr-2" />
              User Management
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}