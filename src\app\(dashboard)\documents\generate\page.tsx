import { Suspense } from 'react'
import { Metadata } from 'next'
import { DocumentGenerationInterface } from '@/components/forms/document-generation-form/document-generation-interface'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

export const metadata: Metadata = {
  title: 'Document Generation - DYY Trading',
  description: 'Generate professional export documents from shipment data'
}

function DocumentGenerationSkeleton() {
  return (
    <div className="container mx-auto px-4 py-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Loading document generation interface...</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function DocumentGenerationError({ error }: { error: Error }) {
  return (
    <div className="container mx-auto px-4 py-6">
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load document generation interface: {error.message}
        </AlertDescription>
      </Alert>
    </div>
  )
}

/**
 * Document Generation Page with Error Boundary
 * Story 5.2: Automated Document Generation Engine
 */
export default function DocumentGenerationPage() {
  return (
    <div className="container mx-auto px-4 py-6">
      <Suspense fallback={<DocumentGenerationSkeleton />}>
        <ErrorBoundary>
          <DocumentGenerationInterface />
        </ErrorBoundary>
      </Suspense>
    </div>
  )
}

// Simple Error Boundary component
function ErrorBoundary({ children }: { children: React.ReactNode }) {
  try {
    return <>{children}</>
  } catch (error) {
    return <DocumentGenerationError error={error as Error} />
  }
}
