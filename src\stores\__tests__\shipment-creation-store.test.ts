import { describe, it, expect, beforeEach, vi } from 'vitest'
import { act, renderHook } from '@testing-library/react'
import { useShipmentCreationStore } from '../shipment-creation-store'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

describe('useShipmentCreationStore', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useShipmentCreationStore.getState().resetForm()
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useShipmentCreationStore())
      const state = result.current

      expect(state.currentStep).toBe(1)
      expect(state.formData).toEqual({
        transportMode: undefined,
        customerId: '',
        shipperId: '',
        consigneeId: '',
        notifyPartyId: '',
        portOfLoading: '',
        portOfDischarge: '',
        containerType: undefined,
        containerQuantity: 1,
        cargoDescription: '',
        grossWeight: 0,
        documents: [],
      })
      expect(state.validation).toEqual({
        isValid: false,
        errors: {},
        touchedFields: new Set(),
      })
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(state.isDraft).toBe(false)
      expect(state.autoSaveEnabled).toBe(true)
    })
  })

  describe('form navigation', () => {
    it('should navigate to next step', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.setStep(2)
      })

      expect(result.current.currentStep).toBe(2)
    })

    it('should navigate to previous step', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.setStep(3)
      })
      act(() => {
        result.current.setStep(2)
      })

      expect(result.current.currentStep).toBe(2)
    })

    it('should not allow step values outside valid range', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.setStep(0)
      })
      expect(result.current.currentStep).toBe(1)

      act(() => {
        result.current.setStep(10)
      })
      expect(result.current.currentStep).toBe(6) // Max step
    })
  })

  describe('form data updates', () => {
    it('should update single form field', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.updateFormData({ transportMode: 'sea' })
      })

      expect(result.current.formData.transportMode).toBe('sea')
    })

    it('should update multiple form fields', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.updateFormData({
          transportMode: 'sea',
          customerId: 'cust_123',
          portOfLoading: 'THBKK',
        })
      })

      expect(result.current.formData.transportMode).toBe('sea')
      expect(result.current.formData.customerId).toBe('cust_123')
      expect(result.current.formData.portOfLoading).toBe('THBKK')
    })

    it('should mark draft status when data is updated', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.updateFormData({ cargoDescription: 'Test cargo' })
      })

      expect(result.current.isDraft).toBe(true)
    })
  })

  describe('validation', () => {
    it('should mark field as touched', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.markFieldTouched('transportMode')
      })

      expect(result.current.validation.touchedFields.has('transportMode')).toBe(
        true
      )
    })

    it('should set validation errors', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      const errors = {
        transportMode: 'Transport mode is required',
        customerId: 'Customer is required',
      }

      act(() => {
        result.current.setValidationErrors(errors)
      })

      expect(result.current.validation.errors).toEqual(errors)
      expect(result.current.validation.isValid).toBe(false)
    })

    it('should clear validation errors when valid', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      // Set errors first
      act(() => {
        result.current.setValidationErrors({
          transportMode: 'Transport mode is required',
        })
      })

      // Clear errors
      act(() => {
        result.current.setValidationErrors({})
      })

      expect(result.current.validation.errors).toEqual({})
      expect(result.current.validation.isValid).toBe(true)
    })
  })

  describe('relationships', () => {
    it('should set relationship recommendations', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      const recommendations = {
        shippers: [
          { id: 'ship_1', name: 'Shipper One', email: '<EMAIL>' },
        ],
        consignees: [
          { id: 'cons_1', name: 'Consignee One', email: '<EMAIL>' },
        ],
        notifyParties: [],
        products: [],
        routes: [],
      }

      act(() => {
        result.current.setRelationshipRecommendations(recommendations)
      })

      expect(result.current.relationships.recommendations).toEqual(
        recommendations
      )
    })

    it('should set relationship analysis', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      const analysis = {
        customerId: 'cust_1',
        partnerId: 'ship_1',
        partnerType: 'shipper' as const,
        strength: 85,
        frequency: 12,
        averageValue: 50000,
        reliability: 95,
        preferredTransportMode: 'sea' as const,
        commonRoutes: ['THBKK-HKHKG'],
        lastShipment: new Date('2024-03-01'),
        trends: {
          volumeGrowth: 15,
          valueGrowth: 10,
          frequencyChange: 5,
        },
        riskFactors: [],
        recommendations: ['Continue partnership', 'Consider volume discounts'],
      }

      act(() => {
        result.current.setRelationshipAnalysis(analysis)
      })

      expect(result.current.relationships.analysis).toEqual(analysis)
    })

    it('should clear relationship data', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      // Set some data first
      act(() => {
        result.current.setRelationshipRecommendations({
          shippers: [
            { id: 'ship_1', name: 'Shipper One', email: '<EMAIL>' },
          ],
          consignees: [],
          notifyParties: [],
          products: [],
          routes: [],
        })
      })

      act(() => {
        result.current.clearRelationships()
      })

      expect(result.current.relationships.recommendations).toEqual({
        shippers: [],
        consignees: [],
        notifyParties: [],
        products: [],
        routes: [],
      })
      expect(result.current.relationships.analysis).toBeNull()
    })
  })

  describe('documents', () => {
    it('should add document', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      const document = {
        id: 'doc_1',
        name: 'invoice.pdf',
        type: 'invoice',
        url: 'https://example.com/invoice.pdf',
        uploadedAt: new Date(),
        uploadedBy: 'user_123',
      }

      act(() => {
        result.current.addDocument(document)
      })

      expect(result.current.formData.documents).toContain(document)
    })

    it('should remove document', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      const document = {
        id: 'doc_1',
        name: 'invoice.pdf',
        type: 'invoice',
        url: 'https://example.com/invoice.pdf',
        uploadedAt: new Date(),
        uploadedBy: 'user_123',
      }

      // Add document first
      act(() => {
        result.current.addDocument(document)
      })

      // Remove document
      act(() => {
        result.current.removeDocument('doc_1')
      })

      expect(result.current.formData.documents).not.toContain(document)
    })

    it('should set upload progress', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      const progress = {
        doc_1: { progress: 50, status: 'uploading' as const },
        doc_2: { progress: 100, status: 'completed' as const },
      }

      act(() => {
        result.current.setUploadProgress(progress)
      })

      expect(result.current.uploadProgress).toEqual(progress)
    })
  })

  describe('loading and error states', () => {
    it('should set loading state', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.setLoading(true)
      })

      expect(result.current.isLoading).toBe(true)

      act(() => {
        result.current.setLoading(false)
      })

      expect(result.current.isLoading).toBe(false)
    })

    it('should set error state', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      const error = 'Something went wrong'

      act(() => {
        result.current.setError(error)
      })

      expect(result.current.error).toBe(error)
    })

    it('should clear error state', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      // Set error first
      act(() => {
        result.current.setError('Error message')
      })

      // Clear error
      act(() => {
        result.current.clearError()
      })

      expect(result.current.error).toBeNull()
    })
  })

  describe('computed selectors', () => {
    it('should calculate form completion percentage', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      // Initially should be 0%
      expect(result.current.getFormCompletionPercentage()).toBe(0)

      // Add some required data
      act(() => {
        result.current.updateFormData({
          transportMode: 'sea',
          customerId: 'cust_123',
          shipperId: 'ship_456',
          portOfLoading: 'THBKK',
          portOfDischarge: 'HKHKG',
          containerType: 'dry_20',
          cargoDescription: 'Test cargo',
          grossWeight: 1000,
        })
      })

      const completion = result.current.getFormCompletionPercentage()
      expect(completion).toBeGreaterThan(70) // Should be high with required fields filled
    })

    it('should identify missing required fields', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      const missingFields = result.current.getMissingRequiredFields()

      expect(missingFields).toContain('transportMode')
      expect(missingFields).toContain('customerId')
      expect(missingFields).toContain('shipperId')
      expect(missingFields).toContain('portOfLoading')
      expect(missingFields).toContain('portOfDischarge')
      expect(missingFields).toContain('containerType')
      expect(missingFields).toContain('cargoDescription')
    })

    it('should validate step completion', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      // Step 1 should not be valid initially
      expect(result.current.isStepValid(1)).toBe(false)

      // Add transport mode (step 1 requirement)
      act(() => {
        result.current.updateFormData({ transportMode: 'sea' })
      })

      expect(result.current.isStepValid(1)).toBe(true)
    })

    it('should check if form can be submitted', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      // Initially should not be submittable
      expect(result.current.canSubmit()).toBe(false)

      // Fill all required fields
      act(() => {
        result.current.updateFormData({
          transportMode: 'sea',
          customerId: 'cust_123',
          shipperId: 'ship_456',
          portOfLoading: 'THBKK',
          portOfDischarge: 'HKHKG',
          containerType: 'dry_20',
          cargoDescription: 'Test cargo',
          grossWeight: 1000,
        })
      })

      // Clear any validation errors
      act(() => {
        result.current.setValidationErrors({})
      })

      expect(result.current.canSubmit()).toBe(true)
    })
  })

  describe('auto-save functionality', () => {
    it('should enable auto-save by default', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      expect(result.current.autoSaveEnabled).toBe(true)
    })

    it('should allow toggling auto-save', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      act(() => {
        result.current.setAutoSave(false)
      })

      expect(result.current.autoSaveEnabled).toBe(false)

      act(() => {
        result.current.setAutoSave(true)
      })

      expect(result.current.autoSaveEnabled).toBe(true)
    })
  })

  describe('form reset', () => {
    it('should reset form to initial state', () => {
      const { result } = renderHook(() => useShipmentCreationStore())

      // Modify the state
      act(() => {
        result.current.setStep(3)
        result.current.updateFormData({
          transportMode: 'sea',
          customerId: 'cust_123',
        })
        result.current.setError('Test error')
        result.current.setLoading(true)
      })

      // Reset the form
      act(() => {
        result.current.resetForm()
      })

      expect(result.current.currentStep).toBe(1)
      expect(result.current.formData.transportMode).toBeUndefined()
      expect(result.current.formData.customerId).toBe('')
      expect(result.current.error).toBeNull()
      expect(result.current.isLoading).toBe(false)
      expect(result.current.isDraft).toBe(false)
    })
  })
})
