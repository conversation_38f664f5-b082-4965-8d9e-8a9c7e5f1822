'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useShipmentDetail } from '@/hooks/use-shipment-detail'
import { useStatusUpdates } from '@/hooks/use-status-updates'
import { useRealTimeStatusHistory } from '@/hooks/use-real-time'
import { useTransportation, useCoordinateUtils } from '@/hooks/use-transportation'
import { useAuth } from '@/hooks/use-auth'
import { useFullShipmentIntelligence } from '@/hooks/use-shipment-relationships'
import { useShipmentDocuments } from '@/hooks/use-shipment-documents'
import { downloadDocument, downloadDocumentsAsZip } from '@/lib/utils/document-download'
import { viewDocument, getDocumentPreviewInfo } from '@/lib/utils/document-viewer'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ArrowLeft, 
  Edit, 
  Package, 
  MapPin, 
  Calendar, 
  Building2, 
  Ship, 
  Truck,
  AlertCircle,
  Loader2,
  Clock,
  Plus,
  User,
  Phone,
  Navigation,
  CheckCircle,
  Edit3,
  Trash2,
  DollarSign,
  TrendingUp,
  Mail,
  MessageCircle,
  Star,
  Info,
  FileText,
  Send,
  Download,
  Copy,
  RefreshCw,
  MoreVertical,
  Camera,
  Eye
} from 'lucide-react'
import { ContainerList } from '@/components/containers/container-list'
import { format } from 'date-fns'
import { StatusUpdateForm } from '@/components/forms/shipment-form/status-update-form'
import { TransportationForm } from '@/components/forms/transportation-form/transportation-form'
import { DocumentGenerationModal } from '@/components/forms/shipment-form/document-generation-modal'
import { StakeholderNotificationModal } from '@/components/forms/shipment-form/stakeholder-notification-modal'
import { QuickEditShortcuts, QuickEditField } from '@/components/forms/shipment-form/quick-edit-shortcuts'
import { Timeline } from '@/components/data-display/timeline'
import { StatusBadge } from '@/components/data-display/status-badge'
import { StatusPhotosGallery } from '@/components/data-display/status-photos-gallery'
import type { ShipmentStatus } from '@/lib/supabase/types'
import type { StatusUpdateForm as StatusUpdateFormType } from '@/lib/validations/status-updates'
import type { TransportationForm as TransportationFormType } from '@/lib/validations/transportation'

// Status badge colors (same as shipments table)
const getStatusColor = (status: string | null) => {
  switch (status) {
    case 'booking_confirmed':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'transport_assigned':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
    case 'driver_assigned':
      return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400'
    case 'loading_started':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'shipped':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
    case 'arrived':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'completed':
      return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400'
    case 'cancelled':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

// Format date helper
const formatDate = (date: string | null) => {
  if (!date) return 'Not set'
  try {
    return format(new Date(date), 'MMM dd, yyyy')
  } catch {
    return 'Invalid date'
  }
}

export default function ShipmentDetailPage() {
  const params = useParams()
  const router = useRouter()
  const shipmentId = params.id as string

  const [showStatusUpdate, setShowStatusUpdate] = useState(false)
  const [statusUpdateError, setStatusUpdateError] = useState<string | null>(null)
  const [showTransportationForm, setShowTransportationForm] = useState(false)
  const [transportationError, setTransportationError] = useState<string | null>(null)
  const [transportationMode, setTransportationMode] = useState<'create' | 'edit'>('create')
  const [editingField, setEditingField] = useState<string | null>(null)
  const [editValue, setEditValue] = useState<string>('')
  
  // New modal states
  const [showDocumentGeneration, setShowDocumentGeneration] = useState(false)
  const [showStakeholderNotification, setShowStakeholderNotification] = useState(false)
  const [isGeneratingDocuments, setIsGeneratingDocuments] = useState(false)
  const [isSendingNotifications, setIsSendingNotifications] = useState(false)

  const { shipment, isLoading, error, refreshShipment } = useShipmentDetail(shipmentId)
  const { statusHistory, isLoading: isLoadingHistory, refetch: refetchStatusHistory } = useRealTimeStatusHistory(shipmentId)
  const { updateStatus, isLoading: isUpdatingStatus } = useStatusUpdates()
  const { 
    transportation, 
    isLoading: isLoadingTransportation,
    createTransportationAssignment,
    updateTransportationAssignment,
    deleteTransportationAssignment 
  } = useTransportation(shipmentId)
  const { parseCoordinates, formatCoordinates } = useCoordinateUtils()
  const { isStaff } = useAuth()
  
  // Documents hook
  const { 
    documents, 
    isLoading: isLoadingDocuments, 
    getDocumentCountByCategory,
    getDocumentsByCategory,
    refreshDocuments 
  } = useShipmentDocuments(shipmentId)

  // Initialize relationship intelligence with current shipment data
  const { state: relationshipState, actions: relationshipActions } = useFullShipmentIntelligence()

  // Auto-refresh interval for relationship data (every 30 seconds)
  useEffect(() => {
    const interval = setInterval(() => {
      if (shipment?.customer_id) {
        // Refresh relationship data
        relationshipActions.populateFromCustomerHistory(shipment.customer_id)
      }
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [shipment?.customer_id]) // Removed relationshipActions to prevent infinite re-renders

  // Use effect to populate relationship intelligence when shipment loads
  useEffect(() => {
    if (shipment?.customer_id) {
      relationshipActions.selectCustomer(shipment.customer_id)
      relationshipActions.populateFromCustomerHistory(shipment.customer_id)
    }
  }, [shipment?.customer_id]) // Removed relationshipActions to prevent infinite re-renders

  // Separate effect to handle shipper selection after customer relationships load
  useEffect(() => {
    if (shipment?.shipper_id && relationshipState.availableShippers.length > 0) {
      // Find if the shipper exists in available shippers
      const shipperExists = relationshipState.availableShippers.find(
        shipper => shipper.shipper_id === shipment.shipper_id
      )
      
      if (shipperExists) {
        relationshipActions.selectShipper(shipment.shipper_id)
      }
    }
  }, [shipment?.shipper_id, relationshipState.availableShippers]) // Removed relationshipActions to prevent infinite re-renders

  // Separate effect to handle consignee selection after relationships load
  useEffect(() => {
    if (shipment?.consignee_id) {
      relationshipActions.selectConsignee(shipment.consignee_id)
    }
  }, [shipment?.consignee_id]) // Removed relationshipActions to prevent infinite re-renders

  // Separate effect to handle notify party selection after consignee relationships load
  useEffect(() => {
    if (shipment?.notify_party_id && relationshipState.availableNotifyParties.length > 0) {
      // Find if the notify party exists in available notify parties
      const notifyPartyExists = relationshipState.availableNotifyParties.find(
        np => np.notify_party_id === shipment.notify_party_id
      )
      
      if (notifyPartyExists) {
        relationshipActions.selectNotifyParty(shipment.notify_party_id)
      }
    }
  }, [shipment?.notify_party_id, relationshipState.availableNotifyParties]) // Removed relationshipActions to prevent infinite re-renders

  // Handle status update
  const handleStatusUpdate = async (data: StatusUpdateFormType & { shipment_id: string }) => {
    try {
      setStatusUpdateError(null)
      await updateStatus(data)
      setShowStatusUpdate(false)
      await refreshShipment()
    } catch (err) {
      setStatusUpdateError(err instanceof Error ? err.message : 'Failed to update status')
    }
  }

  // Handle transportation assignment creation
  const handleTransportationSubmit = async (data: TransportationFormType & { shipment_id: string }) => {
    try {
      setTransportationError(null)
      if (transportationMode === 'create') {
        await createTransportationAssignment(data)
      } else {
        await updateTransportationAssignment({ id: transportation!.id, ...data })
      }
      setShowTransportationForm(false)
      // Refresh both shipment data and status history
      await Promise.all([
        refreshShipment(),
        refetchStatusHistory()
      ])
    } catch (err) {
      setTransportationError(err instanceof Error ? err.message : 'Failed to save transportation assignment')
    }
  }

  // Handle transportation assignment deletion
  const handleDeleteTransportation = async () => {
    if (!transportation?.id) return
    
    try {
      setTransportationError(null)
      await deleteTransportationAssignment(transportation.id)
      // Refresh both shipment data and status history
      await Promise.all([
        refreshShipment(),
        refetchStatusHistory()
      ])
    } catch (err) {
      setTransportationError(err instanceof Error ? err.message : 'Failed to delete transportation assignment')
    }
  }

  // Show transportation form in create mode
  const showCreateTransportationForm = () => {
    setTransportationMode('create')
    setShowTransportationForm(true)
    setTransportationError(null)
  }

  // Show transportation form in edit mode
  const showEditTransportationForm = () => {
    setTransportationMode('edit')
    setShowTransportationForm(true)
    setTransportationError(null)
  }

  // Cancel transportation form
  const cancelTransportationForm = () => {
    setShowTransportationForm(false)
    setTransportationError(null)
  }

  // Handle inline editing
  const startEditing = (field: string, currentValue: string) => {
    setEditingField(field)
    setEditValue(currentValue || '')
  }

  const cancelEditing = () => {
    setEditingField(null)
    setEditValue('')
  }

  const saveEdit = async (field: string) => {
    try {
      // TODO: Implement API call to update the field
      console.log(`Updating ${field} to:`, editValue)
      // This would call a shipment update API
      // await updateShipment(shipmentId, { [field]: editValue })
      
      setEditingField(null)
      setEditValue('')
      // Refresh shipment data after successful update
      // await refreshShipment()
    } catch (error) {
      console.error('Error updating field:', error)
    }
  }

  // Document generation handler
  const handleDocumentGeneration = async (selectedDocuments: string[]) => {
    try {
      setIsGeneratingDocuments(true)
      // TODO: Implement document generation API
      console.log('Generating documents:', selectedDocuments, 'for shipment:', shipmentId)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Refresh documents after generation
      await refreshDocuments()
      
      // Close modal after successful generation
      setShowDocumentGeneration(false)
      
      // Show success message or download documents
      alert('Documents generated successfully!')
    } catch (error) {
      console.error('Error generating documents:', error)
      alert('Failed to generate documents. Please try again.')
    } finally {
      setIsGeneratingDocuments(false)
    }
  }

  // Stakeholder notification handler
  const handleStakeholderNotification = async (notificationData: {
    recipients: string[]
    channels: string[]
    subject: string
    message: string
    urgency: 'low' | 'normal' | 'high'
    includeShipmentDetails: boolean
  }) => {
    try {
      setIsSendingNotifications(true)
      // TODO: Implement notification API
      console.log('Sending notifications:', notificationData, 'for shipment:', shipmentId)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Close modal after successful sending
      setShowStakeholderNotification(false)
      
      // Show success message
      alert(`Notifications sent to ${notificationData.recipients.length} recipient(s) via ${notificationData.channels.join(', ')}`)
    } catch (error) {
      console.error('Error sending notifications:', error)
      alert('Failed to send notifications. Please try again.')
    } finally {
      setIsSendingNotifications(false)
    }
  }

  // Quick edit handler
  const handleQuickEdit = async (fieldId: string, value: any) => {
    try {
      // TODO: Implement API call to update the field
      console.log(`Quick updating ${fieldId} to:`, value)
      // This would call a shipment update API
      // await updateShipment(shipmentId, { [fieldId]: value })
      
      // Refresh shipment data after successful update
      await refreshShipment()
    } catch (error) {
      console.error('Error updating field:', error)
      throw error // Re-throw to let the component handle the error
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-muted-foreground">Loading shipment details...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/shipments')}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Shipments
          </Button>
        </div>

        <Alert className="bg-red-900/20 border-red-500">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-red-200">
            {error}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!shipment) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/shipments')}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Shipments
          </Button>
        </div>

        <Alert className="bg-yellow-900/20 border-yellow-500">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-yellow-200">
            Shipment not found
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/shipments')}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 min-h-[44px] touch-manipulation"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Back to Shipments</span>
            <span className="sm:hidden">Back</span>
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold text-white">{shipment.shipment_number}</h1>
            <p className="text-muted-foreground mt-1">
              Shipment Details
            </p>
          </div>
        </div>
        
        <div className="flex flex-wrap items-center gap-2 sm:gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              Promise.all([refreshShipment(), refetchStatusHistory()])
              // Also refresh relationship intelligence
              if (shipment?.customer_id) {
                relationshipActions.populateFromCustomerHistory(shipment.customer_id)
              }
            }}
            className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 min-h-[44px] touch-manipulation"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          {/* Quick Actions */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigator.clipboard.writeText(shipment.shipment_number)}
            className="border-slate-500 bg-slate-600/10 text-slate-100 hover:bg-slate-600/20 hover:border-slate-400 min-h-[44px] touch-manipulation"
          >
            <Copy className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Copy Number</span>
            <span className="sm:hidden">Copy</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDocumentGeneration(true)}
            className="border-green-500 bg-green-600/10 text-green-100 hover:bg-green-600/20 hover:border-green-400 min-h-[44px] touch-manipulation"
          >
            <Download className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Generate Docs</span>
            <span className="sm:hidden">Docs</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDocumentGeneration(true)}
            className="border-indigo-500 bg-indigo-600/10 text-indigo-100 hover:bg-indigo-600/20 hover:border-indigo-400 min-h-[44px] touch-manipulation"
          >
            <FileText className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Documents</span>
            <span className="sm:hidden">Files</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowStakeholderNotification(true)}
            className="border-cyan-500 bg-cyan-600/10 text-cyan-100 hover:bg-cyan-600/20 hover:border-cyan-400 min-h-[44px] touch-manipulation"
          >
            <Send className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Notify</span>
            <span className="sm:hidden">Send</span>
          </Button>

          {/* Quick Edit Shortcuts */}
          {isStaff && shipment && (
            <QuickEditShortcuts
              shipmentId={shipmentId}
              fields={[
                {
                  id: 'etd_date',
                  label: 'ETD Date',
                  type: 'date',
                  icon: <Calendar className="h-4 w-4" />,
                  currentValue: shipment.etd_date,
                  required: true,
                  placeholder: 'Select departure date'
                },
                {
                  id: 'eta_date',
                  label: 'ETA Date',
                  type: 'date',
                  icon: <Calendar className="h-4 w-4" />,
                  currentValue: shipment.eta_date,
                  required: true,
                  placeholder: 'Select arrival date'
                },
                {
                  id: 'transportation_mode',
                  label: 'Transportation Mode',
                  type: 'select',
                  icon: <Ship className="h-4 w-4" />,
                  currentValue: shipment.transportation_mode,
                  required: true,
                  options: [
                    { value: 'sea', label: 'Sea' },
                    { value: 'land', label: 'Land' },
                    { value: 'rail', label: 'Rail' },
                    { value: 'air', label: 'Air' }
                  ]
                },
                {
                  id: 'closing_time',
                  label: 'Closing Time',
                  type: 'datetime-local',
                  icon: <Clock className="h-4 w-4" />,
                  currentValue: shipment.closing_time,
                  required: false,
                  placeholder: 'Select closing date and time'
                }
              ]}
              onSave={handleQuickEdit}
              isLoading={isLoading}
            />
          )}
          
          {/* Staff Actions */}
          {isStaff && shipment?.status && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowStatusUpdate(!showStatusUpdate)}
              className="border-orange-500 bg-orange-600/10 text-orange-100 hover:bg-orange-600/20 hover:border-orange-400 min-h-[44px] touch-manipulation"
            >
              <Clock className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">{showStatusUpdate ? 'Cancel Update' : 'Update Status'}</span>
              <span className="lg:hidden">Status</span>
            </Button>
          )}

          {isStaff && shipment && (
            <Button
              variant="outline"
              size="sm"
              onClick={transportation ? showEditTransportationForm : showCreateTransportationForm}
              className="border-purple-500 bg-purple-600/10 text-purple-100 hover:bg-purple-600/20 hover:border-purple-400 min-h-[44px] touch-manipulation"
            >
              <Truck className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">{transportation ? 'Edit Transportation' : 'Assign Transportation'}</span>
              <span className="lg:hidden">Transport</span>
            </Button>
          )}
          
          <Button
            size="sm"
            onClick={() => router.push(`/shipments/${shipmentId}/edit`)}
            className="bg-blue-600 hover:bg-blue-700 text-white min-h-[44px] touch-manipulation"
          >
            <Edit className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Edit Shipment</span>
            <span className="sm:hidden">Edit</span>
          </Button>
        </div>
      </div>

      {/* Status Update Form */}
      {showStatusUpdate && isStaff && shipment?.status && (
        <div className="space-y-4">
          <StatusUpdateForm
            currentStatus={shipment.status as ShipmentStatus}
            onSubmit={handleStatusUpdate}
            shipment_id={shipmentId}
            isSubmitting={isUpdatingStatus}
            onCancel={() => {
              setShowStatusUpdate(false)
              setStatusUpdateError(null)
            }}
          />
          
          {statusUpdateError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{statusUpdateError}</AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* Transportation Assignment Form */}
      {showTransportationForm && isStaff && shipment && (
        <div className="space-y-4">
          <TransportationForm
            shipmentId={shipmentId}
            mode={transportationMode}
            initialData={transportation ? {
              carrier_id: transportation.carrier_id || '',
              driver_id: transportation.driver_id || '',
              vehicle_head_number: transportation.vehicle_head_number || '',
              vehicle_tail_number: transportation.vehicle_tail_number || '',
              driver_phone: transportation.driver_phone || '',
              assignment_date: transportation.assignment_date || new Date().toISOString(),
              pickup_container_location: transportation.pickup_container_location || '',
              pickup_container_gps_coordinates: parseCoordinates(transportation.pickup_container_gps_coordinates),
              pickup_product_location: transportation.pickup_product_location || '',
              pickup_product_gps_coordinates: parseCoordinates(transportation.pickup_product_gps_coordinates),
              delivery_location: transportation.delivery_location || '',
              delivery_gps_coordinates: parseCoordinates(transportation.delivery_gps_coordinates),
              estimated_distance: transportation.estimated_distance || undefined,
              notes: transportation.notes || '',
            } : undefined}
            onSubmit={handleTransportationSubmit}
            onCancel={cancelTransportationForm}
            isSubmitting={false}
          />
          
          {transportationError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{transportationError}</AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
        {/* Left Column - Main Details */}
        <div className="lg:col-span-2 space-y-4 lg:space-y-6">
          {/* Basic Information */}
          <Card className="bg-card border-secondary">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Package className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm text-muted-foreground">Shipment Number</label>
                  <p className="text-white font-medium">{shipment.shipment_number}</p>
                </div>
                
                {shipment.invoice_number && (
                  <div>
                    <label className="text-sm text-muted-foreground">Invoice Number</label>
                    <p className="text-white font-medium">{shipment.invoice_number}</p>
                  </div>
                )}
                
                <div>
                  <label className="text-sm text-muted-foreground">Status</label>
                  <div className="mt-1">
                    <StatusBadge 
                      status={shipment.status as ShipmentStatus} 
                      size="default"
                      showIcon
                    />
                  </div>
                </div>
                
                <div>
                  <label className="text-sm text-muted-foreground">Transportation Mode</label>
                  <div className="mt-1">
                    <Badge variant="outline" className="border-slate-600 text-muted-foreground">
                      {shipment.transportation_mode?.toUpperCase() || 'N/A'}
                    </Badge>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm text-muted-foreground">Currency Code</label>
                  <div className="mt-1">
                    <Badge variant="outline" className="border-slate-600 text-muted-foreground">
                      {shipment.currency_code?.toUpperCase() || 'N/A'}
                    </Badge>
                  </div>
                </div>

                {/* Additional Basic Information Fields */}
                {(shipment.number_of_pallet || shipment.pallet_description || shipment.ephyto_refno) && (
                  <>
                    {shipment.number_of_pallet && (
                      <div>
                        <label className="text-sm text-muted-foreground">Number of Pallets</label>
                        <p className="text-white font-medium">{shipment.number_of_pallet}</p>
                      </div>
                    )}

                    {shipment.pallet_description && (
                      <div>
                        <label className="text-sm text-muted-foreground">Pallet Description</label>
                        <p className="text-white font-medium">{shipment.pallet_description}</p>
                      </div>
                    )}

                    {shipment.ephyto_refno && (
                      <div>
                        <label className="text-sm text-muted-foreground">Ephyto Reference Number</label>
                        <p className="text-white font-medium">{shipment.ephyto_refno}</p>
                      </div>
                    )}
                  </>
                )}
              </div>
              
              {(shipment.notes || isStaff) && (
                <div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm text-muted-foreground">Notes</label>
                    {isStaff && editingField !== 'notes' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => startEditing('notes', shipment.notes || '')}
                        className="h-6 px-2 text-xs text-muted-foreground hover:text-white"
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  
                  {editingField === 'notes' ? (
                    <div className="mt-1 space-y-2">
                      <textarea
                        value={editValue}
                        onChange={(e) => setEditValue(e.target.value)}
                        className="w-full p-2 bg-slate-700 border border-slate-600 rounded text-white text-sm resize-none"
                        rows={3}
                        placeholder="Enter shipment notes..."
                      />
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => saveEdit('notes')}
                          className="bg-green-600 hover:bg-green-700 text-white h-7 px-3 text-xs"
                        >
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelEditing}
                          className="border-slate-600 text-muted-foreground hover:bg-slate-700 h-7 px-3 text-xs"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-muted-foreground mt-1">
                      {shipment.notes || <span className="text-muted-foreground italic">No notes added yet</span>}
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Routes & Dates */}
          <Card className="bg-card border-secondary">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Route & Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Route */}
              <div className="flex items-center justify-between p-4 bg-dark/50 rounded-lg">
                <div className="text-center">
                  <div className="text-sm text-muted-foreground">Origin Port</div>
                  <div className="text-white font-medium">
                    {shipment.origin_port?.code || 'N/A'}
                  </div>
                  {shipment.origin_port?.name && (
                    <div className="text-xs text-muted-foreground">{shipment.origin_port.name}</div>
                  )}
                </div>
                
                <div className="flex items-center">
                  <div className="flex-1 h-px bg-slate-600"></div>
                  <Ship className="h-4 w-4 mx-2 text-muted-foreground" />
                  <div className="flex-1 h-px bg-slate-600"></div>
                </div>
                
                <div className="text-center">
                  <div className="text-sm text-muted-foreground">Destination Port</div>
                  <div className="text-white font-medium">
                    {shipment.destination_port?.code || 'N/A'}
                  </div>
                  {shipment.destination_port?.name && (
                    <div className="text-xs text-muted-foreground">{shipment.destination_port.name}</div>
                  )}
                </div>
              </div>
              
              {/* Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm text-muted-foreground">ETD (Estimated Time of Departure)</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-white">{formatDate(shipment.etd_date)}</span>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm text-muted-foreground">ETA (Estimated Time of Arrival)</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-white">{formatDate(shipment.eta_date)}</span>
                  </div>
                </div>
              </div>

              {/* Additional Schedule Information */}
              {(shipment.closing_time || shipment.cy_date || shipment.booking_number || 
                shipment.vessel_name || shipment.liner || shipment.voyage_number) && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-slate-700">
                  {shipment.closing_time && (
                    <div>
                      <label className="text-sm text-muted-foreground">Closing Time</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="text-white">{new Date(shipment.closing_time).toLocaleString()}</span>
                      </div>
                    </div>
                  )}

                  {shipment.cy_date && (
                    <div>
                      <label className="text-sm text-muted-foreground">CY Date</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-white">{new Date(shipment.cy_date).toLocaleString()}</span>
                      </div>
                    </div>
                  )}

                  {shipment.booking_number && (
                    <div>
                      <label className="text-sm text-muted-foreground">Booking Number</label>
                      <p className="text-white font-medium">{shipment.booking_number}</p>
                    </div>
                  )}

                  {shipment.vessel_name && (
                    <div>
                      <label className="text-sm text-muted-foreground">Vessel Name</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Ship className="h-4 w-4 text-muted-foreground" />
                        <span className="text-white">{shipment.vessel_name}</span>
                      </div>
                    </div>
                  )}

                  {shipment.liner && (
                    <div>
                      <label className="text-sm text-muted-foreground">Liner/Shipping Line</label>
                      <p className="text-white font-medium">{shipment.liner}</p>
                    </div>
                  )}

                  {shipment.voyage_number && (
                    <div>
                      <label className="text-sm text-muted-foreground">Voyage Number</label>
                      <p className="text-white font-medium">{shipment.voyage_number}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Containers */}
          <ContainerList 
            shipment_id={shipmentId} 
            editable={true}
            onContainerUpdate={() => {
              // Refetch shipment data when containers are updated
              refreshShipment()
            }}
          />

          {/* Total Values */}
          <Card className="bg-card border-secondary">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Shipment Totals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                {/* Total Quantity */}
                <div className="p-3 bg-blue-900/20 rounded-lg border border-blue-700/30">
                  <label className="text-blue-400 text-sm font-medium">Total Quantity</label>
                  <p className="text-white text-lg font-semibold mt-1">
                    {shipment.total_quantity?.toLocaleString() || '0'}
                  </p>
                </div>
                
                {/* Total Net Weight */}
                <div className="p-3 bg-green-900/20 rounded-lg border border-green-700/30">
                  <label className="text-green-400 text-sm font-medium">Total Net Weight</label>
                  <p className="text-white text-lg font-semibold mt-1">
                    {shipment.total_net_weight?.toLocaleString() || '0'} kg
                  </p>
                </div>
                
                {/* Total Gross Weight */}
                <div className="p-3 bg-yellow-900/20 rounded-lg border border-yellow-700/30">
                  <label className="text-yellow-400 text-sm font-medium">Total Gross Weight</label>
                  <p className="text-white text-lg font-semibold mt-1">
                    {shipment.total_gross_weight?.toLocaleString() || '0'} kg
                  </p>
                </div>
                
                {/* Total Value CIF */}
                <div className="p-3 bg-purple-900/20 rounded-lg border border-purple-700/30">
                  <label className="text-purple-400 text-sm font-medium">Total CIF Value</label>
                  <p className="text-white text-lg font-semibold mt-1">
                    {shipment.total_value_cif?.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    }) || '0.00'} {shipment.currency_code || 'USD'}
                  </p>
                </div>
                
                {/* Total Value FOB */}
                <div className="p-3 bg-orange-900/20 rounded-lg border border-orange-700/30">
                  <label className="text-orange-400 text-sm font-medium">Total FOB Value</label>
                  <p className="text-white text-lg font-semibold mt-1">
                    {shipment.total_value_fob?.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    }) || '0.00'} {shipment.currency_code_fob || 'USD'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Products */}
          {shipment.shipment_products && shipment.shipment_products.length > 0 && (
            <Card className="bg-gradient-to-br from-blue-950/50 to-cyan-950/50 border-blue-500/30 shadow-lg shadow-blue-500/10">
              <CardHeader className="bg-transparent !bg-transparent" style={{ backgroundColor: 'transparent' }}>
                <CardTitle className="text-white flex items-center gap-2">
                  <Package className="h-5 w-5 text-blue-400" />
                  Products ({shipment.shipment_products.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="bg-gradient-to-b from-blue-900/40 to-cyan-900/40">
                <div className="space-y-4">
                  {shipment.shipment_products.map((shipmentProduct) => (
                    <div key={shipmentProduct.id} className="p-4 bg-gradient-to-br from-blue-950/90 to-blue-950/90 rounded-lg border border-teal-500/20 shadow-md hover:shadow-teal-500/20 transition-all duration-200">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="text-white font-medium">
                            {shipmentProduct.product?.name || shipmentProduct.product_description || 'Unknown Product'}
                          </div>
                          {shipmentProduct.product?.code && (
                            <div className="text-sm text-muted-foreground mt-1">
                              Code: {shipmentProduct.product.code}
                            </div>
                          )}
                          {shipmentProduct.product_description && (
                            <div className="text-sm text-muted-foreground mt-1 p-2 bg-blue-900/30 rounded">
                              <span className="text-muted-foreground text-xs">Product Description:</span>
                              <div className="mt-1 text-slate-200">{shipmentProduct.product_description}</div>
                            </div>
                          )}
                          
                          {/* Additional Product Information */}
                          {(shipmentProduct.shipping_mark || shipmentProduct.lot_number || shipmentProduct.mfg_date || shipmentProduct.expire_date) && (
                            <div className="text-sm text-muted-foreground mt-2 p-2 bg-blue-900/30 rounded">
                              <span className="text-muted-foreground text-xs">Product Details:</span>
                              <div className="mt-1 grid grid-cols-1 sm:grid-cols-2 gap-2">
                                {shipmentProduct.shipping_mark && (
                                  <div>
                                    <span className="text-muted-foreground text-xs">Shipping Mark:</span>
                                    <div className="text-slate-200">{shipmentProduct.shipping_mark}</div>
                                  </div>
                                )}
                                {shipmentProduct.lot_number && (
                                  <div>
                                    <span className="text-muted-foreground text-xs">Lot Number:</span>
                                    <div className="text-slate-200">{shipmentProduct.lot_number}</div>
                                  </div>
                                )}
                                {shipmentProduct.mfg_date && (
                                  <div>
                                    <span className="text-muted-foreground text-xs">Manufacturing Date:</span>
                                    <div className="text-slate-200">{formatDate(shipmentProduct.mfg_date)}</div>
                                  </div>
                                )}
                                {shipmentProduct.expire_date && (
                                  <div>
                                    <span className="text-muted-foreground text-xs">Expiry Date:</span>
                                    <div className="text-slate-200">{formatDate(shipmentProduct.expire_date)}</div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                          {/* {shipmentProduct.product?.description && (
                            <div className="text-sm text-muted-foreground mt-1">
                              Master Data: {shipmentProduct.product.description}
                            </div>
                          )} */}
                        </div>
                        
                        <div className="text-right ml-4">
                          <Badge variant="outline" className="border-orange-500 text-orange-300 bg-orange-500/10">
                            {shipmentProduct.packaging_type?.replace('_', ' ') || 'Unknown'}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                        <div>
                          <label className="text-muted-foreground">Quantity</label>
                          <p className="text-white font-medium">
                            {shipmentProduct.quantity.toLocaleString()} {shipmentProduct.packaging_type || ''}
                          </p>
                        </div>
                        
                        <div>
                          <label className="text-muted-foreground">Net Weight (per unit)</label>
                          <p className="text-white font-medium">{shipmentProduct.net_weight.toLocaleString()} kg</p>
                        </div>
                        
                        <div>
                          <label className="text-muted-foreground">Gross Weight (per unit)</label>
                          <p className="text-white font-medium">{shipmentProduct.gross_weight.toLocaleString()} kg</p>
                        </div>
                        
                        <div>
                          <label className="text-muted-foreground">Quality Grade</label>
                          <p className="text-white font-medium">{shipmentProduct.quality_grade || 'N/A'}</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm mt-3 pt-3 border-t border-slate-600">
                        <div className="col-span-1 sm:col-span-2 lg:col-span-4">
                          <label className="text-muted-foreground text-xs font-medium">Total Weights</label>
                        </div>
                        
                        <div>
                          <label className="text-muted-foreground">Total Net Weight</label>
                          <p className="text-white font-medium">
                            {(shipmentProduct.total_net_weight || (shipmentProduct.quantity * shipmentProduct.net_weight)).toLocaleString()} kg
                          </p>
                          <p className="text-xs text-muted-foreground">
                            ({shipmentProduct.quantity.toLocaleString()} × {shipmentProduct.net_weight.toLocaleString()})
                          </p>
                        </div>
                        
                        <div>
                          <label className="text-muted-foreground">Total Gross Weight</label>
                          <p className="text-white font-medium">
                            {(shipmentProduct.total_gross_weight || (shipmentProduct.quantity * shipmentProduct.gross_weight)).toLocaleString()} kg
                          </p>
                          <p className="text-xs text-muted-foreground">
                            ({shipmentProduct.quantity.toLocaleString()} × {shipmentProduct.gross_weight.toLocaleString()})
                          </p>
                        </div>
                        
                        <div className="col-span-2">
                          <label className="text-muted-foreground">Weight Difference</label>
                          <p className="text-white font-medium">
                            {((shipmentProduct.total_gross_weight || (shipmentProduct.quantity * shipmentProduct.gross_weight)) - 
                              (shipmentProduct.total_net_weight || (shipmentProduct.quantity * shipmentProduct.net_weight))).toLocaleString()} kg
                          </p>
                          <p className="text-xs text-muted-foreground">Packaging + handling weight</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm mt-3 pt-3 border-t border-slate-700">
                        <div>
                          <label className="text-muted-foreground">Unit Price (CIF)</label>
                          <p className="text-white font-medium">
                            {shipmentProduct.unit_price_cif.toFixed(2)} {shipment.currency_code || 'USD'}
                          </p>
                        </div>
                        
                        <div>
                          <label className="text-muted-foreground">Unit Price (FOB)</label>
                          <p className="text-white font-medium">
                            {shipmentProduct.unit_price_fob.toFixed(2)} {shipment.currency_code_fob || 'USD'}
                          </p>
                        </div>
                        
                        <div>
                          <label className="text-muted-foreground">Total CIF Value</label>
                          <p className="text-white font-medium">
                            {shipmentProduct.total_value_cif.toLocaleString()} {shipment.currency_code || 'USD'}
                          </p>
                        </div>
                        
                        <div>
                          <label className="text-muted-foreground">Total FOB Value</label>
                          <p className="text-white font-medium">
                            {shipmentProduct.total_value_fob.toLocaleString()} {shipment.currency_code_fob || 'USD'}
                          </p>
                        </div>
                      </div>
                      
                      {(shipmentProduct.shipping_mark || shipmentProduct.lot_number || 
                        shipmentProduct.mfg_date || shipmentProduct.expire_date) && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mt-3 pt-3 border-t border-slate-700">
                          {shipmentProduct.shipping_mark && (
                            <div>
                              <label className="text-muted-foreground">Shipping Mark</label>
                              <p className="text-text-slate-200">{shipmentProduct.shipping_mark}</p>
                            </div>
                          )}
                          
                          {shipmentProduct.lot_number && (
                            <div>
                              <label className="text-muted-foreground">Lot Number</label>
                              <p className="text-text-slate-200">{shipmentProduct.lot_number}</p>
                            </div>
                          )}
                          
                          {shipmentProduct.mfg_date && (
                            <div>
                              <label className="text-muted-foreground">Manufacturing Date</label>
                              <p className="text-text-slate-200">{formatDate(shipmentProduct.mfg_date)}</p>
                            </div>
                          )}
                          
                          {shipmentProduct.expire_date && (
                            <div>
                              <label className="text-muted-foreground">Expiry Date</label>
                              <p className="text-muted-foreground">{formatDate(shipmentProduct.expire_date)}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Transportation Information */}
          <Card className="bg-card border-secondary">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Transportation Assignment
                </div>
                {transportation && isStaff && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={showEditTransportationForm}
                      className="border-green-500 bg-green-600/10 text-green-100 hover:bg-green-600/20 hover:border-green-400"
                    >
                      <Edit3 className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDeleteTransportation}
                      className="border-red-500 bg-red-600/10 text-red-100 hover:bg-red-600/20 hover:border-red-400"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Remove
                    </Button>
                  </div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingTransportation ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-blue-600 mr-2" />
                  <span className="text-muted-foreground">Loading transportation details...</span>
                </div>
              ) : transportation ? (
                <div className="space-y-4">
                  {/* Carrier and Driver Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm text-muted-foreground">Carrier Company</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Building2 className="h-4 w-4 text-purple-500" />
                        <span className="text-white font-medium">
                          {transportation.carrier?.name || 'Not assigned'}
                        </span>
                      </div>
                    </div>
                    
                    {transportation.driver && (
                      <div>
                        <label className="text-sm text-muted-foreground">Driver</label>
                        <div className="flex items-center gap-2 mt-1">
                          <User className="h-4 w-4 text-purple-500" />
                          <span className="text-white font-medium">
                            {transportation.driver.driver_first_name} {transportation.driver.driver_last_name}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Vehicle Information */}
                  {(transportation.vehicle_head_number || transportation.vehicle_tail_number) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {transportation.vehicle_head_number && (
                        <div>
                          <label className="text-sm text-muted-foreground">Vehicle Head Number</label>
                          <p className="text-white font-medium">{transportation.vehicle_head_number}</p>
                        </div>
                      )}
                      
                      {transportation.vehicle_tail_number && (
                        <div>
                          <label className="text-sm text-muted-foreground">Vehicle Tail Number</label>
                          <p className="text-white font-medium">{transportation.vehicle_tail_number}</p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Contact Information */}
                  {transportation.driver_phone && (
                    <div>
                      <label className="text-sm text-muted-foreground">Driver Contact</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Phone className="h-4 w-4 text-purple-500" />
                        <span className="text-white font-medium">{transportation.driver_phone}</span>
                      </div>
                    </div>
                  )}

                  {/* Assignment Date */}
                  {transportation.assignment_date && (
                    <div>
                      <label className="text-sm text-muted-foreground">Assignment Date</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Calendar className="h-4 w-4 text-purple-500" />
                        <span className="text-white">{formatDate(transportation.assignment_date)}</span>
                      </div>
                    </div>
                  )}

                  {/* Location Information */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-purple-500" />
                      <h4 className="text-md font-medium text-white">Locations</h4>
                    </div>
                    
                    <div className="grid grid-cols-1 gap-3 pl-6">
                      {transportation.pickup_container_location && (
                        <div>
                          <label className="text-sm text-muted-foreground">Container Pickup</label>
                          <p className="text-muted-foreground">{transportation.pickup_container_location}</p>
                        </div>
                      )}
                      
                      {transportation.pickup_product_location && (
                        <div>
                          <label className="text-sm text-muted-foreground">Product Pickup</label>
                          <p className="text-muted-foreground">{transportation.pickup_product_location}</p>
                        </div>
                      )}
                      
                      {transportation.delivery_location && (
                        <div>
                          <label className="text-sm text-muted-foreground">Delivery</label>
                          <p className="text-muted-foreground font-medium">{transportation.delivery_location}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* GPS Coordinates */}
                  {(parseCoordinates(transportation.pickup_product_gps_coordinates) || 
                    parseCoordinates(transportation.delivery_gps_coordinates)) && (
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Navigation className="h-4 w-4 text-purple-500" />
                        <h4 className="text-md font-medium text-white">GPS Coordinates</h4>
                      </div>
                      
                      <div className="grid grid-cols-1 gap-3 pl-6">
                        {parseCoordinates(transportation.pickup_product_gps_coordinates) && (
                          <div>
                            <label className="text-sm text-muted-foreground">Product Pickup GPS</label>
                            <p className="text-muted-foreground font-mono text-sm">
                              {formatCoordinates(
                                parseCoordinates(transportation.pickup_product_gps_coordinates)!.lat,
                                parseCoordinates(transportation.pickup_product_gps_coordinates)!.lng
                              )}
                            </p>
                          </div>
                        )}
                        
                        {parseCoordinates(transportation.delivery_gps_coordinates) && (
                          <div>
                            <label className="text-sm text-muted-foreground">Delivery GPS</label>
                            <p className="text-muted-foreground font-mono text-sm">
                              {formatCoordinates(
                                parseCoordinates(transportation.delivery_gps_coordinates)!.lat,
                                parseCoordinates(transportation.delivery_gps_coordinates)!.lng
                              )}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Distance and Notes */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {transportation.estimated_distance && (
                      <div>
                        <label className="text-sm text-muted-foreground">Estimated Distance</label>
                        <p className="text-white font-medium">{transportation.estimated_distance} km</p>
                      </div>
                    )}
                    
                    {transportation.notes && (
                      <div className="md:col-span-2">
                        <label className="text-sm text-muted-foreground">Notes</label>
                        <p className="text-muted-foreground mt-1">{transportation.notes}</p>
                      </div>
                    )}
                  </div>

                  {/* Status Badge */}
                  <div className="flex items-center justify-between pt-3 border-t border-slate-600">
                    <Badge
                      variant="outline"
                      className="bg-purple-500/20 text-purple-300 border-purple-400"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Transportation Assigned
                    </Badge>
                    
                    <span className="text-xs text-muted-foreground">
                      Assigned on {formatDate(transportation.created_at)}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Truck className="h-12 w-12 text-slate-600 mx-auto mb-3" />
                  <p className="text-muted-foreground mb-4">No transportation assigned yet</p>
                  {isStaff && (
                    <Button
                      onClick={showCreateTransportationForm}
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Assign Transportation
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Companies & Additional Info */}
        <div className="space-y-4 lg:space-y-6">
          {/* Status Timeline */}
          <Timeline
            statusHistory={statusHistory}
            isLoading={isLoadingHistory}
            className="lg:col-span-1"
          />
          
          {/* Status Photos Gallery */}
          <StatusPhotosGallery
            shipmentId={shipmentId}
            className="lg:col-span-1"
          />
          
          {/* Companies */}
          <Card className="bg-card border-secondary">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Companies & Relationships
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {shipment.customer && (
                <div className="p-3 bg-dark/50 rounded-lg">
                  <label className="text-sm text-muted-foreground">Customer</label>
                  <p className="text-white font-medium">{shipment.customer.name}</p>
                  
                  {/* Customer Intelligence */}
                  {relationshipState.shipmentData?.averageShipmentVolume && (
                    <div className="mt-2 flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        Avg Volume: {relationshipState.shipmentData.averageShipmentVolume.toLocaleString()} units
                      </div>
                      {relationshipState.shipmentData.preferredTransportModes && (
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          Preferred: {relationshipState.shipmentData.preferredTransportModes.join(', ')}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {shipment.factory && (
                <div className="p-3 bg-dark/50 rounded-lg">
                  <label className="text-sm text-muted-foreground">Factory</label>
                  <p className="text-white font-medium">{shipment.factory.name}</p>
                </div>
              )}

              {shipment.shipper && (
                <div className="p-3 bg-dark/50 rounded-lg">
                  <label className="text-sm text-muted-foreground">Shipper</label>
                  <p className="text-white font-medium">{shipment.shipper.name}</p>
                  
                  {/* Shipper Intelligence */}
                  {(relationshipState.shipmentData?.shipperData || shipment.shipper) && (
                    <div className="mt-2 space-y-1">
                      {/* Show relationship intelligence if available */}
                      {relationshipState.shipmentData?.shipperData ? (
                        <>
                          {relationshipState.shipmentData.shipperData.shipper.contact_phone && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Phone className="h-3 w-3" />
                              {relationshipState.shipmentData.shipperData.shipper.contact_phone}
                            </div>
                          )}
                          {relationshipState.shipmentData.shipperData.shipper.contact_email && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Mail className="h-3 w-3" />
                              {relationshipState.shipmentData.shipperData.shipper.contact_email}
                            </div>
                          )}
                          {relationshipState.shipmentData.shipperData.is_default && (
                            <Badge variant="outline" className="border-orange-500 text-orange-300 bg-orange-500/10 text-xs">
                              <Star className="h-2 w-2 mr-1" />
                              Default Shipper
                            </Badge>
                          )}
                        </>
                      ) : (
                        /* Fallback to basic shipment shipper info */
                        <>
                          {shipment.shipper?.contact_phone && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Phone className="h-3 w-3" />
                              {shipment.shipper.contact_phone}
                            </div>
                          )}
                          {shipment.shipper?.contact_email && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Mail className="h-3 w-3" />
                              {shipment.shipper.contact_email}
                            </div>
                          )}
                          <Badge variant="outline" className="border-slate-600 text-muted-foreground text-xs">
                            <Info className="h-2 w-2 mr-1" />
                            Basic Info
                          </Badge>
                        </>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {shipment.consignee && (
                <div className="p-3 bg-dark/50 rounded-lg">
                  <label className="text-sm text-muted-foreground">Consignee</label>
                  <p className="text-white font-medium">{shipment.consignee.name}</p>
                </div>
              )}
              
              {shipment.notify_party && (
                <div className="p-3 bg-dark/50 rounded-lg">
                  <label className="text-sm text-muted-foreground">Notify Party</label>
                  <p className="text-white font-medium">{shipment.notify_party.name}</p>
                  
                  {/* Notify Party Intelligence */}
                  {(relationshipState.shipmentData?.consigneeNotifyPartyData || shipment.notify_party) && (
                    <div className="mt-2 space-y-1">
                      {/* Show relationship intelligence if available */}
                      {relationshipState.shipmentData?.consigneeNotifyPartyData ? (
                        <>
                          {/* Communication Preferences */}
                          {relationshipState.shipmentData.consigneeNotifyPartyData.notification_preferences && (
                            <div className="grid grid-cols-2 gap-1 mt-1">
                              {relationshipState.shipmentData.consigneeNotifyPartyData.notification_preferences.email && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <Mail className="h-3 w-3 text-blue-400" />
                                  Email
                                </div>
                              )}
                              {relationshipState.shipmentData.consigneeNotifyPartyData.notification_preferences.sms && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <Phone className="h-3 w-3 text-green-400" />
                                  SMS
                                </div>
                              )}
                              {relationshipState.shipmentData.consigneeNotifyPartyData.notification_preferences.line && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <MessageCircle className="h-3 w-3 text-green-400" />
                                  LINE
                                </div>
                              )}
                              {relationshipState.shipmentData.consigneeNotifyPartyData.notification_preferences.wechat && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <MessageCircle className="h-3 w-3 text-green-400" />
                                  WeChat
                                </div>
                              )}
                            </div>
                          )}
                          
                          {/* Special Instructions */}
                          {relationshipState.shipmentData.consigneeNotifyPartyData.special_instructions && (
                            <div className="mt-1 p-2 bg-yellow-900/20 rounded border border-yellow-500/20">
                              <div className="text-xs text-yellow-400 flex items-center gap-1">
                                <Info className="h-3 w-3" />
                                Special Instructions
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                {relationshipState.shipmentData.consigneeNotifyPartyData.special_instructions}
                              </div>
                            </div>
                          )}
                          
                          {/* Default Badge */}
                          {relationshipState.shipmentData.consigneeNotifyPartyData.is_default && (
                            <Badge variant="outline" className="border-orange-500 text-orange-300 bg-orange-500/10 text-xs">
                              <Star className="h-2 w-2 mr-1" />
                              Default Notify Party
                            </Badge>
                          )}
                        </>
                      ) : (
                        /* Fallback to basic notify party info */
                        <>
                          {shipment.notify_party?.contact_phone && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Phone className="h-3 w-3" />
                              {shipment.notify_party.contact_phone}
                            </div>
                          )}
                          {shipment.notify_party?.contact_email && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Mail className="h-3 w-3" />
                              {shipment.notify_party.contact_email}
                            </div>
                          )}
                          <Badge variant="outline" className="border-slate-600 text-muted-foreground text-xs">
                            <Info className="h-2 w-2 mr-1" />
                            Basic Info
                          </Badge>
                        </>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {shipment.forwarder_agent && (
                <div className="p-3 bg-dark/50 rounded-lg">
                  <label className="text-sm text-muted-foreground">Forwarder Agent</label>
                  <p className="text-white font-medium">{shipment.forwarder_agent.name}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Pricing Intelligence */}
          {(relationshipState.shipmentData?.estimatedPricing || 
            relationshipState.availableProducts?.length > 0) && (
            <Card className="bg-card border-secondary">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Pricing Intelligence
                  </div>
                  <Badge variant="outline" className="border-slate-600 text-muted-foreground text-xs">
                    <RefreshCw className="h-2 w-2 mr-1 animate-pulse" />
                    Auto-refresh
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {relationshipState.shipmentData?.estimatedPricing && (
                  <div className="p-3 bg-dark/50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <label className="text-sm text-muted-foreground">Estimated Pricing</label>
                      <Badge variant="outline" className="border-green-500 text-green-300 bg-green-500/10 text-xs">
                        <Info className="h-2 w-2 mr-1" />
                        Based on History
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <p className="text-xs text-muted-foreground">CIF Price</p>
                        <p className="text-white font-medium">
                          {relationshipState.shipmentData.estimatedPricing.unit_price_cif.toFixed(2)} {relationshipState.shipmentData.estimatedPricing.currency_code}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">FOB Price</p>
                        <p className="text-white font-medium">
                          {relationshipState.shipmentData.estimatedPricing.unit_price_fob.toFixed(2)} {relationshipState.shipmentData.estimatedPricing.currency_code}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {relationshipState.availableProducts?.length > 0 && (
                  <div className="p-3 bg-dark/50 rounded-lg">
                    <label className="text-sm text-muted-foreground">Available Products for Customer</label>
                    <div className="mt-2 space-y-2">
                      {relationshipState.availableProducts.slice(0, 3).map((productRel) => (
                        <div key={productRel.id} className="flex items-center justify-between text-xs">
                          <span className="text-muted-foreground">{productRel.product.name}</span>
                          <div className="flex gap-2">
                            <span className="text-muted-foreground">
                              CIF: {productRel.unit_price_cif.toFixed(2)}
                            </span>
                            <span className="text-muted-foreground">
                              FOB: {productRel.unit_price_fob.toFixed(2)}
                            </span>
                          </div>
                        </div>
                      ))}
                      {relationshipState.availableProducts.length > 3 && (
                        <p className="text-xs text-muted-foreground">
                          +{relationshipState.availableProducts.length - 3} more products
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Route Intelligence */}
          {relationshipState.shipmentData?.frequentRoutes?.length > 0 && (
            <Card className="bg-card border-secondary">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Ship className="h-5 w-5" />
                    Route Intelligence
                  </div>
                  <Badge variant="outline" className="border-slate-600 text-muted-foreground text-xs">
                    <RefreshCw className="h-2 w-2 mr-1 animate-pulse" />
                    Auto-refresh
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-3 bg-dark/50 rounded-lg">
                  <label className="text-sm text-muted-foreground">Frequent Routes for This Customer</label>
                  <div className="mt-2 space-y-2">
                    {relationshipState.shipmentData.frequentRoutes.slice(0, 3).map((route, index) => (
                      <div key={index} className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">
                          {route.origin_port_id} → {route.destination_port_id}
                        </span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="border-blue-500 text-blue-300 bg-blue-500/10">
                            {route.usage_count} times
                          </Badge>
                          {index === 0 && (
                            <Badge variant="outline" className="border-orange-500 text-orange-300 bg-orange-500/10">
                              <Star className="h-2 w-2 mr-1" />
                              Most Used
                            </Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {relationshipState.shipmentData?.typicalLeadTimes && (
                  <div className="p-3 bg-dark/50 rounded-lg">
                    <label className="text-sm text-muted-foreground">Typical Lead Times</label>
                    <div className="grid grid-cols-2 gap-4 mt-2 text-xs">
                      <div>
                        <p className="text-muted-foreground">Closing to ETD</p>
                        <p className="text-white font-medium">
                          {relationshipState.shipmentData.typicalLeadTimes.closing_to_etd_hours} hours
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">ETD to ETA</p>
                        <p className="text-white font-medium">
                          {relationshipState.shipmentData.typicalLeadTimes.etd_to_eta_days} days
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Financial Information */}
          {(shipment.total_value_cif || shipment.total_value_fob || shipment.total_weight || shipment.total_volume) && (
            <Card className="bg-card border-secondary">
              <CardHeader>
                <CardTitle className="text-white">Financial & Cargo Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {shipment.total_value_cif && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">CIF Value</span>
                    <span className="text-white">
                      {shipment.total_value_cif.toLocaleString()} {shipment.currency_code || 'USD'}
                    </span>
                  </div>
                )}
                
                {shipment.total_value_fob && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">FOB Value</span>
                    <span className="text-white">
                      {shipment.total_value_fob.toLocaleString()} {shipment.currency_code_fob || 'USD'}
                    </span>
                  </div>
                )}
                
                <div className="border-t border-slate-600 my-3"></div>
                
                {shipment.total_weight && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Weight</span>
                    <span className="text-white">{shipment.total_weight} kg</span>
                  </div>
                )}
                
                {shipment.total_volume && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Volume</span>
                    <span className="text-white">{shipment.total_volume} m³</span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Documents */}
          <Card className="bg-card border-secondary">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Documents & Files
                </div>
                {isStaff && (
                  <Button
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                    onClick={() => {
                      // TODO: Implement document upload
                      alert('Document upload feature coming soon!')
                    }}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Upload
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Document Categories */}
              <div className="space-y-4">
                {/* Shipping Documents */}
                <div className="p-4 bg-dark/50 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-white font-medium flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-500" />
                      Shipping Documents
                      {isLoadingDocuments && (
                        <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />
                      )}
                    </h4>
                    <Badge variant="outline" className="border-blue-500 text-blue-300 bg-blue-500/10">
                      {getDocumentCountByCategory('shipping')} files
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mb-3">
                    Bill of Lading, Commercial Invoice, Packing List, Certificate of Origin
                  </div>
                  
                  {/* Display actual documents if available */}
                  {getDocumentsByCategory('shipping').length > 0 ? (
                    <div className="space-y-2">
                      {getDocumentsByCategory('shipping').map((doc) => (
                        <div key={doc.id} className="p-3 bg-dark/70 rounded border border-secondary">
                          {/* Mobile-First Layout */}
                          <div className="flex items-start gap-3">
                            <FileText className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              {/* Document Info */}
                              <div className="mb-2">
                                <p className="text-white text-sm font-medium truncate">{doc.document_name}</p>
                                {doc.document_number && (
                                  <p className="text-xs text-muted-foreground">#{doc.document_number}</p>
                                )}
                              </div>

                              {/* Badge and Actions - Responsive Layout */}
                              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                                {/* Document Type Badge */}
                                <Badge variant="outline" className="border-green-500 text-green-300 bg-green-500/10 text-xs w-fit">
                                  {doc.document_type.replace('_', ' ')}
                                </Badge>

                                {/* Action Buttons */}
                                <div className="flex items-center gap-2">
                                  {/* View Button */}
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="h-8 px-3 text-xs border-purple-500 text-purple-300 hover:bg-purple-600/20 flex-1 sm:flex-none"
                                    onClick={async () => {
                                      const previewInfo = getDocumentPreviewInfo(doc.file_name)
                                      if (!previewInfo.canPreview) {
                                        alert(`File type '${previewInfo.extension}' cannot be previewed. Please download to view.`)
                                        return
                                      }

                                      const result = await viewDocument(doc.file_path, doc.file_name, doc.storage_bucket)
                                      if (!result.success) {
                                        // Only show error for actual failures, not popup blocker issues
                                        if (result.error?.code !== 'POPUP_BLOCKED') {
                                          alert(`Preview failed: ${result.error?.message}`)
                                        }
                                      }
                                    }}
                                    title="View document"
                                  >
                                    <Eye className="h-3 w-3 mr-1 sm:mr-0" />
                                    <span className="sm:hidden">View</span>
                                  </Button>
                                  {/* Download Button */}
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="h-8 px-3 text-xs border-blue-500 text-blue-300 hover:bg-blue-600/20 flex-1 sm:flex-none"
                                    onClick={async () => {
                                      const result = await downloadDocument(doc.file_path, doc.file_name, doc.storage_bucket)
                                      if (!result.success) {
                                        alert(`Download failed: ${result.error?.message}`)
                                      }
                                    }}
                                    title="Download document"
                                  >
                                    <Download className="h-3 w-3 mr-1 sm:mr-0" />
                                    <span className="sm:hidden">Download</span>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 text-xs">
                      <div className="p-2 bg-dark/30 rounded border border-dashed border-secondary text-center text-muted-foreground">
                        <FileText className="h-4 w-4 mx-auto mb-1" />
                        Bill of Lading
                      </div>
                      <div className="p-2 bg-dark/30 rounded border border-dashed border-secondary text-center text-muted-foreground">
                        <FileText className="h-4 w-4 mx-auto mb-1" />
                        Commercial Invoice
                      </div>
                      <div className="p-2 bg-dark/30 rounded border border-dashed border-secondary text-center text-muted-foreground">
                        <FileText className="h-4 w-4 mx-auto mb-1" />
                        Packing List
                      </div>
                      <div className="p-2 bg-dark/30 rounded border border-dashed border-secondary text-center text-muted-foreground">
                        <FileText className="h-4 w-4 mx-auto mb-1" />
                        Certificate
                      </div>
                    </div>
                  )}
                </div>

                {/* Certificates & Compliance */}
                <div className="p-4 bg-dark/50 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-white font-medium flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Certificates & Compliance
                    </h4>
                    <Badge variant="outline" className="border-green-500 text-green-300 bg-green-500/10">
                      {getDocumentCountByCategory('certificates')} files
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mb-3">
                    Phytosanitary Certificate, Quality Certificate, Fumigation Certificate
                  </div>
                  {shipment.ephyto_refno && (
                    <div className="mb-3 p-2 bg-green-900/20 rounded border border-green-500/20">
                      <div className="text-xs text-green-400">ePhyto Reference</div>
                      <div className="text-sm text-white font-mono">{shipment.ephyto_refno}</div>
                    </div>
                  )}
                  
                  {/* Display actual certificate documents if available */}
                  {getDocumentsByCategory('certificates').length > 0 ? (
                    <div className="space-y-2">
                      {getDocumentsByCategory('certificates').map((doc) => (
                        <div key={doc.id} className="p-3 bg-dark/70 rounded border border-border">
                          {/* Mobile-First Layout */}
                          <div className="flex items-start gap-3">
                            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              {/* Document Info */}
                              <div className="mb-2">
                                <p className="text-white text-sm font-medium truncate">{doc.document_name}</p>
                                {doc.document_number && (
                                  <p className="text-xs text-muted-foreground">#{doc.document_number}</p>
                                )}
                              </div>

                              {/* Badge and Actions - Responsive Layout */}
                              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                                {/* Document Type Badge */}
                                <Badge variant="outline" className="border-green-500 text-green-300 bg-green-500/10 text-xs w-fit">
                                  {doc.document_type.replace('_', ' ')}
                                </Badge>

                                {/* Action Buttons */}
                                <div className="flex items-center gap-2">
                                  {/* View Button */}
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="h-8 px-3 text-xs border-purple-500 text-purple-300 hover:bg-purple-600/20 flex-1 sm:flex-none"
                                    onClick={async () => {
                                      const previewInfo = getDocumentPreviewInfo(doc.file_name)
                                      if (!previewInfo.canPreview) {
                                        alert(`File type '${previewInfo.extension}' cannot be previewed. Please download to view.`)
                                        return
                                      }

                                      const result = await viewDocument(doc.file_path, doc.file_name, doc.storage_bucket)
                                      if (!result.success) {
                                        // Only show error for actual failures, not popup blocker issues
                                        if (result.error?.code !== 'POPUP_BLOCKED') {
                                          alert(`Preview failed: ${result.error?.message}`)
                                        }
                                      }
                                    }}
                                    title="View document"
                                  >
                                    <Eye className="h-3 w-3 mr-1" />
                                    <span className="sm:hidden">View</span>
                                  </Button>
                                  {/* Download Button */}
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="h-8 px-3 text-xs border-green-500 text-green-300 hover:bg-green-600/20 flex-1 sm:flex-none"
                                    onClick={async () => {
                                      const result = await downloadDocument(doc.file_path, doc.file_name, doc.storage_bucket)
                                      if (!result.success) {
                                        alert(`Download failed: ${result.error?.message}`)
                                      }
                                    }}
                                    title="Download document"
                                  >
                                    <Download className="h-3 w-3 mr-1" />
                                    <span className="sm:hidden">Download</span>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 text-xs">
                      <div className="p-2 bg-dark/30 rounded border border-dashed border-border text-center text-muted-foreground">
                        <CheckCircle className="h-4 w-4 mx-auto mb-1" />
                        Phytosanitary
                      </div>
                      <div className="p-2 bg-dark/30 rounded border border-dashed border-border text-center text-muted-foreground">
                        <CheckCircle className="h-4 w-4 mx-auto mb-1" />
                        Quality Cert
                      </div>
                      <div className="p-2 bg-dark/30 rounded border border-dashed border-border text-center text-muted-foreground">
                        <CheckCircle className="h-4 w-4 mx-auto mb-1" />
                        Fumigation
                      </div>
                    </div>
                  )}
                </div>

                {/* Photos & Media */}
                <div className="p-4 bg-dark/50 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-white font-medium flex items-center gap-2">
                      <Camera className="h-4 w-4 text-orange-500" />
                      Photos & Media
                    </h4>
                    <Badge variant="outline" className="border-orange-500 text-orange-300 bg-orange-500/10">
                      {getDocumentCountByCategory('photos')} files
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mb-3">
                    Loading photos, product photos, container condition, quality inspection
                  </div>
                  
                  {/* Display actual photo documents if available */}
                  {getDocumentsByCategory('photos').length > 0 ? (
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                      {getDocumentsByCategory('photos').map((doc) => (
                        <div key={doc.id} className="relative aspect-square bg-dark/70 rounded border border-border overflow-hidden group">
                          <div className="absolute inset-0 flex flex-col items-center justify-center p-2 text-center">
                            <Camera className="h-6 w-6 text-orange-500 mb-2" />
                            <p className="text-white text-xs font-medium truncate w-full">{doc.document_name}</p>
                            <p className="text-xs text-muted-foreground">{doc.document_type.replace('_', ' ')}</p>
                          </div>
                          <div className="absolute inset-0 bg-orange-600/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-8 px-3 text-xs border-orange-500 text-orange-300 hover:bg-orange-600/30"
                              onClick={() => {
                                // TODO: Implement photo view
                                console.log('View photo:', doc.file_path)
                              }}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2">
                      {[1, 2, 3, 4, 5, 6].map((i) => (
                        <div key={i} className="aspect-square p-2 bg-dark/30 rounded border border-dashed border-border text-center text-muted-foreground flex flex-col items-center justify-center text-xs">
                          <Camera className="h-4 w-4 mb-1" />
                          Photo {i}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500 bg-blue-600/10 text-blue-100 hover:bg-blue-600/20 hover:border-blue-400 min-h-[44px] touch-manipulation justify-start"
                    onClick={() => alert('Generate documents feature coming soon!')}
                  >
                    <FileText className="h-3 w-3 mr-2" />
                    Generate Docs
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-green-500 bg-green-600/10 text-green-100 hover:bg-green-600/20 hover:border-green-400 min-h-[44px] touch-manipulation justify-start"
                    onClick={async () => {
                      if (documents.length === 0) {
                        alert('No documents available to download')
                        return
                      }
                      
                      const documentList = documents.map(doc => ({
                        filePath: doc.file_path,
                        fileName: doc.file_name,
                        bucket: doc.storage_bucket
                      }))
                      
                      const result = await downloadDocumentsAsZip(documentList, `${shipment?.shipment_number || 'shipment'}-documents.zip`)
                      if (!result.success) {
                        alert(`Bulk download failed: ${result.error?.message}`)
                      }
                    }}
                  >
                    <Download className="h-3 w-3 mr-2" />
                    Download All
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-purple-500 bg-purple-600/10 text-purple-100 hover:bg-purple-600/20 hover:border-purple-400 min-h-[44px] touch-manipulation justify-start"
                    onClick={() => alert('Email documents feature coming soon!')}
                  >
                    <Mail className="h-3 w-3 mr-2" />
                    Email Docs
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-orange-500 bg-orange-600/10 text-orange-100 hover:bg-orange-600/20 hover:border-orange-400 min-h-[44px] touch-manipulation justify-start"
                    onClick={() => alert('Share documents feature coming soon!')}
                  >
                    <Send className="h-3 w-3 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timestamps */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="text-white">Record Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Created</span>
                <span className="text-white">{formatDate(shipment.created_at)}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last Updated</span>
                <span className="text-white">{formatDate(shipment.updated_at)}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Document Generation Modal */}
      <DocumentGenerationModal
        open={showDocumentGeneration}
        onOpenChange={setShowDocumentGeneration}
        shipmentId={shipmentId}
        shipmentNumber={shipment.shipment_number}
        isGenerating={isGeneratingDocuments}
        onGenerate={handleDocumentGeneration}
      />

      {/* Stakeholder Notification Modal */}
      <StakeholderNotificationModal
        open={showStakeholderNotification}
        onOpenChange={setShowStakeholderNotification}
        shipmentId={shipmentId}
        shipmentNumber={shipment.shipment_number}
        stakeholders={[
          ...(shipment.customer ? [{
            id: `customer_${shipment.customer.id}`,
            type: 'customer' as const,
            name: shipment.customer.name,
            email: shipment.customer.contact_email,
            phone: shipment.customer.contact_phone,
            isDefault: true,
            preferredChannels: ['email', 'sms']
          }] : []),
          ...(shipment.shipper ? [{
            id: `shipper_${shipment.shipper.id}`,
            type: 'shipper' as const,
            name: shipment.shipper.name,
            email: shipment.shipper.contact_email,
            phone: shipment.shipper.contact_phone,
            isDefault: relationshipState.shipmentData?.shipperData?.is_default || false,
            preferredChannels: ['email']
          }] : []),
          ...(shipment.consignee ? [{
            id: `consignee_${shipment.consignee.id}`,
            type: 'consignee' as const,
            name: shipment.consignee.name,
            email: shipment.consignee.contact_email,
            phone: shipment.consignee.contact_phone,
            preferredChannels: ['email']
          }] : []),
          ...(shipment.notify_party ? [{
            id: `notify_party_${shipment.notify_party.id}`,
            type: 'notify_party' as const,
            name: shipment.notify_party.name,
            email: shipment.notify_party.contact_email,
            phone: shipment.notify_party.contact_phone,
            isDefault: relationshipState.shipmentData?.consigneeNotifyPartyData?.is_default || false,
            preferredChannels: relationshipState.shipmentData?.consigneeNotifyPartyData?.notification_preferences 
              ? Object.entries(relationshipState.shipmentData.consigneeNotifyPartyData.notification_preferences)
                  .filter(([key, value]) => value)
                  .map(([key]) => key)
              : ['email']
          }] : []),
          ...(shipment.forwarder_agent ? [{
            id: `forwarder_agent_${shipment.forwarder_agent.id}`,
            type: 'forwarder_agent' as const,
            name: shipment.forwarder_agent.name,
            email: shipment.forwarder_agent.contact_email,
            phone: shipment.forwarder_agent.contact_phone,
            preferredChannels: ['email']
          }] : []),
          ...(transportation?.carrier ? [{
            id: `carrier_${transportation.carrier.id}`,
            type: 'carrier' as const,
            name: transportation.carrier.name,
            email: transportation.carrier.contact_email,
            phone: transportation.carrier.contact_phone,
            preferredChannels: ['email', 'sms']
          }] : []),
          ...(transportation?.driver ? [{
            id: `driver_${transportation.driver.id}`,
            type: 'driver' as const,
            name: `${transportation.driver.driver_first_name} ${transportation.driver.driver_last_name}`,
            phone: transportation.driver_phone,
            contactPerson: 'Driver',
            preferredChannels: ['sms']
          }] : [])
        ]}
        isSending={isSendingNotifications}
        onSendNotification={handleStakeholderNotification}
      />
    </div>
  )
}