import type { SupabaseClient } from '@supabase/supabase-js'
import type { 
  Database, 
  ShipmentWithRelations, 
  ShipmentFilters, 
  SortConfig,
  PaginationConfig 
} from '@/lib/supabase/types'

export interface ShipmentQueryOptions {
  filters: ShipmentFilters
  sort: SortConfig
  pagination: PaginationConfig
}

export interface ShipmentQueryResult {
  data: ShipmentWithRelations[]
  totalCount: number
  error?: string
}

/**
 * Service class for optimized shipment database operations
 * Handles RLS policies, query optimization, and performance enhancements
 */
export class ShipmentService {
  constructor(private supabase: SupabaseClient<Database>) {}

  /**
   * Fetch shipments with optimized queries and RLS enforcement
   */
  async getShipments(options: ShipmentQueryOptions): Promise<ShipmentQueryResult> {
    try {
      const { filters, sort, pagination } = options
      
      // Build base query with optimized joins
      let query = this.supabase
        .from('shipments')
        .select(`
          id,
          shipment_number,
          invoice_number,
          status,
          transportation_mode,
          etd_date,
          eta_date,
          closing_time,
          vessel_name,
          voyage_number,
          booking_number,
          total_weight,
          total_volume,
          total_quantity,
          total_gross_weight,
          total_net_weight,
          total_value_cif,
          total_value_fob,
          currency_code,
          notes,
          created_at,
          updated_at,
          customer:companies!customer_id(id, name, company_type),
          shipper:companies!shipper_id(id, name, company_type),
          consignee:companies!consignee_id(id, name, company_type),
          notify_party:companies!notify_party_id(id, name, company_type),
          factory:companies!factory_id(id, name, company_type),
          forwarder_agent:companies!forwarder_agent_id(id, name, company_type),
          origin_port:ports!origin_port_id(id, name, code, country),
          destination_port:ports!destination_port_id(id, name, code, country),
          containers(
            id,
            container_number,
            container_type,
            container_size,
            seal_number,
            temperature,
            vent,
            status,
            gross_weight,
            volume
          )
        `)

      // Apply search filters with proper indexing hints
      if (filters.search.trim()) {
        const searchTerm = `%${filters.search.trim()}%`
        // Use indexed columns for better performance
        query = query.or(`shipment_number.ilike.${searchTerm},invoice_number.ilike.${searchTerm},vessel_name.ilike.${searchTerm},booking_number.ilike.${searchTerm}`)
      }

      // Apply status filters (indexed column)
      if (filters.status.length > 0) {
        query = query.in('status', filters.status)
      }

      // Apply customer filters (indexed column)
      if (filters.customer_id.length > 0) {
        query = query.in('customer_id', filters.customer_id)
      }

      // Apply transportation mode filters (indexed column)
      if (filters.transportation_mode.length > 0) {
        query = query.in('transportation_mode', filters.transportation_mode)
      }

      // Apply date range filters (indexed columns)
      if (filters.date_range.start) {
        query = query.gte('etd_date', filters.date_range.start)
      }
      if (filters.date_range.end) {
        query = query.lte('etd_date', filters.date_range.end)
      }

      // Apply port filters (indexed columns)
      if (filters.origin_port_id.length > 0) {
        query = query.in('origin_port_id', filters.origin_port_id)
      }
      if (filters.destination_port_id.length > 0) {
        query = query.in('destination_port_id', filters.destination_port_id)
      }

      // Apply sorting with proper indexing
      query = query.order(sort.field, { ascending: sort.direction === 'asc' })

      // Get total count for pagination (separate optimized query)
      const countQuery = this.buildCountQuery(filters)
      
      // Apply pagination using efficient offset/limit
      const from = (pagination.page - 1) * pagination.pageSize
      const to = from + pagination.pageSize - 1
      query = query.range(from, to)

      // Execute queries in parallel for better performance
      const [dataResult, countResult] = await Promise.all([
        query,
        countQuery
      ])

      if (dataResult.error) {
        throw new Error(`Query failed: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Count query failed: ${countResult.error.message}`)
      }

      return {
        data: dataResult.data as ShipmentWithRelations[],
        totalCount: countResult.count || 0,
      }

    } catch (error: any) {
      console.error('ShipmentService.getShipments error:', error)
      return {
        data: [],
        totalCount: 0,
        error: error.message || 'Failed to fetch shipments',
      }
    }
  }

  /**
   * Build optimized count query with same filters
   */
  private buildCountQuery(filters: ShipmentFilters) {
    let query = this.supabase
      .from('shipments')
      .select('id', { count: 'exact', head: true })

    // Apply the same filters to count query for accuracy
    if (filters.search.trim()) {
      const searchTerm = `%${filters.search.trim()}%`
      query = query.or(`shipment_number.ilike.${searchTerm},invoice_number.ilike.${searchTerm},vessel_name.ilike.${searchTerm},booking_number.ilike.${searchTerm}`)
    }

    if (filters.status.length > 0) {
      query = query.in('status', filters.status)
    }

    if (filters.customer_id.length > 0) {
      query = query.in('customer_id', filters.customer_id)
    }

    if (filters.transportation_mode.length > 0) {
      query = query.in('transportation_mode', filters.transportation_mode)
    }

    if (filters.date_range.start) {
      query = query.gte('etd_date', filters.date_range.start)
    }
    if (filters.date_range.end) {
      query = query.lte('etd_date', filters.date_range.end)
    }

    if (filters.origin_port_id.length > 0) {
      query = query.in('origin_port_id', filters.origin_port_id)
    }

    if (filters.destination_port_id.length > 0) {
      query = query.in('destination_port_id', filters.destination_port_id)
    }

    return query
  }

  /**
   * Get single shipment with full details
   */
  async getShipmentById(id: string): Promise<ShipmentWithRelations | null> {
    try {
      // First, get the basic shipment data with essential relations
      const { data: shipmentData, error: shipmentError } = await this.supabase
        .from('shipments')
        .select(`
          *,
          customer:companies!customer_id(*),
          shipper:companies!shipper_id(*),
          consignee:companies!consignee_id(*),
          notify_party:companies!notify_party_id(*),
          factory:companies!factory_id(*),
          forwarder_agent:companies!forwarder_agent_id(*),
          origin_port:ports!origin_port_id(*),
          destination_port:ports!destination_port_id(*)
        `)
        .eq('id', id)
        .single()

      if (shipmentError) {
        console.error('Error fetching shipment:', shipmentError)
        return null
      }

      // Then fetch containers separately
      const { data: containers, error: containersError } = await this.supabase
        .from('containers')
        .select('*')
        .eq('shipment_id', id)

      if (containersError) {
        console.error('Error fetching containers:', containersError)
      }

      // Finally fetch shipment products with their relations separately
      const { data: shipmentProducts, error: productsError } = await this.supabase
        .from('shipment_products')
        .select(`
          *,
          product:products(*),
          unit_of_measure:units_of_measure(*)
        `)
        .eq('shipment_id', id)
        .order('created_at', { ascending: true })

      if (productsError) {
        console.error('Error fetching shipment products:', productsError)
      }

      // Combine all data
      const result = {
        ...shipmentData,
        containers: containers || [],
        shipment_products: shipmentProducts || []
      } as ShipmentWithRelations

      return result
    } catch (error) {
      console.error('ShipmentService.getShipmentById error:', error)
      return null
    }
  }

  /**
   * Subscribe to real-time shipment updates
   */
  subscribeToShipmentUpdates(
    callback: (payload: any) => void,
    filters?: Partial<ShipmentFilters>
  ) {
    // Create a wrapper callback that handles filtering client-side
    const filteredCallback = (payload: any) => {
      const { new: newRecord, old: oldRecord } = payload
      
      // For INSERT and UPDATE events, check if the record matches filters
      if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
        const recordToCheck = newRecord || oldRecord
        
        // Apply status filter if provided
        if (filters?.status && filters.status.length > 0) {
          if (!recordToCheck.status || !filters.status.includes(recordToCheck.status)) {
            return // Skip this update if it doesn't match status filter
          }
        }
        
        // Apply customer filter if provided
        if (filters?.customer_id && filters.customer_id.length > 0) {
          if (!recordToCheck.customer_id || !filters.customer_id.includes(recordToCheck.customer_id)) {
            return // Skip this update if it doesn't match customer filter
          }
        }
      }
      
      // If we reach here, the update matches filters (or no filters are set)
      callback(payload)
    }

    const subscription = this.supabase
      .channel('shipment-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'shipments',
        },
        filteredCallback
      )

    return subscription.subscribe()
  }

  /**
   * Get shipment statistics for dashboard
   */
  async getShipmentStats(filters?: Partial<ShipmentFilters>) {
    try {
      let query = this.supabase
        .from('shipments')
        .select('status, transportation_mode, created_at')

      // Apply filters if provided
      if (filters?.customer_id && filters.customer_id.length > 0) {
        query = query.in('customer_id', filters.customer_id)
      }

      if (filters?.date_range?.start) {
        query = query.gte('created_at', filters.date_range.start)
      }
      if (filters?.date_range?.end) {
        query = query.lte('created_at', filters.date_range.end)
      }

      const { data, error } = await query

      if (error) throw error

      // Calculate statistics
      const stats = {
        total: data?.length || 0,
        byStatus: {} as Record<string, number>,
        byTransportMode: {} as Record<string, number>,
        recentCount: 0,
      }

      const now = new Date()
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

      data?.forEach(shipment => {
        // Status counts
        const status = shipment.status || 'unknown'
        stats.byStatus[status] = (stats.byStatus[status] || 0) + 1

        // Transport mode counts
        const mode = shipment.transportation_mode || 'unknown'
        stats.byTransportMode[mode] = (stats.byTransportMode[mode] || 0) + 1

        // Recent shipments (last 7 days)
        const createdAt = new Date(shipment.created_at || '')
        if (createdAt >= sevenDaysAgo) {
          stats.recentCount++
        }
      })

      return stats
    } catch (error) {
      console.error('ShipmentService.getShipmentStats error:', error)
      return {
        total: 0,
        byStatus: {},
        byTransportMode: {},
        recentCount: 0,
      }
    }
  }

  /**
   * Bulk update shipment statuses
   */
  async bulkUpdateStatus(shipmentIds: string[], newStatus: string) {
    try {
      const { data, error } = await this.supabase
        .from('shipments')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString(),
        })
        .in('id', shipmentIds)
        .select('id, shipment_number, status')

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      console.error('ShipmentService.bulkUpdateStatus error:', error)
      return { data: null, error: error.message }
    }
  }
}

// Singleton instance
let shipmentService: ShipmentService | null = null

/**
 * Get shipment service instance
 */
export const getShipmentService = (supabase: SupabaseClient<Database>) => {
  if (!shipmentService) {
    shipmentService = new ShipmentService(supabase)
  }
  return shipmentService
}