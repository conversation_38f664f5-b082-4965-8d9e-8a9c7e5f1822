'use client'

import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type { DriverFilter } from '@/lib/validations/drivers'

// Driver interface matching database schema
export interface Driver {
  id: string
  carrier_id: string
  driver_first_name: string
  driver_last_name: string
  driver_code: string | null
  user_id: string | null
  phone: string | null
  line_id: string | null
  driver_picture_path: string | null
  driver_picture_mime_type: string | null
  notes: string | null
  is_active: boolean
  vehicle_head_number: string | null
  vehicle_tail_number: string | null
  created_at: string
  updated_at: string
  // Joined carrier company information
  carrier?: {
    id: string
    name: string
    company_type: string
    contact_phone: string | null
    is_active: boolean
  } | null
}

// Driver insert interface
export interface DriverInsert {
  carrier_id: string
  driver_first_name: string
  driver_last_name: string
  driver_code?: string | null
  user_id?: string | null
  phone?: string | null
  line_id?: string | null
  driver_picture_path?: string | null
  driver_picture_mime_type?: string | null
  notes?: string | null
  is_active?: boolean
  vehicle_head_number?: string | null
  vehicle_tail_number?: string | null
}

// Driver update interface
export interface DriverUpdate {
  carrier_id?: string
  driver_first_name?: string
  driver_last_name?: string
  driver_code?: string | null
  user_id?: string | null
  phone?: string | null
  line_id?: string | null
  driver_picture_path?: string | null
  driver_picture_mime_type?: string | null
  notes?: string | null
  is_active?: boolean
  vehicle_head_number?: string | null
  vehicle_tail_number?: string | null
}

interface DriverState {
  // Data state
  drivers: Driver[]
  loading: boolean
  error: string | null

  // Filter and search state
  filter: DriverFilter
  searchTerm: string
  sortBy:
    | 'driver_first_name'
    | 'driver_last_name'
    | 'driver_code'
    | 'phone'
    | 'is_active'
    | 'created_at'
  sortOrder: 'asc' | 'desc'

  // Pagination state
  currentPage: number
  pageSize: number
  totalCount: number

  // UI state
  selectedDrivers: Set<string>
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  isUploadingPhoto: boolean

  // Actions
  fetchDrivers: () => Promise<void>
  fetchDriverById: (id: string) => Promise<Driver | null>
  createDriver: (driver: DriverInsert) => Promise<Driver>
  updateDriver: (id: string, updates: Partial<DriverUpdate>) => Promise<Driver>
  deleteDriver: (id: string) => Promise<void>
  deleteDrivers: (ids: string[]) => Promise<void>

  // Photo upload actions
  uploadDriverPhoto: (driverId: string, file: File) => Promise<string>
  deleteDriverPhoto: (driverId: string) => Promise<void>

  // Filter and search actions
  setFilter: (filter: Partial<DriverFilter>) => void
  setSearchTerm: (term: string) => void
  setSorting: (
    sortBy: DriverState['sortBy'],
    sortOrder: DriverState['sortOrder']
  ) => void

  // Pagination actions
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Selection actions
  selectDriver: (id: string) => void
  deselectDriver: (id: string) => void
  selectAllDrivers: () => void
  clearSelection: () => void

  // Utility actions
  clearError: () => void
  reset: () => void

  // Real-time subscription management
  subscribeToDrivers: () => () => void
}

const initialState = {
  drivers: [],
  loading: false,
  error: null,
  filter: {},
  searchTerm: '',
  sortBy: 'driver_first_name' as const,
  sortOrder: 'asc' as const,
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  selectedDrivers: new Set<string>(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isUploadingPhoto: false,
}

export const useDriverStore = create<DriverState>((set, get) => ({
  ...initialState,

  // Fetch drivers with filters, search, and pagination
  fetchDrivers: async () => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()
      const { filter, searchTerm, sortBy, sortOrder, currentPage, pageSize } =
        get()

      let query = supabase.from('drivers').select(
        `
          *,
          carrier:companies!carrier_id(
            id,
            name,
            company_type,
            contact_phone,
            is_active
          )
        `,
        { count: 'exact' }
      )

      // Apply filters
      if (filter.carrier_id) {
        query = query.eq('carrier_id', filter.carrier_id)
      }

      if (filter.is_active !== undefined) {
        query = query.eq('is_active', filter.is_active)
      } else {
        // Default to show only active drivers
        query = query.eq('is_active', true)
      }

      // Filter to only show drivers from active carrier companies
      query = query.eq('companies.is_active', true)
      query = query.eq('companies.company_type', 'carrier')

      // Apply search
      if (searchTerm.trim()) {
        query = query.or(
          `driver_first_name.ilike.%${searchTerm}%,driver_last_name.ilike.%${searchTerm}%,driver_code.ilike.%${searchTerm}%,phone.ilike.%${searchTerm}%`
        )
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })

      // Apply pagination
      const from = (currentPage - 1) * pageSize
      const to = from + pageSize - 1
      query = query.range(from, to)

      const { data, count, error } = await query

      if (error) {
        throw new Error(error.message)
      }

      set({
        drivers: data || [],
        totalCount: count || 0,
        loading: false,
      })
    } catch (error) {
      console.error('Error fetching drivers:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch drivers',
        loading: false,
      })
    }
  },

  // Fetch single driver by ID
  fetchDriverById: async (id: string) => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('drivers')
        .select(
          `
          *,
          carrier:companies!carrier_id(
            id,
            name,
            company_type,
            contact_phone,
            is_active
          )
        `
        )
        .eq('id', id)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    } catch (error) {
      console.error('Error fetching driver:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch driver',
      })
      return null
    }
  },

  // Create new driver
  createDriver: async (driverData: DriverInsert) => {
    set({ isCreating: true, error: null })

    try {
      const supabase = createClient()

      // Validate carrier company exists and is of type 'carrier'
      const { data: carrierCompany, error: carrierError } = await supabase
        .from('companies')
        .select('id, company_type, is_active')
        .eq('id', driverData.carrier_id)
        .single()

      if (carrierError || !carrierCompany) {
        throw new Error('Selected carrier company not found')
      }

      if (carrierCompany.company_type !== 'carrier') {
        throw new Error('Selected company must be of type "carrier"')
      }

      if (!carrierCompany.is_active) {
        throw new Error('Selected carrier company is inactive')
      }

      // Check for duplicate driver code if provided
      if (driverData.driver_code) {
        const { data: existingDriver } = await supabase
          .from('drivers')
          .select('id')
          .eq('driver_code', driverData.driver_code)
          .eq('is_active', true)
          .single()

        if (existingDriver) {
          throw new Error(
            `Driver with code "${driverData.driver_code}" already exists`
          )
        }
      }

      // Check for duplicate user_id if provided
      if (driverData.user_id && driverData.user_id !== null) {
        const { data: existingUserDriver } = await supabase
          .from('drivers')
          .select('id')
          .eq('user_id', driverData.user_id)
          .eq('is_active', true)
          .single()

        if (existingUserDriver) {
          throw new Error(
            'This user account is already linked to another driver'
          )
        }
      }

      // Create the driver
      const { data: driver, error: driverError } = await supabase
        .from('drivers')
        .insert(driverData)
        .select()
        .single()

      if (driverError) {
        throw new Error(driverError.message)
      }

      // Refresh the list to show the new driver
      await get().fetchDrivers()

      set({ isCreating: false })
      return driver
    } catch (error) {
      console.error('Error creating driver:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to create driver',
        isCreating: false,
      })
      throw error
    }
  },

  // Update existing driver
  updateDriver: async (id: string, updates: Partial<DriverUpdate>) => {
    set({ isUpdating: true, error: null })

    try {
      const supabase = createClient()

      // If updating carrier_id, validate it exists and is of type 'carrier'
      if (updates.carrier_id) {
        const { data: carrierCompany, error: carrierError } = await supabase
          .from('companies')
          .select('id, company_type, is_active')
          .eq('id', updates.carrier_id)
          .single()

        if (carrierError || !carrierCompany) {
          throw new Error('Selected carrier company not found')
        }

        if (carrierCompany.company_type !== 'carrier') {
          throw new Error('Selected company must be of type "carrier"')
        }

        if (!carrierCompany.is_active) {
          throw new Error('Selected carrier company is inactive')
        }
      }

      // If updating driver code, check for duplicates
      if (updates.driver_code) {
        const { data: existingDriver } = await supabase
          .from('drivers')
          .select('id')
          .eq('driver_code', updates.driver_code)
          .eq('is_active', true)
          .neq('id', id)
          .single()

        if (existingDriver) {
          throw new Error(
            `Driver with code "${updates.driver_code}" already exists`
          )
        }
      }

      // If updating user_id, check for duplicates
      if (updates.user_id && updates.user_id !== null) {
        const { data: existingUserDriver } = await supabase
          .from('drivers')
          .select('id')
          .eq('user_id', updates.user_id)
          .eq('is_active', true)
          .neq('id', id)
          .single()

        if (existingUserDriver) {
          throw new Error(
            'This user account is already linked to another driver'
          )
        }
      }

      // Update the driver
      const { error: updateError } = await supabase
        .from('drivers')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (updateError) {
        throw new Error(updateError.message)
      }

      // Fetch the updated driver with carrier information
      const { data: updatedDriver, error: fetchError } = await supabase
        .from('drivers')
        .select(
          `
          *,
          carrier:companies!carrier_id(
            id,
            name,
            company_type,
            contact_phone,
            is_active
          )
        `
        )
        .eq('id', id)
        .single()

      if (fetchError) {
        throw new Error(fetchError.message)
      }

      // Update the driver in local state
      set(state => ({
        drivers: state.drivers.map(driver =>
          driver.id === id ? { ...driver, ...updatedDriver } : driver
        ),
        isUpdating: false,
      }))

      return updatedDriver
    } catch (error) {
      console.error('Error updating driver:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to update driver',
        isUpdating: false,
      })
      throw error
    }
  },

  // Delete single driver
  deleteDriver: async (id: string) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('drivers')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        drivers: state.drivers.filter(driver => driver.id !== id),
        selectedDrivers: new Set(
          [...state.selectedDrivers].filter(selectedId => selectedId !== id)
        ),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting driver:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete driver',
        isDeleting: false,
      })
      throw error
    }
  },

  // Delete multiple drivers
  deleteDrivers: async (ids: string[]) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('drivers')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .in('id', ids)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        drivers: state.drivers.filter(driver => !ids.includes(driver.id)),
        selectedDrivers: new Set(),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting drivers:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete drivers',
        isDeleting: false,
      })
      throw error
    }
  },

  // Upload driver photo
  uploadDriverPhoto: async (driverId: string, file: File) => {
    set({ isUploadingPhoto: true, error: null })

    try {
      const supabase = createClient()

      // Compress image before upload
      const compressedFile = await compressImage(file)

      // Generate unique filename
      const fileExtension = compressedFile.name.split('.').pop()
      const fileName = `drivers/${driverId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('driver-photos')
        .upload(fileName, compressedFile, {
          cacheControl: '3600',
          upsert: true,
        })

      if (uploadError) {
        throw new Error(`Photo upload failed: ${uploadError.message}`)
      }

      // Update driver record with photo path and MIME type
      const { error: updateError } = await supabase
        .from('drivers')
        .update({
          driver_picture_path: uploadData.path,
          driver_picture_mime_type: compressedFile.type,
          updated_at: new Date().toISOString(),
        })
        .eq('id', driverId)

      if (updateError) {
        // Clean up uploaded file if database update fails
        await supabase.storage.from('driver-photos').remove([uploadData.path])
        throw new Error(`Failed to update driver photo: ${updateError.message}`)
      }

      set({ isUploadingPhoto: false })
      return uploadData.path
    } catch (error) {
      console.error('Error uploading driver photo:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to upload photo',
        isUploadingPhoto: false,
      })
      throw error
    }
  },

  // Delete driver photo
  deleteDriverPhoto: async (driverId: string) => {
    set({ isUploadingPhoto: true, error: null })

    try {
      const supabase = createClient()

      // Get current photo path
      const { data: driver, error: fetchError } = await supabase
        .from('drivers')
        .select('driver_picture_path')
        .eq('id', driverId)
        .single()

      if (fetchError) {
        throw new Error('Failed to fetch driver photo information')
      }

      // Remove photo from storage if it exists
      if (driver.driver_picture_path) {
        const { error: deleteError } = await supabase.storage
          .from('driver-photos')
          .remove([driver.driver_picture_path])

        if (deleteError) {
          console.warn('Failed to delete photo from storage:', deleteError)
        }
      }

      // Update driver record to remove photo references
      const { error: updateError } = await supabase
        .from('drivers')
        .update({
          driver_picture_path: null,
          driver_picture_mime_type: null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', driverId)

      if (updateError) {
        throw new Error(
          `Failed to update driver record: ${updateError.message}`
        )
      }

      set({ isUploadingPhoto: false })
    } catch (error) {
      console.error('Error deleting driver photo:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete photo',
        isUploadingPhoto: false,
      })
      throw error
    }
  },

  // Filter and search actions
  setFilter: (newFilter: Partial<DriverFilter>) => {
    set(state => ({
      filter: { ...state.filter, ...newFilter },
      currentPage: 1, // Reset to first page when filtering
    }))
    get().fetchDrivers()
  },

  setSearchTerm: (term: string) => {
    set({ searchTerm: term, currentPage: 1 })
    // Debounce the search
    setTimeout(() => {
      if (get().searchTerm === term) {
        get().fetchDrivers()
      }
    }, 300)
  },

  setSorting: (
    sortBy: DriverState['sortBy'],
    sortOrder: DriverState['sortOrder']
  ) => {
    set({ sortBy, sortOrder })
    get().fetchDrivers()
  },

  // Pagination actions
  setPage: (page: number) => {
    set({ currentPage: page })
    get().fetchDrivers()
  },

  setPageSize: (size: number) => {
    set({ pageSize: size, currentPage: 1 })
    get().fetchDrivers()
  },

  // Selection actions
  selectDriver: (id: string) => {
    set(state => ({
      selectedDrivers: new Set([...state.selectedDrivers, id]),
    }))
  },

  deselectDriver: (id: string) => {
    set(state => {
      const newSelection = new Set(state.selectedDrivers)
      newSelection.delete(id)
      return { selectedDrivers: newSelection }
    })
  },

  selectAllDrivers: () => {
    set(state => ({
      selectedDrivers: new Set(state.drivers.map(driver => driver.id)),
    }))
  },

  clearSelection: () => {
    set({ selectedDrivers: new Set() })
  },

  // Utility actions
  clearError: () => {
    set({ error: null })
  },

  reset: () => {
    set(initialState)
  },

  // Real-time subscription
  subscribeToDrivers: () => {
    const supabase = createClient()

    const subscription = supabase
      .channel('drivers_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'drivers',
        },
        payload => {
          console.log('Driver change received:', payload)
          // Refresh the list when changes occur
          get().fetchDrivers()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  },
}))

// Image compression utility function
async function compressImage(file: File): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // Calculate new dimensions (max 400x400)
      const maxWidth = 400
      const maxHeight = 400
      let { width, height } = img

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }

      canvas.width = width
      canvas.height = height

      // Draw and compress
      if (ctx) {
        ctx.drawImage(img, 0, 0, width, height)
        canvas.toBlob(
          blob => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now(),
              })
              resolve(compressedFile)
            } else {
              reject(new Error('Failed to compress image'))
            }
          },
          'image/jpeg',
          0.8 // Quality setting
        )
      } else {
        reject(new Error('Failed to get canvas context'))
      }
    }

    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}

// Selector hooks for better performance
export const useDrivers = () => useDriverStore(state => state.drivers)
export const useDriversLoading = () => useDriverStore(state => state.loading)
export const useDriversError = () => useDriverStore(state => state.error)
export const useDriverActions = () =>
  useDriverStore(state => ({
    fetchDrivers: state.fetchDrivers,
    fetchDriverById: state.fetchDriverById,
    createDriver: state.createDriver,
    updateDriver: state.updateDriver,
    deleteDriver: state.deleteDriver,
    deleteDrivers: state.deleteDrivers,
    uploadDriverPhoto: state.uploadDriverPhoto,
    deleteDriverPhoto: state.deleteDriverPhoto,
    setFilter: state.setFilter,
    setSearchTerm: state.setSearchTerm,
    setSorting: state.setSorting,
    setPage: state.setPage,
    selectDriver: state.selectDriver,
    deselectDriver: state.deselectDriver,
    clearSelection: state.clearSelection,
    clearError: state.clearError,
    subscribeToDrivers: state.subscribeToDrivers,
  }))
