'use client'

import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  FileDown,
  Calendar,
  CreditCard,
  Wallet,
  Target,
  ArrowUpRight,
  ArrowDownRight,
  Download,
  Calculator,
  Receipt,
  Banknote,
  CircleDollarSign
} from 'lucide-react'

// Mock financial data
const mockFinancialOverview = {
  totalRevenue: '$8,947,250',
  monthlyRevenue: '$2,847,650',
  quarterlyRevenue: '$7,234,890',
  profit: '$1,789,430',
  profitMargin: '19.98%',
  outstandingInvoices: '$456,780',
  paidInvoices: '$2,390,870',
  averageOrderValue: '$23,450'
}

const mockRevenueByMonth = [
  { month: 'Jan', revenue: 2450000, expenses: 1890000 },
  { month: 'Feb', revenue: 2680000, expenses: 2100000 },
  { month: 'Mar', revenue: 2890000, expenses: 2200000 },
  { month: 'Apr', revenue: 3120000, expenses: 2350000 },
  { month: 'May', revenue: 2950000, expenses: 2180000 },
  { month: 'Jun', revenue: 3340000, expenses: 2480000 },
  { month: 'Jul', revenue: 3580000, expenses: 2650000 },
  { month: 'Aug', revenue: 3220000, expenses: 2420000 },
  { month: 'Sep', revenue: 2847650, expenses: 2058220 }
]

const mockRevenueByRegion = [
  { region: 'North America', revenue: '$3,245,890', percentage: 36.3, color: 'bg-blue-500' },
  { region: 'Europe', revenue: '$2,890,450', percentage: 32.3, color: 'bg-green-500' },
  { region: 'Asia Pacific', revenue: '$1,890,340', percentage: 21.1, color: 'bg-orange-500' },
  { region: 'Others', revenue: '$920,570', percentage: 10.3, color: 'bg-purple-500' }
]

const mockRevenueByProduct = [
  { product: 'Mangoes', revenue: '$2,456,780', percentage: 27.5 },
  { product: 'Pineapples', revenue: '$1,890,340', percentage: 21.1 },
  { product: 'Dragon Fruit', revenue: '$1,567,230', percentage: 17.5 },
  { product: 'Lychees', revenue: '$1,234,560', percentage: 13.8 },
  { product: 'Rambutans', revenue: '$1,023,450', percentage: 11.4 },
  { product: 'Others', revenue: '$774,890', percentage: 8.7 }
]

const mockTopCustomers = [
  { name: 'Golden Fruits International', revenue: '$567,890', orders: 45, growth: '+18.5%' },
  { name: 'Fresh Express Trading', revenue: '$489,230', orders: 38, growth: '+12.3%' },
  { name: 'Premium Fruit Co.', revenue: '$423,670', orders: 29, growth: '+8.7%' },
  { name: 'Tropical Harvest Ltd.', revenue: '$389,450', orders: 34, growth: '+15.2%' },
  { name: 'Exotic Fruit Imports', revenue: '$345,780', orders: 26, growth: '+6.9%' }
]

const mockInvoiceStatus = [
  { status: 'Paid', amount: '$2,390,870', count: 156, color: 'bg-green-500' },
  { status: 'Outstanding', amount: '$456,780', count: 23, color: 'bg-yellow-500' },
  { status: 'Overdue', amount: '$123,450', count: 8, color: 'bg-red-500' },
  { status: 'Draft', amount: '$89,230', count: 12, color: 'bg-slate-500' }
]

const StatCard = ({ title, value, trend, icon: Icon, color, subtitle, trendIcon }: {
  title: string
  value: string
  trend?: string
  icon: any
  color: string
  subtitle?: string
  trendIcon?: 'up' | 'down'
}) => {
  const isPositive = trend?.startsWith('+') || trendIcon === 'up'
  const isNegative = trend?.startsWith('-') || trendIcon === 'down'

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-slate-400 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-white mt-1">{value}</p>
            {subtitle && <p className="text-xs text-slate-500 mt-1">{subtitle}</p>}
            {trend && (
              <div className="flex items-center mt-2">
                {isPositive && <ArrowUpRight className="w-3 h-3 text-green-500 mr-1" />}
                {isNegative && <ArrowDownRight className="w-3 h-3 text-red-500 mr-1" />}
                <span className={`text-xs font-medium ${
                  isPositive ? 'text-green-500' : isNegative ? 'text-red-500' : 'text-slate-400'
                }`}>
                  {trend}
                </span>
              </div>
            )}
          </div>
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
      </CardContent>
    </Card>
  )
}

const RevenueChart = () => {
  return (
    <div className="space-y-4">
      {mockRevenueByMonth.slice(-6).map((item, index) => {
        const profit = item.revenue - item.expenses
        const profitMargin = ((profit / item.revenue) * 100).toFixed(1)

        return (
          <div key={index} className="flex items-center justify-between py-3 border-b border-slate-700 last:border-b-0">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <span className="text-slate-300 font-medium">{item.month} 2024</span>
                <span className="text-green-400 text-sm">{profitMargin}% margin</span>
              </div>
              <div className="flex space-x-4 text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-slate-400">Revenue: ${(item.revenue / 1000000).toFixed(2)}M</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  <span className="text-slate-400">Expenses: ${(item.expenses / 1000000).toFixed(2)}M</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <p className="text-white font-bold">${(profit / 1000000).toFixed(2)}M</p>
              <p className="text-slate-400 text-xs">Profit</p>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default function FinancialReportsPage() {
  const [dateRange, setDateRange] = useState('30')
  const [currency, setCurrency] = useState('usd')
  const [reportType, setReportType] = useState('overview')

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Financial Reports</h1>
          <p className="text-slate-400">Revenue analytics and financial performance insights</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
            <FileDown className="w-4 h-4 mr-2" />
            Export PDF
          </Button>
          <Button className="bg-orange-500 hover:bg-orange-600 text-white">
            <Download className="w-4 h-4 mr-2" />
            Download Report
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700 mb-6">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-slate-400" />
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last quarter</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-slate-400" />
              <Select value={currency} onValueChange={setCurrency}>
                <SelectTrigger className="w-32 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="usd">USD ($)</SelectItem>
                  <SelectItem value="eur">EUR (€)</SelectItem>
                  <SelectItem value="thb">THB (฿)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4 text-slate-400" />
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="overview">Overview</SelectItem>
                  <SelectItem value="detailed">Detailed</SelectItem>
                  <SelectItem value="comparison">Comparison</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Financial Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Revenue"
          value={mockFinancialOverview.totalRevenue}
          trend="+23.5%"
          icon={CircleDollarSign}
          color="text-green-500"
          subtitle="Year to date"
          trendIcon="up"
        />
        <StatCard
          title="Monthly Revenue"
          value={mockFinancialOverview.monthlyRevenue}
          trend="+12.8%"
          icon={Banknote}
          color="text-blue-500"
          subtitle="This month"
          trendIcon="up"
        />
        <StatCard
          title="Profit"
          value={mockFinancialOverview.profit}
          trend="+18.2%"
          icon={TrendingUp}
          color="text-purple-500"
          subtitle={`${mockFinancialOverview.profitMargin} margin`}
          trendIcon="up"
        />
        <StatCard
          title="Avg Order Value"
          value={mockFinancialOverview.averageOrderValue}
          trend="+5.4%"
          icon={Calculator}
          color="text-orange-500"
          subtitle="Per shipment"
          trendIcon="up"
        />
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="bg-slate-800 border-slate-700">
          <TabsTrigger value="overview" className="data-[state=active]:bg-orange-500">Revenue Overview</TabsTrigger>
          <TabsTrigger value="invoices" className="data-[state=active]:bg-orange-500">Invoices & Payments</TabsTrigger>
          <TabsTrigger value="customers" className="data-[state=active]:bg-orange-500">Customer Analysis</TabsTrigger>
          <TabsTrigger value="products" className="data-[state=active]:bg-orange-500">Product Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Monthly Revenue Trend */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 text-green-500" />
                  Monthly Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RevenueChart />
              </CardContent>
            </Card>

            {/* Revenue by Region */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <PieChart className="w-5 h-5 mr-2 text-blue-500" />
                  Revenue by Region
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockRevenueByRegion.map((region, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${region.color}`} />
                        <span className="text-slate-300 text-sm">{region.region}</span>
                      </div>
                      <div className="text-right">
                        <p className="text-white font-medium">{region.revenue}</p>
                        <p className="text-slate-400 text-xs">{region.percentage}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="invoices" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Invoice Status */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Receipt className="w-5 h-5 mr-2 text-blue-500" />
                  Invoice Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockInvoiceStatus.map((invoice, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-slate-700">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${invoice.color}`} />
                        <div>
                          <p className="text-slate-300 font-medium">{invoice.status}</p>
                          <p className="text-slate-400 text-xs">{invoice.count} invoices</p>
                        </div>
                      </div>
                      <p className="text-white font-bold">{invoice.amount}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Payment Summary */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Wallet className="w-5 h-5 mr-2 text-green-500" />
                  Payment Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 rounded-lg bg-slate-700">
                    <div>
                      <p className="text-slate-300 font-medium">Paid Invoices</p>
                      <p className="text-slate-400 text-xs">Current month</p>
                    </div>
                    <p className="text-green-400 font-bold">{mockFinancialOverview.paidInvoices}</p>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-slate-700">
                    <div>
                      <p className="text-slate-300 font-medium">Outstanding</p>
                      <p className="text-slate-400 text-xs">Pending payment</p>
                    </div>
                    <p className="text-yellow-400 font-bold">{mockFinancialOverview.outstandingInvoices}</p>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-green-500/10 border border-green-500/20">
                    <div>
                      <p className="text-green-300 font-medium">Collection Rate</p>
                      <p className="text-green-400 text-xs">This month</p>
                    </div>
                    <p className="text-green-400 font-bold">84.3%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Target className="w-5 h-5 mr-2 text-purple-500" />
                Top Customers by Revenue
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockTopCustomers.map((customer, index) => (
                  <div key={index} className="flex items-center justify-between p-4 rounded-lg bg-slate-700 hover:bg-slate-600 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <p className="text-white font-medium">{customer.name}</p>
                        <p className="text-slate-400 text-sm">{customer.orders} orders</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-orange-400 font-bold">{customer.revenue}</p>
                      <div className="flex items-center">
                        <ArrowUpRight className="w-3 h-3 text-green-500 mr-1" />
                        <span className="text-green-500 text-sm">{customer.growth}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-orange-500" />
                Product Revenue Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRevenueByProduct.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-slate-700">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center text-white text-sm font-bold">
                        {product.product.charAt(0)}
                      </div>
                      <div>
                        <p className="text-slate-300 font-medium">{product.product}</p>
                        <p className="text-slate-400 text-xs">{product.percentage}% of total revenue</p>
                      </div>
                    </div>
                    <p className="text-orange-400 font-bold">{product.revenue}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}