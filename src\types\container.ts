export type ContainerType = 'dry_container' | 'reefer' | 'open_top' | 'flat_rack'
export type ContainerSize = '20ft' | '40ft' | '40hc' | '45ft'
export type ContainerStatus = 'empty' | 'loaded' | 'in_transit' | 'delivered' | 'returned'

export interface Container {
  id: string
  shipment_id?: string
  container_number?: string
  container_type?: ContainerType
  container_size?: ContainerSize
  seal_number?: string
  tare_weight?: number
  gross_weight?: number
  volume?: number
  temperature?: string
  vent?: string
  status: ContainerStatus
  created_at: string
  updated_at: string
  // Confirmation fields
  container_number_confirmed?: boolean
  container_number_confirmed_by?: string | null
  container_number_confirmed_at?: string | null
  seal_number_confirmed?: boolean
  seal_number_confirmed_by?: string | null
  seal_number_confirmed_at?: string | null
}

export interface ContainerValidationResult {
  isValid: boolean
  errors: string[]
  checkDigitValid?: boolean
  formattedNumber?: string
}

export interface SealValidationResult {
  isValid: boolean
  detectedFormat: 'numeric' | 'alphanumeric' | 'iso' | 'unknown'
  errors: string[]
}

export interface ContainerUpdateData {
  container_number?: string
  seal_number?: string
  container_type?: ContainerType
  container_size?: ContainerSize
}

export interface OfflineContainerData {
  id: string
  container_id: string
  shipment_id: string
  container_data: Partial<Container>
  timestamp: number
  sync_status: 'pending' | 'syncing' | 'synced' | 'failed'
  validation_status: {
    container_number_valid: boolean
    seal_number_valid: boolean
    uniqueness_checked: boolean
  }
}

// New interfaces for confirmation system
export interface ContainerConfirmationRequest {
  confirm_container_number: boolean
  confirm_seal_number: boolean
  confirmation_notes?: string
}

export interface ContainerConfirmationStatus {
  container_number_confirmed: boolean
  container_number_confirmed_by: string | null
  container_number_confirmed_at: string | null
  seal_number_confirmed: boolean
  seal_number_confirmed_by: string | null
  seal_number_confirmed_at: string | null
}

export interface ContainerConfirmationOverride {
  container_number?: string
  seal_number?: string
  override_reason: string
}

export interface ContainerPermissions {
  canConfirm: boolean
  canEditAfterConfirmation: boolean
  canEditBeforeConfirmation: boolean
}