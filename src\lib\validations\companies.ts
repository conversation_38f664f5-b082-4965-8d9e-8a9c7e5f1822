import { z } from 'zod'

// Company type enums based on database schema
export const companyTypeSchema = z.enum([
  'customer',
  'carrier',
  'factory',
  'shipper',
  'consignee',
  'notify_party',
  'forwarder_agent',
])

export const customerTypeSchema = z.enum(['regular', 'premium', 'vip'])

export const incotermsSchema = z.enum(['FOB', 'CIF', 'EXW', 'CFR'])

// Coordinate validation schema (reused from ports)
export const coordinatesSchema = z.object({
  lat: z
    .number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90'),
  lng: z
    .number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180'),
})

// Bilingual text schema for Thai/English support (required version)
export const bilingualTextSchema = z
  .object({
    th: z.string().optional(),
    en: z.string().optional(),
  })
  .refine(data => data.th || data.en, {
    message: 'At least one language (Thai or English) must be provided',
  })

// Optional bilingual text schema for address fields
export const optionalBilingualTextSchema = z
  .object({
    th: z.string().optional(),
    en: z.string().optional(),
  })
  .optional()
  .nullable()

// Address schema with bilingual support and coordinates (all fields truly optional)
export const addressSchema = z
  .object({
    street: optionalBilingualTextSchema,
    city: optionalBilingualTextSchema,
    province: optionalBilingualTextSchema,
    postal_code: z.string().optional().nullable(),
    country: optionalBilingualTextSchema,
    coordinates: coordinatesSchema.optional(),
  })
  .optional()
  .nullable()

// Base company validation schema
export const companyBaseSchema = z.object({
  name: z
    .string()
    .min(2, 'Company name must be at least 2 characters')
    .max(100, 'Company name must be 100 characters or less'),
  company_type: companyTypeSchema,
  tax_id: z
    .string()
    .optional()
    .refine(
      val => !val || val.length >= 3,
      'Tax ID must be at least 3 characters if provided'
    ),
  contact_email: z
    .string()
    .email('Must be a valid email address')
    .optional()
    .or(z.literal('')),
  contact_phone: z
    .string()
    .optional()
    .refine(
      val => !val || /^[+]?[\d\s-()]+$/.test(val),
      'Phone number must contain only digits, spaces, hyphens, parentheses, and plus sign'
    ),
  contact_fax: z
    .string()
    .optional()
    .refine(
      val => !val || /^[+]?[\d\s-()]+$/.test(val),
      'Fax number must contain only digits, spaces, hyphens, parentheses, and plus sign'
    ),
  contact_person_first_name: z
    .string()
    .max(50, 'First name must be 50 characters or less')
    .optional(),
  contact_person_last_name: z
    .string()
    .max(50, 'Last name must be 50 characters or less')
    .optional(),
  address: addressSchema.optional(),
  gps_coordinates: coordinatesSchema.optional(),
  notes: z.string().optional(),
  is_active: z.boolean().default(true),
})

// Customer info validation schema
export const customerInfoSchema = z.object({
  customer_type: customerTypeSchema.default('regular'),
  credit_limit: z
    .number()
    .min(0, 'Credit limit must be non-negative')
    .optional()
    .default(0),
  incoterms: incotermsSchema.optional(),
  special_requirements: z.string().optional(),
})

// Carrier info validation schema
export const carrierInfoSchema = z.object({
  carrier_code: z
    .string()
    .max(20, 'Carrier code must be 20 characters or less')
    .optional(),
  fleet_size: z
    .number()
    .min(0, 'Fleet size must be non-negative')
    .optional()
    .default(0),
  license_types: z.array(z.string()).optional().default([]),
  coverage_areas: z.array(z.string()).optional().default([]),
  insurance_policy_no: z.string().optional(),
  insurance_expiry_date: z.string().optional(), // Date as string for form handling
  insurance_coverage_amount: z
    .number()
    .min(0, 'Insurance coverage amount must be non-negative')
    .optional(),
  max_weight_capacity: z
    .number()
    .min(0, 'Weight capacity must be non-negative')
    .optional(),
  max_volume_capacity: z
    .number()
    .min(0, 'Volume capacity must be non-negative')
    .optional(),
  operating_hours: z.record(z.string(), z.string()).optional(),
  emergency_contact_phone: z
    .string()
    .optional()
    .refine(
      val => !val || /^[+]?[\d\s-()]+$/.test(val),
      'Emergency contact phone must contain only digits, spaces, hyphens, parentheses, and plus sign'
    ),
  gps_tracking_available: z.boolean().optional().default(false),
})

// Factory info validation schema
export const factoryInfoSchema = z.object({
  factory_code: z
    .string()
    .min(1, 'Factory code is required')
    .max(20, 'Factory code must be 20 characters or less'),
  license_no: z
    .string()
    .min(1, 'License number is required')
    .max(50, 'License number must be 50 characters or less'),
  certifications: z.array(z.string()).optional().default([]),
  production_capacity_tons_per_day: z
    .number()
    .min(0, 'Production capacity must be non-negative')
    .optional()
    .default(0),
  cold_storage_capacity_tons: z
    .number()
    .min(0, 'Cold storage capacity must be non-negative')
    .optional()
    .default(0),
  operating_hours: z.record(z.string(), z.string()).optional(),
  specializations: z.array(z.string()).optional().default([]),
  quality_control_manager: z.string().optional(),
  quality_control_phone: z
    .string()
    .optional()
    .refine(
      val => !val || /^[+]?[\d\s-()]+$/.test(val),
      'Quality control phone must contain only digits, spaces, hyphens, parentheses, and plus sign'
    ),
  loading_dock_count: z
    .number()
    .min(1, 'Loading dock count must be at least 1')
    .optional()
    .default(1),
  container_loading_time_minutes: z
    .number()
    .min(1, 'Loading time must be at least 1 minute')
    .optional()
    .default(120),
  advance_booking_required_hours: z
    .number()
    .min(0, 'Booking hours must be non-negative')
    .optional()
    .default(24),
})

// Forwarder agent metadata validation schema
export const forwarderAgentMetadataSchema = z.object({
  services_provided: z
    .array(z.string())
    .min(1, 'At least one transportation service is required'),
  coverage_areas: z
    .array(z.string())
    .min(1, 'At least one coverage area is required'),
  operational_capacity: z.string().optional(),
  equipment_types: z.array(z.string()).optional().default([]),
  service_specializations: z.array(z.string()).optional().default([]),
  emergency_contact: z.string().optional(),
  emergency_contact_phone: z
    .string()
    .optional()
    .refine(
      val => !val || /^[+]?[\d\s-()]+$/.test(val),
      'Emergency contact phone must contain only digits, spaces, hyphens, parentheses, and plus sign'
    ),
  office_hours: z.string().optional(),
  response_time_hours: z
    .number()
    .min(0, 'Response time must be non-negative')
    .optional(),
  insurance_coverage: z.string().optional(),
  customs_license: z.string().optional(),
  years_of_experience: z
    .number()
    .min(0, 'Years of experience must be non-negative')
    .optional(),
  preferred_carriers: z.array(z.string()).optional().default([]),
  certifications: z.array(z.string()).optional().default([]),
})

// Complete company form schema with dynamic validation
export const companyFormSchema = z
  .object({
    ...companyBaseSchema.shape,
    customer_info: customerInfoSchema.optional(),
    carrier_info: carrierInfoSchema.optional(),
    factory_info: factoryInfoSchema.optional(),
    metadata: z.record(z.string(), z.any()).optional(), // For simple company types
  })
  .refine(
    data => {
      const {
        company_type,
        customer_info,
        carrier_info,
        factory_info,
        metadata,
      } = data

      // Complex types must have dedicated info, no metadata
      if (['customer', 'carrier', 'factory'].includes(company_type)) {
        if (metadata && Object.keys(metadata).length > 0) {
          return false // Complex types should not have metadata
        }

        // Check if appropriate info is provided
        if (company_type === 'customer' && !customer_info) return false
        if (company_type === 'carrier' && !carrier_info) return false
        if (company_type === 'factory' && !factory_info) return false
      }

      // Simple types should not have dedicated info tables
      if (
        ['shipper', 'consignee', 'notify_party', 'forwarder_agent'].includes(
          company_type
        )
      ) {
        if (customer_info || carrier_info || factory_info) {
          return false // Simple types should not have dedicated info
        }

        // Validate forwarder agent metadata
        if (company_type === 'forwarder_agent' && metadata) {
          try {
            forwarderAgentMetadataSchema.parse(metadata)
          } catch {
            return false // Invalid forwarder agent metadata
          }
        }
      }

      return true
    },
    {
      message: 'Invalid company type and information combination',
    }
  )

// Update schemas (all fields optional except id)
export const companyUpdateSchema = companyFormSchema.partial().extend({
  id: z.string().uuid('Invalid company ID'),
})

// Search and filter schemas
export const companyFilterSchema = z.object({
  company_type: companyTypeSchema.optional(),
  is_active: z.boolean().optional(),
  search: z.string().optional(),
})

// Bulk operations schemas
export const bulkDeleteCompaniesSchema = z.object({
  ids: z
    .array(z.string().uuid('Invalid company ID'))
    .min(1, 'At least one company must be selected'),
})

// GPS coordinate entry schema for manual input
export const gpsCoordinateInputSchema = z.object({
  latitude: z
    .string()
    .refine(val => {
      const num = parseFloat(val)
      return !isNaN(num) && num >= -90 && num <= 90
    }, 'Must be a valid latitude between -90 and 90')
    .transform(val => parseFloat(val)),
  longitude: z
    .string()
    .refine(val => {
      const num = parseFloat(val)
      return !isNaN(num) && num >= -180 && num <= 180
    }, 'Must be a valid longitude between -180 and 180')
    .transform(val => parseFloat(val)),
})

// Validation helpers
export const validateTaxId = (taxId: string, country?: string): boolean => {
  if (!taxId) return true // Optional field
  return taxId.length >= 3
}

export const validatePhoneNumber = (phone: string): boolean => {
  if (!phone) return true // Optional field
  return /^[+]?[\d\s-()]+$/.test(phone)
}

export const validateCoordinates = (lat: number, lng: number): boolean => {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180
}

// Type exports
export type CompanyForm = z.infer<typeof companyFormSchema>
export type CompanyUpdate = z.infer<typeof companyUpdateSchema>
export type CompanyFilter = z.infer<typeof companyFilterSchema>
export type CompanyType = z.infer<typeof companyTypeSchema>
export type CustomerType = z.infer<typeof customerTypeSchema>
export type Incoterms = z.infer<typeof incotermsSchema>
export type CustomerInfo = z.infer<typeof customerInfoSchema>
export type CarrierInfo = z.infer<typeof carrierInfoSchema>
export type FactoryInfo = z.infer<typeof factoryInfoSchema>
export type ForwarderAgentMetadata = z.infer<
  typeof forwarderAgentMetadataSchema
>
export type Coordinates = z.infer<typeof coordinatesSchema>
export type BilingualText = z.infer<typeof bilingualTextSchema>
export type OptionalBilingualText = z.infer<typeof optionalBilingualTextSchema>
export type Address = z.infer<typeof addressSchema>
export type GPSCoordinateInput = z.infer<typeof gpsCoordinateInputSchema>

// Constants for UI
export const COMPANY_TYPES = [
  { value: 'customer', label: 'Customer', complex: true },
  { value: 'carrier', label: 'Carrier', complex: true },
  { value: 'factory', label: 'Factory', complex: true },
  { value: 'shipper', label: 'Shipper', complex: false },
  { value: 'consignee', label: 'Consignee', complex: false },
  { value: 'notify_party', label: 'Notify Party', complex: false },
  { value: 'forwarder_agent', label: 'Forwarder Agent', complex: false },
] as const

export const CUSTOMER_TYPES = [
  { value: 'regular', label: 'Regular' },
  { value: 'premium', label: 'Premium' },
  { value: 'vip', label: 'VIP' },
] as const

export const INCOTERMS_OPTIONS = [
  { value: 'FOB', label: 'FOB (Free on Board)' },
  { value: 'CIF', label: 'CIF (Cost, Insurance & Freight)' },
  { value: 'EXW', label: 'EXW (Ex Works)' },
  { value: 'CFR', label: 'CFR (Cost & Freight)' },
] as const

export const LICENSE_TYPES = [
  'Commercial Transport',
  'Hazardous Materials',
  'Refrigerated Transport',
  'Container Transport',
  'Heavy Machinery',
  'International Transport',
] as const

export const COVERAGE_AREAS = [
  'Bangkok Metropolitan',
  'Central Region',
  'Northern Region',
  'Northeastern Region',
  'Eastern Region',
  'Western Region',
  'Southern Region',
  'International',
] as const

export const CERTIFICATIONS = [
  'HACCP',
  'ISO22000',
  'GMP',
  'BRC',
  'IFS',
  'Organic',
  'Halal',
  'FDA',
] as const

export const SPECIALIZATIONS = [
  'durian',
  'mangosteen',
  'longan',
  'rambutan',
  'coconut',
  'pineapple',
  'mango',
  'dragon_fruit',
  'frozen_fruits',
  'dried_fruits',
] as const

// Forwarder Agent specific constants
export const TRANSPORT_MODES = [
  'sea',
  'land',
  'rail',
  'air',
  'multimodal',
] as const

export const FORWARDER_COVERAGE_AREAS = [
  'Southeast Asia',
  'East Asia',
  'China',
  'Japan',
  'South Korea',
  'Thailand',
  'Vietnam',
  'Malaysia',
  'Singapore',
  'Indonesia',
  'Philippines',
  'Myanmar',
  'Cambodia',
  'Laos',
  'Hong Kong',
  'Taiwan',
  'Europe',
  'North America',
  'Australia',
  'Global',
] as const

export const SERVICE_SPECIALIZATIONS = [
  'container_shipping',
  'bulk_cargo',
  'refrigerated_transport',
  'hazardous_materials',
  'oversized_cargo',
  'express_delivery',
  'door_to_door',
  'customs_clearance',
  'warehousing',
  'consolidation',
  'documentation',
  'insurance_services',
] as const

export const EQUIPMENT_TYPES = [
  '20ft_container',
  '40ft_container',
  '40ft_hc_container',
  'refrigerated_container',
  'tank_container',
  'flat_rack',
  'open_top',
  'dry_bulk',
  'specialized_equipment',
] as const
