'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { createClient } from '@/lib/supabase/client'
import {
  adminUserUpdateSchema,
  type AdminUserUpdateFormData,
} from '@/lib/validations/auth'
import type { UserProfile } from '@/lib/supabase/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Switch } from '@/components/ui/switch'
import { Loader2, X } from 'lucide-react'

interface EditUserDialogProps {
  user: UserProfile | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onUserUpdated: () => void
}

interface Company {
  id: string
  name: string
  company_type: string
}

const roles = [
  { value: 'admin', label: 'Admin' },
  { value: 'cs', label: 'Customer Service' },
  { value: 'account', label: 'Account Manager' },
  { value: 'customer', label: 'Customer' },
  { value: 'carrier', label: 'Carrier' },
  { value: 'driver', label: 'Driver' },
  { value: 'factory', label: 'Factory' },
  { value: 'shipper', label: 'Shipper' },
  { value: 'consignee', label: 'Consignee' },
  { value: 'notify_party', label: 'Notify Party' },
  { value: 'forwarder_agent', label: 'Forwarder Agent' },
]

export function EditUserDialog({
  user,
  open,
  onOpenChange,
  onUserUpdated,
}: EditUserDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [companies, setCompanies] = useState<Company[]>([])
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  const form = useForm<AdminUserUpdateFormData>({
    resolver: zodResolver(adminUserUpdateSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      phoneNumber: '',
      lineId: '',
      wechatId: '',
      role: 'customer',
      companyId: '',
      isActive: true,
    },
  })

  const selectedRole = form.watch('role')

  // Get company type based on selected role
  const getCompanyTypeForRole = (role: string): string | null => {
    const roleCompanyMapping: Record<string, string> = {
      customer: 'customer',
      carrier: 'carrier',
      driver: 'carrier', // drivers work for carriers
      factory: 'factory',
      shipper: 'shipper',
      consignee: 'consignee',
      notify_party: 'notify_party',
      forwarder_agent: 'forwarder_agent',
    }
    return roleCompanyMapping[role] || null
  }

  // Clear company_id when role doesn't require company or when company type changes
  useEffect(() => {
    const staffRoles = ['admin', 'cs', 'account']
    if (selectedRole && staffRoles.includes(selectedRole)) {
      form.setValue('companyId', '')
    } else if (selectedRole) {
      // Clear company selection when switching to a role with different company type requirement
      const currentCompanyId = form.getValues('companyId')
      if (currentCompanyId) {
        const currentCompany = companies.find(c => c.id === currentCompanyId)
        const requiredCompanyType = getCompanyTypeForRole(selectedRole)
        if (
          currentCompany &&
          requiredCompanyType &&
          currentCompany.company_type !== requiredCompanyType
        ) {
          form.setValue('companyId', '')
        }
      }
    }
  }, [selectedRole, form, companies])

  useEffect(() => {
    if (open && user) {
      loadCompanies()
      // Reset form with user data
      const formData = {
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        phoneNumber: user.phone_number || '',
        lineId: user.line_id || '',
        wechatId: user.wechat_id || '',
        role: user.role,
        companyId: user.company_id || '',
        isActive: user.is_active,
      }

      form.reset(formData)

      // Additional approach: explicitly set the values after a microtask
      setTimeout(() => {
        form.setValue('role', user.role)
        if (user.company_id) {
          form.setValue('companyId', user.company_id)
        }
      }, 0)

      setError(null)
    }
  }, [open, user, form])

  async function loadCompanies() {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('id, name, company_type')
        .eq('is_active', true)
        .order('name')

      if (error) throw error
      setCompanies(data || [])
    } catch (err) {
      console.error('Error loading companies:', err)
    }
  }

  async function onSubmit(data: AdminUserUpdateFormData) {
    if (!user) return

    setIsSubmitting(true)
    setError(null)
    console.log('data', data)
    try {
      // Update profile record
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          first_name: data.firstName,
          last_name: data.lastName,
          phone_number: data.phoneNumber || null,
          line_id: data.lineId || null,
          wechat_id: data.wechatId || null,
          role: data.role,
          company_id: data.companyId || null,
          is_active: data.isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.user_id)

      if (profileError) throw profileError

      onOpenChange(false)
      onUserUpdated()
    } catch (err) {
      console.error('Error updating user:', err)
      setError(err instanceof Error ? err.message : 'Failed to update user')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!open || !user) return null

  const staffRoles = ['admin', 'cs', 'account']
  const requiresCompany = !staffRoles.includes(selectedRole)

  // Filter companies based on selected role
  const filteredCompanies = companies.filter(company => {
    const requiredCompanyType = getCompanyTypeForRole(selectedRole)
    return requiredCompanyType
      ? company.company_type === requiredCompanyType
      : true
  })

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-slate-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Edit User</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="text-slate-400 hover:text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* User Info */}
        <div className="bg-slate-700 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {(user.first_name?.[0] || user.email[0]).toUpperCase()}
              </span>
            </div>
            <div>
              <p className="text-white font-medium">{user.email}</p>
              <p className="text-slate-400 text-sm">User ID: {user.user_id}</p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4">
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        )}

        {/* Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* First Name */}
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">First Name *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="John"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Last Name */}
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Last Name *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Doe"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Phone Number */}
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Phone Number</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="+****************"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Line ID */}
            <FormField
              control={form.control}
              name="lineId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Line ID</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="@username or line_id"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* WeChat ID */}
            <FormField
              control={form.control}
              name="wechatId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">WeChat ID</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="wechat_username"
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Role */}
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-white">Role *</FormLabel>
                  <Select
                    key={`role-${user?.user_id}`}
                    onValueChange={field.onChange}
                    value={field.value || user?.role || ''}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-slate-800 border-slate-700">
                      {roles.map(role => (
                        <SelectItem
                          key={role.value}
                          value={role.value}
                          className="text-slate-300 hover:bg-slate-700"
                        >
                          {role.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-red-300" />
                </FormItem>
              )}
            />

            {/* Company (conditional) */}
            {requiresCompany && (
              <FormField
                control={form.control}
                name="companyId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Company *</FormLabel>
                    <Select
                      key={`company-${user?.user_id}`}
                      onValueChange={field.onChange}
                      value={field.value || user?.company_id || ''}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select company" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700">
                        {filteredCompanies.map(company => (
                          <SelectItem
                            key={company.id}
                            value={company.id}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            {company.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage className="text-red-300" />
                  </FormItem>
                )}
              />
            )}

            {/* Active Status */}
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4 bg-slate-700/30">
                  <div className="space-y-0.5">
                    <FormLabel className="text-white font-medium">
                      Account Status
                    </FormLabel>
                    <div className="text-sm font-medium">
                      <span
                        className={
                          field.value ? 'text-green-400' : 'text-red-400'
                        }
                      >
                        {field.value
                          ? 'Active - User can access the system'
                          : 'Disabled - User access is blocked'}
                      </span>
                    </div>
                  </div>
                  <FormControl>
                    <div className="flex items-center space-x-2">
                      <span
                        className={`text-xs font-medium ${field.value ? 'text-green-400' : 'text-red-400'}`}
                      >
                        {field.value ? 'ON' : 'OFF'}
                      </span>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isSubmitting}
                        className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500"
                      />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Actions */}
            <div className="flex space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
              >
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Update User
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}
