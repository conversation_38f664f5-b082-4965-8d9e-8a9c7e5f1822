import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/supabase/types'
import type {
  DocumentTemplate,
  DocumentTemplateInsert,
  DocumentTemplateUpdate,
  DocumentTemplateFilters,
  DocumentTemplateSortConfig,
  DocumentTemplateQueryResult,
  DocumentType
} from '@/types/document-template'

export interface DocumentTemplateQueryOptions {
  filters: DocumentTemplateFilters
  sort: DocumentTemplateSortConfig
  pagination: {
    page: number
    pageSize: number
  }
}

/**
 * Service class for document template database operations
 * Handles template CRUD operations, version management, and access control
 * Story 5.1: Document Template Management System
 */
export class DocumentTemplateService {
  constructor(private supabase: SupabaseClient<Database>) {}

  /**
   * Get document templates with filtering, sorting, and pagination
   */
  async getTemplates(options: DocumentTemplateQueryOptions): Promise<DocumentTemplateQueryResult> {
    try {
      const { filters, sort, pagination } = options
      
      // Build base query
      let query = this.supabase
        .from('document_templates')
        .select(`
          id,
          template_name,
          document_type,
          version,
          template_content,
          template_data,
          template_styles,
          page_size,
          page_orientation,
          margin_top,
          margin_bottom,
          margin_left,
          margin_right,
          language,
          currency_format,
          date_format,
          number_format,
          description,
          usage_notes,
          required_fields,
          is_active,
          is_default,
          created_by,
          created_at,
          updated_at
        `)

      // Apply search filter
      if (filters.search?.trim()) {
        const searchTerm = `%${filters.search.trim()}%`
        query = query.or(`template_name.ilike.${searchTerm},description.ilike.${searchTerm}`)
      }

      // Apply document type filter (indexed column)
      if (filters.document_type && filters.document_type.length > 0) {
        query = query.in('document_type', filters.document_type)
      }

      // Apply active status filter (indexed column)
      if (filters.is_active !== undefined) {
        query = query.eq('is_active', filters.is_active)
      }

      // Apply default status filter (indexed column)
      if (filters.is_default !== undefined) {
        query = query.eq('is_default', filters.is_default)
      }

      // Apply created_by filter
      if (filters.created_by) {
        query = query.eq('created_by', filters.created_by)
      }

      // Apply language filter
      if (filters.language) {
        query = query.eq('language', filters.language)
      }

      // Apply sorting
      query = query.order(sort.field, { ascending: sort.direction === 'asc' })

      // Get total count for pagination (separate optimized query)
      const countQuery = this.buildCountQuery(filters)
      
      // Apply pagination
      const from = (pagination.page - 1) * pagination.pageSize
      const to = from + pagination.pageSize - 1
      query = query.range(from, to)

      // Execute queries in parallel
      const [dataResult, countResult] = await Promise.all([
        query,
        countQuery
      ])

      if (dataResult.error) {
        throw new Error(`Query failed: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Count query failed: ${countResult.error.message}`)
      }

      return {
        data: dataResult.data as DocumentTemplate[],
        totalCount: countResult.count || 0,
      }

    } catch (error: any) {
      console.error('DocumentTemplateService.getTemplates error:', error)
      return {
        data: [],
        totalCount: 0,
        error: error.message || 'Failed to fetch templates',
      }
    }
  }

  /**
   * Build optimized count query with same filters
   */
  private buildCountQuery(filters: DocumentTemplateFilters) {
    let query = this.supabase
      .from('document_templates')
      .select('id', { count: 'exact', head: true })

    // Apply the same filters to count query for accuracy
    if (filters.search?.trim()) {
      const searchTerm = `%${filters.search.trim()}%`
      query = query.or(`template_name.ilike.${searchTerm},description.ilike.${searchTerm}`)
    }

    if (filters.document_type && filters.document_type.length > 0) {
      query = query.in('document_type', filters.document_type)
    }

    if (filters.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active)
    }

    if (filters.is_default !== undefined) {
      query = query.eq('is_default', filters.is_default)
    }

    if (filters.created_by) {
      query = query.eq('created_by', filters.created_by)
    }

    if (filters.language) {
      query = query.eq('language', filters.language)
    }

    return query
  }

  /**
   * Get single template by ID
   */
  async getTemplateById(id: string): Promise<DocumentTemplate | null> {
    try {
      const { data, error } = await this.supabase
        .from('document_templates')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching template:', error)
        return null
      }

      return data as DocumentTemplate
    } catch (error) {
      console.error('DocumentTemplateService.getTemplateById error:', error)
      return null
    }
  }

  /**
   * Create new document template
   */
  async createTemplate(template: DocumentTemplateInsert, userId: string): Promise<{ data: DocumentTemplate | null; error: string | null }> {
    try {
      // Check for duplicate template name
      const { data: existing } = await this.supabase
        .from('document_templates')
        .select('id, template_name')
        .eq('template_name', template.template_name)
        .single()

      if (existing) {
        return { data: null, error: 'Template name already exists' }
      }

      // If this is set as default, unset other defaults for the same document type
      if (template.is_default) {
        await this.unsetDefaultTemplates(template.document_type)
      }

      const { data, error } = await this.supabase
        .from('document_templates')
        .insert({
          ...template,
          created_by: userId,
          version: template.version || '1.0',
          page_size: template.page_size || 'A4',
          page_orientation: template.page_orientation || 'portrait',
          margin_top: template.margin_top || 20,
          margin_bottom: template.margin_bottom || 20,
          margin_left: template.margin_left || 20,
          margin_right: template.margin_right || 20,
          language: template.language || 'en',
          currency_format: template.currency_format || 'USD',
          date_format: template.date_format || 'YYYY-MM-DD',
          number_format: template.number_format || 'en-US',
          is_active: template.is_active !== undefined ? template.is_active : true,
          is_default: template.is_default || false,
        })
        .select()
        .single()

      if (error) throw error

      return { data: data as DocumentTemplate, error: null }
    } catch (error: any) {
      console.error('DocumentTemplateService.createTemplate error:', error)
      return { data: null, error: error.message }
    }
  }

  /**
   * Update existing template
   */
  async updateTemplate(id: string, updates: DocumentTemplateUpdate): Promise<{ data: DocumentTemplate | null; error: string | null }> {
    try {
      // If template name is being updated, check for duplicates
      if (updates.template_name) {
        const { data: existing } = await this.supabase
          .from('document_templates')
          .select('id, template_name')
          .eq('template_name', updates.template_name)
          .neq('id', id)
          .single()

        if (existing) {
          return { data: null, error: 'Template name already exists' }
        }
      }

      // If this is being set as default, unset other defaults for the same document type
      if (updates.is_default && updates.document_type) {
        await this.unsetDefaultTemplates(updates.document_type)
      }

      const { data, error } = await this.supabase
        .from('document_templates')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      return { data: data as DocumentTemplate, error: null }
    } catch (error: any) {
      console.error('DocumentTemplateService.updateTemplate error:', error)
      return { data: null, error: error.message }
    }
  }

  /**
   * Delete template (only if not referenced by documents)
   */
  async deleteTemplate(id: string): Promise<{ success: boolean; error: string | null }> {
    try {
      // Check if template is referenced by any documents
      const { data: documents } = await this.supabase
        .from('documents')
        .select('id')
        .eq('template_id', id)
        .limit(1)

      if (documents && documents.length > 0) {
        return { success: false, error: 'Cannot delete template that is referenced by existing documents' }
      }

      const { error } = await this.supabase
        .from('document_templates')
        .delete()
        .eq('id', id)

      if (error) throw error

      return { success: true, error: null }
    } catch (error: any) {
      console.error('DocumentTemplateService.deleteTemplate error:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Get default template for a document type
   */
  async getDefaultTemplate(documentType: DocumentType): Promise<DocumentTemplate | null> {
    try {
      const { data, error } = await this.supabase
        .from('document_templates')
        .select('*')
        .eq('document_type', documentType)
        .eq('is_default', true)
        .eq('is_active', true)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error('Error fetching default template:', error)
        return null
      }

      return data as DocumentTemplate
    } catch (error) {
      console.error('DocumentTemplateService.getDefaultTemplate error:', error)
      return null
    }
  }

  /**
   * Set template as default for its document type
   */
  async setDefaultTemplate(id: string): Promise<{ success: boolean; error: string | null }> {
    try {
      // Get the template to know its document type
      const template = await this.getTemplateById(id)
      if (!template) {
        return { success: false, error: 'Template not found' }
      }

      // Unset current default for this document type
      await this.unsetDefaultTemplates(template.document_type)

      // Set this template as default
      const { error } = await this.supabase
        .from('document_templates')
        .update({
          is_default: true,
          is_active: true, // Ensure it's active when set as default
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)

      if (error) throw error

      return { success: true, error: null }
    } catch (error: any) {
      console.error('DocumentTemplateService.setDefaultTemplate error:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Unset all default templates for a document type
   */
  private async unsetDefaultTemplates(documentType: DocumentType) {
    try {
      await this.supabase
        .from('document_templates')
        .update({
          is_default: false,
          updated_at: new Date().toISOString(),
        })
        .eq('document_type', documentType)
        .eq('is_default', true)
    } catch (error) {
      console.error('Error unsetting default templates:', error)
      // Don't throw here as this is a helper function
    }
  }

  /**
   * Activate/deactivate template
   */
  async toggleTemplateStatus(id: string, isActive: boolean): Promise<{ success: boolean; error: string | null }> {
    try {
      const { error } = await this.supabase
        .from('document_templates')
        .update({
          is_active: isActive,
          // If deactivating and it was default, unset default
          ...((!isActive) && { is_default: false }),
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)

      if (error) throw error

      return { success: true, error: null }
    } catch (error: any) {
      console.error('DocumentTemplateService.toggleTemplateStatus error:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Clone template with new version
   */
  async cloneTemplate(id: string, newVersion: string, userId: string): Promise<{ data: DocumentTemplate | null; error: string | null }> {
    try {
      const original = await this.getTemplateById(id)
      if (!original) {
        return { data: null, error: 'Original template not found' }
      }

      // Create new template name with version suffix
      const newTemplateName = `${original.template_name} (v${newVersion})`

      const clonedTemplate: DocumentTemplateInsert = {
        template_name: newTemplateName,
        document_type: original.document_type,
        version: newVersion,
        template_content: original.template_content,
        template_data: original.template_data,
        template_styles: original.template_styles,
        page_size: original.page_size,
        page_orientation: original.page_orientation,
        margin_top: original.margin_top,
        margin_bottom: original.margin_bottom,
        margin_left: original.margin_left,
        margin_right: original.margin_right,
        language: original.language,
        currency_format: original.currency_format,
        date_format: original.date_format,
        number_format: original.number_format,
        description: original.description,
        usage_notes: original.usage_notes,
        required_fields: original.required_fields,
        is_active: false, // Clone starts as inactive
        is_default: false, // Clone cannot be default initially
      }

      return await this.createTemplate(clonedTemplate, userId)
    } catch (error: any) {
      console.error('DocumentTemplateService.cloneTemplate error:', error)
      return { data: null, error: error.message }
    }
  }

  /**
   * Get template versions (templates with same base name)
   */
  async getTemplateVersions(templateName: string): Promise<DocumentTemplate[]> {
    try {
      const { data, error } = await this.supabase
        .from('document_templates')
        .select('*')
        .or(`template_name.eq.${templateName},template_name.like.${templateName} (v%)`)
        .order('created_at', { ascending: false })

      if (error) throw error

      return data as DocumentTemplate[]
    } catch (error) {
      console.error('DocumentTemplateService.getTemplateVersions error:', error)
      return []
    }
  }

  /**
   * Subscribe to template changes
   */
  subscribeToTemplateUpdates(callback: (payload: any) => void, filters?: DocumentTemplateFilters) {
    const filteredCallback = (payload: any) => {
      const { new: newRecord, old: oldRecord } = payload
      
      if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
        const recordToCheck = newRecord || oldRecord
        
        // Apply document type filter if provided
        if (filters?.document_type && filters.document_type.length > 0) {
          if (!recordToCheck.document_type || !filters.document_type.includes(recordToCheck.document_type)) {
            return
          }
        }
        
        // Apply active status filter if provided
        if (filters?.is_active !== undefined) {
          if (recordToCheck.is_active !== filters.is_active) {
            return
          }
        }
      }
      
      callback(payload)
    }

    const subscription = this.supabase
      .channel('template-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'document_templates',
        },
        filteredCallback
      )

    return subscription.subscribe()
  }
}

// Singleton instance
let documentTemplateService: DocumentTemplateService | null = null

/**
 * Get document template service instance
 */
export const getDocumentTemplateService = (supabase: SupabaseClient<Database>) => {
  if (!documentTemplateService) {
    documentTemplateService = new DocumentTemplateService(supabase)
  }
  return documentTemplateService
}