'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Package,
  Eye,
  X,
  Calendar,
  Globe,
  Navigation,
} from 'lucide-react'
import { ProductForm } from '@/components/forms/product-form/product-form'
import { useProductsManagement } from '@/hooks/use-products'
import { useUnitsData } from '@/hooks/use-units'
import type { Product } from '@/lib/supabase/types'
import type { ProductForm as ProductFormData } from '@/lib/validations/products'
import { formatDistanceToNow, format } from 'date-fns'

export default function ProductsPage() {
  const {
    // Data
    products,
    loading,
    error,
    categories,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedProducts,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createProduct,
    updateProduct,
    deleteProduct,
    bulkDeleteProducts,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    toggleProduct,
    toggleAll,
    clearSelection,
    clearError,
    refreshProducts,
  } = useProductsManagement()

  const { activeUnits } = useUnitsData()

  // UI state
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null)
  const [deletingProduct, setDeletingProduct] = useState<Product | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // Handle create product
  const handleCreate = async (data: ProductFormData) => {
    try {
      await createProduct(data)
      setShowCreateDialog(false)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle update product
  const handleUpdate = async (data: ProductFormData) => {
    if (!editingProduct) return

    try {
      await updateProduct(editingProduct.id, data)
      setEditingProduct(null)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle delete product
  const handleDelete = async (product: Product) => {
    try {
      await deleteProduct(product.id)
      setDeletingProduct(null)
    } catch (error) {
      // Error handling is done in the store
      console.error('Delete failed:', error)
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      await bulkDeleteProducts(selectedProducts)
      setShowBulkDeleteDialog(false)
      clearSelection()
    } catch (error) {
      // Error handling is done in the store
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle sorting
  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSorting(column, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSorting(column, 'asc')
    }
  }

  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  // Get unit name for display
  const getUnitName = (unitId: string | null) => {
    if (!unitId) return 'No unit'
    const unit = activeUnits.find(u => u.id === unitId)
    return unit ? `${unit.name} (${unit.symbol})` : 'Unknown unit'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Products</h1>
          <p className="text-slate-400 mt-1">
            Manage product catalog and specifications
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">Create Product</DialogTitle>
              <DialogDescription className="text-slate-400">
                Add a new product to your catalog
              </DialogDescription>
            </DialogHeader>
            <ProductForm
              onSubmit={handleCreate}
              onCancel={() => setShowCreateDialog(false)}
              isLoading={isCreating}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Search */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {(searchTerm || filter.category || filter.unit_of_measure_id) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFilter({})
                setSearchTerm('')
              }}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Search Products
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search by name or code..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Category
            </label>
            <Select
              value={filter.category || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  category: value === 'all' ? undefined : value,
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Categories
                </SelectItem>
                {categories.map(category => (
                  <SelectItem
                    key={category}
                    value={category}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Unit Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Unit of Measure
            </label>
            <Select
              value={filter.unit_of_measure_id || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  unit_of_measure_id: value === 'all' ? undefined : value,
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Units" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Units
                </SelectItem>
                {activeUnits.map(unit => (
                  <SelectItem
                    key={unit.id}
                    value={unit.id}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {unit.name} ({unit.symbol})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {(searchTerm || filter.category || filter.unit_of_measure_id) && (
          <div className="flex flex-wrap gap-2 pt-2">
            {searchTerm && (
              <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-2 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.category && (
              <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                Category: {filter.category}
                <button
                  onClick={() => setFilter({ ...filter, category: undefined })}
                  className="ml-2 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.unit_of_measure_id && (
              <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
                Unit:{' '}
                {activeUnits.find(u => u.id === filter.unit_of_measure_id)
                  ?.name || 'Unknown'}
                <button
                  onClick={() =>
                    setFilter({ ...filter, unit_of_measure_id: undefined })
                  }
                  className="ml-2 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedCount} product{selectedCount !== 1 ? 's' : ''} selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowBulkDeleteDialog(true)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Products Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Package className="h-5 w-5 text-orange-500" />
              Products ({totalCount})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshProducts}
              disabled={loading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {loading && products.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-slate-300">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              <span className="ml-2">Loading products...</span>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No products found</p>
              <p className="text-sm text-slate-400 mt-1">
                Create your first product to get started
              </p>
            </div>
          ) : (
            <>
              <Table className="bg-slate-800">
                <TableHeader className="bg-slate-700">
                  <TableRow className="border-slate-600 hover:bg-slate-700">
                    <TableHead className="w-12 text-slate-200">
                      <Checkbox
                        checked={isAllSelected || isPartiallySelected}
                        onCheckedChange={toggleAll}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('name')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Name {getSortIcon('name')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('code')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Code {getSortIcon('code')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('category')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Category {getSortIcon('category')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      Unit of Measure
                    </TableHead>
                    <TableHead className="text-slate-200">HS Code</TableHead>
                    <TableHead className="text-slate-200">Status</TableHead>
                    <TableHead className="w-32 text-slate-200">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-slate-800">
                  {products.map(product => (
                    <TableRow
                      key={product.id}
                      className="border-slate-600 hover:bg-slate-700 text-slate-200"
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected(product.id)}
                          onCheckedChange={() => toggleProduct(product.id)}
                          className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                      </TableCell>
                      <TableCell className="font-medium">
                        {product.name}
                      </TableCell>
                      <TableCell>
                        {product.code || (
                          <span className="text-slate-400">No code</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {product.category ? (
                          <Badge
                            variant="outline"
                            className="border-slate-400 text-slate-200 bg-slate-700 hover:bg-slate-600"
                          >
                            {product.category}
                          </Badge>
                        ) : (
                          <span className="text-slate-400">No category</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {getUnitName(product.unit_of_measure_id)}
                        </span>
                      </TableCell>
                      <TableCell>
                        {product.hs_code || (
                          <span className="text-slate-400">No HS code</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={product.is_active ? 'default' : 'secondary'}
                          className={
                            product.is_active
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                          }
                        >
                          {product.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setViewingProduct(product)}
                            className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingProduct(product)}
                            className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingProduct(product)}
                            className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-6 pb-6">
                  <div className="text-sm text-slate-300">
                    Page {currentPage} of {totalPages} ({totalCount} products)
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(1)}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={previousPage}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* View Product Dialog */}
      {viewingProduct && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-slate-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">
                Product Details
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewingProduct(null)}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Product Avatar and Basic Info */}
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl font-medium">
                    {viewingProduct.name[0].toUpperCase()}
                  </span>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white">
                    {viewingProduct.name}
                  </h3>
                  <p className="text-slate-400">
                    {viewingProduct.code || 'No code assigned'}
                  </p>
                  <div className="flex items-center space-x-2 mt-2">
                    {viewingProduct.category && (
                      <Badge
                        variant="outline"
                        className="border-slate-400 text-slate-200 bg-slate-700 hover:bg-slate-600"
                      >
                        <Package className="h-3 w-3 mr-1" />
                        {viewingProduct.category}
                      </Badge>
                    )}
                    <div className="flex items-center space-x-1">
                      <div
                        className={`w-2 h-2 rounded-full ${viewingProduct.is_active ? 'bg-green-400' : 'bg-red-400'}`}
                      />
                      <span
                        className={`text-sm ${viewingProduct.is_active ? 'text-green-300' : 'text-red-300'}`}
                      >
                        {viewingProduct.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              {viewingProduct.description && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Edit className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Description</span>
                  </div>
                  <p className="text-slate-300 text-sm">
                    {viewingProduct.description}
                  </p>
                </div>
              )}

              {/* Product Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Package className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Product Code</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingProduct.code || 'No code assigned'}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Globe className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">HS Code</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingProduct.hs_code || 'No HS code'}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Navigation className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">
                      Unit of Measure
                    </span>
                  </div>
                  <p className="text-slate-300">
                    {getUnitName(viewingProduct.unit_of_measure_id)}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Filter className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Category</span>
                  </div>
                  <p className="text-slate-300">
                    {viewingProduct.category || 'No category'}
                  </p>
                </div>
              </div>

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Created</span>
                  </div>
                  <p className="text-slate-300">
                    {format(new Date(viewingProduct.created_at!), 'PPP')}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {formatDistanceToNow(new Date(viewingProduct.created_at!), {
                      addSuffix: true,
                    })}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Edit className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Last Updated</span>
                  </div>
                  <p className="text-slate-300">
                    {format(new Date(viewingProduct.updated_at!), 'PPP')}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {formatDistanceToNow(new Date(viewingProduct.updated_at!), {
                      addSuffix: true,
                    })}
                  </p>
                </div>
              </div>

              {/* Product ID */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Package className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Product ID</span>
                </div>
                <p className="text-slate-300 font-mono text-sm">
                  {viewingProduct.id}
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => setViewingProduct(null)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Product Dialog */}
      <Dialog
        open={!!editingProduct}
        onOpenChange={() => setEditingProduct(null)}
      >
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">Edit Product</DialogTitle>
            <DialogDescription className="text-slate-400">
              Update product information and specifications
            </DialogDescription>
          </DialogHeader>
          {editingProduct && (
            <ProductForm
              product={editingProduct}
              onSubmit={handleUpdate}
              onCancel={() => setEditingProduct(null)}
              isLoading={isUpdating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Product Dialog */}
      <AlertDialog
        open={!!deletingProduct}
        onOpenChange={() => setDeletingProduct(null)}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Product
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete &quot;{deletingProduct?.name}
              &quot;? This action cannot be undone.
              {deletingProduct && (
                <div className="mt-2 p-2 bg-slate-700 rounded text-sm border border-slate-600">
                  <strong className="text-white">Name:</strong>{' '}
                  <span className="text-slate-200">{deletingProduct.name}</span>
                  <br />
                  {deletingProduct.code && (
                    <>
                      <strong className="text-white">Code:</strong>{' '}
                      <span className="text-slate-200">
                        {deletingProduct.code}
                      </span>
                      <br />
                    </>
                  )}
                  <strong className="text-white">Category:</strong>{' '}
                  <span className="text-slate-200">
                    {deletingProduct.category || 'No category'}
                  </span>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setDeletingProduct(null)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deletingProduct && handleDelete(deletingProduct)}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Product
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Selected Products
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete {selectedCount} selected product
              {selectedCount !== 1 ? 's' : ''}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowBulkDeleteDialog(false)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Products
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
