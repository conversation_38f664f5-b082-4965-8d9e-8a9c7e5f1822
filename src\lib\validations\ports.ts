import { z } from 'zod'

// Port type enum
export const portTypeSchema = z.enum(['origin', 'destination', 'transit'])

// Coordinate validation schema
export const coordinatesSchema = z.object({
  lat: z
    .number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90'),
  lng: z
    .number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180'),
})

// Bilingual text schema for Thai/English support
export const bilingualTextSchema = z
  .object({
    th: z.string().optional(),
    en: z.string().optional(),
  })
  .refine(data => data.th || data.en, {
    message: 'At least one language (Thai or English) must be provided',
  })

// Address schema with bilingual support and coordinates
export const addressSchema = z.object({
  street: bilingualTextSchema.optional(),
  city: bilingualTextSchema.optional(),
  province: bilingualTextSchema.optional(),
  postal_code: z.string().optional(),
  country: bilingualTextSchema.optional(),
  coordinates: coordinatesSchema.optional(),
})

// Port validation schema
export const portSchema = z.object({
  code: z
    .string()
    .min(3, 'Port code must be at least 3 characters')
    .max(10, 'Port code must be 10 characters or less')
    .transform(val => val.toUpperCase())
    .refine(
      val => /^[A-Z]{2}[A-Z0-9]+$/.test(val),
      'Port code must start with 2 uppercase letters followed by letters/numbers (e.g., THBKK, CNSHA)'
    ),
  name: z
    .string()
    .min(2, 'Port name must be at least 2 characters')
    .max(100, 'Port name must be 100 characters or less'),
  city: z
    .string()
    .min(1, 'City is required')
    .max(100, 'City must be 100 characters or less'),
  country: z
    .string()
    .min(2, 'Country is required')
    .max(100, 'Country must be 100 characters or less'),
  port_type: portTypeSchema,
  gps_coordinates: coordinatesSchema.optional(),
  address: addressSchema.optional(),
  timezone: z
    .string()
    .regex(
      /^[A-Za-z]+\/[A-Za-z_]+$/,
      'Timezone must be in format: Continent/City (e.g., Asia/Bangkok)'
    )
    .optional(),
  is_active: z.boolean().default(true),
})

// Geographic search schema
export const geographicSearchSchema = z.object({
  center_lat: z
    .number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90'),
  center_lng: z
    .number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180'),
  radius_km: z
    .number()
    .min(1, 'Radius must be at least 1 km')
    .max(20000, 'Radius cannot exceed 20,000 km')
    .default(100),
})

// Form schemas (without database-generated fields)
export const portFormSchema = portSchema.omit({
  is_active: true,
})

// Update schemas (all fields optional except id)
export const portUpdateSchema = portSchema.partial().extend({
  id: z.string().uuid('Invalid port ID'),
})

// Bulk operations schemas
export const bulkDeletePortsSchema = z.object({
  ids: z
    .array(z.string().uuid('Invalid port ID'))
    .min(1, 'At least one port must be selected'),
})

// Search and filter schemas
export const portFilterSchema = z.object({
  port_type: portTypeSchema.optional(),
  country: z.string().optional(),
  is_active: z.boolean().optional(),
  search: z.string().optional(),
})

// GPS coordinate entry schema for manual input
export const gpsCoordinateInputSchema = z.object({
  latitude: z
    .string()
    .refine(val => {
      const num = parseFloat(val)
      return !isNaN(num) && num >= -90 && num <= 90
    }, 'Must be a valid latitude between -90 and 90')
    .transform(val => parseFloat(val)),
  longitude: z
    .string()
    .refine(val => {
      const num = parseFloat(val)
      return !isNaN(num) && num >= -180 && num <= 180
    }, 'Must be a valid longitude between -180 and 180')
    .transform(val => parseFloat(val)),
})

// Distance calculation schema
export const distanceCalculationSchema = z.object({
  port1_id: z.string().uuid('Invalid first port ID'),
  port2_id: z.string().uuid('Invalid second port ID'),
})

// Validation helpers
export const validatePortCode = (code: string): boolean => {
  return /^[A-Z]{2}[A-Z0-9]+$/.test(code)
}

export const validateCoordinates = (lat: number, lng: number): boolean => {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180
}

export const formatPortCode = (code: string): string => {
  return code.toUpperCase().trim()
}

// Type exports
export type PortForm = z.infer<typeof portFormSchema>
export type PortUpdate = z.infer<typeof portUpdateSchema>
export type PortFilter = z.infer<typeof portFilterSchema>
export type PortType = z.infer<typeof portTypeSchema>
export type Coordinates = z.infer<typeof coordinatesSchema>
export type BilingualText = z.infer<typeof bilingualTextSchema>
export type Address = z.infer<typeof addressSchema>
export type GeographicSearch = z.infer<typeof geographicSearchSchema>
export type GPSCoordinateInput = z.infer<typeof gpsCoordinateInputSchema>
export type DistanceCalculation = z.infer<typeof distanceCalculationSchema>
