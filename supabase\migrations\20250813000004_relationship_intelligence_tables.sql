-- Relationship Intelligence Tables
-- Story 1.2: Database Schema & RLS Foundation
-- This migration creates relationship intelligence tables for smart pre-population

-- ============================================================================
-- CUSTOMER SHIPPERS RELATIONSHIP TABLE
-- ============================================================================

-- Customer-shipper relationships for intelligent pre-population
CREATE TABLE customer_shippers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    shipper_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Ensure customer is of type customer and shipper is of type shipper
    CONSTRAINT customer_shippers_customer_type_check 
        CHECK (EXISTS (SELECT 1 FROM companies WHERE id = customer_id AND company_type = 'customer')),
    CONSTRAINT customer_shippers_shipper_type_check 
        CHECK (EXISTS (SELECT 1 FROM companies WHERE id = shipper_id AND company_type = 'shipper')),
    
    -- Unique constraint to prevent duplicate relationships
    UNIQUE(customer_id, shipper_id)
);

-- ============================================================================
-- CONSIGNEE NOTIFY PARTIES RELATIONSHIP TABLE
-- ============================================================================

-- Consignee-notify party relationships for intelligent pre-population
CREATE TABLE consignee_notify_parties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    consignee_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    notify_party_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    -- Notification preferences for this relationship
    notification_preferences JSONB, -- {"email": true, "sms": false, "line": true}
    priority_order INTEGER DEFAULT 1, -- Order of notification (1 = primary, 2 = secondary, etc.)
    
    -- Special handling instructions
    special_instructions TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Ensure consignee is of type consignee and notify_party is of type notify_party
    CONSTRAINT consignee_notify_parties_consignee_type_check 
        CHECK (EXISTS (SELECT 1 FROM companies WHERE id = consignee_id AND company_type = 'consignee')),
    CONSTRAINT consignee_notify_parties_notify_party_type_check 
        CHECK (EXISTS (SELECT 1 FROM companies WHERE id = notify_party_id AND company_type = 'notify_party')),
    
    -- Unique constraint to prevent duplicate relationships
    UNIQUE(consignee_id, notify_party_id)
);

-- ============================================================================
-- CUSTOMER PRODUCTS RELATIONSHIP TABLE
-- ============================================================================

-- Customer-product relationships with pricing and packaging specifications
CREATE TABLE customer_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    
    -- Product-specific details for this customer
    customer_product_code TEXT, -- Customer's internal product code
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    -- Pricing information (per KG as base unit)
    unit_price_cif NUMERIC(12,4), -- Cost, Insurance, and Freight price per KG
    unit_price_fob NUMERIC(12,4), -- Free on Board price per KG
    currency_code currency_enum DEFAULT 'USD',
    
    -- Physical specifications and packaging
    standard_quantity NUMERIC(10,2), -- Standard order quantity (number of packages)
    unit_of_measure_id UUID REFERENCES units_of_measure(id), -- Base UOM is KG
    gross_weight_per_package NUMERIC(8,4), -- Gross weight per 1 package in KG
    net_weight_per_package NUMERIC(8,4), -- Net weight per 1 package in KG
    
    -- Quality and packaging
    quality_grade TEXT, -- Premium, Grade A, Standard, etc.
    packaging_type packaging_type_enum NOT NULL, -- Type of packaging (box, bag, carton, pallet, etc.) - defines what quantity represents
    packaging_specifications JSONB, -- Flexible storage for packaging details
    
    -- Logistics information
    handling_instructions TEXT,
    temperature_require TEXT,
    vent_require TEXT,
    shelf_life_days INTEGER, -- Expected shelf life in days
    
    -- Documentation
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Ensure customer is of type customer
    CONSTRAINT customer_products_customer_type_check 
        CHECK (EXISTS (SELECT 1 FROM companies WHERE id = customer_id AND company_type = 'customer')),
    
    -- Unique constraint to prevent duplicate relationships
    UNIQUE(customer_id, product_id)
);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_customer_shippers_updated_at 
    BEFORE UPDATE ON customer_shippers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_consignee_notify_parties_updated_at 
    BEFORE UPDATE ON consignee_notify_parties 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_products_updated_at 
    BEFORE UPDATE ON customer_products 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INTELLIGENCE FUNCTIONS
-- ============================================================================

-- Function to update frequency scores when relationships are used
CREATE OR REPLACE FUNCTION update_relationship_frequency(
    table_name TEXT,
    relationship_id UUID
)
RETURNS VOID AS $$
BEGIN
    IF table_name = 'customer_shippers' THEN
        UPDATE customer_shippers 
        SET frequency_score = frequency_score + 1,
            last_used_date = CURRENT_TIMESTAMP
        WHERE id = relationship_id;
    ELSIF table_name = 'consignee_notify_parties' THEN
        UPDATE consignee_notify_parties 
        SET frequency_score = frequency_score + 1,
            last_used_date = CURRENT_TIMESTAMP
        WHERE id = relationship_id;
    ELSIF table_name = 'customer_products' THEN
        UPDATE customer_products 
        SET frequency_score = frequency_score + 1,
            last_ordered_date = CURRENT_TIMESTAMP,
            total_orders = total_orders + 1
        WHERE id = relationship_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get intelligent suggestions for customer shippers
CREATE OR REPLACE FUNCTION get_customer_shipper_suggestions(
    p_customer_id UUID,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE (
    shipper_id UUID,
    shipper_name TEXT,
    frequency_score INTEGER,
    is_default BOOLEAN,
    last_used_date TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cs.shipper_id,
        c.name,
        cs.frequency_score,
        cs.is_default,
        cs.last_used_date
    FROM customer_shippers cs
    JOIN companies c ON c.id = cs.shipper_id
    WHERE cs.customer_id = p_customer_id 
    AND cs.is_active = true
    AND c.is_active = true
    ORDER BY cs.is_default DESC, cs.frequency_score DESC, cs.last_used_date DESC NULLS LAST
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to get intelligent suggestions for consignee notify parties
CREATE OR REPLACE FUNCTION get_consignee_notify_party_suggestions(
    p_consignee_id UUID,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE (
    notify_party_id UUID,
    notify_party_name TEXT,
    notification_priority INTEGER,
    is_default BOOLEAN,
    last_used_date TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cnp.notify_party_id,
        c.name,
        cnp.notification_priority,
        cnp.is_default,
        cnp.last_used_date
    FROM consignee_notify_parties cnp
    JOIN companies c ON c.id = cnp.notify_party_id
    WHERE cnp.consignee_id = p_consignee_id 
    AND cnp.is_active = true
    AND c.is_active = true
    ORDER BY cnp.is_default DESC, cnp.notification_priority ASC, cnp.frequency_score DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Customer shippers indexes
CREATE INDEX idx_customer_shippers_customer ON customer_shippers(customer_id);
CREATE INDEX idx_customer_shippers_shipper ON customer_shippers(shipper_id);
CREATE INDEX idx_customer_shippers_default ON customer_shippers(customer_id, is_default) WHERE is_default = true;
CREATE INDEX idx_customer_shippers_frequency ON customer_shippers(customer_id, frequency_score DESC);
CREATE INDEX idx_customer_shippers_active ON customer_shippers(is_active);

-- Consignee notify parties indexes
CREATE INDEX idx_consignee_notify_parties_consignee ON consignee_notify_parties(consignee_id);
CREATE INDEX idx_consignee_notify_parties_notify ON consignee_notify_parties(notify_party_id);
CREATE INDEX idx_consignee_notify_parties_default ON consignee_notify_parties(consignee_id, is_default) WHERE is_default = true;
CREATE INDEX idx_consignee_notify_parties_priority ON consignee_notify_parties(consignee_id, notification_priority);
CREATE INDEX idx_consignee_notify_parties_active ON consignee_notify_parties(is_active);

-- Customer products indexes
CREATE INDEX idx_customer_products_customer ON customer_products(customer_id);
CREATE INDEX idx_customer_products_product ON customer_products(product_id);
CREATE INDEX idx_customer_products_default ON customer_products(customer_id, is_default) WHERE is_default = true;
CREATE INDEX idx_customer_products_frequency ON customer_products(customer_id, frequency_score DESC);
CREATE INDEX idx_customer_products_price_valid ON customer_products(price_valid_from, price_valid_to);
CREATE INDEX idx_customer_products_active ON customer_products(is_active);

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE customer_shippers IS 'Relationship intelligence table linking customers to their preferred shippers with frequency tracking';
COMMENT ON TABLE consignee_notify_parties IS 'Relationship intelligence table linking consignees to notify parties with notification preferences';
COMMENT ON TABLE customer_products IS 'Customer-specific product pricing and packaging specifications with intelligent pre-population support';

COMMENT ON COLUMN customer_shippers.frequency_score IS 'Tracking metric for how often this customer-shipper relationship is used';
COMMENT ON COLUMN customer_shippers.is_default IS 'Flag to mark the default shipper for intelligent pre-population';
COMMENT ON COLUMN consignee_notify_parties.notification_priority IS 'Priority order for notifications (1=primary, 2=secondary, etc.)';
COMMENT ON COLUMN customer_products.is_default IS 'Flag to mark default product selection for this customer';
COMMENT ON COLUMN customer_products.quality_specifications IS 'JSONB field for customer-specific quality requirements and specifications';

COMMENT ON FUNCTION update_relationship_frequency(TEXT, UUID) IS 'Updates frequency tracking when relationships are used in shipments';
COMMENT ON FUNCTION get_customer_shipper_suggestions(UUID, INTEGER) IS 'Returns intelligent shipper suggestions based on relationship history';
COMMENT ON FUNCTION get_consignee_notify_party_suggestions(UUID, INTEGER) IS 'Returns intelligent notify party suggestions based on relationship history';