# Story 2.6: Customer-Shipper Relationship Management

## Status
Done

## Story
**As a** CS representative,  
**I want** to configure customer-shipper relationships with default preferences,  
**so that** shipment creation is streamlined with intelligent pre-population.

## Acceptance Criteria

**1:** Customer-shipper relationship interface allows associating customers with multiple shippers.

**2:** Default shipper designation ensures only one default per customer with automatic toggle of previous defaults.

**3:** Relationship status (active/inactive) controls visibility in shipment creation workflows.

**4:** Bulk import functionality supports initial relationship setup from existing data.

**5:** Relationship changes trigger real-time updates in shipment creation interfaces.

## Tasks / Subtasks

- [ ] Create customer-shipper relationship management page and navigation integration (AC: 1, 3)
  - [ ] Add relationships navigation item to master-data section in sidebar
  - [ ] Create `src/app/(dashboard)/master-data/relationships/page.tsx` with relationship list view
  - [ ] Implement customer-shipper data table with filtering and search capabilities
  - [ ] Add filtering by customer company and relationship status (active/inactive)
  - [ ] Implement search functionality for customer and shipper names

- [ ] Develop customer-shipper relationship form component (AC: 1, 2, 3)
  - [ ] Create `src/components/forms/customer-shipper-form/customer-shipper-form.tsx` component
  - [ ] Implement customer selection dropdown (customer type only)
  - [ ] Implement shipper selection dropdown (shipper type only)
  - [ ] Add default shipper toggle with automatic previous default reset logic
  - [ ] Add relationship status toggle (is_active field) and notes input
  - [ ] Apply form validation with Zod schema for required fields and constraints

- [ ] Implement bulk import functionality for relationship setup (AC: 4)
  - [ ] Create bulk import component with CSV file upload support
  - [ ] Implement CSV validation for customer/shipper matching and data integrity
  - [ ] Add preview functionality showing import results before commit
  - [ ] Handle error cases for invalid customer/shipper references
  - [ ] Provide import summary with success/failure counts and error details

- [ ] Create relationship validation and state management (AC: 1, 2, 3, 5)
  - [ ] Extend `src/lib/validations/` with customer-shippers.ts validation schema
  - [ ] Create customer-shipper store in `src/stores/customer-shipper-store.ts` for state management
  - [ ] Implement `src/hooks/use-customer-shippers.ts` for CRUD operations
  - [ ] Add automatic default toggle validation to ensure only one default per customer
  - [ ] Implement optimistic updates and real-time synchronization for shipment creation

- [ ] Integrate real-time updates in shipment creation interfaces (AC: 5)
  - [ ] Update shipment creation form to subscribe to customer-shipper relationship changes
  - [ ] Implement intelligent pre-population when customer is selected
  - [ ] Add real-time shipper dropdown updates when relationships change
  - [ ] Ensure default shipper selection works seamlessly in shipment workflows

- [ ] Create comprehensive testing suite (All ACs)
  - [ ] Write unit tests for customer-shipper form validation and type restrictions
  - [ ] Test default shipper toggle functionality with automatic previous default reset
  - [ ] Create integration tests for relationship CRUD operations with company type validation
  - [ ] Test bulk import functionality with CSV parsing and error handling
  - [ ] Test real-time updates in shipment creation interface integration
  - [ ] Validate relationship status management and filtering capabilities

## Dev Notes

### Previous Story Insights
From Story 2.5: Driver Management and Carrier Association completed with comprehensive company management system and type-specific validation patterns. The established patterns for company type filtering (carrier companies only for drivers) and form validation can be directly applied to customer-shipper relationships, ensuring only customers can be associated with only shippers with proper validation and constraint handling.

### Data Models and Database Schema Context
**Customer-Shipper Relationship Table Design:**
[Source: User-provided schema]
Customer-shipper relationships use the `customer_shippers` table with the following structure:
- id: UUID primary key with auto-generation
- customer_id: UUID reference to companies table (company_type must equal 'customer')
- shipper_id: UUID reference to companies table (company_type must equal 'shipper')
- is_default: boolean - Default shipper designation per customer (only one per customer)
- is_active: boolean - Relationship status controlling visibility in workflows (default: true)
- notes: text - Additional relationship information and special instructions
- created_at/updated_at: timestamptz audit fields

**Database Constraints and Validation:**
[Source: User-provided schema]
- Unique constraint on (customer_id, shipper_id) prevents duplicate relationships
- Foreign key constraints with CASCADE DELETE for data integrity
- Company type validation: `is_company_type(customer_id, 'customer')` ensures only customer companies
- Company type validation: `is_company_type(shipper_id, 'shipper')` ensures only shipper companies
- Default boolean logic requires application-level enforcement for single default per customer

**Relationship Intelligence Integration:**
[Source: core-workflows.md#intelligent-shipment-creation]
Customer-shipper relationships integrate with the Relationship Intelligence Engine for shipment pre-population:
- When customer is selected, query `customer_shippers` for available and default shippers
- Default shipper automatically populates in shipment creation forms
- Active relationships control shipper dropdown options during shipment creation
- Real-time subscriptions ensure immediate updates when relationships change

### API Specifications and Database Access Patterns
[Source: backend-architecture.md#data-access-layer]
**Customer-Shipper Data Access Pattern:**
```typescript
// Fetch customer-shipper relationships with company information
const { data: relationships } = await supabase
  .from('customer_shippers')
  .select(`
    *,
    customer:companies!customer_id(name, company_type, contact_phone),
    shipper:companies!shipper_id(name, company_type, contact_phone)
  `)
  .eq('is_active', true)
  .order('customer:companies(name)')

// Create customer-shipper relationship with default validation
const { data: relationship } = await supabase
  .from('customer_shippers')
  .insert({
    customer_id,
    shipper_id,
    is_default,
    is_active: true,
    notes
  })
  .select()
  .single()
```

**Default Shipper Management Pattern:**
```typescript
// Reset previous default when setting new default
if (is_default) {
  await supabase
    .from('customer_shippers')
    .update({ is_default: false })
    .eq('customer_id', customer_id)
    .neq('id', relationship_id)
}

// Get default shipper for customer (shipment creation)
const { data: defaultShipper } = await supabase
  .from('customer_shippers')
  .select(`
    shipper:companies!shipper_id(id, name)
  `)
  .eq('customer_id', customer_id)
  .eq('is_default', true)
  .eq('is_active', true)
  .single()
```

### Component Architecture and Integration Patterns
[Source: frontend-architecture.md#component-architecture]
**Customer-Shipper Management Architecture:**
- Relationship list page at `src/app/(dashboard)/master-data/relationships/page.tsx`
- Customer-shipper form component at `src/components/forms/customer-shipper-form/customer-shipper-form.tsx`
- Bulk import component integrated within relationships page for initial data setup
- Leverage existing company selection patterns for customer and shipper dropdowns
- Use existing data table patterns with ShadCN UI components

**ShadCN UI Component Usage:**
- Use existing DataTable components for relationship list with pagination and sorting
- Implement Select component for customer/shipper selection with company type filtering
- Use Switch components for default shipper and active status toggles
- Leverage Badge components for relationship status (Active/Inactive) visual indicators
- Apply existing form validation patterns with react-hook-form and Zod schemas

**Real-time Integration Patterns:**
- Subscribe to customer_shippers table changes for live updates in relationship list
- Integrate with shipment creation forms for real-time shipper dropdown updates
- Use Zustand store for optimistic updates and state synchronization
- Implement real-time notifications when default shipper designations change

### File Locations and Project Structure Alignment
[Source: unified-project-structure.md]
**Customer-Shipper Relationship File Structure:**
- Main page: `src/app/(dashboard)/master-data/relationships/page.tsx`
- Relationship form: `src/components/forms/customer-shipper-form/customer-shipper-form.tsx`
- Bulk import: Integrate within relationships page as bulk-import section
- Validation: `src/lib/validations/customer-shippers.ts` for relationship schema validation
- State management: `src/stores/customer-shipper-store.ts` for relationship operations
- Hooks: `src/hooks/use-customer-shippers.ts` for relationship CRUD operations
- Types: Extend existing database types for customer-shipper interface definitions

### Technical Constraints and Requirements
[Source: tech-stack.md#technology-stack-table]
- Maintain consistency with Next.js 14.2+ App Router and TypeScript 5.3+
- Follow existing Supabase Client API patterns with auto-generated TypeScript types
- Use PostgreSQL foreign key constraints and company type validation functions
- Maintain ShadCN UI components with established dark blue theme colors
- Follow existing Zustand 4.5+ state management patterns with real-time subscriptions
- Apply consistent Zod validation patterns for form validation and company type checking
- Integrate with existing Relationship Intelligence Engine for shipment pre-population

### Company Type Validation and Filtering Requirements
**Customer Selection Filtering:**
- Filter companies table to show only company_type = 'customer' in customer dropdown
- Validate selected customer_id references valid customer company type
- Use existing company management patterns from previous stories for consistency

**Shipper Selection Filtering:**
- Filter companies table to show only company_type = 'shipper' in shipper dropdown
- Validate selected shipper_id references valid shipper company type
- Apply existing company type validation patterns from driver management story

**Default Shipper Logic:**
- Implement application-level logic to ensure only one default shipper per customer
- When setting new default, automatically reset is_default = false for other relationships
- Validate default shipper selection in shipment creation workflows
- Handle edge cases where customer has no default shipper designated

### Bulk Import Functionality Requirements
**CSV Import Processing:**
- Support CSV format with columns: Customer Name, Shipper Name, Is Default, Notes
- Implement fuzzy matching for company names to handle minor variations
- Validate customer and shipper company types before creating relationships
- Handle duplicate relationship prevention with clear error messaging
- Provide detailed import summary with success/failure reporting

**Import Validation and Error Handling:**
- Validate CSV structure and required columns before processing
- Check for duplicate customer-shipper combinations in import data
- Verify customer and shipper company existence and types
- Handle default shipper conflicts within import data and existing relationships
- Provide line-by-line error reporting with clear resolution guidance

### Testing

#### Testing Standards
**Test Location**: `tests/` directory with `unit/`, `integration/`, and `e2e/` subdirectories for customer-shipper relationship tests
**Testing Framework**: Vitest + Testing Library for unit tests, Playwright for E2E relationship management workflows
**Testing Patterns**: 
- Component testing for customer-shipper form with company type filtering and default logic
- Integration testing with local Supabase instance for relationship CRUD operations
- Mock data for isolated component tests with realistic customer and shipper information
- E2E testing for complete relationship management workflows including bulk import
- Company type restriction validation testing for both customers and shippers

**Specific Testing Requirements for This Story**:
- Test customer-shipper relationship form with company type filtering (customer and shipper types only)
- Validate relationship creation, update, and deletion operations with proper constraints
- Test default shipper toggle functionality with automatic previous default reset
- Verify relationship list filtering by customer company and active status
- Test search functionality for customer and shipper names in relationship list
- Validate bulk import functionality with CSV parsing and error handling
- Test real-time integration with shipment creation interface and pre-population
- Verify relationship status management and visibility control in shipment workflows
- Test integration with existing company management system and type validation patterns

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-22 | 1.0 | Initial story creation with comprehensive architecture context | Scrum Master |

## Dev Agent Record

### Agent Model Used

*This section will be populated by the development agent during implementation*

### Debug Log References

*This section will be populated by the development agent during implementation*

### Completion Notes List

*This section will be populated by the development agent during implementation*

### File List

*This section will be populated by the development agent during implementation*

## QA Results

*This section will be populated by the QA agent during review*