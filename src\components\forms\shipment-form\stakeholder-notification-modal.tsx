'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Mail, 
  Phone, 
  MessageCircle, 
  Send,
  User,
  Building2,
  CheckCircle,
  Loader2,
  AlertTriangle,
  Info,
  Star
} from 'lucide-react'

interface NotificationChannel {
  id: string
  name: string
  icon: React.ReactNode
  color: string
  available: boolean
  description: string
}

interface Stakeholder {
  id: string
  type: 'customer' | 'shipper' | 'consignee' | 'notify_party' | 'forwarder_agent' | 'carrier' | 'driver'
  name: string
  email?: string
  phone?: string
  preferredChannels?: string[]
  isDefault?: boolean
  contactPerson?: string
}

interface StakeholderNotificationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  shipmentId: string
  shipmentNumber: string
  stakeholders: Stakeholder[]
  isSending?: boolean
  onSendNotification: (data: {
    recipients: string[]
    channels: string[]
    subject: string
    message: string
    urgency: 'low' | 'normal' | 'high'
    includeShipmentDetails: boolean
  }) => Promise<void>
}

const NOTIFICATION_CHANNELS: NotificationChannel[] = [
  {
    id: 'email',
    name: 'Email',
    icon: <Mail className="h-4 w-4" />,
    color: 'border-blue-500 text-blue-300 bg-blue-500/10',
    available: true,
    description: 'Send email notifications with detailed information'
  },
  {
    id: 'sms',
    name: 'SMS',
    icon: <Phone className="h-4 w-4" />,
    color: 'border-green-500 text-green-300 bg-green-500/10',
    available: true,
    description: 'Send SMS for urgent notifications and status updates'
  },
  {
    id: 'line',
    name: 'LINE',
    icon: <MessageCircle className="h-4 w-4" />,
    color: 'border-green-500 text-green-300 bg-green-500/10',
    available: true,
    description: 'Send LINE messages (popular in Thailand and Asia)'
  },
  {
    id: 'wechat',
    name: 'WeChat',
    icon: <MessageCircle className="h-4 w-4" />,
    color: 'border-green-500 text-green-300 bg-green-500/10',
    available: true,
    description: 'Send WeChat messages (popular in China)'
  }
]

const NOTIFICATION_TEMPLATES = {
  status_update: {
    subject: 'Shipment Status Update - {shipment_number}',
    message: 'Your shipment {shipment_number} has been updated to: {status}\n\nFor more details, please check your shipment dashboard or contact us.'
  },
  departure: {
    subject: 'Shipment Departure Notification - {shipment_number}',
    message: 'Your shipment {shipment_number} has departed from {origin_port} and is now on its way to {destination_port}.\n\nEstimated arrival: {eta_date}\n\nTracking information will be updated regularly.'
  },
  arrival: {
    subject: 'Shipment Arrival Notification - {shipment_number}',
    message: 'Your shipment {shipment_number} has arrived at {destination_port}.\n\nPlease prepare for customs clearance and delivery arrangements.'
  },
  delay: {
    subject: 'Shipment Delay Notice - {shipment_number}',
    message: 'We regret to inform you that your shipment {shipment_number} has been delayed.\n\nNew estimated arrival: {eta_date}\n\nWe apologize for any inconvenience and will keep you updated.'
  },
  document_ready: {
    subject: 'Shipping Documents Ready - {shipment_number}',
    message: 'The shipping documents for {shipment_number} are now ready for collection or download.\n\nPlease access your dashboard or contact us to receive the documents.'
  },
  custom: {
    subject: '',
    message: ''
  }
}

export function StakeholderNotificationModal({
  open,
  onOpenChange,
  shipmentId,
  shipmentNumber,
  stakeholders,
  isSending = false,
  onSendNotification
}: StakeholderNotificationModalProps) {
  const [selectedStakeholders, setSelectedStakeholders] = useState<string[]>([])
  const [selectedChannels, setSelectedChannels] = useState<string[]>(['email'])
  const [notificationType, setNotificationType] = useState<keyof typeof NOTIFICATION_TEMPLATES>('status_update')
  const [customSubject, setCustomSubject] = useState('')
  const [customMessage, setCustomMessage] = useState('')
  const [urgency, setUrgency] = useState<'low' | 'normal' | 'high'>('normal')
  const [includeShipmentDetails, setIncludeShipmentDetails] = useState(true)

  const handleStakeholderToggle = (stakeholderId: string, checked: boolean) => {
    setSelectedStakeholders(prev => 
      checked 
        ? [...prev, stakeholderId]
        : prev.filter(id => id !== stakeholderId)
    )
  }

  const handleChannelToggle = (channelId: string, checked: boolean) => {
    setSelectedChannels(prev => 
      checked 
        ? [...prev, channelId]
        : prev.filter(id => id !== channelId)
    )
  }

  const handleSelectAllStakeholders = () => {
    setSelectedStakeholders(stakeholders.map(s => s.id))
  }

  const handleSelectDefaultStakeholders = () => {
    setSelectedStakeholders(stakeholders.filter(s => s.isDefault).map(s => s.id))
  }

  const handleTemplateChange = (template: keyof typeof NOTIFICATION_TEMPLATES) => {
    setNotificationType(template)
    if (template === 'custom') {
      setCustomSubject('')
      setCustomMessage('')
    } else {
      setCustomSubject(NOTIFICATION_TEMPLATES[template].subject)
      setCustomMessage(NOTIFICATION_TEMPLATES[template].message)
    }
  }

  const handleSendNotification = async () => {
    if (selectedStakeholders.length === 0 || selectedChannels.length === 0) return
    
    await onSendNotification({
      recipients: selectedStakeholders,
      channels: selectedChannels,
      subject: customSubject,
      message: customMessage,
      urgency,
      includeShipmentDetails
    })
  }

  const getStakeholderTypeIcon = (type: string) => {
    switch (type) {
      case 'customer':
        return <Star className="h-4 w-4 text-yellow-500" />
      case 'shipper':
      case 'consignee':
      case 'notify_party':
        return <Building2 className="h-4 w-4 text-blue-500" />
      case 'forwarder_agent':
        return <Building2 className="h-4 w-4 text-purple-500" />
      case 'carrier':
      case 'driver':
        return <User className="h-4 w-4 text-green-500" />
      default:
        return <User className="h-4 w-4 text-slate-500" />
    }
  }

  const getStakeholderTypeName = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const getUrgencyColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'border-red-500 text-red-300 bg-red-500/10'
      case 'normal':
        return 'border-blue-500 text-blue-300 bg-blue-500/10'
      case 'low':
        return 'border-gray-500 text-gray-300 bg-gray-500/10'
      default:
        return 'border-slate-500 text-slate-300 bg-slate-500/10'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] bg-slate-800 border-slate-700 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Send className="h-5 w-5" />
            Send Notifications - {shipmentNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recipients Selection */}
          <Card className="bg-slate-900/50 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-white font-medium">Select Recipients</h4>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectDefaultStakeholders}
                    className="border-orange-500 bg-orange-600/10 text-orange-100 hover:bg-orange-600/20"
                  >
                    <Star className="h-3 w-3 mr-2" />
                    Defaults Only
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAllStakeholders}
                    className="border-green-500 bg-green-600/10 text-green-100 hover:bg-green-600/20"
                  >
                    Select All
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {stakeholders.map((stakeholder) => (
                  <div
                    key={stakeholder.id}
                    className="flex items-start gap-3 p-3 rounded-lg border border-slate-600 bg-slate-800/30"
                  >
                    <Checkbox
                      checked={selectedStakeholders.includes(stakeholder.id)}
                      disabled={isSending}
                      onCheckedChange={(checked) => 
                        handleStakeholderToggle(stakeholder.id, checked as boolean)
                      }
                      className="border-slate-500 mt-1"
                    />

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        {getStakeholderTypeIcon(stakeholder.type)}
                        <span className="font-medium text-white truncate">
                          {stakeholder.name}
                        </span>
                        {stakeholder.isDefault && (
                          <Badge variant="outline" className="border-orange-500 text-orange-300 bg-orange-500/10 text-xs">
                            <Star className="h-2 w-2 mr-1" />
                            Default
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-xs text-slate-400 mb-2">
                        {getStakeholderTypeName(stakeholder.type)}
                        {stakeholder.contactPerson && ` • ${stakeholder.contactPerson}`}
                      </p>

                      <div className="space-y-1">
                        {stakeholder.email && (
                          <div className="flex items-center gap-1 text-xs text-slate-400">
                            <Mail className="h-3 w-3" />
                            {stakeholder.email}
                          </div>
                        )}
                        {stakeholder.phone && (
                          <div className="flex items-center gap-1 text-xs text-slate-400">
                            <Phone className="h-3 w-3" />
                            {stakeholder.phone}
                          </div>
                        )}
                        {stakeholder.preferredChannels && stakeholder.preferredChannels.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {stakeholder.preferredChannels.map(channel => (
                              <Badge
                                key={channel}
                                variant="outline"
                                className="border-blue-500 text-blue-300 bg-blue-500/10 text-xs"
                              >
                                {channel.toUpperCase()}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {selectedStakeholders.length > 0 && (
                <div className="mt-3 p-2 bg-blue-900/20 rounded border border-blue-500/20">
                  <div className="flex items-center gap-2 text-blue-300 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    <span>{selectedStakeholders.length} recipient(s) selected</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notification Channels */}
          <Card className="bg-slate-900/50 border-slate-700">
            <CardContent className="p-4">
              <h4 className="text-white font-medium mb-3">Notification Channels</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {NOTIFICATION_CHANNELS.map((channel) => (
                  <div
                    key={channel.id}
                    className={`flex items-center gap-2 p-3 rounded-lg border ${
                      channel.available 
                        ? 'border-slate-600 bg-slate-800/30 cursor-pointer' 
                        : 'border-slate-700 bg-slate-800/10 opacity-50'
                    }`}
                    onClick={() => channel.available && handleChannelToggle(channel.id, !selectedChannels.includes(channel.id))}
                  >
                    <Checkbox
                      checked={selectedChannels.includes(channel.id)}
                      disabled={!channel.available || isSending}
                      onCheckedChange={(checked) => 
                        handleChannelToggle(channel.id, checked as boolean)
                      }
                      className="border-slate-500"
                    />
                    <div className="flex items-center gap-2">
                      {channel.icon}
                      <span className="text-white font-medium">{channel.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Message Configuration */}
          <Card className="bg-slate-900/50 border-slate-700">
            <CardContent className="p-4 space-y-4">
              <h4 className="text-white font-medium">Message Configuration</h4>

              {/* Template Selection */}
              <div>
                <Label htmlFor="template" className="text-slate-300">Notification Template</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                  {Object.keys(NOTIFICATION_TEMPLATES).map((template) => (
                    <Button
                      key={template}
                      variant={notificationType === template ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleTemplateChange(template as keyof typeof NOTIFICATION_TEMPLATES)}
                      className={notificationType === template 
                        ? "bg-blue-600 text-white" 
                        : "border-slate-600 text-slate-300 hover:bg-slate-700"
                      }
                    >
                      {template.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Urgency Level */}
              <div>
                <Label htmlFor="urgency" className="text-slate-300">Urgency Level</Label>
                <div className="flex gap-2 mt-2">
                  {(['low', 'normal', 'high'] as const).map((level) => (
                    <Button
                      key={level}
                      variant={urgency === level ? "default" : "outline"}
                      size="sm"
                      onClick={() => setUrgency(level)}
                      className={urgency === level 
                        ? "bg-blue-600 text-white" 
                        : "border-slate-600 text-slate-300 hover:bg-slate-700"
                      }
                    >
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Subject */}
              <div>
                <Label htmlFor="subject" className="text-slate-300">Subject</Label>
                <Input
                  id="subject"
                  value={customSubject}
                  onChange={(e) => setCustomSubject(e.target.value)}
                  placeholder="Enter notification subject..."
                  className="bg-slate-700 border-slate-600 text-white mt-1"
                  disabled={isSending}
                />
              </div>

              {/* Message */}
              <div>
                <Label htmlFor="message" className="text-slate-300">Message</Label>
                <Textarea
                  id="message"
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="Enter notification message..."
                  rows={4}
                  className="bg-slate-700 border-slate-600 text-white mt-1 resize-none"
                  disabled={isSending}
                />
              </div>

              {/* Options */}
              <div className="flex items-center gap-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-details"
                    checked={includeShipmentDetails}
                    onCheckedChange={(checked) => setIncludeShipmentDetails(checked as boolean)}
                    disabled={isSending}
                    className="border-slate-500"
                  />
                  <Label htmlFor="include-details" className="text-slate-300 text-sm">
                    Include shipment details
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSending}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSendNotification}
            disabled={selectedStakeholders.length === 0 || selectedChannels.length === 0 || !customSubject.trim() || !customMessage.trim() || isSending}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Notifications
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}