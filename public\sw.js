const CACHE_NAME = 'dyy-driver-app-v1.0.0';
const STATIC_CACHE_NAME = 'dyy-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'dyy-dynamic-v1.0.0';

// Assets to cache immediately on install
const STATIC_ASSETS = [
  '/driver/dashboard',
  '/driver/login',
  '/manifest.json',
  // Add core styling and JS files
  '/_next/static/css/app/layout.css',
  '/_next/static/chunks/webpack.js',
  // Offline fallback page
  '/offline.html'
];

// Dynamic assets that should be cached
const CACHE_PATTERNS = [
  /^\/mobile\/driver\//,
  /^\/api\/driver\//,
  /^\/icons\//,
  /^\/screenshots\//
];

// Assets to skip caching (too large or frequently changing)
const SKIP_CACHE_PATTERNS = [
  /^\/api\/auth\//,
  /^\/api\/socket/,
  /chrome-extension:/,
  /extension\//,
  /hot-update/
];

// Install event - cache static assets
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached successfully');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Error caching static assets:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName !== CACHE_NAME) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated successfully');
        return self.clients.claim();
      })
      .catch(error => {
        console.error('Service Worker: Activation error:', error);
      })
  );
});

// Fetch event - handle requests with cache strategies
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests and chrome extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }

  // Skip requests that match skip patterns
  if (SKIP_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    return;
  }

  // Handle driver app routes
  if (url.pathname.startsWith('/mobile/driver/')) {
    event.respondWith(handleDriverAppRequest(request));
    return;
  }

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static assets
  if (CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    event.respondWith(handleStaticAssetRequest(request));
    return;
  }

  // Default: network first, fallback to cache
  event.respondWith(handleDefaultRequest(request));
});

// Handle driver app requests (cache first with network fallback)
async function handleDriverAppRequest(request) {
  try {
    // Try cache first for faster loading
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      // Update cache in background
      updateCacheInBackground(request);
      return cachedResponse;
    }

    // If not in cache, fetch from network
    const response = await fetch(request);
    if (response.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, response.clone());
    }
    return response;

  } catch (error) {
    console.warn('Service Worker: Driver app request failed:', error);
    
    // Return offline fallback for navigation requests
    if (request.mode === 'navigate') {
      const offlineResponse = await caches.match('/offline.html');
      if (offlineResponse) {
        return offlineResponse;
      }
    }
    
    // Return minimal offline response
    return new Response(
      JSON.stringify({ 
        error: 'Offline', 
        message: 'You are currently offline. Please check your connection.' 
      }),
      { 
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle API requests (network first with cache fallback)
async function handleApiRequest(request) {
  try {
    const response = await fetch(request);
    
    // Cache successful GET responses
    if (response.ok && request.method === 'GET') {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, response.clone());
    }
    
    return response;

  } catch (error) {
    console.warn('Service Worker: API request failed:', error);
    
    // Try to return cached version
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      // Add offline indicator to cached response
      const clonedResponse = cachedResponse.clone();
      const data = await clonedResponse.json();
      return new Response(
        JSON.stringify({ ...data, _offline: true }),
        {
          status: 200,
          statusText: 'OK (Cached)',
          headers: cachedResponse.headers
        }
      );
    }

    // Return offline response
    return new Response(
      JSON.stringify({ 
        error: 'Offline', 
        message: 'Unable to fetch data while offline.',
        _offline: true
      }),
      { 
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle static asset requests (cache first)
async function handleStaticAssetRequest(request) {
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    const response = await fetch(request);
    if (response.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, response.clone());
    }
    return response;
  } catch (error) {
    console.warn('Service Worker: Static asset request failed:', error);
    throw error;
  }
}

// Handle default requests (network first)
async function handleDefaultRequest(request) {
  try {
    const response = await fetch(request);
    return response;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// Update cache in background
async function updateCacheInBackground(request) {
  try {
    const response = await fetch(request);
    if (response.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, response.clone());
    }
  } catch (error) {
    console.warn('Service Worker: Background cache update failed:', error);
  }
}

// Handle background sync for offline actions
self.addEventListener('sync', event => {
  console.log('Service Worker: Background sync triggered:', event.tag);
  
  if (event.tag === 'assignment-sync') {
    event.waitUntil(syncAssignments());
  }
});

// Sync assignments when back online
async function syncAssignments() {
  try {
    // Get pending sync data from IndexedDB
    // This would need to be implemented based on your offline storage strategy
    console.log('Service Worker: Syncing assignments...');
    
    // Clear cache for assignments to fetch fresh data
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const keys = await cache.keys();
    const assignmentRequests = keys.filter(request => 
      request.url.includes('/api/driver/assignments')
    );
    
    await Promise.all(
      assignmentRequests.map(request => cache.delete(request))
    );
    
    console.log('Service Worker: Assignment sync completed');
  } catch (error) {
    console.error('Service Worker: Assignment sync failed:', error);
  }
}

// Handle push notifications (for future feature)
self.addEventListener('push', event => {
  if (!event.data) return;

  const data = event.data.json();
  const options = {
    body: data.body,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: data.tag || 'general',
    data: data.data,
    actions: data.actions || [],
    requireInteraction: data.urgent || false,
    silent: false
  };

  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Handle notification click
self.addEventListener('notificationclick', event => {
  event.notification.close();

  const data = event.notification.data;
  const action = event.action;

  let url = '/driver/dashboard';
  
  if (action === 'view' && data.assignmentId) {
    url = `/driver/assignments/${data.assignmentId}`;
  } else if (data.url) {
    url = data.url;
  }

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then(clientList => {
        // Check if there's already a window open
        for (const client of clientList) {
          if (client.url.includes('/mobile/driver/') && 'focus' in client) {
            client.navigate(url);
            return client.focus();
          }
        }
        
        // Open new window
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
  );
});

// Log service worker errors
self.addEventListener('error', event => {
  console.error('Service Worker: Global error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('Service Worker: Unhandled promise rejection:', event.reason);
});