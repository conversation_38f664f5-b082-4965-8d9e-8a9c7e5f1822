'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Building2,
  Bell,
  Hash,
  Star,
  AlertCircle,
  Loader2,
  Mail,
  Smartphone,
  MessageSquare,
  Info,
} from 'lucide-react'
import {
  consigneeNotifyPartyFormSchema,
  DEFAULT_CONSIGNEE_NOTIFY_PARTY,
  type ConsigneeNotifyPartyForm,
} from '@/lib/validations/consignee-notify-parties'
import {
  useConsigneeOptions,
  useNotifyPartyOptions,
} from '@/hooks/use-consignee-notify-parties'

import type { ConsigneeNotifyParty } from '@/stores/consignee-notify-party-store'

interface ConsigneeNotifyPartyFormProps {
  consigneeNotifyParty?: ConsigneeNotifyParty | null
  onSubmit: (data: ConsigneeNotifyPartyForm) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function ConsigneeNotifyPartyForm({
  consigneeNotifyParty,
  onSubmit,
  onCancel,
  isLoading = false,
}: ConsigneeNotifyPartyFormProps) {
  const [error, setError] = useState<string | null>(null)
  const [selectedConsignee, setSelectedConsignee] = useState<string>('')
  const [selectedNotifyParty, setSelectedNotifyParty] = useState<string>('')

  const { consignees, loading: loadingConsignees } = useConsigneeOptions()
  const { notifyParties, loading: loadingNotifyParties } =
    useNotifyPartyOptions()

  const form = useForm<ConsigneeNotifyPartyForm>({
    resolver: zodResolver(consigneeNotifyPartyFormSchema),
    defaultValues: consigneeNotifyParty
      ? {
          consignee_id: consigneeNotifyParty.consignee_id,
          notify_party_id: consigneeNotifyParty.notify_party_id,
          is_default: consigneeNotifyParty.is_default || false,
          is_active: consigneeNotifyParty.is_active ?? true,
          notification_preferences:
            consigneeNotifyParty.notification_preferences || {
              email: true,
              sms: false,
              line: false,
              wechat: false,
            },
          priority_order: consigneeNotifyParty.priority_order || 1,
          special_instructions: consigneeNotifyParty.special_instructions || '',
          notes: consigneeNotifyParty.notes || '',
        }
      : {
          ...DEFAULT_CONSIGNEE_NOTIFY_PARTY,
          special_instructions: '',
          notes: '',
        },
  })

  // Watch form values for validation feedback
  const watchedConsignee = form.watch('consignee_id')
  const watchedNotifyParty = form.watch('notify_party_id')
  const watchedNotificationPreferences = form.watch('notification_preferences')

  // Set selected values for display
  useEffect(() => {
    setSelectedConsignee(watchedConsignee)
    setSelectedNotifyParty(watchedNotifyParty)
  }, [watchedConsignee, watchedNotifyParty])

  // Handle form submission
  const handleSubmit = async (data: ConsigneeNotifyPartyForm) => {
    try {
      setError(null)

      // Convert empty strings to null for optional fields
      const processedData = {
        ...data,
        special_instructions: data.special_instructions || null,
        notes: data.notes || null,
      }

      await onSubmit(processedData)
    } catch (error) {
      console.error('Form submission error:', error)
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to save consignee-notify party relationship'
      )
    }
  }

  // Find selected company details
  const selectedConsigneeDetails = consignees.find(
    c => c.id === selectedConsignee
  )
  const selectedNotifyPartyDetails = notifyParties.find(
    n => n.id === selectedNotifyParty
  )

  // Check if at least one notification preference is enabled
  const hasNotificationChannel = Object.values(
    watchedNotificationPreferences
  ).some(value => value === true)

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Company Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Consignee Selection */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-blue-200 flex items-center gap-2">
                <Building2 className="h-5 w-5 text-blue-500" />
                Consignee Selection
              </CardTitle>
              <CardDescription className="text-slate-400">
                Select the consignee company for this relationship
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="consignee_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Consignee Company *
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select consignee company" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                        {loadingConsignees ? (
                          <div className="p-4 text-center text-slate-400">
                            <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                            Loading consignees...
                          </div>
                        ) : consignees.length === 0 ? (
                          <div className="p-4 text-center text-slate-400">
                            No consignee companies found
                          </div>
                        ) : (
                          consignees.map(consignee => (
                            <SelectItem
                              key={consignee.id}
                              value={consignee.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Building2 className="h-3 w-3 text-blue-500" />
                                <span>{consignee.name}</span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedConsigneeDetails && (
                <div className="bg-slate-800 rounded p-3 border border-slate-600">
                  <div className="text-sm text-slate-300">
                    <div className="font-medium">
                      {selectedConsigneeDetails.name}
                    </div>
                    {selectedConsigneeDetails.contact_phone && (
                      <div className="text-slate-400">
                        Phone: {selectedConsigneeDetails.contact_phone}
                      </div>
                    )}
                    <Badge className="mt-2 bg-blue-600 text-white border-blue-600">
                      Consignee
                    </Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notify Party Selection */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-green-200 flex items-center gap-2">
                <Bell className="h-5 w-5 text-green-500" />
                Notify Party Selection
              </CardTitle>
              <CardDescription className="text-slate-400">
                Select the notify party company for this relationship
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="notify_party_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Notify Party Company *
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select notify party company" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                        {loadingNotifyParties ? (
                          <div className="p-4 text-center text-slate-400">
                            <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                            Loading notify parties...
                          </div>
                        ) : notifyParties.length === 0 ? (
                          <div className="p-4 text-center text-slate-400">
                            No notify party companies found
                          </div>
                        ) : (
                          notifyParties.map(notifyParty => (
                            <SelectItem
                              key={notifyParty.id}
                              value={notifyParty.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Bell className="h-3 w-3 text-green-500" />
                                <div>
                                  <div>{notifyParty.name}</div>
                                  {notifyParty.contact_email && (
                                    <div className="text-xs text-slate-400">
                                      Email: {notifyParty.contact_email}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedNotifyPartyDetails && (
                <div className="bg-slate-800 rounded p-3 border border-slate-600">
                  <div className="text-sm text-slate-300">
                    <div className="font-medium">
                      {selectedNotifyPartyDetails.name}
                    </div>
                    {selectedNotifyPartyDetails.contact_phone && (
                      <div className="text-slate-400">
                        Phone: {selectedNotifyPartyDetails.contact_phone}
                      </div>
                    )}
                    {selectedNotifyPartyDetails.contact_email && (
                      <div className="text-slate-400">
                        Email: {selectedNotifyPartyDetails.contact_email}
                      </div>
                    )}
                    <Badge className="mt-2 bg-green-600 text-white border-green-600">
                      Notify Party
                    </Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Notification Preferences */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-yellow-200 flex items-center gap-2">
              <Bell className="h-5 w-5 text-yellow-500" />
              Notification Preferences
            </CardTitle>
            <CardDescription className="text-slate-400">
              Configure communication channels and preferences (at least one
              channel required)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <FormField
                control={form.control}
                name="notification_preferences.email"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-slate-600 p-4 bg-slate-800">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-slate-200 flex items-center gap-2">
                        <Mail className="h-4 w-4 text-blue-400" />
                        Email
                      </FormLabel>
                      <FormDescription className="text-slate-400">
                        Email notifications
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notification_preferences.sms"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-slate-600 p-4 bg-slate-800">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-slate-200 flex items-center gap-2">
                        <Smartphone className="h-4 w-4 text-green-400" />
                        SMS
                      </FormLabel>
                      <FormDescription className="text-slate-400">
                        Text message notifications
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notification_preferences.line"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-slate-600 p-4 bg-slate-800">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-slate-200 flex items-center gap-2">
                        <MessageSquare className="h-4 w-4 text-green-500" />
                        LINE
                      </FormLabel>
                      <FormDescription className="text-slate-400">
                        LINE app notifications
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notification_preferences.wechat"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-slate-600 p-4 bg-slate-800">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-slate-200 flex items-center gap-2">
                        <MessageSquare className="h-4 w-4 text-green-600" />
                        WeChat
                      </FormLabel>
                      <FormDescription className="text-slate-400">
                        WeChat notifications
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {/* Notification preferences validation alert */}
            {!hasNotificationChannel && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className="text-slate-300">
                  At least one notification channel must be enabled to create
                  this relationship.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Priority and Configuration */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-orange-200 flex items-center gap-2">
              <Hash className="h-5 w-5 text-orange-500" />
              Priority & Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="priority_order"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Priority Order *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        min="1"
                        max="999"
                        placeholder="1"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : 1
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Notification priority sequence (1 = highest priority, 999
                      = lowest)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="is_default"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4 bg-slate-800">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base text-slate-200 flex items-center gap-2">
                          <Star className="h-4 w-4 text-yellow-500" />
                          Default Notify Party
                        </FormLabel>
                        <FormDescription className="text-slate-400">
                          Make this the default notify party for this consignee
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                          className="data-[state=checked]:bg-orange-500"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4 bg-slate-800">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base text-slate-200">
                          Active Relationship
                        </FormLabel>
                        <FormDescription className="text-slate-400">
                          Controls visibility in shipment workflows
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                          className="data-[state=checked]:bg-green-500"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Special Instructions */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-purple-200 flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-purple-500" />
              Special Instructions & Communication Requirements
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="special_instructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200">
                    Special Instructions
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Consignee-specific handling or communication requirements..."
                      className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[100px]"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription className="text-slate-400">
                    Consignee-specific handling or communication requirements
                    and instructions
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Additional Notes */}
        <Card className="bg-slate-700 border-slate-600">
          <CardContent className="pt-6">
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200">
                    Additional Notes
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Additional relationship information and context..."
                      className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[100px]"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription className="text-slate-400">
                    Any additional relationship information and context
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading || !hasNotificationChannel}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {consigneeNotifyParty
              ? 'Update Relationship'
              : 'Create Relationship'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
