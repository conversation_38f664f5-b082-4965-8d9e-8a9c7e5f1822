# Story 2.1: Products and Units of Measure Management

## Status
Done

## Story
**As a** CS representative,  
**I want** to create and manage products with their units of measure,  
**so that** I can maintain accurate product catalogs with consistent measurement standards for shipment creation.

## Acceptance Criteria

**1:** Product management interface allows creating products with name, code, description, category, HS code, and unit of measure selection.

**2:** Units of measure management supports creating measurement units with conversion factors, categories (weight, count, volume, length), and base unit relationships.

**3:** Product list view displays all products with search, filtering by category, and sorting capabilities.

**4:** Edit and delete operations maintain data integrity with validation for products in use by existing shipments.

**5:** Interface follows ShadCN UI patterns with responsive design and proper error handling.

## Tasks / Subtasks

- [x] Create Units of Measure management interface (AC: 2)
  - [x] Create units of measure page at `src/app/(dashboard)/master-data/units-of-measure/page.tsx`
  - [x] Build units of measure data table with filtering and search capabilities
  - [x] Create unit creation form with category selection (weight, count, volume, length)
  - [x] Implement unit edit functionality with conversion factor management
  - [x] Add base unit relationship configuration and validation
  - [x] Integrate with Supabase client for units of measure CRUD operations

- [x] Create Products management interface (AC: 1, 3)
  - [x] Create products page at `src/app/(dashboard)/master-data/products/page.tsx`
  - [x] Build product data table with search, category filtering, and sorting
  - [x] Create product creation form with all required fields (name, code, description, category, HS code)
  - [x] Implement unit of measure selection dropdown connected to units table
  - [x] Add product edit functionality with validation and error handling
  - [x] Create product detail view for comprehensive product information

- [x] Implement data integrity and validation (AC: 4)
  - [x] Create Zod validation schemas for products and units in `src/lib/validations/products.ts`
  - [x] Implement server-side validation for product deletion (check shipment usage)
  - [x] Add confirmation dialogs for delete operations with usage warnings
  - [x] Create data integrity checks for unit of measure relationships
  - [x] Implement optimistic updates with rollback on failure

- [x] Build responsive UI components (AC: 5)
  - [x] Create product form component at `src/components/forms/product-form/product-form.tsx`
  - [x] Create unit of measure form component at `src/components/forms/unit-form/unit-form.tsx`
  - [x] Implement responsive data tables using ShadCN UI components
  - [x] Add proper loading states and error handling throughout interface
  - [x] Create mobile-friendly layouts and interactions

- [x] Implement product and unit state management (AC: 1, 2, 3)
  - [x] Create products Zustand store at `src/stores/product-store.ts`
  - [x] Create units of measure Zustand store at `src/stores/unit-store.ts`
  - [x] Implement custom hooks in `src/hooks/use-products.ts` and `src/hooks/use-units.ts`
  - [x] Add real-time subscriptions for products and units data
  - [x] Integrate state management with Supabase client operations

- [x] Create comprehensive testing suite (All ACs)
  - [x] Write unit tests for product and unit validation schemas
  - [x] Create unit tests for validation error scenarios and edge cases
  - [x] Test data integrity validation with proper UUID formats and constraints
  - [x] Validate form schemas for product and unit creation workflows
  - [x] Test category validation and enum constraints for units of measure

## Dev Notes

### Previous Story Insights
From Story 1.3: Authentication system completed with role-based access control, user management interfaces established, and comprehensive navigation system implemented. All infrastructure for protected dashboard routes is ready for master data management implementation.

### Data Models and Schema Context
[Source: architecture/data-models.md#product]
**Product Data Model:**
- id: string (UUID) - Primary identifier
- name: string - Product name (required)
- code: string - Internal product code (optional)
- description: string - Product description (optional)
- category: string - Product classification (optional)
- hs_code: string - Harmonized System code for customs (optional)
- unit_of_measure_id: string - Reference to base measurement unit (required)
- is_active: boolean - Status flag for operational control
- created_at/updated_at: string - Audit timestamps

**Unit of Measure Data Model:**
- id: string (UUID) - Primary identifier
- code: string - Unique unit code (required) (e.g., "KG", "PCS", "LTR", "MTR", "TON")
- name: string - Unit name (e.g., "Kilogram", "Count")
- symbol: string - Unit symbol (e.g., "kg", "pcs", "L", "m", "t", "box")
- category: string - Unit category (weight, count, volume, length)
- conversion_factor: number - Conversion to base unit (default 1.0)
- base_unit_id: string - Reference to base unit (self-referencing)
- is_active: boolean - Status flag
- created_at/updated_at: string - Audit timestamps

### API Specifications and Database Access
[Source: architecture/api-specification.md#supabase-client-api]
**Supabase Client Integration:**
- Type-safe database access using auto-generated TypeScript types
- Real-time subscriptions for data synchronization across users
- Row Level Security policies for role-based data access
- Direct client-side queries with security enforced at database level
- Optimistic updates with error handling and rollback capabilities

**Core API Patterns for Products:**
```typescript
// Product CRUD operations
const { data: products, error } = await supabase
  .from('products')
  .select(`
    *,
    unit_of_measure:unit_of_measure_id (*)
  `)
  .eq('is_active', true)
  .order('name')
```

### Component Architecture Requirements
[Source: architecture/frontend-architecture.md#component-architecture]
**Master Data Page Structure:**
- Page location: `src/app/(dashboard)/master-data/products/page.tsx`
- Component organization: Forms in `src/components/forms/`, UI components in `src/components/ui/`
- State management: Zustand stores for products and units with real-time subscriptions
- Custom hooks: `use-products.ts` and `use-units.ts` for encapsulated business logic

**ShadCN UI Component Usage:**
- Data tables for listing products and units with search/filter capabilities
- Form components with validation using react-hook-form and Zod schemas
- Dialog modals for create/edit operations
- Select dropdowns for unit of measure selection and category filtering
- Badge components for status indicators and categories

### File Locations for Products Management Code
[Source: architecture/unified-project-structure.md]
- Products page: `src/app/(dashboard)/master-data/products/page.tsx`
- Units page: `src/app/(dashboard)/master-data/units-of-measure/page.tsx`
- Form components: `src/components/forms/product-form/`, `src/components/forms/unit-form/`
- Validation schemas: `src/lib/validations/products.ts`
- State stores: `src/stores/product-store.ts`, `src/stores/unit-store.ts`
- Custom hooks: `src/hooks/use-products.ts`, `src/hooks/use-units.ts`
- Types: `src/types/products.ts` (if extended types needed beyond database schema)

### Technical Constraints and Requirements
[Source: architecture/tech-stack.md#technology-stack-table]
- Next.js 14.2+ App Router with TypeScript 5.3+
- Supabase Client API with auto-generated TypeScript types
- ShadCN UI components with Tailwind CSS styling
- Dark blue theme colors (#1e293b, #0f172a, #334155, #f97316)
- Zustand 4.5+ for state management with real-time subscriptions
- Zod validation library for form validation and data integrity

### Navigation and Layout Integration
**Dashboard Layout Integration:**
- Products management accessible via master data navigation section
- Role-based access control: CS representatives and admin users only
- Breadcrumb navigation showing Master Data > Products path
- Mobile-responsive design with sidebar navigation and mobile menu support

### Data Integrity and Validation Requirements
**Product Validation Rules:**
- Name: Required, minimum 2 characters, maximum 100 characters
- Code: Optional, unique constraint if provided, alphanumeric format
- HS Code: Optional, international trade classification format validation
- Unit of Measure: Required, must reference existing active unit
- Category: Optional, predefined categories or free text entry

**Units of Measure Validation Rules:**
- Name: Required, unique constraint, minimum 2 characters
- Code: Required, unique constraint, maximum 20 characters
- Symbol: Required, unique constraint, maximum 10 characters
- Category: Required, enum validation (weight, count, volume, length)
- Conversion Factor: Required, positive number, default 1.0 for base units
- Base Unit: Optional, self-referencing for unit hierarchies

### Security and Access Control
**Role-Based Restrictions:**
- Create/Edit/Delete: CS representatives and admin users only
- View: All authenticated users can view active products and units
- Audit Logging: All product and unit changes logged with user information
- Data Validation: Server-side validation before database operations

### Real-Time Features
**Live Data Synchronization:**
- Real-time updates when products or units are modified by other users
- Optimistic UI updates with rollback on server errors
- Live search and filtering with debounced input handling
- Notification system for concurrent edit conflicts

## Testing

### Testing Standards
**Test Location**: `tests/` directory with `unit/`, `integration/`, and `e2e/` subdirectories for products management tests
**Testing Framework**: Vitest + Testing Library for unit tests, Playwright for E2E product management flows
**Products Testing Patterns**: 
- Component testing for product and unit forms with validation scenarios
- Integration testing with local Supabase instance for CRUD operations
- Mock data for isolated component tests with realistic product information
- E2E testing for complete product management workflows across roles
- Data integrity testing for product-shipment relationships

**Specific Testing Requirements for This Story**:
- Test product creation form with all field validations and error handling
- Validate unit of measure management with conversion factor calculations
- Test product list view with search, filtering, and sorting functionality
- Verify data integrity checks for product deletion with shipment dependencies
- Test responsive design across desktop, tablet, and mobile viewports
- Validate role-based access control for different user types
- Test real-time synchronization of product data across multiple sessions
- Verify error handling for network failures and optimistic update rollbacks

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-17 | 1.0 | Initial story creation with comprehensive architecture context | Scrum Master |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

### Agent Model Used

### Debug Log References

### Completion Notes List

### File List

## QA Results

*This section will be populated by the QA agent during review*