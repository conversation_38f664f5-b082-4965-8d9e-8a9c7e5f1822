'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/use-auth'

export default function DashboardRootPage() {
  const router = useRouter()
  const { profile, loading } = useAuth()

  useEffect(() => {
    if (!loading && profile) {
      // Redirect based on role - this matches the middleware logic
      switch (profile.role) {
        case 'driver':
          router.replace('/driver/dashboard')
          break
        case 'admin':
          router.replace('/dashboard/admin')
          break
        default:
          router.replace('/dashboard/overview')
          break
      }
    }
  }, [loading, profile, router])

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
        <p className="text-slate-300 mt-4">Redirecting to dashboard...</p>
      </div>
    </div>
  )
}