'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuthStore } from '@/stores/auth-store'

export default function AuthTestPage() {
  const [sessionData, setSessionData] = useState<any>(null)
  const [profileData, setProfileData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [errors, setErrors] = useState<string[]>([])

  // Get Zustand store state
  const storeState = useAuthStore()

  useEffect(() => {
    async function testAuth() {
      const supabase = createClient()

      try {
        console.log('Testing auth...')

        // Test 1: Get session
        const { data: sessionResult, error: sessionError } =
          await supabase.auth.getSession()
        console.log('Session result:', sessionResult)
        if (sessionError) {
          console.error('Session error:', sessionError)
          setErrors(prev => [...prev, `Session error: ${sessionError.message}`])
        }
        setSessionData(sessionResult)

        // Test 2: Get profile if session exists
        if (sessionResult?.session?.user) {
          const { data: profileResult, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', sessionResult.session.user.id)
            .single()

          console.log('Profile result:', profileResult)
          if (profileError) {
            console.error('Profile error:', profileError)
            setErrors(prev => [
              ...prev,
              `Profile error: ${profileError.message}`,
            ])
          }
          setProfileData(profileResult)
        }

        // Test 3: Check environment variables
        console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
        console.log(
          'Supabase Key exists:',
          !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        )
      } catch (error) {
        console.error('Auth test error:', error)
        setErrors(prev => [...prev, `General error: ${error}`])
      } finally {
        setLoading(false)
      }
    }

    testAuth()
  }, [])

  if (loading) {
    return <div className="p-6 text-white">Testing auth...</div>
  }

  return (
    <div className="p-6 bg-slate-900 min-h-screen text-white">
      <h1 className="text-2xl font-bold mb-6">Authentication Test</h1>

      {errors.length > 0 && (
        <div className="mb-6 p-4 bg-red-500/20 border border-red-500 rounded">
          <h3 className="font-bold text-red-300 mb-2">Errors:</h3>
          {errors.map((error, i) => (
            <div key={i} className="text-red-200">
              {error}
            </div>
          ))}
        </div>
      )}

      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold mb-2">Environment Check</h2>
          <div className="bg-slate-800 p-4 rounded">
            <p>
              <strong>URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL}
            </p>
            <p>
              <strong>Key Present:</strong>{' '}
              {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Yes' : 'No'}
            </p>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-2">Zustand Store State</h2>
          <pre className="bg-slate-800 p-4 rounded overflow-auto text-xs">
            {JSON.stringify(
              {
                user: storeState.user,
                profile: storeState.profile,
                loading: storeState.loading,
                error: storeState.error,
                hasSession: !!storeState.session,
              },
              null,
              2
            )}
          </pre>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-2">Direct Session Check</h2>
          <pre className="bg-slate-800 p-4 rounded overflow-auto text-xs">
            {JSON.stringify(sessionData, null, 2)}
          </pre>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-2">Profile Data</h2>
          <pre className="bg-slate-800 p-4 rounded overflow-auto text-xs">
            {JSON.stringify(profileData, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  )
}
