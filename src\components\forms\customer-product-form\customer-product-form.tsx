'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Users,
  Package,
  DollarSign,
  Weight,
  Package2,
  Thermometer,
  Wind,
  Clock,
  AlertCircle,
  Loader2,
  Star,
  Info,
} from 'lucide-react'
import {
  customerProductFormSchema,
  DEFAULT_CUSTOMER_PRODUCT,
  CURRENCY_OPTIONS,
  PACKAGING_TYPE_OPTIONS,
  QUALITY_GRADES,
  type CustomerProductForm,
  type CurrencyCode,
  type PackagingType,
} from '@/lib/validations/customer-products'
import {
  useCustomerOptions,
  useProductOptions,
  useUnitOptions,
} from '@/hooks/use-customer-products'
import type { CustomerProduct } from '@/stores/customer-product-store'

interface CustomerProductFormProps {
  customerProduct?: CustomerProduct | null
  onSubmit: (data: CustomerProductForm) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function CustomerProductForm({
  customerProduct,
  onSubmit,
  onCancel,
  isLoading = false,
}: CustomerProductFormProps) {
  const [error, setError] = useState<string | null>(null)
  const [selectedCustomer, setSelectedCustomer] = useState<string>('')
  const [selectedProduct, setSelectedProduct] = useState<string>('')

  const { customers, loading: loadingCustomers } = useCustomerOptions()
  const { products, loading: loadingProducts } = useProductOptions()
  const { units, loading: loadingUnits } = useUnitOptions()

  const form = useForm<CustomerProductForm>({
    resolver: zodResolver(customerProductFormSchema),
    defaultValues: customerProduct
      ? {
          customer_id: customerProduct.customer_id,
          product_id: customerProduct.product_id,
          customer_product_code: customerProduct.customer_product_code || '',
          is_default: customerProduct.is_default || false,
          is_active: customerProduct.is_active ?? true,
          unit_price_cif: customerProduct.unit_price_cif || null,
          unit_price_fob: customerProduct.unit_price_fob || null,
          currency_code:
            (customerProduct.currency_code as CurrencyCode) || 'USD',
          standard_quantity: customerProduct.standard_quantity || null,
          unit_of_measure_id: customerProduct.unit_of_measure_id || '',
          gross_weight_per_package:
            customerProduct.gross_weight_per_package || null,
          net_weight_per_package:
            customerProduct.net_weight_per_package || null,
          quality_grade: customerProduct.quality_grade || '',
          packaging_type:
            (customerProduct.packaging_type as PackagingType) || 'Bag',
          packaging_specifications:
            customerProduct.packaging_specifications || null,
          handling_instructions: customerProduct.handling_instructions || '',
          temperature_require: customerProduct.temperature_require || '',
          vent_require: customerProduct.vent_require || '',
          shelf_life_days: customerProduct.shelf_life_days || null,
          notes: customerProduct.notes || '',
        }
      : {
          ...DEFAULT_CUSTOMER_PRODUCT,
          customer_product_code: '',
          unit_of_measure_id: '',
          quality_grade: '',
          handling_instructions: '',
          temperature_require: '',
          vent_require: '',
          notes: '',
        },
  })

  // Watch form values for validation feedback
  const watchedCustomer = form.watch('customer_id')
  const watchedProduct = form.watch('product_id')
  const watchedCurrency = form.watch('currency_code')
  const watchedCif = form.watch('unit_price_cif')
  const watchedFob = form.watch('unit_price_fob')
  const watchedGrossWeight = form.watch('gross_weight_per_package')
  const watchedNetWeight = form.watch('net_weight_per_package')

  // Set selected values for display
  useEffect(() => {
    setSelectedCustomer(watchedCustomer)
    setSelectedProduct(watchedProduct)
  }, [watchedCustomer, watchedProduct])

  // Validate weight relationship
  useEffect(() => {
    if (
      watchedGrossWeight &&
      watchedNetWeight &&
      watchedNetWeight > watchedGrossWeight
    ) {
      form.setError('net_weight_per_package', {
        message: 'Net weight cannot exceed gross weight',
      })
    } else {
      form.clearErrors('net_weight_per_package')
    }
  }, [watchedGrossWeight, watchedNetWeight, form])

  // Handle form submission
  const handleSubmit = async (data: CustomerProductForm) => {
    try {
      setError(null)

      // Convert empty strings to null for optional fields
      const processedData = {
        ...data,
        customer_product_code: data.customer_product_code || null,
        unit_price_cif: data.unit_price_cif || null,
        unit_price_fob: data.unit_price_fob || null,
        standard_quantity: data.standard_quantity || null,
        unit_of_measure_id: data.unit_of_measure_id || null,
        gross_weight_per_package: data.gross_weight_per_package || null,
        net_weight_per_package: data.net_weight_per_package || null,
        quality_grade: data.quality_grade || null,
        handling_instructions: data.handling_instructions || null,
        temperature_require: data.temperature_require || null,
        vent_require: data.vent_require || null,
        shelf_life_days: data.shelf_life_days || null,
        notes: data.notes || null,
      }

      await onSubmit(processedData)
    } catch (error) {
      console.error('Form submission error:', error)
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to save customer-product relationship'
      )
    }
  }

  // Find selected customer and product details
  const selectedCustomerDetails = customers.find(c => c.id === selectedCustomer)
  const selectedProductDetails = products.find(p => p.id === selectedProduct)
  const selectedCurrencyDetails = CURRENCY_OPTIONS.find(
    c => c.value === watchedCurrency
  )

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Company and Product Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customer Selection */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-blue-200 flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                Customer Selection
              </CardTitle>
              <CardDescription className="text-slate-400">
                Select the customer company for this relationship
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="customer_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Customer Company *
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select customer company" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                        {loadingCustomers ? (
                          <div className="p-4 text-center text-slate-400">
                            <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                            Loading customers...
                          </div>
                        ) : customers.length === 0 ? (
                          <div className="p-4 text-center text-slate-400">
                            No customer companies found
                          </div>
                        ) : (
                          customers.map(customer => (
                            <SelectItem
                              key={customer.id}
                              value={customer.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Users className="h-3 w-3 text-blue-500" />
                                <span>{customer.name}</span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedCustomerDetails && (
                <div className="bg-slate-800 rounded p-3 border border-slate-600">
                  <div className="text-sm text-slate-300">
                    <div className="font-medium">
                      {selectedCustomerDetails.name}
                    </div>
                    {selectedCustomerDetails.contact_phone && (
                      <div className="text-slate-400">
                        Phone: {selectedCustomerDetails.contact_phone}
                      </div>
                    )}
                    {selectedCustomerDetails.contact_email && (
                      <div className="text-slate-400">
                        Email: {selectedCustomerDetails.contact_email}
                      </div>
                    )}
                    <Badge className="mt-2 bg-blue-600 text-white border-blue-600">
                      Customer
                    </Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Product Selection */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-green-200 flex items-center gap-2">
                <Package className="h-5 w-5 text-green-500" />
                Product Selection
              </CardTitle>
              <CardDescription className="text-slate-400">
                Select the product for this relationship
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="product_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">Product *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                        {loadingProducts ? (
                          <div className="p-4 text-center text-slate-400">
                            <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                            Loading products...
                          </div>
                        ) : products.length === 0 ? (
                          <div className="p-4 text-center text-slate-400">
                            No products found
                          </div>
                        ) : (
                          products.map(product => (
                            <SelectItem
                              key={product.id}
                              value={product.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              <div className="flex items-center space-x-2">
                                <Package2 className="h-3 w-3 text-green-500" />
                                <div>
                                  <div>{product.name}</div>
                                  {product.code && (
                                    <div className="text-xs text-slate-400">
                                      Code: {product.code}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedProductDetails && (
                <div className="bg-slate-800 rounded p-3 border border-slate-600">
                  <div className="text-sm text-slate-300">
                    <div className="font-medium">
                      {selectedProductDetails.name}
                    </div>
                    {selectedProductDetails.code && (
                      <div className="text-slate-400">
                        Code: {selectedProductDetails.code}
                      </div>
                    )}
                    {selectedProductDetails.category && (
                      <Badge
                        variant="outline"
                        className="mt-2 border-slate-500 text-slate-300"
                      >
                        {selectedProductDetails.category}
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Customer Product Code and Default Settings */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-orange-200 flex items-center gap-2">
              <Package className="h-5 w-5 text-orange-500" />
              Relationship Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="customer_product_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Customer Product Code
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Customer's internal product code"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Optional customer-specific product identifier
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="is_default"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4 bg-slate-800">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base text-slate-200 flex items-center gap-2">
                          <Star className="h-4 w-4 text-yellow-500" />
                          Default Product
                        </FormLabel>
                        <FormDescription className="text-slate-400">
                          Make this the default product for this customer
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                          className="data-[state=checked]:bg-orange-500"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4 bg-slate-800">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base text-slate-200">
                          Active Relationship
                        </FormLabel>
                        <FormDescription className="text-slate-400">
                          Controls visibility in shipment workflows
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                          className="data-[state=checked]:bg-green-500"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pricing Information */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-yellow-200 flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-yellow-500" />
              Pricing Configuration
            </CardTitle>
            <CardDescription className="text-slate-400">
              Configure pricing per KG with currency selection (at least one
              price required)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="currency_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">Currency *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700">
                        {CURRENCY_OPTIONS.map(option => (
                          <SelectItem
                            key={option.value}
                            value={option.value}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            <div className="flex items-center space-x-2">
                              <span>{option.symbol}</span>
                              <span>{option.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="unit_price_cif"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      CIF Price per KG{' '}
                      {selectedCurrencyDetails &&
                        `(${selectedCurrencyDetails.symbol})`}
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.0001"
                        min="0"
                        placeholder="0.0000"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : null
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Cost, Insurance & Freight price
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="unit_price_fob"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      FOB Price per KG{' '}
                      {selectedCurrencyDetails &&
                        `(${selectedCurrencyDetails.symbol})`}
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.0001"
                        min="0"
                        placeholder="0.0000"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : null
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Free on Board price
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Pricing validation alert */}
            {!watchedCif && !watchedFob && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className="text-slate-300">
                  At least one price (CIF or FOB) must be provided to create
                  this relationship.
                </AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="standard_quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Standard Quantity (packages)
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : null
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Typical order quantity in packages
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="unit_of_measure_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Unit of Measure
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700">
                        {loadingUnits ? (
                          <div className="p-4 text-center text-slate-400">
                            <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                            Loading units...
                          </div>
                        ) : (
                          units.map(unit => (
                            <SelectItem
                              key={unit.id}
                              value={unit.id}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              {unit.name} {unit.symbol && `(${unit.symbol})`}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Packaging & Weight Information */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-purple-200 flex items-center gap-2">
              <Weight className="h-5 w-5 text-purple-500" />
              Packaging & Weight Specifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="packaging_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Packaging Type *
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700">
                        {PACKAGING_TYPE_OPTIONS.map(option => (
                          <SelectItem
                            key={option.value}
                            value={option.value}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quality_grade"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Quality Grade
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select quality grade" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700">
                        {QUALITY_GRADES.map(grade => (
                          <SelectItem
                            key={grade}
                            value={grade}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            {grade}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="gross_weight_per_package"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Gross Weight per Package (KG)
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.0001"
                        min="0"
                        placeholder="0.0000"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : null
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Total weight including packaging
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="net_weight_per_package"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Net Weight per Package (KG)
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        step="0.0001"
                        min="0"
                        placeholder="0.0000"
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        disabled={isLoading}
                        onChange={e =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : null
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Product weight excluding packaging
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="shelf_life_days"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Shelf Life (days)
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      min="0"
                      placeholder="Enter shelf life in days"
                      className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      disabled={isLoading}
                      onChange={e =>
                        field.onChange(
                          e.target.value ? Number(e.target.value) : null
                        )
                      }
                    />
                  </FormControl>
                  <FormDescription className="text-slate-400">
                    Product shelf life in days from production
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Logistics Requirements */}
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-cyan-200 flex items-center gap-2">
              <Thermometer className="h-5 w-5 text-cyan-500" />
              Logistics & Handling Requirements
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="handling_instructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200">
                    Handling Instructions
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Special handling requirements and instructions..."
                      className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[80px]"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription className="text-slate-400">
                    Special handling requirements and instructions
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="temperature_require"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 flex items-center gap-2">
                      <Thermometer className="h-4 w-4" />
                      Temperature Requirements
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Temperature control requirements..."
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[80px]"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Temperature control and cold chain requirements
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="vent_require"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 flex items-center gap-2">
                      <Wind className="h-4 w-4" />
                      Ventilation Requirements
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Ventilation and air circulation requirements..."
                        className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[80px]"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Ventilation and air circulation requirements
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Additional Notes */}
        <Card className="bg-slate-700 border-slate-600">
          <CardContent className="pt-6">
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200">
                    Additional Notes
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Additional information and special instructions..."
                      className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500 min-h-[100px]"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription className="text-slate-400">
                    Any additional relationship information and special
                    instructions
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {customerProduct ? 'Update Relationship' : 'Create Relationship'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
