'use client'

import { useEffect, useState, useMemo, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import {
  useCustomerProductStore,
  useCustomerProductsData,
  useCustomerProductsActions,
  useCustomerProductsSelection,
  type CustomerProduct,
  type CustomerProductBulkData,
  type CustomerProductBulkResult,
} from '@/stores/customer-product-store'
import type {
  CustomerProductForm,
  CustomerProductFilter,
} from '@/lib/validations/customer-products'

// Main hook for customer-products management
export const useCustomerProductsManagement = () => {
  const data = useCustomerProductsData()
  const actions = useCustomerProductsActions()
  const selection = useCustomerProductsSelection()

  // Get filter and search state from store
  const filter = useCustomerProductStore(state => state.filter)
  const searchTerm = useCustomerProductStore(state => state.searchTerm)
  const sortBy = useCustomerProductStore(state => state.sortBy)
  const sortOrder = useCustomerProductStore(state => state.sortOrder)
  const isCreating = useCustomerProductStore(state => state.isCreating)
  const isUpdating = useCustomerProductStore(state => state.isUpdating)
  const isDeleting = useCustomerProductStore(state => state.isDeleting)
  const isBulkImporting = useCustomerProductStore(
    state => state.isBulkImporting
  )

  // Initialize data on mount
  useEffect(() => {
    actions.fetchCustomerProducts()
  }, []) // Remove actions from dependency array to prevent infinite loop

  return {
    // Data
    customerProducts: data.customerProducts,
    loading: data.loading,
    error: data.error,

    // Pagination
    currentPage: data.currentPage,
    totalPages: data.totalPages,
    totalCount: data.totalCount,
    hasNextPage: data.hasNextPage,
    hasPreviousPage: data.hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedCustomerProducts: selection.selectedCustomerProducts,
    selectedCount: selection.selectedCount,
    isSelected: selection.isSelected,
    isAllSelected: selection.isAllSelected,
    isPartiallySelected: selection.isPartiallySelected,

    // CRUD operations
    createCustomerProduct: actions.createCustomerProduct,
    updateCustomerProduct: actions.updateCustomerProduct,
    deleteCustomerProduct: actions.deleteCustomerProduct,
    bulkDeleteCustomerProducts: actions.bulkDeleteCustomerProducts,
    isCreating,
    isUpdating,
    isDeleting,

    // Default product management
    setDefaultProduct: actions.setDefaultProduct,
    getDefaultProduct: actions.getDefaultProduct,

    // Bulk operations
    bulkImportCustomerProducts: actions.bulkImportCustomerProducts,
    isBulkImporting,

    // Actions
    setFilter: actions.setFilter,
    setSearchTerm: actions.setSearchTerm,
    setSorting: actions.setSorting,
    clearFilters: actions.clearFilters,
    setPage: actions.setPage,
    nextPage: actions.nextPage,
    previousPage: actions.previousPage,
    toggleCustomerProduct: actions.toggleCustomerProduct,
    toggleAll: actions.toggleAll,
    clearSelection: actions.clearSelection,
    clearError: actions.clearError,
    refreshCustomerProducts: actions.refreshCustomerProducts,
  }
}

// Hook for shipment relationship integration (compatible with useShipmentRelationships)
export function useCustomerProducts(customerId?: string) {
  const management = useCustomerProductsManagement()

  // Filter products for specific customer
  const customerProducts = useMemo(() => {
    if (!customerId) return []
    return management.customerProducts.filter(
      rel => rel.customer_id === customerId
    )
  }, [management.customerProducts, customerId])

  const getProductsForCustomer = useCallback(
    (customerIdParam: string) => {
      return management.customerProducts.filter(
        rel => rel.customer_id === customerIdParam
      )
    },
    [management.customerProducts]
  )

  const getDefaultProductForCustomer = useCallback(
    (customerIdParam: string) => {
      return (
        management.customerProducts.find(
          rel =>
            rel.customer_id === customerIdParam &&
            rel.is_default &&
            rel.is_active
        ) || null
      )
    },
    [management.customerProducts]
  )

  return {
    customerProducts,
    loading: management.loading,
    error: management.error,
    getProductsForCustomer,
    getDefaultProductForCustomer,
  }
}

// Hook for customer selection (filtered for customers only)
export const useCustomerOptions = () => {
  const [customers, setCustomers] = useState<
    Array<{
      id: string
      name: string
      contact_phone: string | null
      contact_email: string | null
    }>
  >([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCustomers = async () => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('companies')
        .select('id, name, contact_phone, contact_email')
        .eq('company_type', 'customer')
        .eq('is_active', true)
        .order('name')

      if (error) throw error

      setCustomers(data || [])
    } catch (error) {
      console.error('Error fetching customers:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to load customers'
      )
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCustomers()
  }, [])

  return { customers, loading, error, refetch: fetchCustomers }
}

// Hook for product selection
export const useProductOptions = () => {
  const [products, setProducts] = useState<
    Array<{
      id: string
      name: string
      code: string | null
      category: string | null
    }>
  >([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchProducts = async () => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('products')
        .select('id, name, code, category')
        .eq('is_active', true)
        .order('name')

      if (error) throw error

      setProducts(data || [])
    } catch (error) {
      console.error('Error fetching products:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to load products'
      )
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [])

  return { products, loading, error, refetch: fetchProducts }
}

// Hook for units of measure selection
export const useUnitOptions = () => {
  const [units, setUnits] = useState<
    Array<{
      id: string
      name: string
      symbol: string | null
    }>
  >([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchUnits = async () => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('units_of_measure')
        .select('id, name, symbol')
        .eq('is_active', true)
        .order('name')

      if (error) throw error

      setUnits(data || [])
    } catch (error) {
      console.error('Error fetching units:', error)
      setError(error instanceof Error ? error.message : 'Failed to load units')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUnits()
  }, [])

  return { units, loading, error, refetch: fetchUnits }
}

// Hook for product categories (derived from products)
export const useProductCategories = () => {
  const [categories, setCategories] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = async () => {
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('products')
        .select('category')
        .not('category', 'is', null)
        .eq('is_active', true)

      if (error) throw error

      // Extract unique categories
      const uniqueCategories = Array.from(
        new Set(data?.map(item => item.category).filter(Boolean))
      ).sort()

      setCategories(uniqueCategories)
    } catch (error) {
      console.error('Error fetching categories:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to load categories'
      )
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  return { categories, loading, error, refetch: fetchCategories }
}

// Hook for checking if customer has existing relationships
export const useCustomerProductCheck = (customerId?: string) => {
  const [hasRelationships, setHasRelationships] = useState(false)
  const [defaultProduct, setDefaultProduct] = useState<CustomerProduct | null>(
    null
  )
  const [loading, setLoading] = useState(false)

  const checkCustomer = async (id: string) => {
    setLoading(true)

    try {
      const supabase = createClient()

      // Check for any relationships
      const { count } = await supabase
        .from('customer_products')
        .select('*', { count: 'exact', head: true })
        .eq('customer_id', id)
        .eq('is_active', true)

      setHasRelationships((count || 0) > 0)

      // Get default product if exists
      const { data: defaultData } = await supabase
        .from('customer_products')
        .select(
          `
          *,
          product:products!product_id(id, name, code, category),
          unit_of_measure:units_of_measure(id, name, symbol)
        `
        )
        .eq('customer_id', id)
        .eq('is_default', true)
        .eq('is_active', true)
        .single()

      setDefaultProduct(defaultData || null)
    } catch (error) {
      console.error('Error checking customer relationships:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (customerId) {
      checkCustomer(customerId)
    } else {
      setHasRelationships(false)
      setDefaultProduct(null)
    }
  }, [customerId])

  return {
    hasRelationships,
    defaultProduct,
    loading,
    refetch: customerId ? () => checkCustomer(customerId) : undefined,
  }
}

// Hook for real-time customer-product updates (for shipment integration)
export const useCustomerProductsRealtime = (customerId?: string) => {
  const [customerProducts, setCustomerProducts] = useState<CustomerProduct[]>(
    []
  )
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!customerId) {
      setCustomerProducts([])
      return
    }

    setLoading(true)

    const supabase = createClient()

    // Initial fetch
    const fetchCustomerProducts = async () => {
      try {
        const { data, error } = await supabase
          .from('customer_products')
          .select(
            `
            *,
            product:products!product_id(id, name, code, category),
            unit_of_measure:units_of_measure(id, name, symbol)
          `
          )
          .eq('customer_id', customerId)
          .eq('is_active', true)
          .order('is_default', { ascending: false })
          .order('product.name')

        if (error) throw error

        setCustomerProducts(data || [])
      } catch (error) {
        console.error('Error fetching customer products:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCustomerProducts()

    // Set up real-time subscription
    const subscription = supabase
      .channel(`customer_products:${customerId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'customer_products',
          filter: `customer_id=eq.${customerId}`,
        },
        () => {
          // Refetch data when changes occur
          fetchCustomerProducts()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [customerId])

  return { customerProducts, loading }
}

// Hook for bulk import operations
export function useCustomerProductBulkImport() {
  const { bulkImportCustomerProducts, isBulkImporting } =
    useCustomerProductsManagement()

  // Parse CSV line with proper handling of quoted fields
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    let i = 0

    while (i < line.length) {
      const char = line[i]
      const nextChar = line[i + 1]

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote inside quoted field
          current += '"'
          i += 2
          continue
        } else {
          // Start or end of quoted field
          inQuotes = !inQuotes
          i++
          continue
        }
      }

      if (char === ',' && !inQuotes) {
        // Field separator outside quotes
        result.push(current.trim())
        current = ''
        i++
        continue
      }

      // Regular character
      current += char
      i++
    }

    // Add the last field
    result.push(current.trim())
    return result
  }

  const importFromCSV = async (
    csvData: string
  ): Promise<CustomerProductBulkResult> => {
    try {
      // Parse CSV data
      const lines = csvData.trim().split('\n')
      const headers = parseCSVLine(lines[0]).map(h =>
        h.toLowerCase().replace(/^["']|["']$/g, '')
      )

      // Validate headers
      const requiredHeaders = [
        'customer_name',
        'product_name',
        'packaging_type',
      ]
      const hasRequiredHeaders = requiredHeaders.every(required =>
        headers.includes(required)
      )

      if (!hasRequiredHeaders) {
        throw new Error(
          'CSV must contain columns: customer_name, product_name, packaging_type'
        )
      }

      // Parse rows
      const data: CustomerProductBulkData[] = []
      for (let i = 1; i < lines.length; i++) {
        const values = parseCSVLine(lines[i]).map(v =>
          v.replace(/^["']|["']$/g, '')
        )
        if (values.length < 3 || !values.some(v => v)) continue // Skip empty rows

        const row: CustomerProductBulkData = {
          customer_name: values[headers.indexOf('customer_name')] || '',
          product_name: values[headers.indexOf('product_name')] || '',
          packaging_type: (values[headers.indexOf('packaging_type')] ||
            'Bag') as 'Bag' | 'Plastic Basket' | 'Carton',
          customer_product_code: null,
          unit_price_cif: null,
          unit_price_fob: null,
          currency_code: 'USD',
          standard_quantity: null,
          gross_weight_per_package: null,
          net_weight_per_package: null,
          quality_grade: null,
          handling_instructions: null,
          temperature_require: null,
          vent_require: null,
          shelf_life_days: null,
          is_default: false,
          notes: null,
        }

        // Parse optional columns
        const customerCodeIndex = headers.indexOf('customer_product_code')
        if (customerCodeIndex >= 0 && values[customerCodeIndex]) {
          row.customer_product_code = values[customerCodeIndex]
        }

        const cifIndex = headers.indexOf('unit_price_cif')
        if (cifIndex >= 0 && values[cifIndex]) {
          const cifPrice = parseFloat(values[cifIndex])
          if (!isNaN(cifPrice)) row.unit_price_cif = cifPrice
        }

        const fobIndex = headers.indexOf('unit_price_fob')
        if (fobIndex >= 0 && values[fobIndex]) {
          const fobPrice = parseFloat(values[fobIndex])
          if (!isNaN(fobPrice)) row.unit_price_fob = fobPrice
        }

        const currencyIndex = headers.indexOf('currency_code')
        if (currencyIndex >= 0 && values[currencyIndex]) {
          const currency = values[currencyIndex].toUpperCase()
          if (['THB', 'CNY', 'USD', 'EUR'].includes(currency)) {
            row.currency_code = currency as 'THB' | 'CNY' | 'USD' | 'EUR'
          }
        }

        const quantityIndex = headers.indexOf('standard_quantity')
        if (quantityIndex >= 0 && values[quantityIndex]) {
          const quantity = parseFloat(values[quantityIndex])
          if (!isNaN(quantity)) row.standard_quantity = quantity
        }

        const grossWeightIndex = headers.indexOf('gross_weight_per_package')
        if (grossWeightIndex >= 0 && values[grossWeightIndex]) {
          const weight = parseFloat(values[grossWeightIndex])
          if (!isNaN(weight)) row.gross_weight_per_package = weight
        }

        const netWeightIndex = headers.indexOf('net_weight_per_package')
        if (netWeightIndex >= 0 && values[netWeightIndex]) {
          const weight = parseFloat(values[netWeightIndex])
          if (!isNaN(weight)) row.net_weight_per_package = weight
        }

        const qualityIndex = headers.indexOf('quality_grade')
        if (qualityIndex >= 0 && values[qualityIndex]) {
          row.quality_grade = values[qualityIndex]
        }

        const handlingIndex = headers.indexOf('handling_instructions')
        if (handlingIndex >= 0 && values[handlingIndex]) {
          row.handling_instructions = values[handlingIndex]
        }

        const tempIndex = headers.indexOf('temperature_require')
        if (tempIndex >= 0 && values[tempIndex]) {
          row.temperature_require = values[tempIndex]
        }

        const ventIndex = headers.indexOf('vent_require')
        if (ventIndex >= 0 && values[ventIndex]) {
          row.vent_require = values[ventIndex]
        }

        const shelfLifeIndex = headers.indexOf('shelf_life_days')
        if (shelfLifeIndex >= 0 && values[shelfLifeIndex]) {
          const days = parseInt(values[shelfLifeIndex])
          if (!isNaN(days)) row.shelf_life_days = days
        }

        const isDefaultIndex = headers.indexOf('is_default')
        if (isDefaultIndex >= 0 && values[isDefaultIndex]) {
          const defaultValue = values[isDefaultIndex].toLowerCase()
          row.is_default = ['true', '1', 'yes', 'y'].includes(defaultValue)
        }

        const notesIndex = headers.indexOf('notes')
        if (notesIndex >= 0 && values[notesIndex]) {
          row.notes = values[notesIndex]
        }

        data.push(row)
      }

      return await bulkImportCustomerProducts(data)
    } catch (error) {
      console.error('CSV import error:', error)
      throw error
    }
  }

  const generateCSVTemplate = (): string => {
    const headers = [
      'customer_name',
      'product_name',
      'customer_product_code',
      'unit_price_cif',
      'unit_price_fob',
      'currency_code',
      'standard_quantity',
      'gross_weight_per_package',
      'net_weight_per_package',
      'quality_grade',
      'packaging_type',
      'handling_instructions',
      'temperature_require',
      'vent_require',
      'shelf_life_days',
      'is_default',
      'notes',
    ]
    const sampleRow = [
      'Customer Company Ltd',
      'Product Name',
      'CUST-PROD-001',
      '125.50',
      '120.00',
      'USD',
      '1000',
      '25.5000',
      '23.0000',
      'Grade A',
      'Bag',
      'Handle with care',
      'Cool and dry',
      'Ventilated',
      '365',
      'false',
      'Optional notes',
    ]

    return [headers.join(','), sampleRow.join(',')].join('\n')
  }

  return {
    importFromCSV,
    generateCSVTemplate,
    isBulkImporting,
  }
}

// Hook for customer-product relationship validation
export function useCustomerProductValidation() {
  const validateBulkData = (
    data: CustomerProductBulkData[]
  ): Array<{
    row: number
    errors: string[]
  }> => {
    return data
      .map((item, index) => {
        const errors: string[] = []

        if (!item.customer_name?.trim()) {
          errors.push('Customer name is required')
        }

        if (!item.product_name?.trim()) {
          errors.push('Product name is required')
        }

        if (item.customer_name === item.product_name) {
          errors.push('Customer and product cannot have the same name')
        }

        if (!item.unit_price_cif && !item.unit_price_fob) {
          errors.push('At least one price (CIF or FOB) must be provided')
        }

        if (item.unit_price_cif && item.unit_price_cif < 0) {
          errors.push('CIF price must be non-negative')
        }

        if (item.unit_price_fob && item.unit_price_fob < 0) {
          errors.push('FOB price must be non-negative')
        }

        if (
          item.gross_weight_per_package &&
          item.net_weight_per_package &&
          item.net_weight_per_package > item.gross_weight_per_package
        ) {
          errors.push('Net weight cannot exceed gross weight')
        }

        if (
          !['Bag', 'Plastic Basket', 'Carton'].includes(item.packaging_type)
        ) {
          errors.push('Packaging type must be Bag, Plastic Basket, or Carton')
        }

        if (
          item.currency_code &&
          !['THB', 'CNY', 'USD', 'EUR'].includes(item.currency_code)
        ) {
          errors.push('Currency code must be THB, CNY, USD, or EUR')
        }

        return {
          row: index + 1,
          errors,
        }
      })
      .filter(result => result.errors.length > 0)
  }

  return {
    validateBulkData,
  }
}
