'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { MapPin, Navigation, Info, X } from 'lucide-react'
import type { Coordinates } from '@/lib/validations/companies'

interface GPSCoordinateInputProps {
  value?: Coordinates
  onChange: (coords?: Coordinates) => void
  error?: string
}

export function GPSCoordinateInput({
  value,
  onChange,
  error,
}: GPSCoordinateInputProps) {
  const [manualEntry, setManualEntry] = useState(false)
  const [latitudeStr, setLatitudeStr] = useState('')
  const [longitudeStr, setLongitudeStr] = useState('')
  const [locationError, setLocationError] = useState<string | null>(null)
  const [isGettingLocation, setIsGettingLocation] = useState(false)

  // Initialize string values from coordinates
  useEffect(() => {
    if (value?.lat !== undefined && value?.lng !== undefined) {
      setLatitudeStr(value.lat.toString())
      setLongitudeStr(value.lng.toString())
      setManualEntry(true)
    } else {
      setLatitudeStr('')
      setLongitudeStr('')
    }
  }, [value])

  // Get current location using browser geolocation
  const getCurrentLocation = async () => {
    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by this browser')
      return
    }

    setIsGettingLocation(true)
    setLocationError(null)

    try {
      const position = await new Promise<GeolocationPosition>(
        (resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000,
          })
        }
      )

      const coords = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      }

      setLatitudeStr(coords.lat.toString())
      setLongitudeStr(coords.lng.toString())
      onChange(coords)
      setManualEntry(true)
    } catch (err) {
      const error = err as GeolocationPositionError
      let message = 'Failed to get location'

      switch (error.code) {
        case error.PERMISSION_DENIED:
          message = 'Location access denied by user'
          break
        case error.POSITION_UNAVAILABLE:
          message = 'Location information is unavailable'
          break
        case error.TIMEOUT:
          message = 'Location request timed out'
          break
        default:
          message = 'An unknown error occurred'
          break
      }

      setLocationError(message)
    } finally {
      setIsGettingLocation(false)
    }
  }

  // Update coordinates when manual input changes
  const updateCoordinates = (latStr?: string, lngStr?: string) => {
    // Use provided values or fall back to state
    const currentLatStr = latStr !== undefined ? latStr : latitudeStr
    const currentLngStr = lngStr !== undefined ? lngStr : longitudeStr
    
    // Clear previous error
    setLocationError(null)
    
    // If either field is empty, clear coordinates
    if (!currentLatStr.trim() || !currentLngStr.trim()) {
      onChange(undefined)
      return
    }

    const lat = parseFloat(currentLatStr.trim())
    const lng = parseFloat(currentLngStr.trim())

    // If parsing failed, clear coordinates but don't show error yet
    if (isNaN(lat) || isNaN(lng)) {
      onChange(undefined)
      return
    }

    // Validate ranges
    if (lat < -90 || lat > 90) {
      setLocationError('Latitude must be between -90 and 90')
      onChange(undefined)
      return
    }

    if (lng < -180 || lng > 180) {
      setLocationError('Longitude must be between -180 and 180')
      onChange(undefined)
      return
    }

    // Valid coordinates
    onChange({ lat, lng })
  }

  // Handle input changes (only update state, no validation)
  const handleLatitudeChange = (value: string) => {
    setLatitudeStr(value)
  }

  const handleLongitudeChange = (value: string) => {
    setLongitudeStr(value)
  }

  // Handle blur events (when user leaves input field)
  const handleLatitudeBlur = () => {
    updateCoordinates(latitudeStr, longitudeStr)
  }

  const handleLongitudeBlur = () => {
    updateCoordinates(latitudeStr, longitudeStr)
  }

  // Handle key press events (when user presses Enter)
  const handleLatitudeKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      updateCoordinates(latitudeStr, longitudeStr)
    }
  }

  const handleLongitudeKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      updateCoordinates(latitudeStr, longitudeStr)
    }
  }

  // Clear coordinates
  const clearCoordinates = () => {
    setLatitudeStr('')
    setLongitudeStr('')
    setLocationError(null)
    onChange(undefined)
    setManualEntry(false)
  }

  // Format coordinates for display
  const formatCoordinates = (lat: number, lng: number) => {
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Navigation className="h-4 w-4 text-orange-500" />
          <Label className="text-slate-200">GPS Coordinates</Label>
        </div>
        {value && (
          <Badge
            variant="secondary"
            className="bg-green-500/20 text-green-300 border-green-400"
          >
            <MapPin className="h-3 w-3 mr-1" />
            Set
          </Badge>
        )}
      </div>

      {/* Current coordinates display */}
      {value && !manualEntry && (
        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="text-sm text-slate-300 font-mono">
            {formatCoordinates(value.lat, value.lng)}
          </div>
          <div className="flex space-x-2 mt-2">
            <Button
              type="button"
              size="sm"
              variant="outline"
              onClick={() => setManualEntry(true)}
              className="bg-slate-600 border-slate-500 text-slate-200 hover:bg-slate-500"
            >
              Edit
            </Button>
            <Button
              type="button"
              size="sm"
              variant="outline"
              onClick={clearCoordinates}
              className="bg-slate-600 border-slate-500 text-slate-200 hover:bg-slate-500"
            >
              <X className="h-3 w-3" />
              Clear
            </Button>
          </div>
        </div>
      )}

      {/* Manual entry controls */}
      {!value || manualEntry ? (
        <div className="space-y-4">
          {/* Get current location button */}
          <div className="flex space-x-2">
            <Button
              type="button"
              size="sm"
              onClick={getCurrentLocation}
              disabled={isGettingLocation}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              <Navigation className="h-4 w-4 mr-2" />
              {isGettingLocation
                ? 'Getting Location...'
                : 'Use Current Location'}
            </Button>

            {value && (
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={clearCoordinates}
                className="bg-slate-600 border-slate-500 text-slate-200 hover:bg-slate-500"
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            )}
          </div>

          {/* Manual coordinate input */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-slate-200 text-sm">Latitude</Label>
              <Input
                type="text"
                placeholder="13.7563"
                value={latitudeStr}
                onChange={e => handleLatitudeChange(e.target.value)}
                onBlur={handleLatitudeBlur}
                onKeyPress={handleLatitudeKeyPress}
                className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
              <div className="text-xs text-slate-400">Range: -90 to 90</div>
            </div>

            <div className="space-y-2">
              <Label className="text-slate-200 text-sm">Longitude</Label>
              <Input
                type="text"
                placeholder="100.5018"
                value={longitudeStr}
                onChange={e => handleLongitudeChange(e.target.value)}
                onBlur={handleLongitudeBlur}
                onKeyPress={handleLongitudeKeyPress}
                className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
              <div className="text-xs text-slate-400">Range: -180 to 180</div>
            </div>
          </div>

          {/* Current coordinates preview */}
          {value && (
            <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
              <div className="flex items-center space-x-2 mb-1">
                <MapPin className="h-3 w-3 text-orange-500" />
                <span className="text-xs text-slate-300 font-medium">
                  Current Coordinates
                </span>
              </div>
              <div className="text-sm text-slate-200 font-mono">
                {formatCoordinates(value.lat, value.lng)}
              </div>
            </div>
          )}
        </div>
      ) : null}

      {/* Error messages */}
      {(error || locationError) && (
        <Alert variant="destructive">
          <Info className="h-4 w-4" />
          <AlertDescription>{error || locationError}</AlertDescription>
        </Alert>
      )}

      {/* Help text */}
      <div className="text-xs text-slate-400 space-y-1">
        <p>• You can use your current location or enter coordinates manually</p>
        <p>
          • Coordinates should be in decimal degrees format (e.g., 13.7563,
          100.5018)
        </p>
        <p>• This will help with location-based features and mapping</p>
      </div>
    </div>
  )
}
