'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Activity,
  Target,
  Gauge,
  Clock,
  TrendingUp,
  TrendingDown,
  BarChart3,
  FileDown,
  Calendar,
  Award,
  Users,
  Package,
  Truck,
  Star,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Timer,
  Zap,
  Download,
  ArrowUpRight,
  ArrowDownRight,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react'

// Mock performance data
const mockKPIMetrics = {
  onTimeDelivery: { value: 92.5, target: 95, trend: '+2.3%' },
  customerSatisfaction: { value: 88.7, target: 90, trend: '+1.8%' },
  operationalEfficiency: { value: 85.2, target: 88, trend: '+4.2%' },
  costPerShipment: { value: 1847, target: 1750, trend: '-3.5%' },
  utilizationRate: { value: 78.9, target: 80, trend: '****%' },
  qualityScore: { value: 94.3, target: 95, trend: '+0.8%' }
}

const mockPerformanceTrends = [
  { metric: 'On-Time Delivery', current: 92.5, lastMonth: 90.2, target: 95, status: 'improving' },
  { metric: 'Customer Satisfaction', current: 88.7, lastMonth: 86.9, target: 90, status: 'improving' },
  { metric: 'Operational Efficiency', current: 85.2, lastMonth: 81.0, target: 88, status: 'improving' },
  { metric: 'Cost Control', current: 82.3, lastMonth: 85.8, target: 85, status: 'declining' },
  { metric: 'Quality Score', current: 94.3, lastMonth: 93.5, target: 95, status: 'improving' },
  { metric: 'Resource Utilization', current: 78.9, lastMonth: 76.8, target: 80, status: 'improving' }
]

const mockTeamPerformance = [
  { name: 'Logistics Team A', efficiency: 94.2, onTime: 96.8, satisfaction: 92.3, status: 'excellent' },
  { name: 'Logistics Team B', efficiency: 87.5, onTime: 89.2, satisfaction: 86.7, status: 'good' },
  { name: 'Customer Service', efficiency: 91.8, onTime: 94.5, satisfaction: 95.1, status: 'excellent' },
  { name: 'Operations Team', efficiency: 83.4, onTime: 85.6, satisfaction: 84.2, status: 'needs_improvement' },
  { name: 'Quality Control', efficiency: 96.7, onTime: 98.1, satisfaction: 93.8, status: 'excellent' }
]

const mockOperationalMetrics = [
  { metric: 'Average Processing Time', value: '2.3 hours', change: '-15 min', trend: 'down' },
  { metric: 'Documentation Accuracy', value: '97.8%', change: '****%', trend: 'up' },
  { metric: 'Route Optimization', value: '89.4%', change: '****%', trend: 'up' },
  { metric: 'Fuel Efficiency', value: '94.2%', change: '****%', trend: 'up' },
  { metric: 'Capacity Utilization', value: '78.9%', change: '****%', trend: 'up' },
  { metric: 'Customer Response Time', value: '1.2 hours', change: '-0.3 hours', trend: 'down' }
]

const mockAlerts = [
  { type: 'warning', message: 'Cost per shipment trending above target by 5.5%', priority: 'medium' },
  { type: 'success', message: 'Customer satisfaction improved by 1.8% this month', priority: 'low' },
  { type: 'error', message: 'Team B efficiency below target - requires attention', priority: 'high' },
  { type: 'info', message: 'Q3 performance review scheduled for next week', priority: 'low' }
]

const mockCustomerFeedback = [
  { score: 4.8, count: 156, category: 'Delivery Speed', sentiment: 'positive' },
  { score: 4.6, count: 142, category: 'Product Quality', sentiment: 'positive' },
  { score: 4.4, count: 128, category: 'Communication', sentiment: 'positive' },
  { score: 3.9, count: 89, category: 'Pricing', sentiment: 'neutral' },
  { score: 4.7, count: 134, category: 'Customer Service', sentiment: 'positive' }
]

const StatCard = ({ title, value, target, trend, icon: Icon, color, unit = '' }: {
  title: string
  value: number
  target?: number
  trend?: string
  icon: any
  color: string
  unit?: string
}) => {
  const percentage = target ? (value / target) * 100 : 0
  const isOnTarget = !target || value >= target * 0.95
  const isAboveTarget = target && value >= target
  const isPositiveTrend = trend?.startsWith('+')
  const isNegativeTrend = trend?.startsWith('-')

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex-1">
            <p className="text-slate-400 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-white mt-1">
              {unit === '%' ? `${value}%` : unit === '$' ? `$${value.toLocaleString()}` : value}
              {unit && unit !== '%' && unit !== '$' && ` ${unit}`}
            </p>
            {target && (
              <p className="text-xs text-slate-500 mt-1">
                Target: {unit === '%' ? `${target}%` : unit === '$' ? `$${target.toLocaleString()}` : target}
                {unit && unit !== '%' && unit !== '$' && ` ${unit}`}
              </p>
            )}
          </div>
          <Icon className={`w-8 h-8 ${color}`} />
        </div>

        {target && (
          <div className="mb-3">
            <div className="flex items-center justify-between text-xs mb-1">
              <span className="text-slate-400">Progress</span>
              <span className={isAboveTarget ? 'text-green-500' : isOnTarget ? 'text-yellow-500' : 'text-red-500'}>
                {percentage.toFixed(1)}%
              </span>
            </div>
            <Progress
              value={Math.min(percentage, 100)}
              className="h-2"
            />
          </div>
        )}

        {trend && (
          <div className="flex items-center">
            {isPositiveTrend && <ArrowUpRight className="w-3 h-3 text-green-500 mr-1" />}
            {isNegativeTrend && <ArrowDownRight className="w-3 h-3 text-red-500 mr-1" />}
            <span className={`text-xs font-medium ${
              isPositiveTrend ? 'text-green-500' : isNegativeTrend ? 'text-red-500' : 'text-slate-400'
            }`}>
              {trend} vs last month
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

const PerformanceBadge = ({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'excellent':
        return { color: 'bg-green-500 text-white', label: 'Excellent' }
      case 'good':
        return { color: 'bg-blue-500 text-white', label: 'Good' }
      case 'needs_improvement':
        return { color: 'bg-yellow-500 text-black', label: 'Needs Improvement' }
      case 'poor':
        return { color: 'bg-red-500 text-white', label: 'Poor' }
      default:
        return { color: 'bg-slate-500 text-white', label: 'Unknown' }
    }
  }

  const config = getStatusConfig(status)
  return <Badge className={`${config.color} text-xs`}>{config.label}</Badge>
}

const AlertItem = ({ alert }: { alert: any }) => {
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <Activity className="w-4 h-4 text-blue-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500'
      case 'medium':
        return 'border-l-yellow-500'
      case 'low':
        return 'border-l-green-500'
      default:
        return 'border-l-slate-500'
    }
  }

  return (
    <div className={`flex items-start space-x-3 p-3 rounded-lg bg-slate-700 border-l-4 ${getPriorityColor(alert.priority)}`}>
      <div className="flex-shrink-0 mt-0.5">
        {getAlertIcon(alert.type)}
      </div>
      <div className="flex-1">
        <p className="text-sm text-white">{alert.message}</p>
        <p className="text-xs text-slate-400 mt-1 capitalize">{alert.priority} Priority</p>
      </div>
    </div>
  )
}

export default function PerformanceReportsPage() {
  const [dateRange, setDateRange] = useState('30')
  const [performanceLevel, setPerformanceLevel] = useState('all')
  const [department, setDepartment] = useState('all')

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Performance Reports</h1>
          <p className="text-slate-400">KPI tracking and operational performance analytics</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
            <FileDown className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button className="bg-orange-500 hover:bg-orange-600 text-white">
            <Download className="w-4 h-4 mr-2" />
            Download KPI
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700 mb-6">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-slate-400" />
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last quarter</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-slate-400" />
              <Select value={department} onValueChange={setDepartment}>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="logistics">Logistics</SelectItem>
                  <SelectItem value="operations">Operations</SelectItem>
                  <SelectItem value="customer_service">Customer Service</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-slate-400" />
              <Select value={performanceLevel} onValueChange={setPerformanceLevel}>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Performance</SelectItem>
                  <SelectItem value="excellent">Excellent</SelectItem>
                  <SelectItem value="good">Good</SelectItem>
                  <SelectItem value="needs_improvement">Needs Improvement</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <StatCard
          title="On-Time Delivery"
          value={mockKPIMetrics.onTimeDelivery.value}
          target={mockKPIMetrics.onTimeDelivery.target}
          trend={mockKPIMetrics.onTimeDelivery.trend}
          icon={Timer}
          color="text-green-500"
          unit="%"
        />
        <StatCard
          title="Customer Satisfaction"
          value={mockKPIMetrics.customerSatisfaction.value}
          target={mockKPIMetrics.customerSatisfaction.target}
          trend={mockKPIMetrics.customerSatisfaction.trend}
          icon={Star}
          color="text-yellow-500"
          unit="%"
        />
        <StatCard
          title="Operational Efficiency"
          value={mockKPIMetrics.operationalEfficiency.value}
          target={mockKPIMetrics.operationalEfficiency.target}
          trend={mockKPIMetrics.operationalEfficiency.trend}
          icon={Zap}
          color="text-blue-500"
          unit="%"
        />
        <StatCard
          title="Cost Per Shipment"
          value={mockKPIMetrics.costPerShipment.value}
          target={mockKPIMetrics.costPerShipment.target}
          trend={mockKPIMetrics.costPerShipment.trend}
          icon={BarChart3}
          color="text-red-500"
          unit="$"
        />
        <StatCard
          title="Utilization Rate"
          value={mockKPIMetrics.utilizationRate.value}
          target={mockKPIMetrics.utilizationRate.target}
          trend={mockKPIMetrics.utilizationRate.trend}
          icon={Gauge}
          color="text-purple-500"
          unit="%"
        />
        <StatCard
          title="Quality Score"
          value={mockKPIMetrics.qualityScore.value}
          target={mockKPIMetrics.qualityScore.target}
          trend={mockKPIMetrics.qualityScore.trend}
          icon={Award}
          color="text-orange-500"
          unit="%"
        />
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="bg-slate-800 border-slate-700">
          <TabsTrigger value="overview" className="data-[state=active]:bg-orange-500">Performance Overview</TabsTrigger>
          <TabsTrigger value="teams" className="data-[state=active]:bg-orange-500">Team Performance</TabsTrigger>
          <TabsTrigger value="operations" className="data-[state=active]:bg-orange-500">Operational Metrics</TabsTrigger>
          <TabsTrigger value="feedback" className="data-[state=active]:bg-orange-500">Customer Feedback</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Performance Trends */}
            <Card className="lg:col-span-2 bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-green-500" />
                  Performance Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockPerformanceTrends.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-slate-700">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-slate-300 font-medium">{item.metric}</span>
                          <Badge className={
                            item.status === 'improving' ? 'bg-green-500 text-white' :
                            item.status === 'declining' ? 'bg-red-500 text-white' :
                            'bg-yellow-500 text-black'
                          }>
                            {item.status}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-400">Current: {item.current}%</span>
                          <span className="text-slate-400">Target: {item.target}%</span>
                        </div>
                        <Progress value={(item.current / item.target) * 100} className="mt-2 h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Alerts */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2 text-yellow-500" />
                  Performance Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {mockAlerts.map((alert, index) => (
                    <AlertItem key={index} alert={alert} />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="teams" className="space-y-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Users className="w-5 h-5 mr-2 text-blue-500" />
                Team Performance Scorecard
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockTeamPerformance.map((team, index) => (
                  <div key={index} className="p-4 rounded-lg bg-slate-700 hover:bg-slate-600 transition-colors">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white font-medium">{team.name}</h4>
                      <PerformanceBadge status={team.status} />
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <p className="text-slate-400 text-xs">Efficiency</p>
                        <p className="text-white font-bold">{team.efficiency}%</p>
                        <Progress value={team.efficiency} className="mt-1 h-1" />
                      </div>
                      <div>
                        <p className="text-slate-400 text-xs">On-Time Delivery</p>
                        <p className="text-white font-bold">{team.onTime}%</p>
                        <Progress value={team.onTime} className="mt-1 h-1" />
                      </div>
                      <div>
                        <p className="text-slate-400 text-xs">Satisfaction</p>
                        <p className="text-white font-bold">{team.satisfaction}%</p>
                        <Progress value={team.satisfaction} className="mt-1 h-1" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operations" className="space-y-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Activity className="w-5 h-5 mr-2 text-purple-500" />
                Operational Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {mockOperationalMetrics.map((metric, index) => (
                  <div key={index} className="p-4 rounded-lg bg-slate-700">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-slate-300 font-medium">{metric.metric}</h4>
                      <div className="flex items-center">
                        {metric.trend === 'up' ? (
                          <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                        ) : (
                          <TrendingDown className="w-4 h-4 text-green-500 mr-1" />
                        )}
                        <span className="text-green-500 text-sm">{metric.change}</span>
                      </div>
                    </div>
                    <p className="text-2xl font-bold text-white">{metric.value}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="feedback" className="space-y-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Star className="w-5 h-5 mr-2 text-yellow-500" />
                Customer Feedback Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockCustomerFeedback.map((feedback, index) => (
                  <div key={index} className="flex items-center justify-between p-4 rounded-lg bg-slate-700">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        {feedback.sentiment === 'positive' ? (
                          <ThumbsUp className="w-5 h-5 text-green-500 mr-2" />
                        ) : feedback.sentiment === 'negative' ? (
                          <ThumbsDown className="w-5 h-5 text-red-500 mr-2" />
                        ) : (
                          <Activity className="w-5 h-5 text-yellow-500 mr-2" />
                        )}
                        <div>
                          <p className="text-white font-medium">{feedback.category}</p>
                          <p className="text-slate-400 text-sm">{feedback.count} responses</p>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-500 mr-1" />
                        <span className="text-white font-bold">{feedback.score}</span>
                      </div>
                      <Progress value={(feedback.score / 5) * 100} className="mt-1 w-20 h-1" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}