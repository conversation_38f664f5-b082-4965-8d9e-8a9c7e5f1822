/**
 * PDF Generation Service Tests
 * Story 5.2: Automated Document Generation Engine
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { PDFGenerationService } from '../pdf-generation-service'
import type { DocumentTemplate } from '@/types/document-template'
import type { PDFGenerationOptions } from '@/types/document-generation'

// Mock data
const mockTemplate: DocumentTemplate = {
  id: 'template-1',
  template_name: 'Test Template',
  document_type: 'booking_confirmation',
  version: '1.0',
  template_content: '<h1>Test Document</h1><p>Content here</p>',
  template_data: {},
  template_styles: 'body { font-family: Arial, sans-serif; }',
  page_size: 'A4',
  page_orientation: 'portrait',
  margin_top: 20,
  margin_bottom: 20,
  margin_left: 20,
  margin_right: 20,
  language: 'en',
  currency_format: 'USD',
  date_format: 'YYYY-MM-DD',
  number_format: 'en-US',
  description: 'Test template',
  usage_notes: null,
  required_fields: [],
  is_active: true,
  is_default: false,
  created_by: 'user-1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

describe('PDFGenerationService', () => {
  let service: PDFGenerationService

  beforeEach(() => {
    service = new PDFGenerationService()
  })

  describe('generatePDF', () => {
    it('should generate a PDF buffer from content and template', async () => {
      const content = '<h1>Test Document</h1><p>This is test content</p>'
      
      const result = await service.generatePDF(content, mockTemplate)
      
      expect(result).toBeInstanceOf(Buffer)
      expect(result.length).toBeGreaterThan(0)
      
      // Check that the PDF contains expected PDF markers
      const pdfString = result.toString()
      expect(pdfString).toContain('%PDF')
      expect(pdfString).toContain('%%EOF')
    })

    it('should handle empty content', async () => {
      const content = ''
      
      const result = await service.generatePDF(content, mockTemplate)
      
      expect(result).toBeInstanceOf(Buffer)
      expect(result.length).toBeGreaterThan(0)
    })

    it('should handle complex HTML content', async () => {
      const content = `
        <div>
          <h1>Shipment Document</h1>
          <table>
            <tr>
              <th>Product</th>
              <th>Quantity</th>
              <th>Price</th>
            </tr>
            <tr>
              <td>Mangoes</td>
              <td>1000 kg</td>
              <td>$2.50/kg</td>
            </tr>
          </table>
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="test" />
        </div>
      `
      
      const result = await service.generatePDF(content, mockTemplate)
      
      expect(result).toBeInstanceOf(Buffer)
      expect(result.length).toBeGreaterThan(0)
    })
  })

  describe('generatePDFWithOptions', () => {
    it('should generate PDF with custom metadata', async () => {
      const options: PDFGenerationOptions = {
        template: mockTemplate,
        content: '<h1>Test Document</h1>',
        metadata: {
          title: 'Custom Document Title',
          author: 'Test Author',
          subject: 'Test Subject',
          creator: 'Test Creator'
        }
      }
      
      const result = await service.generatePDFWithOptions(options)
      
      expect(result).toBeInstanceOf(Buffer)
      expect(result.length).toBeGreaterThan(0)
    })

    it('should use default metadata when not provided', async () => {
      const options: PDFGenerationOptions = {
        template: mockTemplate,
        content: '<h1>Test Document</h1>'
      }
      
      const result = await service.generatePDFWithOptions(options)
      
      expect(result).toBeInstanceOf(Buffer)
    })
  })

  describe('validateRequirements', () => {
    it('should validate template requirements successfully', () => {
      const result = service.validateRequirements(mockTemplate)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should detect missing template content', () => {
      const invalidTemplate: DocumentTemplate = {
        ...mockTemplate,
        template_content: ''
      }
      
      const result = service.validateRequirements(invalidTemplate)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Template content is required')
    })

    it('should detect missing page size', () => {
      const invalidTemplate: DocumentTemplate = {
        ...mockTemplate,
        page_size: '' as any
      }
      
      const result = service.validateRequirements(invalidTemplate)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Page size is required')
    })

    it('should detect missing page orientation', () => {
      const invalidTemplate: DocumentTemplate = {
        ...mockTemplate,
        page_orientation: '' as any
      }
      
      const result = service.validateRequirements(invalidTemplate)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Page orientation is required')
    })

    it('should detect negative margins', () => {
      const invalidTemplate: DocumentTemplate = {
        ...mockTemplate,
        margin_top: -10,
        margin_left: -5
      }
      
      const result = service.validateRequirements(invalidTemplate)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Margins must be non-negative')
    })

    it('should handle multiple validation errors', () => {
      const invalidTemplate: DocumentTemplate = {
        ...mockTemplate,
        template_content: '',
        page_size: '' as any,
        margin_top: -10
      }
      
      const result = service.validateRequirements(invalidTemplate)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(1)
      expect(result.errors).toContain('Template content is required')
      expect(result.errors).toContain('Page size is required')
      expect(result.errors).toContain('Margins must be non-negative')
    })
  })

  describe('content parsing', () => {
    it('should extract paragraphs from HTML content', () => {
      // This tests private methods indirectly through the main generatePDF method
      const contentWithParagraphs = `
        <p>First paragraph</p>
        <p>Second paragraph with <strong>bold text</strong></p>
        <p>Third paragraph</p>
      `
      
      // Since extractParagraphs is private, we test it through generatePDF
      expect(async () => {
        const result = await service.generatePDF(contentWithParagraphs, mockTemplate)
        expect(result).toBeInstanceOf(Buffer)
      }).not.toThrow()
    })

    it('should extract tables from HTML content', () => {
      const contentWithTable = `
        <table>
          <tr>
            <th>Header 1</th>
            <th>Header 2</th>
          </tr>
          <tr>
            <td>Cell 1</td>
            <td>Cell 2</td>
          </tr>
        </table>
      `
      
      expect(async () => {
        const result = await service.generatePDF(contentWithTable, mockTemplate)
        expect(result).toBeInstanceOf(Buffer)
      }).not.toThrow()
    })

    it('should extract images from HTML content', () => {
      const contentWithImage = `
        <div>
          <img src="https://example.com/image.jpg" alt="Test Image" width="200" height="100" />
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" />
        </div>
      `
      
      expect(async () => {
        const result = await service.generatePDF(contentWithImage, mockTemplate)
        expect(result).toBeInstanceOf(Buffer)
      }).not.toThrow()
    })
  })

  describe('style processing', () => {
    it('should handle templates with custom CSS styles', async () => {
      const styledTemplate: DocumentTemplate = {
        ...mockTemplate,
        template_styles: `
          .header { 
            font-size: 18px; 
            font-weight: bold; 
            color: #333; 
          }
          .content { 
            font-size: 12px; 
            line-height: 1.5; 
          }
          .table { 
            border-collapse: collapse; 
            width: 100%; 
          }
        `
      }
      
      const content = `
        <div class="header">Document Header</div>
        <div class="content">Document content here</div>
      `
      
      const result = await service.generatePDF(content, styledTemplate)
      
      expect(result).toBeInstanceOf(Buffer)
      expect(result.length).toBeGreaterThan(0)
    })

    it('should handle templates without custom styles', async () => {
      const unstyledTemplate: DocumentTemplate = {
        ...mockTemplate,
        template_styles: null
      }
      
      const content = '<h1>Simple Document</h1><p>Basic content</p>'
      
      const result = await service.generatePDF(content, unstyledTemplate)
      
      expect(result).toBeInstanceOf(Buffer)
    })

    it('should handle malformed CSS styles gracefully', async () => {
      const malformedTemplate: DocumentTemplate = {
        ...mockTemplate,
        template_styles: `
          .header { font-size: 18px
          .content color: red; }
          invalid-css-here
        `
      }
      
      const content = '<div class="header">Header</div>'
      
      // Should not throw error even with malformed CSS
      expect(async () => {
        const result = await service.generatePDF(content, malformedTemplate)
        expect(result).toBeInstanceOf(Buffer)
      }).not.toThrow()
    })
  })

  describe('page settings', () => {
    it('should handle different page sizes', async () => {
      const pageSizes: Array<'A4' | 'A3' | 'Letter' | 'Legal' | 'A5'> = ['A4', 'A3', 'Letter', 'Legal', 'A5']
      
      for (const pageSize of pageSizes) {
        const template: DocumentTemplate = {
          ...mockTemplate,
          page_size: pageSize
        }
        
        const result = await service.generatePDF('<h1>Test</h1>', template)
        expect(result).toBeInstanceOf(Buffer)
      }
    })

    it('should handle different page orientations', async () => {
      const orientations: Array<'portrait' | 'landscape'> = ['portrait', 'landscape']
      
      for (const orientation of orientations) {
        const template: DocumentTemplate = {
          ...mockTemplate,
          page_orientation: orientation
        }
        
        const result = await service.generatePDF('<h1>Test</h1>', template)
        expect(result).toBeInstanceOf(Buffer)
      }
    })

    it('should handle different margin settings', async () => {
      const template: DocumentTemplate = {
        ...mockTemplate,
        margin_top: 50,
        margin_bottom: 50,
        margin_left: 30,
        margin_right: 30
      }
      
      const result = await service.generatePDF('<h1>Test with margins</h1>', template)
      expect(result).toBeInstanceOf(Buffer)
    })
  })

  describe('error handling', () => {
    it('should handle PDF generation errors gracefully', async () => {
      // This is a bit tricky to test since our current implementation is a mock
      // In a real implementation, we might test network errors, memory issues, etc.
      
      const content = '<h1>Test</h1>'
      
      // Should not throw unhandled errors
      expect(async () => {
        const result = await service.generatePDF(content, mockTemplate)
        expect(result).toBeInstanceOf(Buffer)
      }).not.toThrow()
    })

    it('should handle very large content', async () => {
      // Generate large content
      let largeContent = '<h1>Large Document</h1>'
      for (let i = 0; i < 1000; i++) {
        largeContent += `<p>This is paragraph number ${i + 1} with some content to make it longer.</p>`
      }
      
      const result = await service.generatePDF(largeContent, mockTemplate)
      expect(result).toBeInstanceOf(Buffer)
      expect(result.length).toBeGreaterThan(1000) // Should be a substantial PDF
    })
  })
})