import { createClient } from '@/lib/supabase/client'
import type {
  Container,
  ContainerType,
  ContainerSize,
  PackagingType,
} from '@/lib/validations/shipment'

export interface ProductAllocation {
  product_id: string
  quantity: number
  packaging_type: PackagingType
  gross_weight_per_package: number
  net_weight_per_package: number
  volume_per_package?: number
  temperature_require?: string
  vent_require?: string
}

export interface ContainerRequirement {
  total_volume: number
  total_weight: number
  requires_refrigeration: boolean
  requires_ventilation: boolean
  special_requirements: string[]
}

export interface ContainerConfig {
  type: ContainerType
  size: ContainerSize
  capacity_volume: number
  capacity_weight: number
  count: number
}

export interface GeneratedContainer {
  container_type: ContainerType
  container_size: ContainerSize
  volume: number
  status: 'empty'
  container_number?: string
}

export interface ContainerGenerationError {
  code: string
  message: string
  details?: any
  timestamp: Date
}

export class ContainerGenerationService {
  private supabase = createClient()

  // Container specifications (standard industry values)
  private readonly CONTAINER_SPECS = {
    '20ft': {
      dry: { volume: 33.2, weight: 28200 },
      reefer: { volume: 28.3, weight: 27400 },
      open_top: { volume: 32.4, weight: 27600 },
      flat_rack: { volume: 0, weight: 30500 },
      tank: { volume: 26.0, weight: 30480 },
    },
    '40ft': {
      dry: { volume: 67.7, weight: 26700 },
      reefer: { volume: 59.3, weight: 27300 },
      open_top: { volume: 65.9, weight: 26500 },
      flat_rack: { volume: 0, weight: 30500 },
      tank: { volume: 50.0, weight: 30480 },
    },
    '40hc': {
      dry: { volume: 76.4, weight: 26580 },
      reefer: { volume: 67.3, weight: 27300 },
      open_top: { volume: 74.3, weight: 26200 },
      flat_rack: { volume: 0, weight: 30500 },
      tank: { volume: 58.0, weight: 30480 },
    },
    '45ft': {
      dry: { volume: 86.0, weight: 26000 },
      reefer: { volume: 76.0, weight: 26800 },
      open_top: { volume: 84.0, weight: 25800 },
      flat_rack: { volume: 0, weight: 30500 },
      tank: { volume: 65.0, weight: 30480 },
    },
  } as const

  // Safety factor to prevent overloading
  private readonly SAFETY_FACTOR = 0.9

  private mapError(error: any): ContainerGenerationError {
    const timestamp = new Date()

    if (error.code) {
      switch (error.code) {
        case 'PGRST301':
          return {
            code: 'CUSTOMER_PRODUCT_NOT_FOUND',
            message: 'Customer product configuration not found.',
            timestamp,
          }
        case '23503':
          return {
            code: 'FOREIGN_KEY_VIOLATION',
            message: 'Invalid reference data provided.',
            timestamp,
          }
        default:
          console.error('Container generation error:', error)
          return {
            code: 'DATABASE_ERROR',
            message: 'Database operation failed.',
            details: error,
            timestamp,
          }
      }
    }

    return {
      code: 'CONTAINER_GENERATION_ERROR',
      message: error.message || 'Container generation failed.',
      details: error,
      timestamp,
    }
  }

  /**
   * Calculate total requirements from product allocations
   */
  calculateTotalRequirements(
    products: ProductAllocation[]
  ): ContainerRequirement {
    let total_volume = 0
    let total_weight = 0
    let requires_refrigeration = false
    let requires_ventilation = false
    const special_requirements: string[] = []

    for (const product of products) {
      // Calculate total weight
      total_weight += product.gross_weight_per_package * product.quantity

      // Calculate volume if available
      if (product.volume_per_package) {
        total_volume += product.volume_per_package * product.quantity
      } else {
        // Estimate volume based on packaging type if not provided
        const estimated_volume = this.estimateVolumeByPackaging(
          product.packaging_type,
          product.quantity
        )
        total_volume += estimated_volume
      }

      // Check special requirements
      if (product.temperature_require) {
        requires_refrigeration = true
        if (!special_requirements.includes(product.temperature_require)) {
          special_requirements.push(product.temperature_require)
        }
      }

      if (product.vent_require) {
        requires_ventilation = true
        if (!special_requirements.includes(product.vent_require)) {
          special_requirements.push(product.vent_require)
        }
      }
    }

    return {
      total_volume,
      total_weight,
      requires_refrigeration,
      requires_ventilation,
      special_requirements,
    }
  }

  /**
   * Estimate volume based on packaging type (fallback when volume not provided)
   */
  private estimateVolumeByPackaging(
    packaging_type: PackagingType,
    quantity: number
  ): number {
    const VOLUME_ESTIMATES = {
      Bag: 0.05, // 50 liters per bag
      'Plastic Basket': 0.08, // 80 liters per basket
      Carton: 0.06, // 60 liters per carton
    }

    return VOLUME_ESTIMATES[packaging_type] * quantity
  }

  /**
   * Determine optimal container configuration
   */
  optimizeContainerAllocation(
    requirements: ContainerRequirement
  ): ContainerConfig[] {
    const configs: ContainerConfig[] = []

    // Determine container type based on requirements
    let container_type: ContainerType = 'dry'

    if (requirements.requires_refrigeration) {
      container_type = 'reefer'
    } else if (requirements.requires_ventilation) {
      container_type = 'open_top'
    }

    // Find optimal size and count
    const optimal = this.findOptimalContainerSize(
      container_type,
      requirements.total_volume,
      requirements.total_weight
    )

    if (optimal) {
      configs.push({
        type: container_type,
        size: optimal.size,
        capacity_volume: optimal.capacity_volume,
        capacity_weight: optimal.capacity_weight,
        count: optimal.count,
      })
    } else {
      // Fallback to multiple 40ft containers if no single optimal solution
      const fallback = this.createFallbackConfiguration(
        container_type,
        requirements.total_volume,
        requirements.total_weight
      )
      configs.push(fallback)
    }

    return configs
  }

  /**
   * Find the optimal container size for given requirements
   */
  private findOptimalContainerSize(
    container_type: ContainerType,
    total_volume: number,
    total_weight: number
  ): { size: ContainerSize; capacity_volume: number; capacity_weight: number; count: number } | null {
    const sizes: ContainerSize[] = ['20ft', '40ft', '40hc', '45ft']
    
    for (const size of sizes) {
      const spec = this.CONTAINER_SPECS[size][container_type]
      if (!spec) continue

      const max_volume = spec.volume * this.SAFETY_FACTOR
      const max_weight = spec.weight * this.SAFETY_FACTOR

      // Calculate how many containers needed
      const containers_for_volume = Math.ceil(total_volume / max_volume)
      const containers_for_weight = Math.ceil(total_weight / max_weight)
      const containers_needed = Math.max(containers_for_volume, containers_for_weight, 1) // At least 1 container

      // Prefer solutions with fewer containers, but reasonable count
      if (containers_needed <= 10) {
        return {
          size,
          capacity_volume: spec.volume,
          capacity_weight: spec.weight,
          count: containers_needed,
        }
      }
    }

    return null
  }

  /**
   * Create fallback configuration when optimal solution not found
   */
  private createFallbackConfiguration(
    container_type: ContainerType,
    total_volume: number,
    total_weight: number
  ): ContainerConfig {
    // Default to 40ft containers
    const size: ContainerSize = '40ft'
    const spec = this.CONTAINER_SPECS[size][container_type]

    const max_volume = spec.volume * this.SAFETY_FACTOR
    const max_weight = spec.weight * this.SAFETY_FACTOR

    const containers_for_volume = Math.ceil(total_volume / max_volume)
    const containers_for_weight = Math.ceil(total_weight / max_weight)
    const containers_needed = Math.max(containers_for_volume, containers_for_weight, 1)

    return {
      type: container_type,
      size,
      capacity_volume: spec.volume,
      capacity_weight: spec.weight,
      count: containers_needed,
    }
  }

  /**
   * Generate container number (placeholder implementation)
   */
  private generateContainerNumber(index: number): string {
    const timestamp = Date.now().toString(36).toUpperCase()
    const sequence = (index + 1).toString().padStart(3, '0')
    return `CONT${timestamp}${sequence}`
  }

  /**
   * Generate containers based on configuration
   */
  async generateContainers(
    shipment_id: string,
    configurations: ContainerConfig[]
  ): Promise<GeneratedContainer[]> {
    try {
      const containers: GeneratedContainer[] = []

      for (const config of configurations) {
        for (let i = 0; i < config.count; i++) {
          containers.push({
            container_type: config.type,
            container_size: config.size,
            volume: config.capacity_volume,
            status: 'empty',
            container_number: this.generateContainerNumber(containers.length),
          })
        }
      }

      return containers
    } catch (error: any) {
      throw this.mapError(error)
    }
  }

  /**
   * Get customer product specifications for weight calculations
   */
  async getCustomerProductSpecs(
    customer_id: string,
    product_ids: string[]
  ): Promise<ProductAllocation[]> {
    try {
      const { data, error } = await this.supabase
        .from('customer_products')
        .select(
          `
          product_id,
          gross_weight_per_package,
          net_weight_per_package,
          packaging_type,
          temperature_require,
          vent_require,
          products (
            id,
            name,
            description
          )
        `
        )
        .eq('customer_id', customer_id)
        .in('product_id', product_ids)
        .eq('is_active', true)

      if (error) throw error

      return data.map(item => ({
        product_id: item.product_id,
        quantity: 0, // Will be set by caller
        packaging_type: item.packaging_type as PackagingType,
        gross_weight_per_package: item.gross_weight_per_package,
        net_weight_per_package: item.net_weight_per_package,
        temperature_require: item.temperature_require,
        vent_require: item.vent_require,
      }))
    } catch (error: any) {
      throw this.mapError(error)
    }
  }

  /**
   * Main function to generate containers for a shipment
   */
  async generateContainersForShipment(
    shipment_id: string,
    customer_id: string,
    products: Array<{ product_id: string; quantity: number }>
  ): Promise<GeneratedContainer[]> {
    try {
      // Get customer product specifications
      const product_ids = products.map(p => p.product_id)
      const product_specs = await this.getCustomerProductSpecs(
        customer_id,
        product_ids
      )

      // Merge quantities with specifications
      const product_allocations: ProductAllocation[] = product_specs.map(
        spec => {
          const product = products.find(p => p.product_id === spec.product_id)
          return {
            ...spec,
            quantity: product?.quantity || 0,
          }
        }
      )

      // Calculate total requirements
      const requirements = this.calculateTotalRequirements(product_allocations)

      // Optimize container allocation
      const configurations = this.optimizeContainerAllocation(requirements)

      // Generate containers
      const containers = await this.generateContainers(
        shipment_id,
        configurations
      )

      return containers
    } catch (error: any) {
      throw this.mapError(error)
    }
  }
}

// Export singleton instance
export const containerGenerationService = new ContainerGenerationService()