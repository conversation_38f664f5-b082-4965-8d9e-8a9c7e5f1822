'use client'

import { useEffect, useCallback } from 'react'
import {
  useUnitStore,
  useUnits,
  useUnitsLoading,
  useUnitsError,
  useUnitActions,
} from '@/stores/unit-store'
import type {
  UnitOfMeasure,
  UnitOfMeasureInsert,
  UnitOfMeasureUpdate,
} from '@/lib/supabase/types'
import type { UnitFilter } from '@/lib/validations/products'

export function useUnitsData() {
  const units = useUnits()
  const loading = useUnitsLoading()
  const error = useUnitsError()
  const {
    fetchUnits,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    clearError,
    subscribeToUnits,
  } = useUnitActions()

  // Pagination and filtering state
  const currentPage = useUnitStore(state => state.currentPage)
  const pageSize = useUnitStore(state => state.pageSize)
  const totalCount = useUnitStore(state => state.totalCount)
  const filter = useUnitStore(state => state.filter)
  const searchTerm = useUnitStore(state => state.searchTerm)
  const sortBy = useUnitStore(state => state.sortBy)
  const sortOrder = useUnitStore(state => state.sortOrder)

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / pageSize)
  const hasNextPage = currentPage < totalPages
  const hasPreviousPage = currentPage > 1

  // Load units on mount and setup real-time subscription
  useEffect(() => {
    fetchUnits()

    // Setup real-time subscription
    const unsubscribe = subscribeToUnits()

    return () => {
      unsubscribe()
    }
  }, [fetchUnits, subscribeToUnits])

  // Filtered units for dropdown/selection purposes
  const activeUnits = units.filter(unit => unit.is_active)
  const unitsByCategory = units.reduce(
    (acc, unit) => {
      const category = unit.category || 'other'
      if (!acc[category]) acc[category] = []
      acc[category].push(unit)
      return acc
    },
    {} as Record<string, typeof units>
  )

  return {
    // Data
    units,
    activeUnits,
    unitsByCategory,
    loading,
    error,

    // Pagination
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    hasNextPage,
    hasPreviousPage,

    // Filters and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Actions
    fetchUnits,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage: (page: number) => setPage(Math.max(1, Math.min(page, totalPages))),
    nextPage: () => hasNextPage && setPage(currentPage + 1),
    previousPage: () => hasPreviousPage && setPage(currentPage - 1),
    clearError,

    // Utility functions
    refreshUnits: fetchUnits,
    getUnitById: (id: string) => units.find(unit => unit.id === id),
    getUnitByCode: (code: string) => units.find(unit => unit.code === code),
  }
}

export function useUnitCRUD() {
  const { createUnit, updateUnit, deleteUnit, deleteUnits, fetchUnitById } =
    useUnitActions()

  const isCreating = useUnitStore(state => state.isCreating)
  const isUpdating = useUnitStore(state => state.isUpdating)
  const isDeleting = useUnitStore(state => state.isDeleting)

  const handleCreate = useCallback(
    async (unitData: UnitOfMeasureInsert): Promise<UnitOfMeasure> => {
      try {
        const result = await createUnit(unitData)
        return result
      } catch (error) {
        console.error('Failed to create unit:', error)
        throw error
      }
    },
    [createUnit]
  )

  const handleUpdate = useCallback(
    async (
      id: string,
      updates: Partial<UnitOfMeasureUpdate>
    ): Promise<UnitOfMeasure> => {
      try {
        const result = await updateUnit(id, updates)
        return result
      } catch (error) {
        console.error('Failed to update unit:', error)
        throw error
      }
    },
    [updateUnit]
  )

  const handleDelete = useCallback(
    async (id: string): Promise<void> => {
      try {
        await deleteUnit(id)
      } catch (error) {
        console.error('Failed to delete unit:', error)
        throw error
      }
    },
    [deleteUnit]
  )

  const handleBulkDelete = useCallback(
    async (ids: string[]): Promise<void> => {
      try {
        await deleteUnits(ids)
      } catch (error) {
        console.error('Failed to delete units:', error)
        throw error
      }
    },
    [deleteUnits]
  )

  const handleFetchById = useCallback(
    async (id: string) => {
      try {
        return await fetchUnitById(id)
      } catch (error) {
        console.error('Failed to fetch unit:', error)
        throw error
      }
    },
    [fetchUnitById]
  )

  return {
    // Loading states
    isCreating,
    isUpdating,
    isDeleting,

    // CRUD operations
    createUnit: handleCreate,
    updateUnit: handleUpdate,
    deleteUnit: handleDelete,
    bulkDeleteUnits: handleBulkDelete,
    fetchUnitById: handleFetchById,

    // Utility
    isLoading: isCreating || isUpdating || isDeleting,
  }
}

export function useUnitSelection() {
  const selectedUnits = useUnitStore(state => state.selectedUnits)
  const { selectUnit, deselectUnit, clearSelection } = useUnitActions()

  const selectAllUnits = useUnitStore(state => state.selectAllUnits)
  const units = useUnits()

  const selectedIds = Array.from(selectedUnits)
  const selectedCount = selectedUnits.size
  const isSelected = (id: string) => selectedUnits.has(id)
  const isAllSelected = units.length > 0 && selectedUnits.size === units.length
  const isPartiallySelected =
    selectedUnits.size > 0 && selectedUnits.size < units.length

  const toggleUnit = useCallback(
    (id: string) => {
      if (selectedUnits.has(id)) {
        deselectUnit(id)
      } else {
        selectUnit(id)
      }
    },
    [selectedUnits, selectUnit, deselectUnit]
  )

  const toggleAll = useCallback(() => {
    if (isAllSelected) {
      clearSelection()
    } else {
      selectAllUnits()
    }
  }, [isAllSelected, clearSelection, selectAllUnits])

  return {
    selectedUnits: selectedIds,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,
    selectUnit,
    deselectUnit,
    toggleUnit,
    selectAllUnits,
    toggleAll,
    clearSelection,
  }
}

// Validation hooks
export function useUnitValidation() {
  const units = useUnits()

  const validateUniqueCode = useCallback(
    (code: string, excludeId?: string) => {
      const existing = units.find(
        unit =>
          unit.code.toLowerCase() === code.toLowerCase() &&
          unit.is_active &&
          unit.id !== excludeId
      )
      return !existing
    },
    [units]
  )

  const validateConversionFactor = useCallback(
    (factor: number, baseUnitId?: string) => {
      if (!baseUnitId && factor !== 1) {
        return 'Base units must have a conversion factor of 1'
      }
      if (baseUnitId && factor <= 0) {
        return 'Conversion factor must be positive'
      }
      return null
    },
    []
  )

  const validateCircularReference = useCallback(
    (unitId: string, baseUnitId?: string) => {
      if (!baseUnitId || unitId === baseUnitId) {
        return unitId === baseUnitId ? 'Unit cannot be its own base unit' : null
      }

      // Check for circular references
      const visited = new Set<string>()
      let currentId = baseUnitId

      while (currentId && !visited.has(currentId)) {
        visited.add(currentId)
        const unit = units.find(u => u.id === currentId)
        if (!unit) break

        if (unit.base_unit_id === unitId) {
          return 'Circular reference detected in unit hierarchy'
        }

        currentId = unit.base_unit_id || undefined
      }

      return null
    },
    [units]
  )

  return {
    validateUniqueCode,
    validateConversionFactor,
    validateCircularReference,
  }
}

// Categories and constants
export const UNIT_CATEGORIES = [
  { value: 'weight', label: 'Weight' },
  { value: 'count', label: 'Count' },
  { value: 'volume', label: 'Volume' },
  { value: 'length', label: 'Length' },
] as const

export const DEFAULT_UNIT_FILTER: UnitFilter = {
  is_active: true,
}

// Main hook that combines all functionality
export function useUnitsManagement() {
  const data = useUnitsData()
  const crud = useUnitCRUD()
  const selection = useUnitSelection()
  const validation = useUnitValidation()

  return {
    ...data,
    ...crud,
    ...selection,
    ...validation,

    // Convenience methods
    resetFilters: () => data.setFilter(DEFAULT_UNIT_FILTER),
    clearSearch: () => data.setSearchTerm(''),
  }
}
