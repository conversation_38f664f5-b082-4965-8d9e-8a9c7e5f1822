import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import type {
  ShipmentCreation,
  TransportMode,
  TransportModeConfig,
  DocumentUpload,
} from '@/lib/validations/shipment'
import type { TransportModeConfig as ModalTransportModeConfig } from '@/components/forms/shipment-form/transport-mode-modal'

// Form validation state
export interface ValidationState {
  isValid: boolean
  errors: Record<string, string[]>
  warnings: Record<string, string[]>
  fieldTouched: Record<string, boolean>
  validationMode: 'onChange' | 'onBlur' | 'onSubmit'
}

// Form step state for wizard-like progression
export interface FormStepState {
  currentStep: number
  completedSteps: Set<number>
  stepValidation: Record<number, boolean>
  canAdvance: boolean
  canGoBack: boolean
}

// Document upload state
export interface DocumentState {
  uploadedFiles: Record<string, File>
  uploadProgress: Record<string, number>
  uploadErrors: Record<string, string>
  isUploading: boolean
}

// Draft state for form persistence
export interface DraftState {
  hasDraft: boolean
  draftId: string | null
  lastSaved: Date | null
  isDirty: boolean
  autoSaveEnabled: boolean
}

// Relationship intelligence state
export interface RelationshipState {
  selectedCustomerId: string | null
  selectedShipperId: string | null
  selectedProductId: string | null
  selectedConsigneeId: string | null
  selectedNotifyPartyId: string | null

  // Pre-populated data
  suggestedPorts: {
    origin: string[]
    destination: string[]
  }
  estimatedPricing: {
    cif: number
    fob: number
    currency: string
  } | null

  // Relationship validation
  relationshipErrors: string[]
  hasValidRelationships: boolean
}

// Main shipment creation store state
export interface ShipmentCreationState {
  // Form data
  formData: Partial<ShipmentCreation>

  // Transport mode configuration
  transportMode: TransportMode | null
  transportConfig: ModalTransportModeConfig | null
  showTransportModal: boolean

  // Validation state
  validation: ValidationState

  // Form steps state
  steps: FormStepState

  // Document upload state
  documents: DocumentState

  // Draft state
  draft: DraftState

  // Relationship intelligence state
  relationships: RelationshipState

  // UI state
  isSubmitting: boolean
  submitError: string | null
  submitSuccess: boolean

  // Loading states
  isLoading: boolean
  isInitializing: boolean
}

// Store actions
export interface ShipmentCreationActions {
  // Form data actions
  updateFormData: (updates: Partial<ShipmentCreation>) => void
  setFieldValue: <K extends keyof ShipmentCreation>(
    field: K,
    value: ShipmentCreation[K]
  ) => void
  getFieldValue: <K extends keyof ShipmentCreation>(
    field: K
  ) => ShipmentCreation[K] | undefined

  // Transport mode actions
  setTransportMode: (
    mode: TransportMode,
    config: ModalTransportModeConfig
  ) => void
  clearTransportMode: () => void
  setShowTransportModal: (show: boolean) => void

  // Validation actions
  validateField: (field: keyof ShipmentCreation, value: any) => Promise<void>
  validateForm: () => Promise<boolean>
  clearValidationErrors: (field?: keyof ShipmentCreation) => void
  setFieldTouched: (field: keyof ShipmentCreation, touched: boolean) => void
  setValidationMode: (mode: ValidationState['validationMode']) => void

  // Form step actions
  goToStep: (step: number) => void
  nextStep: () => void
  previousStep: () => void
  completeStep: (step: number) => void
  isStepComplete: (step: number) => boolean
  canAdvanceToStep: (step: number) => boolean

  // Document upload actions
  uploadDocument: (type: string, file: File) => Promise<void>
  removeDocument: (type: string) => void
  updateUploadProgress: (type: string, progress: number) => void
  setUploadError: (type: string, error: string) => void

  // Draft actions
  saveDraft: () => Promise<void>
  loadDraft: (draftId?: string) => Promise<void>
  deleteDraft: (draftId?: string) => Promise<void>
  setAutoSave: (enabled: boolean) => void
  markDirty: () => void
  markClean: () => void

  // Relationship actions
  updateRelationships: (relationships: Partial<RelationshipState>) => void
  setCustomerRelationship: (customerId: string) => void
  clearRelationships: () => void
  validateRelationships: () => Promise<boolean>

  // Submission actions
  submitForm: () => Promise<void>
  retrySubmission: () => Promise<void>
  clearSubmissionState: () => void

  // Utility actions
  resetForm: () => void
  initializeForm: (initialData?: Partial<ShipmentCreation>) => void
  exportFormData: () => ShipmentCreation
  importFormData: (data: Partial<ShipmentCreation>) => void
}

// Initial state
const initialState: ShipmentCreationState = {
  formData: {
    transportation_mode: 'sea',
    currency_code: 'USD',
    status: 'booking_confirmed',
  },

  transportMode: null,
  transportConfig: null,
  showTransportModal: true,

  validation: {
    isValid: false,
    errors: {},
    warnings: {},
    fieldTouched: {},
    validationMode: 'onChange',
  },

  steps: {
    currentStep: 0,
    completedSteps: new Set(),
    stepValidation: {},
    canAdvance: false,
    canGoBack: false,
  },

  documents: {
    uploadedFiles: {},
    uploadProgress: {},
    uploadErrors: {},
    isUploading: false,
  },

  draft: {
    hasDraft: false,
    draftId: null,
    lastSaved: null,
    isDirty: false,
    autoSaveEnabled: true,
  },

  relationships: {
    selectedCustomerId: null,
    selectedShipperId: null,
    selectedProductId: null,
    selectedConsigneeId: null,
    selectedNotifyPartyId: null,
    suggestedPorts: {
      origin: [],
      destination: [],
    },
    estimatedPricing: null,
    relationshipErrors: [],
    hasValidRelationships: false,
  },

  isSubmitting: false,
  submitError: null,
  submitSuccess: false,

  isLoading: false,
  isInitializing: false,
}

// Form step configuration
export const FORM_STEPS = [
  {
    id: 0,
    name: 'transport_mode',
    title: 'Transportation Mode',
    description: 'Select transportation method',
    requiredFields: ['transportation_mode'],
  },
  {
    id: 1,
    name: 'stakeholders',
    title: 'Stakeholders',
    description: 'Configure stakeholders and relationships',
    requiredFields: ['customer_id', 'factory_id', 'forwarder_agent_id'],
  },
  {
    id: 2,
    name: 'route_schedule',
    title: 'Route & Schedule',
    description: 'Set routing and timing',
    requiredFields: [
      'origin_port_id',
      'destination_port_id',
      'etd_date',
      'eta_date',
      'closing_time',
    ],
  },
  {
    id: 3,
    name: 'documents',
    title: 'Documents',
    description: 'Upload required documentation',
    requiredFields: ['booking_confirmation'],
  },
  {
    id: 4,
    name: 'review',
    title: 'Review',
    description: 'Review and submit',
    requiredFields: [],
  },
] as const

// Create the store
export const useShipmentCreationStore = create<
  ShipmentCreationState & ShipmentCreationActions
>()(
  persist(
    immer((set, get) => ({
      ...initialState,

      // Form data actions
      updateFormData: updates => {
        set(state => {
          Object.assign(state.formData, updates)
          state.draft.isDirty = true
        })
        get().markDirty()
      },

      setFieldValue: (field, value) => {
        set(state => {
          state.formData[field] = value
          state.draft.isDirty = true
        })
        get().markDirty()

        // Trigger validation if in onChange mode
        if (get().validation.validationMode === 'onChange') {
          get().validateField(field, value)
        }
      },

      getFieldValue: field => {
        return get().formData[field]
      },

      // Transport mode actions
      setTransportMode: (mode, config) => {
        set(state => {
          state.transportMode = mode
          state.transportConfig = config
          state.formData.transportation_mode = mode
          state.showTransportModal = false
          state.steps.completedSteps.add(0)
          state.draft.isDirty = true
        })
        get().completeStep(0)
        get().markDirty()
      },

      clearTransportMode: () => {
        set(state => {
          state.transportMode = null
          state.transportConfig = null
          state.showTransportModal = true
          state.steps.completedSteps.delete(0)
        })
      },

      setShowTransportModal: show => {
        set(state => {
          state.showTransportModal = show
        })
      },

      // Validation actions
      validateField: async (field, value) => {
        // Import validation schema dynamically to avoid circular deps
        const { shipmentCreationSchema } = await import(
          '@/lib/validations/shipment'
        )

        try {
          // Validate single field
          shipmentCreationSchema.shape[field]?.parse(value)

          set(state => {
            delete state.validation.errors[field as string]
            state.validation.fieldTouched[field as string] = true
          })
        } catch (error: any) {
          set(state => {
            state.validation.errors[field as string] = error.errors?.map(
              (e: any) => e.message
            ) || [error.message]
            state.validation.fieldTouched[field as string] = true
          })
        }
      },

      validateForm: async () => {
        const { shipmentCreationSchema } = await import(
          '@/lib/validations/shipment'
        )

        try {
          shipmentCreationSchema.parse(get().formData)

          set(state => {
            state.validation.isValid = true
            state.validation.errors = {}
          })

          return true
        } catch (error: any) {
          const errorsByField: Record<string, string[]> = {}

          if (error.errors) {
            error.errors.forEach((err: any) => {
              const field = err.path?.[0]
              if (field) {
                if (!errorsByField[field]) errorsByField[field] = []
                errorsByField[field].push(err.message)
              }
            })
          }

          set(state => {
            state.validation.isValid = false
            state.validation.errors = errorsByField
          })

          return false
        }
      },

      clearValidationErrors: field => {
        set(state => {
          if (field) {
            delete state.validation.errors[field as string]
          } else {
            state.validation.errors = {}
          }
        })
      },

      setFieldTouched: (field, touched) => {
        set(state => {
          state.validation.fieldTouched[field as string] = touched
        })
      },

      setValidationMode: mode => {
        set(state => {
          state.validation.validationMode = mode
        })
      },

      // Form step actions
      goToStep: step => {
        if (get().canAdvanceToStep(step)) {
          set(state => {
            state.steps.currentStep = step
            state.steps.canGoBack = step > 0
            state.steps.canAdvance = step < FORM_STEPS.length - 1
          })
        }
      },

      nextStep: () => {
        const currentStep = get().steps.currentStep
        if (currentStep < FORM_STEPS.length - 1) {
          get().goToStep(currentStep + 1)
        }
      },

      previousStep: () => {
        const currentStep = get().steps.currentStep
        if (currentStep > 0) {
          get().goToStep(currentStep - 1)
        }
      },

      completeStep: step => {
        set(state => {
          state.steps.completedSteps.add(step)
          state.steps.stepValidation[step] = true
        })
      },

      isStepComplete: step => {
        return get().steps.completedSteps.has(step)
      },

      canAdvanceToStep: step => {
        // Can advance if all previous steps are completed
        for (let i = 0; i < step; i++) {
          if (!get().isStepComplete(i)) {
            return false
          }
        }
        return true
      },

      // Document upload actions
      uploadDocument: async (type, file) => {
        set(state => {
          state.documents.isUploading = true
          state.documents.uploadProgress[type] = 0
          delete state.documents.uploadErrors[type]
        })

        try {
          // Simulate upload progress
          for (let progress = 0; progress <= 100; progress += 10) {
            get().updateUploadProgress(type, progress)
            await new Promise(resolve => setTimeout(resolve, 100))
          }

          set(state => {
            state.documents.uploadedFiles[type] = file
            state.documents.isUploading = false
            state.documents.uploadProgress[type] = 100
            state.draft.isDirty = true
          })

          get().markDirty()
        } catch (error: any) {
          get().setUploadError(type, error.message)
          set(state => {
            state.documents.isUploading = false
          })
        }
      },

      removeDocument: type => {
        set(state => {
          delete state.documents.uploadedFiles[type]
          delete state.documents.uploadProgress[type]
          delete state.documents.uploadErrors[type]
          state.draft.isDirty = true
        })
        get().markDirty()
      },

      updateUploadProgress: (type, progress) => {
        set(state => {
          state.documents.uploadProgress[type] = progress
        })
      },

      setUploadError: (type, error) => {
        set(state => {
          state.documents.uploadErrors[type] = error
        })
      },

      // Draft actions
      saveDraft: async () => {
        try {
          const draftId = get().draft.draftId || `draft_${Date.now()}`
          const draftData = {
            id: draftId,
            formData: get().formData,
            transportMode: get().transportMode,
            transportConfig: get().transportConfig,
            relationships: get().relationships,
            savedAt: new Date().toISOString(),
          }

          // Save to localStorage (could be extended to server storage)
          localStorage.setItem(
            `shipment_draft_${draftId}`,
            JSON.stringify(draftData)
          )

          set(state => {
            state.draft.draftId = draftId
            state.draft.lastSaved = new Date()
            state.draft.isDirty = false
            state.draft.hasDraft = true
          })

          console.log('Draft saved successfully')
        } catch (error) {
          console.error('Failed to save draft:', error)
        }
      },

      loadDraft: async draftId => {
        try {
          const targetDraftId = draftId || get().draft.draftId
          if (!targetDraftId) return

          const draftData = localStorage.getItem(
            `shipment_draft_${targetDraftId}`
          )
          if (!draftData) return

          const parsed = JSON.parse(draftData)

          set(state => {
            state.formData = { ...initialState.formData, ...parsed.formData }
            state.transportMode = parsed.transportMode
            state.transportConfig = parsed.transportConfig
            state.relationships = {
              ...initialState.relationships,
              ...parsed.relationships,
            }
            state.draft.draftId = targetDraftId
            state.draft.lastSaved = new Date(parsed.savedAt)
            state.draft.hasDraft = true
            state.draft.isDirty = false
          })

          console.log('Draft loaded successfully')
        } catch (error) {
          console.error('Failed to load draft:', error)
        }
      },

      deleteDraft: async draftId => {
        try {
          const targetDraftId = draftId || get().draft.draftId
          if (!targetDraftId) return

          localStorage.removeItem(`shipment_draft_${targetDraftId}`)

          if (targetDraftId === get().draft.draftId) {
            set(state => {
              state.draft.draftId = null
              state.draft.lastSaved = null
              state.draft.hasDraft = false
              state.draft.isDirty = false
            })
          }

          console.log('Draft deleted successfully')
        } catch (error) {
          console.error('Failed to delete draft:', error)
        }
      },

      setAutoSave: enabled => {
        set(state => {
          state.draft.autoSaveEnabled = enabled
        })
      },

      markDirty: () => {
        set(state => {
          state.draft.isDirty = true
        })

        // Auto-save if enabled
        if (get().draft.autoSaveEnabled) {
          setTimeout(() => {
            if (get().draft.isDirty) {
              get().saveDraft()
            }
          }, 2000) // Auto-save after 2 seconds of inactivity
        }
      },

      markClean: () => {
        set(state => {
          state.draft.isDirty = false
        })
      },

      // Relationship actions
      updateRelationships: relationships => {
        set(state => {
          Object.assign(state.relationships, relationships)
        })
      },

      setCustomerRelationship: customerId => {
        set(state => {
          state.relationships.selectedCustomerId = customerId
          // Clear dependent selections
          state.relationships.selectedShipperId = null
          state.relationships.selectedProductId = null
          state.relationships.selectedConsigneeId = null
          state.relationships.selectedNotifyPartyId = null

          // Update form data
          state.formData.customer_id = customerId
          state.formData.shipper_id = ''
          state.formData.consignee_id = ''
          state.formData.notify_party_id = ''
        })
      },

      clearRelationships: () => {
        set(state => {
          state.relationships = { ...initialState.relationships }
        })
      },

      validateRelationships: async () => {
        const relationships = get().relationships
        const errors: string[] = []

        if (!relationships.selectedCustomerId) {
          errors.push('Customer selection is required')
        }

        set(state => {
          state.relationships.relationshipErrors = errors
          state.relationships.hasValidRelationships = errors.length === 0
        })

        return errors.length === 0
      },

      // Submission actions
      submitForm: async () => {
        set(state => {
          state.isSubmitting = true
          state.submitError = null
        })

        try {
          // Validate form before submission
          const isValid = await get().validateForm()
          if (!isValid) {
            throw new Error('Form validation failed')
          }

          // Validate relationships
          const relationshipsValid = await get().validateRelationships()
          if (!relationshipsValid) {
            throw new Error('Relationship validation failed')
          }

          // TODO: Actual API submission
          await new Promise(resolve => setTimeout(resolve, 2000))

          set(state => {
            state.isSubmitting = false
            state.submitSuccess = true
            state.draft.isDirty = false
          })

          // Clear draft on successful submission
          get().deleteDraft()
        } catch (error: any) {
          set(state => {
            state.isSubmitting = false
            state.submitError = error.message
          })
        }
      },

      retrySubmission: async () => {
        get().clearSubmissionState()
        await get().submitForm()
      },

      clearSubmissionState: () => {
        set(state => {
          state.submitError = null
          state.submitSuccess = false
        })
      },

      // Utility actions
      resetForm: () => {
        set(() => ({ ...initialState }))
      },

      initializeForm: initialData => {
        set(state => {
          if (initialData) {
            Object.assign(state.formData, initialData)
          }
          state.isInitializing = false
        })
      },

      exportFormData: () => {
        return get().formData as ShipmentCreation
      },

      importFormData: data => {
        set(state => {
          Object.assign(state.formData, data)
          state.draft.isDirty = true
        })
      },
    })),
    {
      name: 'shipment-creation-store',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        // Only persist form data and draft information
        formData: state.formData,
        transportMode: state.transportMode,
        transportConfig: state.transportConfig,
        relationships: state.relationships,
        draft: state.draft,
      }),
      version: 1,
    }
  )
)

// Helper hooks for specific parts of the store
export const useShipmentFormData = () =>
  useShipmentCreationStore(state => state.formData)
export const useShipmentValidation = () =>
  useShipmentCreationStore(state => state.validation)
export const useShipmentSteps = () =>
  useShipmentCreationStore(state => state.steps)
export const useShipmentDocuments = () =>
  useShipmentCreationStore(state => state.documents)
export const useShipmentDraft = () =>
  useShipmentCreationStore(state => state.draft)
export const useShipmentRelationships = () =>
  useShipmentCreationStore(state => state.relationships)

// Computed selectors
export const useShipmentFormCompletion = () => {
  return useShipmentCreationStore(state => ({
    totalSteps: FORM_STEPS.length,
    completedSteps: state.steps.completedSteps.size,
    completionPercentage:
      (state.steps.completedSteps.size / FORM_STEPS.length) * 100,
    currentStepName: FORM_STEPS[state.steps.currentStep]?.name,
    isFormComplete: state.steps.completedSteps.size === FORM_STEPS.length,
  }))
}
