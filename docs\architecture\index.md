# DYY Trading Fruit Export Management System Fullstack Architecture Document

## Table of Contents

- [DYY Trading Fruit Export Management System Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
    - [Company](./data-models.md#company)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [Product](./data-models.md#product)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [Shipment](./data-models.md#shipment)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [CustomerProduct](./data-models.md#customerproduct)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [Driver](./data-models.md#driver)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
  - [API Specification](./api-specification.md)
    - [Supabase Client API](./api-specification.md#supabase-client-api)
      - [Core API Patterns](./api-specification.md#core-api-patterns)
  - [Components](./components.md)
    - [Frontend Application Layer](./components.md#frontend-application-layer)
    - [Mobile PWA Layer](./components.md#mobile-pwa-layer)
    - [Real-time Subscription Manager](./components.md#real-time-subscription-manager)
    - [Relationship Intelligence Engine](./components.md#relationship-intelligence-engine)
    - [Authentication & Authorization Module](./components.md#authentication-authorization-module)
    - [Component Diagrams](./components.md#component-diagrams)
  - [External APIs](./external-apis.md)
    - [SMS Service API](./external-apis.md#sms-service-api)
    - [Line Messaging API](./external-apis.md#line-messaging-api)
    - [WeChat Work API](./external-apis.md#wechat-work-api)
  - [Core Workflows](./core-workflows.md)
    - [Intelligent Shipment Creation Workflow](./core-workflows.md#intelligent-shipment-creation-workflow)
    - [Driver Mobile Status Update Workflow](./core-workflows.md#driver-mobile-status-update-workflow)
  - [Database Schema](./database-schema.md)
    - [Core Schema Implementation](./database-schema.md#core-schema-implementation)
    - [Performance Indexes](./database-schema.md#performance-indexes)
  - [Frontend Architecture](./frontend-architecture.md)
    - [Component Architecture](./frontend-architecture.md#component-architecture)
      - [Component Organization](./frontend-architecture.md#component-organization)
    - [State Management Architecture](./frontend-architecture.md#state-management-architecture)
      - [State Structure](./frontend-architecture.md#state-structure)
      - [State Management Patterns](./frontend-architecture.md#state-management-patterns)
    - [Routing Architecture](./frontend-architecture.md#routing-architecture)
      - [Protected Route Pattern](./frontend-architecture.md#protected-route-pattern)
    - [Frontend Services Layer](./frontend-architecture.md#frontend-services-layer)
      - [API Client Setup](./frontend-architecture.md#api-client-setup)
  - [Backend Architecture](./backend-architecture.md)
    - [Service Architecture](./backend-architecture.md#service-architecture)
      - [Serverless Architecture](./backend-architecture.md#serverless-architecture)
    - [Database Architecture](./backend-architecture.md#database-architecture)
      - [Schema Design](./backend-architecture.md#schema-design)
      - [Data Access Layer](./backend-architecture.md#data-access-layer)
    - [Authentication and Authorization](./backend-architecture.md#authentication-and-authorization)
      - [Auth Flow](./backend-architecture.md#auth-flow)
      - [Middleware/Guards](./backend-architecture.md#middlewareguards)
  - [Unified Project Structure](./unified-project-structure.md)
