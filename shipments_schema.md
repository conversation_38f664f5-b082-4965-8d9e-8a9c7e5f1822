create table public.shipments (
  id uuid not null default gen_random_uuid (),
  shipment_number text not null,
  invoice_number text null,
  customer_id uuid null,
  shipper_id uuid null,
  consignee_id uuid null,
  notify_party_id uuid null,
  factory_id uuid null,
  forwarder_agent_id uuid null,
  origin_port_id uuid null,
  destination_port_id uuid null,
  liner text null,
  vessel_name text null,
  voyage_number text null,
  booking_number text null,
  etd_date timestamp with time zone null,
  eta_date timestamp with time zone null,
  closing_time timestamp with time zone null,
  cy_date timestamp with time zone null,
  number_of_pallet integer null,
  pallet_description text null,
  ephyto_refno text null,
  currency_code public.currency_enum null default 'USD'::currency_enum,
  total_weight numeric null,
  total_volume numeric null,
  status public.shipment_status_enum null default 'booking_confirmed'::shipment_status_enum,
  transportation_mode public.transport_mode_enum null default 'sea'::transport_mode_enum,
  notes text null,
  metadata jsonb null,
  created_by uuid null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  total_value_cif numeric null,
  total_value_fob numeric null,
  total_quantity numeric null,
  total_gross_weight numeric null,
  total_net_weight numeric null,
  currency_code_fob public.currency_enum null default 'USD'::currency_enum,
  constraint shipments_pkey primary key (id),
  constraint shipments_shipment_number_key unique (shipment_number),
  constraint shipments_customer_id_fkey foreign KEY (customer_id) references companies (id),
  constraint shipments_destination_port_id_fkey foreign KEY (destination_port_id) references ports (id),
  constraint shipments_factory_id_fkey foreign KEY (factory_id) references companies (id),
  constraint shipments_forwarder_agent_id_fkey foreign KEY (forwarder_agent_id) references companies (id),
  constraint shipments_notify_party_id_fkey foreign KEY (notify_party_id) references companies (id),
  constraint shipments_origin_port_id_fkey foreign KEY (origin_port_id) references ports (id),
  constraint shipments_shipper_id_fkey foreign KEY (shipper_id) references companies (id),
  constraint shipments_consignee_id_fkey foreign KEY (consignee_id) references companies (id),
  constraint shipments_created_by_fkey foreign KEY (created_by) references profiles (user_id),
  constraint valid_date_sequence check (
    (
      (etd_date is null)
      or (eta_date is null)
      or (eta_date >= etd_date)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_shipments_customer_status on public.shipments using btree (customer_id, status) TABLESPACE pg_default;

create index IF not exists idx_shipments_factory_status on public.shipments using btree (factory_id, status) TABLESPACE pg_default
where
  (factory_id is not null);

create index IF not exists idx_shipments_etd on public.shipments using btree (etd_date) TABLESPACE pg_default
where
  (etd_date is not null);

create index IF not exists idx_shipments_eta on public.shipments using btree (eta_date) TABLESPACE pg_default
where
  (eta_date is not null);

create index IF not exists idx_shipments_created_date on public.shipments using btree (created_at) TABLESPACE pg_default;

create index IF not exists idx_shipments_customer_dates on public.shipments using btree (customer_id, etd_date, eta_date) TABLESPACE pg_default;

create index IF not exists idx_shipments_number_pattern on public.shipments using btree (shipment_number text_pattern_ops) TABLESPACE pg_default;

create index IF not exists idx_shipments_transport_mode on public.shipments using btree (transportation_mode) TABLESPACE pg_default;

create index IF not exists idx_shipments_origin_destination on public.shipments using btree (origin_port_id, destination_port_id) TABLESPACE pg_default;

create index IF not exists idx_shipments_dashboard_customer on public.shipments using btree (
  customer_id,
  status,
  etd_date desc,
  created_at desc
) TABLESPACE pg_default;

create index IF not exists idx_shipments_all_stakeholders on public.shipments using btree (customer_id, shipper_id, consignee_id) TABLESPACE pg_default;

create index IF not exists idx_shipments_incomplete on public.shipments using btree (status, etd_date) TABLESPACE pg_default
where
  (
    status <> all (
      array[
        'completed'::shipment_status_enum,
        'cancelled'::shipment_status_enum
      ]
    )
  );

create index IF not exists idx_shipments_duration on public.shipments using btree (((eta_date - etd_date))) TABLESPACE pg_default
where
  (
    (eta_date is not null)
    and (etd_date is not null)
  );

create index IF not exists idx_shipments_created_at on public.shipments using btree (created_at) TABLESPACE pg_default;

create trigger notify_shipment_status_change
after
update on shipments for EACH row when (old.status is distinct from new.status)
execute FUNCTION notify_status_change ();

create trigger track_shipment_status_changes_trigger
after INSERT
or
update on shipments for EACH row
execute FUNCTION track_shipment_status_changes ();

create trigger update_shipments_updated_at BEFORE
update on shipments for EACH row
execute FUNCTION update_updated_at_column ();

create table public.shipment_products (
  id uuid not null default gen_random_uuid (),
  shipment_id uuid null,
  container_id uuid null,
  product_id uuid null,
  product_description text null,
  quantity numeric not null,
  unit_of_measure_id uuid null,
  unit_price_cif numeric not null,
  unit_price_fob numeric not null,
  total_value_cif numeric not null,
  total_value_fob numeric not null,
  gross_weight numeric(18, 4) not null default 0,
  net_weight numeric(18, 4) not null default 0,
  shipping_mark text null,
  mfg_date date null,
  expire_date date null,
  lot_number text null,
  packaging_type public.packaging_type_enum not null,
  quality_grade text null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  total_gross_weight numeric null,
  total_net_weight numeric null,
  constraint shipment_products_pkey primary key (id),
  constraint shipment_products_container_id_fkey foreign KEY (container_id) references containers (id),
  constraint shipment_products_product_id_fkey foreign KEY (product_id) references products (id),
  constraint shipment_products_shipment_id_fkey foreign KEY (shipment_id) references shipments (id) on delete CASCADE,
  constraint shipment_products_unit_of_measure_id_fkey foreign KEY (unit_of_measure_id) references units_of_measure (id)
) TABLESPACE pg_default;

create index IF not exists idx_shipment_products_product_quantity on public.shipment_products using btree (product_id, quantity) TABLESPACE pg_default;

create index IF not exists idx_shipment_products_shipment_product on public.shipment_products using btree (shipment_id, product_id) TABLESPACE pg_default;

create index IF not exists idx_shipment_products_net_weight on public.shipment_products using btree (net_weight) TABLESPACE pg_default;

create index IF not exists idx_shipment_products_container on public.shipment_products using btree (container_id) TABLESPACE pg_default
where
  (container_id is not null);

create index IF not exists idx_shipment_products_packaging on public.shipment_products using btree (packaging_type, quantity) TABLESPACE pg_default;

create trigger update_shipment_products_updated_at BEFORE
update on shipment_products for EACH row
execute FUNCTION update_updated_at_column ();


create table public.status_history (
  id uuid not null default gen_random_uuid (),
  shipment_id uuid null,
  status_from public.shipment_status_enum null,
  status_to public.shipment_status_enum not null,
  notes text null,
  location text null,
  latitude numeric null,
  longitude numeric null,
  updated_by uuid null,
  created_at timestamp with time zone null default now(),
  gps_coordinates geography null,
  constraint status_history_pkey primary key (id),
  constraint status_history_shipment_id_fkey foreign KEY (shipment_id) references shipments (id) on delete CASCADE,
  constraint status_history_updated_by_fkey foreign KEY (updated_by) references profiles (user_id)
) TABLESPACE pg_default;

create index IF not exists idx_status_history_shipment_created_at on public.status_history using btree (shipment_id, created_at) TABLESPACE pg_default;

create index IF not exists idx_status_history_status_from_created_at on public.status_history using btree (status_from, created_at) TABLESPACE pg_default;

create index IF not exists idx_status_history_status_to_created_at on public.status_history using btree (status_to, created_at) TABLESPACE pg_default;

create index IF not exists idx_status_history_updated_by on public.status_history using btree (updated_by) TABLESPACE pg_default
where
  (updated_by is not null);

create index IF not exists idx_status_history_gps on public.status_history using gist (gps_coordinates) TABLESPACE pg_default
where
  (gps_coordinates is not null);
  

create table public.status_images (
  id uuid not null default gen_random_uuid (),
  shipment_id uuid null,
  status_history_id uuid null,
  image_url text not null,
  image_path text not null,
  file_size integer null,
  mime_type text null,
  metadata jsonb null,
  uploaded_by uuid null,
  created_at timestamp with time zone null default now(),
  constraint status_images_pkey primary key (id),
  constraint status_images_shipment_id_fkey foreign KEY (shipment_id) references shipments (id) on delete CASCADE,
  constraint status_images_status_history_id_fkey foreign KEY (status_history_id) references status_history (id),
  constraint status_images_uploaded_by_fkey foreign KEY (uploaded_by) references profiles (user_id)
) TABLESPACE pg_default;

create index IF not exists idx_status_images_status_history on public.status_images using btree (status_history_id) TABLESPACE pg_default;

create trigger sync_status_images_coordinates BEFORE INSERT
or
update on status_images for EACH row
execute FUNCTION sync_gps_coordinates ();

create trigger update_status_images_updated_at BEFORE
update on status_images for EACH row
execute FUNCTION update_updated_at_column ();


create table public.notifications (
  id uuid not null default gen_random_uuid (),
  notification_type public.notification_type_enum not null,
  title text not null,
  message text not null,
  recipient_id uuid not null,
  sender_id uuid null,
  shipment_id uuid null,
  document_id uuid null,
  status_history_id uuid null,
  channels notification_channel_enum[] not null,
  email_sent boolean null default false,
  email_sent_at timestamp with time zone null,
  sms_sent boolean null default false,
  sms_sent_at timestamp with time zone null,
  line_sent boolean null default false,
  line_sent_at timestamp with time zone null,
  wechat_sent boolean null default false,
  wechat_sent_at timestamp with time zone null,
  in_app_read boolean null default false,
  in_app_read_at timestamp with time zone null,
  notification_data jsonb null,
  language text null default 'en'::text,
  priority integer null default 1,
  is_sent boolean null default false,
  send_attempts integer null default 0,
  max_attempts integer null default 3,
  last_attempt_at timestamp with time zone null,
  error_message text null,
  scheduled_for timestamp with time zone null default CURRENT_TIMESTAMP,
  expires_at timestamp with time zone null,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  constraint notifications_pkey primary key (id),
  constraint notifications_recipient_id_fkey foreign KEY (recipient_id) references profiles (user_id),
  constraint notifications_sender_id_fkey foreign KEY (sender_id) references profiles (user_id),
  constraint notifications_shipment_id_fkey foreign KEY (shipment_id) references shipments (id),
  constraint notifications_status_history_id_fkey foreign KEY (status_history_id) references status_history (id),
  constraint notifications_document_id_fkey foreign KEY (document_id) references documents (id),
  constraint positive_attempts check (
    (
      (send_attempts >= 0)
      and (max_attempts > 0)
    )
  ),
  constraint valid_language check ((language ~* '^[a-z]{2}(-[A-Z]{2})?$'::text)),
  constraint valid_priority check (
    (
      (priority >= 1)
      and (priority <= 4)
    )
  ),
  constraint valid_schedule check (
    (
      (expires_at is null)
      or (expires_at >= scheduled_for)
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_notifications_recipient on public.notifications using btree (recipient_id) TABLESPACE pg_default;

create index IF not exists idx_notifications_shipment on public.notifications using btree (shipment_id) TABLESPACE pg_default;

create index IF not exists idx_notifications_type on public.notifications using btree (notification_type) TABLESPACE pg_default;

create index IF not exists idx_notifications_priority on public.notifications using btree (priority) TABLESPACE pg_default;

create index IF not exists idx_notifications_scheduled on public.notifications using btree (scheduled_for) TABLESPACE pg_default;

create index IF not exists idx_notifications_sent on public.notifications using btree (is_sent) TABLESPACE pg_default;

create index IF not exists idx_notifications_read on public.notifications using btree (in_app_read) TABLESPACE pg_default;

create index IF not exists idx_notifications_recipient_type on public.notifications using btree (recipient_id, notification_type) TABLESPACE pg_default;

create index IF not exists idx_notifications_recipient_priority on public.notifications using btree (recipient_id, priority desc, created_at desc) TABLESPACE pg_default;

create index IF not exists idx_notifications_recipient_unread on public.notifications using btree (recipient_id, in_app_read) TABLESPACE pg_default
where
  (in_app_read = false);

create index IF not exists idx_notifications_send_status on public.notifications using btree (is_sent, scheduled_for) TABLESPACE pg_default;

create index IF not exists idx_notifications_send_attempts on public.notifications using btree (send_attempts, last_attempt_at) TABLESPACE pg_default
where
  (send_attempts > 0);

create index IF not exists idx_notifications_email_sent on public.notifications using btree (email_sent, email_sent_at) TABLESPACE pg_default;

create index IF not exists idx_notifications_sms_sent on public.notifications using btree (sms_sent, sms_sent_at) TABLESPACE pg_default;

create index IF not exists idx_notifications_shipment_type on public.notifications using btree (shipment_id, notification_type) TABLESPACE pg_default
where
  (shipment_id is not null);

create trigger update_notifications_updated_at BEFORE
update on notifications for EACH row
execute FUNCTION update_updated_at_column ();


create table public.notification_preferences (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  email_enabled boolean null default true,
  sms_enabled boolean null default false,
  line_enabled boolean null default false,
  wechat_enabled boolean null default false,
  in_app_enabled boolean null default true,
  status_updates_enabled boolean null default true,
  assignment_notifications_enabled boolean null default true,
  document_notifications_enabled boolean null default true,
  delay_alerts_enabled boolean null default true,
  system_notifications_enabled boolean null default true,
  quiet_hours_start time without time zone null,
  quiet_hours_end time without time zone null,
  timezone text null default 'UTC'::text,
  language text null default 'en'::text,
  date_format text null default 'YYYY-MM-DD'::text,
  time_format text null default '24h'::text,
  email_digest_frequency text null default 'immediate'::text,
  max_sms_per_day integer null default 10,
  max_line_per_day integer null default 20,
  emergency_override boolean null default true,
  weekend_delivery boolean null default false,
  created_at timestamp with time zone null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone null default CURRENT_TIMESTAMP,
  constraint notification_preferences_pkey primary key (id),
  constraint notification_preferences_user_id_key unique (user_id),
  constraint notification_preferences_user_id_fkey foreign KEY (user_id) references profiles (user_id) on delete CASCADE,
  constraint valid_language check ((language ~* '^[a-z]{2}(-[A-Z]{2})?$'::text)),
  constraint valid_quiet_hours check (
    (
      (
        (quiet_hours_start is null)
        and (quiet_hours_end is null)
      )
      or (
        (quiet_hours_start is not null)
        and (quiet_hours_end is not null)
      )
    )
  ),
  constraint valid_time_format check (
    (
      time_format = any (array['12h'::text, '24h'::text])
    )
  ),
  constraint positive_limits check (
    (
      (max_sms_per_day > 0)
      and (max_line_per_day > 0)
    )
  ),
  constraint valid_frequency check (
    (
      email_digest_frequency = any (
        array[
          'immediate'::text,
          'hourly'::text,
          'daily'::text,
          'weekly'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_notification_preferences_user on public.notification_preferences using btree (user_id) TABLESPACE pg_default;

create trigger update_notification_preferences_updated_at BEFORE
update on notification_preferences for EACH row
execute FUNCTION update_updated_at_column ();


create table public.transportation (
  id uuid not null default gen_random_uuid (),
  shipment_id uuid null,
  carrier_id uuid null,
  driver_id uuid null,
  vehicle_head_number text null,
  vehicle_tail_number text null,
  driver_phone text null,
  assignment_date timestamp with time zone null,
  pickup_container_location text null,
  pickup_container_gps_coordinates point null,
  pickup_product_location text null,
  pickup_product_gps_coordinates point null,
  delivery_location text null,
  delivery_gps_coordinates point null,
  notes text null,
  estimated_distance numeric null,
  created_at timestamp with time zone null default now(),
  constraint transportation_pkey primary key (id),
  constraint transportation_carrier_id_fkey foreign KEY (carrier_id) references companies (id),
  constraint transportation_driver_id_fkey foreign KEY (driver_id) references profiles (user_id),
  constraint transportation_shipment_id_fkey foreign KEY (shipment_id) references shipments (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_transportation_carrier_driver on public.transportation using btree (carrier_id, driver_id) TABLESPACE pg_default;

create index IF not exists idx_transportation_shipment on public.transportation using btree (shipment_id) TABLESPACE pg_default;

create index IF not exists idx_transportation_pickup_gps on public.transportation using gist (pickup_product_gps_coordinates) TABLESPACE pg_default
where
  (pickup_product_gps_coordinates is not null);

create index IF not exists idx_transportation_delivery_gps on public.transportation using gist (delivery_gps_coordinates) TABLESPACE pg_default
where
  (delivery_gps_coordinates is not null);

create index IF not exists idx_transportation_vehicle_head on public.transportation using btree (vehicle_head_number) TABLESPACE pg_default
where
  (vehicle_head_number is not null);

create index IF not exists idx_transportation_vehicle_tail on public.transportation using btree (vehicle_tail_number) TABLESPACE pg_default
where
  (vehicle_tail_number is not null);

create trigger sync_transportation_pickup_coordinates BEFORE INSERT
or
update on transportation for EACH row
execute FUNCTION sync_gps_coordinates ();

create trigger update_transportation_updated_at BEFORE
update on transportation for EACH row
execute FUNCTION update_updated_at_column ();


create table public.drivers (
  id uuid not null default gen_random_uuid (),
  carrier_id uuid not null,
  driver_first_name text not null,
  driver_last_name text not null,
  driver_code text null,
  phone text null,
  line_id text null,
  driver_picture_path text null,
  driver_picture_mime_type text null,
  notes text null,
  is_active boolean not null default true,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint drivers_pkey primary key (id),
  constraint drivers_carrier_id_fkey foreign KEY (carrier_id) references companies (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_drivers_carrier on public.drivers using btree (carrier_id) TABLESPACE pg_default;

create index IF not exists idx_drivers_code on public.drivers using btree (driver_code) TABLESPACE pg_default;

create index IF not exists idx_drivers_phone on public.drivers using btree (phone) TABLESPACE pg_default;

create index IF not exists idx_drivers_active on public.drivers using btree (is_active) TABLESPACE pg_default;

create index IF not exists idx_drivers_carrier_active on public.drivers using btree (carrier_id, is_active) TABLESPACE pg_default
where
  (is_active = true);

create index IF not exists idx_drivers_phone_lookup on public.drivers using btree (phone) TABLESPACE pg_default;

create index IF not exists idx_drivers_line_id on public.drivers using btree (line_id) TABLESPACE pg_default
where
  (line_id is not null);

create trigger update_drivers_updated_at BEFORE
update on drivers for EACH row
execute FUNCTION update_updated_at_column ();

