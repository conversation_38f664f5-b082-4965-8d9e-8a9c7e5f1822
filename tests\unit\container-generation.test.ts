import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock the Supabase client creation before importing the service
vi.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          in: vi.fn(() => ({
            eq: vi.fn(() => ({
              then: vi.fn(),
            })),
          })),
        })),
      })),
    })),
  }),
}))

import { ContainerGenerationService } from '@/lib/services/container-generation'
import type { ProductAllocation } from '@/lib/services/container-generation'

describe('ContainerGenerationService', () => {
  let service: ContainerGenerationService

  beforeEach(() => {
    service = new ContainerGenerationService()
    vi.clearAllMocks()
  })

  describe('calculateTotalRequirements', () => {
    it('should calculate correct total requirements for products', () => {
      const products: ProductAllocation[] = [
        {
          product_id: '1',
          quantity: 100,
          packaging_type: 'Bag',
          gross_weight_per_package: 50,
          net_weight_per_package: 45,
        },
        {
          product_id: '2',
          quantity: 50,
          packaging_type: 'Carton',
          gross_weight_per_package: 30,
          net_weight_per_package: 25,
        },
      ]

      const requirements = service.calculateTotalRequirements(products)

      expect(requirements.total_weight).toBe(6500) // (100 * 50) + (50 * 30)
      expect(requirements.total_volume).toBe(8) // (100 * 0.05) + (50 * 0.06)
      expect(requirements.requires_refrigeration).toBe(false)
      expect(requirements.requires_ventilation).toBe(false)
    })

    it('should detect refrigeration requirements', () => {
      const products: ProductAllocation[] = [
        {
          product_id: '1',
          quantity: 10,
          packaging_type: 'Bag',
          gross_weight_per_package: 25,
          net_weight_per_package: 20,
          temperature_require: '-18°C to -15°C',
        },
      ]

      const requirements = service.calculateTotalRequirements(products)

      expect(requirements.requires_refrigeration).toBe(true)
      expect(requirements.special_requirements).toContain('-18°C to -15°C')
    })

    it('should detect ventilation requirements', () => {
      const products: ProductAllocation[] = [
        {
          product_id: '1',
          quantity: 10,
          packaging_type: 'Bag',
          gross_weight_per_package: 25,
          net_weight_per_package: 20,
          vent_require: 'Fresh air circulation required',
        },
      ]

      const requirements = service.calculateTotalRequirements(products)

      expect(requirements.requires_ventilation).toBe(true)
      expect(requirements.special_requirements).toContain('Fresh air circulation required')
    })
  })

  describe('optimizeContainerAllocation', () => {
    it('should select dry container for standard products', () => {
      const requirements = {
        total_volume: 30,
        total_weight: 15000,
        requires_refrigeration: false,
        requires_ventilation: false,
        special_requirements: [],
      }

      const configs = service.optimizeContainerAllocation(requirements)

      expect(configs).toHaveLength(1)
      expect(configs[0].type).toBe('dry')
      expect(configs[0].count).toBeGreaterThan(0)
    })

    it('should select reefer container for refrigerated products', () => {
      const requirements = {
        total_volume: 25,
        total_weight: 12000,
        requires_refrigeration: true,
        requires_ventilation: false,
        special_requirements: ['-18°C'],
      }

      const configs = service.optimizeContainerAllocation(requirements)

      expect(configs).toHaveLength(1)
      expect(configs[0].type).toBe('reefer')
      expect(configs[0].count).toBeGreaterThan(0)
    })

    it('should select open_top container for ventilation requirements', () => {
      const requirements = {
        total_volume: 20,
        total_weight: 10000,
        requires_refrigeration: false,
        requires_ventilation: true,
        special_requirements: ['Fresh air'],
      }

      const configs = service.optimizeContainerAllocation(requirements)

      expect(configs).toHaveLength(1)
      expect(configs[0].type).toBe('open_top')
      expect(configs[0].count).toBeGreaterThan(0)
    })

    it('should calculate correct container count for large volumes', () => {
      const requirements = {
        total_volume: 150, // Requires multiple 40ft containers
        total_weight: 20000,
        requires_refrigeration: false,
        requires_ventilation: false,
        special_requirements: [],
      }

      const configs = service.optimizeContainerAllocation(requirements)

      expect(configs).toHaveLength(1)
      expect(configs[0].count).toBeGreaterThan(1) // Should need multiple containers
    })

    it('should handle weight-limited scenarios', () => {
      const requirements = {
        total_volume: 10, // Small volume
        total_weight: 40000, // Heavy weight requiring multiple containers
        requires_refrigeration: false,
        requires_ventilation: false,
        special_requirements: [],
      }

      const configs = service.optimizeContainerAllocation(requirements)

      expect(configs).toHaveLength(1)
      expect(configs[0].count).toBeGreaterThan(1) // Should need multiple containers due to weight
    })
  })

  describe('generateContainers', () => {
    it('should generate correct number of containers', async () => {
      const shipment_id = 'test-shipment-id'
      const configs = [
        {
          type: 'dry' as const,
          size: '40ft' as const,
          capacity_volume: 67.7,
          capacity_weight: 26700,
          count: 2,
        },
      ]

      const containers = await service.generateContainers(shipment_id, configs)

      expect(containers).toHaveLength(2)
      expect(containers[0].container_type).toBe('dry')
      expect(containers[0].container_size).toBe('40ft')
      expect(containers[0].volume).toBe(67.7)
      expect(containers[0].status).toBe('empty')
      expect(containers[0].container_number).toBeDefined()
    })

    it('should generate unique container numbers', async () => {
      const shipment_id = 'test-shipment-id'
      const configs = [
        {
          type: 'dry' as const,
          size: '20ft' as const,
          capacity_volume: 33.2,
          capacity_weight: 28200,
          count: 3,
        },
      ]

      const containers = await service.generateContainers(shipment_id, configs)

      expect(containers).toHaveLength(3)
      
      const containerNumbers = containers.map(c => c.container_number)
      const uniqueNumbers = new Set(containerNumbers)
      
      expect(uniqueNumbers.size).toBe(3) // All numbers should be unique
    })
  })

  describe('integration scenarios', () => {
    it('should handle mixed packaging types correctly', () => {
      const products: ProductAllocation[] = [
        {
          product_id: '1',
          quantity: 50,
          packaging_type: 'Bag',
          gross_weight_per_package: 25,
          net_weight_per_package: 20,
        },
        {
          product_id: '2',
          quantity: 30,
          packaging_type: 'Plastic Basket',
          gross_weight_per_package: 15,
          net_weight_per_package: 12,
        },
        {
          product_id: '3',
          quantity: 100,
          packaging_type: 'Carton',
          gross_weight_per_package: 8,
          net_weight_per_package: 7,
        },
      ]

      const requirements = service.calculateTotalRequirements(products)

      // Total weight: (50*25) + (30*15) + (100*8) = 1250 + 450 + 800 = 2500kg
      expect(requirements.total_weight).toBe(2500)
      
      // Total volume: (50*0.05) + (30*0.08) + (100*0.06) = 2.5 + 2.4 + 6 = 10.9m³
      expect(requirements.total_volume).toBe(10.9)

      const configs = service.optimizeContainerAllocation(requirements)
      expect(configs).toHaveLength(1)
      expect(configs[0].count).toBe(1) // Should fit in one 20ft container
    })

    it('should handle zero quantity gracefully', () => {
      const products: ProductAllocation[] = [
        {
          product_id: '1',
          quantity: 0,
          packaging_type: 'Bag',
          gross_weight_per_package: 25,
          net_weight_per_package: 20,
        },
      ]

      const requirements = service.calculateTotalRequirements(products)

      expect(requirements.total_weight).toBe(0)
      expect(requirements.total_volume).toBe(0)

      const configs = service.optimizeContainerAllocation(requirements)
      expect(configs).toHaveLength(1)
      expect(configs[0].count).toBe(1) // Fallback to at least one container
    })

    it('should handle edge case of very heavy small items', () => {
      const products: ProductAllocation[] = [
        {
          product_id: '1',
          quantity: 1000,
          packaging_type: 'Bag',
          gross_weight_per_package: 100, // Very heavy bags
          net_weight_per_package: 95,
        },
      ]

      const requirements = service.calculateTotalRequirements(products)

      expect(requirements.total_weight).toBe(100000) // 100kg * 1000
      expect(requirements.total_volume).toBe(50) // 0.05m³ * 1000

      const configs = service.optimizeContainerAllocation(requirements)
      expect(configs).toHaveLength(1)
      expect(configs[0].count).toBeGreaterThan(3) // Should need multiple containers due to weight
    })
  })
})