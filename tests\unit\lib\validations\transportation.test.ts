import { describe, expect, it } from 'vitest'
import {
  transportationFormSchema,
  transportationUpdateSchema,
  validateTransportationForm,
  validateTransportationUpdate,
  validateCarrierCompany,
  calculateDistance,
} from '@/lib/validations/transportation'

describe('Transportation Validation', () => {
  describe('transportationFormSchema', () => {
    const validFormData = {
      carrier_id: '550e8400-e29b-41d4-a716-************',
      driver_id: '550e8400-e29b-41d4-a716-446655440001',
      vehicle_head_number: 'HEAD-123',
      vehicle_tail_number: 'TAIL-456',
      driver_phone: '+66123456789',
      assignment_date: '2024-01-01T10:00:00Z',
      pickup_container_location: 'Container Terminal A',
      pickup_product_location: 'Factory B',
      delivery_location: 'Warehouse C',
      estimated_distance: 150.5,
      notes: 'Handle with care',
    }

    it('accepts valid form data', () => {
      const result = transportationFormSchema.safeParse(validFormData)
      expect(result.success).toBe(true)
    })

    it('requires carrier_id to be a valid UUID', () => {
      const invalidData = { ...validFormData, carrier_id: 'invalid-uuid' }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Please select a valid carrier company')
      }
    })

    it('allows optional driver_id', () => {
      const dataWithoutDriver = { ...validFormData, driver_id: '' }
      const result = transportationFormSchema.safeParse(dataWithoutDriver)
      expect(result.success).toBe(true)
    })

    it('validates driver_id as UUID when provided', () => {
      const invalidData = { ...validFormData, driver_id: 'invalid-uuid' }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Please select a valid driver')
      }
    })

    it('requires delivery_location', () => {
      const invalidData = { ...validFormData, delivery_location: '' }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Delivery location is required')
      }
    })

    it('validates vehicle number length limits', () => {
      const longNumber = 'A'.repeat(21) // Over 20 character limit
      const invalidData = { ...validFormData, vehicle_head_number: longNumber }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('must be less than 20 characters')
      }
    })

    it('validates phone number format', () => {
      const invalidData = { ...validFormData, driver_phone: 'invalid-phone' }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Please enter a valid phone number')
      }
    })

    it('accepts valid phone number formats', () => {
      const phoneNumbers = [
        '+66123456789',
        '0123456789',
        '+****************',
        '************',
      ]

      phoneNumbers.forEach(phone => {
        const data = { ...validFormData, driver_phone: phone }
        const result = transportationFormSchema.safeParse(data)
        expect(result.success).toBe(true)
      })
    })

    it('validates assignment_date as ISO datetime', () => {
      const invalidData = { ...validFormData, assignment_date: 'invalid-date' }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Please select a valid assignment date')
      }
    })

    it('validates location field lengths', () => {
      const longLocation = 'A'.repeat(256) // Over 255 character limit
      const invalidData = { ...validFormData, pickup_container_location: longLocation }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('must be less than 255 characters')
      }
    })

    it('validates GPS coordinates', () => {
      const dataWithCoords = {
        ...validFormData,
        pickup_product_gps_coordinates: { lat: 13.7563, lng: 100.5018 },
        delivery_gps_coordinates: { lat: 13.7763, lng: 100.5218 },
      }
      
      const result = transportationFormSchema.safeParse(dataWithCoords)
      expect(result.success).toBe(true)
    })

    it('validates GPS coordinate ranges', () => {
      const dataWithInvalidCoords = {
        ...validFormData,
        pickup_product_gps_coordinates: { lat: 91, lng: 100.5018 }, // Lat > 90
      }
      
      const result = transportationFormSchema.safeParse(dataWithInvalidCoords)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Latitude must be between -90 and 90')
      }
    })

    it('validates estimated distance range', () => {
      const invalidData = { ...validFormData, estimated_distance: 15000 } // Over 10,000 limit
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Distance must be less than 10,000 km')
      }
    })

    it('validates negative distances', () => {
      const invalidData = { ...validFormData, estimated_distance: -10 }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Distance must be positive')
      }
    })

    it('validates notes length limit', () => {
      const longNotes = 'A'.repeat(1001) // Over 1000 character limit
      const invalidData = { ...validFormData, notes: longNotes }
      const result = transportationFormSchema.safeParse(invalidData)
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('must be less than 1000 characters')
      }
    })

    it('accepts empty optional fields', () => {
      const minimalData = {
        carrier_id: '550e8400-e29b-41d4-a716-************',
        assignment_date: '2024-01-01T10:00:00Z',
        delivery_location: 'Warehouse C',
        driver_id: '',
        vehicle_head_number: '',
        vehicle_tail_number: '',
        driver_phone: '',
        pickup_container_location: '',
        pickup_product_location: '',
        notes: '',
      }
      
      const result = transportationFormSchema.safeParse(minimalData)
      expect(result.success).toBe(true)
    })
  })

  describe('transportationUpdateSchema', () => {
    it('requires id field', () => {
      const updateData = {
        carrier_id: '550e8400-e29b-41d4-a716-************',
        delivery_location: 'Updated Warehouse',
      }
      
      const result = transportationUpdateSchema.safeParse(updateData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.path.includes('id') && issue.code === 'invalid_type'
        )).toBe(true)
      }
    })

    it('allows partial updates', () => {
      const updateData = {
        id: '550e8400-e29b-41d4-a716-************',
        delivery_location: 'Updated Warehouse',
        notes: 'Updated notes',
      }
      
      const result = transportationUpdateSchema.safeParse(updateData)
      expect(result.success).toBe(true)
    })

    it('validates id as UUID', () => {
      const updateData = {
        id: 'invalid-uuid',
        delivery_location: 'Updated Warehouse',
      }
      
      const result = transportationUpdateSchema.safeParse(updateData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.path.includes('id') && issue.message.includes('Invalid')
        )).toBe(true)
      }
    })
  })

  describe('validateTransportationForm', () => {
    it('returns success for valid data', () => {
      const validData = {
        carrier_id: '550e8400-e29b-41d4-a716-************',
        assignment_date: '2024-01-01T10:00:00Z',
        delivery_location: 'Warehouse C',
      }
      
      const result = validateTransportationForm(validData)
      expect(result.success).toBe(true)
    })

    it('returns error for invalid data', () => {
      const invalidData = {
        carrier_id: 'invalid-uuid',
        assignment_date: '2024-01-01T10:00:00Z',
        delivery_location: '',
      }
      
      const result = validateTransportationForm(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('validateTransportationUpdate', () => {
    it('returns success for valid update data', () => {
      const validData = {
        id: '550e8400-e29b-41d4-a716-************',
        delivery_location: 'Updated Warehouse',
      }
      
      const result = validateTransportationUpdate(validData)
      expect(result.success).toBe(true)
    })

    it('returns error for invalid update data', () => {
      const invalidData = {
        delivery_location: 'Updated Warehouse',
      }
      
      const result = validateTransportationUpdate(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('validateCarrierCompany', () => {
    it('returns true for carrier company type', () => {
      expect(validateCarrierCompany('carrier')).toBe(true)
    })

    it('returns false for non-carrier company types', () => {
      expect(validateCarrierCompany('customer')).toBe(false)
      expect(validateCarrierCompany('shipper')).toBe(false)
      expect(validateCarrierCompany('factory')).toBe(false)
      expect(validateCarrierCompany('')).toBe(false)
    })
  })

  describe('calculateDistance', () => {
    it('calculates distance between Bangkok and Pattaya correctly', () => {
      // Bangkok coordinates
      const bangkokLat = 13.7563
      const bangkokLng = 100.5018
      
      // Pattaya coordinates  
      const pattayaLat = 12.9236
      const pattayaLng = 100.8825
      
      const distance = calculateDistance(bangkokLat, bangkokLng, pattayaLat, pattayaLng)
      
      // Distance should be approximately 101 km
      expect(distance).toBeCloseTo(101, 0)
    })

    it('calculates distance between same coordinates as zero', () => {
      const distance = calculateDistance(13.7563, 100.5018, 13.7563, 100.5018)
      expect(distance).toBe(0)
    })

    it('calculates distance between Bangkok and Chiang Mai correctly', () => {
      // Bangkok coordinates
      const bangkokLat = 13.7563
      const bangkokLng = 100.5018
      
      // Chiang Mai coordinates
      const chiangMaiLat = 18.7883
      const chiangMaiLng = 98.9853
      
      const distance = calculateDistance(bangkokLat, bangkokLng, chiangMaiLat, chiangMaiLng)
      
      // Distance should be approximately 582 km
      expect(distance).toBeCloseTo(582, 0)
    })

    it('returns rounded result to 2 decimal places', () => {
      const distance = calculateDistance(13.7563, 100.5018, 13.7564, 100.5019)
      
      // Should return a number with at most 2 decimal places
      const decimalPlaces = (distance.toString().split('.')[1] || '').length
      expect(decimalPlaces).toBeLessThanOrEqual(2)
    })

    it('handles negative coordinates correctly', () => {
      // Test with coordinates in different hemispheres
      const distance = calculateDistance(-33.8688, 151.2093, 40.7128, -74.0060) // Sydney to NYC
      
      // Should return a positive distance
      expect(distance).toBeGreaterThan(0)
      expect(distance).toBeCloseTo(15989, 0) // Approximate distance
    })

    it('handles longitude crossing 180 degrees', () => {
      // Test coordinates that cross the international date line
      const distance = calculateDistance(35.6762, 139.6503, 21.3099, -157.8581) // Tokyo to Honolulu
      
      expect(distance).toBeGreaterThan(0)
      expect(distance).toBeCloseTo(6209, 0) // Approximate distance
    })
  })
})

describe('Edge Cases', () => {
  describe('GPS Coordinate Validation', () => {
    it('validates extreme but valid coordinates', () => {
      const extremeCoords = {
        carrier_id: '550e8400-e29b-41d4-a716-************',
        assignment_date: '2024-01-01T10:00:00Z',
        delivery_location: 'North Pole',
        delivery_gps_coordinates: { lat: 90, lng: 180 }, // Valid extreme coordinates
      }
      
      const result = transportationFormSchema.safeParse(extremeCoords)
      expect(result.success).toBe(true)
    })

    it('rejects coordinates outside valid ranges', () => {
      const invalidCoords = {
        carrier_id: '550e8400-e29b-41d4-a716-************',
        assignment_date: '2024-01-01T10:00:00Z',
        delivery_location: 'Invalid Location',
        delivery_gps_coordinates: { lat: 91, lng: 181 }, // Invalid coordinates
      }
      
      const result = transportationFormSchema.safeParse(invalidCoords)
      expect(result.success).toBe(false)
    })
  })

  describe('Special Characters in Text Fields', () => {
    it('handles special characters in location names', () => {
      const dataWithSpecialChars = {
        carrier_id: '550e8400-e29b-41d4-a716-************',
        assignment_date: '2024-01-01T10:00:00Z',
        delivery_location: 'Café & Co. (Warehouse #1)',
        pickup_container_location: 'Terminal-A/Gate-3',
        notes: 'Handle with care! Temperature: 2-8°C',
      }
      
      const result = transportationFormSchema.safeParse(dataWithSpecialChars)
      expect(result.success).toBe(true)
    })
  })

  describe('International Phone Numbers', () => {
    it('accepts various international phone number formats', () => {
      const phoneFormats = [
        '******-123-4567',    // US format
        '+44 20 7946 0958',   // UK format  
        '+86 138 0013 8000',  // China format
        '+81-3-1234-5678',    // Japan format
        '+33 1 42 86 83 26',  // France format
      ]

      phoneFormats.forEach(phone => {
        const data = {
          carrier_id: '550e8400-e29b-41d4-a716-************',
          assignment_date: '2024-01-01T10:00:00Z',
          delivery_location: 'Test Location',
          driver_phone: phone,
        }
        
        const result = transportationFormSchema.safeParse(data)
        expect(result.success).toBe(true)
      })
    })
  })
})