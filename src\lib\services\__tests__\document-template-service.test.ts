import { describe, it, expect, vi, beforeEach, Mock } from 'vitest'
import { DocumentTemplateService } from '../document-template-service'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/supabase/types'
import type { DocumentTemplate, DocumentTemplateInsert } from '@/types/document-template'

// Create a chainable mock builder
const createMockQueryBuilder = (result?: any) => {
  const mockBuilder = {
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    neq: vi.fn().mockReturnThis(),
    in: vi.fn().mockReturnThis(),
    or: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    single: vi.fn(),
    // Add resolved value for the end of the chain
    ...result
  }
  
  return mockBuilder
}

// Mock Supabase client
const mockSupabaseClient = {
  from: vi.fn(),
  channel: vi.fn(() => ({
    on: vi.fn(() => ({
      subscribe: vi.fn()
    }))
  }))
} as unknown as SupabaseClient<Database>

const mockFrom = mockSupabaseClient.from as Mock

// Mock template data
const mockTemplate: DocumentTemplate = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  template_name: 'Test Invoice Template',
  document_type: 'invoice_fob',
  version: '1.0',
  template_content: 'Invoice #{{invoice_number}} for {{customer_name}}',
  template_data: null,
  template_styles: null,
  page_size: 'A4',
  page_orientation: 'portrait',
  margin_top: 20,
  margin_bottom: 20,
  margin_left: 20,
  margin_right: 20,
  language: 'en',
  currency_format: 'USD',
  date_format: 'YYYY-MM-DD',
  number_format: 'en-US',
  description: 'Test template for invoices',
  usage_notes: null,
  required_fields: ['invoice_number', 'customer_name'],
  is_active: true,
  is_default: false,
  created_by: 'user-123',
  created_at: '2024-01-15T10:00:00.000Z',
  updated_at: '2024-01-15T10:00:00.000Z',
}

const mockTemplateInsert: DocumentTemplateInsert = {
  template_name: 'New Test Template',
  document_type: 'invoice_cif',
  version: '1.0',
  template_content: 'Test content with {{placeholder}}',
  description: 'New test template',
}

describe('DocumentTemplateService', () => {
  let service: DocumentTemplateService

  beforeEach(() => {
    vi.clearAllMocks()
    service = new DocumentTemplateService(mockSupabaseClient)
  })

  describe('getTemplates', () => {
    it('should fetch templates with basic filters and pagination', async () => {
      const mockData = [mockTemplate]
      
      // Create the main query mock
      const mockMainQuery = createMockQueryBuilder()
      mockMainQuery.range.mockResolvedValue({ data: mockData, error: null })
      
      // Create the count query mock  
      const mockCountQuery = createMockQueryBuilder()
      mockCountQuery.eq.mockResolvedValue({ count: 1, error: null })
      
      // Setup the from mock to return different builders for different calls
      mockFrom
        .mockReturnValueOnce(mockMainQuery) // First call for main query
        .mockReturnValueOnce({ // Second call for count query
          select: vi.fn().mockReturnValue(mockCountQuery)
        })

      const result = await service.getTemplates({
        filters: { document_type: ['invoice_fob'] },
        sort: { field: 'template_name', direction: 'asc' },
        pagination: { page: 1, pageSize: 10 }
      })

      expect(result.data).toEqual(mockData)
      expect(result.totalCount).toBe(1)
      expect(result.error).toBeUndefined()
      expect(mockFrom).toHaveBeenCalledWith('document_templates')
      expect(mockMainQuery.in).toHaveBeenCalledWith('document_type', ['invoice_fob'])
      expect(mockMainQuery.order).toHaveBeenCalledWith('template_name', { ascending: true })
      expect(mockMainQuery.range).toHaveBeenCalledWith(0, 9)
    })

    it('should handle search filters', async () => {
      const mockData = [mockTemplate]
      mockRange.mockResolvedValueOnce({ data: mockData, error: null })
      mockFrom.mockReturnValueOnce({
        select: vi.fn().mockReturnValue({
          or: mockOr.mockResolvedValue({ count: 1, error: null })
        })
      })

      await service.getTemplates({
        filters: { search: 'test' },
        sort: { field: 'updated_at', direction: 'desc' },
        pagination: { page: 1, pageSize: 10 }
      })

      expect(mockOr).toHaveBeenCalledWith('template_name.ilike.%test%,description.ilike.%test%')
    })

    it('should handle query errors gracefully', async () => {
      const mockError = new Error('Database connection failed')
      mockRange.mockResolvedValueOnce({ data: null, error: mockError })

      const result = await service.getTemplates({
        filters: {},
        sort: { field: 'updated_at', direction: 'desc' },
        pagination: { page: 1, pageSize: 10 }
      })

      expect(result.data).toEqual([])
      expect(result.totalCount).toBe(0)
      expect(result.error).toContain('Database connection failed')
    })
  })

  describe('getTemplateById', () => {
    it('should fetch a single template by ID', async () => {
      mockSingle.mockResolvedValueOnce({ data: mockTemplate, error: null })

      const result = await service.getTemplateById('123e4567-e89b-12d3-a456-426614174000')

      expect(result).toEqual(mockTemplate)
      expect(mockFrom).toHaveBeenCalledWith('document_templates')
      expect(mockEq).toHaveBeenCalledWith('id', '123e4567-e89b-12d3-a456-426614174000')
      expect(mockSingle).toHaveBeenCalled()
    })

    it('should return null for non-existent template', async () => {
      mockSingle.mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } })

      const result = await service.getTemplateById('non-existent-id')

      expect(result).toBeNull()
    })

    it('should handle database errors', async () => {
      const mockError = new Error('Database error')
      mockSingle.mockResolvedValueOnce({ data: null, error: mockError })

      const result = await service.getTemplateById('test-id')

      expect(result).toBeNull()
    })
  })

  describe('createTemplate', () => {
    it('should create a new template successfully', async () => {
      // Mock duplicate check - no existing template
      mockSingle.mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } })
      
      // Mock successful insert
      mockSingle.mockResolvedValueOnce({ data: mockTemplate, error: null })

      const result = await service.createTemplate(mockTemplateInsert, 'user-123')

      expect(result.data).toEqual(mockTemplate)
      expect(result.error).toBeNull()
      expect(mockInsert).toHaveBeenCalledWith(
        expect.objectContaining({
          template_name: mockTemplateInsert.template_name,
          document_type: mockTemplateInsert.document_type,
          created_by: 'user-123',
          version: '1.0',
          page_size: 'A4',
          page_orientation: 'portrait',
          is_active: true,
        })
      )
    })

    it('should prevent duplicate template names', async () => {
      // Mock duplicate check - existing template found
      mockSingle.mockResolvedValueOnce({ 
        data: { id: 'existing-id', template_name: mockTemplateInsert.template_name }, 
        error: null 
      })

      const result = await service.createTemplate(mockTemplateInsert, 'user-123')

      expect(result.data).toBeNull()
      expect(result.error).toBe('Template name already exists')
      expect(mockInsert).not.toHaveBeenCalled()
    })

    it('should handle insert errors', async () => {
      // Mock duplicate check - no existing template
      mockSingle.mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } })
      
      // Mock insert error
      const mockError = new Error('Insert failed')
      mockSingle.mockResolvedValueOnce({ data: null, error: mockError })

      const result = await service.createTemplate(mockTemplateInsert, 'user-123')

      expect(result.data).toBeNull()
      expect(result.error).toBe('Insert failed')
    })
  })

  describe('updateTemplate', () => {
    const updateData = {
      template_name: 'Updated Template Name',
      description: 'Updated description'
    }

    it('should update template successfully', async () => {
      const updatedTemplate = { ...mockTemplate, ...updateData }
      
      // Mock name uniqueness check - no conflicts
      mockSingle.mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } })
      
      // Mock successful update
      mockSingle.mockResolvedValueOnce({ data: updatedTemplate, error: null })

      const result = await service.updateTemplate('test-id', updateData)

      expect(result.data).toEqual(updatedTemplate)
      expect(result.error).toBeNull()
      expect(mockUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          template_name: updateData.template_name,
          description: updateData.description,
          updated_at: expect.any(String),
        })
      )
      expect(mockEq).toHaveBeenCalledWith('id', 'test-id')
    })

    it('should prevent duplicate names during update', async () => {
      // Mock name uniqueness check - conflict found
      mockSingle.mockResolvedValueOnce({ 
        data: { id: 'other-id', template_name: updateData.template_name }, 
        error: null 
      })

      const result = await service.updateTemplate('test-id', updateData)

      expect(result.data).toBeNull()
      expect(result.error).toBe('Template name already exists')
      expect(mockUpdate).not.toHaveBeenCalled()
    })

    it('should handle update errors', async () => {
      // Mock name uniqueness check - no conflicts
      mockSingle.mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } })
      
      // Mock update error
      const mockError = new Error('Update failed')
      mockSingle.mockResolvedValueOnce({ data: null, error: mockError })

      const result = await service.updateTemplate('test-id', updateData)

      expect(result.data).toBeNull()
      expect(result.error).toBe('Update failed')
    })
  })

  describe('deleteTemplate', () => {
    it('should delete template successfully when not referenced', async () => {
      // Mock document reference check - no references
      mockLimit.mockResolvedValueOnce({ data: [], error: null })
      
      // Mock successful delete
      vi.fn().mockResolvedValueOnce({ error: null })

      const result = await service.deleteTemplate('test-id')

      expect(result.success).toBe(true)
      expect(result.error).toBeNull()
    })

    it('should prevent deletion when template is referenced', async () => {
      // Mock document reference check - references found
      mockLimit.mockResolvedValueOnce({ data: [{ id: 'doc-1' }], error: null })

      const result = await service.deleteTemplate('test-id')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Cannot delete template that is referenced by existing documents')
      expect(mockDelete).not.toHaveBeenCalled()
    })
  })

  describe('getDefaultTemplate', () => {
    it('should fetch default template for document type', async () => {
      mockSingle.mockResolvedValueOnce({ data: mockTemplate, error: null })

      const result = await service.getDefaultTemplate('invoice_fob')

      expect(result).toEqual(mockTemplate)
      expect(mockEq).toHaveBeenCalledWith('document_type', 'invoice_fob')
      expect(mockEq).toHaveBeenCalledWith('is_default', true)
      expect(mockEq).toHaveBeenCalledWith('is_active', true)
    })

    it('should return null when no default template exists', async () => {
      mockSingle.mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } })

      const result = await service.getDefaultTemplate('invoice_fob')

      expect(result).toBeNull()
    })
  })

  describe('toggleTemplateStatus', () => {
    it('should activate template successfully', async () => {
      vi.fn().mockResolvedValueOnce({ error: null })

      const result = await service.toggleTemplateStatus('test-id', true)

      expect(result.success).toBe(true)
      expect(result.error).toBeNull()
      expect(mockUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          is_active: true,
          updated_at: expect.any(String),
        })
      )
    })

    it('should deactivate template and unset default', async () => {
      vi.fn().mockResolvedValueOnce({ error: null })

      const result = await service.toggleTemplateStatus('test-id', false)

      expect(result.success).toBe(true)
      expect(mockUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          is_active: false,
          is_default: false,
          updated_at: expect.any(String),
        })
      )
    })
  })

  describe('cloneTemplate', () => {
    it('should clone template successfully', async () => {
      // Mock original template fetch
      mockSingle.mockResolvedValueOnce({ data: mockTemplate, error: null })
      
      // Mock duplicate check for new name
      mockSingle.mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } })
      
      // Mock successful insert
      const clonedTemplate = { ...mockTemplate, id: 'new-id', version: '2.0' }
      mockSingle.mockResolvedValueOnce({ data: clonedTemplate, error: null })

      const result = await service.cloneTemplate('original-id', '2.0', 'user-123')

      expect(result.success).toBe(true)
      expect(result.data?.version).toBe('2.0')
      expect(result.data?.template_name).toContain('(v2.0)')
    })

    it('should handle missing original template', async () => {
      mockSingle.mockResolvedValueOnce({ data: null, error: null })

      const result = await service.cloneTemplate('non-existent-id', '2.0', 'user-123')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Original template not found')
    })
  })
})