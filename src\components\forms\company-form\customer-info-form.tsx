'use client'

import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { CreditCard, Globe, FileText, Users, DollarSign } from 'lucide-react'
import type { CustomerInfo } from '@/lib/validations/companies'
import { CUSTOMER_TYPES, INCOTERMS_OPTIONS } from '@/lib/validations/companies'

interface CustomerInfoFormProps {
  value?: Partial<CustomerInfo>
  onChange: (info: Partial<CustomerInfo>) => void
  errors?: any
}

export function CustomerInfoForm({
  value = {},
  onChange,
  errors = {},
}: CustomerInfoFormProps) {
  const updateField = <K extends keyof CustomerInfo>(
    field: K,
    fieldValue: CustomerInfo[K]
  ) => {
    onChange({ ...value, [field]: fieldValue })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2 pb-2 border-b border-slate-600">
        <Users className="h-5 w-5 text-blue-500" />
        <h3 className="text-lg font-semibold text-white">Customer Details</h3>
        <Badge
          variant="secondary"
          className="bg-blue-500/20 text-blue-300 border-blue-400"
        >
          Business Information
        </Badge>
      </div>

      {/* Customer Type and Credit Limit */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Customer Type */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Users className="h-4 w-4 text-blue-500" />
            <span>Customer Type *</span>
          </Label>
          <Select
            value={value.customer_type || 'regular'}
            onValueChange={val => updateField('customer_type', val as any)}
          >
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-blue-500 focus:ring-blue-500">
              <SelectValue placeholder="Select customer type" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {CUSTOMER_TYPES.map(type => (
                <SelectItem
                  key={type.value}
                  value={type.value}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  <div className="flex items-center space-x-2">
                    <span>{type.label}</span>
                    {type.value === 'vip' && (
                      <Badge
                        variant="secondary"
                        className="text-xs bg-yellow-500/20 text-yellow-300 border-yellow-400"
                      >
                        Premium
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.customer_type && (
            <p className="text-sm text-red-400">
              {errors.customer_type.message}
            </p>
          )}
        </div>

        {/* Credit Limit */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <CreditCard className="h-4 w-4 text-green-500" />
            <span>Credit Limit (THB)</span>
          </Label>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              type="number"
              placeholder="0.00"
              min="0"
              step="0.01"
              value={value.credit_limit || ''}
              onChange={e =>
                updateField('credit_limit', parseFloat(e.target.value) || 0)
              }
              className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-500 focus:ring-green-500"
            />
          </div>
          {errors.credit_limit && (
            <p className="text-sm text-red-400">
              {errors.credit_limit.message}
            </p>
          )}
          <p className="text-xs text-slate-400">
            Enter maximum credit amount in Thai Baht
          </p>
        </div>
      </div>

      {/* Incoterms */}
      <div className="space-y-2">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Globe className="h-4 w-4 text-orange-500" />
          <span>Preferred Incoterms</span>
        </Label>
        <Select
          value={value.incoterms || 'no_preference'}
          onValueChange={val =>
            updateField(
              'incoterms',
              val === 'no_preference' ? undefined : (val as any)
            )
          }
        >
          <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
            <SelectValue placeholder="Select incoterms preference" />
          </SelectTrigger>
          <SelectContent className="bg-slate-800 border-slate-700">
            <SelectItem
              value="no_preference"
              className="text-slate-300 hover:bg-slate-700"
            >
              No preference
            </SelectItem>
            {INCOTERMS_OPTIONS.map(incoterm => (
              <SelectItem
                key={incoterm.value}
                value={incoterm.value}
                className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
              >
                {incoterm.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.incoterms && (
          <p className="text-sm text-red-400">{errors.incoterms.message}</p>
        )}
        <p className="text-xs text-slate-400">
          International Commercial Terms for trade transactions
        </p>
      </div>

      {/* Special Requirements */}
      <div className="space-y-2">
        <Label className="text-slate-200 flex items-center space-x-2">
          <FileText className="h-4 w-4 text-purple-500" />
          <span>Special Requirements</span>
        </Label>
        <Textarea
          placeholder="Enter any special handling requirements, preferences, or notes..."
          value={value.special_requirements || ''}
          onChange={e => updateField('special_requirements', e.target.value)}
          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-purple-500"
          rows={4}
        />
        {errors.special_requirements && (
          <p className="text-sm text-red-400">
            {errors.special_requirements.message}
          </p>
        )}
        <p className="text-xs text-slate-400">
          Describe any specific requirements for handling this customer's
          shipments
        </p>
      </div>

      {/* Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Users className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-slate-200">
              Customer Type
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Determines service level and pricing tier
          </p>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <CreditCard className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-slate-200">
              Credit Limit
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Maximum outstanding amount allowed
          </p>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Globe className="h-4 w-4 text-orange-500" />
            <span className="text-sm font-medium text-slate-200">
              Incoterms
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Preferred trade terms for shipments
          </p>
        </div>
      </div>
    </div>
  )
}
