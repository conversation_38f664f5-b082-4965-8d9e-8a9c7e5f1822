'use client'

import { useState, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Upload,
  Download,
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw,
  Trash2,
} from 'lucide-react'
import {
  useBulkImport,
  useRelationshipValidation,
} from '@/hooks/use-customer-shippers'
import type {
  CustomerShipperBulkData,
  CustomerShipperBulkResult,
} from '@/stores/customer-shipper-store'

interface BulkImportFormProps {
  onClose: () => void
  onSuccess?: (result: CustomerShipperBulkResult) => void
}

interface ParsedRow extends CustomerShipperBulkData {
  rowIndex: number
  isValid: boolean
  errors: string[]
}

export function BulkImportForm({ onClose, onSuccess }: BulkImportFormProps) {
  const { importFromCSV, generateCSVTemplate, isBulkImporting } =
    useBulkImport()
  const { validateBulkData } = useRelationshipValidation()

  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState<string>('')
  const [parsedRows, setParsedRows] = useState<ParsedRow[]>([])
  const [importResult, setImportResult] =
    useState<CustomerShipperBulkResult | null>(null)
  const [step, setStep] = useState<'upload' | 'preview' | 'result'>('upload')
  const [uploadError, setUploadError] = useState<string>('')

  const fileInputRef = useRef<HTMLInputElement>(null)

  // Download CSV template
  const handleDownloadTemplate = () => {
    const template = generateCSVTemplate()
    const blob = new Blob([template], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'customer-shipper-relationships-template.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      setUploadError('Please select a valid CSV file')
      return
    }

    if (file.size > 5 * 1024 * 1024) {
      // 5MB limit
      setUploadError('File size must be less than 5MB')
      return
    }

    setCsvFile(file)
    setUploadError('')

    // Read file content
    const reader = new FileReader()
    reader.onload = e => {
      const content = e.target?.result as string
      setCsvData(content)
      parseCsvData(content)
    }
    reader.onerror = () => {
      setUploadError('Error reading file')
    }
    reader.readAsText(file)
  }

  // Parse CSV line with proper handling of quoted fields
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    let i = 0

    while (i < line.length) {
      const char = line[i]
      const nextChar = line[i + 1]

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote inside quoted field
          current += '"'
          i += 2
          continue
        } else {
          // Start or end of quoted field
          inQuotes = !inQuotes
          i++
          continue
        }
      }

      if (char === ',' && !inQuotes) {
        // Field separator outside quotes
        result.push(current.trim())
        current = ''
        i++
        continue
      }

      // Regular character
      current += char
      i++
    }

    // Add the last field
    result.push(current.trim())
    return result
  }

  // Parse CSV data and validate
  const parseCsvData = useCallback(
    (content: string) => {
      try {
        const lines = content.trim().split('\n')
        if (lines.length < 2) {
          setUploadError(
            'CSV file must contain at least a header row and one data row'
          )
          return
        }

        const headers = parseCSVLine(lines[0]).map(h =>
          h.toLowerCase().replace(/^["']|["']$/g, '')
        )

        // Validate headers
        const requiredHeaders = ['customer_name', 'shipper_name']
        const hasRequiredHeaders = requiredHeaders.every(required =>
          headers.includes(required)
        )

        if (!hasRequiredHeaders) {
          setUploadError(
            'CSV must contain columns: customer_name, shipper_name'
          )
          return
        }

        // Parse rows
        const parsed: CustomerShipperBulkData[] = []
        for (let i = 1; i < lines.length; i++) {
          const values = parseCSVLine(lines[i]).map(v =>
            v.replace(/^["']|["']$/g, '')
          )
          if (values.length < 2 || !values.some(v => v)) continue // Skip empty rows

          const row: CustomerShipperBulkData = {
            customer_name: values[headers.indexOf('customer_name')] || '',
            shipper_name: values[headers.indexOf('shipper_name')] || '',
            is_default: false,
            notes: null,
          }

          // Parse optional columns
          const isDefaultIndex = headers.indexOf('is_default')
          if (isDefaultIndex >= 0 && values[isDefaultIndex]) {
            const defaultValue = values[isDefaultIndex].toLowerCase()
            row.is_default = ['true', '1', 'yes', 'y'].includes(defaultValue)
          }

          const notesIndex = headers.indexOf('notes')
          if (notesIndex >= 0 && values[notesIndex]) {
            row.notes = values[notesIndex]
          }

          parsed.push(row)
        }

        // Validate parsed data
        const validationResults = validateBulkData(parsed)
        const validationMap = new Map(
          validationResults.map(result => [result.row - 1, result.errors])
        )

        const parsedWithValidation: ParsedRow[] = parsed.map((row, index) => ({
          ...row,
          rowIndex: index + 2, // +2 because index starts at 0 and we skip header row
          isValid: !validationMap.has(index),
          errors: validationMap.get(index) || [],
        }))

        setParsedRows(parsedWithValidation)
        setStep('preview')
        setUploadError('')
      } catch (error) {
        setUploadError('Error parsing CSV file. Please check the format.')
      }
    },
    [validateBulkData]
  )

  // Handle import
  const handleImport = async () => {
    if (!csvData) return

    try {
      const result = await importFromCSV(csvData)
      setImportResult(result)
      setStep('result')
      onSuccess?.(result)
    } catch (error) {
      setUploadError(error instanceof Error ? error.message : 'Import failed')
    }
  }

  // Reset form
  const handleReset = () => {
    setCsvFile(null)
    setCsvData('')
    setParsedRows([])
    setImportResult(null)
    setStep('upload')
    setUploadError('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Valid rows for import
  const validRows = parsedRows.filter(row => row.isValid)
  const invalidRows = parsedRows.filter(row => !row.isValid)

  return (
    <div className="space-y-6">
      {/* Step: Upload */}
      {step === 'upload' && (
        <>
          {/* Template Download */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Download className="h-5 w-5 text-orange-500" />
                CSV Template
              </CardTitle>
              <CardDescription className="text-slate-400">
                Download the CSV template to ensure proper formatting
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={handleDownloadTemplate}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Template
              </Button>
              <div className="mt-4 text-sm text-slate-400">
                <p className="mb-2">Required columns:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <strong>customer_name</strong> - Name of the customer
                    company
                  </li>
                  <li>
                    <strong>shipper_name</strong> - Name of the shipper company
                  </li>
                </ul>
                <p className="mt-2 mb-2">Optional columns:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>
                    <strong>is_default</strong> - true/false or 1/0 for default
                    preference
                  </li>
                  <li>
                    <strong>notes</strong> - Additional notes for the
                    relationship
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Upload className="h-5 w-5 text-orange-500" />
                Upload CSV File
              </CardTitle>
              <CardDescription className="text-slate-400">
                Select a CSV file containing customer-shipper relationships
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  className="bg-slate-800 border-slate-600 text-white file:bg-orange-500 file:text-white file:border-0 file:rounded-md file:px-3 file:py-1"
                />

                {uploadError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{uploadError}</AlertDescription>
                  </Alert>
                )}

                {csvFile && (
                  <div className="flex items-center space-x-2 p-3 bg-slate-800 rounded border border-slate-600">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <span className="text-slate-200">{csvFile.name}</span>
                    <span className="text-slate-400 text-sm">
                      ({(csvFile.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Step: Preview */}
      {step === 'preview' && (
        <>
          <Card className="bg-slate-700 border-slate-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <FileText className="h-5 w-5 text-orange-500" />
                Import Preview
              </CardTitle>
              <CardDescription className="text-slate-400">
                Review the data before importing. {validRows.length} valid rows,{' '}
                {invalidRows.length} invalid rows
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Summary */}
                <div className="flex space-x-4">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-green-400">
                      {validRows.length} Valid
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-red-400">
                      {invalidRows.length} Invalid
                    </span>
                  </div>
                </div>

                {/* Preview Table */}
                <div className="max-h-96 overflow-auto border border-slate-600 rounded">
                  <Table>
                    <TableHeader className="bg-slate-800 sticky top-0">
                      <TableRow className="border-slate-600">
                        <TableHead className="text-slate-200">Row</TableHead>
                        <TableHead className="text-slate-200">Status</TableHead>
                        <TableHead className="text-slate-200">
                          Customer
                        </TableHead>
                        <TableHead className="text-slate-200">
                          Shipper
                        </TableHead>
                        <TableHead className="text-slate-200">
                          Default
                        </TableHead>
                        <TableHead className="text-slate-200">Notes</TableHead>
                        <TableHead className="text-slate-200">Errors</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {parsedRows.map((row, index) => (
                        <TableRow key={index} className="border-slate-600">
                          <TableCell className="text-slate-300">
                            {row.rowIndex}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={row.isValid ? 'default' : 'destructive'}
                              className={
                                row.isValid
                                  ? 'bg-green-600 text-white'
                                  : 'bg-red-600 text-white'
                              }
                            >
                              {row.isValid ? 'Valid' : 'Invalid'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-slate-300">
                            {row.customer_name}
                          </TableCell>
                          <TableCell className="text-slate-300">
                            {row.shipper_name}
                          </TableCell>
                          <TableCell className="text-slate-300">
                            {row.is_default ? 'Yes' : 'No'}
                          </TableCell>
                          <TableCell className="text-slate-300 max-w-32 truncate">
                            {row.notes || '-'}
                          </TableCell>
                          <TableCell className="text-slate-300">
                            {row.errors.length > 0 ? (
                              <div className="space-y-1">
                                {row.errors.map((error, errorIndex) => (
                                  <div
                                    key={errorIndex}
                                    className="text-red-400 text-xs"
                                  >
                                    {error}
                                  </div>
                                ))}
                              </div>
                            ) : (
                              '-'
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {invalidRows.length > 0 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      {invalidRows.length} rows contain errors and will be
                      skipped during import. Only {validRows.length} valid rows
                      will be processed.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleReset}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Start Over
            </Button>
            <Button
              onClick={handleImport}
              disabled={validRows.length === 0 || isBulkImporting}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              {isBulkImporting && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Import {validRows.length} Relationships
            </Button>
          </div>
        </>
      )}

      {/* Step: Result */}
      {step === 'result' && importResult && (
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Import Complete
            </CardTitle>
            <CardDescription className="text-slate-400">
              Import process has finished
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Results Summary */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="bg-slate-800 p-4 rounded border border-slate-600">
                  <div className="text-2xl font-bold text-green-400">
                    {importResult.summary.successful}
                  </div>
                  <div className="text-sm text-slate-400">Successful</div>
                </div>
                <div className="bg-slate-800 p-4 rounded border border-slate-600">
                  <div className="text-2xl font-bold text-red-400">
                    {importResult.summary.failed}
                  </div>
                  <div className="text-sm text-slate-400">Failed</div>
                </div>
                <div className="bg-slate-800 p-4 rounded border border-slate-600">
                  <div className="text-2xl font-bold text-slate-300">
                    {importResult.summary.total}
                  </div>
                  <div className="text-sm text-slate-400">Total Processed</div>
                </div>
              </div>

              {/* Error Details */}
              {importResult.errors && importResult.errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-white font-medium">Import Errors:</h4>
                  <div className="max-h-32 overflow-auto bg-slate-800 border border-slate-600 rounded p-3">
                    {importResult.errors.map((errorItem, index) => (
                      <div key={index} className="text-red-400 text-sm mb-1">
                        Row {errorItem.row}: {errorItem.error}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex space-x-2">
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Import Another File
                </Button>
                <Button
                  onClick={onClose}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Close
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
