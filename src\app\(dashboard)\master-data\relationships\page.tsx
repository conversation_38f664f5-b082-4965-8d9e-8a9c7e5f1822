'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Eye,
  X,
  Link,
  Building,
  Users,
  Star,
  StarOff,
  Upload,
  Ship,
  Phone,
  Mail,
  Package,
} from 'lucide-react'
import { CustomerShipperForm } from '@/components/forms/customer-shipper-form/customer-shipper-form'
import { CustomerConsigneeForm } from '@/components/forms/customer-consignee-form/customer-consignee-form'
import { BulkImportForm } from '@/components/forms/bulk-import-form/bulk-import-form'
import {
  useCustomerShippersManagement,
  useCompanySelection as useShipperCompanySelection,
} from '@/hooks/use-customer-shippers'
import {
  useCustomerConsigneesManagement,
  useCompanySelection as useConsigneeCompanySelection,
} from '@/hooks/use-customer-consignees'
import { useCustomerShipperStore } from '@/stores/customer-shipper-store'
import { useCustomerConsigneeStore } from '@/stores/customer-consignee-store'
import type {
  CustomerShipper,
  CustomerShipperBulkResult,
} from '@/stores/customer-shipper-store'
import type {
  CustomerConsignee,
  CustomerConsigneeBulkResult,
} from '@/stores/customer-consignee-store'
import type { CustomerShipperForm as CustomerShipperFormData } from '@/lib/validations/customer-shippers'
import type { CustomerConsigneeForm as CustomerConsigneeFormData } from '@/lib/validations/customer-consignees'
import type { Company } from '@/stores/company-store'
import { formatDistanceToNow, format } from 'date-fns'

export default function RelationshipsPage() {
  // Tab state
  const [activeTab, setActiveTab] = useState('customer-shipper')

  // Customer-Shipper Management
  const {
    // Data
    relationships: shipperRelationships,
    loading: shipperLoading,
    error: shipperError,

    // Pagination
    currentPage: shipperCurrentPage,
    totalPages: shipperTotalPages,
    totalCount: shipperTotalCount,
    hasNextPage: shipperHasNextPage,
    hasPreviousPage: shipperHasPreviousPage,

    // Filter and search
    filter: shipperFilter,
    searchTerm: shipperSearchTerm,
    sortBy: shipperSortBy,
    sortOrder: shipperSortOrder,

    // Selection
    selectedRelationships: selectedShipperRelationships,
    selectedCount: shipperSelectedCount,
    isSelected: isShipperSelected,
    isAllSelected: isAllShipperSelected,
    isPartiallySelected: isPartiallyShipperSelected,

    // CRUD operations
    createRelationship: createShipperRelationship,
    updateRelationship: updateShipperRelationship,
    deleteRelationship: deleteShipperRelationship,
    bulkDeleteRelationships: bulkDeleteShipperRelationships,
    isCreating: isCreatingShipper,
    isUpdating: isUpdatingShipper,
    isDeleting: isDeletingShipper,

    // Actions
    setFilter: setShipperFilter,
    setSearchTerm: setShipperSearchTerm,
    setSorting: setShipperSorting,
    setPage: setShipperPage,
    nextPage: shipperNextPage,
    previousPage: shipperPreviousPage,
    toggleRelationship: toggleShipperRelationship,
    toggleAll: toggleAllShipper,
    clearSelection: clearShipperSelection,
    clearError: clearShipperError,
    refreshRelationships: refreshShipperRelationships,
    getCustomerOptions: getShipperCustomerOptions,
    getShipperOptions,
  } = useCustomerShippersManagement()

  // Customer-Consignee Management
  const {
    // Data
    relationships: consigneeRelationships,
    loading: consigneeLoading,
    error: consigneeError,

    // Pagination
    currentPage: consigneeCurrentPage,
    totalPages: consigneeTotalPages,
    totalCount: consigneeTotalCount,
    hasNextPage: consigneeHasNextPage,
    hasPreviousPage: consigneeHasPreviousPage,

    // Filter and search
    filter: consigneeFilter,
    searchTerm: consigneeSearchTerm,
    sortBy: consigneeSortBy,
    sortOrder: consigneeSortOrder,

    // Selection
    selectedRelationships: selectedConsigneeRelationships,
    selectedCount: consigneeSelectedCount,
    isSelected: isConsigneeSelected,
    isAllSelected: isAllConsigneeSelected,
    isPartiallySelected: isPartiallyConsigneeSelected,

    // CRUD operations
    createRelationship: createConsigneeRelationship,
    updateRelationship: updateConsigneeRelationship,
    deleteRelationship: deleteConsigneeRelationship,
    bulkDeleteRelationships: bulkDeleteConsigneeRelationships,
    isCreating: isCreatingConsignee,
    isUpdating: isUpdatingConsignee,
    isDeleting: isDeletingConsignee,

    // Actions
    setFilter: setConsigneeFilter,
    setSearchTerm: setConsigneeSearchTerm,
    setSorting: setConsigneeSorting,
    setPage: setConsigneePage,
    nextPage: consigneeNextPage,
    previousPage: consigneePreviousPage,
    toggleRelationship: toggleConsigneeRelationship,
    toggleAll: toggleAllConsignee,
    clearSelection: clearConsigneeSelection,
    clearError: clearConsigneeError,
    refreshRelationships: refreshConsigneeRelationships,
    getCustomerOptions: getConsigneeCustomerOptions,
    getConsigneeOptions,
  } = useCustomerConsigneesManagement()

  // Company options for filters
  const [shipperCustomerOptions, setShipperCustomerOptions] = useState<Company[]>([])
  const [consigneeCustomerOptions, setConsigneeCustomerOptions] = useState<Company[]>([])
  const [loadingShipperOptions, setLoadingShipperOptions] = useState(false)
  const [loadingConsigneeOptions, setLoadingConsigneeOptions] = useState(false)

  // Load customer options for filter dropdowns
  useEffect(() => {
    const loadShipperCustomerOptions = async () => {
      setLoadingShipperOptions(true)
      try {
        // Use store selector directly to avoid function dependency issues
        const shipperActions = useCustomerShipperStore.getState()
        const customers = await shipperActions.getCustomerOptions()
        setShipperCustomerOptions(customers)
      } catch (error) {
        console.error('Error loading shipper customer options:', error)
      } finally {
        setLoadingShipperOptions(false)
      }
    }
    loadShipperCustomerOptions()
  }, [])

  useEffect(() => {
    const loadConsigneeCustomerOptions = async () => {
      setLoadingConsigneeOptions(true)
      try {
        // Use store selector directly to avoid function dependency issues
        const consigneeActions = useCustomerConsigneeStore.getState()
        const customers = await consigneeActions.getCustomerOptions()
        setConsigneeCustomerOptions(customers)
      } catch (error) {
        console.error('Error loading consignee customer options:', error)
      } finally {
        setLoadingConsigneeOptions(false)
      }
    }
    loadConsigneeCustomerOptions()
  }, [])

  // Customer-Shipper UI state
  const [showShipperCreateDialog, setShowShipperCreateDialog] = useState(false)
  const [editingShipperRelationship, setEditingShipperRelationship] =
    useState<CustomerShipper | null>(null)
  const [viewingShipperRelationship, setViewingShipperRelationship] =
    useState<CustomerShipper | null>(null)
  const [deletingShipperRelationship, setDeletingShipperRelationship] =
    useState<CustomerShipper | null>(null)
  const [showShipperBulkDeleteDialog, setShowShipperBulkDeleteDialog] = useState(false)
  const [showShipperBulkImportDialog, setShowShipperBulkImportDialog] = useState(false)

  // Customer-Consignee UI state
  const [showConsigneeCreateDialog, setShowConsigneeCreateDialog] = useState(false)
  const [editingConsigneeRelationship, setEditingConsigneeRelationship] =
    useState<CustomerConsignee | null>(null)
  const [viewingConsigneeRelationship, setViewingConsigneeRelationship] =
    useState<CustomerConsignee | null>(null)
  const [deletingConsigneeRelationship, setDeletingConsigneeRelationship] =
    useState<CustomerConsignee | null>(null)
  const [showConsigneeBulkDeleteDialog, setShowConsigneeBulkDeleteDialog] = useState(false)
  const [showConsigneeBulkImportDialog, setShowConsigneeBulkImportDialog] = useState(false)

  // Customer-Shipper Handlers
  const handleShipperCreate = async (data: CustomerShipperFormData) => {
    try {
      await createShipperRelationship(data)
      setShowShipperCreateDialog(false)
    } catch (error) {
      // Let the form component handle the error display
      throw error
    }
  }

  const handleShipperUpdate = async (data: CustomerShipperFormData) => {
    if (!editingShipperRelationship) return

    try {
      await updateShipperRelationship(editingShipperRelationship.id, data)
      setEditingShipperRelationship(null)
    } catch (error) {
      // Let the form component handle the error display
      throw error
    }
  }

  const handleShipperDelete = async (relationship: CustomerShipper) => {
    try {
      await deleteShipperRelationship(relationship.id)
      setDeletingShipperRelationship(null)
    } catch (error) {
      console.error('Shipper delete failed:', error)
    }
  }

  const handleShipperBulkDelete = async () => {
    try {
      await bulkDeleteShipperRelationships(Array.from(selectedShipperRelationships))
      setShowShipperBulkDeleteDialog(false)
      clearShipperSelection()
    } catch (error) {
      console.error('Shipper bulk delete failed:', error)
    }
  }

  const handleShipperBulkImportSuccess = (result: CustomerShipperBulkResult) => {
    // Refresh relationships to show imported data
    refreshShipperRelationships()
    // Close dialog after a brief delay to show results
    setTimeout(() => {
      setShowShipperBulkImportDialog(false)
    }, 3000)
  }

  // Customer-Consignee Handlers
  const handleConsigneeCreate = async (data: CustomerConsigneeFormData) => {
    try {
      await createConsigneeRelationship(data)
      setShowConsigneeCreateDialog(false)
    } catch (error) {
      // Let the form component handle the error display
      throw error
    }
  }

  const handleConsigneeUpdate = async (data: CustomerConsigneeFormData) => {
    if (!editingConsigneeRelationship) return

    try {
      await updateConsigneeRelationship(editingConsigneeRelationship.id, data)
      setEditingConsigneeRelationship(null)
    } catch (error) {
      // Let the form component handle the error display
      throw error
    }
  }

  const handleConsigneeDelete = async (relationship: CustomerConsignee) => {
    try {
      await deleteConsigneeRelationship(relationship.id)
      setDeletingConsigneeRelationship(null)
    } catch (error) {
      console.error('Consignee delete failed:', error)
    }
  }

  const handleConsigneeBulkDelete = async () => {
    try {
      await bulkDeleteConsigneeRelationships(Array.from(selectedConsigneeRelationships))
      setShowConsigneeBulkDeleteDialog(false)
      clearConsigneeSelection()
    } catch (error) {
      console.error('Consignee bulk delete failed:', error)
    }
  }

  const handleConsigneeBulkImportSuccess = (result: CustomerConsigneeBulkResult) => {
    // Refresh relationships to show imported data
    refreshConsigneeRelationships()
    // Close dialog after a brief delay to show results
    setTimeout(() => {
      setShowConsigneeBulkImportDialog(false)
    }, 3000)
  }

  // Handle sorting for Customer-Shipper
  const handleShipperSort = (column: typeof shipperSortBy) => {
    if (shipperSortBy === column) {
      setShipperSorting(column, shipperSortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setShipperSorting(column, 'asc')
    }
  }

  // Get sort icon for Customer-Shipper
  const getShipperSortIcon = (column: string) => {
    if (shipperSortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return shipperSortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  // Handle sorting for Customer-Consignee
  const handleConsigneeSort = (column: typeof consigneeSortBy) => {
    if (consigneeSortBy === column) {
      setConsigneeSorting(column, consigneeSortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setConsigneeSorting(column, 'asc')
    }
  }

  // Get sort icon for Customer-Consignee
  const getConsigneeSortIcon = (column: string) => {
    if (consigneeSortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return consigneeSortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">
            Relationship Management
          </h1>
          <p className="text-slate-400 mt-1">
            Manage customer relationships with shippers and consignees
          </p>
        </div>
      </div>

      {/* Tabbed Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 bg-slate-800 border-slate-700">
          <TabsTrigger
            value="customer-shipper"
            className="text-slate-300 data-[state=active]:bg-slate-700 data-[state=active]:text-white"
          >
            <Ship className="h-4 w-4 mr-2" />
            Customer-Shipper
          </TabsTrigger>
          <TabsTrigger
            value="customer-consignee"
            className="text-slate-300 data-[state=active]:bg-slate-700 data-[state=active]:text-white"
          >
            <Package className="h-4 w-4 mr-2" />
            Customer-Consignee
          </TabsTrigger>
        </TabsList>

        {/* Customer-Shipper Tab */}
        <TabsContent value="customer-shipper" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white">
                Customer-Shipper Relationships
              </h2>
              <p className="text-slate-400 mt-1">
                Manage customer-shipper relationships and default preferences
              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowShipperBulkImportDialog(true)}
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
              >
                <Upload className="h-4 w-4 mr-2" />
                Bulk Import
              </Button>
              <Dialog
                open={showShipperCreateDialog}
                onOpenChange={open => {
                  if (open) {
                    clearShipperError()
                  }
                  setShowShipperCreateDialog(open)
                }}
              >
                <DialogTrigger asChild>
                  <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Relationship
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
                  <DialogHeader>
                    <DialogTitle className="text-white">
                      Create Customer-Shipper Relationship
                    </DialogTitle>
                    <DialogDescription className="text-slate-400">
                      Associate a customer with a shipper and configure default
                      preferences
                    </DialogDescription>
                  </DialogHeader>
                  <CustomerShipperForm
                    onSubmit={handleShipperCreate}
                    onCancel={() => setShowShipperCreateDialog(false)}
                    isLoading={isCreatingShipper}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-slate-800 rounded-lg p-6 space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Filter className="h-5 w-5 text-orange-500" />
              <h3 className="text-lg font-medium text-white">Filters</h3>
              {(shipperSearchTerm ||
                shipperFilter.is_active !== undefined ||
                shipperFilter.customer_id) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShipperSearchTerm('')
                    setShipperFilter({})
                  }}
                  className="text-slate-400 hover:text-white ml-auto"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-slate-300 text-sm font-medium">
                  Search Relationships
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="Search by customer or shipper name..."
                    value={shipperSearchTerm}
                    onChange={e => setShipperSearchTerm(e.target.value)}
                    className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>
              </div>

              {/* Customer Filter */}
              <div className="space-y-2">
                <label className="text-slate-300 text-sm font-medium">
                  Customer Company
                </label>
                <Select
                  value={shipperFilter.customer_id || 'all'}
                  onValueChange={value =>
                    setShipperFilter({
                      ...shipperFilter,
                      customer_id: value === 'all' ? undefined : value,
                    })
                  }
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                    <SelectValue placeholder="All Customers" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                    <SelectItem
                      value="all"
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      All Customers
                    </SelectItem>
                    {loadingShipperOptions ? (
                      <div className="p-4 text-center text-slate-400">
                        <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                        Loading...
                      </div>
                    ) : (
                      shipperCustomerOptions.map(customer => (
                        <SelectItem
                          key={customer.id}
                          value={customer.id}
                          className="text-slate-300 hover:bg-slate-700"
                        >
                          <div className="flex items-center space-x-2">
                            <Users className="h-3 w-3 text-blue-500" />
                            <span>{customer.name}</span>
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-slate-300 text-sm font-medium">
                  Relationship Status
                </label>
                <Select
                  value={
                    shipperFilter.is_active === undefined
                      ? 'all'
                      : shipperFilter.is_active.toString()
                  }
                  onValueChange={value =>
                    setShipperFilter({
                      ...shipperFilter,
                      is_active: value === 'all' ? undefined : value === 'true',
                    })
                  }
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem
                      value="all"
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      All Statuses
                    </SelectItem>
                    <SelectItem
                      value="true"
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      Active
                    </SelectItem>
                    <SelectItem
                      value="false"
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {(shipperSearchTerm ||
              shipperFilter.is_active !== undefined ||
              shipperFilter.customer_id) && (
              <div className="flex flex-wrap gap-2 pt-2">
                {shipperSearchTerm && (
                  <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                    Search: "{shipperSearchTerm}"
                    <button
                      onClick={() => setShipperSearchTerm('')}
                      className="ml-2 hover:text-orange-200"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                )}
                {shipperFilter.customer_id && (
                  <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                    Customer:{' '}
                    {shipperCustomerOptions.find(c => c.id === shipperFilter.customer_id)?.name ||
                      'Unknown'}
                    <button
                      onClick={() =>
                        setShipperFilter({ ...shipperFilter, customer_id: undefined })
                      }
                      className="ml-2 hover:text-blue-200"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                )}
                {shipperFilter.is_active !== undefined && (
                  <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
                    Status: {shipperFilter.is_active ? 'Active' : 'Inactive'}
                    <button
                      onClick={() => setShipperFilter({ ...shipperFilter, is_active: undefined })}
                      className="ml-2 hover:text-green-200"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Error Display */}
          {shipperError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                {shipperError}
                <Button variant="outline" size="sm" onClick={clearShipperError}>
                  Dismiss
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Bulk Actions */}
          {shipperSelectedCount > 0 && (
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="pt-6 bg-slate-800">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-300">
                    {shipperSelectedCount} relationship{shipperSelectedCount !== 1 ? 's' : ''}{' '}
                    selected
                  </span>
                  <div className="space-x-2">
                    <Button
                      variant="outline"
                      onClick={clearShipperSelection}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      Clear Selection
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => setShowShipperBulkDeleteDialog(true)}
                      disabled={isDeletingShipper}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      {isDeletingShipper && (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      )}
                      Delete Selected
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Relationships Table */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white flex items-center gap-2">
                  <Link className="h-5 w-5 text-orange-500" />
                  Customer-Shipper Relationships ({shipperRelationships.length})
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshShipperRelationships}
                  disabled={shipperLoading}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  {shipperLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent className="bg-slate-800 p-0">
              {shipperLoading && shipperRelationships.length === 0 && !shipperSearchTerm && !shipperFilter.customer_id && shipperFilter.is_active === undefined ? (
                <div className="flex items-center justify-center py-8 text-slate-300">
                  <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
                  <span className="ml-2">Loading relationships...</span>
                </div>
              ) : shipperRelationships.length === 0 ? (
                <div className="text-center py-8">
                  <Link className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-300">No relationships found</p>
                  <p className="text-sm text-slate-400 mt-1">
                    {shipperSearchTerm || shipperFilter.customer_id || shipperFilter.is_active !== undefined
                      ? 'Try adjusting your search or filters'
                      : 'Create your first customer-shipper relationship to get started'}
                  </p>
                </div>
              ) : (
                <>
                  <Table className="bg-slate-800">
                    <TableHeader className="bg-slate-700">
                      <TableRow className="border-slate-600 hover:bg-slate-700">
                        <TableHead className="w-12 text-slate-200">
                          <Checkbox
                            checked={isAllShipperSelected || isPartiallyShipperSelected}
                            onCheckedChange={toggleAllShipper}
                            className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                          />
                        </TableHead>
                        <TableHead className="text-slate-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShipperSort('customer_name')}
                            className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                          >
                            Customer {getShipperSortIcon('customer_name')}
                          </Button>
                        </TableHead>
                        <TableHead className="text-slate-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShipperSort('shipper_name')}
                            className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                          >
                            Shipper {getShipperSortIcon('shipper_name')}
                          </Button>
                        </TableHead>
                        <TableHead className="text-slate-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShipperSort('is_default')}
                            className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                          >
                            Default {getShipperSortIcon('is_default')}
                          </Button>
                        </TableHead>
                        <TableHead className="text-slate-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShipperSort('is_active')}
                            className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                          >
                            Status {getShipperSortIcon('is_active')}
                          </Button>
                        </TableHead>
                        <TableHead className="text-slate-200">Notes</TableHead>
                        <TableHead className="w-32 text-slate-200">
                          Actions
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="bg-slate-800">
                      {shipperRelationships.map(relationship => (
                        <TableRow
                          key={relationship.id}
                          className="border-slate-600 hover:bg-slate-700 text-slate-200"
                        >
                          <TableCell>
                            <Checkbox
                              checked={isShipperSelected(relationship.id)}
                              onCheckedChange={() =>
                                toggleShipperRelationship(relationship.id)
                              }
                              className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Users className="h-4 w-4 text-blue-500 flex-shrink-0" />
                              <div className="min-w-0">
                                <div className="font-medium text-slate-200 truncate">
                                  {relationship.customer?.name ||
                                    'Unknown Customer'}
                                </div>
                                {relationship.customer?.contact_phone && (
                                  <div className="text-xs text-slate-400 truncate flex items-center">
                                    <Phone className="h-3 w-3 mr-1" />
                                    {relationship.customer.contact_phone}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Ship className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                              <div className="min-w-0">
                                <div className="font-medium text-slate-200 truncate">
                                  {relationship.shipper?.name || 'Unknown Shipper'}
                                </div>
                                {relationship.shipper?.contact_phone && (
                                  <div className="text-xs text-slate-400 truncate flex items-center">
                                    <Phone className="h-3 w-3 mr-1" />
                                    {relationship.shipper.contact_phone}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {relationship.is_default ? (
                                <Star className="h-4 w-4 text-yellow-500" />
                              ) : (
                                <StarOff className="h-4 w-4 text-slate-500" />
                              )}
                              <Badge
                                variant={
                                  relationship.is_default ? 'default' : 'secondary'
                                }
                                className={
                                  relationship.is_default
                                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600'
                                    : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                                }
                              >
                                {relationship.is_default ? 'Default' : 'Standard'}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                relationship.is_active ? 'default' : 'secondary'
                              }
                              className={
                                relationship.is_active
                                  ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                                  : 'bg-red-600 hover:bg-red-700 text-white border-red-600'
                              }
                            >
                              {relationship.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-slate-300 max-w-xs truncate">
                              {relationship.notes || 'No notes'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setViewingShipperRelationship(relationship)}
                                className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  clearShipperError()
                                  setEditingShipperRelationship(relationship)
                                }}
                                className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  setDeletingShipperRelationship(relationship)
                                }
                                className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  {shipperTotalPages > 1 && (
                    <div className="flex items-center justify-between mt-6 px-6 pb-6">
                      <div className="text-sm text-slate-300">
                        Page {shipperCurrentPage} of {shipperTotalPages} ({shipperTotalCount}{' '}
                        relationships)
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShipperPage(1)}
                          disabled={!shipperHasPreviousPage}
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                        >
                          <ChevronsLeft className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={shipperPreviousPage}
                          disabled={!shipperHasPreviousPage}
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={shipperNextPage}
                          disabled={!shipperHasNextPage}
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShipperPage(shipperTotalPages)}
                          disabled={!shipperHasNextPage}
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                        >
                          <ChevronsRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customer-Consignee Tab */}
        <TabsContent value="customer-consignee" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white">
                Customer-Consignee Relationships
              </h2>
              <p className="text-slate-400 mt-1">
                Manage customer-consignee relationships and default preferences
              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowConsigneeBulkImportDialog(true)}
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
              >
                <Upload className="h-4 w-4 mr-2" />
                Bulk Import
              </Button>
              <Dialog
                open={showConsigneeCreateDialog}
                onOpenChange={open => {
                  if (open) {
                    clearConsigneeError()
                  }
                  setShowConsigneeCreateDialog(open)
                }}
              >
                <DialogTrigger asChild>
                  <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Relationship
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
                  <DialogHeader>
                    <DialogTitle className="text-white">
                      Create Customer-Consignee Relationship
                    </DialogTitle>
                    <DialogDescription className="text-slate-400">
                      Associate a customer with a consignee and configure default
                      preferences
                    </DialogDescription>
                  </DialogHeader>
                  <CustomerConsigneeForm
                    onSubmit={handleConsigneeCreate}
                    onCancel={() => setShowConsigneeCreateDialog(false)}
                    isLoading={isCreatingConsignee}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-slate-800 rounded-lg p-6 space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Filter className="h-5 w-5 text-orange-500" />
              <h3 className="text-lg font-medium text-white">Filters</h3>
              {(consigneeSearchTerm ||
                consigneeFilter.is_active !== undefined ||
                consigneeFilter.customer_id) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setConsigneeSearchTerm('')
                    setConsigneeFilter({})
                  }}
                  className="text-slate-400 hover:text-white ml-auto"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-slate-300 text-sm font-medium">
                  Search Relationships
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="Search by customer or consignee name..."
                    value={consigneeSearchTerm}
                    onChange={e => setConsigneeSearchTerm(e.target.value)}
                    className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>
              </div>

              {/* Customer Filter */}
              <div className="space-y-2">
                <label className="text-slate-300 text-sm font-medium">
                  Customer Company
                </label>
                <Select
                  value={consigneeFilter.customer_id || 'all'}
                  onValueChange={value =>
                    setConsigneeFilter({
                      ...consigneeFilter,
                      customer_id: value === 'all' ? undefined : value,
                    })
                  }
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                    <SelectValue placeholder="All Customers" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                    <SelectItem
                      value="all"
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      All Customers
                    </SelectItem>
                    {loadingConsigneeOptions ? (
                      <div className="p-4 text-center text-slate-400">
                        <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                        Loading...
                      </div>
                    ) : (
                      consigneeCustomerOptions.map(customer => (
                        <SelectItem
                          key={customer.id}
                          value={customer.id}
                          className="text-slate-300 hover:bg-slate-700"
                        >
                          <div className="flex items-center space-x-2">
                            <Users className="h-3 w-3 text-blue-500" />
                            <span>{customer.name}</span>
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-slate-300 text-sm font-medium">
                  Relationship Status
                </label>
                <Select
                  value={
                    consigneeFilter.is_active === undefined
                      ? 'all'
                      : consigneeFilter.is_active.toString()
                  }
                  onValueChange={value =>
                    setConsigneeFilter({
                      ...consigneeFilter,
                      is_active: value === 'all' ? undefined : value === 'true',
                    })
                  }
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem
                      value="all"
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      All Statuses
                    </SelectItem>
                    <SelectItem
                      value="true"
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      Active
                    </SelectItem>
                    <SelectItem
                      value="false"
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {(consigneeSearchTerm ||
              consigneeFilter.is_active !== undefined ||
              consigneeFilter.customer_id) && (
              <div className="flex flex-wrap gap-2 pt-2">
                {consigneeSearchTerm && (
                  <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                    Search: "{consigneeSearchTerm}"
                    <button
                      onClick={() => setConsigneeSearchTerm('')}
                      className="ml-2 hover:text-orange-200"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                )}
                {consigneeFilter.customer_id && (
                  <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                    Customer:{' '}
                    {consigneeCustomerOptions.find(c => c.id === consigneeFilter.customer_id)?.name ||
                      'Unknown'}
                    <button
                      onClick={() =>
                        setConsigneeFilter({ ...consigneeFilter, customer_id: undefined })
                      }
                      className="ml-2 hover:text-blue-200"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                )}
                {consigneeFilter.is_active !== undefined && (
                  <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
                    Status: {consigneeFilter.is_active ? 'Active' : 'Inactive'}
                    <button
                      onClick={() => setConsigneeFilter({ ...consigneeFilter, is_active: undefined })}
                      className="ml-2 hover:text-green-200"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Error Display */}
          {consigneeError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                {consigneeError}
                <Button variant="outline" size="sm" onClick={clearConsigneeError}>
                  Dismiss
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Bulk Actions */}
          {consigneeSelectedCount > 0 && (
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="pt-6 bg-slate-800">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-300">
                    {consigneeSelectedCount} relationship{consigneeSelectedCount !== 1 ? 's' : ''}{' '}
                    selected
                  </span>
                  <div className="space-x-2">
                    <Button
                      variant="outline"
                      onClick={clearConsigneeSelection}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      Clear Selection
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => setShowConsigneeBulkDeleteDialog(true)}
                      disabled={false}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      {false && (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      )}
                      Delete Selected
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Relationships Table */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white flex items-center gap-2">
                  <Link className="h-5 w-5 text-orange-500" />
                  Customer-Consignee Relationships ({consigneeRelationships.length})
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshConsigneeRelationships}
                  disabled={consigneeLoading}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  {consigneeLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent className="bg-slate-800 p-0">
              {consigneeLoading && consigneeRelationships.length === 0 && !consigneeSearchTerm && !consigneeFilter.customer_id && consigneeFilter.is_active === undefined ? (
                <div className="flex items-center justify-center py-8 text-slate-300">
                  <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
                  <span className="ml-2">Loading relationships...</span>
                </div>
              ) : consigneeRelationships.length === 0 ? (
                <div className="text-center py-8">
                  <Link className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-300">No relationships found</p>
                  <p className="text-sm text-slate-400 mt-1">
                    {consigneeSearchTerm || consigneeFilter.customer_id || consigneeFilter.is_active !== undefined
                      ? 'Try adjusting your search or filters'
                      : 'Create your first customer-consignee relationship to get started'}
                  </p>
                </div>
              ) : (
                <>
                  <Table className="bg-slate-800">
                    <TableHeader className="bg-slate-700">
                      <TableRow className="border-slate-600 hover:bg-slate-700">
                        <TableHead className="w-12 text-slate-200">
                          <Checkbox
                            checked={isAllConsigneeSelected || isPartiallyConsigneeSelected}
                            onCheckedChange={toggleAllConsignee}
                            className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                          />
                        </TableHead>
                        <TableHead className="text-slate-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleConsigneeSort('customer_name')}
                            className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                          >
                            Customer {getConsigneeSortIcon('customer_name')}
                          </Button>
                        </TableHead>
                        <TableHead className="text-slate-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleConsigneeSort('consignee_name')}
                            className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                          >
                            Consignee {getConsigneeSortIcon('consignee_name')}
                          </Button>
                        </TableHead>
                        <TableHead className="text-slate-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleConsigneeSort('is_default')}
                            className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                          >
                            Default {getConsigneeSortIcon('is_default')}
                          </Button>
                        </TableHead>
                        <TableHead className="text-slate-200">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleConsigneeSort('is_active')}
                            className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                          >
                            Status {getConsigneeSortIcon('is_active')}
                          </Button>
                        </TableHead>
                        <TableHead className="text-slate-200">Notes</TableHead>
                        <TableHead className="w-32 text-slate-200">
                          Actions
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="bg-slate-800">
                      {consigneeRelationships.map(relationship => (
                        <TableRow
                          key={relationship.id}
                          className="border-slate-600 hover:bg-slate-700 text-slate-200"
                        >
                          <TableCell>
                            <Checkbox
                              checked={isConsigneeSelected(relationship.id)}
                              onCheckedChange={() =>
                                toggleConsigneeRelationship(relationship.id)
                              }
                              className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Users className="h-4 w-4 text-blue-500 flex-shrink-0" />
                              <div className="min-w-0">
                                <div className="font-medium text-slate-200 truncate">
                                  {relationship.customer?.name ||
                                    'Unknown Customer'}
                                </div>
                                {relationship.customer?.contact_phone && (
                                  <div className="text-xs text-slate-400 truncate flex items-center">
                                    <Phone className="h-3 w-3 mr-1" />
                                    {relationship.customer.contact_phone}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Package className="h-4 w-4 text-green-500 flex-shrink-0" />
                              <div className="min-w-0">
                                <div className="font-medium text-slate-200 truncate">
                                  {relationship.consignee?.name || 'Unknown Consignee'}
                                </div>
                                {relationship.consignee?.contact_phone && (
                                  <div className="text-xs text-slate-400 truncate flex items-center">
                                    <Phone className="h-3 w-3 mr-1" />
                                    {relationship.consignee.contact_phone}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {relationship.is_default ? (
                                <Star className="h-4 w-4 text-yellow-500" />
                              ) : (
                                <StarOff className="h-4 w-4 text-slate-500" />
                              )}
                              <Badge
                                variant={
                                  relationship.is_default ? 'default' : 'secondary'
                                }
                                className={
                                  relationship.is_default
                                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600'
                                    : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                                }
                              >
                                {relationship.is_default ? 'Default' : 'Standard'}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                relationship.is_active ? 'default' : 'secondary'
                              }
                              className={
                                relationship.is_active
                                  ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                                  : 'bg-red-600 hover:bg-red-700 text-white border-red-600'
                              }
                            >
                              {relationship.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-slate-300 max-w-xs truncate">
                              {relationship.notes || 'No notes'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setViewingConsigneeRelationship(relationship)}
                                className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  clearConsigneeError()
                                  setEditingConsigneeRelationship(relationship)
                                }}
                                className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  setDeletingConsigneeRelationship(relationship)
                                }
                                className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  {consigneeTotalPages > 1 && (
                    <div className="flex items-center justify-between mt-6 px-6 pb-6">
                      <div className="text-sm text-slate-300">
                        Page {consigneeCurrentPage} of {consigneeTotalPages} ({consigneeTotalCount}{' '}
                        relationships)
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setConsigneePage(1)}
                          disabled={!consigneeHasPreviousPage}
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                        >
                          <ChevronsLeft className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={consigneePreviousPage}
                          disabled={!consigneeHasPreviousPage}
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={consigneeNextPage}
                          disabled={!consigneeHasNextPage}
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setConsigneePage(consigneeTotalPages)}
                          disabled={!consigneeHasNextPage}
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                        >
                          <ChevronsRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Customer-Shipper Dialogs */}

      {/* View Shipper Relationship Dialog */}
      <Dialog
        open={!!viewingShipperRelationship}
        onOpenChange={() => setViewingShipperRelationship(null)}
      >
        <DialogContent className="max-w-4xl bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">
              Shipper Relationship Details
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              View customer-shipper relationship information
            </DialogDescription>
          </DialogHeader>
          {viewingShipperRelationship && (
            <div className="space-y-6">
              {/* Basic relationship info display - simplified for brevity */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-blue-200 flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-500" />
                      Customer Company
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white font-medium">
                      {viewingShipperRelationship.customer?.name || 'Unknown'}
                    </p>
                  </CardContent>
                </Card>
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-yellow-200 flex items-center gap-2">
                      <Ship className="h-5 w-5 text-yellow-500" />
                      Shipper Company
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white font-medium">
                      {viewingShipperRelationship.shipper?.name || 'Unknown'}
                    </p>
                  </CardContent>
                </Card>
              </div>
              <div className="flex space-x-2 pt-4">
                <Button
                  onClick={() => {
                    clearShipperError()
                    setEditingShipperRelationship(viewingShipperRelationship)
                    setViewingShipperRelationship(null)
                  }}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Relationship
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setViewingShipperRelationship(null)}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* View Consignee Relationship Dialog */}
      <Dialog
        open={!!viewingConsigneeRelationship}
        onOpenChange={() => setViewingConsigneeRelationship(null)}
      >
        <DialogContent className="max-w-4xl bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">
              Consignee Relationship Details
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              View customer-consignee relationship information
            </DialogDescription>
          </DialogHeader>
          {viewingConsigneeRelationship && (
            <div className="space-y-6">
              {/* Basic relationship info display - simplified for brevity */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-blue-200 flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-500" />
                      Customer Company
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white font-medium">
                      {viewingConsigneeRelationship.customer?.name || 'Unknown'}
                    </p>
                  </CardContent>
                </Card>
                <Card className="bg-slate-700 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-green-200 flex items-center gap-2">
                      <Package className="h-5 w-5 text-green-500" />
                      Consignee Company
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-white font-medium">
                      {viewingConsigneeRelationship.consignee?.name || 'Unknown'}
                    </p>
                  </CardContent>
                </Card>
              </div>
              <div className="flex space-x-2 pt-4">
                <Button
                  onClick={() => {
                    clearConsigneeError()
                    setEditingConsigneeRelationship(viewingConsigneeRelationship)
                    setViewingConsigneeRelationship(null)
                  }}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Relationship
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setViewingConsigneeRelationship(null)}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Shipper Relationship Dialog */}
      <Dialog
        open={!!editingShipperRelationship}
        onOpenChange={open => {
          if (!open) {
            setEditingShipperRelationship(null)
          } else if (editingShipperRelationship) {
            clearShipperError()
          }
        }}
      >
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">
              Edit Customer-Shipper Relationship
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Update relationship configuration and preferences
            </DialogDescription>
          </DialogHeader>
          {editingShipperRelationship && (
            <CustomerShipperForm
              relationship={editingShipperRelationship}
              onSubmit={handleShipperUpdate}
              onCancel={() => setEditingShipperRelationship(null)}
              isLoading={isUpdatingShipper}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Consignee Relationship Dialog */}
      <Dialog
        open={!!editingConsigneeRelationship}
        onOpenChange={open => {
          if (!open) {
            setEditingConsigneeRelationship(null)
          } else if (editingConsigneeRelationship) {
            clearConsigneeError()
          }
        }}
      >
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">
              Edit Customer-Consignee Relationship
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Update relationship configuration and preferences
            </DialogDescription>
          </DialogHeader>
          {editingConsigneeRelationship && (
            <CustomerConsigneeForm
              relationship={editingConsigneeRelationship}
              onSubmit={handleConsigneeUpdate}
              onCancel={() => setEditingConsigneeRelationship(null)}
              isLoading={isUpdatingConsignee}
            />
          )}
        </DialogContent>
      </Dialog>

    </div>
  )
}
