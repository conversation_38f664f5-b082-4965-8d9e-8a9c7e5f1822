import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import {
  useShipmentRelationships,
  useRelationshipAnalysis,
  usePricingIntelligence,
  useRouteIntelligence,
} from '../use-shipment-relationships'
import { createClient } from '@/lib/supabase/client'

// Mock Supabase client
vi.mock('@/lib/supabase/client')

// Mock the consignee-notify party relationships hook
vi.mock('../use-consignee-notify-party-relationships', () => ({
  useConsigneeNotifyPartyRelationships: () => ({
    relationships: [
      {
        id: 'rel_1',
        customerId: 'cust_1',
        consigneeId: 'cons_1',
        notifyPartyId: 'notify_1',
        frequency: 25,
        lastUsed: new Date('2024-03-01'),
        isActive: true,
      },
    ],
    loading: false,
    error: null,
    searchRelationships: vi.fn(),
    createRelationship: vi.fn(),
  }),
}))

const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        order: vi.fn(() => ({
          limit: vi.fn().mockResolvedValue({
            data: [],
            error: null,
          }),
        })),
      })),
      or: vi.fn(() => ({
        order: vi.fn(() => ({
          limit: vi.fn().mockResolvedValue({
            data: [],
            error: null,
          }),
        })),
      })),
      in: vi.fn(() => ({
        order: vi.fn(() => ({
          limit: vi.fn().mockResolvedValue({
            data: [],
            error: null,
          }),
        })),
      })),
    })),
  })),
}

describe('useShipmentRelationships', () => {
  beforeEach(() => {
    vi.mocked(createClient).mockReturnValue(mockSupabase as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useShipmentRelationships())

    expect(result.current.recommendations).toEqual({
      shippers: [],
      consignees: [],
      notifyParties: [],
      products: [],
      routes: [],
    })
    expect(result.current.analysis).toBeNull()
    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBeNull()
  })

  describe('getRecommendations', () => {
    it('should fetch shipper recommendations based on customer', async () => {
      const mockShippers = [
        {
          id: 'ship_1',
          name: 'Shipper One',
          email: '<EMAIL>',
          frequency: 15,
          lastUsed: new Date('2024-02-01'),
          averageValue: 50000,
          reliability: 95,
        },
      ]

      mockSupabase.from.mockImplementation(table => {
        if (table === 'shipments') {
          return {
            select: () => ({
              eq: () => ({
                order: () => ({
                  limit: () =>
                    Promise.resolve({
                      data: mockShippers.map(s => ({
                        shipper: { id: s.id, name: s.name, email: s.email },
                        created_at: s.lastUsed,
                        total_value: s.averageValue,
                      })),
                      error: null,
                    }),
                }),
              }),
            }),
          }
        }
        return {
          select: () => ({
            eq: () => ({
              order: () => ({
                limit: () =>
                  Promise.resolve({
                    data: [],
                    error: null,
                  }),
              }),
            }),
          }),
        }
      })

      const { result } = renderHook(() => useShipmentRelationships())

      await waitFor(() => {
        result.current.getRecommendations('cust_1', 'shipper')
      })

      // Wait for async operation
      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })
    })

    it('should handle recommendation fetch errors gracefully', async () => {
      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            order: () => ({
              limit: () => Promise.reject(new Error('Database error')),
            }),
          }),
        }),
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const { result } = renderHook(() => useShipmentRelationships())

      await waitFor(() => {
        result.current.getRecommendations('cust_1', 'shipper')
      })

      await waitFor(() => {
        expect(result.current.error).toContain(
          'Failed to fetch shipper recommendations'
        )
        expect(consoleSpy).toHaveBeenCalled()
      })

      consoleSpy.mockRestore()
    })
  })

  describe('analyzeRelationship', () => {
    it('should analyze customer-shipper relationship', async () => {
      const mockAnalysisData = [
        {
          created_at: new Date('2024-01-01'),
          total_value: 100000,
          status: 'delivered',
          transport_mode: 'sea',
        },
        {
          created_at: new Date('2024-02-01'),
          total_value: 150000,
          status: 'delivered',
          transport_mode: 'sea',
        },
      ]

      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            eq: () => ({
              order: () =>
                Promise.resolve({
                  data: mockAnalysisData,
                  error: null,
                }),
            }),
          }),
        }),
      })

      const { result } = renderHook(() => useShipmentRelationships())

      await waitFor(() => {
        result.current.analyzeRelationship('cust_1', 'ship_1')
      })

      await waitFor(() => {
        expect(result.current.analysis).not.toBeNull()
        expect(result.current.analysis?.totalShipments).toBe(2)
        expect(result.current.analysis?.averageValue).toBe(125000)
        expect(result.current.analysis?.preferredTransportMode).toBe('sea')
      })
    })

    it('should handle analysis errors gracefully', async () => {
      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            eq: () => ({
              order: () => Promise.reject(new Error('Database error')),
            }),
          }),
        }),
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const { result } = renderHook(() => useShipmentRelationships())

      await waitFor(() => {
        result.current.analyzeRelationship('cust_1', 'ship_1')
      })

      await waitFor(() => {
        expect(result.current.error).toContain('Failed to analyze relationship')
        expect(consoleSpy).toHaveBeenCalled()
      })

      consoleSpy.mockRestore()
    })
  })
})

describe('useRelationshipAnalysis', () => {
  beforeEach(() => {
    vi.mocked(createClient).mockReturnValue(mockSupabase as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should calculate relationship strength correctly', async () => {
    const mockData = [
      { created_at: new Date('2024-01-01'), status: 'delivered' },
      { created_at: new Date('2024-02-01'), status: 'delivered' },
      { created_at: new Date('2024-03-01'), status: 'in_transit' },
    ]

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          eq: () =>
            Promise.resolve({
              data: mockData,
              error: null,
            }),
        }),
      }),
    })

    const { result } = renderHook(() =>
      useRelationshipAnalysis('cust_1', 'ship_1')
    )

    await waitFor(() => {
      expect(result.current.data).not.toBeNull()
      expect(result.current.data?.strength).toBeGreaterThan(0)
    })
  })

  it('should handle empty relationship data', async () => {
    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          eq: () =>
            Promise.resolve({
              data: [],
              error: null,
            }),
        }),
      }),
    })

    const { result } = renderHook(() =>
      useRelationshipAnalysis('cust_1', 'ship_1')
    )

    await waitFor(() => {
      expect(result.current.data?.strength).toBe(0)
      expect(result.current.data?.frequency).toBe(0)
    })
  })
})

describe('usePricingIntelligence', () => {
  beforeEach(() => {
    vi.mocked(createClient).mockReturnValue(mockSupabase as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should calculate pricing trends for route', async () => {
    const mockPricingData = [
      {
        created_at: new Date('2024-01-01'),
        total_value: 50000,
        transport_mode: 'sea',
        container_type: 'dry_20',
      },
      {
        created_at: new Date('2024-02-01'),
        total_value: 55000,
        transport_mode: 'sea',
        container_type: 'dry_20',
      },
    ]

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          eq: () => ({
            eq: () => ({
              order: () =>
                Promise.resolve({
                  data: mockPricingData,
                  error: null,
                }),
            }),
          }),
        }),
      }),
    })

    const { result } = renderHook(() =>
      usePricingIntelligence('THBKK', 'HKHKG', 'sea')
    )

    await waitFor(() => {
      expect(result.current.data).not.toBeNull()
      expect(result.current.data?.averagePrice).toBe(52500)
      expect(result.current.data?.trend).toBe('increasing')
    })
  })

  it('should handle insufficient pricing data', async () => {
    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          eq: () => ({
            eq: () => ({
              order: () =>
                Promise.resolve({
                  data: [],
                  error: null,
                }),
            }),
          }),
        }),
      }),
    })

    const { result } = renderHook(() =>
      usePricingIntelligence('THBKK', 'HKHKG', 'sea')
    )

    await waitFor(() => {
      expect(result.current.data?.trend).toBe('stable')
      expect(result.current.data?.confidence).toBeLessThan(50)
    })
  })
})

describe('useRouteIntelligence', () => {
  beforeEach(() => {
    vi.mocked(createClient).mockReturnValue(mockSupabase as any)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should suggest optimal routes based on history', async () => {
    const mockRouteData = [
      {
        port_of_loading: 'THBKK',
        port_of_discharge: 'HKHKG',
        transport_mode: 'sea',
        average_transit_time: 7,
        success_rate: 95,
        shipment_count: 10,
      },
      {
        port_of_loading: 'THBKK',
        port_of_discharge: 'SGSIN',
        transport_mode: 'sea',
        average_transit_time: 5,
        success_rate: 98,
        shipment_count: 15,
      },
    ]

    mockSupabase.from.mockReturnValue({
      select: () => ({
        or: () => ({
          order: () => ({
            limit: () =>
              Promise.resolve({
                data: mockRouteData,
                error: null,
              }),
          }),
        }),
      }),
    })

    const { result } = renderHook(() => useRouteIntelligence('cust_1'))

    await waitFor(() => {
      expect(result.current.data).not.toBeNull()
      expect(result.current.data?.suggestions).toHaveLength(2)
      expect(result.current.data?.suggestions[0].successRate).toBe(98)
    })
  })

  it('should prioritize routes by success rate and frequency', async () => {
    const mockRouteData = [
      {
        port_of_loading: 'THBKK',
        port_of_discharge: 'HKHKG',
        transport_mode: 'sea',
        average_transit_time: 7,
        success_rate: 85,
        shipment_count: 20,
      },
      {
        port_of_loading: 'THBKK',
        port_of_discharge: 'SGSIN',
        transport_mode: 'sea',
        average_transit_time: 5,
        success_rate: 95,
        shipment_count: 5,
      },
    ]

    mockSupabase.from.mockReturnValue({
      select: () => ({
        or: () => ({
          order: () => ({
            limit: () =>
              Promise.resolve({
                data: mockRouteData,
                error: null,
              }),
          }),
        }),
      }),
    })

    const { result } = renderHook(() => useRouteIntelligence('cust_1'))

    await waitFor(() => {
      expect(result.current.data?.suggestions).toHaveLength(2)
      // Should be sorted by composite score (success rate + frequency)
      expect(result.current.data?.suggestions[0].port).toBe('SGSIN') // Higher success rate
    })
  })
})
