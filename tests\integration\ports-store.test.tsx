import { describe, expect, it, beforeEach, vi, afterEach } from 'vitest'
import { act, renderHook } from '@testing-library/react'
import { usePortStore, type Port, type PortInsert } from '@/stores/port-store'

// Mock Supabase client
const mockSupabaseClient = {
  from: vi.fn(() => mockSupabaseClient),
  select: vi.fn(() => mockSupabaseClient),
  insert: vi.fn(() => mockSupabaseClient),
  update: vi.fn(() => mockSupabaseClient),
  delete: vi.fn(() => mockSupabaseClient),
  eq: vi.fn(() => mockSupabaseClient),
  neq: vi.fn(() => mockSupabaseClient),
  in: vi.fn(() => mockSupabaseClient),
  or: vi.fn(() => mockSupabaseClient),
  ilike: vi.fn(() => mockSupabaseClient),
  order: vi.fn(() => mockSupabaseClient),
  range: vi.fn(() => mockSupabaseClient),
  single: vi.fn(() => Promise.resolve({ data: null, error: null })),
  rpc: vi.fn(() => Promise.resolve({ data: null, error: null })),
  channel: vi.fn(() => ({
    on: vi.fn(() => ({ subscribe: vi.fn() })),
    subscribe: vi.fn(),
    unsubscribe: vi.fn(),
  })),
}

vi.mock('@/lib/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

// Sample test data
const mockPort: Port = {
  id: '550e8400-e29b-41d4-a716-446655440000',
  code: 'THBKK',
  name: 'Bangkok Port',
  city: 'Bangkok',
  country: 'Thailand',
  port_type: 'origin',
  gps_coordinates: 'SRID=4326;POINT(100.5018 13.7563)',
  timezone: 'Asia/Bangkok',
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

const mockPorts: Port[] = [
  mockPort,
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    code: 'CNSHA',
    name: 'Shanghai Port',
    city: 'Shanghai',
    country: 'China',
    port_type: 'destination',
    gps_coordinates: 'SRID=4326;POINT(121.4737 31.2304)',
    timezone: 'Asia/Shanghai',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
]

describe('Ports Store Integration', () => {
  beforeEach(() => {
    // Reset the store state
    usePortStore.getState().reset()
    
    // Reset all mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('fetchPorts', () => {
    it('should fetch ports successfully', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockPorts,
        count: mockPorts.length,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        await result.current.fetchPorts()
      })

      expect(result.current.ports).toEqual(mockPorts)
      expect(result.current.totalCount).toBe(mockPorts.length)
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle fetch error', async () => {
      const errorMessage = 'Failed to fetch ports'
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: null,
        error: { message: errorMessage },
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        await result.current.fetchPorts()
      })

      expect(result.current.ports).toEqual([])
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(errorMessage)
    })

    it('should apply search filter', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: [mockPort],
        count: 1,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        result.current.setSearchTerm('Bangkok')
      })

      // Wait for debounced search
      await new Promise(resolve => setTimeout(resolve, 350))

      expect(mockSupabaseClient.or).toHaveBeenCalledWith(
        expect.stringContaining('Bangkok')
      )
    })

    it('should apply port type filter', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: [mockPort],
        count: 1,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        result.current.setFilter({ port_type: 'origin' })
      })

      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('port_type', 'origin')
    })

    it('should apply country filter', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: [mockPort],
        count: 1,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        result.current.setFilter({ country: 'Thailand' })
      })

      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('country', 'Thailand')
    })

    it('should handle sorting', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockPorts,
        count: mockPorts.length,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        result.current.setSorting('name', 'desc')
      })

      expect(mockSupabaseClient.order).toHaveBeenCalledWith('name', { ascending: false })
    })

    it('should handle pagination', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockPorts,
        count: mockPorts.length,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        result.current.setPage(2)
      })

      expect(mockSupabaseClient.range).toHaveBeenCalledWith(20, 39) // Page 2, pageSize 20
    })
  })

  describe('createPort', () => {
    it('should create port successfully', async () => {
      const newPortData: PortInsert = {
        code: 'USLAX',
        name: 'Los Angeles Port',
        city: 'Los Angeles',
        country: 'United States',
        port_type: 'destination',
        timezone: 'America/Los_Angeles',
      }

      const createdPort = {
        ...newPortData,
        id: '550e8400-e29b-41d4-a716-446655440002',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock duplicate check - no existing port found
      mockSupabaseClient.single
        .mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116', message: 'Row not found' },
        })
        // Mock insert
        .mockResolvedValueOnce({
          data: createdPort,
          error: null,
        })
        // Mock refresh call
        .mockResolvedValueOnce({
          data: [...mockPorts, createdPort],
          count: mockPorts.length + 1,
          error: null,
        })

      const { result } = renderHook(() => usePortStore())

      let returnedPort: Port
      await act(async () => {
        returnedPort = await result.current.createPort(newPortData)
      })

      expect(returnedPort!).toEqual(createdPort)
      expect(result.current.isCreating).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle duplicate code error', async () => {
      const newPortData: PortInsert = {
        code: 'THBKK', // Duplicate code
        name: 'Another Bangkok Port',
        city: 'Bangkok',
        country: 'Thailand',
        port_type: 'origin',
      }

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock duplicate check - returns existing port
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: { id: 'existing-id' },
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        try {
          await result.current.createPort(newPortData)
          // Should not reach here
          expect(false).toBe(true)
        } catch (error) {
          expect(error).toBeInstanceOf(Error)
          expect((error as Error).message).toContain('already exists')
        }
      })

      expect(result.current.isCreating).toBe(false)
      expect(result.current.error).toContain('already exists')
    })

    it('should transform coordinates to PostGIS format', async () => {
      const newPortData: PortInsert = {
        code: 'USLAX2',
        name: 'Los Angeles Port',
        city: 'Los Angeles',
        country: 'United States',
        port_type: 'destination',
        gps_coordinates: JSON.stringify({ lat: 34.0522, lng: -118.2437 }),
      }

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock duplicate check
      mockSupabaseClient.single
        .mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116', message: 'Row not found' },
        })
        // Mock insert
        .mockResolvedValueOnce({
          data: { ...newPortData, id: 'new-id' },
          error: null,
        })
        // Mock refresh
        .mockResolvedValueOnce({
          data: mockPorts,
          count: mockPorts.length,
          error: null,
        })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        await result.current.createPort(newPortData)
      })

      expect(mockSupabaseClient.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          gps_coordinates: 'SRID=4326;POINT(-118.2437 34.0522)',
        })
      )
    })
  })

  describe('updatePort', () => {
    it('should update port successfully', async () => {
      const updates = {
        name: 'Updated Bangkok Port',
        city: 'New Bangkok',
      }

      const updatedPort = {
        ...mockPort,
        ...updates,
        updated_at: '2024-01-02T00:00:00Z',
      }

      mockSupabaseClient.single.mockResolvedValueOnce({
        data: updatedPort,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      // Set initial ports
      act(() => {
        result.current.ports = [mockPort]
      })

      let returnedPort: Port
      await act(async () => {
        returnedPort = await result.current.updatePort(mockPort.id, updates)
      })

      expect(returnedPort!).toEqual(updatedPort)
      expect(result.current.isUpdating).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle duplicate code error on update', async () => {
      const updates = {
        code: 'CNSHA', // Duplicate code
      }

      // Mock duplicate check - returns existing port with different ID
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: { id: 'different-id' },
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        try {
          await result.current.updatePort(mockPort.id, updates)
        } catch (error) {
          expect(error).toBeInstanceOf(Error)
          expect((error as Error).message).toContain('already exists')
        }
      })

      expect(result.current.isUpdating).toBe(false)
      expect(result.current.error).toContain('already exists')
    })
  })

  describe('deletePort', () => {
    it('should soft delete port successfully', async () => {
      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock the complete chain: from().update().eq()
      mockSupabaseClient.update.mockReturnValueOnce(mockSupabaseClient)
      mockSupabaseClient.eq.mockResolvedValueOnce({
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      // Set initial ports
      act(() => {
        result.current.ports = [mockPort]
      })

      await act(async () => {
        await result.current.deletePort(mockPort.id)
      })

      expect(mockSupabaseClient.update).toHaveBeenCalledWith(
        expect.objectContaining({ is_active: false })
      )
      expect(result.current.ports).toHaveLength(0)
      expect(result.current.isDeleting).toBe(false)
    })

    it('should handle delete error', async () => {
      const errorMessage = 'Failed to delete port'
      
      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock the complete chain with error
      mockSupabaseClient.update.mockReturnValueOnce(mockSupabaseClient)
      mockSupabaseClient.eq.mockResolvedValueOnce({
        error: { message: errorMessage },
      })

      const { result } = renderHook(() => usePortStore())

      await act(async () => {
        try {
          await result.current.deletePort(mockPort.id)
          // Should not reach here
          expect(false).toBe(true)
        } catch (error) {
          // Error is expected to be thrown
          expect(error).toBeInstanceOf(Error)
        }
      })

      expect(result.current.isDeleting).toBe(false)
      expect(result.current.error).toBe(errorMessage)
    })
  })

  describe('deletePorts (bulk)', () => {
    it('should bulk delete ports successfully', async () => {
      const idsToDelete = ['id1', 'id2']

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock the complete chain: from().update().in()
      mockSupabaseClient.update.mockReturnValueOnce(mockSupabaseClient)
      mockSupabaseClient.in.mockResolvedValueOnce({
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      // Set initial ports
      act(() => {
        result.current.ports = mockPorts
      })

      await act(async () => {
        await result.current.deletePorts(idsToDelete)
      })

      expect(mockSupabaseClient.in).toHaveBeenCalledWith('id', idsToDelete)
      expect(result.current.selectedPorts.size).toBe(0)
      expect(result.current.isDeleting).toBe(false)
    })
  })

  describe('Geographic Search Functions', () => {
    it('should search ports within radius', async () => {
      const searchParams = {
        center_lat: 13.7563,
        center_lng: 100.5018,
        radius_km: 100,
      }

      const nearbyPorts = [
        {
          ...mockPort,
          distance_km: 25.5,
        },
      ]

      mockSupabaseClient.rpc.mockResolvedValueOnce({
        data: nearbyPorts,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      let returnedPorts: any[]
      await act(async () => {
        returnedPorts = await result.current.searchPortsWithinRadius(searchParams)
      })

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('find_ports_within_radius', {
        center_lat: 13.7563,
        center_lng: 100.5018,
        radius_km: 100,
      })
      expect(returnedPorts!).toEqual(nearbyPorts)
      expect(result.current.geographicLoading).toBe(false)
    })

    it('should fallback when PostGIS function unavailable', async () => {
      const searchParams = {
        center_lat: 13.7563,
        center_lng: 100.5018,
        radius_km: 100,
      }

      // Mock RPC failure
      mockSupabaseClient.rpc.mockResolvedValueOnce({
        data: null,
        error: { message: 'Function not found' },
      })

      // Mock fallback query
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockPorts,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      let returnedPorts: any[]
      await act(async () => {
        returnedPorts = await result.current.searchPortsWithinRadius(searchParams)
      })

      expect(returnedPorts!).toEqual(mockPorts)
      expect(result.current.geographicLoading).toBe(false)
    })

    it('should get ports by country with distance', async () => {
      const country = 'Thailand'
      const referenceCoords = { lat: 13.7563, lng: 100.5018 }

      const portsWithDistance = [
        {
          ...mockPort,
          distance_km: 0,
        },
      ]

      mockSupabaseClient.rpc.mockResolvedValueOnce({
        data: portsWithDistance,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      let returnedPorts: any[]
      await act(async () => {
        returnedPorts = await result.current.getPortsByCountry(country, referenceCoords)
      })

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('get_ports_by_country_with_distance', {
        target_country: country,
        ref_lat: referenceCoords.lat,
        ref_lng: referenceCoords.lng,
      })
      expect(returnedPorts!).toEqual(portsWithDistance)
    })

    it('should calculate distance between ports', async () => {
      const port1Id = 'port1-id'
      const port2Id = 'port2-id'
      const expectedDistance = 150.75

      mockSupabaseClient.rpc.mockResolvedValueOnce({
        data: expectedDistance,
        error: null,
      })

      const { result } = renderHook(() => usePortStore())

      let distance: number
      await act(async () => {
        distance = await result.current.calculatePortDistance(port1Id, port2Id)
      })

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('calculate_port_distance', {
        port1_id: port1Id,
        port2_id: port2Id,
      })
      expect(distance!).toBe(expectedDistance)
    })
  })

  describe('Selection Management', () => {
    it('should handle port selection', () => {
      const { result } = renderHook(() => usePortStore())

      act(() => {
        result.current.selectPort(mockPort.id)
      })

      expect(result.current.selectedPorts.has(mockPort.id)).toBe(true)

      act(() => {
        result.current.deselectPort(mockPort.id)
      })

      expect(result.current.selectedPorts.has(mockPort.id)).toBe(false)
    })

    it('should select all ports', () => {
      const { result } = renderHook(() => usePortStore())

      // Set initial ports
      act(() => {
        result.current.ports = mockPorts
        result.current.selectAllPorts()
      })

      mockPorts.forEach(port => {
        expect(result.current.selectedPorts.has(port.id)).toBe(true)
      })
    })

    it('should clear selection', () => {
      const { result } = renderHook(() => usePortStore())

      // Select ports first
      act(() => {
        result.current.ports = mockPorts
        result.current.selectAllPorts()
        result.current.clearSelection()
      })

      expect(result.current.selectedPorts.size).toBe(0)
    })
  })

  describe('Real-time Subscription', () => {
    it('should setup subscription correctly', () => {
      const { result } = renderHook(() => usePortStore())

      const unsubscribe = result.current.subscribeToPorts()

      expect(mockSupabaseClient.channel).toHaveBeenCalledWith('ports_changes')
      expect(typeof unsubscribe).toBe('function')
    })
  })
})