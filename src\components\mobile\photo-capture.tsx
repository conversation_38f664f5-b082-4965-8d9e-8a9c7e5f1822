'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import { Camera, Upload, X, RotateCcw, CheckCircle, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useLanguage } from '@/hooks/use-language'
import type { PhotoMetadata } from '@/types/status-update'

interface PhotoCaptureProps {
  maxPhotos?: number
  onPhotosChange: (photos: File[]) => void
  compressionQuality?: number
  maxFileSizeMB?: number
  acceptedFormats?: string[]
  disabled?: boolean
}

interface PhotoPreview {
  id: string
  file: File
  preview: string
  metadata?: PhotoMetadata
  isCompressing?: boolean
}

export function PhotoCapture({
  maxPhotos = 5,
  onPhotosChange,
  compressionQuality = 0.8,
  maxFileSizeMB = 10,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/heic', 'image/webp'],
  disabled = false
}: PhotoCaptureProps) {
  const { t } = useLanguage()
  const [photos, setPhotos] = useState<PhotoPreview[]>([])
  const [isCapturing, setIsCapturing] = useState(false)
  const [isCameraSupported, setIsCameraSupported] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  // Check camera support on component mount
  useEffect(() => {
    const checkCameraSupport = () => {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setIsCameraSupported(false)
      }
    }
    
    checkCameraSupport()
  }, [])

  // Cleanup stream on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  // Update parent component when photos change
  useEffect(() => {
    onPhotosChange(photos.map(p => p.file))
  }, [photos, onPhotosChange])

  const validateFile = (file: File): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    // Check file format
    if (!acceptedFormats.includes(file.type)) {
      errors.push(`Invalid file format. Accepted formats: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`)
    }
    
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > maxFileSizeMB) {
      errors.push(`File size too large. Maximum size: ${maxFileSizeMB}MB`)
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  const compressImage = async (file: File, quality: number = compressionQuality): Promise<File> => {
    return new Promise((resolve, reject) => {
      const canvas = canvasRef.current
      const ctx = canvas?.getContext('2d')
      if (!canvas || !ctx) {
        reject(new Error('Canvas not available'))
        return
      }

      const img = new Image()
      img.onload = () => {
        // Calculate dimensions (max 1920x1080 for mobile optimization)
        const maxWidth = 1920
        const maxHeight = 1080
        let { width, height } = img
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }
        
        canvas.width = width
        canvas.height = height
        
        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height)
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              })
              resolve(compressedFile)
            } else {
              reject(new Error('Compression failed'))
            }
          },
          'image/jpeg',
          quality
        )
      }
      
      img.onerror = () => reject(new Error('Image loading failed'))
      img.src = URL.createObjectURL(file)
    })
  }

  const processFile = async (file: File): Promise<PhotoPreview | null> => {
    // Validate file
    const validation = validateFile(file)
    if (!validation.isValid) {
      setError(validation.errors[0])
      return null
    }

    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    
    try {
      // Create preview
      const preview = URL.createObjectURL(file)
      
      // Compress image
      const originalSize = file.size
      const compressedFile = await compressImage(file)
      const compressedSize = compressedFile.size
      
      // Create metadata
      const metadata: PhotoMetadata = {
        originalSize,
        compressedSize,
        dimensions: { width: 0, height: 0 }, // Will be populated when image loads
        compressionQuality
      }
      
      return {
        id,
        file: compressedFile,
        preview,
        metadata
      }
    } catch (error) {
      console.error('Error processing file:', error)
      setError('Failed to process image')
      return null
    }
  }

  const startCamera = async () => {
    if (!isCameraSupported) {
      setError('Camera not supported on this device')
      return
    }

    try {
      setIsCapturing(true)
      setError(null)
      
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Prefer rear camera
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      })
      
      streamRef.current = stream
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }
    } catch (error) {
      console.error('Camera access error:', error)
      setError('Unable to access camera. Please check permissions.')
      setIsCapturing(false)
    }
  }

  const capturePhoto = useCallback(() => {
    const video = videoRef.current
    const canvas = canvasRef.current
    const ctx = canvas?.getContext('2d')
    
    if (!video || !canvas || !ctx) {
      setError('Camera capture not available')
      return
    }

    // Set canvas dimensions to video dimensions
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight
    
    // Draw current video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
    
    // Convert to blob and create file
    canvas.toBlob(async (blob) => {
      if (!blob) {
        setError('Failed to capture photo')
        return
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const file = new File([blob], `captured-photo-${timestamp}.jpg`, {
        type: 'image/jpeg',
        lastModified: Date.now()
      })
      
      const photoPreview = await processFile(file)
      if (photoPreview) {
        setPhotos(prev => [...prev, photoPreview])
        stopCamera()
      }
    }, 'image/jpeg', compressionQuality)
  }, [compressionQuality])

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    setIsCapturing(false)
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length === 0) return

    setError(null)
    
    // Check if adding files would exceed limit
    const totalPhotos = photos.length + files.length
    if (totalPhotos > maxPhotos) {
      setError(t('photoCapture.maximumPhotos').replace('{max}', maxPhotos.toString()).replace('{current}', photos.length.toString()))
      return
    }

    // Process each file
    const newPhotos: PhotoPreview[] = []
    for (const file of files) {
      const photoPreview = await processFile(file)
      if (photoPreview) {
        newPhotos.push(photoPreview)
      }
    }

    if (newPhotos.length > 0) {
      setPhotos(prev => [...prev, ...newPhotos])
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const removePhoto = (id: string) => {
    setPhotos(prev => {
      const updated = prev.filter(p => p.id !== id)
      // Cleanup preview URL
      const photoToRemove = prev.find(p => p.id === id)
      if (photoToRemove) {
        URL.revokeObjectURL(photoToRemove.preview)
      }
      return updated
    })
  }

  const reorderPhotos = (dragIndex: number, dropIndex: number) => {
    setPhotos(prev => {
      const newPhotos = [...prev]
      const draggedPhoto = newPhotos[dragIndex]
      newPhotos.splice(dragIndex, 1)
      newPhotos.splice(dropIndex, 0, draggedPhoto)
      return newPhotos
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-4">
      {/* Error Display */}
      {error && (
        <Alert className="border-red-500/30 bg-red-500/10">
          <AlertCircle className="h-4 w-4 text-red-400" />
          <AlertDescription className="text-red-300">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Camera Capture */}
      {isCapturing && (
        <div className="bg-slate-900 rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full aspect-video object-cover"
            autoPlay
            playsInline
            muted
          />
          <div className="p-4 bg-slate-800 border-t border-slate-700">
            <div className="flex justify-center space-x-3">
              <Button
                onClick={capturePhoto}
                disabled={disabled}
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                <Camera className="w-4 h-4 mr-2" />
                {t('photoCapture.capture')}
              </Button>
              <Button
                onClick={stopCamera}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                {t('common.cancel')}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Capture/Upload Controls */}
      {!isCapturing && photos.length < maxPhotos && (
        <div className="flex flex-col sm:flex-row gap-3">
          {isCameraSupported && (
            <Button
              onClick={startCamera}
              disabled={disabled}
              className="flex-1 bg-orange-600 hover:bg-orange-700 text-white min-h-[48px]"
            >
              <Camera className="w-5 h-5 mr-2" />
              {t('photoCapture.takePhoto')}
            </Button>
          )}
          
          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled}
            variant="outline"
            className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 min-h-[48px]"
          >
            <Upload className="w-5 h-5 mr-2" />
            {t('photoCapture.selectFromGallery')}
          </Button>
        </div>
      )}

      {/* Photo Count Display */}
      <div className="text-center">
        <span className="text-sm text-slate-400">
          {t('photoCapture.photosCount').replace('{count}', photos.length.toString()).replace('{max}', maxPhotos.toString())}
        </span>
        {photos.length > 0 && (
          <div className="w-full bg-slate-700 rounded-full h-2 mt-2">
            <div 
              className="bg-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(photos.length / maxPhotos) * 100}%` }}
            />
          </div>
        )}
      </div>

      {/* Photo Previews */}
      {photos.length > 0 && (
        <div className="grid grid-cols-2 gap-3">
          {photos.map((photo, index) => (
            <div
              key={photo.id}
              className="relative bg-slate-800 rounded-lg overflow-hidden border border-slate-700"
            >
              <img
                src={photo.preview}
                alt={`Photo ${index + 1}`}
                className="w-full aspect-square object-cover"
              />
              
              {/* Remove Button */}
              <button
                onClick={() => removePhoto(photo.id)}
                disabled={disabled}
                className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 rounded-full p-1.5 text-white transition-colors"
              >
                <X className="w-3 h-3" />
              </button>

              {/* Photo Index */}
              <div className="absolute top-2 left-2 bg-slate-900/80 rounded-full px-2 py-1 text-xs text-white">
                {index + 1}
              </div>

              {/* Compression Info */}
              {photo.metadata && (
                <div className="absolute bottom-0 left-0 right-0 bg-slate-900/80 p-2 text-xs text-slate-300">
                  <div className="flex justify-between items-center">
                    <span>{formatFileSize(photo.metadata.compressedSize)}</span>
                    <CheckCircle className="w-3 h-3 text-green-400" />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        multiple
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Hidden Canvas for Processing */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  )
}