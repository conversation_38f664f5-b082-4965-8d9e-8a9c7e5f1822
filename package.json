{"name": "dyy-trading-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:unit": "vitest run --reporter=verbose src/**/__tests__/**/*.test.{ts,tsx}", "test:integration": "vitest run --reporter=verbose tests/integration/**/*.test.{ts,tsx}", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:ci": "vitest run --coverage --reporter=verbose"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@react-pdf/renderer": "^4.3.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^4.1.0", "html-react-parser": "^5.2.6", "html2canvas": "^1.4.1", "immer": "^10.1.1", "jspdf": "^3.0.2", "lucide-react": "^0.294.0", "next": "14.2.15", "next-themes": "^0.4.6", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.62.0", "react-to-print": "^3.1.1", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.17", "zustand": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.2.15", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jsdom": "^25.0.1", "playwright": "^1.40.0", "postcss": "^8", "prettier": "^3.0.0", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "vitest": "^2.1.2"}, "engines": {"node": ">=18.0.0"}}