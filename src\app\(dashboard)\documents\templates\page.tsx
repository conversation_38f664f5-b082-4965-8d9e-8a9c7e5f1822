'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { DocumentTemplateList } from '@/components/forms/document-template-form/document-template-list'
import type { DocumentTemplate } from '@/types/document-template'

/**
 * Document Templates List Page
 * Story 5.1: Document Template Management System
 * 
 * Main page for viewing and managing document templates with filtering,
 * sorting, and admin-only actions for template management.
 */
export default function DocumentTemplatesPage() {
  const router = useRouter()

  const handleCreateNew = () => {
    router.push('/documents/templates/new')
  }

  const handleEdit = (template: DocumentTemplate) => {
    router.push(`/documents/templates/${template.id}/edit`)
  }

  const handleView = (template: DocumentTemplate) => {
    router.push(`/documents/templates/${template.id}`)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight text-slate-900 dark:text-slate-100">
          Document Templates
        </h1>
        <p className="text-slate-600 dark:text-slate-400">
          Create and manage templates for export documents with automated field population and version control.
        </p>
      </div>

      <DocumentTemplateList
        onCreateNew={handleCreateNew}
        onEdit={handleEdit}
        onView={handleView}
      />
    </div>
  )
}