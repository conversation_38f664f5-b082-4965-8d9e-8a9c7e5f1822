import type { ContainerValidationResult, SealValidationResult } from '@/types/container'

/**
 * ISO 6346 Container Number Validation Utilities
 * Format: 4 letters (owner code) + 6 digits + 1 check digit
 * Example: ABCD1234567 where 7 is the check digit
 */

// Character to numeric mapping for check digit calculation (ISO 6346)
const charToNum: Record<string, number> = {
  'A': 10, 'B': 12, 'C': 13, 'D': 14, 'E': 15, 'F': 16, 'G': 17, 'H': 18, 'I': 19, 'J': 20,
  'K': 21, 'L': 23, 'M': 24, 'N': 25, 'O': 26, 'P': 27, 'Q': 28, 'R': 29, 'S': 30, 'T': 31,
  'U': 32, 'V': 34, 'W': 35, 'X': 36, 'Y': 37, 'Z': 38
}

// Position weights for check digit calculation
const weights = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512]

/**
 * Calculate ISO 6346 check digit for container number
 * @param ownerAndSerial - First 10 characters (4 letters + 6 digits)
 * @returns Check digit (0-9)
 */
export function calculateCheckDigit(ownerAndSerial: string): number {
  if (ownerAndSerial.length !== 10) {
    throw new Error('Owner code and serial must be exactly 10 characters')
  }

  let sum = 0
  
  for (let i = 0; i < 10; i++) {
    const char = ownerAndSerial[i].toUpperCase()
    let value: number
    
    if (i < 4) {
      // First 4 characters are letters
      value = charToNum[char]
      if (!value) {
        throw new Error(`Invalid character '${char}' in owner code`)
      }
    } else {
      // Next 6 characters are digits
      value = parseInt(char, 10)
      if (isNaN(value)) {
        throw new Error(`Invalid digit '${char}' in serial number`)
      }
    }
    
    sum += value * weights[i]
  }
  
  const remainder = sum % 11
  return remainder === 10 ? 0 : remainder
}

/**
 * Format container number with proper spacing/structure
 * @param input - Raw container number input
 * @returns Formatted container number
 */
export function formatContainerNumber(input: string): string {
  // Remove all non-alphanumeric characters and convert to uppercase
  const cleaned = input.replace(/[^A-Z0-9]/gi, '').toUpperCase()
  
  if (cleaned.length >= 11) {
    // Format as: ABCD 123456 7 (with spaces for readability in display)
    return `${cleaned.substring(0, 4)} ${cleaned.substring(4, 10)} ${cleaned.substring(10, 11)}`
  }
  
  return cleaned
}

/**
 * Validate complete container number format (ISO 6346)
 * @param containerNumber - Container number to validate
 * @returns Validation result with errors and formatting
 */
export function validateContainerNumber(containerNumber: string): ContainerValidationResult {
  const errors: string[] = []
  
  if (!containerNumber || containerNumber.trim() === '') {
    return {
      isValid: false,
      errors: ['Container number is required'],
      formattedNumber: ''
    }
  }

  // Clean input (remove spaces, special characters)
  const cleaned = containerNumber.replace(/[^A-Z0-9]/gi, '').toUpperCase()
  
  // Check overall length
  if (cleaned.length !== 11) {
    errors.push(`Container number must be 11 characters (4 letters + 7 digits), got ${cleaned.length}`)
  }

  // Check format: 4 letters + 7 digits
  const formatRegex = /^[A-Z]{4}[0-9]{7}$/
  if (!formatRegex.test(cleaned)) {
    errors.push('Container number must follow format: 4 letters + 7 digits (e.g., ABCD1234567)')
  }

  let checkDigitValid = false
  
  if (cleaned.length === 11 && formatRegex.test(cleaned)) {
    try {
      const ownerAndSerial = cleaned.substring(0, 10)
      const providedCheckDigit = parseInt(cleaned.substring(10, 11), 10)
      const calculatedCheckDigit = calculateCheckDigit(ownerAndSerial)
      
      checkDigitValid = providedCheckDigit === calculatedCheckDigit
      
      if (!checkDigitValid) {
        errors.push(`Invalid check digit. Expected ${calculatedCheckDigit}, got ${providedCheckDigit}`)
      }
    } catch (error) {
      errors.push(`Check digit validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    checkDigitValid,
    formattedNumber: formatContainerNumber(cleaned)
  }
}

/**
 * Validate seal number format (flexible for different seal types)
 * @param sealNumber - Seal number to validate
 * @returns Validation result with detected format
 */
export function validateSealNumber(sealNumber: string): SealValidationResult {
  const errors: string[] = []
  
  if (!sealNumber || sealNumber.trim() === '') {
    return {
      isValid: false,
      detectedFormat: 'unknown',
      errors: ['Seal number is required']
    }
  }

  const cleaned = sealNumber.trim().toUpperCase()
  
  // Check length (typically 6-12 characters)
  if (cleaned.length < 3 || cleaned.length > 15) {
    errors.push('Seal number must be between 3-15 characters')
  }

  // Detect format
  let detectedFormat: 'numeric' | 'alphanumeric' | 'iso' | 'unknown' = 'unknown'
  
  if (/^\d+$/.test(cleaned)) {
    // All numeric (e.g., "123456789")
    detectedFormat = 'numeric'
    if (cleaned.length < 6 || cleaned.length > 12) {
      errors.push('Numeric seal numbers should be 6-12 digits')
    }
  } else if (/^[A-Z]{3}\d{9}$/.test(cleaned)) {
    // ISO format (e.g., "ABC123456789" - exactly 3 letters + 9 digits)
    detectedFormat = 'iso'
  } else if (/^[A-Z]{2,3}\d{6,9}$/.test(cleaned)) {
    // Alphanumeric format (e.g., "AB123456" or other patterns)
    detectedFormat = 'alphanumeric'
  } else if (/^[A-Z0-9]+$/.test(cleaned)) {
    // Mixed alphanumeric but not standard format
    detectedFormat = 'alphanumeric'
  } else {
    errors.push('Seal number contains invalid characters. Only letters and numbers are allowed')
  }

  return {
    isValid: errors.length === 0,
    detectedFormat,
    errors
  }
}

/**
 * Generate example container numbers for user guidance
 */
export function getContainerNumberExamples(): string[] {
  return [
    'ABCD1234560', // Valid with check digit 0
    'MSKU1234565', // Valid container from major shipping line
    'GESU1234564'  // Valid container from another major line
  ]
}

/**
 * Generate example seal numbers for user guidance
 */
export function getSealNumberExamples(): string[] {
  return [
    '123456789',     // Numeric format
    'AB123456',      // Alphanumeric format
    'ABC123456789'   // ISO format
  ]
}

/**
 * Real-time validation for container number input (debounced)
 * @param input - Current input value
 * @param previousValidation - Previous validation result to compare
 * @returns Updated validation result or null if no change needed
 */
export function validateContainerNumberRealTime(
  input: string, 
  previousValidation?: ContainerValidationResult
): ContainerValidationResult | null {
  const currentValidation = validateContainerNumber(input)
  
  // Only return new validation if it's different from previous
  if (!previousValidation || 
      currentValidation.isValid !== previousValidation.isValid ||
      currentValidation.errors.join(',') !== previousValidation.errors.join(',')) {
    return currentValidation
  }
  
  return null
}

/**
 * Real-time validation for seal number input (debounced)
 */
export function validateSealNumberRealTime(
  input: string,
  previousValidation?: SealValidationResult
): SealValidationResult | null {
  const currentValidation = validateSealNumber(input)
  
  // Only return new validation if it's different from previous
  if (!previousValidation ||
      currentValidation.isValid !== previousValidation.isValid ||
      currentValidation.detectedFormat !== previousValidation.detectedFormat ||
      currentValidation.errors.join(',') !== previousValidation.errors.join(',')) {
    return currentValidation
  }
  
  return null
}