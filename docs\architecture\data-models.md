# Data Models

## Company

**Purpose:** Universal entity representing all stakeholders in the export process using a hybrid approach - base table with separate info tables for complex types (customers, carriers, factories) and JSONB metadata for simple types (shippers, consignees, notify parties, forwarder agents).

**Key Attributes:**
- id: string (UUID) - Primary identifier
- name: string - Company name
- company_type: CompanyType - Enum defining stakeholder role
- address: Address - Multi-language address with GPS coordinates
- contact_email: string - Primary communication email
- contact_phone: string - Primary phone number
- is_active: boolean - Operational status flag
- metadata: Record<string, any> | null - JSONB for simple company types only

### TypeScript Interface
```typescript
export interface Company {
  id: string;
  name: string;
  company_type: 'customer' | 'carrier' | 'factory' | 'shipper' | 'consignee' | 'notify_party' | 'forwarder_agent';
  tax_id?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_fax?: string;
  contact_person_first_name?: string;
  contact_person_last_name?: string;
  address?: Address;
  gps_coordinates?: { lat: number; lng: number };
  metadata?: Record<string, any>; // Only for simple types
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Type-specific info (populated via joins)
  customer_info?: CustomerInfo;
  carrier_info?: CarrierInfo;
  factory_info?: FactoryInfo;
}
```

### Relationships
- One-to-many with CustomerShipper (as customer or shipper)
- One-to-many with CustomerProduct (as customer)
- One-to-many with ConsigneeNotifyParty (as consignee or notify party)
- One-to-many with Shipments (multiple roles)
- One-to-many with Drivers (for carriers)

## Product

**Purpose:** Catalog of exportable fruit products with standardized measurement units and specifications for consistent pricing and packaging management.

**Key Attributes:**
- id: string (UUID) - Primary identifier
- name: string - Product name
- code: string - Internal product code
- category: string - Product classification
- hs_code: string - Harmonized System code for customs
- unit_of_measure_id: string - Reference to base measurement unit (KG)

### TypeScript Interface
```typescript
export interface Product {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category?: string;
  hs_code?: string;
  unit_of_measure_id: string;
  unit_of_measure?: UnitOfMeasure;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
```

### Relationships
- Many-to-one with UnitOfMeasure
- One-to-many with CustomerProduct
- One-to-many with ShipmentProduct

## Shipment

**Purpose:** Core business entity representing export transactions with complete stakeholder coordination, transportation management, and status tracking throughout the fruit export lifecycle.

**Key Attributes:**
- id: string (UUID) - Primary identifier
- shipment_number: string - Auto-generated unique identifier (EX[Mode]-[Port]-YYMMDD-[Running])
- status: ShipmentStatus - Current lifecycle stage
- transportation_mode: 'sea' | 'land' | 'rail' - Transport method
- etd_date: string - Estimated Time of Departure
- eta_date: string - Estimated Time of Arrival
- closing_time: string - Booking closure deadline

### TypeScript Interface
```typescript
export interface Shipment {
  id: string;
  shipment_number: string;
  invoice_number?: string;
  
  // Stakeholder references
  customer_id?: string;
  shipper_id?: string;
  consignee_id?: string;
  notify_party_id?: string;
  factory_id?: string;
  forwarder_agent_id?: string;
  
  // Port and vessel information
  origin_port_id?: string;
  destination_port_id?: string;
  liner?: string;
  vessel_name?: string;
  voyage_number?: string;
  booking_number?: string;
  
  // Dates and times
  etd_date?: string;
  eta_date?: string;
  closing_time?: string;
  cy_date?: string;
  
  // Product totals
  total_weight?: number;
  total_volume?: number;
  currency_code: 'USD' | 'THB' | 'CNY' | 'EUR';
  
  // Status and metadata
  status: ShipmentStatus;
  transportation_mode: 'sea' | 'land' | 'rail';
  notes?: string;
  
  // Related entities (populated via joins)
  customer?: Company;
  shipper?: Company;
  consignee?: Company;
  notify_party?: Company;
  factory?: Company;
  forwarder_agent?: Company;
  origin_port?: Port;
  destination_port?: Port;
  containers?: Container[];
  products?: ShipmentProduct[];
  status_history?: StatusHistory[];
  
  created_by?: string;
  created_at: string;
  updated_at: string;
}
```

### Relationships
- Many-to-one with Company (multiple stakeholder roles)
- Many-to-one with Port (origin and destination)
- One-to-many with Container
- One-to-many with ShipmentProduct
- One-to-many with StatusHistory
- One-to-one with Transportation

## CustomerProduct

**Purpose:** Relationship entity managing customer-specific product pricing, packaging specifications, and default selections for intelligent shipment pre-population.

**Key Attributes:**
- customer_id: string - Reference to customer company
- product_id: string - Reference to product
- unit_price_cif: number - Cost, Insurance, Freight price per KG
- unit_price_fob: number - Free on Board price per KG
- packaging_type: 'Bag' | 'Plastic Basket' | 'Carton' - Package format
- gross_weight_per_package: number - Gross weight per package in KG
- net_weight_per_package: number - Net weight per package in KG
- is_default: boolean - Default product for this customer

### TypeScript Interface
```typescript
export interface CustomerProduct {
  id: string;
  customer_id: string;
  product_id: string;
  customer_product_code?: string;
  is_default: boolean;
  is_active: boolean;
  
  // Pricing (per KG)
  unit_price_cif?: number;
  unit_price_fob?: number;
  currency_code: 'USD' | 'THB' | 'CNY' | 'EUR';
  
  // Packaging specifications
  standard_quantity?: number;
  packaging_type: 'Bag' | 'Plastic Basket' | 'Carton';
  gross_weight_per_package?: number;
  net_weight_per_package?: number;
  quality_grade?: string;
  
  // Logistics
  handling_instructions?: string;
  temperature_require?: string;
  vent_require?: string;
  shelf_life_days?: number;
  
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Related entities
  customer?: Company;
  product?: Product;
}
```

### Relationships
- Many-to-one with Company (customer)
- Many-to-one with Product
- Unique constraint on (customer_id, product_id)

## ShipmentProduct

**Purpose:** Junction entity linking products to shipments with detailed quantity, pricing, weight, and quality specifications for each product in a shipment container.

**Key Attributes:**
- id: string (UUID) - Primary identifier
- shipment_id: string - Reference to parent shipment
- container_id: string - Reference to container holding this product
- product_id: string - Reference to base product
- product_description: string - Specific product description for this shipment
- quantity: number - Number of packages/units
- unit_price_cif: number - Cost, Insurance, Freight price per unit weight
- unit_price_fob: number - Free on Board price per unit weight
- total_value_cif: number - Total CIF value (auto-calculated: quantity × net_weight × unit_price_cif)
- total_value_fob: number - Total FOB value (auto-calculated: quantity × net_weight × unit_price_fob)
- gross_weight: number - Gross weight per package/unit (kg)
- net_weight: number - Net weight per package/unit (kg)
- total_gross_weight: number - Total gross weight (auto-calculated: quantity × gross_weight)
- total_net_weight: number - Total net weight (auto-calculated: quantity × net_weight)
- packaging_type: 'Bag' | 'Plastic Basket' | 'Carton' - Package format
- quality_grade: string - Quality specification for this batch
- shipping_mark: string - Identification marking for packages
- lot_number: string - Production lot identification
- mfg_date: string - Manufacturing/harvest date
- expire_date: string - Expiration date

### TypeScript Interface
```typescript
export interface ShipmentProduct {
  id: string;
  shipment_id: string;
  container_id?: string;
  product_id: string;
  product_description?: string;
  quantity: number;
  unit_of_measure_id?: string;
  
  // Pricing per unit weight
  unit_price_cif: number;
  unit_price_fob: number;
  
  // Auto-calculated totals
  total_value_cif: number;
  total_value_fob: number;
  
  // Weight specifications
  gross_weight: number;
  net_weight: number;
  total_gross_weight: number; // quantity × gross_weight
  total_net_weight: number;   // quantity × net_weight
  
  // Quality and identification
  packaging_type: 'Bag' | 'Plastic Basket' | 'Carton';
  quality_grade?: string;
  shipping_mark?: string;
  lot_number?: string;
  mfg_date?: string;
  expire_date?: string;
  
  created_at: string;
  updated_at: string;
  
  // Related entities
  shipment?: Shipment;
  container?: Container;
  product?: Product;
  unit_of_measure?: UnitOfMeasure;
}
```

### Relationships
- Many-to-one with Shipment
- Many-to-one with Container (optional)
- Many-to-one with Product
- Many-to-one with UnitOfMeasure

### Business Rules
1. **Auto-calculation:** total_value_cif = quantity × net_weight × unit_price_cif
2. **Auto-calculation:** total_value_fob = quantity × net_weight × unit_price_fob
3. **Auto-calculation:** total_gross_weight = quantity × gross_weight
4. **Auto-calculation:** total_net_weight = quantity × net_weight
5. **Weight validation:** gross_weight ≥ net_weight
6. **Quantity validation:** quantity > 0
7. **Container assignment:** Products can be assigned to containers for organization

## Driver

**Purpose:** Transportation personnel linked to carrier companies with mobile interface access for status updates and photo documentation during fruit transportation.

**Key Attributes:**
- id: string (UUID) - Primary identifier
- carrier_id: string - Reference to carrier company
- driver_first_name: string - First name
- driver_last_name: string - Last name
- phone: string - Contact phone for coordination
- line_id: string - Line messaging app ID
- driver_picture_path: string - Profile photo for identification

### TypeScript Interface
```typescript
export interface Driver {
  id: string;
  carrier_id: string;
  driver_first_name: string;
  driver_last_name: string;
  driver_code?: string;
  phone?: string;
  line_id?: string;
  driver_picture_path?: string;
  driver_picture_mime_type?: string;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Related entities
  carrier?: Company;
}
```

### Relationships
- Many-to-one with Company (carrier type only)
- One-to-many with Transportation assignments
