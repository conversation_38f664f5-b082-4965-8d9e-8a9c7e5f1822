import type { GPSCoordinates } from '@/types/status-update'

export class LocationService {
  private static readonly DEFAULT_TIMEOUT = 15000 // 15 seconds
  private static readonly DEFAULT_ACCURACY_THRESHOLD = 50 // 50 meters

  /**
   * Get current GPS position using browser Geolocation API
   */
  static async getCurrentPosition(options?: {
    enableHighAccuracy?: boolean
    timeout?: number
    maximumAge?: number
  }): Promise<GPSCoordinates> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation not supported by this device'))
        return
      }

      const positionOptions: PositionOptions = {
        enableHighAccuracy: options?.enableHighAccuracy ?? true,
        timeout: options?.timeout ?? this.DEFAULT_TIMEOUT,
        maximumAge: options?.maximumAge ?? 30000 // 30 seconds cache
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const coords: GPSCoordinates = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          }
          resolve(coords)
        },
        (error) => {
          let message = 'Failed to get location'
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              message = 'Location access denied. Please enable location permissions in your browser settings.'
              break
            case error.POSITION_UNAVAILABLE:
              message = 'Location information unavailable. Please ensure GPS is enabled and try again.'
              break
            case error.TIMEOUT:
              message = 'Location request timed out. Please try again or move to an area with better GPS reception.'
              break
            default:
              message = error.message || 'Unknown location error occurred'
          }
          
          reject(new Error(message))
        },
        positionOptions
      )
    })
  }

  /**
   * Watch position changes for real-time location tracking
   */
  static watchPosition(
    callback: (coordinates: GPSCoordinates) => void,
    errorCallback?: (error: string) => void,
    options?: {
      enableHighAccuracy?: boolean
      timeout?: number
      maximumAge?: number
    }
  ): number {
    if (!navigator.geolocation) {
      errorCallback?.('Geolocation not supported by this device')
      return -1
    }

    const positionOptions: PositionOptions = {
      enableHighAccuracy: options?.enableHighAccuracy ?? true,
      timeout: options?.timeout ?? 10000,
      maximumAge: options?.maximumAge ?? 5000
    }

    return navigator.geolocation.watchPosition(
      (position) => {
        const coords: GPSCoordinates = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp
        }
        callback(coords)
      },
      (error) => {
        let message = 'Failed to watch location'
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            message = 'Location access denied'
            break
          case error.POSITION_UNAVAILABLE:
            message = 'Location information unavailable'
            break
          case error.TIMEOUT:
            message = 'Location request timed out'
            break
          default:
            message = error.message || 'Unknown location error'
        }
        
        errorCallback?.(message)
      },
      positionOptions
    )
  }

  /**
   * Clear position watch
   */
  static clearWatch(watchId: number): void {
    if (navigator.geolocation && watchId !== -1) {
      navigator.geolocation.clearWatch(watchId)
    }
  }

  /**
   * Format coordinates for PostGIS geography storage in database
   * PostGIS expects POINT(longitude latitude) format
   */
  static formatForDatabase(coords: { latitude: number; longitude: number }): string {
    return `POINT(${coords.longitude} ${coords.latitude})`
  }

  /**
   * Format coordinates for display in DMS (Degrees, Minutes, Seconds) format
   */
  static formatCoordinatesDisplay(latitude: number, longitude: number): string {
    const formatCoord = (coord: number, isLatitude: boolean): string => {
      const abs = Math.abs(coord)
      const degrees = Math.floor(abs)
      const minutes = (abs - degrees) * 60
      const direction = isLatitude 
        ? (coord >= 0 ? 'N' : 'S')
        : (coord >= 0 ? 'E' : 'W')
      return `${degrees}°${minutes.toFixed(4)}'${direction}`
    }

    return `${formatCoord(latitude, true)}, ${formatCoord(longitude, false)}`
  }

  /**
   * Format coordinates for display in decimal degrees
   */
  static formatCoordinatesDecimal(latitude: number, longitude: number, precision: number = 6): string {
    return `${latitude.toFixed(precision)}, ${longitude.toFixed(precision)}`
  }

  /**
   * Validate GPS coordinates
   */
  static validateCoordinates(latitude: number, longitude: number): boolean {
    return (
      latitude >= -90 && latitude <= 90 &&
      longitude >= -180 && longitude <= 180
    )
  }

  /**
   * Check if GPS accuracy meets threshold requirements
   */
  static isAccuracyAcceptable(
    accuracy: number, 
    threshold: number = this.DEFAULT_ACCURACY_THRESHOLD
  ): boolean {
    return accuracy <= threshold
  }

  /**
   * Get accuracy assessment for user feedback
   */
  static getAccuracyAssessment(accuracy: number): {
    level: 'excellent' | 'good' | 'fair' | 'poor'
    message: string
    color: string
  } {
    if (accuracy <= 5) {
      return {
        level: 'excellent',
        message: 'Excellent location accuracy',
        color: 'text-green-400'
      }
    } else if (accuracy <= 20) {
      return {
        level: 'good',
        message: 'Good location accuracy',
        color: 'text-blue-400'
      }
    } else if (accuracy <= 50) {
      return {
        level: 'fair',
        message: 'Acceptable location accuracy',
        color: 'text-yellow-400'
      }
    } else {
      return {
        level: 'poor',
        message: 'Poor location accuracy - consider moving to open area',
        color: 'text-red-400'
      }
    }
  }

  /**
   * Calculate distance between two GPS coordinates (Haversine formula)
   * Returns distance in meters
   */
  static calculateDistance(
    coord1: { latitude: number; longitude: number },
    coord2: { latitude: number; longitude: number }
  ): number {
    const R = 6371e3 // Earth's radius in meters
    const φ1 = (coord1.latitude * Math.PI) / 180
    const φ2 = (coord2.latitude * Math.PI) / 180
    const Δφ = ((coord2.latitude - coord1.latitude) * Math.PI) / 180
    const Δλ = ((coord2.longitude - coord1.longitude) * Math.PI) / 180

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return R * c // Distance in meters
  }

  /**
   * Check if device supports geolocation
   */
  static isGeolocationSupported(): boolean {
    return 'geolocation' in navigator
  }

  /**
   * Check geolocation permission status
   */
  static async getPermissionStatus(): Promise<'prompt' | 'granted' | 'denied' | 'unavailable'> {
    if (!('permissions' in navigator)) {
      return 'unavailable'
    }

    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' })
      return permission.state as 'prompt' | 'granted' | 'denied'
    } catch (error) {
      return 'unavailable'
    }
  }

  /**
   * Get user-friendly location description from coordinates
   * This is a placeholder - in production, you might use a reverse geocoding service
   */
  static async getLocationDescription(
    latitude: number, 
    longitude: number
  ): Promise<string> {
    // This would typically call a reverse geocoding API
    // For now, return formatted coordinates
    return this.formatCoordinatesDisplay(latitude, longitude)
  }
}