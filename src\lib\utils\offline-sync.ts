import { createClient } from '@/lib/supabase/client'
import { OfflineStorageService } from './offline-storage'
import { LocationService } from './location-service'
import type { OfflineStatusUpdate } from '@/types/status-update'

interface SyncResult {
  success: boolean
  syncedCount: number
  failedCount: number
  errors: string[]
}

interface SyncStatus {
  isOnline: boolean
  isSyncing: boolean
  pendingCount: number
  lastSyncTime: number | null
  errors: string[]
}

export class OfflineSyncService {
  private static syncInProgress = false
  private static syncListeners: Array<(status: SyncStatus) => void> = []

  /**
   * Check if device is currently online
   */
  static isOnline(): boolean {
    return navigator.onLine
  }

  /**
   * Add listener for sync status updates
   */
  static addSyncListener(listener: (status: SyncStatus) => void): () => void {
    this.syncListeners.push(listener)
    
    // Return unsubscribe function
    return () => {
      const index = this.syncListeners.indexOf(listener)
      if (index > -1) {
        this.syncListeners.splice(index, 1)
      }
    }
  }

  /**
   * Notify all listeners of sync status changes
   */
  private static notifyListeners(status: SyncStatus): void {
    this.syncListeners.forEach(listener => {
      try {
        listener(status)
      } catch (error) {
        console.error('Sync listener error:', error)
      }
    })
  }

  /**
   * Get current sync status
   */
  static async getSyncStatus(): Promise<SyncStatus> {
    const pendingUpdates = await OfflineStorageService.getPendingUpdates()
    const lastSyncTime = this.getLastSyncTime()

    return {
      isOnline: this.isOnline(),
      isSyncing: this.syncInProgress,
      pendingCount: pendingUpdates.length,
      lastSyncTime,
      errors: []
    }
  }

  /**
   * Sync all pending status updates
   */
  static async syncPendingUpdates(): Promise<SyncResult> {
    if (this.syncInProgress) {
      throw new Error('Sync already in progress')
    }

    if (!this.isOnline()) {
      throw new Error('Device is offline')
    }

    this.syncInProgress = true
    
    const result: SyncResult = {
      success: false,
      syncedCount: 0,
      failedCount: 0,
      errors: []
    }

    try {
      // Notify listeners that sync started
      const initialStatus = await this.getSyncStatus()
      this.notifyListeners({ ...initialStatus, isSyncing: true })

      const pendingUpdates = await OfflineStorageService.getPendingUpdates()
      
      if (pendingUpdates.length === 0) {
        result.success = true
        return result
      }

      // Process each update
      for (const update of pendingUpdates) {
        try {
          await this.syncSingleUpdate(update)
          result.syncedCount++
          
          // Update status to synced
          await OfflineStorageService.updateSyncStatus(update.id, 'synced')
          
        } catch (error) {
          result.failedCount++
          const errorMessage = error instanceof Error ? error.message : 'Unknown sync error'
          result.errors.push(`${update.shipment_id}: ${errorMessage}`)
          
          // Update retry count and status
          const newRetryCount = (update.retry_count || 0) + 1
          const maxRetries = 3
          
          if (newRetryCount >= maxRetries) {
            await OfflineStorageService.updateSyncStatus(update.id, 'failed', newRetryCount)
          } else {
            await OfflineStorageService.updateSyncStatus(update.id, 'pending', newRetryCount)
          }
        }
      }

      // Clean up successfully synced updates
      await OfflineStorageService.clearSyncedUpdates()
      
      result.success = result.failedCount === 0
      this.setLastSyncTime(Date.now())

      return result

    } finally {
      this.syncInProgress = false
      
      // Notify listeners that sync completed
      const finalStatus = await this.getSyncStatus()
      this.notifyListeners({ ...finalStatus, errors: result.errors })
    }
  }

  /**
   * Sync a single status update
   */
  private static async syncSingleUpdate(update: OfflineStatusUpdate): Promise<void> {
    const supabase = createClient()
    
    // Update sync status to 'syncing'
    await OfflineStorageService.updateSyncStatus(update.id, 'syncing')

    try {
      // 1. Create status history record
      const { data: user } = await supabase.auth.getUser()
      
      if (!user.user) {
        throw new Error('User not authenticated')
      }

      const statusHistoryData = {
        shipment_id: update.shipment_id,
        status_to: update.status_update.status_to,
        status_from: update.status_update.status_from,
        notes: update.status_update.notes,
        latitude: update.status_update.latitude,
        longitude: update.status_update.longitude,
        location: update.status_update.location || LocationService.formatCoordinatesDisplay(
          update.status_update.latitude!,
          update.status_update.longitude!
        ),
        updated_by: user.user.id
      }

      // Add GPS coordinates in PostGIS format if we have coordinates
      if (update.status_update.latitude && update.status_update.longitude) {
        statusHistoryData['gps_coordinates'] = LocationService.formatForDatabase({
          latitude: update.status_update.latitude,
          longitude: update.status_update.longitude
        })
      }

      const { data: statusHistory, error: statusError } = await supabase
        .from('status_history')
        .insert(statusHistoryData)
        .select()
        .single()

      if (statusError) {
        throw statusError
      }

      // 2. Upload photos if any
      if (update.photos.length > 0) {
        await this.uploadPhotos(statusHistory.id, update.shipment_id, update.photos)
      }

      // 3. Update shipment status
      const { error: shipmentError } = await supabase
        .from('shipments')
        .update({ 
          status: update.status_update.status_to,
          updated_at: new Date().toISOString()
        })
        .eq('id', update.shipment_id)

      if (shipmentError) {
        throw shipmentError
      }

    } catch (error) {
      // Revert sync status back to pending on error
      await OfflineStorageService.updateSyncStatus(update.id, 'pending')
      throw error
    }
  }

  /**
   * Upload photos for a status update
   */
  private static async uploadPhotos(
    statusHistoryId: string,
    shipmentId: string,
    photos: File[]
  ): Promise<void> {
    const supabase = createClient()

    for (let i = 0; i < photos.length; i++) {
      const photo = photos[i]
      
      try {
        // Create unique filename
        const timestamp = Date.now()
        const randomId = Math.random().toString(36).substr(2, 9)
        const fileExtension = photo.name.split('.').pop() || 'jpg'
        const fileName = `${statusHistoryId}_${timestamp}_${randomId}.${fileExtension}`
        const filePath = `status-photos/${shipmentId}/${fileName}`

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('status-images')
          .upload(filePath, photo, {
            cacheControl: '3600',
            upsert: false
          })

        if (uploadError) {
          throw uploadError
        }

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('status-images')
          .getPublicUrl(filePath)

        // Create status_images record
        const { error: recordError } = await supabase
          .from('status_images')
          .insert({
            shipment_id: shipmentId,
            status_history_id: statusHistoryId,
            image_url: urlData.publicUrl,
            image_path: filePath,
            file_size: photo.size,
            mime_type: photo.type,
            metadata: {
              original_name: photo.name,
              uploaded_at: new Date().toISOString(),
              photo_order: i + 1,
              synced_from_offline: true
            }
          })

        if (recordError) {
          throw recordError
        }

      } catch (error) {
        console.error(`Failed to upload photo ${i + 1}:`, error)
        throw new Error(`Failed to upload photo ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  /**
   * Start automatic sync when device comes online
   */
  static startAutoSync(): void {
    if (typeof window === 'undefined') return

    // Listen for online events
    window.addEventListener('online', async () => {
      console.log('Device came online, starting auto-sync...')
      
      try {
        const result = await this.syncPendingUpdates()
        console.log('Auto-sync completed:', result)
      } catch (error) {
        console.error('Auto-sync failed:', error)
      }
    })

    // Check for pending updates on page load
    if (this.isOnline()) {
      setTimeout(async () => {
        try {
          const pendingUpdates = await OfflineStorageService.getPendingUpdates()
          if (pendingUpdates.length > 0) {
            console.log(`Found ${pendingUpdates.length} pending updates, starting sync...`)
            await this.syncPendingUpdates()
          }
        } catch (error) {
          console.error('Initial sync check failed:', error)
        }
      }, 2000) // Wait 2 seconds after page load
    }
  }

  /**
   * Stop automatic sync
   */
  static stopAutoSync(): void {
    if (typeof window === 'undefined') return

    window.removeEventListener('online', this.syncPendingUpdates)
  }

  /**
   * Get last sync timestamp from localStorage
   */
  private static getLastSyncTime(): number | null {
    try {
      const timestamp = localStorage.getItem('dyy_last_sync_time')
      return timestamp ? parseInt(timestamp, 10) : null
    } catch {
      return null
    }
  }

  /**
   * Set last sync timestamp in localStorage
   */
  private static setLastSyncTime(timestamp: number): void {
    try {
      localStorage.setItem('dyy_last_sync_time', timestamp.toString())
    } catch (error) {
      console.warn('Failed to save last sync time:', error)
    }
  }

  /**
   * Force sync with retry logic
   */
  static async forceSyncWithRetry(maxRetries: number = 3): Promise<SyncResult> {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.syncPendingUpdates()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown sync error')
        
        if (attempt < maxRetries) {
          // Exponential backoff: wait 2^attempt seconds
          const delayMs = Math.pow(2, attempt) * 1000
          await new Promise(resolve => setTimeout(resolve, delayMs))
        }
      }
    }

    throw lastError || new Error('Max retry attempts reached')
  }

  /**
   * Get sync statistics
   */
  static async getSyncStats(): Promise<{
    totalPending: number
    totalFailed: number
    storageUsage: { updates: number; photos: number; totalSizeMB: number }
    lastSyncTime: number | null
  }> {
    const pendingUpdates = await OfflineStorageService.getPendingUpdates()
    const storageUsage = await OfflineStorageService.getStorageUsage()
    const lastSyncTime = this.getLastSyncTime()

    const failedUpdates = pendingUpdates.filter(update => update.sync_status === 'failed')

    return {
      totalPending: pendingUpdates.length,
      totalFailed: failedUpdates.length,
      storageUsage,
      lastSyncTime
    }
  }
}