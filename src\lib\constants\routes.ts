import { UserRole } from '@/lib/supabase/auth'
import {
  LayoutDashboard,
  Package,
  Truck,
  Users,
  Building2,
  FileText,
  Settings,
  BarChart3,
  MapPin,
  Bell,
  Shield,
  CreditCard,
  Warehouse,
  Ship,
  Plane,
  User,
  Link,
  type LucideIcon,
} from 'lucide-react'

export interface NavigationItem {
  id: string
  title: string
  href: string
  icon: LucideIcon
  description?: string
  badge?: string
  allowedRoles: UserRole[]
  children?: NavigationItem[]
}

// Main navigation structure
export const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    href: '/dashboard/overview',
    icon: LayoutDashboard,
    description: 'Overview and analytics',
    allowedRoles: [
      'admin',
      'cs',
      'account',
      'customer',
      'carrier',
      'factory',
      'shipper',
      'consignee',
      'notify_party',
      'forwarder_agent',
    ],
  },
  {
    id: 'shipments',
    title: 'Shipments',
    href: '/shipments',
    icon: Package,
    description: 'Manage shipping operations',
    allowedRoles: [
      'admin',
      'cs',
      'account',
      'customer',
      'carrier',
      'factory',
      'shipper',
      'consignee',
      'notify_party',
      'forwarder_agent',
    ],
    children: [
      {
        id: 'shipments-all',
        title: 'All Shipments',
        href: '/shipments',
        icon: Package,
        allowedRoles: [
          'admin',
          'cs',
          'account',
          'customer',
          'carrier',
          'factory',
          'shipper',
          'consignee',
          'notify_party',
          'forwarder_agent',
        ],
      },
      {
        id: 'shipments-create',
        title: 'Create Shipment',
        href: '/shipments/create',
        icon: Package,
        allowedRoles: ['admin', 'cs', 'account', 'shipper', 'forwarder_agent'],
      },
      {
        id: 'shipments-tracking',
        title: 'Track Shipments',
        href: '/dashboard/shipments/tracking',
        icon: MapPin,
        allowedRoles: [
          'admin',
          'cs',
          'account',
          'customer',
          'carrier',
          'factory',
          'shipper',
          'consignee',
          'notify_party',
          'forwarder_agent',
        ],
      },
    ],
  },
  {
    id: 'logistics',
    title: 'Logistics',
    href: '/dashboard/logistics',
    icon: Truck,
    description: 'Transportation and delivery',
    allowedRoles: ['admin', 'cs', 'account', 'carrier', 'forwarder_agent'],
    children: [
      {
        id: 'logistics-routes',
        title: 'Routes',
        href: '/dashboard/logistics/routes',
        icon: MapPin,
        allowedRoles: ['admin', 'cs', 'account', 'carrier', 'forwarder_agent'],
      },
      {
        id: 'logistics-vehicles',
        title: 'Vehicles',
        href: '/dashboard/logistics/vehicles',
        icon: Truck,
        allowedRoles: ['admin', 'cs', 'account', 'carrier'],
      },
      // {
      //   id: 'logistics-drivers',
      //   title: 'Drivers',
      //   href: '/dashboard/logistics/drivers',
      //   icon: User,
      //   allowedRoles: ['admin', 'cs', 'account', 'carrier'],
      // },
    ],
  },
  // {
  //   id: 'inventory',
  //   title: 'Inventory',
  //   href: '/dashboard/inventory',
  //   icon: Warehouse,
  //   description: 'Warehouse and stock management',
  //   allowedRoles: ['admin', 'cs', 'account', 'factory', 'shipper'],
  //   children: [
  //     {
  //       id: 'inventory-products',
  //       title: 'Products',
  //       href: '/dashboard/inventory/products',
  //       icon: Package,
  //       allowedRoles: ['admin', 'cs', 'account', 'factory', 'shipper'],
  //     },
  //     {
  //       id: 'inventory-warehouses',
  //       title: 'Warehouses',
  //       href: '/dashboard/inventory/warehouses',
  //       icon: Warehouse,
  //       allowedRoles: ['admin', 'cs', 'account', 'factory'],
  //     },
  //   ],
  // },
  {
    id: 'customers',
    title: 'Customers',
    href: '/customers',
    icon: Users,
    description: 'Customer relationship management',
    allowedRoles: ['admin', 'cs', 'account'],
    children: [
      {
        id: 'customers-all',
        title: 'All Customers',
        href: '/customers',
        icon: Users,
        allowedRoles: ['admin', 'cs', 'account'],
      },
      {
        id: 'master-data-relationships',
        title: 'Customer Relationships',
        href: '/master-data/relationships',
        icon: Link,
        allowedRoles: ['admin', 'cs'],
      },
      {
        id: 'master-data-product-relationships',
        title: 'Customer Product Relationships',
        href: '/master-data/product-relationships',
        icon: Package,
        allowedRoles: ['admin', 'cs'],
      },
    ],
  },
  {
    id: 'companies',
    title: 'Companies',
    href: '/dashboard/companies',
    icon: Building2,
    description: 'Manage business partners',
    allowedRoles: ['admin', 'cs', 'account'],
    children: [
      {
        id: 'companies-all',
        title: 'All Companies',
        href: '/master-data/companies',
        icon: Building2,
        allowedRoles: ['admin', 'cs', 'account'],
      },
      {
        id: 'companies-create',
        title: 'Add Company',
        href: '/dashboard/companies/create',
        icon: Building2,
        allowedRoles: ['admin', 'cs', 'account'],
      },
    ],
  },
  {
    id: 'master-data',
    title: 'Master Data',
    href: '/dashboard/master-data',
    icon: Settings,
    description: 'Manage products, units, and reference data',
    allowedRoles: ['admin', 'cs'],
    children: [
      {
        id: 'master-data-companies',
        title: 'Companies',
        href: '/master-data/companies',
        icon: Building2,
        allowedRoles: ['admin', 'cs'],
      },
      {
        id: 'master-data-notify-parties',
        title: 'Consignee Notify Party Relationships',
        href: '/master-data/notify-parties',
        icon: Link,
        allowedRoles: ['admin', 'cs'],
      },
      {
        id: 'master-data-products',
        title: 'Products',
        href: '/master-data/products',
        icon: Package,
        allowedRoles: ['admin', 'cs'],
      },
      {
        id: 'master-data-units',
        title: 'Units of Measure',
        href: '/master-data/units-of-measure',
        icon: Settings,
        allowedRoles: ['admin', 'cs'],
      },
      {
        id: 'master-data-ports',
        title: 'Ports',
        href: '/master-data/ports',
        icon: Ship,
        allowedRoles: ['admin', 'cs'],
      },
      {
        id: 'master-data-drivers',
        title: 'Drivers',
        href: '/master-data/drivers',
        icon: User,
        allowedRoles: ['admin', 'cs'],
      },
    ],
  },
  {
    id: 'documents',
    title: 'Documents',
    href: '/documents',
    icon: FileText,
    description: 'Shipping and legal documents',
    allowedRoles: [
      'admin',
      'cs',
      'account',
      'customer',
      'carrier',
      'factory',
      'shipper',
      'consignee',
      'notify_party',
      'forwarder_agent',
    ],
    children: [
      {
        id: 'documents-generate',
        title: 'Generate Documents',
        href: '/documents/generate',
        icon: FileText,
        description: 'Generate invoices, packing lists, and other documents',
        allowedRoles: [
          'admin',
          'cs',
          'account',
          'factory',
          'shipper',
          'forwarder_agent',
        ],
      },
      {
        id: 'documents-templates',
        title: 'Document Templates',
        href: '/documents/templates',
        icon: Settings,
        description: 'Manage document templates and layouts',
        allowedRoles: ['admin', 'cs'],
      },
    ],
  },
  {
    id: 'notifications',
    title: 'Notifications',
    href: '/dashboard/notifications',
    icon: Bell,
    description: 'Alerts and messages',
    allowedRoles: [
      'admin',
      'cs',
      'account',
      'customer',
      'carrier',
      'factory',
      'shipper',
      'consignee',
      'notify_party',
      'forwarder_agent',
    ],
  },
  {
    id: 'reports',
    title: 'Reports',
    href: '/dashboard/reports',
    icon: BarChart3,
    description: 'Analytics and insights',
    allowedRoles: ['admin', 'cs', 'account'],
    children: [
      {
        id: 'reports-shipments',
        title: 'Shipment Reports',
        href: '/dashboard/reports/shipments',
        icon: Package,
        allowedRoles: ['admin', 'cs', 'account'],
      },
      {
        id: 'reports-financial',
        title: 'Financial Reports',
        href: '/dashboard/reports/financial',
        icon: CreditCard,
        allowedRoles: ['admin', 'account'],
      },
      {
        id: 'reports-performance',
        title: 'Performance Reports',
        href: '/dashboard/reports/performance',
        icon: BarChart3,
        allowedRoles: ['admin', 'cs', 'account'],
      },
    ],
  },
  {
    id: 'admin',
    title: 'Administration',
    href: '/dashboard/admin',
    icon: Shield,
    description: 'System administration',
    allowedRoles: ['admin'],
    children: [
      {
        id: 'admin-users',
        title: 'User Management',
        href: '/admin/users',
        icon: Users,
        allowedRoles: ['admin'],
      },
      {
        id: 'admin-system',
        title: 'System Settings',
        href: '/dashboard/admin/system',
        icon: Settings,
        allowedRoles: ['admin'],
      },
      {
        id: 'admin-audit',
        title: 'Audit Logs',
        href: '/dashboard/admin/audit',
        icon: FileText,
        allowedRoles: ['admin'],
      },
    ],
  },
  {
    id: 'settings',
    title: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
    description: 'Account and preferences',
    allowedRoles: [
      'admin',
      'cs',
      'account',
      'customer',
      'carrier',
      'driver',
      'factory',
      'shipper',
      'consignee',
      'notify_party',
      'forwarder_agent',
    ],
  },
]

// Mobile-specific navigation for drivers
export const mobileNavigationItems: NavigationItem[] = [
  {
    id: 'mobile-dashboard',
    title: 'Dashboard',
    href: '/driver/dashboard',
    icon: LayoutDashboard,
    description: 'Driver overview',
    allowedRoles: ['driver'],
  },
  {
    id: 'mobile-deliveries',
    title: 'My Deliveries',
    href: '/mobile/deliveries',
    icon: Package,
    description: 'Assigned shipments',
    allowedRoles: ['driver'],
  },
  {
    id: 'mobile-routes',
    title: 'Routes',
    href: '/mobile/routes',
    icon: MapPin,
    description: 'Navigation and routing',
    allowedRoles: ['driver'],
  },
  {
    id: 'mobile-status',
    title: 'Status Updates',
    href: '/mobile/status',
    icon: Bell,
    description: 'Update delivery status',
    allowedRoles: ['driver'],
  },
  {
    id: 'mobile-settings',
    title: 'Settings',
    href: '/mobile/settings',
    icon: Settings,
    description: 'Driver preferences',
    allowedRoles: ['driver'],
  },
]

// Filter navigation items based on user role
export function getNavigationForRole(
  role: UserRole,
  isMobile: boolean = false
): NavigationItem[] {
  const items = isMobile ? mobileNavigationItems : navigationItems

  return items
    .filter(item => item.allowedRoles.includes(role))
    .map(item => ({
      ...item,
      children: item.children?.filter(child =>
        child.allowedRoles.includes(role)
      ),
    }))
}

// Get navigation item by id
export function getNavigationItem(
  id: string,
  isMobile: boolean = false
): NavigationItem | undefined {
  const items = isMobile ? mobileNavigationItems : navigationItems

  for (const item of items) {
    if (item.id === id) return item
    if (item.children) {
      const child = item.children.find(child => child.id === id)
      if (child) return child
    }
  }

  return undefined
}

// Check if user can access a specific route
export function canAccessRoute(
  href: string,
  role: UserRole,
  isMobile: boolean = false
): boolean {
  const items = isMobile ? mobileNavigationItems : navigationItems

  for (const item of items) {
    if (item.href === href && item.allowedRoles.includes(role)) return true
    if (item.children) {
      for (const child of item.children) {
        if (child.href === href && child.allowedRoles.includes(role))
          return true
      }
    }
  }

  return false
}
