import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { NextRequest, NextResponse } from 'next/server'
import { withRole } from '@/components/auth/with-role'
import {
  getNavigationForRole,
  canAccessRoute,
  getNavigationItem,
  navigationItems,
  mobileNavigationItems,
} from '@/lib/constants/routes'
import type { UserRole } from '@/lib/supabase/auth'

// Mock getCurrentUser for withRole testing
vi.mock('@/lib/supabase/auth', () => ({
  getCurrentUser: vi.fn(),
  isAdmin: vi.fn(),
  isStaff: vi.fn(),
  canManageUsers: vi.fn(),
  canViewAllData: vi.fn(),
  validateRoleCompanyAssociation: vi.fn(),
}))

// Mock Supabase server client for middleware testing
vi.mock('@supabase/ssr', () => ({
  createServerClient: vi.fn(),
}))

describe('Role-Based Access Control Tests', () => {
  describe('withRole HOC Component Protection', () => {
    const TestComponent = ({ message }: { message: string }) => (
      <div data-testid="protected-content">{message}</div>
    )

    const FallbackComponent = () => (
      <div data-testid="fallback-content">Custom Fallback</div>
    )

    beforeEach(() => {
      vi.clearAllMocks()
    })

    describe('Single Role Access', () => {
      it('should render component when user has required role', async () => {
        const { getCurrentUser } = require('@/lib/supabase/auth')
        getCurrentUser.mockResolvedValue({
          user: { id: 'user-123' },
          profile: { role: 'admin', is_active: true },
        })

        const ProtectedComponent = withRole(TestComponent, 'admin')
        render(<ProtectedComponent message="Admin Content" />)

        await waitFor(() => {
          expect(screen.getByTestId('protected-content')).toBeInTheDocument()
          expect(screen.getByText('Admin Content')).toBeInTheDocument()
        })
      })

      it('should show access denied when user lacks required role', async () => {
        const { getCurrentUser } = require('@/lib/supabase/auth')
        getCurrentUser.mockResolvedValue({
          user: { id: 'user-123' },
          profile: { role: 'customer', is_active: true },
        })

        const ProtectedComponent = withRole(TestComponent, 'admin')
        render(<ProtectedComponent message="Admin Content" />)

        await waitFor(() => {
          expect(screen.getByText('Access Denied')).toBeInTheDocument()
          expect(screen.getByText("You don't have permission to view this content.")).toBeInTheDocument()
          expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
        })
      })

      it('should show custom fallback component when access denied', async () => {
        const { getCurrentUser } = require('@/lib/supabase/auth')
        getCurrentUser.mockResolvedValue({
          user: { id: 'user-123' },
          profile: { role: 'customer', is_active: true },
        })

        const ProtectedComponent = withRole(TestComponent, 'admin', FallbackComponent)
        render(<ProtectedComponent message="Admin Content" />)

        await waitFor(() => {
          expect(screen.getByTestId('fallback-content')).toBeInTheDocument()
          expect(screen.getByText('Custom Fallback')).toBeInTheDocument()
          expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
        })
      })
    })

    describe('Multiple Role Access', () => {
      it('should render component when user has one of multiple required roles', async () => {
        const { getCurrentUser } = require('@/lib/supabase/auth')
        getCurrentUser.mockResolvedValue({
          user: { id: 'user-123' },
          profile: { role: 'cs', is_active: true },
        })

        const ProtectedComponent = withRole(TestComponent, ['admin', 'cs', 'account'])
        render(<ProtectedComponent message="Staff Content" />)

        await waitFor(() => {
          expect(screen.getByTestId('protected-content')).toBeInTheDocument()
          expect(screen.getByText('Staff Content')).toBeInTheDocument()
        })
      })

      it('should deny access when user role not in allowed roles list', async () => {
        const { getCurrentUser } = require('@/lib/supabase/auth')
        getCurrentUser.mockResolvedValue({
          user: { id: 'user-123' },
          profile: { role: 'driver', is_active: true },
        })

        const ProtectedComponent = withRole(TestComponent, ['admin', 'cs', 'account'])
        render(<ProtectedComponent message="Staff Content" />)

        await waitFor(() => {
          expect(screen.getByText('Access Denied')).toBeInTheDocument()
          expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
        })
      })
    })

    describe('Authentication States', () => {
      it('should show loading state while checking access', async () => {
        const { getCurrentUser } = require('@/lib/supabase/auth')
        getCurrentUser.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({
          user: { id: 'user-123' },
          profile: { role: 'admin', is_active: true },
        }), 100)))

        const ProtectedComponent = withRole(TestComponent, 'admin')
        render(<ProtectedComponent message="Admin Content" />)

        // Should show loading spinner initially
        expect(document.querySelector('.animate-spin')).toBeInTheDocument()

        await waitFor(() => {
          expect(screen.getByTestId('protected-content')).toBeInTheDocument()
        })
      })

      it('should deny access when user is not authenticated', async () => {
        const { getCurrentUser } = require('@/lib/supabase/auth')
        getCurrentUser.mockResolvedValue(null)

        const ProtectedComponent = withRole(TestComponent, 'admin')
        render(<ProtectedComponent message="Admin Content" />)

        await waitFor(() => {
          expect(screen.getByText('Access Denied')).toBeInTheDocument()
          expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
        })
      })

      it('should handle authentication errors gracefully', async () => {
        const { getCurrentUser } = require('@/lib/supabase/auth')
        getCurrentUser.mockRejectedValue(new Error('Auth service unavailable'))

        const ProtectedComponent = withRole(TestComponent, 'admin')
        render(<ProtectedComponent message="Admin Content" />)

        await waitFor(() => {
          expect(screen.getByText('Access Denied')).toBeInTheDocument()
          expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
        })
      })
    })
  })

  describe('Navigation Role Filtering', () => {
    describe('Desktop Navigation', () => {
      it('should return admin navigation items for admin role', () => {
        const adminNavigation = getNavigationForRole('admin')

        // Admin should see all navigation items
        expect(adminNavigation).toHaveLength(navigationItems.length)
        
        // Should include admin-only items
        const adminSection = adminNavigation.find(item => item.id === 'admin')
        expect(adminSection).toBeDefined()
        expect(adminSection!.allowedRoles).toContain('admin')
      })

      it('should filter navigation items for customer role', () => {
        const customerNavigation = getNavigationForRole('customer')

        // Customer should not see admin, logistics, inventory, customers, companies, or reports
        const restrictedSections = ['admin', 'logistics', 'inventory', 'customers', 'companies', 'reports']
        restrictedSections.forEach(sectionId => {
          const section = customerNavigation.find(item => item.id === sectionId)
          expect(section).toBeUndefined()
        })

        // Customer should see dashboard, shipments, documents, notifications, settings
        const allowedSections = ['dashboard', 'shipments', 'documents', 'notifications', 'settings']
        allowedSections.forEach(sectionId => {
          const section = customerNavigation.find(item => item.id === sectionId)
          expect(section).toBeDefined()
        })
      })

      it('should filter sub-navigation items based on role', () => {
        const customerNavigation = getNavigationForRole('customer')
        const shipmentsSection = customerNavigation.find(item => item.id === 'shipments')
        
        expect(shipmentsSection).toBeDefined()
        expect(shipmentsSection!.children).toBeDefined()
        
        // Customer should see 'All Shipments' and 'Track Shipments' but not 'Create Shipment'
        const allowedChildren = shipmentsSection!.children!.map(child => child.id)
        expect(allowedChildren).toContain('shipments-all')
        expect(allowedChildren).toContain('shipments-tracking')
        expect(allowedChildren).not.toContain('shipments-create')
      })

      it('should return different navigation sets for different roles', () => {
        const adminNav = getNavigationForRole('admin')
        const customerNav = getNavigationForRole('customer')
        const driverNav = getNavigationForRole('driver')

        // Different roles should have different navigation lengths
        expect(adminNav.length).toBeGreaterThan(customerNav.length)
        expect(customerNav.length).toBeGreaterThan(driverNav.length)
      })
    })

    describe('Mobile Navigation', () => {
      it('should return mobile navigation for driver role', () => {
        const driverMobileNav = getNavigationForRole('driver', true)

        expect(driverMobileNav).toHaveLength(mobileNavigationItems.length)
        
        // All mobile items should allow driver role
        driverMobileNav.forEach(item => {
          expect(item.allowedRoles).toContain('driver')
        })
      })

      it('should return empty navigation for non-driver roles on mobile', () => {
        const adminMobileNav = getNavigationForRole('admin', true)
        const customerMobileNav = getNavigationForRole('customer', true)

        expect(adminMobileNav).toHaveLength(0)
        expect(customerMobileNav).toHaveLength(0)
      })
    })
  })

  describe('Route Access Validation', () => {
    describe('canAccessRoute Function', () => {
      it('should allow admin access to all routes', () => {
        const testRoutes = [
          '/dashboard/overview',
          '/dashboard/admin/users',
          '/dashboard/shipments',
          '/dashboard/reports/financial',
        ]

        testRoutes.forEach(route => {
          expect(canAccessRoute(route, 'admin')).toBe(true)
        })
      })

      it('should restrict customer access to appropriate routes', () => {
        // Routes customer should access
        const allowedRoutes = [
          '/dashboard/overview',
          '/dashboard/shipments',
          '/dashboard/shipments/tracking',
          '/dashboard/documents',
          '/dashboard/notifications',
          '/dashboard/settings',
        ]

        allowedRoutes.forEach(route => {
          expect(canAccessRoute(route, 'customer')).toBe(true)
        })

        // Routes customer should not access
        const restrictedRoutes = [
          '/dashboard/admin/users',
          '/dashboard/shipments/create',
          '/dashboard/logistics/routes',
          '/dashboard/inventory/products',
          '/dashboard/customers',
          '/dashboard/companies',
          '/dashboard/reports/financial',
        ]

        restrictedRoutes.forEach(route => {
          expect(canAccessRoute(route, 'customer')).toBe(false)
        })
      })

      it('should handle mobile route access for driver role', () => {
        const driverMobileRoutes = [
          '/driver/dashboard',
          '/mobile/deliveries',
          '/mobile/routes',
          '/mobile/status',
          '/mobile/settings',
        ]

        driverMobileRoutes.forEach(route => {
          expect(canAccessRoute(route, 'driver', true)).toBe(true)
        })

        // Non-drivers should not access mobile routes
        driverMobileRoutes.forEach(route => {
          expect(canAccessRoute(route, 'admin', true)).toBe(false)
          expect(canAccessRoute(route, 'customer', true)).toBe(false)
        })
      })

      it('should handle role-specific sub-routes', () => {
        // Financial reports should only be accessible to admin and account roles
        expect(canAccessRoute('/dashboard/reports/financial', 'admin')).toBe(true)
        expect(canAccessRoute('/dashboard/reports/financial', 'account')).toBe(true)
        expect(canAccessRoute('/dashboard/reports/financial', 'cs')).toBe(false)
        expect(canAccessRoute('/dashboard/reports/financial', 'customer')).toBe(false)
      })
    })

    describe('getNavigationItem Function', () => {
      it('should find navigation item by id', () => {
        const dashboardItem = getNavigationItem('dashboard')
        expect(dashboardItem).toBeDefined()
        expect(dashboardItem!.id).toBe('dashboard')
        expect(dashboardItem!.title).toBe('Dashboard')
      })

      it('should find child navigation item by id', () => {
        const createShipmentItem = getNavigationItem('shipments-create')
        expect(createShipmentItem).toBeDefined()
        expect(createShipmentItem!.id).toBe('shipments-create')
        expect(createShipmentItem!.title).toBe('Create Shipment')
      })

      it('should find mobile navigation item by id', () => {
        const mobileDeliveriesItem = getNavigationItem('mobile-deliveries', true)
        expect(mobileDeliveriesItem).toBeDefined()
        expect(mobileDeliveriesItem!.id).toBe('mobile-deliveries')
        expect(mobileDeliveriesItem!.title).toBe('My Deliveries')
      })

      it('should return undefined for non-existent navigation item', () => {
        const nonExistentItem = getNavigationItem('non-existent')
        expect(nonExistentItem).toBeUndefined()
      })
    })
  })

  describe('Role-Specific Navigation Content', () => {
    const roleNavigationTests: Array<{
      role: UserRole
      shouldSee: string[]
      shouldNotSee: string[]
    }> = [
      {
        role: 'admin',
        shouldSee: ['dashboard', 'shipments', 'logistics', 'inventory', 'customers', 'companies', 'documents', 'notifications', 'reports', 'admin', 'settings'],
        shouldNotSee: [],
      },
      {
        role: 'cs',
        shouldSee: ['dashboard', 'shipments', 'logistics', 'inventory', 'customers', 'companies', 'documents', 'notifications', 'reports', 'settings'],
        shouldNotSee: ['admin'],
      },
      {
        role: 'account',
        shouldSee: ['dashboard', 'shipments', 'logistics', 'inventory', 'customers', 'companies', 'documents', 'notifications', 'reports', 'settings'],
        shouldNotSee: ['admin'],
      },
      {
        role: 'customer',
        shouldSee: ['dashboard', 'shipments', 'documents', 'notifications', 'settings'],
        shouldNotSee: ['logistics', 'inventory', 'customers', 'companies', 'reports', 'admin'],
      },
      {
        role: 'carrier',
        shouldSee: ['dashboard', 'shipments', 'logistics', 'documents', 'notifications', 'settings'],
        shouldNotSee: ['inventory', 'customers', 'companies', 'reports', 'admin'],
      },
      {
        role: 'driver',
        shouldSee: ['dashboard', 'shipments', 'documents', 'notifications', 'settings'],
        shouldNotSee: ['logistics', 'inventory', 'customers', 'companies', 'reports', 'admin'],
      },
      {
        role: 'factory',
        shouldSee: ['dashboard', 'shipments', 'inventory', 'documents', 'notifications', 'settings'],
        shouldNotSee: ['logistics', 'customers', 'companies', 'reports', 'admin'],
      },
      {
        role: 'shipper',
        shouldSee: ['dashboard', 'shipments', 'inventory', 'documents', 'notifications', 'settings'],
        shouldNotSee: ['logistics', 'customers', 'companies', 'reports', 'admin'],
      },
      {
        role: 'consignee',
        shouldSee: ['dashboard', 'shipments', 'documents', 'notifications', 'settings'],
        shouldNotSee: ['logistics', 'inventory', 'customers', 'companies', 'reports', 'admin'],
      },
      {
        role: 'notify_party',
        shouldSee: ['dashboard', 'shipments', 'documents', 'notifications', 'settings'],
        shouldNotSee: ['logistics', 'inventory', 'customers', 'companies', 'reports', 'admin'],
      },
      {
        role: 'forwarder_agent',
        shouldSee: ['dashboard', 'shipments', 'logistics', 'documents', 'notifications', 'settings'],
        shouldNotSee: ['inventory', 'customers', 'companies', 'reports', 'admin'],
      },
    ]

    roleNavigationTests.forEach(({ role, shouldSee, shouldNotSee }) => {
      describe(`${role} role navigation`, () => {
        it(`should see appropriate navigation items for ${role}`, () => {
          const navigation = getNavigationForRole(role)
          const navigationIds = navigation.map(item => item.id)

          shouldSee.forEach(expectedId => {
            expect(navigationIds).toContain(expectedId)
          })
        })

        it(`should not see restricted navigation items for ${role}`, () => {
          const navigation = getNavigationForRole(role)
          const navigationIds = navigation.map(item => item.id)

          shouldNotSee.forEach(restrictedId => {
            expect(navigationIds).not.toContain(restrictedId)
          })
        })
      })
    })
  })

  describe('Edge Cases and Error Handling', () => {
    it('should handle undefined role gracefully', () => {
      const navigation = getNavigationForRole(undefined as any)
      expect(navigation).toEqual([])
    })

    it('should handle empty role string gracefully', () => {
      const navigation = getNavigationForRole('' as any)
      expect(navigation).toEqual([])
    })

    it('should handle invalid route access checks', () => {
      expect(canAccessRoute('', 'admin')).toBe(false)
      expect(canAccessRoute('/non-existent-route', 'admin')).toBe(false)
      expect(canAccessRoute('/dashboard/overview', '' as any)).toBe(false)
    })

    it('should maintain referential integrity for navigation items', () => {
      const adminNav1 = getNavigationForRole('admin')
      const adminNav2 = getNavigationForRole('admin')

      // Should return equivalent but not identical objects
      expect(adminNav1).toEqual(adminNav2)
      expect(adminNav1).not.toBe(adminNav2)
    })

    it('should filter out items with empty children arrays', () => {
      const navigation = getNavigationForRole('customer')
      
      navigation.forEach(item => {
        if (item.children) {
          expect(item.children.length).toBeGreaterThan(0)
        }
      })
    })
  })
})