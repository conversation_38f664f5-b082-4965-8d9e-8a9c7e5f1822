import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import LoginPage from '@/app/(auth)/login/page'
import RegisterPage from '@/app/(auth)/register/page'

// Mock the hooks and dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('@/hooks/use-auth', () => ({
  useAuth: vi.fn(),
}))

vi.mock('@/lib/supabase/auth', () => ({
  authClient: {
    signIn: vi.fn(),
    signUp: vi.fn(),
  },
}))

vi.mock('@/lib/supabase/client', () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        order: vi.fn(() => Promise.resolve({ data: [], error: null })),
      })),
    })),
  })),
}))

// Mock the AuthErrorDisplay component
vi.mock('@/components/auth/auth-error-display', () => ({
  AuthErrorDisplay: ({ error, onRetry }: any) => 
    error ? (
      <div data-testid="auth-error-display">
        <span>{typeof error === 'string' ? error : error.message}</span>
        {onRetry && <button onClick={onRetry} data-testid="retry-button">Retry</button>}
      </div>
    ) : null,
}))

describe('LoginPage Component', () => {
  const mockPush = vi.fn()
  const mockUseAuth = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue({ push: mockPush })
    ;(require('@/hooks/use-auth').useAuth as any).mockReturnValue({
      isAuthenticated: false,
      loading: false,
    })
  })

  describe('Initial Render', () => {
    it('should render login form with all required fields', () => {
      render(<LoginPage />)
      
      expect(screen.getByText('Welcome back')).toBeInTheDocument()
      expect(screen.getByText('Sign in to your account to continue')).toBeInTheDocument()
      expect(screen.getByLabelText('Email')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
      expect(screen.getByText('Forgot your password?')).toBeInTheDocument()
      expect(screen.getByText("Don't have an account?")).toBeInTheDocument()
    })

    it('should have correct initial form values', () => {
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email') as HTMLInputElement
      const passwordInput = screen.getByLabelText('Password') as HTMLInputElement
      
      expect(emailInput.value).toBe('')
      expect(passwordInput.value).toBe('')
      expect(passwordInput.type).toBe('password')
    })

    it('should render password field with eye icon toggle', () => {
      render(<LoginPage />)
      
      const toggleButton = screen.getByRole('button', { name: '' }) // Eye icon button
      expect(toggleButton).toBeInTheDocument()
    })
  })

  describe('Form Interactions', () => {
    it('should update form fields when user types', async () => {
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      
      await waitFor(() => {
        expect((emailInput as HTMLInputElement).value).toBe('<EMAIL>')
        expect((passwordInput as HTMLInputElement).value).toBe('password123')
      })
    })

    it('should toggle password visibility when eye icon is clicked', async () => {
      render(<LoginPage />)
      
      const passwordInput = screen.getByLabelText('Password') as HTMLInputElement
      const toggleButton = screen.getByRole('button', { name: '' })
      
      expect(passwordInput.type).toBe('password')
      
      fireEvent.click(toggleButton)
      
      await waitFor(() => {
        expect(passwordInput.type).toBe('text')
      })
      
      fireEvent.click(toggleButton)
      
      await waitFor(() => {
        expect(passwordInput.type).toBe('password')
      })
    })

    it('should show loading state during form submission', async () => {
      const mockSignIn = vi.fn(() => new Promise(resolve => setTimeout(resolve, 100)))
      ;(require('@/lib/supabase/auth').authClient.signIn as any) = mockSignIn
      
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)
      
      // Should show loading spinner
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /sign in/i })).toBeDisabled()
      })
    })
  })

  describe('Authentication Logic', () => {
    it('should call authClient.signIn with correct credentials', async () => {
      const mockSignIn = vi.fn().mockResolvedValue({})
      ;(require('@/lib/supabase/auth').authClient.signIn as any) = mockSignIn
      
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith('<EMAIL>', 'password123')
      })
    })

    it('should redirect to dashboard when authenticated', () => {
      ;(require('@/hooks/use-auth').useAuth as any).mockReturnValue({
        isAuthenticated: true,
        loading: false,
      })
      
      render(<LoginPage />)
      
      expect(mockPush).toHaveBeenCalledWith('/dashboard')
    })

    it('should not redirect when loading', () => {
      ;(require('@/hooks/use-auth').useAuth as any).mockReturnValue({
        isAuthenticated: false,
        loading: true,
      })
      
      render(<LoginPage />)
      
      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should display authentication errors', async () => {
      const mockSignIn = vi.fn().mockRejectedValue({
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password',
        timestamp: new Date(),
      })
      ;(require('@/lib/supabase/auth').authClient.signIn as any) = mockSignIn
      
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByTestId('auth-error-display')).toBeInTheDocument()
        expect(screen.getByText('Invalid email or password')).toBeInTheDocument()
      })
    })

    it('should handle generic errors', async () => {
      const mockSignIn = vi.fn().mockRejectedValue(new Error('Network error'))
      ;(require('@/lib/supabase/auth').authClient.signIn as any) = mockSignIn
      
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByTestId('auth-error-display')).toBeInTheDocument()
        expect(screen.getByText('Network error')).toBeInTheDocument()
      })
    })

    it('should handle retry functionality', async () => {
      const mockSignIn = vi.fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({})
      ;(require('@/lib/supabase/auth').authClient.signIn as any) = mockSignIn
      
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByTestId('auth-error-display')).toBeInTheDocument()
      })
      
      const retryButton = screen.getByTestId('retry-button')
      fireEvent.click(retryButton)
      
      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledTimes(2)
      })
    })
  })

  describe('Form Validation', () => {
    it('should show validation errors for invalid email', async () => {
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email')
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/invalid email/i)).toBeInTheDocument()
      })
    })

    it('should show validation errors for missing password', async () => {
      render(<LoginPage />)
      
      const emailInput = screen.getByLabelText('Email')
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/password is required/i)).toBeInTheDocument()
      })
    })
  })

  describe('Navigation Links', () => {
    it('should have correct navigation links', () => {
      render(<LoginPage />)
      
      const forgotPasswordLink = screen.getByText('Forgot your password?')
      const signUpLink = screen.getByText('Sign up')
      
      expect(forgotPasswordLink.closest('a')).toHaveAttribute('href', '/forgot-password')
      expect(signUpLink.closest('a')).toHaveAttribute('href', '/register')
    })
  })
})

describe('RegisterPage Component', () => {
  const mockPush = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue({ push: mockPush })
  })

  describe('Initial Render', () => {
    it('should render registration form with all required fields', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Create your account')).toBeInTheDocument()
        expect(screen.getByLabelText('Email')).toBeInTheDocument()
        expect(screen.getByLabelText('Password')).toBeInTheDocument()
        expect(screen.getByLabelText('Confirm Password')).toBeInTheDocument()
        expect(screen.getByLabelText('First Name')).toBeInTheDocument()
        expect(screen.getByLabelText('Last Name')).toBeInTheDocument()
        expect(screen.getByLabelText('Phone Number')).toBeInTheDocument()
        expect(screen.getByText('Role')).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
      })
    })

    it('should have customer as default role selection', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        // Check if role selector shows default value
        expect(screen.getByText('Customer')).toBeInTheDocument()
      })
    })

    it('should show password toggle buttons for both password fields', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        // Should have two password toggle buttons (password and confirm password)
        const toggleButtons = screen.getAllByRole('button', { name: '' })
        expect(toggleButtons.length).toBeGreaterThanOrEqual(2)
      })
    })
  })

  describe('Form Interactions', () => {
    it('should update form fields when user types', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        const emailInput = screen.getByLabelText('Email')
        const firstNameInput = screen.getByLabelText('First Name')
        
        fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
        fireEvent.change(firstNameInput, { target: { value: 'John' } })
        
        expect((emailInput as HTMLInputElement).value).toBe('<EMAIL>')
        expect((firstNameInput as HTMLInputElement).value).toBe('John')
      })
    })

    it('should toggle password visibility for both password fields', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        const passwordInput = screen.getByLabelText('Password') as HTMLInputElement
        const confirmPasswordInput = screen.getByLabelText('Confirm Password') as HTMLInputElement
        
        expect(passwordInput.type).toBe('password')
        expect(confirmPasswordInput.type).toBe('password')
        
        // Click first toggle button (password field)
        const toggleButtons = screen.getAllByRole('button', { name: '' })
        fireEvent.click(toggleButtons[0])
        
        expect(passwordInput.type).toBe('text')
      })
    })

    it('should load companies on component mount', async () => {
      const mockClient = {
        from: vi.fn(() => ({
          select: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: [
                { id: '1', name: 'Test Company', company_type: 'customer' }
              ],
              error: null
            }))
          }))
        }))
      }
      ;(require('@/lib/supabase/client').createClient as any).mockReturnValue(mockClient)
      
      render(<RegisterPage />)
      
      await waitFor(() => {
        expect(mockClient.from).toHaveBeenCalledWith('companies')
      })
    })
  })

  describe('Role Selection', () => {
    it('should show role descriptions when different roles are selected', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        // Should show customer description by default
        expect(screen.getByText('Customer portal access to shipments')).toBeInTheDocument()
      })
    })

    it('should show company selection for non-staff roles', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        // Customer role should show company selection
        expect(screen.getByText('Company')).toBeInTheDocument()
      })
    })
  })

  describe('Form Validation', () => {
    it('should validate email format', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        const emailInput = screen.getByLabelText('Email')
        const submitButton = screen.getByRole('button', { name: /create account/i })
        
        fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
        fireEvent.click(submitButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/invalid email/i)).toBeInTheDocument()
      })
    })

    it('should validate password confirmation match', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        const passwordInput = screen.getByLabelText('Password')
        const confirmPasswordInput = screen.getByLabelText('Confirm Password')
        const submitButton = screen.getByRole('button', { name: /create account/i })
        
        fireEvent.change(passwordInput, { target: { value: 'password123' } })
        fireEvent.change(confirmPasswordInput, { target: { value: 'differentpassword' } })
        fireEvent.click(submitButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
      })
    })

    it('should validate required fields', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: /create account/i })
        fireEvent.click(submitButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument()
        expect(screen.getByText(/first name is required/i)).toBeInTheDocument()
        expect(screen.getByText(/last name is required/i)).toBeInTheDocument()
      })
    })
  })

  describe('Registration Submission', () => {
    it('should call authClient.signUp with correct data', async () => {
      const mockSignUp = vi.fn().mockResolvedValue({})
      ;(require('@/lib/supabase/auth').authClient.signUp as any) = mockSignUp
      
      render(<RegisterPage />)
      
      await waitFor(() => {
        const emailInput = screen.getByLabelText('Email')
        const passwordInput = screen.getByLabelText('Password')
        const confirmPasswordInput = screen.getByLabelText('Confirm Password')
        const firstNameInput = screen.getByLabelText('First Name')
        const lastNameInput = screen.getByLabelText('Last Name')
        const submitButton = screen.getByRole('button', { name: /create account/i })
        
        fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
        fireEvent.change(passwordInput, { target: { value: 'StrongPassword123!' } })
        fireEvent.change(confirmPasswordInput, { target: { value: 'StrongPassword123!' } })
        fireEvent.change(firstNameInput, { target: { value: 'John' } })
        fireEvent.change(lastNameInput, { target: { value: 'Doe' } })
        
        fireEvent.click(submitButton)
      })
      
      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith(
          expect.objectContaining({
            email: '<EMAIL>',
            password: 'StrongPassword123!',
            firstName: 'John',
            lastName: 'Doe',
            role: 'customer'
          })
        )
      })
    })

    it('should show loading state during registration', async () => {
      const mockSignUp = vi.fn(() => new Promise(resolve => setTimeout(resolve, 100)))
      ;(require('@/lib/supabase/auth').authClient.signUp as any) = mockSignUp
      
      render(<RegisterPage />)
      
      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: /create account/i })
        fireEvent.click(submitButton)
        
        expect(submitButton).toBeDisabled()
      })
    })
  })

  describe('Navigation Links', () => {
    it('should have link to login page', async () => {
      render(<RegisterPage />)
      
      await waitFor(() => {
        const signInLink = screen.getByText('Sign in')
        expect(signInLink.closest('a')).toHaveAttribute('href', '/login')
      })
    })
  })
})