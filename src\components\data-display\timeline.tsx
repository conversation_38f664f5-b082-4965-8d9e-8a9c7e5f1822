'use client'

import { useState } from 'react'
import { formatDistanceToNow, parseISO } from 'date-fns'
import { MapPin, User, Clock, FileText, AlertCircle, ChevronDown, ChevronUp, Eye, EyeOff } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { StatusBadge } from './status-badge'
import type { StatusHistory } from '@/lib/validations/status-updates'
import type { ShipmentStatus } from '@/lib/supabase/types'
import { SHIPMENT_STATUSES } from '@/lib/validations/shipment'

interface TimelineProps {
  statusHistory: StatusHistory[]
  isLoading?: boolean
  error?: string
  className?: string
}

export function Timeline({
  statusHistory = [],
  isLoading = false,
  error,
  className = '',
}: TimelineProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (isLoading) {
    return (
      <Card className={`bg-slate-800 border-slate-700 ${className}`}>
        <CardContent className="p-6">
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <TimelineItemSkeleton key={i} />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={`bg-slate-800 border-slate-700 ${className}`}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  if (statusHistory.length === 0) {
    return (
      <Card className={`bg-slate-800 border-slate-700 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center">
            <Clock className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-200 mb-2">
              No Status History
            </h3>
            <p className="text-slate-400">
              Status updates will appear here as the shipment progresses.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Sort history by created_at descending (newest first)
  const sortedHistory = [...statusHistory].sort(
    (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  )

  // Show only latest status by default, all when expanded
  const displayedHistory = isExpanded ? sortedHistory : sortedHistory.slice(0, 1)
  const hiddenCount = sortedHistory.length - 1

  return (
    <Card className={`bg-card border-secondary ${className}`}>
      <CardContent className="p-4 sm:p-6">
        <div className="space-y-4 sm:space-y-6">
          {/* Header with expand/collapse functionality */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-orange-500" />
              <h3 className="text-lg font-medium text-slate-200">Status History</h3>
              <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                {statusHistory.length} updates
              </Badge>
            </div>

            {/* Toggle button - only show if more than 1 update */}
            {statusHistory.length > 1 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-slate-400 hover:text-slate-200 hover:bg-slate-700/50 transition-colors p-2 h-auto"
              >
                <div className="flex items-center space-x-2">
                  {isExpanded ? (
                    <>
                      <EyeOff className="h-4 w-4" />
                      <span className="text-xs hidden sm:inline">Show Less</span>
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4" />
                      <span className="text-xs hidden sm:inline">
                        Show All ({hiddenCount} more)
                      </span>
                      <span className="text-xs sm:hidden">
                        +{hiddenCount}
                      </span>
                    </>
                  )}
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </Button>
            )}
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-slate-600" />

            <div className="space-y-4 sm:space-y-6">
              {displayedHistory.map((item, index) => (
                <TimelineItem
                  key={item.id}
                  item={item}
                  isLatest={index === 0 && !isExpanded ? true : sortedHistory[0].id === item.id}
                />
              ))}
            </div>

            {/* Collapsed state hint */}
            {!isExpanded && hiddenCount > 0 && (
              <div className="relative flex items-center justify-center mt-4">
                <div className="absolute left-6 w-0.5 h-8 bg-gradient-to-b from-slate-600 to-transparent" />
                <div className="bg-slate-800 px-4 py-2 rounded-lg border border-slate-700 ml-12">
                  <div className="flex items-center space-x-2 text-sm text-slate-400">
                    <Clock className="h-4 w-4" />
                    <span>{hiddenCount} more status updates</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={() => setIsExpanded(true)}
                      className="text-orange-400 hover:text-orange-300 p-0 h-auto font-normal underline"
                    >
                      View All
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface TimelineItemProps {
  item: StatusHistory
  isLatest?: boolean
}

function TimelineItem({ item, isLatest = false }: TimelineItemProps) {
  const statusConfig = SHIPMENT_STATUSES.find(s => s.value === item.status_to) || {
    label: item.status_to,
    variant: 'default' as const,
    color: '#0ea5e9',
  }

  const createdAt = parseISO(item.created_at)
  const timeAgo = formatDistanceToNow(createdAt, { addSuffix: true })

  return (
    <div className="relative flex space-x-4">
      {/* Timeline dot */}
      <div className="relative flex items-center justify-center">
        <div
          className={`w-3 h-3 rounded-full border-2 ${
            isLatest
              ? 'animate-pulse ring-2 ring-offset-2 ring-offset-slate-800'
              : ''
          }`}
          style={{
            backgroundColor: statusConfig.color,
            borderColor: statusConfig.color,
            ringColor: isLatest ? statusConfig.color + '40' : undefined,
          }}
        />
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0 pb-6">
        <div className={`rounded-lg border p-4 ${
          isLatest
            ? 'bg-slate-700/50 border-slate-600'
            : 'bg-slate-800/50 border-slate-700'
        }`}>
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 mb-3">
            <div className="flex items-center space-x-3">
              <StatusBadge status={item.status_to as ShipmentStatus} size="default" showIcon />
              {isLatest && (
                <Badge
                  variant="secondary"
                  className="bg-green-500/20 text-green-300 border-green-400 text-xs"
                >
                  Latest
                </Badge>
              )}
            </div>
            
            <div className="flex items-center space-x-2 text-xs text-slate-400">
              <Clock className="h-3 w-3" />
              <span>{timeAgo}</span>
            </div>
          </div>

          {/* Status transition */}
          {item.status_from && item.status_from !== item.status_to && (
            <div className="flex items-center space-x-2 mb-3 text-sm text-slate-300">
              <span>From:</span>
              <StatusBadge status={item.status_from as ShipmentStatus} size="sm" />
              <span className="text-slate-500">→</span>
              <span>To:</span>
              <StatusBadge status={item.status_to as ShipmentStatus} size="sm" />
            </div>
          )}

          {/* Location */}
          {item.location && (
            <div className="flex items-start space-x-2 mb-2">
              <MapPin className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-sm text-slate-200">{item.location}</p>
                {item.latitude && item.longitude && (
                  <p className="text-xs text-slate-400 font-mono mt-1">
                    {item.latitude.toFixed(6)}, {item.longitude.toFixed(6)}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Notes */}
          {item.notes && (
            <div className="flex items-start space-x-2 mb-2">
              <FileText className="h-4 w-4 text-slate-400 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-slate-300">{item.notes}</p>
            </div>
          )}

          {/* Updated by */}
          <div className="flex items-center justify-between pt-2 border-t border-slate-600">
            <div className="flex items-center space-x-2">
              <User className="h-3 w-3 text-slate-400" />
              <span className="text-xs text-slate-400">
                {item.updated_by_profile?.full_name ||
                  item.updated_by_profile?.email ||
                  'System'}
              </span>
            </div>
            <div className="text-xs text-slate-500">
              {createdAt.toLocaleString()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Skeleton loader for timeline items
function TimelineItemSkeleton() {
  return (
    <div className="relative flex space-x-4">
      <div className="relative flex items-center justify-center">
        <div className="w-3 h-3 rounded-full bg-slate-600 animate-pulse" />
      </div>
      <div className="flex-1 min-w-0 pb-6">
        <div className="rounded-lg border border-slate-700 p-4 bg-slate-800/50">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="h-6 bg-slate-600 rounded animate-pulse w-32" />
              <div className="h-4 bg-slate-600 rounded animate-pulse w-20" />
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-slate-700 rounded animate-pulse w-3/4" />
              <div className="h-4 bg-slate-700 rounded animate-pulse w-1/2" />
            </div>
            <div className="flex items-center justify-between pt-2 border-t border-slate-600">
              <div className="h-3 bg-slate-700 rounded animate-pulse w-24" />
              <div className="h-3 bg-slate-700 rounded animate-pulse w-32" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Compact timeline for smaller spaces
interface CompactTimelineProps {
  statusHistory: StatusHistory[]
  maxItems?: number
  className?: string
}

export function CompactTimeline({
  statusHistory = [],
  maxItems = 5,
  className = '',
}: CompactTimelineProps) {
  const sortedHistory = [...statusHistory]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, maxItems)

  if (statusHistory.length === 0) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <p className="text-sm text-slate-400">No status history available</p>
      </div>
    )
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {sortedHistory.map((item, index) => (
        <div key={item.id} className="flex items-center space-x-3 py-2">
          <div
            className="w-2 h-2 rounded-full flex-shrink-0"
            style={{
              backgroundColor: SHIPMENT_STATUSES.find(s => s.value === item.status_to)?.color || '#0ea5e9'
            }}
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <StatusBadge status={item.status_to as ShipmentStatus} size="sm" />
              <span className="text-xs text-slate-400">
                {formatDistanceToNow(parseISO(item.created_at), { addSuffix: true })}
              </span>
            </div>
            {item.location && (
              <p className="text-xs text-slate-400 mt-1 truncate">{item.location}</p>
            )}
          </div>
        </div>
      ))}
      
      {statusHistory.length > maxItems && (
        <div className="text-center pt-2">
          <span className="text-xs text-slate-500">
            +{statusHistory.length - maxItems} more updates
          </span>
        </div>
      )}
    </div>
  )
}