/**
 * Document Download Utilities
 * 
 * Handles downloading documents from Supabase Storage
 */

import { createClient } from '@/lib/supabase/client'

export interface DownloadError {
  message: string
  code?: string
}

export interface DownloadResult {
  success: boolean
  error?: DownloadError
}

/**
 * Download a document from Supabase Storage
 * @param filePath - The file path in storage (e.g., "documents/EX-SEA-BKK-250911-001/invoice_cif/invoice.pdf")
 * @param fileName - Optional custom filename for download
 * @param bucket - Optional bucket name (defaults to 'documents')
 * @returns Promise<DownloadResult>
 */
export async function downloadDocument(filePath: string, fileName?: string, bucket?: string): Promise<DownloadResult> {
  try {
    const supabase = createClient()

    // Determine bucket: if not specified, try to infer from file path or default to 'documents'
    const bucketName = bucket || (filePath.includes('shipment-documents/') ? 'shipment-documents' : 'documents')

    // Get the file from Supabase Storage
    const { data: fileData, error: downloadError } = await supabase.storage
      .from(bucketName)
      .download(filePath)
    
    if (downloadError) {
      console.error('Error downloading document:', downloadError)
      return {
        success: false,
        error: {
          message: `Failed to download document: ${downloadError.message}`,
          code: downloadError.name
        }
      }
    }

    if (!fileData) {
      return {
        success: false,
        error: {
          message: 'No file data received'
        }
      }
    }

    // Create blob URL for download
    const blob = new Blob([fileData], { type: fileData.type || 'application/octet-stream' })
    const url = URL.createObjectURL(blob)
    
    // Determine filename
    const downloadFileName = fileName || filePath.split('/').pop() || 'document'
    
    // Create temporary link element and trigger download
    const link = document.createElement('a')
    link.href = url
    link.download = downloadFileName
    document.body.appendChild(link)
    link.click()
    
    // Cleanup
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    return { success: true }
    
  } catch (error) {
    console.error('Unexpected error during download:', error)
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unexpected error occurred'
      }
    }
  }
}

/**
 * Get a temporary download URL for a document (useful for viewing)
 * @param filePath - The file path in storage
 * @param expiresIn - Expiration time in seconds (default: 3600 = 1 hour)
 * @param bucket - Optional bucket name (defaults to 'documents')
 * @returns Promise with signed URL or error
 */
export async function getDocumentDownloadUrl(filePath: string, expiresIn: number = 3600, bucket?: string) {
  try {
    const supabase = createClient()

    // Determine bucket: if not specified, try to infer from file path or default to 'documents'
    const bucketName = bucket || (filePath.includes('shipment-documents/') ? 'shipment-documents' : 'documents')

    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(filePath, expiresIn)
    
    if (error) {
      console.error('Error creating signed URL:', error)
      return {
        success: false,
        error: {
          message: `Failed to create download URL: ${error.message}`,
          code: error.name
        }
      }
    }
    
    return {
      success: true,
      url: data.signedUrl
    }
    
  } catch (error) {
    console.error('Unexpected error creating signed URL:', error)
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unexpected error occurred'
      }
    }
  }
}

/**
 * Download multiple documents as a ZIP file
 * @param documents - Array of documents with filePath, optional fileName, and optional bucket
 * @param zipFileName - Name for the ZIP file
 * @returns Promise<DownloadResult>
 */
export async function downloadDocumentsAsZip(
  documents: Array<{ filePath: string; fileName?: string; bucket?: string }>,
  zipFileName: string = 'documents.zip'
): Promise<DownloadResult> {
  try {
    // Check if JSZip is available (would need to be installed)
    // For now, we'll implement sequential downloads
    // In a production environment, you might want to install jszip
    
    let successCount = 0
    const errors: string[] = []
    
    for (const doc of documents) {
      const result = await downloadDocument(doc.filePath, doc.fileName, doc.bucket)
      if (result.success) {
        successCount++
      } else {
        errors.push(`${doc.filePath}: ${result.error?.message}`)
      }
    }
    
    if (successCount === 0) {
      return {
        success: false,
        error: {
          message: `Failed to download any documents: ${errors.join(', ')}`
        }
      }
    }
    
    if (errors.length > 0) {
      console.warn('Some documents failed to download:', errors)
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Unexpected error during bulk download:', error)
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Unexpected error occurred'
      }
    }
  }
}

/**
 * Check if a document exists in storage
 * @param filePath - The file path in storage
 * @param bucket - Optional bucket name (defaults to 'documents')
 * @returns Promise with existence status
 */
export async function checkDocumentExists(filePath: string, bucket?: string) {
  try {
    const supabase = createClient()

    // Determine bucket: if not specified, try to infer from file path or default to 'documents'
    const bucketName = bucket || (filePath.includes('shipment-documents/') ? 'shipment-documents' : 'documents')

    const { data, error } = await supabase.storage
      .from(bucketName)
      .list(filePath.substring(0, filePath.lastIndexOf('/')))
    
    if (error) {
      return {
        exists: false,
        error: error.message
      }
    }
    
    const fileName = filePath.split('/').pop()
    const fileExists = data?.some(file => file.name === fileName) || false
    
    return {
      exists: fileExists
    }
    
  } catch (error) {
    return {
      exists: false,
      error: error instanceof Error ? error.message : 'Unexpected error occurred'
    }
  }
}