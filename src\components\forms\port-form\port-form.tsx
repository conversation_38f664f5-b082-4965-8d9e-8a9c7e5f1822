'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, Info, MapPin, Navigation, Globe } from 'lucide-react'
import { portFormSchema, type PortForm } from '@/lib/validations/ports'
import {
  usePortValidation,
  usePortCategories,
  useCoordinateUtils,
} from '@/hooks/use-ports'
import type { Port } from '@/stores/port-store'
import { GPSCoordinateInput } from './gps-coordinate-input'

interface PortFormProps {
  port?: Port | null
  onSubmit: (data: PortForm) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  className?: string
}

export function PortForm({
  port,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: PortFormProps) {
  const [error, setError] = useState<string | null>(null)
  const { validateUniqueCode, validatePortCode, validateTimezone } =
    usePortValidation()
  const { portTypeOptions, countryOptions } = usePortCategories()
  const { parseCoordinatesFromString } = useCoordinateUtils()

  // Parse existing coordinates if editing
  const existingCoords = port?.gps_coordinates
    ? (() => {
        try {
          // Handle different PostGIS geography formats that Supabase might return
          const coordString = port.gps_coordinates.toString()

          // ST_AsText returns format: POINT(lng lat)
          const pointMatch = coordString.match(
            /POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
          )
          if (pointMatch) {
            return {
              lat: parseFloat(pointMatch[2]),
              lng: parseFloat(pointMatch[1]),
            }
          }

          // Handle WKT format with SRID: SRID=4326;POINT(lng lat)
          const sridMatch = coordString.match(
            /SRID=\d+;POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
          )
          if (sridMatch) {
            return {
              lat: parseFloat(sridMatch[2]),
              lng: parseFloat(sridMatch[1]),
            }
          }

          // Supabase might return as JSON: {"type":"Point","coordinates":[lng,lat]}
          const jsonMatch = coordString.match(/^\{.*\}$/)
          if (jsonMatch) {
            const parsed = JSON.parse(coordString)
            if (
              parsed.type === 'Point' &&
              parsed.coordinates &&
              parsed.coordinates.length === 2
            ) {
              return {
                lat: parsed.coordinates[1],
                lng: parsed.coordinates[0],
              }
            }
          }

          return null
        } catch {
          return null
        }
      })()
    : null

  const form = useForm<PortForm>({
    resolver: zodResolver(portFormSchema),
    defaultValues: {
      code: port?.code || '',
      name: port?.name || '',
      city: port?.city || '',
      country: port?.country || '',
      port_type: port?.port_type || 'origin',
      gps_coordinates: existingCoords || undefined,
      timezone: port?.timezone || '',
    },
  })

  const watchedCode = form.watch('code')
  const watchedTimezone = form.watch('timezone')
  const watchedCoordinates = form.watch('gps_coordinates')
  const watchedCountry = form.watch('country')

  // Real-time validation for unique code
  useEffect(() => {
    if (watchedCode) {
      const codeValidation = validatePortCode(watchedCode)
      if (!codeValidation.isValid) {
        form.setError('code', {
          type: 'manual',
          message: codeValidation.message,
        })
      } else {
        const uniqueValidation = validateUniqueCode(watchedCode, port?.id)
        if (!uniqueValidation.isValid) {
          form.setError('code', {
            type: 'manual',
            message: uniqueValidation.message,
          })
        } else {
          form.clearErrors('code')
        }
      }
    }
  }, [watchedCode, validatePortCode, validateUniqueCode, port?.id, form])

  // Real-time validation for timezone
  useEffect(() => {
    if (watchedTimezone) {
      const validation = validateTimezone(watchedTimezone)
      if (!validation.isValid) {
        form.setError('timezone', {
          type: 'manual',
          message: validation.message,
        })
      } else {
        form.clearErrors('timezone')
      }
    }
  }, [watchedTimezone, validateTimezone, form])

  // Auto-suggest timezone based on country
  useEffect(() => {
    if (watchedCountry && !watchedTimezone) {
      const timezoneMap: Record<string, string> = {
        Thailand: 'Asia/Bangkok',
        China: 'Asia/Shanghai',
        Japan: 'Asia/Tokyo',
        Singapore: 'Asia/Singapore',
        Malaysia: 'Asia/Kuala_Lumpur',
        'United States': 'America/New_York',
        'United Kingdom': 'Europe/London',
        Germany: 'Europe/Berlin',
        Australia: 'Australia/Sydney',
      }

      const suggestedTimezone = timezoneMap[watchedCountry]
      if (suggestedTimezone) {
        form.setValue('timezone', suggestedTimezone)
      }
    }
  }, [watchedCountry, watchedTimezone, form])

  const handleSubmit = async (data: PortForm) => {
    try {
      setError(null)

      // Transform coordinates to PostGIS format if provided
      const transformedData = { ...data }
      if (data.gps_coordinates) {
        transformedData.gps_coordinates = JSON.stringify(data.gps_coordinates)
      }

      await onSubmit(transformedData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save port')
    }
  }

  const handleCancel = () => {
    form.reset()
    setError(null)
    onCancel?.()
  }

  return (
    <div className={className}>
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
              <CardTitle className="text-white flex items-center gap-2">
                <MapPin className="h-5 w-5 text-orange-500" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="bg-slate-800 space-y-4 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Port Code */}
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-300">
                        Port Code *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="e.g., THBKK, CNSHA"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          onChange={e =>
                            field.onChange(e.target.value.toUpperCase())
                          }
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        Unique port code (2 letters + letters/numbers)
                      </FormDescription>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />

                {/* Port Name */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-300">
                        Port Name *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="e.g., Bangkok Port, Shanghai Port"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        />
                      </FormControl>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* City */}
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-300">City *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="e.g., Bangkok, Shanghai"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        />
                      </FormControl>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />

                {/* Country */}
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-300">
                        Country *
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="e.g., Thailand, China"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        />
                      </FormControl>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Port Type */}
                <FormField
                  control={form.control}
                  name="port_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-300">
                        Port Type *
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                            <SelectValue placeholder="Select port type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          {portTypeOptions.map(option => (
                            <SelectItem
                              key={option.value}
                              value={option.value}
                              className="text-slate-300 hover:bg-slate-700"
                            >
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />

                {/* Timezone */}
                <FormField
                  control={form.control}
                  name="timezone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-300">Timezone</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="e.g., Asia/Bangkok"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        Format: Continent/City (auto-suggested based on country)
                      </FormDescription>
                      <FormMessage className="text-red-400" />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* GPS Coordinates */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
              <CardTitle className="text-white flex items-center gap-2">
                <Navigation className="h-5 w-5 text-orange-500" />
                GPS Coordinates
              </CardTitle>
            </CardHeader>
            <CardContent className="bg-slate-800 space-y-4 pt-6">
              <FormField
                control={form.control}
                name="gps_coordinates"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-300">
                      Coordinates
                    </FormLabel>
                    <FormControl>
                      <GPSCoordinateInput
                        value={field.value}
                        onChange={field.onChange}
                        className="bg-slate-700 border-slate-600 text-white"
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Enter GPS coordinates for geographic search capabilities
                    </FormDescription>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />

              {watchedCoordinates && (
                <div className="bg-slate-700 p-4 rounded-lg border border-slate-600">
                  <h4 className="text-sm font-medium text-slate-300 mb-2 flex items-center gap-2">
                    <Globe className="h-4 w-4 text-orange-500" />
                    Coordinate Preview
                  </h4>
                  <div className="space-y-2 text-sm text-slate-400">
                    <div>
                      <span className="text-slate-300">Decimal:</span>{' '}
                      {watchedCoordinates.lat.toFixed(6)},{' '}
                      {watchedCoordinates.lng.toFixed(6)}
                    </div>
                    <div>
                      <span className="text-slate-300">PostGIS Point:</span>{' '}
                      POINT({watchedCoordinates.lng} {watchedCoordinates.lat})
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-orange-500 hover:bg-orange-600 text-white flex-1 sm:flex-none"
            >
              {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {port ? 'Update Port' : 'Create Port'}
            </Button>

            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white flex-1 sm:flex-none"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  )
}
