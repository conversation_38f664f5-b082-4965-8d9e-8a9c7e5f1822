/**
 * Template Preview Component Tests
 * Story 5.1: Document Template Management System - AC4
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import { TemplatePreview } from '../template-preview'
import type { DocumentTemplate } from '@/types/document-template'

// Mock the PDF generation libraries
vi.mock('react-to-print', () => ({
  useReactToPrint: vi.fn(() => vi.fn())
}))

vi.mock('jspdf', () => {
  const mockPDF = {
    addImage: vi.fn(),
    save: vi.fn()
  }
  return {
    default: vi.fn().mockImplementation(() => mockPDF)
  }
})

vi.mock('html2canvas', () => ({
  default: vi.fn(() => Promise.resolve({
    toDataURL: vi.fn().mockReturnValue('data:image/png;base64,mock'),
    height: 800,
    width: 600
  }))
}))

// Mock the sample data utilities
vi.mock('@/lib/utils/template-sample-data', () => ({
  generateSampleData: vi.fn(() => ({
    customer_name: 'Test Customer',
    shipment_number: 'TEST-001',
    total_amount_fob: 1000,
    currency: 'USD',
    products: [
      {
        product_name: 'Test Product',
        quantity: 10,
        unit: 'boxes',
        total_amount: 1000
      }
    ]
  })),
  replacePlaceholders: vi.fn((content, data) => content.replace(/{{customer_name}}/g, data.customer_name)),
  getAvailablePlaceholders: vi.fn(() => ['customer_name', 'shipment_number', 'total_amount_fob'])
}))

// Mock the validation utilities
vi.mock('@/lib/utils/template-validation', () => ({
  validateTemplatePlaceholders: vi.fn(() => ({
    isValid: true,
    errors: null,
    warnings: null
  }))
}))

describe('TemplatePreview Component', () => {
  const mockTemplate: Partial<DocumentTemplate> = {
    id: 'test-id',
    template_name: 'Test Template',
    document_type: 'invoice_fob',
    version: '1.0',
    template_content: '<h1>Invoice</h1><p>Customer: {{customer_name}}</p>',
    template_styles: 'body { font-family: Arial; }',
    page_size: 'A4',
    page_orientation: 'portrait',
    margin_top: 20,
    margin_bottom: 20,
    margin_left: 20,
    margin_right: 20,
    language: 'en',
    currency_format: 'USD',
    is_active: true,
    is_default: false
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Component Rendering', () => {
    it('should render template preview with basic controls', () => {
      render(<TemplatePreview template={mockTemplate} />)

      expect(screen.getByText('Template Preview')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /validate/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /print/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /pdf/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /fullscreen/i })).toBeInTheDocument()
    })

    it('should render template information', () => {
      render(<TemplatePreview template={mockTemplate} />)

      expect(screen.getByText('Test Template - invoice_fob')).toBeInTheDocument()
      expect(screen.getByText(/page: a4 \(portrait\)/i)).toBeInTheDocument()
      expect(screen.getByText(/language: en/i)).toBeInTheDocument()
      expect(screen.getByText(/currency: usd/i)).toBeInTheDocument()
    })

    it('should render processed template content', () => {
      render(<TemplatePreview template={mockTemplate} />)

      // Should show the processed content with placeholders replaced
      expect(screen.getByRole('heading', { level: 1, name: /invoice/i })).toBeInTheDocument()
    })

    it('should show placeholder when no content provided', () => {
      const emptyTemplate = { ...mockTemplate, template_content: '' }
      render(<TemplatePreview template={emptyTemplate} />)

      expect(screen.getByText(/no template content to preview/i)).toBeInTheDocument()
      expect(screen.getByText(/add content in the content tab/i)).toBeInTheDocument()
    })

    it('should display template validation status', () => {
      render(<TemplatePreview template={mockTemplate} />)

      // Should show validation badge
      expect(screen.getByRole('button', { name: /validate/i })).toBeInTheDocument()
    })
  })

  describe('Validation Functionality', () => {
    it('should validate template on validate button click', async () => {
      const { validateTemplatePlaceholders } = await import('@/lib/utils/template-validation')
      
      render(<TemplatePreview template={mockTemplate} />)

      const validateButton = screen.getByRole('button', { name: /validate/i })
      fireEvent.click(validateButton)

      await waitFor(() => {
        expect(validateTemplatePlaceholders).toHaveBeenCalledWith(mockTemplate.template_content)
      })
    })

    it('should show validation errors', () => {
      const templateWithErrors = {
        ...mockTemplate,
        template_name: '', // Missing required field
        document_type: undefined
      }

      render(<TemplatePreview template={templateWithErrors} />)

      const validateButton = screen.getByRole('button', { name: /validate/i })
      fireEvent.click(validateButton)

      // Should show validation errors after clicking validate
      // (In real implementation, this would show error alerts)
    })

    it('should show validation warnings for unknown placeholders', () => {
      const templateWithUnknownPlaceholders = {
        ...mockTemplate,
        template_content: '<p>{{unknown_placeholder}}</p>'
      }

      render(<TemplatePreview template={templateWithUnknownPlaceholders} />)

      const validateButton = screen.getByRole('button', { name: /validate/i })
      fireEvent.click(validateButton)

      // Should show warnings for unknown placeholders
    })
  })

  describe('PDF Generation', () => {
    it('should trigger PDF generation on PDF button click', async () => {
      const html2canvas = (await import('html2canvas')).default
      
      render(<TemplatePreview template={mockTemplate} />)

      const pdfButton = screen.getByRole('button', { name: /pdf/i })
      
      await act(async () => {
        fireEvent.click(pdfButton)
      })

      await waitFor(() => {
        expect(html2canvas).toHaveBeenCalled()
      })
    })

    it('should show loading state during PDF generation', async () => {
      // Mock a delayed PDF generation
      const html2canvas = (await import('html2canvas')).default
      vi.mocked(html2canvas).mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            toDataURL: vi.fn().mockReturnValue('data:image/png;base64,mock'),
            height: 800,
            width: 600
          }), 100)
        )
      )

      render(<TemplatePreview template={mockTemplate} />)

      const pdfButton = screen.getByRole('button', { name: /pdf/i })
      
      await act(async () => {
        fireEvent.click(pdfButton)
      })

      // Should show loading spinner during generation
      expect(screen.getByRole('button', { name: /pdf/i })).toBeDisabled()
      
      // Wait for completion
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /pdf/i })).not.toBeDisabled()
      }, { timeout: 2000 })
    })

    it('should handle different page sizes for PDF generation', async () => {
      const templateA3 = {
        ...mockTemplate,
        page_size: 'A3' as const,
        page_orientation: 'landscape' as const
      }

      render(<TemplatePreview template={templateA3} />)

      const pdfButton = screen.getByRole('button', { name: /pdf/i })
      fireEvent.click(pdfButton)

      // PDF should be generated with A3 landscape settings
    })
  })

  describe('Print Functionality', () => {
    it('should trigger print on print button click', () => {
      render(<TemplatePreview template={mockTemplate} />)

      const printButton = screen.getByRole('button', { name: /print/i })
      fireEvent.click(printButton)

      // Since useReactToPrint is mocked, this should not throw an error
      expect(screen.getByRole('button', { name: /print/i })).toBeInTheDocument()
    })
  })

  describe('Fullscreen Mode', () => {
    it('should toggle fullscreen mode', () => {
      render(<TemplatePreview template={mockTemplate} />)

      const fullscreenButton = screen.getByRole('button', { name: /fullscreen/i })
      fireEvent.click(fullscreenButton)

      expect(screen.getByRole('button', { name: /minimize/i })).toBeInTheDocument()
    })
  })

  describe('Sample Data Integration', () => {
    it('should use sample data to populate placeholders', async () => {
      const { replacePlaceholders } = await import('@/lib/utils/template-sample-data')
      
      render(<TemplatePreview template={mockTemplate} />)

      expect(replacePlaceholders).toHaveBeenCalledWith(
        mockTemplate.template_content,
        expect.objectContaining({
          customer_name: 'Test Customer'
        }),
        false
      )
    })

    it('should use expanded layout when expanded prop is true', async () => {
      const { replacePlaceholders } = await import('@/lib/utils/template-sample-data')
      
      render(<TemplatePreview template={mockTemplate} expanded={true} />)

      expect(replacePlaceholders).toHaveBeenCalledWith(
        mockTemplate.template_content,
        expect.objectContaining({
          customer_name: 'Test Customer'
        }),
        true
      )

      // Check that the expanded layout is used (no Card component, direct content)
      expect(screen.queryByRole('heading', { level: 3 })).toBeInTheDocument()
      expect(screen.queryByTestId('template-card')).not.toBeInTheDocument()
      
      // Check that the expanded layout styling is applied
      const previewContainer = document.querySelector('.template-preview')
      expect(previewContainer).toHaveStyle({ minWidth: '850px' })
    })

    it('should display preview information', () => {
      render(<TemplatePreview template={mockTemplate} />)

      expect(screen.getByText(/preview information/i)).toBeInTheDocument()
      expect(screen.getByText(/sample data injected/i)).toBeInTheDocument()
      expect(screen.getByText(/all placeholders populated/i)).toBeInTheDocument()
      expect(screen.getByText(/styling applied/i)).toBeInTheDocument()
      expect(screen.getByText(/ready for pdf generation/i)).toBeInTheDocument()
    })
  })

  describe('Styling and Layout', () => {
    it('should apply custom template styles', () => {
      render(<TemplatePreview template={mockTemplate} />)

      // Should inject custom styles into the document
      const styleElement = document.querySelector('style')
      expect(styleElement?.innerHTML).toContain(mockTemplate.template_styles)
    })

    it('should apply project color scheme', () => {
      render(<TemplatePreview template={mockTemplate} />)

      // Should include project color scheme in styles
      const styleElement = document.querySelector('style')
      expect(styleElement?.innerHTML).toContain('#1e293b') // Dark blue
      expect(styleElement?.innerHTML).toContain('#f97316') // Orange accent
    })

    it('should apply margin settings to preview', () => {
      render(<TemplatePreview template={mockTemplate} />)

      // Preview container should have margin styles applied
      const previewContainer = document.querySelector('.template-preview')
      expect(previewContainer).toHaveStyle({
        padding: '20px 20px 20px 20px'
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle missing template data gracefully', () => {
      render(<TemplatePreview template={{}} />)

      expect(screen.getByText(/untitled template/i)).toBeInTheDocument()
      expect(screen.getByText(/unknown type/i)).toBeInTheDocument()
      expect(screen.getByText(/no template content to preview/i)).toBeInTheDocument()
    })

    it('should handle PDF generation errors gracefully', async () => {
      const html2canvas = (await import('html2canvas')).default
      vi.mocked(html2canvas).mockRejectedValue(new Error('Canvas error'))

      render(<TemplatePreview template={mockTemplate} />)

      const pdfButton = screen.getByRole('button', { name: /pdf/i })
      fireEvent.click(pdfButton)

      // Should handle error without crashing
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /pdf/i })).not.toBeDisabled()
      })
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<TemplatePreview template={mockTemplate} />)

      expect(screen.getByRole('button', { name: /validate/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /print/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /pdf/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /fullscreen/i })).toBeInTheDocument()
    })

    it('should handle keyboard navigation', () => {
      render(<TemplatePreview template={mockTemplate} />)

      const validateButton = screen.getByRole('button', { name: /validate/i })
      validateButton.focus()
      expect(document.activeElement).toBe(validateButton)
    })

    it('should provide meaningful feedback for screen readers', () => {
      render(<TemplatePreview template={mockTemplate} />)

      // Should have descriptive text for screen readers
      expect(screen.getByText(/sample data injected with realistic export scenarios/i)).toBeInTheDocument()
      expect(screen.getByText(/preview information/i)).toBeInTheDocument()
    })
  })
})