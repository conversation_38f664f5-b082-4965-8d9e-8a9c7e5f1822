'use client'

import React, { useState, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { isStaff } from '@/lib/supabase/auth'
import type { 
  ContainerConfirmationRequest,
  ContainerConfirmationStatus,
  ContainerConfirmationOverride,
  ContainerPermissions
} from '@/types/container'

// Hook for container confirmation operations
export function useContainerConfirmation() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const supabase = React.useMemo(() => createClient(), [])

  // Confirm container and/or seal numbers
  const confirmNumbers = useCallback(
    async (containerId: string, request: ContainerConfirmationRequest): Promise<boolean> => {
      setLoading(true)
      setError(null)

      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (!session) {
          throw new Error('No active session')
        }

        const response = await fetch(`/api/containers/${containerId}/confirm-numbers`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify(request)
        })

        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.details || result.error || 'Confirmation failed')
        }

        return true
      } catch (error: any) {
        console.error('Container confirmation error:', error)
        setError(error.message || 'Failed to confirm container numbers')
        return false
      } finally {
        setLoading(false)
      }
    },
    [supabase]
  )

  // Get confirmation status
  const getConfirmationStatus = useCallback(
    async (containerId: string): Promise<ContainerConfirmationStatus | null> => {
      setLoading(true)
      setError(null)

      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (!session) {
          throw new Error('No active session')
        }

        const response = await fetch(`/api/containers/${containerId}/confirmation-status`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${session.access_token}`
          }
        })

        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.details || result.error || 'Failed to get confirmation status')
        }

        return result.confirmation_status
      } catch (error: any) {
        console.error('Get confirmation status error:', error)
        setError(error.message || 'Failed to get confirmation status')
        return null
      } finally {
        setLoading(false)
      }
    },
    [supabase]
  )

  // Update confirmed numbers (override)
  const updateConfirmedNumbers = useCallback(
    async (containerId: string, override: ContainerConfirmationOverride): Promise<boolean> => {
      setLoading(true)
      setError(null)

      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (!session) {
          throw new Error('No active session')
        }

        const response = await fetch(`/api/containers/${containerId}/update-confirmed`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify(override)
        })

        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.details || result.error || 'Update failed')
        }

        return true
      } catch (error: any) {
        console.error('Container update error:', error)
        setError(error.message || 'Failed to update container numbers')
        return false
      } finally {
        setLoading(false)
      }
    },
    [supabase]
  )

  // Check user permissions for confirmation operations
  const checkPermissions = useCallback(
    async (): Promise<ContainerPermissions> => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (!session) {
          return {
            canConfirm: false,
            canEditAfterConfirmation: false,
            canEditBeforeConfirmation: false
          }
        }

        // Get user profile to check role
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('user_id', session.user.id)
          .single()

        if (error || !profile) {
          return {
            canConfirm: false,
            canEditAfterConfirmation: false,
            canEditBeforeConfirmation: false
          }
        }

        const isStaffUser = isStaff(profile.role)

        return {
          canConfirm: isStaffUser,
          canEditAfterConfirmation: isStaffUser,
          canEditBeforeConfirmation: true // All authenticated users can edit before confirmation
        }
      } catch (error) {
        console.error('Permission check error:', error)
        return {
          canConfirm: false,
          canEditAfterConfirmation: false,
          canEditBeforeConfirmation: false
        }
      }
    },
    [supabase]
  )

  // Helper function to check if user can edit a specific container field
  const canEditField = useCallback(
    (isConfirmed: boolean, permissions: ContainerPermissions): boolean => {
      if (!isConfirmed) {
        return permissions.canEditBeforeConfirmation
      } else {
        return permissions.canEditAfterConfirmation
      }
    },
    []
  )

  // Helper function to get confirmation display text
  const getConfirmationDisplayText = useCallback(
    (
      confirmed: boolean,
      confirmedBy: string | null,
      confirmedAt: string | null,
      confirmerName?: string
    ): string => {
      if (!confirmed) {
        return 'Not confirmed'
      }

      const date = confirmedAt ? new Date(confirmedAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }) : ''

      if (confirmerName) {
        return `Confirmed by ${confirmerName}${date ? ` on ${date}` : ''}`
      }

      return `Confirmed${date ? ` on ${date}` : ''}`
    },
    []
  )

  return {
    confirmNumbers,
    getConfirmationStatus,
    updateConfirmedNumbers,
    checkPermissions,
    canEditField,
    getConfirmationDisplayText,
    loading,
    error,
    clearError: () => setError(null)
  }
}

// Hook for real-time confirmation status updates
export function useContainerConfirmationStatus(containerId: string) {
  const [status, setStatus] = useState<ContainerConfirmationStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = React.useMemo(() => createClient(), [])

  // Fetch initial status - create stable function reference
  const fetchStatus = useCallback(async () => {
    if (!containerId) return
    
    setLoading(true)
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        throw new Error('No active session')
      }

      const response = await fetch(`/api/containers/${containerId}/confirmation-status`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.details || result.error || 'Failed to get confirmation status')
      }

      setStatus(result.confirmation_status)
    } catch (error: any) {
      console.error('Get confirmation status error:', error)
      setStatus(null)
    } finally {
      setLoading(false)
    }
  }, [containerId, supabase])

  // Subscribe to real-time updates
  React.useEffect(() => {
    fetchStatus()

    // Subscribe to container changes
    const channel = supabase
      .channel(`container_confirmation_${containerId}`)
      .on(
        'postgres_changes',
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'containers', 
          filter: `id=eq.${containerId}` 
        },
        () => {
          fetchStatus() // Refetch status when container is updated
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [containerId, fetchStatus, supabase])

  return {
    status,
    loading,
    refetch: fetchStatus
  }
}