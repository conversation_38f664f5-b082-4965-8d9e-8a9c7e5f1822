<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - DYY Driver App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .container {
            text-align: center;
            max-width: 400px;
            padding: 2rem;
        }
        
        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: #f97316;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
        }
        
        h1 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: white;
        }
        
        p {
            color: #94a3b8;
            margin-bottom: 2rem;
            line-height: 1.5;
        }
        
        .status {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid #334155;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.online {
            background: #22c55e;
            animation: none;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        button {
            background: #f97316;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            max-width: 200px;
        }
        
        button:hover {
            background: #ea580c;
        }
        
        button:disabled {
            background: #64748b;
            cursor: not-allowed;
        }
        
        .features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        
        .features h2 {
            font-size: 1.125rem;
            margin-bottom: 1rem;
            color: #e2e8f0;
        }
        
        .features ul {
            list-style: none;
            color: #94a3b8;
            text-align: left;
        }
        
        .features li {
            padding: 0.5rem 0;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .features li::before {
            content: "•";
            color: #f97316;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 1rem;
            }
            
            .icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
            
            h1 {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">DYY</div>
        
        <h1>You're Offline</h1>
        <p>Don't worry! The DYY Driver App works offline too. Some features may be limited until you reconnect.</p>
        
        <div class="status">
            <div class="status-indicator" id="statusIndicator"></div>
            <span id="statusText">Connection Status: Offline</span>
        </div>
        
        <button onclick="tryReload()" id="reloadBtn">
            Try Again
        </button>
        
        <div class="features">
            <h2>Available Offline</h2>
            <ul>
                <li>View cached assignments</li>
                <li>Access recent shipment details</li>
                <li>Use previously loaded maps</li>
                <li>Take photos for later sync</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const reloadBtn = document.getElementById('reloadBtn');
            
            if (navigator.onLine) {
                indicator.className = 'status-indicator online';
                statusText.textContent = 'Connection Status: Online';
                reloadBtn.textContent = 'Return to App';
                reloadBtn.disabled = false;
            } else {
                indicator.className = 'status-indicator';
                statusText.textContent = 'Connection Status: Offline';
                reloadBtn.textContent = 'Try Again';
                reloadBtn.disabled = false;
            }
        }
        
        // Try to reload the app
        function tryReload() {
            if (navigator.onLine) {
                window.location.href = '/driver/dashboard';
            } else {
                // Flash the button to show we tried
                const btn = document.getElementById('reloadBtn');
                btn.style.background = '#ef4444';
                setTimeout(() => {
                    btn.style.background = '#64748b';
                }, 200);
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Auto-retry when online
        window.addEventListener('online', () => {
            setTimeout(() => {
                if (navigator.onLine) {
                    window.location.href = '/driver/dashboard';
                }
            }, 1000);
        });
        
        // Service worker registration for offline page
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered from offline page:', registration);
                })
                .catch(error => {
                    console.log('SW registration failed from offline page:', error);
                });
        }
    </script>
</body>
</html>