'use client'

import { useEffect, useCallback } from 'react'
import {
  usePortStore,
  usePorts,
  usePortsLoading,
  usePortsError,
  useNearbyPorts,
  useGeographicSearch,
  usePortActions,
  type Port,
  type PortInsert,
  type PortUpdate,
  type PortWithDistance,
} from '@/stores/port-store'
import type {
  PortFilter,
  GeographicSearch,
  Coordinates,
} from '@/lib/validations/ports'

export function usePortsData() {
  const ports = usePorts()
  const loading = usePortsLoading()
  const error = usePortsError()
  const {
    fetchPorts,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    clearError,
    subscribeToPorts,
  } = usePortActions()

  // Pagination and filtering state
  const currentPage = usePortStore(state => state.currentPage)
  const pageSize = usePortStore(state => state.pageSize)
  const totalCount = usePortStore(state => state.totalCount)
  const filter = usePortStore(state => state.filter)
  const searchTerm = usePortStore(state => state.searchTerm)
  const sortBy = usePortStore(state => state.sortBy)
  const sortOrder = usePortStore(state => state.sortOrder)

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / pageSize)
  const hasNextPage = currentPage < totalPages
  const hasPreviousPage = currentPage > 1

  // Load ports on mount and setup real-time subscription
  useEffect(() => {
    fetchPorts()

    // Setup real-time subscription
    const unsubscribe = subscribeToPorts()

    return () => {
      unsubscribe()
    }
  }, [fetchPorts, subscribeToPorts])

  // Filtered ports for dropdown/selection purposes
  const activePorts = ports.filter(port => port.is_active)
  const portsByType = ports.reduce(
    (acc, port) => {
      const type = port.port_type
      if (!acc[type]) acc[type] = []
      acc[type].push(port)
      return acc
    },
    {} as Record<string, typeof ports>
  )

  const portsByCountry = ports.reduce(
    (acc, port) => {
      const country = port.country
      if (!acc[country]) acc[country] = []
      acc[country].push(port)
      return acc
    },
    {} as Record<string, typeof ports>
  )

  // Unique countries for filter dropdown
  const countries = Array.from(
    new Set(ports.map(p => p.country).filter(Boolean))
  ).sort()

  return {
    // Data
    ports,
    activePorts,
    portsByType,
    portsByCountry,
    countries,
    loading,
    error,

    // Pagination
    currentPage,
    pageSize,
    totalCount,
    totalPages,
    hasNextPage,
    hasPreviousPage,

    // Filters and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Actions
    fetchPorts,
    setFilter,
    setSearchTerm,
    setSorting,
    setPage: (page: number) => setPage(Math.max(1, Math.min(page, totalPages))),
    nextPage: () => hasNextPage && setPage(currentPage + 1),
    previousPage: () => hasPreviousPage && setPage(currentPage - 1),
    clearError,

    // Utility functions
    refreshPorts: fetchPorts,
    getPortById: (id: string) => ports.find(port => port.id === id),
    getPortByCode: (code: string) =>
      ports.find(port => port.code === code.toUpperCase()),
  }
}

export function usePortCRUD() {
  const { createPort, updatePort, deletePort, deletePorts, fetchPortById } =
    usePortActions()

  const isCreating = usePortStore(state => state.isCreating)
  const isUpdating = usePortStore(state => state.isUpdating)
  const isDeleting = usePortStore(state => state.isDeleting)

  const handleCreate = useCallback(
    async (portData: PortInsert): Promise<Port> => {
      try {
        const result = await createPort(portData)
        return result
      } catch (error) {
        console.error('Failed to create port:', error)
        throw error
      }
    },
    [createPort]
  )

  const handleUpdate = useCallback(
    async (id: string, updates: Partial<PortUpdate>): Promise<Port> => {
      try {
        const result = await updatePort(id, updates)
        return result
      } catch (error) {
        console.error('Failed to update port:', error)
        throw error
      }
    },
    [updatePort]
  )

  const handleDelete = useCallback(
    async (id: string): Promise<void> => {
      try {
        await deletePort(id)
      } catch (error) {
        console.error('Failed to delete port:', error)
        throw error
      }
    },
    [deletePort]
  )

  const handleBulkDelete = useCallback(
    async (ids: string[]): Promise<void> => {
      try {
        await deletePorts(ids)
      } catch (error) {
        console.error('Failed to delete ports:', error)
        throw error
      }
    },
    [deletePorts]
  )

  const handleFetchById = useCallback(
    async (id: string) => {
      try {
        return await fetchPortById(id)
      } catch (error) {
        console.error('Failed to fetch port:', error)
        throw error
      }
    },
    [fetchPortById]
  )

  return {
    // Loading states
    isCreating,
    isUpdating,
    isDeleting,

    // CRUD operations
    createPort: handleCreate,
    updatePort: handleUpdate,
    deletePort: handleDelete,
    bulkDeletePorts: handleBulkDelete,
    fetchPortById: handleFetchById,

    // Utility
    isLoading: isCreating || isUpdating || isDeleting,
  }
}

export function usePortSelection() {
  const selectedPorts = usePortStore(state => state.selectedPorts)
  const { selectPort, deselectPort, clearSelection } = usePortActions()

  const selectAllPorts = usePortStore(state => state.selectAllPorts)
  const ports = usePorts()

  const selectedIds = Array.from(selectedPorts)
  const selectedCount = selectedPorts.size
  const isSelected = (id: string) => selectedPorts.has(id)
  const isAllSelected = ports.length > 0 && selectedPorts.size === ports.length
  const isPartiallySelected =
    selectedPorts.size > 0 && selectedPorts.size < ports.length

  const togglePort = useCallback(
    (id: string) => {
      if (selectedPorts.has(id)) {
        deselectPort(id)
      } else {
        selectPort(id)
      }
    },
    [selectedPorts, selectPort, deselectPort]
  )

  const toggleAll = useCallback(() => {
    if (isAllSelected) {
      clearSelection()
    } else {
      selectAllPorts()
    }
  }, [isAllSelected, clearSelection, selectAllPorts])

  return {
    selectedPorts: selectedIds,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,
    selectPort,
    deselectPort,
    togglePort,
    selectAllPorts,
    toggleAll,
    clearSelection,
  }
}

// Geographic search capabilities
export function usePortGeographicSearch() {
  const nearbyPorts = useNearbyPorts()
  const geographicSearch = useGeographicSearch()
  const geographicLoading = usePortStore(state => state.geographicLoading)
  const {
    searchPortsWithinRadius,
    getPortsByCountry,
    calculatePortDistance,
    clearGeographicSearch,
  } = usePortActions()

  const handleRadiusSearch = useCallback(
    async (search: GeographicSearch): Promise<PortWithDistance[]> => {
      try {
        return await searchPortsWithinRadius(search)
      } catch (error) {
        console.error('Failed to search ports within radius:', error)
        throw error
      }
    },
    [searchPortsWithinRadius]
  )

  const handleCountrySearch = useCallback(
    async (
      country: string,
      referenceCoords?: Coordinates
    ): Promise<PortWithDistance[]> => {
      try {
        return await getPortsByCountry(country, referenceCoords)
      } catch (error) {
        console.error('Failed to search ports by country:', error)
        throw error
      }
    },
    [getPortsByCountry]
  )

  const handleDistanceCalculation = useCallback(
    async (port1Id: string, port2Id: string): Promise<number> => {
      try {
        return await calculatePortDistance(port1Id, port2Id)
      } catch (error) {
        console.error('Failed to calculate port distance:', error)
        throw error
      }
    },
    [calculatePortDistance]
  )

  // Helper to parse PostGIS coordinates
  const parseCoordinates = useCallback(
    (gpsCoordinates: string | object | null): Coordinates | null => {
      if (!gpsCoordinates) return null

      try {
        // Handle different PostGIS geography formats that Supabase might return
        const coordString =
          typeof gpsCoordinates === 'object'
            ? JSON.stringify(gpsCoordinates)
            : gpsCoordinates.toString()

        // Check if it's binary WKB format (starts with hex digits)
        if (/^[0-9A-Fa-f]+$/.test(coordString) && coordString.length > 20) {
          // This is likely PostGIS binary format - we need to decode it
          // For now, return null and log that we need to handle this case
          console.log(
            'Binary PostGIS coordinate detected, needs conversion:',
            coordString.substring(0, 20) + '...'
          )
          return null
        }

        // ST_AsText returns format: POINT(lng lat)
        const pointMatch = coordString.match(
          /POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
        )
        if (pointMatch) {
          return {
            lng: parseFloat(pointMatch[1]),
            lat: parseFloat(pointMatch[2]),
          }
        }

        // Handle WKT format with SRID: SRID=4326;POINT(lng lat)
        const sridMatch = coordString.match(
          /SRID=\d+;POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
        )
        if (sridMatch) {
          return {
            lng: parseFloat(sridMatch[1]),
            lat: parseFloat(sridMatch[2]),
          }
        }

        // Supabase might return as JSON: {"type":"Point","coordinates":[lng,lat]}
        const jsonMatch = coordString.match(/^\{.*\}$/)
        if (jsonMatch) {
          const parsed = JSON.parse(coordString)
          if (
            parsed.type === 'Point' &&
            parsed.coordinates &&
            parsed.coordinates.length === 2
          ) {
            return {
              lng: parsed.coordinates[0],
              lat: parsed.coordinates[1],
            }
          }
        }

        return null
      } catch (error) {
        console.error('Failed to parse coordinates:', error)
        return null
      }
    },
    []
  )

  return {
    // Data
    nearbyPorts,
    geographicSearch,
    geographicLoading,

    // Actions
    searchWithinRadius: handleRadiusSearch,
    searchByCountry: handleCountrySearch,
    calculateDistance: handleDistanceCalculation,
    clearGeographicSearch,

    // Utilities
    parseCoordinates,
    hasGeographicSearch: !!geographicSearch,
  }
}

// Validation hooks
export function usePortValidation() {
  const ports = usePorts()

  const validateUniqueCode = useCallback(
    (code: string, excludeId?: string) => {
      if (!code) return { isValid: false, message: 'Port code is required' }

      const normalizedCode = code.toUpperCase()
      const existing = ports.find(
        port =>
          port.code.toUpperCase() === normalizedCode &&
          port.is_active &&
          port.id !== excludeId
      )

      return {
        isValid: !existing,
        message: existing
          ? `Port with code "${normalizedCode}" already exists`
          : undefined,
      }
    },
    [ports]
  )

  const validatePortCode = useCallback((code: string) => {
    if (!code) return { isValid: false, message: 'Port code is required' }

    // Port code should start with 2 letters followed by letters/numbers
    const portCodeRegex = /^[A-Z]{2}[A-Z0-9]+$/
    const normalizedCode = code.toUpperCase()

    if (!portCodeRegex.test(normalizedCode)) {
      return {
        isValid: false,
        message:
          'Port code must start with 2 uppercase letters followed by letters/numbers (e.g., THBKK, CNSHA)',
      }
    }

    if (normalizedCode.length < 3 || normalizedCode.length > 10) {
      return {
        isValid: false,
        message: 'Port code must be 3-10 characters long',
      }
    }

    return { isValid: true }
  }, [])

  const validateCoordinates = useCallback((lat: number, lng: number) => {
    const errors: string[] = []

    if (lat < -90 || lat > 90) {
      errors.push('Latitude must be between -90 and 90')
    }

    if (lng < -180 || lng > 180) {
      errors.push('Longitude must be between -180 and 180')
    }

    return {
      isValid: errors.length === 0,
      message: errors.join(', '),
    }
  }, [])

  const validateTimezone = useCallback((timezone: string) => {
    if (!timezone) return { isValid: true } // Optional field

    // Basic timezone validation (Continent/City format)
    const timezoneRegex = /^[A-Za-z]+\/[A-Za-z_]+$/

    if (!timezoneRegex.test(timezone)) {
      return {
        isValid: false,
        message:
          'Timezone must be in format: Continent/City (e.g., Asia/Bangkok)',
      }
    }

    return { isValid: true }
  }, [])

  return {
    validateUniqueCode,
    validatePortCode,
    validateCoordinates,
    validateTimezone,
  }
}

// Port types and countries management
export function usePortCategories() {
  const { countries, portsByType } = usePortsData()

  const createCountryOptions = useCallback(() => {
    return countries.map(country => ({
      value: country,
      label: country,
    }))
  }, [countries])

  const createPortTypeOptions = useCallback(() => {
    return [
      { value: 'origin', label: 'Origin Port' },
      { value: 'destination', label: 'Destination Port' },
      { value: 'transit', label: 'Transit Port' },
    ]
  }, [])

  const getPortTypeLabel = useCallback(
    (type: string) => {
      const options = createPortTypeOptions()
      return options.find(option => option.value === type)?.label || type
    },
    [createPortTypeOptions]
  )

  return {
    countries,
    portsByType,
    countryOptions: createCountryOptions(),
    portTypeOptions: createPortTypeOptions(),
    getPortTypeLabel,
  }
}

// Constants
export const DEFAULT_PORT_FILTER: PortFilter = {
  is_active: true,
}

// Common port statuses for display
export const PORT_STATUSES = [
  { value: true, label: 'Active', variant: 'default' as const },
  { value: false, label: 'Inactive', variant: 'secondary' as const },
] as const

export const PORT_TYPE_BADGES = {
  origin: { label: 'Origin', className: 'bg-blue-500 hover:bg-blue-600' },
  destination: {
    label: 'Destination',
    className: 'bg-green-500 hover:bg-green-600',
  },
  transit: { label: 'Transit', className: 'bg-yellow-500 hover:bg-yellow-600' },
} as const

// Coordinate utilities
export function useCoordinateUtils() {
  const formatCoordinates = useCallback((lat: number, lng: number) => {
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  }, [])

  const formatCoordinatesDMS = useCallback((lat: number, lng: number) => {
    const formatDMS = (decimal: number, isLatitude: boolean) => {
      const absolute = Math.abs(decimal)
      const degrees = Math.floor(absolute)
      const minutesFloat = (absolute - degrees) * 60
      const minutes = Math.floor(minutesFloat)
      const seconds = ((minutesFloat - minutes) * 60).toFixed(2)

      const direction = isLatitude
        ? decimal >= 0
          ? 'N'
          : 'S'
        : decimal >= 0
          ? 'E'
          : 'W'

      return `${degrees}°${minutes}'${seconds}"${direction}`
    }

    return `${formatDMS(lat, true)} ${formatDMS(lng, false)}`
  }, [])

  const parseCoordinatesFromString = useCallback(
    (coordString: string): Coordinates | null => {
      try {
        // Try comma-separated format: "lat, lng"
        const parts = coordString.split(',').map(s => s.trim())
        if (parts.length === 2) {
          const lat = parseFloat(parts[0])
          const lng = parseFloat(parts[1])

          if (!isNaN(lat) && !isNaN(lng)) {
            return { lat, lng }
          }
        }
        return null
      } catch {
        return null
      }
    },
    []
  )

  return {
    formatCoordinates,
    formatCoordinatesDMS,
    parseCoordinatesFromString,
  }
}

// Main hook that combines all functionality
export function usePortsManagement() {
  const data = usePortsData()
  const crud = usePortCRUD()
  const selection = usePortSelection()
  const geographic = usePortGeographicSearch()
  const validation = usePortValidation()
  const categories = usePortCategories()
  const coordinates = useCoordinateUtils()

  return {
    ...data,
    ...crud,
    ...selection,
    ...geographic,
    ...validation,
    ...categories,
    ...coordinates,

    // Convenience methods
    resetFilters: () => data.setFilter(DEFAULT_PORT_FILTER),
    clearSearch: () => data.setSearchTerm(''),

    // Geographic convenience methods
    searchNearby: (lat: number, lng: number, radiusKm: number = 100) =>
      geographic.searchWithinRadius({
        center_lat: lat,
        center_lng: lng,
        radius_km: radiusKm,
      }),
  }
}
