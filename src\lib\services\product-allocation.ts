import { createClient } from '@/lib/supabase/client'
import type { PackagingType } from '@/lib/validations/shipment'
import type { GeneratedContainer, ProductAllocation } from './container-generation'

export interface ContainerWithCapacity extends GeneratedContainer {
  id?: string
  shipment_id: string
  remaining_volume: number
  remaining_weight: number
  allocated_products: AllocatedProduct[]
}

export interface AllocatedProduct {
  product_id: string
  quantity: number
  packaging_type: PackagingType
  gross_weight: number
  net_weight: number
  volume: number
  shipping_mark?: string
  mfg_date?: string
  expire_date?: string
  lot_number?: string
  quality_grade?: string
}

export interface ProductAllocationResult {
  container_id: string
  product_id: string
  quantity: number
  unit_price_cif: number
  unit_price_fob: number
  total_value_cif: number
  total_value_fob: number
  gross_weight: number
  net_weight: number
  packaging_type: PackagingType
  shipping_mark?: string
  mfg_date?: string
  expire_date?: string
  lot_number?: string
  quality_grade?: string
}

export interface ProductAllocationError {
  code: string
  message: string
  details?: any
  timestamp: Date
}

export class ProductAllocationService {
  private supabase = createClient()

  // Packaging compatibility matrix
  private readonly PACKAGING_COMPATIBILITY = {
    Bag: ['Bag', 'Carton'], // Bags can stack with bags and be mixed with cartons
    'Plastic Basket': ['Plastic Basket'], // Baskets should be separate
    Carton: ['Carton', 'Bag'], // Cartons can stack with cartons and bags
  } as const

  // Safety factor for container utilization
  private readonly UTILIZATION_TARGET = 0.85 // Target 85% utilization

  private mapError(error: any): ProductAllocationError {
    const timestamp = new Date()

    return {
      code: 'PRODUCT_ALLOCATION_ERROR',
      message: error.message || 'Product allocation failed.',
      details: error,
      timestamp,
    }
  }

  /**
   * Initialize containers with capacity tracking
   */
  initializeContainerCapacity(
    containers: GeneratedContainer[],
    shipment_id: string
  ): ContainerWithCapacity[] {
    return containers.map(container => ({
      ...container,
      shipment_id,
      remaining_volume: container.volume,
      remaining_weight: this.getContainerWeightCapacity(
        container.container_size,
        container.container_type
      ),
      allocated_products: [],
    }))
  }

  /**
   * Get container weight capacity based on size and type
   */
  private getContainerWeightCapacity(size: string, type: string): number {
    const WEIGHT_CAPACITY = {
      '20ft': { dry: 28200, reefer: 27400, open_top: 27600, flat_rack: 30500, tank: 30480 },
      '40ft': { dry: 26700, reefer: 27300, open_top: 26500, flat_rack: 30500, tank: 30480 },
      '40hc': { dry: 26580, reefer: 27300, open_top: 26200, flat_rack: 30500, tank: 30480 },
      '45ft': { dry: 26000, reefer: 26800, open_top: 25800, flat_rack: 30500, tank: 30480 },
    } as any

    return WEIGHT_CAPACITY[size]?.[type] || 26000 // Default fallback
  }

  /**
   * Check if products are compatible for same container
   */
  private areProductsCompatible(
    packaging1: PackagingType,
    packaging2: PackagingType
  ): boolean {
    return this.PACKAGING_COMPATIBILITY[packaging1].includes(packaging2)
  }

  /**
   * Calculate product volume if not provided
   */
  private estimateProductVolume(
    packaging_type: PackagingType,
    quantity: number
  ): number {
    const VOLUME_ESTIMATES = {
      Bag: 0.05, // 50 liters per bag
      'Plastic Basket': 0.08, // 80 liters per basket  
      Carton: 0.06, // 60 liters per carton
    }

    return VOLUME_ESTIMATES[packaging_type] * quantity
  }

  /**
   * Find the best container for a product allocation
   */
  private findBestContainer(
    containers: ContainerWithCapacity[],
    product: ProductAllocation,
    quantity: number
  ): ContainerWithCapacity | null {
    const required_volume = this.estimateProductVolume(product.packaging_type, quantity)
    const required_weight = product.gross_weight_per_package * quantity

    const suitable_containers = containers.filter(container => {
      // Check capacity constraints
      if (
        container.remaining_volume < required_volume ||
        container.remaining_weight < required_weight
      ) {
        return false
      }

      // Check packaging compatibility
      if (container.allocated_products.length > 0) {
        return container.allocated_products.every(allocated =>
          this.areProductsCompatible(product.packaging_type, allocated.packaging_type)
        )
      }

      return true
    })

    if (suitable_containers.length === 0) {
      return null
    }

    // Sort by utilization efficiency (prefer containers with better utilization)
    suitable_containers.sort((a, b) => {
      const utilizationA =
        (a.volume - a.remaining_volume + required_volume) / a.volume
      const utilizationB =
        (b.volume - b.remaining_volume + required_volume) / b.volume

      // Prefer containers closer to target utilization
      const diffA = Math.abs(utilizationA - this.UTILIZATION_TARGET)
      const diffB = Math.abs(utilizationB - this.UTILIZATION_TARGET)

      return diffA - diffB
    })

    return suitable_containers[0]
  }

  /**
   * Allocate a product to a container
   */
  private allocateProductToContainer(
    container: ContainerWithCapacity,
    product: ProductAllocation,
    quantity: number,
    pricing?: { unit_price_cif: number; unit_price_fob: number }
  ): void {
    const volume = this.estimateProductVolume(product.packaging_type, quantity)
    const gross_weight = product.gross_weight_per_package * quantity
    const net_weight = product.net_weight_per_package * quantity

    const allocated_product: AllocatedProduct = {
      product_id: product.product_id,
      quantity,
      packaging_type: product.packaging_type,
      gross_weight,
      net_weight,
      volume,
    }

    container.allocated_products.push(allocated_product)
    container.remaining_volume -= volume
    container.remaining_weight -= gross_weight
  }

  /**
   * Get product pricing from customer products
   */
  async getProductPricing(
    customer_id: string,
    product_id: string
  ): Promise<{ unit_price_cif: number; unit_price_fob: number }> {
    try {
      const { data, error } = await this.supabase
        .from('customer_products')
        .select('unit_price_cif, unit_price_fob')
        .eq('customer_id', customer_id)
        .eq('product_id', product_id)
        .eq('is_active', true)
        .single()

      if (error) throw error

      return {
        unit_price_cif: data.unit_price_cif || 0,
        unit_price_fob: data.unit_price_fob || 0,
      }
    } catch (error: any) {
      // Return default pricing if not found
      return { unit_price_cif: 0, unit_price_fob: 0 }
    }
  }

  /**
   * Calculate expiration date based on shelf life
   */
  private calculateExpirationDate(
    mfg_date?: string,
    shelf_life_days?: number
  ): string | undefined {
    if (!mfg_date || !shelf_life_days) return undefined

    const mfgDate = new Date(mfg_date)
    const expDate = new Date(mfgDate.getTime() + shelf_life_days * 24 * 60 * 60 * 1000)
    return expDate.toISOString().split('T')[0] // Return YYYY-MM-DD format
  }

  /**
   * Allocate products to containers with optimization
   */
  async allocateProductsToContainers(
    containers: ContainerWithCapacity[],
    products: ProductAllocation[],
    customer_id: string,
    shipment_id: string
  ): Promise<ProductAllocationResult[]> {
    try {
      const allocations: ProductAllocationResult[] = []

      for (const product of products) {
        let remaining_quantity = product.quantity

        while (remaining_quantity > 0) {
          // Find the best container for this product
          const container = this.findBestContainer(
            containers,
            product,
            remaining_quantity
          )

          if (!container) {
            throw new Error(
              `Cannot allocate product ${product.product_id}. No suitable container found with sufficient capacity.`
            )
          }

          // Calculate how much we can fit in this container
          const max_by_volume = Math.floor(
            container.remaining_volume /
              this.estimateProductVolume(product.packaging_type, 1)
          )
          const max_by_weight = Math.floor(
            container.remaining_weight / product.gross_weight_per_package
          )
          const max_quantity = Math.min(
            remaining_quantity,
            max_by_volume,
            max_by_weight
          )

          if (max_quantity <= 0) {
            throw new Error(
              `Cannot allocate product ${product.product_id}. Container capacity exceeded.`
            )
          }

          // Get pricing information
          const pricing = await this.getProductPricing(customer_id, product.product_id)

          // Allocate to container
          this.allocateProductToContainer(container, product, max_quantity, pricing)

          // Create allocation result: quantity × net_weight × unit_price (rounded to 2 decimal places)
          const total_value_cif = Math.round((pricing.unit_price_cif * max_quantity * product.net_weight_per_package) * 100) / 100
          const total_value_fob = Math.round((pricing.unit_price_fob * max_quantity * product.net_weight_per_package) * 100) / 100

          const allocation: ProductAllocationResult = {
            container_id: container.id || '', // Will be set after containers are created
            product_id: product.product_id,
            quantity: max_quantity,
            unit_price_cif: pricing.unit_price_cif,
            unit_price_fob: pricing.unit_price_fob,
            total_value_cif,
            total_value_fob,
            gross_weight: Math.round((product.gross_weight_per_package * max_quantity) * 100) / 100,
            net_weight: Math.round((product.net_weight_per_package * max_quantity) * 100) / 100,
            packaging_type: product.packaging_type,
          }

          allocations.push(allocation)
          remaining_quantity -= max_quantity
        }
      }

      return allocations
    } catch (error: any) {
      throw this.mapError(error)
    }
  }

  /**
   * Optimize container loading for balanced weight distribution
   */
  optimizeContainerLoading(containers: ContainerWithCapacity[]): void {
    // Sort containers by current utilization
    containers.sort((a, b) => {
      const utilizationA = (a.volume - a.remaining_volume) / a.volume
      const utilizationB = (b.volume - b.remaining_volume) / b.volume
      return utilizationA - utilizationB
    })

    // Try to balance loads by moving products between containers
    for (let i = 0; i < containers.length - 1; i++) {
      const container1 = containers[i]
      const container2 = containers[i + 1]

      const utilization1 = (container1.volume - container1.remaining_volume) / container1.volume
      const utilization2 = (container2.volume - container2.remaining_volume) / container2.volume

      // If there's a significant imbalance, try to rebalance
      if (Math.abs(utilization1 - utilization2) > 0.2) {
        this.rebalanceContainers(container1, container2)
      }
    }
  }

  /**
   * Attempt to rebalance products between two containers
   */
  private rebalanceContainers(
    container1: ContainerWithCapacity,
    container2: ContainerWithCapacity
  ): void {
    // Simple rebalancing logic - move compatible products if beneficial
    // This is a placeholder for more sophisticated optimization
    
    const utilization1 = (container1.volume - container1.remaining_volume) / container1.volume
    const utilization2 = (container2.volume - container2.remaining_volume) / container2.volume

    if (utilization1 > utilization2) {
      // Try to move some products from container1 to container2
      for (const product of container1.allocated_products) {
        if (
          container2.remaining_volume >= product.volume &&
          container2.remaining_weight >= product.gross_weight
        ) {
          // Check compatibility
          const compatible = container2.allocated_products.every(allocated =>
            this.areProductsCompatible(product.packaging_type, allocated.packaging_type)
          )

          if (compatible) {
            // Move the product
            container1.allocated_products = container1.allocated_products.filter(
              p => p !== product
            )
            container2.allocated_products.push(product)

            // Update capacities
            container1.remaining_volume += product.volume
            container1.remaining_weight += product.gross_weight
            container2.remaining_volume -= product.volume
            container2.remaining_weight -= product.gross_weight

            break // Only move one product at a time
          }
        }
      }
    }
  }

  /**
   * Generate utilization report for containers
   */
  generateUtilizationReport(containers: ContainerWithCapacity[]): {
    total_containers: number
    average_volume_utilization: number
    average_weight_utilization: number
    underutilized_containers: number
    overutilized_containers: number
  } {
    let total_volume_utilization = 0
    let total_weight_utilization = 0
    let underutilized = 0
    let overutilized = 0

    for (const container of containers) {
      const volume_utilization = (container.volume - container.remaining_volume) / container.volume
      const weight_capacity = this.getContainerWeightCapacity(
        container.container_size,
        container.container_type
      )
      const weight_utilization = (weight_capacity - container.remaining_weight) / weight_capacity

      total_volume_utilization += volume_utilization
      total_weight_utilization += weight_utilization

      const max_utilization = Math.max(volume_utilization, weight_utilization)

      if (max_utilization < 0.7) {
        underutilized++
      } else if (max_utilization > 0.95) {
        overutilized++
      }
    }

    return {
      total_containers: containers.length,
      average_volume_utilization: total_volume_utilization / containers.length,
      average_weight_utilization: total_weight_utilization / containers.length,
      underutilized_containers: underutilized,
      overutilized_containers: overutilized,
    }
  }
}

// Export singleton instance
export const productAllocationService = new ProductAllocationService()