/**
 * Template Sample Data Utilities Tests
 * Story 5.1: Document Template Management System - AC4
 */

import { describe, it, expect } from 'vitest'
import { 
  generateSampleData, 
  replacePlaceholders, 
  getAvailablePlaceholders 
} from '../template-sample-data'

describe('Template Sample Data Utilities', () => {
  describe('generateSampleData', () => {
    it('should generate comprehensive sample data', () => {
      const sampleData = generateSampleData()

      // Test basic shipment data
      expect(sampleData.shipment_number).toMatch(/^EXSEA-[A-Z]+-\d{6}-\d{3}$/)
      expect(sampleData.transport_mode).toBe('Sea')
      expect(sampleData.origin_port).toContain('Bangkok')
      expect(sampleData.destination_port).toContain('Los Angeles')

      // Test customer data
      expect(sampleData.customer_name).toBeTruthy()
      expect(sampleData.customer_code).toMatch(/^CUST-[A-Z]+-\d{3}$/)
      expect(sampleData.customer_email).toContain('@')

      // Test financial data
      expect(sampleData.total_amount_fob).toBeGreaterThan(0)
      expect(sampleData.total_amount_cif).toBeGreaterThan(sampleData.total_amount_fob)
      expect(sampleData.currency).toBe('USD')

      // Test product array
      expect(Array.isArray(sampleData.products)).toBe(true)
      expect(sampleData.products).toHaveLength(3)
      
      sampleData.products.forEach(product => {
        expect(product.product_name).toBeTruthy()
        expect(product.quantity).toBeGreaterThan(0)
        expect(product.unit_price_fob).toBeGreaterThan(0)
        expect(product.total_amount).toBeGreaterThan(0)
      })

      // Test date fields
      expect(sampleData.etd).toMatch(/^\d{4}-\d{2}-\d{2}$/)
      expect(sampleData.eta).toMatch(/^\d{4}-\d{2}-\d{2}$/)
      expect(sampleData.current_date).toMatch(/^\d{4}-\d{2}-\d{2}$/)
    })

    it('should generate consistent data structure', () => {
      const sampleData1 = generateSampleData()
      const sampleData2 = generateSampleData()

      // Should have same keys
      expect(Object.keys(sampleData1)).toEqual(Object.keys(sampleData2))

      // Should have same product structure
      expect(sampleData1.products).toHaveLength(sampleData2.products.length)
      expect(Object.keys(sampleData1.products[0])).toEqual(Object.keys(sampleData2.products[0]))
    })

    it('should generate logical date sequence', () => {
      const sampleData = generateSampleData()
      const etd = new Date(sampleData.etd)
      const eta = new Date(sampleData.eta)
      const currentDate = new Date(sampleData.current_date)

      // ETA should be after ETD
      expect(eta.getTime()).toBeGreaterThan(etd.getTime())
      
      // ETD should be after current date
      expect(etd.getTime()).toBeGreaterThan(currentDate.getTime())
    })
  })

  describe('replacePlaceholders', () => {
    it('should replace simple placeholders correctly', () => {
      const template = 'Hello {{customer_name}}, your shipment {{shipment_number}} is ready.'
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      expect(result).toContain(sampleData.customer_name)
      expect(result).toContain(sampleData.shipment_number)
      expect(result).not.toContain('{{customer_name}}')
      expect(result).not.toContain('{{shipment_number}}')
    })

    it('should handle placeholders with whitespace', () => {
      const template = 'Ship from {{ origin_port }} to {{ destination_port }}.'
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      expect(result).toContain(sampleData.origin_port)
      expect(result).toContain(sampleData.destination_port)
      expect(result).not.toContain('{{')
      expect(result).not.toContain('}}')
    })

    it('should replace numeric placeholders correctly', () => {
      const template = 'Total: {{total_amount_fob}} {{currency}} for {{container_count}} containers.'
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      expect(result).toContain(sampleData.total_amount_fob.toString())
      expect(result).toContain(sampleData.currency)
      expect(result).toContain(sampleData.container_count.toString())
    })

    it('should generate products table when placeholder is used', () => {
      const template = 'Product List:\n{{products_table}}'
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      expect(result).toContain('<table')
      expect(result).toContain('Product')
      expect(result).toContain('Quantity')
      expect(result).toContain('Total Amount')
      expect(result).toContain(sampleData.products[0].product_name)
      expect(result).not.toContain('{{products_table}}')
    })

    it('should replace individual product placeholders', () => {
      const template = 'First product: {{product_1_product_name}} - {{product_1_quantity}} {{product_1_unit}}'
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      expect(result).toContain(sampleData.products[0].product_name)
      expect(result).toContain(sampleData.products[0].quantity.toString())
      expect(result).toContain(sampleData.products[0].unit)
    })

    it('should leave unknown placeholders unchanged', () => {
      const template = 'Known: {{customer_name}}, Unknown: {{unknown_placeholder}}'
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      expect(result).toContain(sampleData.customer_name)
      expect(result).toContain('{{unknown_placeholder}}')
      expect(result).not.toContain('{{customer_name}}')
    })

    it('should handle empty template content', () => {
      const template = ''
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      expect(result).toBe('')
    })

    it('should handle template with no placeholders', () => {
      const template = 'This is plain text with no placeholders.'
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      expect(result).toBe(template)
    })

    it('should handle multiple occurrences of same placeholder', () => {
      const template = 'Customer {{customer_name}} ordered from {{customer_name}} again.'
      const sampleData = generateSampleData()
      
      const result = replacePlaceholders(template, sampleData)
      
      const occurrences = result.split(sampleData.customer_name).length - 1
      expect(occurrences).toBe(2)
      expect(result).not.toContain('{{customer_name}}')
    })
  })

  describe('getAvailablePlaceholders', () => {
    it('should return array of available placeholders', () => {
      const placeholders = getAvailablePlaceholders()
      
      expect(Array.isArray(placeholders)).toBe(true)
      expect(placeholders.length).toBeGreaterThan(0)
    })

    it('should include basic data placeholders', () => {
      const placeholders = getAvailablePlaceholders()
      
      expect(placeholders).toContain('customer_name')
      expect(placeholders).toContain('shipment_number')
      expect(placeholders).toContain('total_amount_fob')
      expect(placeholders).toContain('origin_port')
      expect(placeholders).toContain('destination_port')
    })

    it('should include product-related placeholders', () => {
      const placeholders = getAvailablePlaceholders()
      
      expect(placeholders).toContain('products_table')
      expect(placeholders).toContain('product_1_product_name')
      expect(placeholders).toContain('product_2_product_name')
      expect(placeholders).toContain('product_3_product_name')
    })

    it('should return sorted placeholders', () => {
      const placeholders = getAvailablePlaceholders()
      
      const sorted = [...placeholders].sort()
      expect(placeholders).toEqual(sorted)
    })

    it('should not include complex object placeholders', () => {
      const placeholders = getAvailablePlaceholders()
      
      expect(placeholders).not.toContain('products')
    })

    it('should include financial placeholders', () => {
      const placeholders = getAvailablePlaceholders()
      
      expect(placeholders).toContain('total_amount_cif')
      expect(placeholders).toContain('currency')
      expect(placeholders).toContain('payment_terms')
    })

    it('should include document placeholders', () => {
      const placeholders = getAvailablePlaceholders()
      
      expect(placeholders).toContain('invoice_number')
      expect(placeholders).toContain('contract_number')
      expect(placeholders).toContain('packing_list_number')
    })

    it('should include company placeholders', () => {
      const placeholders = getAvailablePlaceholders()
      
      expect(placeholders).toContain('company_name')
      expect(placeholders).toContain('company_address')
      expect(placeholders).toContain('company_email')
    })
  })

  describe('Sample Data Integration', () => {
    it('should provide realistic export trading data', () => {
      const sampleData = generateSampleData()

      // Verify realistic shipment number format
      expect(sampleData.shipment_number).toMatch(/^EXSEA-[A-Z]+-\d{6}-\d{3}$/)

      // Verify port codes are realistic
      expect(sampleData.origin_port).toContain('THBKK') // Bangkok port code
      expect(sampleData.destination_port).toContain('USLAX') // Los Angeles port code

      // Verify container specifications
      expect(['20GP', '20HC', '40GP', '40HC']).toContain(sampleData.container_type)

      // Verify financial calculations
      const productTotal = sampleData.products.reduce((sum, p) => sum + p.total_amount, 0)
      expect(Math.abs(sampleData.total_amount_fob - productTotal)).toBeLessThan(1) // Allow for rounding
    })

    it('should provide consistent weight calculations', () => {
      const sampleData = generateSampleData()

      const totalNetWeight = sampleData.products.reduce((sum, p) => sum + p.net_weight, 0)
      const totalGrossWeight = sampleData.products.reduce((sum, p) => sum + p.gross_weight, 0)

      expect(sampleData.total_net_weight).toBe(totalNetWeight)
      expect(sampleData.total_gross_weight).toBe(totalGrossWeight)
      expect(sampleData.total_gross_weight).toBeGreaterThan(sampleData.total_net_weight)
    })

    it('should provide proper fruit export products', () => {
      const sampleData = generateSampleData()

      // Verify we have fruit products
      const fruitProducts = sampleData.products.filter(p => 
        p.product_name.toLowerCase().includes('mango') ||
        p.product_name.toLowerCase().includes('pineapple') ||
        p.product_name.toLowerCase().includes('coconut')
      )
      
      expect(fruitProducts.length).toBeGreaterThan(0)

      // Verify Thai fruit varieties
      const thaiVarieties = sampleData.products.filter(p =>
        p.variety.includes('Nam Dok Mai') ||
        p.variety.includes('Sweet Gold') ||
        p.variety.includes('Organic')
      )

      expect(thaiVarieties.length).toBeGreaterThan(0)
    })
  })
})