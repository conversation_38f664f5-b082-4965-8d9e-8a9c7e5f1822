'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Eye,
  X,
  User,
  Mail,
  Phone,
  Calendar,
  Truck,
  Camera,
  MessageSquare,
} from 'lucide-react'
import { DriverForm } from '@/components/forms/driver-form/driver-form'
import { DriverPhoto } from '@/components/drivers/driver-photo'
import { useDriversManagement } from '@/hooks/use-drivers'
import type { Driver } from '@/stores/driver-store'
import type { DriverForm as DriverFormData } from '@/lib/validations/drivers'
import { formatDistanceToNow, format } from 'date-fns'

export default function DriversPage() {
  const {
    // Data
    drivers,
    loading,
    error,

    // Pagination
    currentPage,
    totalPages,
    totalCount,
    hasNextPage,
    hasPreviousPage,

    // Filter and search
    filter,
    searchTerm,
    sortBy,
    sortOrder,

    // Selection
    selectedDrivers,
    selectedCount,
    isSelected,
    isAllSelected,
    isPartiallySelected,

    // CRUD operations
    createDriver,
    updateDriver,
    deleteDriver,
    bulkDeleteDrivers,
    uploadDriverPhoto,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setFilter,
    setSearchTerm,
    setSorting,
    setPage,
    nextPage,
    previousPage,
    toggleDriver,
    toggleAll,
    clearSelection,
    clearError,
    refreshDrivers,

    // Carrier companies
    carrierCompanyOptions,
    getCarrierCompanyName,

    // Utility functions
    getDriverFullName,
    getDriverDisplayName,
  } = useDriversManagement()

  // UI state
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingDriver, setEditingDriver] = useState<Driver | null>(null)
  const [viewingDriver, setViewingDriver] = useState<Driver | null>(null)
  const [deletingDriver, setDeletingDriver] = useState<Driver | null>(null)
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)

  // Handle create driver
  const handleCreate = async (
    data: DriverFormData,
    photoFile?: File | null
  ) => {
    try {
      // Create driver first
      const newDriver = await createDriver(data)

      // Upload photo if provided
      if (photoFile && newDriver) {
        await uploadDriverPhoto(newDriver.id, photoFile)
      }

      setShowCreateDialog(false)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle update driver
  const handleUpdate = async (
    data: DriverFormData,
    photoFile?: File | null
  ) => {
    if (!editingDriver) return

    try {
      // Update driver data
      await updateDriver(editingDriver.id, data)

      // Upload new photo if provided
      if (photoFile) {
        await uploadDriverPhoto(editingDriver.id, photoFile)
      }

      setEditingDriver(null)
    } catch (error) {
      // Error handling is done in the store
      throw error
    }
  }

  // Handle delete driver
  const handleDelete = async (driver: Driver) => {
    try {
      await deleteDriver(driver.id)
      setDeletingDriver(null)
    } catch (error) {
      // Error handling is done in the store
      console.error('Delete failed:', error)
    }
  }

  // Handle bulk delete
  const handleBulkDelete = async () => {
    try {
      await bulkDeleteDrivers(selectedDrivers)
      setShowBulkDeleteDialog(false)
      clearSelection()
    } catch (error) {
      // Error handling is done in the store
      console.error('Bulk delete failed:', error)
    }
  }

  // Handle sorting
  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSorting(column, sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSorting(column, 'asc')
    }
  }

  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Drivers</h1>
          <p className="text-slate-400 mt-1">
            Manage driver information and carrier associations
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Driver
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">Create Driver</DialogTitle>
              <DialogDescription className="text-slate-400">
                Add a new driver with carrier association
              </DialogDescription>
            </DialogHeader>
            <DriverForm
              onSubmit={handleCreate}
              onCancel={() => setShowCreateDialog(false)}
              isLoading={isCreating}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Search */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-5 w-5 text-orange-500" />
          <h3 className="text-lg font-medium text-white">Filters</h3>
          {(searchTerm ||
            filter.carrier_id ||
            filter.is_active !== undefined) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFilter({})
                setSearchTerm('')
              }}
              className="text-slate-400 hover:text-white ml-auto"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Search Drivers
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search by name, code, or phone..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
          </div>

          {/* Carrier Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Carrier Company
            </label>
            <Select
              value={filter.carrier_id || 'all'}
              onValueChange={value =>
                setFilter({
                  ...filter,
                  carrier_id: value === 'all' ? undefined : value,
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Carriers" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Carriers
                </SelectItem>
                {carrierCompanyOptions.map(carrier => (
                  <SelectItem
                    key={carrier.value}
                    value={carrier.value}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    {carrier.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">Status</label>
            <Select
              value={
                filter.is_active === undefined
                  ? 'all'
                  : filter.is_active
                    ? 'active'
                    : 'inactive'
              }
              onValueChange={value =>
                setFilter({
                  ...filter,
                  is_active: value === 'all' ? undefined : value === 'active',
                })
              }
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem
                  value="all"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  All Statuses
                </SelectItem>
                <SelectItem
                  value="active"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  Active
                </SelectItem>
                <SelectItem
                  value="inactive"
                  className="text-slate-300 hover:bg-slate-700"
                >
                  Inactive
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {(searchTerm ||
          filter.carrier_id ||
          filter.is_active !== undefined) && (
          <div className="flex flex-wrap gap-2 pt-2">
            {searchTerm && (
              <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-2 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.carrier_id && (
              <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                Carrier: {getCarrierCompanyName(filter.carrier_id)}
                <button
                  onClick={() =>
                    setFilter({ ...filter, carrier_id: undefined })
                  }
                  className="ml-2 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {filter.is_active !== undefined && (
              <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm flex items-center">
                Status: {filter.is_active ? 'Active' : 'Inactive'}
                <button
                  onClick={() => setFilter({ ...filter, is_active: undefined })}
                  className="ml-2 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Bulk Actions */}
      {selectedCount > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedCount} driver{selectedCount !== 1 ? 's' : ''} selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setShowBulkDeleteDialog(true)}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  {isDeleting && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  Delete Selected
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Drivers Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <User className="h-5 w-5 text-orange-500" />
              Drivers ({totalCount})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshDrivers}
              disabled={loading}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {loading && drivers.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-slate-300">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              <span className="ml-2">Loading drivers...</span>
            </div>
          ) : drivers.length === 0 ? (
            <div className="text-center py-8">
              <User className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No drivers found</p>
              <p className="text-sm text-slate-400 mt-1">
                Create your first driver to get started
              </p>
            </div>
          ) : (
            <>
              <Table className="bg-slate-800">
                <TableHeader className="bg-slate-700">
                  <TableRow className="border-slate-600 hover:bg-slate-700">
                    <TableHead className="w-12 text-slate-200">
                      <Checkbox
                        checked={isAllSelected || isPartiallySelected}
                        onCheckedChange={toggleAll}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableHead>
                    <TableHead className="w-16 text-slate-200">Photo</TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('driver_first_name')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Name {getSortIcon('driver_first_name')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort('driver_code')}
                        className="h-auto p-0 font-semibold text-slate-200 hover:text-white hover:bg-slate-600"
                      >
                        Code {getSortIcon('driver_code')}
                      </Button>
                    </TableHead>
                    <TableHead className="text-slate-200">Carrier</TableHead>
                    <TableHead className="text-slate-200">Contact</TableHead>
                    <TableHead className="text-slate-200">Status</TableHead>
                    <TableHead className="w-32 text-slate-200">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-slate-800">
                  {drivers.map(driver => (
                    <TableRow
                      key={driver.id}
                      className="border-slate-600 hover:bg-slate-700 text-slate-200"
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected(driver.id)}
                          onCheckedChange={() => toggleDriver(driver.id)}
                          className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                      </TableCell>
                      <TableCell>
                        <DriverPhoto driver={driver} size="md" />
                      </TableCell>
                      <TableCell className="font-medium">
                        <div>
                          <div className="text-white">
                            {getDriverFullName(driver)}
                          </div>
                          {driver.notes && (
                            <div className="text-xs text-slate-400 mt-1 truncate max-w-32">
                              {driver.notes}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-slate-300 font-mono">
                          {driver.driver_code || 'Not set'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Truck className="h-4 w-4 text-slate-400" />
                          <span className="text-sm text-slate-300">
                            {driver.carrier?.name || 'Unknown Carrier'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          {driver.phone && (
                            <div className="flex items-center space-x-1">
                              <Phone className="h-3 w-3 text-slate-400" />
                              <span className="text-slate-300">
                                {driver.phone}
                              </span>
                            </div>
                          )}
                          {driver.line_id && (
                            <div className="flex items-center space-x-1">
                              <MessageSquare className="h-3 w-3 text-slate-400" />
                              <span className="text-slate-300">
                                {driver.line_id}
                              </span>
                            </div>
                          )}
                          {!driver.phone && !driver.line_id && (
                            <span className="text-slate-500">
                              No contact info
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={driver.is_active ? 'default' : 'secondary'}
                          className={
                            driver.is_active
                              ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                              : 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500'
                          }
                        >
                          {driver.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setViewingDriver(driver)}
                            className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingDriver(driver)}
                            className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingDriver(driver)}
                            className="text-slate-400 hover:text-red-400 hover:bg-slate-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-6 pb-6">
                  <div className="text-sm text-slate-300">
                    Page {currentPage} of {totalPages} ({totalCount} drivers)
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(1)}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={previousPage}
                      disabled={!hasPreviousPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      disabled={!hasNextPage}
                      className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* View Driver Dialog */}
      {viewingDriver && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-slate-800 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">
                Driver Details
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewingDriver(null)}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Driver Avatar and Basic Info */}
              <div className="flex items-start space-x-4">
                <DriverPhoto
                  driver={viewingDriver}
                  size="lg"
                  className="flex-shrink-0"
                />
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white">
                    {getDriverFullName(viewingDriver)}
                  </h3>
                  <div className="flex items-center space-x-2 mt-2">
                    {viewingDriver.driver_code && (
                      <Badge
                        variant="outline"
                        className="border-blue-400 text-blue-200 bg-blue-500/20"
                      >
                        <User className="h-3 w-3 mr-1" />
                        {viewingDriver.driver_code}
                      </Badge>
                    )}
                    <div className="flex items-center space-x-1">
                      <div
                        className={`w-2 h-2 rounded-full ${viewingDriver.is_active ? 'bg-green-400' : 'bg-red-400'}`}
                      />
                      <span
                        className={`text-sm ${viewingDriver.is_active ? 'text-green-300' : 'text-red-300'}`}
                      >
                        {viewingDriver.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Driver Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Truck className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">
                      Carrier Company
                    </span>
                  </div>
                  <p className="text-slate-300">
                    {viewingDriver.carrier?.name || 'Unknown Carrier'}
                  </p>
                  {viewingDriver.carrier?.contact_phone && (
                    <p className="text-slate-400 text-sm mt-1">
                      {viewingDriver.carrier.contact_phone}
                    </p>
                  )}
                </div>

                {viewingDriver.phone && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Phone className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">Phone</span>
                    </div>
                    <p className="text-slate-300">{viewingDriver.phone}</p>
                  </div>
                )}

                {viewingDriver.line_id && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <MessageSquare className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">Line ID</span>
                    </div>
                    <p className="text-slate-300">{viewingDriver.line_id}</p>
                  </div>
                )}

                {/* Vehicle Information */}
                {(viewingDriver.vehicle_head_number || viewingDriver.vehicle_tail_number) && (
                  <>
                    {viewingDriver.vehicle_head_number && (
                      <div className="bg-slate-700 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <Truck className="h-4 w-4 text-orange-500" />
                          <span className="text-white font-medium">Vehicle Head Number</span>
                        </div>
                        <p className="text-slate-300">{viewingDriver.vehicle_head_number}</p>
                      </div>
                    )}

                    {viewingDriver.vehicle_tail_number && (
                      <div className="bg-slate-700 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <Truck className="h-4 w-4 text-orange-500" />
                          <span className="text-white font-medium">Vehicle Tail Number</span>
                        </div>
                        <p className="text-slate-300">{viewingDriver.vehicle_tail_number}</p>
                      </div>
                    )}
                  </>
                )}

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Camera className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Photo</span>
                  </div>
                  <DriverPhoto
                    driver={viewingDriver}
                    className="w-32 h-32 rounded-lg"
                  />
                </div>
              </div>

              {/* Notes */}
              {viewingDriver.notes && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Notes</span>
                  </div>
                  <p className="text-slate-300">{viewingDriver.notes}</p>
                </div>
              )}

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Created</span>
                  </div>
                  <p className="text-slate-300">
                    {format(new Date(viewingDriver.created_at), 'PPP')}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {formatDistanceToNow(new Date(viewingDriver.created_at), {
                      addSuffix: true,
                    })}
                  </p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Edit className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Last Updated</span>
                  </div>
                  <p className="text-slate-300">
                    {format(new Date(viewingDriver.updated_at), 'PPP')}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {formatDistanceToNow(new Date(viewingDriver.updated_at), {
                      addSuffix: true,
                    })}
                  </p>
                </div>
              </div>

              {/* Driver ID */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <User className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Driver ID</span>
                </div>
                <p className="text-slate-300 font-mono text-sm">
                  {viewingDriver.id}
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => setViewingDriver(null)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Driver Dialog */}
      <Dialog
        open={!!editingDriver}
        onOpenChange={() => setEditingDriver(null)}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">Edit Driver</DialogTitle>
            <DialogDescription className="text-slate-400">
              Update driver information and carrier association
            </DialogDescription>
          </DialogHeader>
          {editingDriver && (
            <DriverForm
              driver={editingDriver}
              onSubmit={handleUpdate}
              onCancel={() => setEditingDriver(null)}
              isLoading={isUpdating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Driver Dialog */}
      <AlertDialog
        open={!!deletingDriver}
        onOpenChange={() => setDeletingDriver(null)}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Driver
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete &quot;
              {deletingDriver && getDriverFullName(deletingDriver)}&quot;? This
              action cannot be undone.
              {deletingDriver && (
                <div className="mt-2 p-2 bg-slate-700 rounded text-sm border border-slate-600">
                  <strong className="text-white">Name:</strong>{' '}
                  <span className="text-slate-200">
                    {getDriverFullName(deletingDriver)}
                  </span>
                  <br />
                  <strong className="text-white">Carrier:</strong>{' '}
                  <span className="text-slate-200">
                    {deletingDriver.carrier?.name || 'Unknown'}
                  </span>
                  <br />
                  {deletingDriver.driver_code && (
                    <>
                      <strong className="text-white">Code:</strong>{' '}
                      <span className="text-slate-200 font-mono">
                        {deletingDriver.driver_code}
                      </span>
                    </>
                  )}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setDeletingDriver(null)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deletingDriver && handleDelete(deletingDriver)}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Driver
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
      >
        <AlertDialogContent className="bg-slate-800 border-slate-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">
              Delete Selected Drivers
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Are you sure you want to delete {selectedCount} selected driver
              {selectedCount !== 1 ? 's' : ''}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => setShowBulkDeleteDialog(false)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 text-white hover:bg-red-700 border-none"
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Drivers
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
