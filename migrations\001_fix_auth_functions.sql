-- Fix the is_admin_or_staff function to use correct field name
CREATE OR REPLACE FUNCTION is_admin_or_staff()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role IN ('admin', 'cs', 'account')
        FROM profiles
        WHERE user_id = auth.uid()
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix the get_user_role function to use correct field name
CREATE OR REPLACE FUNCTION get_user_role(user_uuid UUID DEFAULT auth.uid())
RETURNS role_type AS $$
BEGIN
    RETURN (
        SELECT role
        FROM profiles
        WHERE user_id = user_uuid
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the missing get_user_company_id function
CREATE OR REPLACE FUNCTION get_user_company_id(user_uuid UUID DEFAULT auth.uid())
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT company_id
        FROM profiles
        WHERE user_id = user_uuid
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the validate_role_company_association function
CREATE OR REPLACE FUNCTION validate_role_company_association(
    user_role role_type,
    company_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    company_type_val company_type_enum;
BEGIN
    -- Get company type if company_id is provided
    IF company_id IS NOT NULL THEN
        SELECT company_type INTO company_type_val
        FROM companies
        WHERE id = company_id AND is_active = true;
        
        -- If company not found or inactive, return false
        IF company_type_val IS NULL THEN
            RETURN FALSE;
        END IF;
    END IF;

    -- Validate role-company type combinations
    CASE user_role
        WHEN 'admin', 'cs', 'account' THEN
            -- Admin roles don't require specific company association
            RETURN TRUE;
        WHEN 'customer' THEN
            -- Customer must be associated with customer company
            RETURN company_id IS NOT NULL AND company_type_val = 'customer';
        WHEN 'carrier', 'driver' THEN
            -- Carrier/driver must be associated with carrier company
            RETURN company_id IS NOT NULL AND company_type_val = 'carrier';
        WHEN 'factory' THEN
            -- Factory role must be associated with factory company
            RETURN company_id IS NOT NULL AND company_type_val = 'factory';
        WHEN 'shipper' THEN
            -- Shipper must be associated with shipper company
            RETURN company_id IS NOT NULL AND company_type_val = 'shipper';
        WHEN 'consignee' THEN
            -- Consignee must be associated with consignee company
            RETURN company_id IS NOT NULL AND company_type_val = 'consignee';
        WHEN 'notify_party' THEN
            -- Notify party must be associated with notify_party company
            RETURN company_id IS NOT NULL AND company_type_val = 'notify_party';
        WHEN 'forwarder_agent' THEN
            -- Forwarder agent must be associated with forwarder_agent company
            RETURN company_id IS NOT NULL AND company_type_val = 'forwarder_agent';
        ELSE
            RETURN FALSE;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger function to validate profile before insert/update
CREATE OR REPLACE FUNCTION validate_profile_role_company()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate the role-company association
    IF NOT validate_role_company_association(NEW.role, NEW.company_id) THEN
        RAISE EXCEPTION 'Invalid role-company association: % role cannot be associated with company %', 
            NEW.role, NEW.company_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to profiles table (drop first if exists)
DROP TRIGGER IF EXISTS validate_profile_role_company_trigger ON profiles;
CREATE TRIGGER validate_profile_role_company_trigger
    BEFORE INSERT OR UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION validate_profile_role_company();