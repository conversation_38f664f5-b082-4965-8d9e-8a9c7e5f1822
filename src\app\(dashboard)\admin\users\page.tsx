'use client'

import { Suspense, useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { UsersTable } from '@/components/admin/users-table'
import { CreateUserButton } from '@/components/admin/create-user-button'
import { UserFilters } from '@/components/admin/user-filters'
import { Users } from 'lucide-react'

export interface UserFilterState {
  searchTerm: string
  selectedRole: string
  selectedStatus: string
}

export default function AdminUsersPage() {
  const { isAdmin, loading } = useAuth()

  // Filter state
  const [filters, setFilters] = useState<UserFilterState>({
    searchTerm: '',
    selectedRole: 'all',
    selectedStatus: 'all',
  })

  // Show loading while checking auth
  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-700 rounded w-1/3"></div>
          <div className="h-4 bg-slate-700 rounded w-2/3"></div>
          <div className="h-64 bg-slate-800 rounded-lg"></div>
        </div>
      </div>
    )
  }

  // Show access denied for non-admin users (middleware should prevent this)
  if (!isAdmin) {
    return (
      <div className="container mx-auto py-6">
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6 text-center">
          <h1 className="text-xl font-semibold text-red-300 mb-2">
            Access Denied
          </h1>
          <p className="text-red-400">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Users className="h-6 w-6 text-orange-500" />
          <h1 className="text-2xl font-bold text-white">User Management</h1>
        </div>
        <CreateUserButton />
      </div>

      {/* Page Description */}
      <p className="text-slate-300">
        Manage user accounts, roles, and permissions. View user activity and
        update user information.
      </p>

      {/* Filters */}
      <Suspense
        fallback={
          <div className="h-16 bg-slate-800 rounded-lg animate-pulse" />
        }
      >
        <UserFilters filters={filters} onFiltersChange={setFilters} />
      </Suspense>

      {/* Users Table */}
      <Suspense
        fallback={
          <div className="bg-slate-800 rounded-lg p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-slate-700 rounded w-3/4"></div>
              <div className="h-4 bg-slate-700 rounded w-1/2"></div>
              <div className="h-4 bg-slate-700 rounded w-5/6"></div>
            </div>
          </div>
        }
      >
        <UsersTable filters={filters} />
      </Suspense>
    </div>
  )
}
