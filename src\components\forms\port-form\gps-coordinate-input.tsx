'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { MapPin, Navigation, Target, AlertCircle } from 'lucide-react'
import type { Coordinates } from '@/lib/validations/ports'

interface GPSCoordinateInputProps {
  value?: Coordinates
  onChange: (coordinates: Coordinates | undefined) => void
  className?: string
  disabled?: boolean
}

export function GPSCoordinateInput({
  value,
  onChange,
  className = '',
  disabled = false,
}: GPSCoordinateInputProps) {
  const [latInput, setLatInput] = useState(value?.lat?.toString() || '')
  const [lngInput, setLngInput] = useState(value?.lng?.toString() || '')
  const [coordinateString, setCoordinateString] = useState('')
  const [inputMode, setInputMode] = useState<'separate' | 'combined'>(
    'separate'
  )
  const [errors, setErrors] = useState<{
    lat?: string
    lng?: string
    combined?: string
  }>({})

  // Update internal state when value prop changes
  useEffect(() => {
    if (value) {
      setLatInput(value.lat.toString())
      setLngInput(value.lng.toString())
      setCoordinateString(`${value.lat}, ${value.lng}`)
    } else {
      setLatInput('')
      setLngInput('')
      setCoordinateString('')
    }
  }, [value])

  const validateCoordinate = (lat: number, lng: number) => {
    const newErrors: { lat?: string; lng?: string } = {}

    if (lat < -90 || lat > 90) {
      newErrors.lat = 'Latitude must be between -90 and 90'
    }

    if (lng < -180 || lng > 180) {
      newErrors.lng = 'Longitude must be between -180 and 180'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSeparateInputChange = () => {
    const lat = parseFloat(latInput)
    const lng = parseFloat(lngInput)

    if (isNaN(lat) || isNaN(lng)) {
      if (latInput === '' && lngInput === '') {
        setErrors({})
        onChange(undefined)
      }
      return
    }

    if (validateCoordinate(lat, lng)) {
      onChange({ lat, lng })
    }
  }

  const handleCombinedInputChange = (coordStr: string) => {
    setCoordinateString(coordStr)

    if (!coordStr.trim()) {
      setErrors({})
      onChange(undefined)
      return
    }

    // Try to parse various coordinate formats
    const parseCoordinates = (str: string): Coordinates | null => {
      // Remove extra whitespace and normalize
      const cleaned = str.trim().replace(/\s+/g, ' ')

      // Format: "lat, lng" or "lat,lng"
      const commaMatch = cleaned.match(
        /^([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)$/
      )
      if (commaMatch) {
        return {
          lat: parseFloat(commaMatch[1]),
          lng: parseFloat(commaMatch[2]),
        }
      }

      // Format: "lat lng" (space-separated)
      const spaceMatch = cleaned.match(/^([-+]?\d*\.?\d+)\s+([-+]?\d*\.?\d+)$/)
      if (spaceMatch) {
        return {
          lat: parseFloat(spaceMatch[1]),
          lng: parseFloat(spaceMatch[2]),
        }
      }

      return null
    }

    const parsed = parseCoordinates(coordStr)
    if (parsed) {
      if (validateCoordinate(parsed.lat, parsed.lng)) {
        setLatInput(parsed.lat.toString())
        setLngInput(parsed.lng.toString())
        onChange(parsed)
      }
    } else {
      setErrors({
        combined: 'Invalid coordinate format. Use: "latitude, longitude"',
      })
    }
  }

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      setErrors({ combined: 'Geolocation is not supported by this browser' })
      return
    }

    navigator.geolocation.getCurrentPosition(
      position => {
        const lat = position.coords.latitude
        const lng = position.coords.longitude

        setLatInput(lat.toString())
        setLngInput(lng.toString())
        setCoordinateString(`${lat}, ${lng}`)
        setErrors({})
        onChange({ lat, lng })
      },
      error => {
        let errorMessage = 'Unable to get current location'
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user'
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable'
            break
          case error.TIMEOUT:
            errorMessage = 'Location request timed out'
            break
        }
        setErrors({ combined: errorMessage })
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5 minutes
      }
    )
  }

  const clearCoordinates = () => {
    setLatInput('')
    setLngInput('')
    setCoordinateString('')
    setErrors({})
    onChange(undefined)
  }

  // Update coordinates when separate inputs change
  useEffect(() => {
    if (inputMode === 'separate') {
      handleSeparateInputChange()
    }
  }, [latInput, lngInput, inputMode])

  return (
    <div className="space-y-4">
      {/* Input Mode Toggle */}
      <div className="flex gap-2">
        <Button
          type="button"
          variant={inputMode === 'separate' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setInputMode('separate')}
          className={
            inputMode === 'separate'
              ? 'bg-orange-500 hover:bg-orange-600 text-white'
              : 'bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white'
          }
        >
          <MapPin className="h-4 w-4 mr-1" />
          Separate
        </Button>
        <Button
          type="button"
          variant={inputMode === 'combined' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setInputMode('combined')}
          className={
            inputMode === 'combined'
              ? 'bg-orange-500 hover:bg-orange-600 text-white'
              : 'bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white'
          }
        >
          <Navigation className="h-4 w-4 mr-1" />
          Combined
        </Button>
      </div>

      {/* Input Fields */}
      {inputMode === 'separate' ? (
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            <label className="text-sm font-medium text-slate-300">
              Latitude
            </label>
            <Input
              type="number"
              step="any"
              value={latInput}
              onChange={e => setLatInput(e.target.value)}
              placeholder="e.g., 13.7563"
              disabled={disabled}
              className={`${className} ${errors.lat ? 'border-red-500 focus:border-red-500' : ''}`}
            />
            {errors.lat && (
              <p className="text-xs text-red-400 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.lat}
              </p>
            )}
          </div>
          <div className="space-y-1">
            <label className="text-sm font-medium text-slate-300">
              Longitude
            </label>
            <Input
              type="number"
              step="any"
              value={lngInput}
              onChange={e => setLngInput(e.target.value)}
              placeholder="e.g., 100.5018"
              disabled={disabled}
              className={`${className} ${errors.lng ? 'border-red-500 focus:border-red-500' : ''}`}
            />
            {errors.lng && (
              <p className="text-xs text-red-400 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.lng}
              </p>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-1">
          <label className="text-sm font-medium text-slate-300">
            Coordinates
          </label>
          <Input
            type="text"
            value={coordinateString}
            onChange={e => handleCombinedInputChange(e.target.value)}
            placeholder="e.g., 13.7563, 100.5018 or 13.7563 100.5018"
            disabled={disabled}
            className={`${className} ${errors.combined ? 'border-red-500 focus:border-red-500' : ''}`}
          />
          {errors.combined && (
            <p className="text-xs text-red-400 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              {errors.combined}
            </p>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={getCurrentLocation}
          disabled={disabled}
          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
        >
          <Target className="h-4 w-4 mr-1" />
          Current Location
        </Button>

        {value && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={clearCoordinates}
            disabled={disabled}
            className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
          >
            Clear
          </Button>
        )}
      </div>

      {/* Coordinate Display */}
      {value && (
        <Card className="bg-slate-700 border-slate-600">
          <CardContent className="pt-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <MapPin className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium text-slate-300">
                  Coordinate Information
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="text-slate-400">Decimal Degrees:</span>
                  <div className="text-slate-200 font-mono">
                    {value.lat.toFixed(6)}, {value.lng.toFixed(6)}
                  </div>
                </div>

                <div>
                  <span className="text-slate-400">PostGIS Point:</span>
                  <div className="text-slate-200 font-mono text-xs">
                    POINT({value.lng} {value.lat})
                  </div>
                </div>
              </div>

              {/* Validation status */}
              <div className="flex items-center gap-2 pt-2">
                <Badge
                  variant="default"
                  className="bg-green-600 hover:bg-green-700 text-white border-green-600"
                >
                  Valid Coordinates
                </Badge>
                {value.lat >= -90 &&
                  value.lat <= 90 &&
                  value.lng >= -180 &&
                  value.lng <= 180 && (
                    <span className="text-xs text-slate-400">
                      Ready for geographic search
                    </span>
                  )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
