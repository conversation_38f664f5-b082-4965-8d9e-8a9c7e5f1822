'use client'

import { useEffect, useRef, useCallback, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useShipmentStore } from '@/stores/shipment-store'
import type { RealtimeChannel } from '@supabase/supabase-js'
import type { ShipmentWithRelations } from '@/lib/supabase/types'

// Real-time subscription status
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error'

interface UseRealTimeReturn {
  connectionStatus: ConnectionStatus
  isConnected: boolean
  error: string | null
  forceReconnect: () => void
}

// Main real-time hook for shipment updates
export function useRealTimeShipments(
  filters?: {
    shipmentIds?: string[]
    customerIds?: string[]
    statuses?: string[]
  }
): UseRealTimeReturn {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected')
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createClient()
  const channelRef = useRef<RealtimeChannel | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const { updateShipment, removeShipment } = useShipmentStore()

  // Clean up function
  const cleanup = useCallback(() => {
    if (channelRef.current) {
      channelRef.current.unsubscribe()
      channelRef.current = null
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
  }, [])

  // Handle shipment changes
  const handleShipmentChange = useCallback(
    (payload: any) => {
      const { eventType, new: newRecord, old: oldRecord } = payload

      try {
        switch (eventType) {
          case 'INSERT':
            console.log('New shipment added:', newRecord)
            // Note: Full relation data would need to be fetched separately
            break

          case 'UPDATE':
            console.log('Shipment updated:', newRecord)
            
            // Apply filters if provided
            if (filters?.shipmentIds && !filters.shipmentIds.includes(newRecord.id)) {
              return
            }
            if (filters?.customerIds && !filters.customerIds.includes(newRecord.customer_id)) {
              return
            }
            if (filters?.statuses && !filters.statuses.includes(newRecord.status)) {
              return
            }

            // Update the shipment in the store
            updateShipment(newRecord.id, newRecord)
            break

          case 'DELETE':
            console.log('Shipment deleted:', oldRecord)
            removeShipment(oldRecord.id)
            break

          default:
            console.log('Unknown event type:', eventType)
        }
      } catch (err) {
        console.error('Error handling shipment change:', err)
        setError(err instanceof Error ? err.message : 'Error processing real-time update')
      }
    },
    [filters, updateShipment, removeShipment]
  )

  // Setup subscription
  const setupSubscription = useCallback(() => {
    cleanup()
    setConnectionStatus('connecting')
    setError(null)

    try {
      const channel = supabase
        .channel('shipments-changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'shipments',
          },
          handleShipmentChange
        )
        .subscribe((status) => {
          console.log('Subscription status:', status)
          
          switch (status) {
            case 'SUBSCRIBED':
              setConnectionStatus('connected')
              setError(null)
              break
            case 'CHANNEL_ERROR':
              setConnectionStatus('error')
              setError('Subscription channel error')
              // Auto-retry after 5 seconds
              reconnectTimeoutRef.current = setTimeout(setupSubscription, 5000)
              break
            case 'TIMED_OUT':
              setConnectionStatus('error')
              setError('Subscription timed out')
              // Auto-retry after 3 seconds
              reconnectTimeoutRef.current = setTimeout(setupSubscription, 3000)
              break
            case 'CLOSED':
              setConnectionStatus('disconnected')
              break
          }
        })

      channelRef.current = channel
    } catch (err) {
      console.error('Error setting up subscription:', err)
      setConnectionStatus('error')
      setError(err instanceof Error ? err.message : 'Failed to setup subscription')
    }
  }, [supabase, handleShipmentChange, cleanup])

  // Force reconnect
  const forceReconnect = useCallback(() => {
    console.log('Forcing reconnection...')
    setupSubscription()
  }, [setupSubscription])

  // Setup subscription on mount and cleanup on unmount
  useEffect(() => {
    setupSubscription()
    return cleanup
  }, [setupSubscription, cleanup])

  return {
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    error,
    forceReconnect,
  }
}

// Real-time hook for status history updates
export function useRealTimeStatusHistory(shipmentId: string) {
  const [statusHistory, setStatusHistory] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const channelRef = useRef<RealtimeChannel | null>(null)

  // Fetch initial status history
  const fetchStatusHistory = useCallback(async () => {
    try {
      setIsLoading(true)
      const { data, error } = await supabase
        .from('status_history')
        .select(`
          *,
          updated_by_profile:profiles!updated_by(
            user_id,
            full_name,
            email
          )
        `)
        .eq('shipment_id', shipmentId)
        .order('created_at', { ascending: false })

      if (error) throw error
      setStatusHistory(data || [])
    } catch (err) {
      console.error('Error fetching status history:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch status history')
    } finally {
      setIsLoading(false)
    }
  }, [supabase, shipmentId])

  // Handle status history changes
  const handleStatusHistoryChange = useCallback(
    (payload: any) => {
      const { eventType, new: newRecord } = payload

      // Only process events for the specific shipment
      if (newRecord.shipment_id !== shipmentId) {
        return
      }

      try {
        switch (eventType) {
          case 'INSERT':
            console.log('New status history entry:', newRecord)
            setStatusHistory(prev => [newRecord, ...prev])
            break

          case 'UPDATE':
            console.log('Status history entry updated:', newRecord)
            setStatusHistory(prev =>
              prev.map(item => item.id === newRecord.id ? newRecord : item)
            )
            break

          case 'DELETE':
            console.log('Status history entry deleted:', payload.old)
            setStatusHistory(prev => prev.filter(item => item.id !== payload.old.id))
            break
        }
      } catch (err) {
        console.error('Error handling status history change:', err)
      }
    },
    [shipmentId]
  )

  // Setup subscription
  useEffect(() => {
    if (!shipmentId) return

    fetchStatusHistory()

    const channel = supabase
      .channel(`status-history-${shipmentId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'status_history',
          filter: `shipment_id=eq.${shipmentId}`,
        },
        handleStatusHistoryChange
      )
      .subscribe()

    channelRef.current = channel

    return () => {
      channel.unsubscribe()
    }
  }, [shipmentId, fetchStatusHistory, handleStatusHistoryChange, supabase])

  return {
    statusHistory,
    isLoading,
    error,
    refetch: fetchStatusHistory,
  }
}

// Connection status indicator component data
export function useConnectionStatus() {
  const [status, setStatus] = useState<ConnectionStatus>('disconnected')
  const [lastConnected, setLastConnected] = useState<Date | null>(null)
  
  const supabase = createClient()

  useEffect(() => {
    // Monitor connection status
    const channel = supabase.channel('connection-test')
    
    const subscription = channel.subscribe((status) => {
      switch (status) {
        case 'SUBSCRIBED':
          setStatus('connected')
          setLastConnected(new Date())
          break
        case 'CHANNEL_ERROR':
        case 'TIMED_OUT':
          setStatus('error')
          break
        case 'CLOSED':
          setStatus('disconnected')
          break
      }
    })

    return () => {
      channel.unsubscribe()
    }
  }, [supabase])

  return {
    status,
    lastConnected,
    isOnline: status === 'connected',
  }
}

// Hook for real-time notifications
export function useRealTimeNotifications(userId: string) {
  const [notifications, setNotifications] = useState<any[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  const supabase = createClient()

  useEffect(() => {
    if (!userId) return

    const channel = supabase
      .channel(`notifications-${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `recipient_id=eq.${userId}`,
        },
        (payload) => {
          const { eventType, new: newRecord } = payload

          switch (eventType) {
            case 'INSERT':
              setNotifications(prev => [newRecord, ...prev])
              if (!newRecord.in_app_read) {
                setUnreadCount(prev => prev + 1)
              }
              break

            case 'UPDATE':
              setNotifications(prev =>
                prev.map(item => item.id === newRecord.id ? newRecord : item)
              )
              // Update unread count based on read status change
              if (newRecord.in_app_read) {
                setUnreadCount(prev => Math.max(0, prev - 1))
              }
              break
          }
        }
      )
      .subscribe()

    return () => {
      channel.unsubscribe()
    }
  }, [userId, supabase])

  const markAsRead = useCallback(
    async (notificationId: string) => {
      try {
        const { error } = await supabase
          .from('notifications')
          .update({ in_app_read: true, in_app_read_at: new Date().toISOString() })
          .eq('id', notificationId)

        if (error) throw error
      } catch (err) {
        console.error('Error marking notification as read:', err)
      }
    },
    [supabase]
  )

  return {
    notifications,
    unreadCount,
    markAsRead,
  }
}