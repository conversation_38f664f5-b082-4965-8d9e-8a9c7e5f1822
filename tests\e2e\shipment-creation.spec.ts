import { test, expect } from '@playwright/test'

test.describe('Shipment Creation Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication and navigate to create shipment page
    await page.goto('/shipments/create')
  })

  test('should complete full shipment creation workflow', async ({ page }) => {
    // Step 1: Select Transportation Mode
    await test.step('Select transportation mode', async () => {
      // Should show transport mode selection modal
      await expect(page.getByText('Choose Transportation Mode')).toBeVisible()
      
      // Select sea freight
      await page.getByRole('button', { name: /Sea Freight/i }).click()
      
      // Modal should close
      await expect(page.getByText('Choose Transportation Mode')).not.toBeVisible()
      
      // Should advance to step 2
      await expect(page.getByText('Stakeholder Information')).toBeVisible()
    })

    // Step 2: Select Stakeholders
    await test.step('Select stakeholders', async () => {
      // Select customer
      await page.getByLabel('Customer').click()
      await page.getByText('Test Customer Co.').click()
      
      // Select shipper
      await page.getByLabel('Shipper').click()
      await page.getByText('Test Shipper Ltd.').click()
      
      // Should show relationship recommendations
      await expect(page.getByText('Recommended based on history')).toBeVisible()
      
      // Select recommended consignee
      await page.getByText('Select Relationship').click()
      
      // Click Next
      await page.getByRole('button', { name: 'Next' }).click()
      
      // Should advance to step 3
      await expect(page.getByText('Route Configuration')).toBeVisible()
    })

    // Step 3: Configure Route
    await test.step('Configure route', async () => {
      // Select port of loading
      await page.getByLabel('Port of Loading').click()
      await page.getByText('Bangkok, Thailand (THBKK)').click()
      
      // Select port of discharge
      await page.getByLabel('Port of Discharge').click()
      await page.getByText('Hong Kong (HKHKG)').click()
      
      // Set dates
      await page.getByLabel('Closing Time').fill('2024-03-15T10:00')
      await page.getByLabel('ETD').fill('2024-03-20T10:00')
      await page.getByLabel('ETA').fill('2024-03-25T10:00')
      
      // Click Next
      await page.getByRole('button', { name: 'Next' }).click()
      
      // Should advance to step 4
      await expect(page.getByText('Container & Cargo')).toBeVisible()
    })

    // Step 4: Configure Container
    await test.step('Configure container', async () => {
      // Select container type
      await page.getByLabel('Container Type').click()
      await page.getByText('20ft Dry Container').click()
      
      // Set quantity
      await page.getByLabel('Quantity').fill('2')
      
      // Click Next
      await page.getByRole('button', { name: 'Next' }).click()
      
      // Should advance to step 5
      await expect(page.getByText('Cargo Information')).toBeVisible()
    })

    // Step 5: Enter Cargo Information
    await test.step('Enter cargo information', async () => {
      // Fill cargo description
      await page.getByLabel('Cargo Description').fill('Electronics and Components')
      
      // Fill weights
      await page.getByLabel('Gross Weight (kg)').fill('15000')
      await page.getByLabel('Net Weight (kg)').fill('14000')
      
      // Fill volume
      await page.getByLabel('Volume (m³)').fill('25.5')
      
      // Fill additional details
      await page.getByLabel('Incoterm').click()
      await page.getByText('FOB').click()
      
      await page.getByLabel('Freight Terms').click()
      await page.getByText('Collect').click()
      
      // Add remarks
      await page.getByLabel('Remarks').fill('Handle with care - fragile electronics')
      
      // Click Next
      await page.getByRole('button', { name: 'Next' }).click()
      
      // Should advance to step 6
      await expect(page.getByText('Document Upload')).toBeVisible()
    })

    // Step 6: Upload Documents
    await test.step('Upload documents', async () => {
      // Should show required documents list
      await expect(page.getByText('Commercial Invoice')).toBeVisible()
      await expect(page.getByText('Packing List')).toBeVisible()
      await expect(page.getByText('Bill of Lading')).toBeVisible()
      
      // Upload a test file (mock file upload)
      const fileInput = page.getByLabel('Upload Documents')
      await fileInput.setInputFiles({
        name: 'commercial-invoice.pdf',
        mimeType: 'application/pdf',
        buffer: Buffer.from('Mock PDF content')
      })
      
      // Should show uploaded file
      await expect(page.getByText('commercial-invoice.pdf')).toBeVisible()
      
      // Should show Submit button
      await expect(page.getByRole('button', { name: 'Create Shipment' })).toBeVisible()
    })

    // Final Step: Submit Shipment
    await test.step('Submit shipment', async () => {
      // Click Create Shipment
      await page.getByRole('button', { name: 'Create Shipment' }).click()
      
      // Should show loading state
      await expect(page.getByText('Creating...')).toBeVisible()
      
      // Should redirect to shipment details page
      await expect(page).toHaveURL(/\/shipments\/EXSEA-THBKK-\d{6}-\d{3}/)
      
      // Should show success message
      await expect(page.getByText('Shipment created successfully')).toBeVisible()
      
      // Should show shipment details
      await expect(page.getByText('Shipment Details')).toBeVisible()
      await expect(page.getByText('Sea Freight')).toBeVisible()
      await expect(page.getByText('Bangkok, Thailand → Hong Kong')).toBeVisible()
    })
  })

  test('should show validation errors for required fields', async ({ page }) => {
    await test.step('Try to proceed without selecting transport mode', async () => {
      // Close the transport mode modal without selecting
      await page.getByRole('button', { name: 'Close' }).click()
      
      // Try to click Next
      await page.getByRole('button', { name: 'Next' }).click()
      
      // Should show validation error
      await expect(page.getByText('Transportation mode is required')).toBeVisible()
    })

    await test.step('Try to proceed without filling required stakeholder fields', async () => {
      // Select transport mode first
      await page.getByRole('button', { name: /Sea Freight/i }).click()
      
      // Try to proceed to next step without selecting stakeholders
      await page.getByRole('button', { name: 'Next' }).click()
      
      // Should show validation errors
      await expect(page.getByText('Customer is required')).toBeVisible()
      await expect(page.getByText('Shipper is required')).toBeVisible()
    })
  })

  test('should save draft and restore form data', async ({ page }) => {
    await test.step('Fill partial form data', async () => {
      // Select transport mode
      await page.getByRole('button', { name: /Sea Freight/i }).click()
      
      // Fill some stakeholder information
      await page.getByLabel('Customer').click()
      await page.getByText('Test Customer Co.').click()
      
      // Save draft
      await page.getByRole('button', { name: 'Save Draft' }).click()
      
      // Should show draft saved message
      await expect(page.getByText('Draft saved successfully')).toBeVisible()
    })

    await test.step('Navigate away and return', async () => {
      // Navigate to shipments list
      await page.goto('/shipments')
      
      // Return to create page
      await page.goto('/shipments/create')
      
      // Should restore form data
      await expect(page.getByText('Continue from where you left off')).toBeVisible()
      
      // Click continue
      await page.getByRole('button', { name: 'Continue' }).click()
      
      // Should restore transport mode and customer selection
      await expect(page.getByText('Sea Freight')).toBeVisible()
      await expect(page.getByText('Test Customer Co.')).toBeVisible()
    })
  })

  test('should handle relationship intelligence recommendations', async ({ page }) => {
    await test.step('Set up customer-shipper selection', async () => {
      // Select transport mode
      await page.getByRole('button', { name: /Sea Freight/i }).click()
      
      // Select customer and shipper
      await page.getByLabel('Customer').click()
      await page.getByText('Test Customer Co.').click()
      
      await page.getByLabel('Shipper').click()
      await page.getByText('Test Shipper Ltd.').click()
    })

    await test.step('View relationship recommendations', async () => {
      // Should show relationship analysis
      await expect(page.getByText('Relationship Analysis')).toBeVisible()
      await expect(page.getByText('Partnership Strength: 85%')).toBeVisible()
      await expect(page.getByText('12 shipments in the last 6 months')).toBeVisible()
      
      // Should show recommended consignee and notify party
      await expect(page.getByText('Recommended Consignee')).toBeVisible()
      await expect(page.getByText('ABC Trading Ltd.')).toBeVisible()
      
      // Click to select recommended relationship
      await page.getByText('Use Recommendation').click()
      
      // Should auto-fill consignee and notify party
      await expect(page.getByDisplayValue('ABC Trading Ltd.')).toBeVisible()
    })

    await test.step('View route recommendations', async () => {
      // Continue to route step
      await page.getByRole('button', { name: 'Next' }).click()
      
      // Should show route recommendations based on history
      await expect(page.getByText('Frequently Used Routes')).toBeVisible()
      await expect(page.getByText('Bangkok → Hong Kong')).toBeVisible()
      await expect(page.getByText('95% success rate')).toBeVisible()
      
      // Click to use recommended route
      await page.getByText('Use This Route').click()
      
      // Should auto-fill ports
      await expect(page.getByDisplayValue('Bangkok, Thailand (THBKK)')).toBeVisible()
      await expect(page.getByDisplayValue('Hong Kong (HKHKG)')).toBeVisible()
    })
  })

  test('should handle document upload with validation', async ({ page }) => {
    await test.step('Navigate to document upload step', async () => {
      // Complete all previous steps quickly
      await page.getByRole('button', { name: /Sea Freight/i }).click()
      
      // Skip to document step
      await page.getByText('Documents').click() // Click on progress indicator
    })

    await test.step('Validate required documents', async () => {
      // Should show required documents based on transport mode
      await expect(page.getByText('Required Documents')).toBeVisible()
      await expect(page.getByText('Commercial Invoice')).toBeVisible()
      await expect(page.getByText('Bill of Lading')).toBeVisible()
      await expect(page.getByText('Packing List')).toBeVisible()
      
      // Try to submit without uploading required documents
      await page.getByRole('button', { name: 'Create Shipment' }).click()
      
      // Should show validation error
      await expect(page.getByText('Required documents are missing')).toBeVisible()
    })

    await test.step('Upload and validate documents', async () => {
      // Upload invalid file type
      const invalidFile = page.getByLabel('Upload Documents')
      await invalidFile.setInputFiles({
        name: 'document.txt',
        mimeType: 'text/plain',
        buffer: Buffer.from('Plain text content')
      })
      
      // Should show file type error
      await expect(page.getByText('Invalid file type. Please upload PDF files only.')).toBeVisible()
      
      // Upload valid file
      await invalidFile.setInputFiles({
        name: 'commercial-invoice.pdf',
        mimeType: 'application/pdf',
        buffer: Buffer.from('Mock PDF content')
      })
      
      // Should show upload progress
      await expect(page.getByText('Uploading...')).toBeVisible()
      
      // Should show uploaded file
      await expect(page.getByText('commercial-invoice.pdf')).toBeVisible()
      await expect(page.getByText('✓ Uploaded successfully')).toBeVisible()
    })
  })

  test('should handle error states gracefully', async ({ page }) => {
    await test.step('Handle network error during submission', async () => {
      // Mock network failure
      await page.route('**/api/shipments', route => {
        route.abort('failed')
      })
      
      // Complete form and try to submit
      await page.getByRole('button', { name: /Sea Freight/i }).click()
      // ... complete other steps quickly
      await page.getByRole('button', { name: 'Create Shipment' }).click()
      
      // Should show error message
      await expect(page.getByText('Failed to create shipment. Please try again.')).toBeVisible()
      
      // Should allow retry
      await expect(page.getByRole('button', { name: 'Retry' })).toBeVisible()
    })

    await test.step('Handle validation errors from server', async () => {
      // Mock server validation error
      await page.route('**/api/shipments', route => {
        route.fulfill({
          status: 400,
          body: JSON.stringify({
            error: 'Validation failed',
            details: {
              portOfLoading: 'Invalid port code',
              grossWeight: 'Weight must be greater than 0'
            }
          })
        })
      })
      
      // Try to submit
      await page.getByRole('button', { name: 'Create Shipment' }).click()
      
      // Should show field-specific errors
      await expect(page.getByText('Invalid port code')).toBeVisible()
      await expect(page.getByText('Weight must be greater than 0')).toBeVisible()
    })
  })

  test('should be responsive across different screen sizes', async ({ page }) => {
    await test.step('Test mobile layout', async () => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      
      // Should show mobile-friendly layout
      await expect(page.getByText('Create New Shipment')).toBeVisible()
      
      // Progress indicator should be adapted for mobile
      await expect(page.getByRole('list')).toBeVisible()
      
      // Form should be scrollable
      await page.getByRole('button', { name: /Sea Freight/i }).click()
      await expect(page.getByText('Stakeholder Information')).toBeVisible()
    })

    await test.step('Test tablet layout', async () => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 })
      
      // Should maintain good spacing and readability
      await expect(page.getByText('Create New Shipment')).toBeVisible()
      await expect(page.getByText('Stakeholder Information')).toBeVisible()
    })

    await test.step('Test desktop layout', async () => {
      // Set desktop viewport
      await page.setViewportSize({ width: 1200, height: 800 })
      
      // Should show full desktop layout
      await expect(page.getByText('Create New Shipment')).toBeVisible()
      await expect(page.getByText('Stakeholder Information')).toBeVisible()
    })
  })
})