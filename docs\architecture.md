# DYY Trading Fruit Export Management System Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for DYY Trading Fruit Export Management System, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for the modern Next.js/Supabase fullstack application where these concerns are increasingly intertwined.

### Starter Template or Existing Project

**Decision**: Custom Next.js + Supabase configuration (not using existing starter template)

Based on the PRD, this is a greenfield project with specific technology choices already defined:
- **Frontend**: Next.js 14+ App Router with TypeScript and Tailwind CSS
- **Backend**: Supabase (PostgreSQL with Row Level Security)
- **UI Components**: ShadCN UI component library
- **Mobile**: Progressive Web App (PWA) capabilities

The T3 Stack provides an excellent foundation, but since Supabase is specified instead of tRPC/Prisma, we'll create a custom starter configuration that combines Next.js 14+ App Router, Supabase client configuration, ShadCN UI setup, and TypeScript configuration optimized for Supabase types.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2024-08-13 | 1.0 | Initial architecture document creation | Claude Architect |

## High Level Architecture

### Technical Summary

The DYY Trading Fruit Export Management System follows a modern serverless-first architecture built on Next.js 14+ App Router with Supabase as the Backend-as-a-Service platform. The frontend leverages server-side rendering for optimal performance while maintaining client-side interactivity through real-time subscriptions. The backend utilizes Supabase's PostgreSQL database with Row Level Security policies for multi-tenant data access, complemented by Edge Functions for multi-channel notifications and document processing. The system integrates seamlessly with mobile Progressive Web App capabilities for offline driver operations, supporting the complete fruit export workflow from booking to delivery with sub-2 second page load times and 99.9% uptime requirements.

### Platform and Infrastructure Choice

**Platform:** Vercel (Frontend) + Supabase (Backend)
**Key Services:** Vercel Edge Functions, Supabase Database, Supabase Auth, Supabase Storage, Supabase Edge Functions, Supabase Real-time
**Deployment Host and Regions:** Vercel Global Edge Network (primary: Asia-Pacific for Thailand operations), Supabase Asia-Southeast (Singapore)

**Recommendation: Vercel + Supabase**

**Rationale**: 
- **Performance Alignment**: Vercel's edge network directly supports the <2s load time requirement
- **Development Velocity**: Essential for 6-month development timeline with 6 epics
- **Supabase Synergy**: Native integration reduces complexity for real-time features and authentication
- **Mobile PWA**: Vercel's edge deployment ideal for PWA service worker distribution

### Repository Structure

**Structure:** Monorepo with integrated frontend/backend
**Monorepo Tool:** Native Next.js project structure (no additional monorepo tool needed)
**Package Organization:** 
- `/app` - Next.js App Router pages and API routes
- `/components` - Shared UI components  
- `/lib` - Shared utilities and Supabase client
- `/types` - Shared TypeScript interfaces
- `/supabase` - Database schema, migrations, and Edge Functions

### High Level Architecture Diagram

```mermaid
graph TB
    subgraph "User Interfaces"
        WEB[Web Dashboard<br/>CS/Admin Users]
        PWA[Mobile PWA<br/>Drivers/Field Users]
        MOBILE[Mobile Web<br/>Customers/Stakeholders]
    end
    
    subgraph "Vercel Edge Network"
        EDGE[Edge Functions<br/>Middleware/Auth]
        CDN[Global CDN<br/>Static Assets]
        SSR[Next.js SSR<br/>App Router]
    end
    
    subgraph "Next.js Application"
        APP[App Router Pages]
        API[API Routes]
        COMP[Components/UI]
        PWA_SW[Service Worker<br/>Offline Support]
    end
    
    subgraph "Supabase Platform"
        AUTH[Supabase Auth<br/>Role-based Access]
        DB[(PostgreSQL<br/>RLS Policies)]
        STORAGE[Supabase Storage<br/>Documents/Images]
        REALTIME[Real-time<br/>Subscriptions]
        EDGE_FUNC[Edge Functions<br/>Notifications/Processing]
    end
    
    subgraph "External Services"
        EMAIL[Email Service<br/>SMTP]
        SMS[SMS Service<br/>Twilio]
        LINE[Line API<br/>Thai Market]
        WECHAT[WeChat API<br/>Chinese Market]
        PDF[PDF Generation<br/>Document Service]
    end
    
    WEB --> EDGE
    PWA --> EDGE  
    MOBILE --> EDGE
    EDGE --> SSR
    SSR --> APP
    APP --> API
    API --> AUTH
    API --> DB
    API --> STORAGE
    API --> REALTIME
    PWA_SW --> API
    EDGE_FUNC --> EMAIL
    EDGE_FUNC --> SMS
    EDGE_FUNC --> LINE
    EDGE_FUNC --> WECHAT
    EDGE_FUNC --> PDF
    DB --> EDGE_FUNC
    REALTIME --> APP
```

### Architectural Patterns

- **Jamstack Architecture:** Static site generation with serverless APIs for optimal performance and scalability suitable for international fruit export operations - _Rationale:_ Enables <2s load times globally while supporting dynamic real-time features through Supabase subscriptions

- **Progressive Web App Pattern:** Service worker-enabled mobile interface with offline capabilities - _Rationale:_ Critical for driver operations in areas with poor network connectivity during transportation

- **Row Level Security Pattern:** Database-level security policies for multi-tenant data access - _Rationale:_ Ensures data isolation between customers, carriers, and other stakeholders without application-level complexity

- **Real-time Subscription Pattern:** WebSocket-based status updates for live shipment tracking - _Rationale:_ Provides immediate visibility for the 90% real-time status tracking requirement

- **Edge Computing Pattern:** Global edge deployment for reduced latency - _Rationale:_ Supports international operations with consistent performance across regions

- **Repository Pattern:** Abstracted data access layer with Supabase client - _Rationale:_ Enables testing and provides consistent API across the application

- **Component-Based UI Pattern:** Reusable ShadCN UI components with TypeScript - _Rationale:_ Maintains consistency across desktop and mobile interfaces while supporting the dark blue theme requirements

## Tech Stack

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.3+ | Type-safe development | Essential for Supabase type generation and large codebase maintainability |
| Frontend Framework | Next.js | 14.2+ | React-based full-stack framework | App Router for optimal performance, built-in API routes, excellent Vercel integration |
| UI Component Library | ShadCN UI | Latest | Accessible component system | Headless UI with Tailwind, customizable for dark blue theme, accessibility compliance |
| State Management | Zustand | 4.5+ | Lightweight state management | Simple API, TypeScript-first, perfect for Supabase real-time integration |
| Backend Language | TypeScript | 5.3+ | Unified language across stack | Shared types between frontend and Supabase Edge Functions |
| Backend Framework | Supabase | Latest | Backend-as-a-Service platform | PostgreSQL with RLS, real-time subscriptions, built-in auth, file storage |
| API Style | Supabase Client | Latest | Type-safe database access | Auto-generated TypeScript types, real-time subscriptions, RLS integration |
| Database | PostgreSQL | 15+ (Supabase managed) | Relational database with advanced features | JSONB support for hybrid companies design, PostGIS for GPS coordinates |
| Cache | Vercel Edge Cache | Built-in | Global edge caching | Automatic caching for static assets and API responses |
| File Storage | Supabase Storage | Latest | Secure file storage with CDN | Image uploads for drivers, document storage with access policies |
| Authentication | Supabase Auth | Latest | Role-based authentication | 11 user types support, JWT tokens, RLS integration |
| Frontend Testing | Vitest + Testing Library | Latest | Fast unit testing | Vite-based testing for Next.js, React component testing |
| Backend Testing | Supabase CLI + Jest | Latest | Database and Edge Function testing | Local Supabase instance, API endpoint testing |
| E2E Testing | Playwright | 1.40+ | Cross-browser testing | PWA testing capabilities, mobile device emulation |
| Build Tool | Next.js | 14.2+ | Integrated build system | Built-in bundling, optimization, deployment ready |
| Bundler | Webpack | 5+ (Next.js integrated) | Module bundling | Optimized for PWA, code splitting, tree shaking |
| IaC Tool | Supabase CLI | Latest | Database migrations and deployment | Schema versioning, Edge Function deployment |
| CI/CD | GitHub Actions + Vercel | Latest | Automated deployment pipeline | Git-based deployments, preview environments |
| Monitoring | Vercel Analytics + Sentry | Latest | Performance and error monitoring | Core Web Vitals tracking, error reporting |
| Logging | Vercel Functions Logs + Supabase Logs | Built-in | Centralized logging | Request tracing, database query logs |
| CSS Framework | Tailwind CSS | 3.4+ | Utility-first CSS framework | Dark blue theme implementation, responsive design, ShadCN compatibility |

## Data Models

### Company

**Purpose:** Universal entity representing all stakeholders in the export process using a hybrid approach - base table with separate info tables for complex types (customers, carriers, factories) and JSONB metadata for simple types (shippers, consignees, notify parties, forwarder agents).

**Key Attributes:**
- id: string (UUID) - Primary identifier
- name: string - Company name
- company_type: CompanyType - Enum defining stakeholder role
- address: Address - Multi-language address with GPS coordinates
- contact_email: string - Primary communication email
- contact_phone: string - Primary phone number
- is_active: boolean - Operational status flag
- metadata: Record<string, any> | null - JSONB for simple company types only

#### TypeScript Interface
```typescript
export interface Company {
  id: string;
  name: string;
  company_type: 'customer' | 'carrier' | 'factory' | 'shipper' | 'consignee' | 'notify_party' | 'forwarder_agent';
  tax_id?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_fax?: string;
  contact_person_first_name?: string;
  contact_person_last_name?: string;
  address?: Address;
  gps_coordinates?: { lat: number; lng: number };
  metadata?: Record<string, any>; // Only for simple types
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Type-specific info (populated via joins)
  customer_info?: CustomerInfo;
  carrier_info?: CarrierInfo;
  factory_info?: FactoryInfo;
}
```

#### Relationships
- One-to-many with CustomerShipper (as customer or shipper)
- One-to-many with CustomerProduct (as customer)
- One-to-many with ConsigneeNotifyParty (as consignee or notify party)
- One-to-many with Shipments (multiple roles)
- One-to-many with Drivers (for carriers)

### Product

**Purpose:** Catalog of exportable fruit products with standardized measurement units and specifications for consistent pricing and packaging management.

**Key Attributes:**
- id: string (UUID) - Primary identifier
- name: string - Product name
- code: string - Internal product code
- category: string - Product classification
- hs_code: string - Harmonized System code for customs
- unit_of_measure_id: string - Reference to base measurement unit (KG)

#### TypeScript Interface
```typescript
export interface Product {
  id: string;
  name: string;
  code?: string;
  description?: string;
  category?: string;
  hs_code?: string;
  unit_of_measure_id: string;
  unit_of_measure?: UnitOfMeasure;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
```

#### Relationships
- Many-to-one with UnitOfMeasure
- One-to-many with CustomerProduct
- One-to-many with ShipmentProduct

### Shipment

**Purpose:** Core business entity representing export transactions with complete stakeholder coordination, transportation management, and status tracking throughout the fruit export lifecycle.

**Key Attributes:**
- id: string (UUID) - Primary identifier
- shipment_number: string - Auto-generated unique identifier (EX[Mode]-[Port]-YYMMDD-[Running])
- status: ShipmentStatus - Current lifecycle stage
- transportation_mode: 'sea' | 'land' | 'rail' - Transport method
- etd_date: string - Estimated Time of Departure
- eta_date: string - Estimated Time of Arrival
- closing_time: string - Booking closure deadline

#### TypeScript Interface
```typescript
export interface Shipment {
  id: string;
  shipment_number: string;
  invoice_number?: string;
  
  // Stakeholder references
  customer_id?: string;
  shipper_id?: string;
  consignee_id?: string;
  notify_party_id?: string;
  factory_id?: string;
  forwarder_agent_id?: string;
  
  // Port and vessel information
  origin_port_id?: string;
  destination_port_id?: string;
  liner?: string;
  vessel_name?: string;
  voyage_number?: string;
  booking_number?: string;
  
  // Dates and times
  etd_date?: string;
  eta_date?: string;
  closing_time?: string;
  cy_date?: string;
  
  // Product totals
  total_weight?: number;
  total_volume?: number;
  currency_code: 'USD' | 'THB' | 'CNY' | 'EUR';
  
  // Status and metadata
  status: ShipmentStatus;
  transportation_mode: 'sea' | 'land' | 'rail';
  notes?: string;
  
  // Related entities (populated via joins)
  customer?: Company;
  shipper?: Company;
  consignee?: Company;
  notify_party?: Company;
  factory?: Company;
  forwarder_agent?: Company;
  origin_port?: Port;
  destination_port?: Port;
  containers?: Container[];
  products?: ShipmentProduct[];
  status_history?: StatusHistory[];
  
  created_by?: string;
  created_at: string;
  updated_at: string;
}
```

#### Relationships
- Many-to-one with Company (multiple stakeholder roles)
- Many-to-one with Port (origin and destination)
- One-to-many with Container
- One-to-many with ShipmentProduct
- One-to-many with StatusHistory
- One-to-one with Transportation

### CustomerProduct

**Purpose:** Relationship entity managing customer-specific product pricing, packaging specifications, and default selections for intelligent shipment pre-population.

**Key Attributes:**
- customer_id: string - Reference to customer company
- product_id: string - Reference to product
- unit_price_cif: number - Cost, Insurance, Freight price per KG
- unit_price_fob: number - Free on Board price per KG
- packaging_type: 'Bag' | 'Plastic Basket' | 'Carton' - Package format
- gross_weight_per_package: number - Gross weight per package in KG
- net_weight_per_package: number - Net weight per package in KG
- is_default: boolean - Default product for this customer

#### TypeScript Interface
```typescript
export interface CustomerProduct {
  id: string;
  customer_id: string;
  product_id: string;
  customer_product_code?: string;
  is_default: boolean;
  is_active: boolean;
  
  // Pricing (per KG)
  unit_price_cif?: number;
  unit_price_fob?: number;
  currency_code: 'USD' | 'THB' | 'CNY' | 'EUR';
  
  // Packaging specifications
  standard_quantity?: number;
  packaging_type: 'Bag' | 'Plastic Basket' | 'Carton';
  gross_weight_per_package?: number;
  net_weight_per_package?: number;
  quality_grade?: string;
  
  // Logistics
  handling_instructions?: string;
  temperature_require?: string;
  vent_require?: string;
  shelf_life_days?: number;
  
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Related entities
  customer?: Company;
  product?: Product;
}
```

#### Relationships
- Many-to-one with Company (customer)
- Many-to-one with Product
- Unique constraint on (customer_id, product_id)

### Driver

**Purpose:** Transportation personnel linked to carrier companies with mobile interface access for status updates and photo documentation during fruit transportation.

**Key Attributes:**
- id: string (UUID) - Primary identifier
- carrier_id: string - Reference to carrier company
- driver_first_name: string - First name
- driver_last_name: string - Last name
- phone: string - Contact phone for coordination
- line_id: string - Line messaging app ID
- driver_picture_path: string - Profile photo for identification

#### TypeScript Interface
```typescript
export interface Driver {
  id: string;
  carrier_id: string;
  driver_first_name: string;
  driver_last_name: string;
  driver_code?: string;
  phone?: string;
  line_id?: string;
  driver_picture_path?: string;
  driver_picture_mime_type?: string;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Related entities
  carrier?: Company;
}
```

#### Relationships
- Many-to-one with Company (carrier type only)
- One-to-many with Transportation assignments

## API Specification

Based on the Supabase client API style from the Tech Stack, the system uses Supabase's auto-generated REST API with type-safe TypeScript client libraries. This approach provides real-time subscriptions, Row Level Security integration, and automatic TypeScript type generation.

### Supabase Client API

The API follows Supabase's client-side approach where the frontend directly queries the database through the Supabase client, with security enforced via Row Level Security policies rather than traditional API endpoints.

#### Core API Patterns

```typescript
// Supabase Client Configuration
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Shipment Management API
export class ShipmentService {
  // Create shipment with intelligent pre-population
  async createShipment(shipmentData: Omit<Shipment, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('shipments')
      .insert([shipmentData])
      .select(`
        *,
        customer:customer_id (*),
        shipper:shipper_id (*),
        consignee:consignee_id (*),
        notify_party:notify_party_id (*),
        factory:factory_id (*),
        forwarder_agent:forwarder_agent_id (*),
        origin_port:origin_port_id (*),
        destination_port:destination_port_id (*)
      `)
      .single()
    
    if (error) throw error
    return data
  }

  // Real-time shipment status updates
  subscribeToShipmentUpdates(shipmentId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`shipment-${shipmentId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'shipments',
        filter: `id=eq.${shipmentId}`
      }, callback)
      .subscribe()
  }
}

// Driver Mobile API
export class DriverService {
  // Update shipment status with photo and GPS
  async updateShipmentStatus(statusUpdate: {
    shipment_id: string
    status_to: string
    notes?: string
    location?: string
    latitude?: number
    longitude?: number
    photos: File[]
  }) {
    // Upload photos to Supabase Storage
    const photoUploads = await Promise.all(
      statusUpdate.photos.map(async (photo, index) => {
        const fileName = `${statusUpdate.shipment_id}-${Date.now()}-${index}.jpg`
        const { data, error } = await supabase.storage
          .from('status-photos')
          .upload(fileName, photo, {
            cacheControl: '3600',
            upsert: false
          })
        
        if (error) throw error
        return {
          path: data.path,
          url: supabase.storage.from('status-photos').getPublicUrl(data.path).data.publicUrl
        }
      })
    )

    // Create status history record and update shipment
    const { data: statusHistory, error: statusError } = await supabase
      .from('status_history')
      .insert([{
        shipment_id: statusUpdate.shipment_id,
        status_to: statusUpdate.status_to,
        notes: statusUpdate.notes,
        location: statusUpdate.location,
        latitude: statusUpdate.latitude,
        longitude: statusUpdate.longitude
      }])
      .select()
      .single()

    if (statusError) throw statusError
    return statusHistory
  }
}
```

## Components

Based on the architectural patterns, Next.js/Supabase tech stack, and data models, the system is organized into logical components that span both frontend and backend concerns for the fruit export management domain.

### Frontend Application Layer

**Responsibility:** Next.js App Router application providing web dashboard for CS/Admin users and mobile-responsive interfaces for customers and stakeholders.

**Key Interfaces:**
- React Server Components for optimal performance
- Client Components for real-time interactivity
- ShadCN UI components with dark blue theme
- Progressive Web App service worker registration

**Dependencies:** Supabase Client, Zustand State Management, Tailwind CSS

**Technology Stack:** Next.js 14+ App Router, TypeScript, ShadCN UI, Tailwind CSS

### Mobile PWA Layer

**Responsibility:** Progressive Web App providing offline-capable mobile interface specifically for drivers with photo capture, GPS location, and status update capabilities.

**Key Interfaces:**
- Service Worker for offline functionality
- Camera API for photo capture
- Geolocation API for GPS coordinates
- Background sync for queued status updates
- Push notifications for assignment alerts

**Dependencies:** Frontend Application Layer, Supabase Storage, Browser APIs

**Technology Stack:** Next.js PWA configuration, Service Worker, Web APIs

### Real-time Subscription Manager

**Responsibility:** Manages Supabase real-time subscriptions for live shipment status updates, assignment notifications, and multi-user coordination.

**Key Interfaces:**
- WebSocket connection management
- Subscription lifecycle handling
- State synchronization with Zustand
- Connection retry and error handling

**Dependencies:** Supabase Client, Frontend State Management

**Technology Stack:** Supabase Real-time, WebSocket API

### Relationship Intelligence Engine

**Responsibility:** Implements the intelligent pre-population logic for cascading selections (customer → shippers/products, consignee → notify parties) to achieve the 60% data entry reduction goal.

**Key Interfaces:**
- Customer defaults resolution API
- Relationship validation and management
- Default preference management
- Bulk relationship operations

**Dependencies:** Supabase Client, Database Relationship Tables

**Technology Stack:** TypeScript business logic, Supabase queries

### Authentication & Authorization Module

**Responsibility:** Role-based access control supporting 11 user types with Row Level Security policy enforcement and session management.

**Key Interfaces:**
- Supabase Auth integration
- Role-based route protection
- User profile management
- Company association handling

**Dependencies:** Supabase Auth, Next.js middleware

**Technology Stack:** Supabase Auth, Next.js App Router middleware

### Component Diagrams

```mermaid
graph TB
    subgraph "Frontend Layer"
        FE[Frontend Application Layer]
        PWA[Mobile PWA Layer]
        RTM[Real-time Subscription Manager]
        RIE[Relationship Intelligence Engine]
    end
    
    subgraph "Business Logic Layer"
        AUTH[Authentication & Authorization Module]
        MDM[Master Data Management System]
        SWE[Shipment Workflow Engine]
        DGS[Document Generation Service]
    end
    
    subgraph "Infrastructure Layer"
        MNS[Multi-Channel Notification System]
        FSM[File Storage & Processing Manager]
        DSM[Database Schema Manager]
    end
    
    subgraph "External Services"
        EMAIL[Email Service]
        SMS[SMS Service]
        LINE[Line API]
        WECHAT[WeChat API]
    end
    
    subgraph "Supabase Platform"
        DB[(Database)]
        STORAGE[Storage]
        EDGE[Edge Functions]
        REALTIME[Real-time]
        SAUTH[Supabase Auth]
    end
    
    FE --> AUTH
    FE --> MDM
    FE --> SWE
    PWA --> FSM
    PWA --> SWE
    RTM --> REALTIME
    RIE --> DB
    
    AUTH --> SAUTH
    MDM --> DB
    SWE --> DB
    DGS --> EDGE
    DGS --> STORAGE
    
    MNS --> EDGE
    MNS --> EMAIL
    MNS --> SMS
    MNS --> LINE
    MNS --> WECHAT
    
    FSM --> STORAGE
    DSM --> DB
    
    EDGE --> DB
    EDGE --> STORAGE
```

## External APIs

### SMS Service API

- **Purpose:** SMS notifications for urgent shipment updates, driver assignments, and emergency communications
- **Documentation:** https://www.twilio.com/docs/sms
- **Base URL(s):** https://api.twilio.com/2010-04-01/
- **Authentication:** API Key + Auth Token (Basic Auth)
- **Rate Limits:** 1 message per second per phone number, 100 messages per second per account

**Key Endpoints Used:**
- `POST /Accounts/{AccountSid}/Messages.json` - Send SMS messages to drivers and stakeholders

**Integration Notes:** Integrated via Supabase Edge Functions for server-side rate limiting and security.

### Line Messaging API

- **Purpose:** Primary communication channel for Thai market stakeholders
- **Documentation:** https://developers.line.biz/en/docs/messaging-api/
- **Base URL(s):** https://api.line.me/v2/bot/
- **Authentication:** Channel Access Token (Bearer Token)
- **Rate Limits:** 500 messages per minute, 10,000 messages per month (free tier)

**Key Endpoints Used:**
- `POST /message/push` - Send notifications to individual users
- `POST /message/multicast` - Send notifications to multiple users

**Integration Notes:** Essential for Thai operations where Line penetration exceeds 90%.

### WeChat Work API

- **Purpose:** Communication with Chinese market consignees and supply chain partners
- **Documentation:** https://developer.work.weixin.qq.com/document/
- **Base URL(s):** https://qyapi.weixin.qq.com/cgi-bin/
- **Authentication:** OAuth 2.0 with enterprise application credentials
- **Rate Limits:** 20,000 API calls per hour per enterprise application

**Key Endpoints Used:**
- `POST /message/send` - Send text and rich media messages
- `POST /media/upload` - Upload documents and images

**Integration Notes:** Requires enterprise WeChat Work account setup for B2B communications.

## Core Workflows

### Intelligent Shipment Creation Workflow

```mermaid
sequenceDiagram
    participant CS as CS Representative
    participant FE as Frontend App
    participant RIE as Relationship Intelligence Engine
    participant DB as Supabase Database
    participant SWE as Shipment Workflow Engine
    participant RTM as Real-time Manager
    
    CS->>FE: Select Transportation Mode (Sea/Land/Rail)
    FE->>FE: Configure workflow fields based on mode
    CS->>FE: Select Customer
    FE->>RIE: getCustomerDefaults(customerId)
    RIE->>DB: Query customer_shippers, customer_products
    DB-->>RIE: Return relationships with defaults
    RIE-->>FE: {defaultShipper, availableShippers, defaultProduct, availableProducts}
    FE->>FE: Pre-populate cascading selections
    
    CS->>FE: Complete mandatory fields (Factory, Forwarder Agent, ETD/ETA/Closing Time, Ports)
    CS->>FE: Submit shipment creation
    
    FE->>SWE: createShipment(shipmentData)
    SWE->>DB: Generate shipment number (EX[Mode]-[Port]-YYMMDD-[Running])
    SWE->>DB: Insert shipment record
    SWE->>DB: Auto-generate containers based on product quantities
    SWE->>RTM: Broadcast shipment creation event
    RTM->>RTM: Notify relevant stakeholders
    FE->>CS: Display shipment confirmation with auto-generated number
```

### Driver Mobile Status Update Workflow

```mermaid
sequenceDiagram
    participant DR as Driver
    participant PWA as Mobile PWA
    participant SW as Service Worker
    participant FSM as File Storage Manager
    participant DB as Supabase Database
    participant RTM as Real-time Manager
    participant MNS as Notification System
    
    DR->>PWA: Open assigned shipment
    PWA->>PWA: Check network connectivity
    
    alt Online Mode
        PWA->>DB: Fetch current shipment status
        DB-->>PWA: Return status and workflow options
    else Offline Mode
        PWA->>SW: Retrieve cached shipment data
        SW-->>PWA: Return offline shipment info
    end
    
    DR->>PWA: Select next status and capture photos
    DR->>PWA: Auto-capture GPS coordinates
    DR->>PWA: Submit status update
    
    alt Online Mode
        PWA->>FSM: Upload photos to Supabase Storage
        PWA->>DB: Create status_history record
        PWA->>DB: Update shipment status
        DB->>RTM: Trigger real-time status change event
        RTM->>MNS: Send stakeholder notifications
    else Offline Mode
        PWA->>SW: Queue status update locally
        SW->>SW: Store photos and data for sync
        PWA->>DR: Show "Queued for sync" confirmation
    end
```

## Database Schema

### Core Schema Implementation

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Enums and Types
CREATE TYPE role_type AS ENUM (
  'admin', 'cs', 'account', 'customer', 
  'carrier', 'driver', 'factory', 'shipper', 
  'consignee', 'notify_party', 'forwarder_agent'
);

CREATE TYPE company_type_enum AS ENUM (
  'customer', 'carrier', 'factory', 'shipper', 
  'consignee', 'notify_party', 'forwarder_agent'
);

CREATE TYPE transport_mode_enum AS ENUM ('sea', 'land', 'rail');

CREATE TYPE shipment_status_enum AS ENUM (
  'booking_confirmed', 'transport_assigned', 'driver_assigned',
  'empty_container_picked', 'arrived_at_factory',
  'loading_started', 'departed_factory', 'container_returned',
  'shipped', 'arrived', 'completed'
);

CREATE TYPE currency_enum AS ENUM ('THB', 'CNY', 'USD', 'EUR');
CREATE TYPE packaging_type_enum AS ENUM ('Bag', 'Plastic Basket', 'Carton');
CREATE TYPE document_type_enum AS ENUM (
  'booking_confirmation', 'invoice_fob', 'invoice_cif', 'contract', 
  'shipping_instruction', 'packing_list', 'certificate', 'other'
);

-- User Profiles (extends Supabase auth.users)
CREATE TABLE profiles (
  user_id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  first_name text,
  last_name text,
  phone_number text,
  line_id text,
  wechat_id text,
  role role_type NOT NULL,
  company_id uuid,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- HYBRID COMPANIES DESIGN: Base table with common fields
CREATE TABLE companies (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  company_type company_type_enum NOT NULL,
  
  -- Common fields for all company types
  tax_id text,
  contact_email text,
  contact_phone text,
  contact_fax text,
  contact_person_first_name text,
  contact_person_last_name text,
  
  -- Hybrid address storage: JSONB + dedicated point column
  address jsonb, -- {"street": {"th": "...", "en": "..."}, "coordinates": {"lat": 13.7563, "lng": 100.5018}}
  gps_coordinates point, -- Synced automatically via trigger for efficient geographic queries
  
  -- JSONB metadata ONLY for simple company types
  metadata jsonb,
  
  -- Common audit fields
  notes text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Constraint to ensure complex types have NULL metadata
  CONSTRAINT valid_metadata_structure CHECK (validate_company_metadata(company_type, metadata))
);

-- Main shipments table with all required fields
CREATE TABLE shipments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_number text UNIQUE NOT NULL,
  invoice_number text,
  
  -- Stakeholder references
  customer_id uuid REFERENCES companies(id),
  shipper_id uuid REFERENCES companies(id),
  consignee_id uuid REFERENCES companies(id),
  notify_party_id uuid REFERENCES companies(id),
  factory_id uuid REFERENCES companies(id),
  forwarder_agent_id uuid REFERENCES companies(id),
  
  -- Port and vessel information
  origin_port_id uuid REFERENCES ports(id),
  destination_port_id uuid REFERENCES ports(id),
  liner text,
  vessel_name text,
  voyage_number text,
  booking_number text,
  
  -- Dates and times
  etd_date timestamptz,
  eta_date timestamptz,
  closing_time timestamptz,
  cy_date timestamptz,
  
  -- Product Info (including missing fields from requirements)
  number_of_pallet integer,
  pallet_description text,
  ephyto_refno text,
  currency_code currency_enum DEFAULT 'USD',
  total_weight numeric(10,2),
  total_volume numeric(10,2),
  
  -- Status and metadata
  status shipment_status_enum DEFAULT 'booking_confirmed',
  transportation_mode transport_mode_enum DEFAULT 'sea',
  notes text,
  metadata jsonb,
  
  -- Audit fields
  created_by uuid REFERENCES profiles(user_id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Date validation constraints
  CONSTRAINT valid_date_sequence CHECK (
    closing_time IS NULL OR etd_date IS NULL OR closing_time <= etd_date
  ),
  CONSTRAINT valid_etd_eta CHECK (
    etd_date IS NULL OR eta_date IS NULL OR etd_date <= eta_date
  )
);

-- Customer-Product relationships with pricing and specifications
CREATE TABLE customer_products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  
  -- Product-specific details for this customer
  customer_product_code text,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  
  -- Pricing information (per KG as base unit)
  unit_price_cif numeric(12,4), -- Cost, Insurance, and Freight price per KG
  unit_price_fob numeric(12,4), -- Free on Board price per KG
  currency_code currency_enum DEFAULT 'USD',
  
  -- Physical specifications and packaging
  standard_quantity numeric(10,2), -- Standard order quantity (number of packages)
  unit_of_measure_id uuid REFERENCES units_of_measure(id),
  gross_weight_per_package numeric(8,4), -- Gross weight per 1 package in KG
  net_weight_per_package numeric(8,4), -- Net weight per 1 package in KG
  
  -- Quality and packaging
  quality_grade text,
  packaging_type packaging_type_enum NOT NULL,
  packaging_specifications jsonb,
  
  -- Logistics information
  handling_instructions text,
  temperature_require text,
  vent_require text,
  shelf_life_days integer,
  
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  -- Type validation
  CONSTRAINT customer_products_customer_type_check 
    CHECK (EXISTS (SELECT 1 FROM companies WHERE id = customer_id AND company_type = 'customer')),
  
  -- Unique relationship constraint
  UNIQUE(customer_id, product_id)
);

-- Status history with full audit trail
CREATE TABLE status_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  status_from shipment_status_enum,
  status_to shipment_status_enum NOT NULL,
  notes text,
  location text,
  latitude numeric(10,7),
  longitude numeric(10,7),
  updated_by uuid REFERENCES profiles(user_id),
  created_at timestamptz DEFAULT now()
);

-- Image uploads for status updates
CREATE TABLE status_images (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shipment_id uuid REFERENCES shipments(id) ON DELETE CASCADE,
  status_history_id uuid REFERENCES status_history(id),
  image_url text NOT NULL,
  image_path text NOT NULL,
  file_size integer,
  mime_type text,
  metadata jsonb,
  uploaded_by uuid REFERENCES profiles(user_id),
  created_at timestamptz DEFAULT now()
);
```

### Performance Indexes

```sql
-- Core performance indexes
CREATE INDEX idx_companies_type ON companies(company_type);
CREATE INDEX idx_companies_active ON companies(is_active) WHERE is_active = true;
CREATE INDEX idx_companies_address_gin ON companies USING GIN (address);
CREATE INDEX idx_companies_gps_coordinates ON companies USING GIST (gps_coordinates);

-- Shipment indexes
CREATE INDEX idx_shipments_status ON shipments(status);
CREATE INDEX idx_shipments_customer ON shipments(customer_id);
CREATE INDEX idx_shipments_created_at ON shipments(created_at);
CREATE INDEX idx_shipments_number ON shipments(shipment_number);
CREATE INDEX idx_shipments_transportation_mode ON shipments(transportation_mode);

-- Relationship indexes for intelligent pre-population
CREATE INDEX idx_customer_products_customer ON customer_products(customer_id) WHERE is_active = true;
CREATE INDEX idx_customer_products_default ON customer_products(customer_id, is_default) WHERE is_active = true;

-- Status tracking indexes
CREATE INDEX idx_status_history_shipment ON status_history(shipment_id);
CREATE INDEX idx_status_history_created ON status_history(created_at);
```

## Frontend Architecture

### Component Architecture

#### Component Organization

```text
src/
├── app/                          # Next.js App Router pages
│   ├── (auth)/                   # Auth route group
│   │   ├── login/               
│   │   └── register/            
│   ├── (dashboard)/              # Protected dashboard routes
│   │   ├── shipments/
│   │   │   ├── page.tsx         # Shipment list
│   │   │   ├── create/page.tsx  # Intelligent shipment creation
│   │   │   └── [id]/page.tsx    # Shipment details
│   │   ├── master-data/
│   │   │   ├── companies/
│   │   │   ├── products/
│   │   │   ├── drivers/
│   │   │   └── relationships/
│   │   └── documents/
│   ├── (mobile)/                 # Mobile-optimized routes
│   │   ├── driver/
│   │   │   ├── dashboard/
│   │   │   ├── shipments/
│   │   │   └── status-update/
│   │   └── customer/
│   ├── api/                      # API routes (minimal - mostly Supabase direct)
│   ├── layout.tsx               # Root layout with theme provider
│   └── page.tsx                 # Landing page
├── components/                   # Reusable UI components
│   ├── ui/                      # ShadCN UI components
│   ├── forms/                   # Domain-specific forms
│   │   ├── shipment-form/
│   │   ├── company-form/
│   │   └── driver-form/
│   ├── layout/                  # Layout components
│   ├── data-display/            # Data presentation components
│   └── mobile/                  # Mobile-specific components
├── hooks/                       # Custom React hooks
│   ├── use-supabase.ts         # Supabase client hook
│   ├── use-auth.ts             # Authentication state
│   ├── use-shipments.ts        # Shipment operations
│   ├── use-relationships.ts    # Relationship intelligence
│   └── use-real-time.ts        # Real-time subscriptions
├── lib/                        # Utility functions
│   ├── supabase/
│   │   ├── client.ts           # Supabase client configuration
│   │   ├── auth.ts             # Auth helpers
│   │   └── types.ts            # Generated types
│   └── utils.ts                # General utilities
├── stores/                     # Zustand state stores
│   ├── auth-store.ts           # Authentication state
│   ├── shipment-store.ts       # Shipment management
│   └── notification-store.ts   # Notifications
└── types/                      # TypeScript definitions
    ├── database.ts             # Supabase generated types
    └── shipment.ts             # Business entity types
```

### State Management Architecture

#### State Structure

```typescript
// Zustand store for shipment management
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { Shipment, ShipmentStatus } from '@/types/database'
import { supabase } from '@/lib/supabase/client'

interface ShipmentState {
  // Data
  shipments: Shipment[]
  currentShipment: Shipment | null
  filters: {
    status?: ShipmentStatus
    customer_id?: string
    transportation_mode?: string
    date_range?: { start: string; end: string }
  }
  
  // UI state
  isLoading: boolean
  error: string | null
  
  // Real-time subscriptions
  subscriptions: Map<string, any>
  
  // Actions
  fetchShipments: (filters?: any) => Promise<void>
  createShipment: (data: any) => Promise<Shipment>
  updateShipmentStatus: (id: string, status: ShipmentStatus) => Promise<void>
  subscribeToShipment: (shipmentId: string) => () => void
}
```

#### State Management Patterns

- **Zustand for Global State**: Lightweight state management with TypeScript support
- **React Query Alternative**: Supabase client handles caching and synchronization
- **Real-time Integration**: Direct Supabase subscription management within stores
- **Optimistic Updates**: Immediate UI updates with rollback on failure

### Routing Architecture

#### Protected Route Pattern

```typescript
// middleware.ts - Route protection
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextRequest, NextResponse } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Protect dashboard routes
  if (req.nextUrl.pathname.startsWith('/dashboard') || 
      req.nextUrl.pathname.startsWith('/mobile')) {
    if (!session) {
      return NextResponse.redirect(new URL('/login', req.url))
    }

    // Get user profile for role-based access
    const { data: profile } = await supabase
      .from('profiles')
      .select('role, company_id')
      .eq('user_id', session.user.id)
      .single()

    if (!profile) {
      return NextResponse.redirect(new URL('/login', req.url))
    }

    // Role-based route protection
    if (req.nextUrl.pathname.startsWith('/mobile/driver') && 
        profile.role !== 'driver') {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }
  }

  return res
}
```

### Frontend Services Layer

#### API Client Setup

```typescript
// lib/supabase/client.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Database } from '@/types/database'

export const supabase = createClientComponentClient<Database>()

// Enhanced client with error handling
export class SupabaseService {
  private client = supabase

  async withErrorHandling<T>(operation: () => Promise<{ data: T | null; error: any }>) {
    try {
      const result = await operation()
      if (result.error) {
        throw new Error(result.error.message)
      }
      return result.data
    } catch (error) {
      console.error('Supabase operation failed:', error)
      throw error
    }
  }

  // Real-time subscription helper
  subscribeToTable<T>(
    table: string,
    filter?: string,
    callback: (payload: any) => void
  ) {
    return this.client
      .channel(`${table}-changes`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table,
        filter
      }, callback)
      .subscribe()
  }
}
```

## Backend Architecture

### Service Architecture

#### Serverless Architecture

**Function Organization**

```text
supabase/
├── functions/
│   ├── notifications/
│   │   ├── email-notification/
│   │   │   ├── index.ts              # Email sending Edge Function
│   │   │   └── templates/            # Email templates
│   │   ├── sms-notification/
│   │   │   └── index.ts              # SMS/Line/WeChat notifications
│   │   └── in-app-notification/
│   │       └── index.ts              # Real-time in-app notifications
│   ├── document-processing/
│   │   ├── pdf-generator/
│   │   │   └── index.ts              # PDF generation for reports
│   │   └── file-upload-handler/
│   │       └── index.ts              # File upload processing
│   ├── data-sync/
│   │   ├── relationship-engine/
│   │   │   └── index.ts              # Relationship intelligence
│   │   └── external-api-sync/
│   │       └── index.ts              # Third-party integrations
│   └── mobile-sync/
│       ├── offline-sync/
│       │   └── index.ts              # PWA offline synchronization
│       └── status-update/
│           └── index.ts              # Driver status updates
├── migrations/                        # Database migrations
│   ├── 20240101000000_initial_schema.sql
│   ├── 20240102000000_companies_rls.sql
│   ├── 20240103000000_relationship_functions.sql
│   └── 20240104000000_performance_indexes.sql
└── seed.sql                          # Development seed data
```

**Function Template**

```typescript
// supabase/functions/notifications/email-notification/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface EmailRequest {
  to: string[]
  template: string
  data: Record<string, any>
  shipment_id?: string
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { to, template, data, shipment_id }: EmailRequest = await req.json()

    // Validate request
    if (!to || !template) {
      throw new Error('Missing required fields: to, template')
    }

    // Get email template
    const emailTemplate = await getEmailTemplate(template, data)
    
    // Send email via configured provider (SendGrid, AWS SES, etc.)
    const result = await sendEmail({
      to,
      subject: emailTemplate.subject,
      html: emailTemplate.html,
      text: emailTemplate.text
    })

    // Log notification activity
    if (shipment_id) {
      await supabase
        .from('notification_logs')
        .insert({
          shipment_id,
          notification_type: 'email',
          recipient: to.join(','),
          template,
          status: 'sent',
          provider_response: result
        })
    }

    return new Response(
      JSON.stringify({ success: true, message_id: result.messageId }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

async function getEmailTemplate(templateName: string, data: Record<string, any>) {
  // Load and process email template with data interpolation
  // Support for multiple languages based on user preferences
  return {
    subject: `Shipment Update: ${data.shipment_number}`,
    html: `<html>...</html>`,
    text: `Text version...`
  }
}

async function sendEmail(emailData: any) {
  // Integration with email service provider
  // Return provider response with message ID for tracking
  return { messageId: 'generated-id' }
}
```

### Database Architecture

#### Schema Design

```sql
-- Row Level Security Policies for Multi-Tenant Access

-- Profiles RLS: Users can only see their own profile
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = user_id);

-- Companies RLS: Role-based access with company affiliation
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins see all companies" ON companies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE user_id = auth.uid() 
      AND role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Users see affiliated companies" ON companies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.user_id = auth.uid()
      AND (
        p.company_id = companies.id OR
        p.role IN ('admin', 'staff')
      )
    )
  );

-- Shipments RLS: Complex multi-role access control
ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Shipment access by role" ON shipments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.user_id = auth.uid()
      AND (
        -- Admins and staff see all
        p.role IN ('admin', 'staff') OR
        -- Customers see their shipments
        (p.role = 'customer' AND p.company_id = shipments.customer_id) OR
        -- Drivers see assigned shipments
        (p.role = 'driver' AND p.user_id IN (
          SELECT driver_id FROM shipment_drivers sd 
          WHERE sd.shipment_id = shipments.id
        )) OR
        -- Factory users see relevant shipments
        (p.role = 'factory_user' AND p.company_id = shipments.factory_id) OR
        -- Forwarders see their shipments
        (p.role = 'forwarder' AND p.company_id = shipments.forwarder_agent_id)
      )
    )
  );
```

#### Data Access Layer

```typescript
// lib/repositories/shipment-repository.ts
import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/database'
import { SupabaseService } from '@/lib/supabase/client'

type Shipment = Database['public']['Tables']['shipments']['Row']
type ShipmentInsert = Database['public']['Tables']['shipments']['Insert']
type ShipmentUpdate = Database['public']['Tables']['shipments']['Update']

export class ShipmentRepository extends SupabaseService {
  
  async getShipments(filters?: {
    status?: string
    customer_id?: string
    date_range?: { start: string; end: string }
  }) {
    let query = this.client
      .from('shipments')
      .select(`
        *,
        customer:companies!customer_id(name, company_type),
        shipper:companies!shipper_id(name),
        consignee:companies!consignee_id(name),
        factory:companies!factory_id(name),
        origin_port:ports!origin_port_id(port_name, country),
        destination_port:ports!destination_port_id(port_name, country),
        status_history(status_to, created_at, notes),
        shipment_items(quantity, unit_price, product:products(name))
      `)
      .order('created_at', { ascending: false })

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    
    if (filters?.customer_id) {
      query = query.eq('customer_id', filters.customer_id)
    }
    
    if (filters?.date_range) {
      query = query
        .gte('created_at', filters.date_range.start)
        .lte('created_at', filters.date_range.end)
    }

    return this.withErrorHandling(() => query)
  }

  async createShipment(shipmentData: ShipmentInsert) {
    return this.withErrorHandling(async () => {
      // Create shipment with initial status
      const shipment = await this.client
        .from('shipments')
        .insert(shipmentData)
        .select()
        .single()

      // Create initial status history
      if (shipment.data) {
        await this.client
          .from('status_history')
          .insert({
            shipment_id: shipment.data.id,
            status_to: 'booking_confirmed',
            notes: 'Shipment created'
          })
      }

      return shipment
    })
  }

  async updateShipmentStatus(
    shipmentId: string, 
    newStatus: string, 
    notes?: string,
    location?: { latitude: number; longitude: number }
  ) {
    return this.withErrorHandling(async () => {
      // Get current status
      const { data: currentShipment } = await this.client
        .from('shipments')
        .select('status')
        .eq('id', shipmentId)
        .single()

      if (!currentShipment) {
        throw new Error('Shipment not found')
      }

      // Update shipment status
      const shipment = await this.client
        .from('shipments')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', shipmentId)
        .select()
        .single()

      // Record status history
      await this.client
        .from('status_history')
        .insert({
          shipment_id: shipmentId,
          status_from: currentShipment.status,
          status_to: newStatus,
          notes,
          latitude: location?.latitude,
          longitude: location?.longitude
        })

      // Trigger real-time notification
      await this.client.functions.invoke('notifications/status-change', {
        body: {
          shipment_id: shipmentId,
          old_status: currentShipment.status,
          new_status: newStatus,
          notes
        }
      })

      return shipment
    })
  }

  // Real-time subscription for shipment updates
  subscribeToShipmentUpdates(shipmentId: string, callback: (payload: any) => void) {
    return this.subscribeToTable(
      'shipments',
      `id=eq.${shipmentId}`,
      callback
    )
  }
}
```

### Authentication and Authorization

#### Auth Flow

```mermaid
sequenceDiagram
    participant User
    participant NextJS
    participant Supabase
    participant Database
    
    User->>NextJS: Login Request
    NextJS->>Supabase: Auth.signIn()
    Supabase->>Database: Validate credentials
    Database->>Supabase: User data
    Supabase->>NextJS: Session + JWT
    NextJS->>Database: Get user profile
    Database->>NextJS: Profile with role/company
    NextJS->>User: Redirect to dashboard
    
    Note over User,Database: Subsequent requests
    User->>NextJS: Protected route access
    NextJS->>Supabase: Verify session
    Supabase->>NextJS: Valid session
    NextJS->>Database: Query with RLS
    Database->>NextJS: Filtered data
    NextJS->>User: Authorized content
```

#### Middleware/Guards

```typescript
// lib/auth/auth-guard.ts
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

export async function requireAuth() {
  const supabase = createServerComponentClient({ cookies })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect('/login')
  }

  // Get user profile with role
  const { data: profile } = await supabase
    .from('profiles')
    .select('role, company_id, is_active')
    .eq('user_id', session.user.id)
    .single()

  if (!profile || !profile.is_active) {
    redirect('/login?error=inactive_account')
  }

  return { session, profile }
}

export async function requireRole(allowedRoles: string[]) {
  const { session, profile } = await requireAuth()
  
  if (!allowedRoles.includes(profile.role)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
  
  return { session, profile }
}

// Role-based component wrapper
export function withRole<T extends object>(
  Component: React.ComponentType<T>,
  allowedRoles: string[]
) {
  return async function ProtectedComponent(props: T) {
    await requireRole(allowedRoles)
    return <Component {...props} />
  }
}
```

## Unified Project Structure

```plaintext
dyy-trading-management/
├── .github/                          # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml                   # Test and build pipeline
│       ├── deploy-staging.yaml       # Staging deployment
│       └── deploy-production.yaml    # Production deployment
├── src/                              # Next.js application source
│   ├── app/                          # Next.js App Router
│   │   ├── (auth)/                   # Authentication routes
│   │   │   ├── login/
│   │   │   │   └── page.tsx
│   │   │   ├── register/
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx            # Auth layout
│   │   ├── (dashboard)/              # Protected dashboard routes
│   │   │   ├── shipments/
│   │   │   │   ├── page.tsx          # Shipment list with filters
│   │   │   │   ├── create/
│   │   │   │   │   └── page.tsx      # Intelligent creation form
│   │   │   │   └── [id]/
│   │   │   │       ├── page.tsx      # Shipment details
│   │   │   │       └── edit/page.tsx # Edit shipment
│   │   │   ├── master-data/
│   │   │   │   ├── companies/
│   │   │   │   │   ├── page.tsx      # Company management
│   │   │   │   │   └── [id]/page.tsx # Company details
│   │   │   │   ├── products/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── drivers/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── relationships/
│   │   │   │       └── page.tsx      # Customer-product relationships
│   │   │   ├── documents/
│   │   │   │   ├── page.tsx          # Document management
│   │   │   │   └── upload/page.tsx   # Document upload
│   │   │   ├── reports/
│   │   │   │   └── page.tsx          # Analytics dashboard
│   │   │   └── layout.tsx            # Dashboard layout with navigation
│   │   ├── (mobile)/                 # Mobile-optimized PWA routes
│   │   │   ├── driver/
│   │   │   │   ├── dashboard/
│   │   │   │   │   └── page.tsx      # Driver dashboard
│   │   │   │   ├── shipments/
│   │   │   │   │   ├── page.tsx      # Assigned shipments
│   │   │   │   │   └── [id]/
│   │   │   │   │       └── page.tsx  # Shipment tracking
│   │   │   │   └── status-update/
│   │   │   │       └── page.tsx      # Status update form
│   │   │   ├── customer/
│   │   │   │   ├── dashboard/
│   │   │   │   │   └── page.tsx      # Customer portal
│   │   │   │   └── shipments/
│   │   │   │       └── page.tsx      # Shipment tracking
│   │   │   └── layout.tsx            # Mobile layout
│   │   ├── api/                      # API routes (minimal - proxy to Supabase)
│   │   │   ├── auth/
│   │   │   │   └── callback/route.ts # Auth callback handler
│   │   │   ├── webhooks/
│   │   │   │   └── supabase/route.ts # Supabase webhooks
│   │   │   └── health/route.ts       # Health check endpoint
│   │   ├── globals.css               # Global styles with CSS variables
│   │   ├── layout.tsx                # Root layout with providers
│   │   ├── loading.tsx               # Global loading component
│   │   ├── error.tsx                 # Global error boundary
│   │   ├── not-found.tsx             # 404 page
│   │   └── page.tsx                  # Landing page
│   ├── components/                   # Reusable UI components
│   │   ├── ui/                       # ShadCN UI base components
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── select.tsx
│   │   │   ├── data-table.tsx
│   │   │   ├── form.tsx
│   │   │   ├── sheet.tsx
│   │   │   ├── toast.tsx
│   │   │   └── ...                   # Other ShadCN components
│   │   ├── forms/                    # Domain-specific form components
│   │   │   ├── shipment-form/
│   │   │   │   ├── shipment-form.tsx
│   │   │   │   ├── stakeholder-section.tsx
│   │   │   │   ├── product-section.tsx
│   │   │   │   └── logistics-section.tsx
│   │   │   ├── company-form/
│   │   │   │   ├── company-form.tsx
│   │   │   │   └── address-form.tsx
│   │   │   └── driver-form/
│   │   │       └── driver-form.tsx
│   │   ├── layout/                   # Layout components
│   │   │   ├── navigation/
│   │   │   │   ├── sidebar.tsx
│   │   │   │   ├── mobile-nav.tsx
│   │   │   │   └── breadcrumbs.tsx
│   │   │   ├── header.tsx
│   │   │   └── footer.tsx
│   │   ├── data-display/             # Data presentation components
│   │   │   ├── shipment-card.tsx
│   │   │   ├── status-badge.tsx
│   │   │   ├── timeline.tsx
│   │   │   ├── data-table-wrapper.tsx
│   │   │   └── charts/
│   │   │       ├── shipment-analytics.tsx
│   │   │       └── performance-metrics.tsx
│   │   ├── mobile/                   # Mobile-specific components
│   │   │   ├── status-update-form.tsx
│   │   │   ├── offline-indicator.tsx
│   │   │   ├── camera-capture.tsx
│   │   │   └── location-tracker.tsx
│   │   └── providers/                # Context providers
│   │       ├── theme-provider.tsx
│   │       ├── supabase-provider.tsx
│   │       └── notification-provider.tsx
│   ├── hooks/                        # Custom React hooks
│   │   ├── use-supabase.ts           # Supabase client management
│   │   ├── use-auth.ts               # Authentication state and actions
│   │   ├── use-shipments.ts          # Shipment CRUD operations
│   │   ├── use-companies.ts          # Company management
│   │   ├── use-relationships.ts      # Relationship intelligence engine
│   │   ├── use-real-time.ts          # Real-time subscriptions
│   │   ├── use-notifications.ts      # Notification management
│   │   ├── use-mobile.ts             # Mobile/PWA features
│   │   └── use-offline.ts            # Offline capabilities
│   ├── lib/                          # Utility functions and configurations
│   │   ├── supabase/
│   │   │   ├── client.ts             # Client-side Supabase client
│   │   │   ├── server.ts             # Server-side Supabase client
│   │   │   ├── auth.ts               # Auth helper functions
│   │   │   ├── middleware.ts         # Auth middleware
│   │   │   └── types.ts              # Generated TypeScript types
│   │   ├── validations/              # Zod validation schemas
│   │   │   ├── shipment.ts
│   │   │   ├── company.ts
│   │   │   └── auth.ts
│   │   ├── constants/                # Application constants
│   │   │   ├── enums.ts              # Database enums
│   │   │   ├── routes.ts             # Route definitions
│   │   │   └── theme.ts              # Theme configuration
│   │   ├── utils.ts                  # General utility functions
│   │   └── cn.ts                     # Class name utility
│   ├── stores/                       # Zustand state stores
│   │   ├── auth-store.ts             # Authentication state
│   │   ├── shipment-store.ts         # Shipment management state
│   │   ├── company-store.ts          # Company data state
│   │   ├── notification-store.ts     # Notifications state
│   │   └── ui-store.ts               # UI state (modals, sheets, etc.)
│   └── types/                        # TypeScript definitions
│       ├── database.ts               # Supabase generated types
│       ├── api.ts                    # API response types
│       ├── shipment.ts               # Extended shipment types
│       ├── company.ts                # Extended company types
│       └── global.d.ts               # Global type definitions
├── supabase/                         # Supabase configuration
│   ├── functions/                    # Edge Functions
│   │   ├── notifications/
│   │   │   ├── email-notification/
│   │   │   │   ├── index.ts
│   │   │   │   └── templates/
│   │   │   ├── sms-notification/
│   │   │   │   └── index.ts
│   │   │   └── in-app-notification/
│   │   │       └── index.ts
│   │   ├── document-processing/
│   │   │   ├── pdf-generator/
│   │   │   │   └── index.ts
│   │   │   └── file-upload-handler/
│   │   │       └── index.ts
│   │   ├── data-sync/
│   │   │   ├── relationship-engine/
│   │   │   │   └── index.ts
│   │   │   └── external-api-sync/
│   │   │       └── index.ts
│   │   ├── mobile-sync/
│   │   │   ├── offline-sync/
│   │   │   │   └── index.ts
│   │   │   └── status-update/
│   │   │       └── index.ts
│   │   └── _shared/                  # Shared utilities for functions
│   │       ├── cors.ts
│   │       ├── auth.ts
│   │       └── types.ts
│   ├── migrations/                   # Database migrations
│   │   ├── 20240101000000_initial_schema.sql
│   │   ├── 20240102000000_companies_rls.sql
│   │   ├── 20240103000000_relationship_functions.sql
│   │   ├── 20240104000000_performance_indexes.sql
│   │   ├── 20240105000000_notification_tables.sql
│   │   └── 20240106000000_audit_triggers.sql
│   ├── seed.sql                      # Development seed data
│   └── config.toml                   # Supabase configuration
├── public/                           # Static assets
│   ├── icons/                        # PWA icons
│   │   ├── icon-192x192.png
│   │   ├── icon-512x512.png
│   │   └── favicon.ico
│   ├── images/                       # Application images
│   │   └── logo.png
│   ├── manifest.json                 # PWA manifest
│   └── sw.js                         # Service worker (generated)
├── docs/                             # Documentation
│   ├── prd.md                        # Product Requirements Document
│   ├── data-model-requirements.md    # Database requirements
│   ├── architecture.md               # This document
│   ├── api-documentation.md          # API reference
│   ├── deployment-guide.md           # Deployment instructions
│   └── user-guides/                  # User documentation
│       ├── admin-guide.md
│       ├── driver-guide.md
│       └── customer-guide.md
├── tests/                            # Test files
│   ├── __mocks__/                    # Test mocks
│   ├── e2e/                          # End-to-end tests
│   │   ├── auth.spec.ts
│   │   ├── shipments.spec.ts
│   │   └── mobile-driver.spec.ts
│   ├── integration/                  # Integration tests
│   │   ├── api.test.ts
│   │   └── database.test.ts
│   └── unit/                         # Unit tests
│       ├── components/
│       ├── hooks/
│       └── utils/
├── scripts/                          # Build and utility scripts
│   ├── build.sh                      # Build script
│   ├── deploy.sh                     # Deployment script
│   ├── db-reset.sh                   # Database reset script
│   └── generate-types.sh             # Type generation script
├── .env.example                      # Environment variables template
├── .env.local                        # Local environment (gitignored)
├── .gitignore                        # Git ignore rules
├── .eslintrc.json                    # ESLint configuration
├── .prettierrc                       # Prettier configuration
├── components.json                   # ShadCN UI configuration
├── middleware.ts                     # Next.js middleware for auth
├── next.config.js                    # Next.js configuration with PWA
├── package.json                      # Dependencies and scripts
├── tailwind.config.js                # Tailwind CSS configuration
├── tsconfig.json                     # TypeScript configuration
├── playwright.config.ts              # Playwright test configuration
└── README.md                         # Project documentation
```