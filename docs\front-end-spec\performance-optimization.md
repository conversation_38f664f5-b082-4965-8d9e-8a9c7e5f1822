# Performance Optimization

## Loading Strategy

### Progressive Loading Implementation
```tsx
// Lazy Loading for Route Components
const AdminDashboard = lazy(() => import('./pages/AdminDashboard'));
const ShipmentManagement = lazy(() => import('./pages/ShipmentManagement'));
const MasterDataManagement = lazy(() => import('./pages/MasterDataManagement'));
const DriverMobile = lazy(() => import('./pages/DriverMobile'));

// Code splitting with Suspense boundaries
const AppRoutes = () => (
  <Router>
    <Routes>
      <Route path="/admin/*" element={
        <Suspense fallback={<AdminDashboardSkeleton />}>
          <AdminDashboard />
        </Suspense>
      } />
      
      <Route path="/shipments/*" element={
        <Suspense fallback={<ShipmentManagementSkeleton />}>
          <ShipmentManagement />
        </Suspense>
      } />
      
      <Route path="/driver/*" element={
        <Suspense fallback={<DriverMobileSkeleton />}>
          <DriverMobile />
        </Suspense>
      } />
    </Routes>
  </Router>
);
```

### Image Optimization for Mobile
```tsx
// Optimized Image Component for Status Photos
const OptimizedImage = ({ 
  src, 
  alt, 
  className, 
  priority = false,
  quality = 75 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  
  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-primary-700 animate-pulse rounded-lg" />
      )}
      
      <img
        src={src}
        alt={alt}
        className={`${className} transition-opacity duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
        loading={priority ? 'eager' : 'lazy'}
        onLoad={() => setIsLoading(false)}
        onError={() => setError(true)}
        style={{
          maxWidth: '100%',
          height: 'auto',
        }}
      />
      
      {error && (
        <div className="absolute inset-0 bg-primary-700 flex items-center justify-center rounded-lg">
          <PhotoIcon className="w-8 h-8 text-neutral-400" />
        </div>
      )}
    </div>
  );
};

// Progressive Enhancement for Photo Upload
const PhotoUploadComponent = () => {
  const [photos, setPhotos] = useState([]);
  const [uploading, setUploading] = useState(false);
  
  const handlePhotoCapture = async (file) => {
    // Create thumbnail immediately for UI responsiveness
    const thumbnail = await createThumbnail(file, { width: 150, height: 150 });
    
    // Add to UI immediately with loading state
    const tempPhoto = {
      id: Date.now(),
      file,
      thumbnail,
      uploading: true,
      uploaded: false
    };
    
    setPhotos(prev => [...prev, tempPhoto]);
    
    // Upload in background
    try {
      const uploadedUrl = await uploadToSupabase(file);
      setPhotos(prev => prev.map(photo => 
        photo.id === tempPhoto.id 
          ? { ...photo, url: uploadedUrl, uploading: false, uploaded: true }
          : photo
      ));
    } catch (error) {
      setPhotos(prev => prev.map(photo => 
        photo.id === tempPhoto.id 
          ? { ...photo, uploading: false, error: true }
          : photo
      ));
    }
  };
  
  return (
    <div className="space-y-4">
      <Button 
        onClick={activateCamera}
        className="w-full bg-accent-500 hover:bg-accent-400 min-h-[44px]"
        disabled={uploading || photos.length >= 5}
      >
        <CameraIcon className="w-5 h-5 mr-2" />
        Capture Photo ({photos.length}/5)
      </Button>
      
      <div className="grid grid-cols-3 gap-2">
        {photos.map(photo => (
          <div key={photo.id} className="relative aspect-square">
            <OptimizedImage 
              src={photo.thumbnail} 
              alt="Captured photo"
              className="w-full h-full object-cover rounded-lg"
            />
            
            {photo.uploading && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
                <Loader2 className="w-6 h-6 text-white animate-spin" />
              </div>
            )}
            
            {photo.uploaded && (
              <div className="absolute top-1 right-1">
                <CheckCircleIcon className="w-5 h-5 text-green-500" />
              </div>
            )}
            
            {photo.error && (
              <div className="absolute top-1 right-1">
                <XCircleIcon className="w-5 h-5 text-red-500" />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Offline Capability Implementation
```tsx
// Service Worker Integration for PWA
const useOfflineSync = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pendingUpdates, setPendingUpdates] = useState([]);
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // Register service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          console.log('SW registered:', registration);
        })
        .catch(error => {
          console.log('SW registration failed:', error);
        });
    }
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  const queueUpdate = useCallback((updateData) => {
    const queuedUpdate = {
      id: Date.now(),
      data: updateData,
      timestamp: new Date().toISOString(),
      type: 'status_update'
    };
    
    setPendingUpdates(prev => [...prev, queuedUpdate]);
    
    // Store in localStorage for persistence
    const stored = JSON.parse(localStorage.getItem('pendingUpdates') || '[]');
    localStorage.setItem('pendingUpdates', JSON.stringify([...stored, queuedUpdate]));
    
    // If online, try to sync immediately
    if (isOnline) {
      syncPendingUpdates();
    }
  }, [isOnline]);
  
  const syncPendingUpdates = useCallback(async () => {
    if (!isOnline || pendingUpdates.length === 0) return;
    
    const stored = JSON.parse(localStorage.getItem('pendingUpdates') || '[]');
    
    for (const update of stored) {
      try {
        await api.updateShipmentStatus(update.data);
        
        // Remove from pending after successful sync
        const remaining = stored.filter(item => item.id !== update.id);
        localStorage.setItem('pendingUpdates', JSON.stringify(remaining));
        setPendingUpdates(remaining);
        
        // Show success notification
        toast.success(`Status update synced: ${update.data.shipmentNumber}`);
        
      } catch (error) {
        console.error('Sync failed for update:', update.id, error);
        // Keep in queue for retry
      }
    }
  }, [isOnline, pendingUpdates]);
  
  // Auto-sync when coming back online
  useEffect(() => {
    if (isOnline) {
      syncPendingUpdates();
    }
  }, [isOnline, syncPendingUpdates]);
  
  return {
    isOnline,
    pendingUpdates,
    queueUpdate,
    syncPendingUpdates
  };
};

// Offline-First Status Update Component
const OfflineStatusUpdate = () => {
  const { isOnline, queueUpdate } = useOfflineSync();
  const [statusData, setStatusData] = useState({});
  
  const handleSubmit = async (formData) => {
    if (isOnline) {
      try {
        await api.updateShipmentStatus(formData);
        toast.success('Status updated successfully');
        router.back();
      } catch (error) {
        toast.error('Update failed, queuing for later sync');
        queueUpdate(formData);
      }
    } else {
      queueUpdate(formData);
      toast.info('Update queued - will sync when online');
      router.back();
    }
  };
  
  return (
    <div className="space-y-4">
      {!isOnline && (
        <Alert className="bg-warning-500/10 border-warning-500">
          <WifiOffIcon className="w-4 h-4" />
          <AlertTitle>Offline Mode</AlertTitle>
          <AlertDescription>
            Updates will be queued and synced when connection is restored
          </AlertDescription>
        </Alert>
      )}
      
      <StatusUpdateForm 
        onSubmit={handleSubmit}
        offlineMode={!isOnline}
      />
    </div>
  );
};
```

---
