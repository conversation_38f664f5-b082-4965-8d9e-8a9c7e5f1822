'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Package, Check, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { CustomerProductRelationship } from '@/hooks/use-shipment-relationships'

interface ProductSelectorCardsProps {
  /**
   * Available products for the selected customer
   */
  products: CustomerProductRelationship[]
  
  /**
   * Currently selected product ID
   */
  selectedProductId: string | null
  
  /**
   * Callback when product is selected
   */
  onProductSelect: (productId: string, product: CustomerProductRelationship) => void
  
  /**
   * Customer name for context display
   */
  customerName?: string
  
  /**
   * Disabled state
   */
  disabled?: boolean
  
  /**
   * Loading state
   */
  loading?: boolean
  
  /**
   * Show detailed product information
   */
  showDetails?: boolean
  
  /**
   * Compact mode for smaller displays
   */
  compact?: boolean
}

export function ProductSelectorCards({
  products,
  selectedProductId,
  onProductSelect,
  customerName,
  disabled = false,
  loading = false,
  showDetails = true,
  compact = false,
}: ProductSelectorCardsProps) {
  const [hoveredProductId, setHoveredProductId] = useState<string | null>(null)

  const handleProductSelect = useCallback(
    (productId: string, product: CustomerProductRelationship) => {
      if (disabled || loading) return
      onProductSelect(productId, product)
    },
    [onProductSelect, disabled, loading]
  )

  // Auto-select single product
  const handleAutoSelect = useCallback(() => {
    if (products.length === 1 && !selectedProductId) {
      const singleProduct = products[0]
      handleProductSelect(singleProduct.product_id, singleProduct)
    }
  }, [products, selectedProductId, handleProductSelect])

  // Auto-select when component mounts if single product
  useState(() => {
    if (products.length === 1 && !selectedProductId) {
      const singleProduct = products[0]
      setTimeout(() => {
        handleProductSelect(singleProduct.product_id, singleProduct)
      }, 100)
    }
  })

  if (loading) {
    return (
      <div className="space-y-3">
        <Label className="text-slate-200 text-base font-medium">
          Loading products...
        </Label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map(i => (
            <div
              key={i}
              className="h-32 bg-slate-800/50 border border-slate-600 rounded-lg animate-pulse"
            />
          ))}
        </div>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-8">
        <Package className="h-12 w-12 text-slate-500 mx-auto mb-3" />
        <p className="text-slate-400">
          No product relationships configured for this customer.
        </p>
        <p className="text-slate-500 text-sm mt-1">
          Please set up customer-product relationships in Master Data.
        </p>
      </div>
    )
  }

  // Single product case - auto-selected with info display
  if (products.length === 1) {
    const product = products[0]
    const isSelected = selectedProductId === product.product_id

    return (
      <div className="space-y-3">
        <Label className="text-slate-200 text-base font-medium">
          Product for {customerName} (Auto-selected)
        </Label>
        <Card
          className={cn(
            'cursor-default transition-all duration-200 bg-slate-800',
            isSelected
              ? 'border-green-400 bg-green-500/10 ring-2 ring-green-400/50'
              : 'border-slate-600'
          )}
        >
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div
                  className={cn(
                    'p-2 rounded-lg',
                    isSelected ? 'bg-green-500/20' : 'bg-slate-700'
                  )}
                >
                  <Package 
                    className={cn(
                      'h-5 w-5',
                      isSelected ? 'text-green-400' : 'text-yellow-500'
                    )} 
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-white">
                      {product.product.name}
                    </h4>
                    <Badge
                      variant="outline"
                      className="text-xs bg-blue-500/20 text-blue-300 border-blue-400"
                    >
                      Auto-selected
                    </Badge>
                    {product.is_default && (
                      <Badge
                        variant="outline"
                        className="text-xs bg-green-500/20 text-green-300 border-green-400"
                      >
                        Default
                      </Badge>
                    )}
                  </div>
                  <p className="text-slate-400 text-sm mt-1">
                    Code: {product.product.code}
                  </p>
                  {showDetails && (
                    <div className="flex gap-4 mt-2 text-sm">
                      <span className="text-slate-300">
                        CIF: ${product.unit_price_cif} {product.currency_code}
                      </span>
                      <span className="text-slate-300">
                        FOB: ${product.unit_price_fob} {product.currency_code}
                      </span>
                    </div>
                  )}
                  {product.quality_grade && (
                    <p className="text-slate-400 text-xs mt-1">
                      Quality: {product.quality_grade}
                    </p>
                  )}
                </div>
              </div>
              {isSelected && (
                <div className="p-1 bg-green-500 rounded-full">
                  <Check className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Multiple products case - card selection interface
  return (
    <div className="space-y-4">
      <Label className="text-slate-200 text-base font-medium">
        Select Product for {customerName}
      </Label>
      <p className="text-slate-400 text-sm">
        Choose one product to include in this shipment.
      </p>
      
      <div className={cn(
        'grid gap-4',
        compact 
          ? 'grid-cols-1 lg:grid-cols-2' 
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      )}>
        {products.map(product => {
          const isSelected = selectedProductId === product.product_id
          const isHovered = hoveredProductId === product.product_id

          return (
            <Card
              key={product.id}
              className={cn(
                'cursor-pointer transition-all duration-200 hover:shadow-lg',
                isSelected
                  ? 'border-orange-400 bg-orange-500/10 ring-2 ring-orange-400/50'
                  : isHovered
                  ? 'border-slate-500 bg-slate-750'
                  : 'border-slate-600 bg-slate-800'
              )}
              onClick={() => handleProductSelect(product.product_id, product)}
              onMouseEnter={() => setHoveredProductId(product.product_id)}
              onMouseLeave={() => setHoveredProductId(null)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div
                      className={cn(
                        'p-2 rounded-lg transition-colors',
                        isSelected 
                          ? 'bg-orange-500/20' 
                          : isHovered 
                          ? 'bg-slate-600' 
                          : 'bg-slate-700'
                      )}
                    >
                      <Package 
                        className={cn(
                          'h-5 w-5 transition-colors',
                          isSelected 
                            ? 'text-orange-400' 
                            : 'text-yellow-500'
                        )} 
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-white">
                          {product.product.name}
                        </h4>
                        {product.is_default && (
                          <Badge
                            variant="outline"
                            className="text-xs bg-green-500/20 text-green-300 border-green-400"
                          >
                            Default
                          </Badge>
                        )}
                      </div>
                      <p className="text-slate-400 text-sm mt-1">
                        Code: {product.product.code}
                      </p>
                      {showDetails && !compact && (
                        <>
                          <div className="flex gap-4 mt-2 text-sm">
                            <span className="text-slate-300">
                              CIF: ${product.unit_price_cif} {product.currency_code}
                            </span>
                          </div>
                          <div className="text-sm">
                            <span className="text-slate-300">
                              FOB: ${product.unit_price_fob} {product.currency_code}
                            </span>
                          </div>
                        </>
                      )}
                      {product.quality_grade && (
                        <p className="text-slate-400 text-xs mt-1">
                          Quality: {product.quality_grade}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {/* Selection indicator */}
                  <div className="flex items-center space-x-2">
                    {isSelected ? (
                      <div className="p-1 bg-orange-500 rounded-full">
                        <Check className="h-4 w-4 text-white" />
                      </div>
                    ) : isHovered ? (
                      <div className="p-1 bg-slate-600 rounded-full">
                        <ChevronRight className="h-4 w-4 text-slate-300" />
                      </div>
                    ) : (
                      <div className="w-6 h-6" /> // Spacer to maintain alignment
                    )}
                  </div>
                </div>
                
                {/* Action buttons for selected card */}
                {isSelected && showDetails && (
                  <div className="mt-4 pt-3 border-t border-slate-600">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-green-400 font-medium">
                        ✓ Selected for shipment
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 px-3 text-xs bg-orange-500/10 border-orange-400/50 text-orange-300 hover:bg-orange-500/20"
                        disabled
                      >
                        Selected
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>
      
      {/* Selection summary */}
      {selectedProductId && (
        <div className="mt-4 p-3 bg-orange-500/10 border border-orange-400/30 rounded-lg">
          <div className="flex items-center space-x-2">
            <Check className="h-4 w-4 text-orange-400" />
            <span className="text-orange-300 text-sm font-medium">
              Product selected: {products.find(p => p.product_id === selectedProductId)?.product.name}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}