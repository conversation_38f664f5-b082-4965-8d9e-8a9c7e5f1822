'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useCustomerShippers } from './use-customer-shippers'
import { useCustomerProducts } from './use-customer-products'
import { useShipmentConsigneeIntegration } from './use-shipment-consignee-integration'
import { useCompaniesManagement } from './use-companies'
import { usePortsManagement } from './use-ports'
import type { ConsigneeNotifyParty } from '@/stores/consignee-notify-party-store'

// Extended relationship data types
export interface CustomerShipperRelationship {
  id: string
  customer_id: string
  shipper_id: string
  shipper: {
    id: string
    name: string
    contact_phone?: string
    contact_email?: string
  }
  is_default: boolean
  is_active: boolean
  priority_order?: number
  notes?: string
  created_at: string
}

export interface CustomerProductRelationship {
  id: string
  customer_id: string
  product_id: string
  product: {
    id: string
    name: string
    code: string
    unit_of_measure_id: string
  }
  is_default: boolean
  is_active: boolean
  unit_price_cif: number
  unit_price_fob: number
  currency_code: string
  quality_grade?: string
  standard_quantity?: number
  gross_weight_per_package?: number
  net_weight_per_package?: number
  packaging_type?: string
  notes?: string
  created_at: string
}

export interface ShipmentRelationshipData {
  // Customer relationships
  customerId?: string
  customerName?: string

  // Shipper relationships
  shipperId?: string
  shipperName?: string
  shipperData?: CustomerShipperRelationship

  // Product relationships
  productId?: string
  productName?: string
  productData?: CustomerProductRelationship

  // Consignee-Notify Party relationships (from existing integration)
  consigneeId?: string
  consigneeName?: string
  notifyPartyId?: string
  notifyPartyName?: string
  consigneeNotifyPartyData?: ConsigneeNotifyParty

  // Route intelligence
  suggestedOriginPorts?: string[]
  suggestedDestinationPorts?: string[]
  frequentRoutes?: Array<{
    origin_port_id: string
    destination_port_id: string
    usage_count: number
  }>

  // Pricing intelligence
  estimatedPricing?: {
    unit_price_cif: number
    unit_price_fob: number
    currency_code: string
  }

  // Historical patterns
  averageShipmentVolume?: number
  preferredTransportModes?: string[]
  typicalLeadTimes?: {
    closing_to_etd_hours: number
    etd_to_eta_days: number
  }
}

export interface RelationshipIntegrationState {
  // Selection state
  selectedCustomerId: string | null
  selectedShipperId: string | null
  selectedProductId: string | null
  selectedConsigneeId: string | null
  selectedNotifyPartyId: string | null

  // Available options
  availableShippers: CustomerShipperRelationship[]
  availableProducts: CustomerProductRelationship[]
  availableConsignees: any[]
  availableNotifyParties: ConsigneeNotifyParty[]

  // Intelligence data
  shipmentData: ShipmentRelationshipData

  // Loading states
  loadingShippers: boolean
  loadingProducts: boolean
  loadingConsignees: boolean
  loadingNotifyParties: boolean
  loadingHistory: boolean

  // Default selections
  defaultShipper?: CustomerShipperRelationship
  defaultProduct?: CustomerProductRelationship
  defaultConsignee?: any
  defaultNotifyParty?: ConsigneeNotifyParty

  // Validation state
  hasValidRelationships: boolean
  relationshipErrors: string[]
}

export interface RelationshipIntegrationActions {
  // Selection actions
  selectCustomer: (customerId: string) => void
  selectShipper: (shipperId: string) => void
  selectProduct: (productId: string) => void
  selectConsignee: (consigneeId: string) => void
  selectNotifyParty: (notifyPartyId: string) => void

  // Auto-population actions
  autoSelectDefaults: () => void
  populateFromCustomerHistory: (customerId: string) => Promise<void>

  // Pricing actions
  calculateEstimatedPricing: (
    productId: string,
    quantity?: number
  ) => Promise<void>
  updatePricingFromProduct: (
    productRelationship: CustomerProductRelationship
  ) => void

  // Route intelligence actions
  suggestRoutesFromHistory: (customerId: string) => Promise<void>
  getMostFrequentRoute: () => {
    origin_port_id: string
    destination_port_id: string
  } | null

  // Validation actions
  validateAllRelationships: () => Promise<boolean>
  getRelationshipErrors: () => string[]

  // Export actions
  exportShipmentData: () => ShipmentRelationshipData
  exportFormData: () => Record<string, any>

  // Reset actions
  resetAllSelections: () => void
  resetCustomerDependents: () => void
}

export interface RelationshipIntegrationConfig {
  autoSelectDefaults: boolean
  enablePricePopulation: boolean
  enableRouteIntelligence: boolean
  enableHistoryAnalysis: boolean
  validateRelationships: boolean
}

const DEFAULT_CONFIG: RelationshipIntegrationConfig = {
  autoSelectDefaults: true,
  enablePricePopulation: true,
  enableRouteIntelligence: true,
  enableHistoryAnalysis: true,
  validateRelationships: true,
}

export const useShipmentRelationships = (
  config: Partial<RelationshipIntegrationConfig> = {}
) => {
  const fullConfig = { ...DEFAULT_CONFIG, ...config }

  // State
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(
    null
  )
  const [selectedShipperId, setSelectedShipperId] = useState<string | null>(
    null
  )
  const [selectedProductId, setSelectedProductId] = useState<string | null>(
    null
  )
  const [loadingHistory, setLoadingHistory] = useState(false)
  const [relationshipErrors, setRelationshipErrors] = useState<string[]>([])
  const [shipmentData, setShipmentData] = useState<ShipmentRelationshipData>({})

  // Data hooks
  const { companiesByType, getCompanyById } = useCompaniesManagement()
  const { getPortById, activePorts } = usePortsManagement()

  // Customer-Shipper relationships
  const {
    customerShippers,
    loading: loadingShippers,
    getShippersForCustomer,
    getDefaultShipperForCustomer,
  } = useCustomerShippers(selectedCustomerId || undefined)

  // Customer-Product relationships
  const {
    customerProducts,
    loading: loadingProducts,
    getProductsForCustomer,
    getDefaultProductForCustomer,
  } = useCustomerProducts(selectedCustomerId || undefined)

  // Consignee-Notify Party integration (existing)
  const {
    state: consigneeState,
    actions: consigneeActions,
    config: consigneeConfig,
  } = useShipmentConsigneeIntegration({
    autoSelectDefault: fullConfig.autoSelectDefaults,
    enableRealTimeUpdates: true,
    prePopulateRelatedFields: true,
    validateRelationships: fullConfig.validateRelationships,
  })

  // Computed values
  const availableShippers = useMemo(() => {
    return customerShippers.filter(rel => rel.is_active) || []
  }, [customerShippers])

  const availableProducts = useMemo(() => {
    return customerProducts.filter(rel => rel.is_active) || []
  }, [customerProducts])

  const defaultShipper = useMemo(() => {
    return availableShippers.find(shipper => shipper.is_default) || null
  }, [availableShippers])

  const defaultProduct = useMemo(() => {
    return availableProducts.find(product => product.is_default) || null
  }, [availableProducts])

  const selectedShipperData = useMemo(() => {
    return (
      availableShippers.find(
        shipper => shipper.shipper_id === selectedShipperId
      ) || null
    )
  }, [availableShippers, selectedShipperId])

  const selectedProductData = useMemo(() => {
    return (
      availableProducts.find(
        product => product.product_id === selectedProductId
      ) || null
    )
  }, [availableProducts, selectedProductId])

  // Actions
  const selectCustomer = useCallback(
    (customerId: string) => {
      setSelectedCustomerId(customerId)
      setSelectedShipperId(null)
      setSelectedProductId(null)
      setRelationshipErrors([])

      // Reset consignee relationships when customer changes
      consigneeActions.setConsignee('')

      // Update shipment data
      const customer = getCompanyById(customerId)
      setShipmentData(prev => ({
        ...prev,
        customerId,
        customerName: customer?.name,
        shipperId: undefined,
        shipperName: undefined,
        shipperData: undefined,
        productId: undefined,
        productName: undefined,
        productData: undefined,
        estimatedPricing: undefined,
      }))
    },
    [getCompanyById, consigneeActions]
  )

  const selectShipper = useCallback(
    (shipperId: string) => {
      setSelectedShipperId(shipperId)

      const shipperRelationship = availableShippers.find(
        s => s.shipper_id === shipperId
      )
      if (shipperRelationship) {
        setShipmentData(prev => ({
          ...prev,
          shipperId,
          shipperName: shipperRelationship.shipper.name,
          shipperData: shipperRelationship,
        }))
      }
    },
    [availableShippers]
  )

  const selectProduct = useCallback(
    (productId: string) => {
      setSelectedProductId(productId)

      const productRelationship = availableProducts.find(
        p => p.product_id === productId
      )
      if (productRelationship) {
        setShipmentData(prev => ({
          ...prev,
          productId,
          productName: productRelationship.product.name,
          productData: productRelationship,
          estimatedPricing: fullConfig.enablePricePopulation
            ? {
                unit_price_cif: productRelationship.unit_price_cif,
                unit_price_fob: productRelationship.unit_price_fob,
                currency_code: productRelationship.currency_code,
              }
            : prev.estimatedPricing,
        }))
      }
    },
    [availableProducts, fullConfig.enablePricePopulation]
  )

  const selectConsignee = useCallback(
    (consigneeId: string) => {
      consigneeActions.setConsignee(consigneeId)

      const consignee = getCompanyById(consigneeId)
      if (consignee) {
        setShipmentData(prev => ({
          ...prev,
          consigneeId,
          consigneeName: consignee.name,
        }))
      }
    },
    [consigneeActions, getCompanyById]
  )

  const selectNotifyParty = useCallback(
    (notifyPartyId: string) => {
      consigneeActions.setNotifyParty(notifyPartyId)

      const relationship = consigneeState.availableNotifyParties.find(
        rel => rel.notify_party_id === notifyPartyId
      )
      if (relationship) {
        setShipmentData(prev => ({
          ...prev,
          notifyPartyId,
          notifyPartyName: relationship.notify_party?.name,
          consigneeNotifyPartyData: relationship,
        }))
      }
    },
    [consigneeActions, consigneeState.availableNotifyParties]
  )

  // Auto-select defaults when relationships load
  const autoSelectDefaults = useCallback(() => {
    if (!fullConfig.autoSelectDefaults) return

    // Auto-select default shipper
    if (defaultShipper && !selectedShipperId) {
      selectShipper(defaultShipper.shipper_id)
    }

    // Auto-select default product
    if (defaultProduct && !selectedProductId) {
      selectProduct(defaultProduct.product_id)
    }

    // Let consignee integration handle its own defaults
  }, [
    fullConfig.autoSelectDefaults,
    defaultShipper,
    defaultProduct,
    selectedShipperId,
    selectedProductId,
    selectShipper,
    selectProduct,
  ])

  // Populate customer history and intelligence
  const populateFromCustomerHistory = useCallback(
    async (customerId: string) => {
      if (!fullConfig.enableHistoryAnalysis) return

      setLoadingHistory(true)
      try {
        // TODO: Implement actual API calls to get customer history
        // This would include:
        // - Frequent shipping routes
        // - Average shipment volumes
        // - Preferred transport modes
        // - Typical lead times

        // For now, simulate with mock data
        await new Promise(resolve => setTimeout(resolve, 1000))

        setShipmentData(prev => ({
          ...prev,
          suggestedOriginPorts: ['port1', 'port2'],
          suggestedDestinationPorts: ['port3', 'port4'],
          frequentRoutes: [
            {
              origin_port_id: 'port1',
              destination_port_id: 'port3',
              usage_count: 15,
            },
            {
              origin_port_id: 'port2',
              destination_port_id: 'port4',
              usage_count: 8,
            },
          ],
          averageShipmentVolume: 1500,
          preferredTransportModes: ['sea', 'land'],
          typicalLeadTimes: {
            closing_to_etd_hours: 48,
            etd_to_eta_days: 14,
          },
        }))
      } catch (error) {
        console.error('Failed to load customer history:', error)
      } finally {
        setLoadingHistory(false)
      }
    },
    [fullConfig.enableHistoryAnalysis]
  )

  // Calculate estimated pricing
  const calculateEstimatedPricing = useCallback(
    async (productId: string, quantity: number = 1) => {
      if (!fullConfig.enablePricePopulation) return

      const productRelationship = availableProducts.find(
        p => p.product_id === productId
      )
      if (productRelationship) {
        setShipmentData(prev => ({
          ...prev,
          estimatedPricing: {
            unit_price_cif: productRelationship.unit_price_cif * quantity,
            unit_price_fob: productRelationship.unit_price_fob * quantity,
            currency_code: productRelationship.currency_code,
          },
        }))
      }
    },
    [availableProducts, fullConfig.enablePricePopulation]
  )

  // Route intelligence
  const getMostFrequentRoute = useCallback(() => {
    if (
      !shipmentData.frequentRoutes ||
      shipmentData.frequentRoutes.length === 0
    ) {
      return null
    }

    return shipmentData.frequentRoutes.reduce((prev, current) =>
      prev.usage_count > current.usage_count ? prev : current
    )
  }, [shipmentData.frequentRoutes])

  // Validation
  const validateAllRelationships = useCallback(async (): Promise<boolean> => {
    if (!fullConfig.validateRelationships) return true

    const errors: string[] = []

    if (!selectedCustomerId) errors.push('Customer selection is required')
    if (selectedCustomerId && availableShippers.length === 0) {
      errors.push('No shipper relationships configured for selected customer')
    }
    if (selectedCustomerId && availableProducts.length === 0) {
      errors.push('No product relationships configured for selected customer')
    }

    // Validate consignee relationships
    const consigneeValid = await consigneeActions.validateSelection()
    if (!consigneeValid) {
      errors.push(...consigneeActions.getValidationErrors())
    }

    setRelationshipErrors(errors)
    return errors.length === 0
  }, [
    fullConfig.validateRelationships,
    selectedCustomerId,
    availableShippers,
    availableProducts,
    consigneeActions,
  ])

  // Export functions
  const exportShipmentData = useCallback((): ShipmentRelationshipData => {
    return {
      ...shipmentData,
      consigneeNotifyPartyData:
        consigneeState.selectedRelationship || undefined,
    }
  }, [shipmentData, consigneeState.selectedRelationship])

  const exportFormData = useCallback(() => {
    return {
      customer_id: selectedCustomerId,
      shipper_id: selectedShipperId,
      consignee_id: consigneeState.selectedConsigneeId,
      notify_party_id: consigneeState.selectedNotifyPartyId,
      // Add pricing data if available
      ...(selectedProductData &&
        fullConfig.enablePricePopulation && {
          estimated_unit_price_cif: selectedProductData.unit_price_cif,
          estimated_unit_price_fob: selectedProductData.unit_price_fob,
          currency_code: selectedProductData.currency_code,
        }),
      // Add route suggestions if available
      ...(shipmentData.frequentRoutes &&
        shipmentData.frequentRoutes.length > 0 && {
          suggested_route: getMostFrequentRoute(),
        }),
    }
  }, [
    selectedCustomerId,
    selectedShipperId,
    selectedProductData,
    consigneeState,
    shipmentData,
    fullConfig.enablePricePopulation,
    getMostFrequentRoute,
  ])

  // Reset functions
  const resetAllSelections = useCallback(() => {
    setSelectedCustomerId(null)
    setSelectedShipperId(null)
    setSelectedProductId(null)
    setRelationshipErrors([])
    setShipmentData({})
    consigneeActions.clearSelection()
  }, [consigneeActions])

  const resetCustomerDependents = useCallback(() => {
    setSelectedShipperId(null)
    setSelectedProductId(null)
    setRelationshipErrors([])
    consigneeActions.clearSelection()
    setShipmentData(prev => ({
      customerId: prev.customerId,
      customerName: prev.customerName,
    }))
  }, [consigneeActions])

  // Effects
  useEffect(() => {
    if (selectedCustomerId && fullConfig.autoSelectDefaults) {
      autoSelectDefaults()
    }
  }, [selectedCustomerId, autoSelectDefaults, fullConfig.autoSelectDefaults])

  useEffect(() => {
    if (selectedCustomerId && fullConfig.enableHistoryAnalysis) {
      populateFromCustomerHistory(selectedCustomerId)
    }
  }, [
    selectedCustomerId,
    populateFromCustomerHistory,
    fullConfig.enableHistoryAnalysis,
  ])

  // State object
  const state: RelationshipIntegrationState = {
    selectedCustomerId,
    selectedShipperId,
    selectedProductId,
    selectedConsigneeId: consigneeState.selectedConsigneeId,
    selectedNotifyPartyId: consigneeState.selectedNotifyPartyId,
    availableShippers,
    availableProducts,
    availableConsignees: companiesByType.consignee || [],
    availableNotifyParties: consigneeState.availableNotifyParties,
    shipmentData,
    loadingShippers,
    loadingProducts,
    loadingConsignees: consigneeState.loadingConsignees,
    loadingNotifyParties: consigneeState.loadingRelationships,
    loadingHistory,
    defaultShipper: defaultShipper || undefined,
    defaultProduct: defaultProduct || undefined,
    defaultConsignee: undefined,
    defaultNotifyParty: consigneeState.defaultNotifyParty || undefined,
    hasValidRelationships: relationshipErrors.length === 0,
    relationshipErrors,
  }

  // Actions object
  const actions: RelationshipIntegrationActions = {
    selectCustomer,
    selectShipper,
    selectProduct,
    selectConsignee,
    selectNotifyParty,
    autoSelectDefaults,
    populateFromCustomerHistory,
    calculateEstimatedPricing,
    updatePricingFromProduct: selectProduct,
    suggestRoutesFromHistory: populateFromCustomerHistory,
    getMostFrequentRoute,
    validateAllRelationships,
    getRelationshipErrors: () => relationshipErrors,
    exportShipmentData,
    exportFormData,
    resetAllSelections,
    resetCustomerDependents,
  }

  return {
    state,
    actions,
    config: fullConfig,
  }
}

// Convenience hooks for specific use cases
export const useCustomerShipperIntelligence = (customerId?: string) => {
  return useShipmentRelationships({
    autoSelectDefaults: true,
    enablePricePopulation: false,
    enableRouteIntelligence: false,
    enableHistoryAnalysis: false,
    validateRelationships: true,
  })
}

export const useCustomerProductIntelligence = (customerId?: string) => {
  return useShipmentRelationships({
    autoSelectDefaults: true,
    enablePricePopulation: true,
    enableRouteIntelligence: false,
    enableHistoryAnalysis: false,
    validateRelationships: true,
  })
}

export const useFullShipmentIntelligence = () => {
  return useShipmentRelationships({
    autoSelectDefaults: true,
    enablePricePopulation: true,
    enableRouteIntelligence: true,
    enableHistoryAnalysis: true,
    validateRelationships: true,
  })
}
