'use client'

import { useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuthStore } from '@/stores/auth-store'
import type { UserProfile } from '@/lib/supabase/auth'

export function useAuth() {
  const {
    user,
    profile,
    session,
    loading,
    error,
    setLoading,
    setError,
    updateProfile,
    clearAuth,
  } = useAuthStore()

  const supabase = createClient()

  // Refresh profile data
  const refreshProfile = useCallback(async () => {
    if (!user) return

    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (error) throw error
      updateProfile(data as UserProfile)
    } catch (err) {
      console.error('Error refreshing profile:', err)
      setError(err instanceof Error ? err.message : 'Error refreshing profile')
    } finally {
      setLoading(false)
    }
  }, [user, supabase, setLoading, setError, updateProfile])

  // Sign out
  const signOut = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      clearAuth()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sign out error')
    }
  }, [supabase.auth, clearAuth, setError])

  return {
    user,
    profile,
    session,
    loading,
    error,
    refreshProfile,
    signOut,
    isAuthenticated: !!user,
    isAdmin: profile?.role === 'admin',
    isStaff: profile?.role && ['admin', 'cs', 'account'].includes(profile.role),
  }
}

// Hook for admin operations
export function useAdminAuth() {
  const auth = useAuth()

  if (!auth.isAdmin) {
    throw new Error('Admin access required')
  }

  return auth
}

// Hook for staff operations
export function useStaffAuth() {
  const auth = useAuth()

  if (!auth.isStaff) {
    throw new Error('Staff access required')
  }

  return auth
}
