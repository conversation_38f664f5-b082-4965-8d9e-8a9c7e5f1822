'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Shield, 
  Activity, 
  AlertTriangle,
  Database,
  Server,
  FileText,
  Settings,
  BarChart3,
  Globe,
  HardDrive,
  Cpu,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

// Mock data for admin dashboard
const systemStats = [
  {
    title: "Total Users",
    value: "2,847",
    trend: "+12%",
    icon: Users,
    color: "text-blue-500",
    href: "/admin/users"
  },
  {
    title: "Active Sessions", 
    value: "156",
    trend: "+5%",
    icon: Activity,
    color: "text-green-500"
  },
  {
    title: "System Alerts",
    value: "3", 
    trend: "-2",
    icon: AlertTriangle,
    color: "text-yellow-500"
  },
  {
    title: "Database Size",
    value: "2.4GB",
    trend: "+8%",
    icon: Database,
    color: "text-purple-500"
  }
]

const adminActions = [
  {
    icon: Users,
    label: "User Management",
    description: "Manage user accounts, roles, and permissions",
    href: "/admin/users",
    color: "bg-blue-500"
  },
  {
    icon: Settings,
    label: "System Settings",
    description: "Configure system parameters and preferences",
    href: "/dashboard/admin/system",
    color: "bg-gray-500"
  },
  {
    icon: FileText,
    label: "Audit Logs",
    description: "View system activity and security logs",
    href: "/dashboard/admin/audit",
    color: "bg-green-500"
  },
  {
    icon: BarChart3,
    label: "Analytics",
    description: "System performance and usage analytics",
    href: "/dashboard/reports",
    color: "bg-orange-500"
  },
  {
    icon: Shield,
    label: "Security Center",
    description: "Security settings and threat monitoring",
    href: "/dashboard/admin/security",
    color: "bg-red-500"
  },
  {
    icon: Database,
    label: "Database Management",
    description: "Database maintenance and backup operations",
    href: "/dashboard/admin/database",
    color: "bg-indigo-500"
  }
]

const systemServices = [
  { name: "Web Server", status: "operational", uptime: "99.9%", cpu: 25, memory: 45 },
  { name: "Database", status: "operational", uptime: "100%", cpu: 15, memory: 62 },
  { name: "API Gateway", status: "operational", uptime: "99.8%", cpu: 18, memory: 38 },
  { name: "File Storage", status: "degraded", uptime: "95.2%", cpu: 5, memory: 28 },
  { name: "Email Service", status: "operational", uptime: "98.9%", cpu: 8, memory: 22 },
  { name: "Background Jobs", status: "operational", uptime: "99.5%", cpu: 12, memory: 35 }
]

const recentAdminActivity = [
  {
    id: 1,
    action: "User account created",
    details: "New CS user: <EMAIL>",
    user: "Admin",
    timestamp: "5 minutes ago",
    type: "user_management"
  },
  {
    id: 2,
    action: "System backup completed",
    details: "Daily automated backup finished successfully",
    user: "System",
    timestamp: "2 hours ago",
    type: "system"
  },
  {
    id: 3,
    action: "Security policy updated",
    details: "Password policy strength requirements modified",
    user: "Admin",
    timestamp: "4 hours ago", 
    type: "security"
  },
  {
    id: 4,
    action: "Database optimization",
    details: "Scheduled index rebuild completed",
    user: "System",
    timestamp: "6 hours ago",
    type: "database"
  }
]

function StatCard({ title, value, trend, icon: Icon, color, href }: {
  title: string
  value: string
  trend: string
  icon: any
  color: string
  href?: string
}) {
  const isPositive = trend.startsWith('+')
  const isNegative = trend.startsWith('-')
  
  const CardWrapper = (
    <Card className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors cursor-pointer">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-slate-400 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-white mt-1">{value}</p>
            <div className="flex items-center mt-2">
              {isPositive && <TrendingUp className="w-3 h-3 text-green-500 mr-1" />}
              {isNegative && <TrendingDown className="w-3 h-3 text-red-500 mr-1" />}
              <span className={`text-xs font-medium ${
                isPositive ? 'text-green-500' : isNegative ? 'text-red-500' : 'text-slate-400'
              }`}>
                {trend}
              </span>
            </div>
          </div>
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
      </CardContent>
    </Card>
  )

  if (href) {
    return (
      <div onClick={() => window.location.href = href}>
        {CardWrapper}
      </div>
    )
  }

  return CardWrapper
}

function ActionCard({ icon: Icon, label, description, href, color }: {
  icon: any
  label: string
  description: string
  href: string
  color: string
}) {
  return (
    <Card 
      className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors cursor-pointer"
      onClick={() => window.location.href = href}
    >
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <div className={`p-3 rounded-lg ${color}`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-white mb-1">{label}</h3>
            <p className="text-xs text-slate-400 leading-relaxed">{description}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function ServiceStatus({ service }: { service: typeof systemServices[0] }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'outage':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-slate-400" />
    }
  }

  return (
    <div className="flex items-center justify-between py-3 border-b border-slate-700 last:border-b-0">
      <div className="flex items-center space-x-3">
        {getStatusIcon(service.status)}
        <div>
          <p className="text-sm font-medium text-white">{service.name}</p>
          <p className="text-xs text-slate-400">Uptime: {service.uptime}</p>
        </div>
      </div>
      <div className="text-right">
        <p className="text-xs text-slate-400">CPU: {service.cpu}%</p>
        <p className="text-xs text-slate-400">RAM: {service.memory}%</p>
      </div>
    </div>
  )
}

function ActivityItem({ activity }: { activity: typeof recentAdminActivity[0] }) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_management':
        return <Users className="w-4 h-4 text-blue-500" />
      case 'system':
        return <Server className="w-4 h-4 text-green-500" />
      case 'security':
        return <Shield className="w-4 h-4 text-red-500" />
      case 'database':
        return <Database className="w-4 h-4 text-purple-500" />
      default:
        return <Activity className="w-4 h-4 text-slate-400" />
    }
  }

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-slate-700 rounded-lg transition-colors">
      <div className="flex-shrink-0 mt-0.5">
        {getActivityIcon(activity.type)}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-white">{activity.action}</p>
        <p className="text-xs text-slate-300 mt-1">{activity.details}</p>
        <p className="text-xs text-slate-400 mt-1">
          {activity.user} • {activity.timestamp}
        </p>
      </div>
    </div>
  )
}

export default function AdminDashboardPage() {
  const { isAdmin, loading } = useAuth()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading delay
    const timer = setTimeout(() => setIsLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  // Show loading while checking auth or initializing
  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
          <p className="text-slate-300 mt-4">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  // Show access denied for non-admin users (middleware should prevent this)
  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-center">
          <Shield className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-slate-300 mb-4">
            You don't have permission to access the admin dashboard.
          </p>
          <Button 
            onClick={() => window.location.href = '/dashboard/overview'}
            className="bg-orange-500 hover:bg-orange-600"
          >
            Go to Dashboard
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* Welcome Section */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <Shield className="w-8 h-8 text-orange-500" />
          <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
        </div>
        <p className="text-slate-400">System administration and management center</p>
      </div>

      {/* System Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {systemStats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Admin Actions */}
        <Card className="lg:col-span-2 bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center space-x-2">
              <Settings className="w-5 h-5" />
              <span>Administration Tools</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {adminActions.map((action, index) => (
                <ActionCard key={index} {...action} />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Services Status */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center space-x-2">
              <Server className="w-5 h-5" />
              <span>System Services</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            {systemServices.map((service, index) => (
              <ServiceStatus key={index} service={service} />
            ))}
          </CardContent>
        </Card>

        {/* Recent Admin Activity */}
        <Card className="lg:col-span-2 bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center space-x-2">
              <Activity className="w-5 h-5" />
              <span>Recent Admin Activity</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <div className="max-h-80 overflow-y-auto">
              {recentAdminActivity.map((activity) => (
                <ActivityItem key={activity.id} activity={activity} />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Resources */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center space-x-2">
              <Cpu className="w-5 h-5" />
              <span>System Resources</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-slate-400">CPU Usage</span>
                <span className="text-white">32%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '32%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-slate-400">Memory Usage</span>
                <span className="text-white">68%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '68%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-slate-400">Disk Usage</span>
                <span className="text-white">45%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-slate-400">Network I/O</span>
                <span className="text-white">23%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div className="bg-purple-500 h-2 rounded-full" style={{ width: '23%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}