'use client'

import { useState, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Switch } from '@/components/ui/switch'
import {
  Loader2,
  Info,
  User,
  Truck,
  Phone,
  MessageSquare,
  AlertCircle,
  Camera,
  Upload,
  X,
  Check,
} from 'lucide-react'
import { driverFormSchema, type DriverForm } from '@/lib/validations/drivers'
import {
  useDriverValidation,
  useCarrierCompanies,
  useDriverProfiles,
  useDriverPhotoUtils,
} from '@/hooks/use-drivers'
import type { Driver } from '@/stores/driver-store'

interface DriverFormProps {
  driver?: Driver | null
  onSubmit: (data: DriverForm, photoFile?: File | null) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  className?: string
}

export function DriverForm({
  driver,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: DriverFormProps) {
  const [error, setError] = useState<string | null>(null)
  const [photoFile, setPhotoFile] = useState<File | null>(null)
  const [photoPreview, setPhotoPreview] = useState<string | null>(null)
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false)

  const {
    validateUniqueDriverCode,
    validateDriverName,
    validateDriverCode,
    validatePhoneNumber,
    validateLineId,
    validateNotes,
    validatePhotoFile,
    validateUniqueUserId,
  } = useDriverValidation()

  const {
    carrierCompanyOptions,
    loading: carriersLoading,
    error: carriersError,
  } = useCarrierCompanies()

  const {
    driverProfileOptions,
    loading: profilesLoading,
    error: profilesError,
  } = useDriverProfiles()

  const { usePhotoUrl } = useDriverPhotoUtils()
  const { photoUrl: currentPhotoUrl, loading: photoLoading } =
    usePhotoUrl(driver)

  // Initialize form with default values
  const form = useForm<DriverForm>({
    resolver: zodResolver(driverFormSchema),
    defaultValues: {
      carrier_id: driver?.carrier_id || '',
      driver_first_name: driver?.driver_first_name || '',
      driver_last_name: driver?.driver_last_name || '',
      driver_code: driver?.driver_code || '',
      user_id: driver?.user_id || null,
      phone: driver?.phone || '',
      line_id: driver?.line_id || '',
      notes: driver?.notes || '',
      is_active: driver?.is_active ?? true,
      driver_picture_path: driver?.driver_picture_path || '',
      driver_picture_mime_type: driver?.driver_picture_mime_type || '',
      vehicle_head_number: driver?.vehicle_head_number || '',
      vehicle_tail_number: driver?.vehicle_tail_number || '',
    },
  })

  // Watch form values for validation
  const watchedDriverCode = form.watch('driver_code')

  // Handle form submission
  const handleSubmit = async (data: DriverForm) => {
    try {
      setError(null)

      // Additional validation
      if (
        data.driver_code &&
        !validateUniqueDriverCode(data.driver_code, driver?.id).isValid
      ) {
        const validation = validateUniqueDriverCode(
          data.driver_code,
          driver?.id
        )
        setError(validation.message || 'Driver code validation failed')
        return
      }

      // Validate unique user_id
      if (
        data.user_id &&
        !validateUniqueUserId(data.user_id, driver?.id).isValid
      ) {
        const validation = validateUniqueUserId(data.user_id, driver?.id)
        setError(validation.message || 'User account validation failed')
        return
      }

      // Pass the photo file to the submit handler
      await onSubmit(data, photoFile)
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to save driver'
      setError(errorMessage)
    }
  }

  // Handle photo file selection
  const handlePhotoSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0]
      if (!file) return

      // Validate photo file
      const validation = validatePhotoFile(file)
      if (!validation.isValid) {
        setError(validation.message || 'Invalid photo file')
        return
      }

      setPhotoFile(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = e => {
        setPhotoPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    },
    [validatePhotoFile]
  )

  // Handle photo removal
  const handlePhotoRemove = useCallback(() => {
    setPhotoFile(null)
    setPhotoPreview(null)
    form.setValue('driver_picture_path', '')
    form.setValue('driver_picture_mime_type', '')
  }, [form])

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()

      const files = e.dataTransfer.files
      if (files.length > 0) {
        const file = files[0]

        // Create a synthetic event to reuse existing logic
        const syntheticEvent = {
          target: { files: [file] },
        } as React.ChangeEvent<HTMLInputElement>

        handlePhotoSelect(syntheticEvent)
      }
    },
    [handlePhotoSelect]
  )

  // Get current photo URL if editing
  const getCurrentPhotoUrl = () => {
    if (photoPreview) return photoPreview
    return currentPhotoUrl
  }

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Carrier Company Error */}
          {carriersError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to load carrier companies: {carriersError}
              </AlertDescription>
            </Alert>
          )}

          {/* Driver Profiles Error */}
          {profilesError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to load driver profiles: {profilesError}
              </AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <User className="h-5 w-5 text-orange-500" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Carrier Company Selection */}
              <FormField
                control={form.control}
                name="carrier_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Carrier Company <span className="text-red-400">*</span>
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ''}
                      disabled={carriersLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select a carrier company" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700">
                        {carrierCompanyOptions.map(carrier => (
                          <SelectItem
                            key={carrier.value}
                            value={carrier.value}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            <div className="flex items-center space-x-2">
                              <Truck className="h-4 w-4 text-green-400" />
                              <span>{carrier.label}</span>
                              {carrier.phone && (
                                <span className="text-slate-400 text-sm">
                                  ({carrier.phone})
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription className="text-slate-400">
                      Select the carrier company this driver belongs to
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Driver Names */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="driver_first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        First Name <span className="text-red-400">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter first name"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="driver_last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Last Name <span className="text-red-400">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter last name"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Driver Code */}
              <FormField
                control={form.control}
                name="driver_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">
                      Driver Code
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter driver code (e.g., DRV001)"
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        style={{ textTransform: 'uppercase' }}
                        {...field}
                        onChange={e =>
                          field.onChange(e.target.value.toUpperCase())
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Optional unique identifier for internal tracking
                      (uppercase letters, numbers, and hyphens)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* User Binding */}
              <FormField
                control={form.control}
                name="user_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Link to User Account
                    </FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(value === 'none' ? null : value)}
                      value={field.value || 'none'}
                      disabled={profilesLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue placeholder="Select a user account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-slate-800 border-slate-700">
                        <SelectItem value="none" className="text-slate-300 hover:bg-slate-700">
                          No user account
                        </SelectItem>
                        {driverProfileOptions.map(profile => (
                          <SelectItem
                            key={profile.value}
                            value={profile.value}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            <div className="flex flex-col">
                              <span className="font-medium">{profile.label}</span>
                              {profile.displayName && (
                                <span className="text-slate-400 text-sm">
                                  {profile.displayName}
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription className="text-slate-400">
                      Link this driver to a user account for app access (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Availability Status */}
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border border-slate-600 p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base text-slate-200">
                        Active Status
                      </FormLabel>
                      <FormDescription className="text-slate-400">
                        Enable this driver for assignment coordination
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-slate-600"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Phone className="h-5 w-5 text-orange-500" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Phone Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter phone number"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        Contact phone number for coordination
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="line_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200 flex items-center gap-2">
                        <MessageSquare className="h-4 w-4" />
                        Line ID
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter Line ID"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        Line messaging app ID for mobile communication
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Truck Information */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Truck className="h-5 w-5 text-orange-500" />
                Truck Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="vehicle_head_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Vehicle Head Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter vehicle head number"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        License plate number of the truck head/tractor (supports Thai/English)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="vehicle_tail_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-slate-200">
                        Vehicle Tail Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter vehicle tail number"
                          className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-slate-400">
                        License plate number of the trailer/tail (supports Thai/English)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Photo Upload */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Camera className="h-5 w-5 text-orange-500" />
                Driver Photo
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div
                className="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center hover:border-slate-500 transition-colors"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                {photoLoading ? (
                  <div className="space-y-4">
                    <div className="w-32 h-32 rounded-lg bg-slate-700 mx-auto flex items-center justify-center">
                      <Loader2 className="h-6 w-6 text-slate-400 animate-spin" />
                    </div>
                    <p className="text-slate-400 text-sm">Loading photo...</p>
                  </div>
                ) : getCurrentPhotoUrl() || photoPreview ? (
                  <div className="space-y-4">
                    <img
                      src={getCurrentPhotoUrl() || photoPreview || undefined}
                      alt="Driver photo preview"
                      className="w-32 h-32 rounded-lg object-cover mx-auto"
                      onError={e => {
                        console.error('Failed to load photo:', e)
                        e.currentTarget.style.display = 'none'
                      }}
                    />
                    <div className="flex justify-center space-x-2">
                      <label htmlFor="photo-upload">
                        <Button
                          type="button"
                          variant="outline"
                          className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white cursor-pointer"
                          asChild
                        >
                          <span>
                            <Upload className="h-4 w-4 mr-2" />
                            Change Photo
                          </span>
                        </Button>
                      </label>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handlePhotoRemove}
                        className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-red-600 hover:text-white"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Remove
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Camera className="h-12 w-12 text-slate-400 mx-auto" />
                    <div className="space-y-2">
                      <p className="text-slate-300">
                        Drop photo here or click to upload
                      </p>
                      <p className="text-slate-400 text-sm">
                        Supports JPEG, PNG, WebP • Max 2MB • Recommended
                        400x400px
                      </p>
                    </div>
                    <label htmlFor="photo-upload">
                      <Button
                        type="button"
                        className="bg-orange-500 hover:bg-orange-600 text-white cursor-pointer"
                        asChild
                      >
                        <span>
                          <Upload className="h-4 w-4 mr-2" />
                          Select Photo
                        </span>
                      </Button>
                    </label>
                  </div>
                )}
                <input
                  id="photo-upload"
                  type="file"
                  accept="image/jpeg,image/png,image/webp"
                  onChange={handlePhotoSelect}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Info className="h-5 w-5 text-orange-500" />
                Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200">Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional notes about this driver..."
                        className="min-h-[100px] bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Additional driver information (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isLoading || isUploadingPhoto}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {driver ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  {driver ? 'Update Driver' : 'Create Driver'}
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
