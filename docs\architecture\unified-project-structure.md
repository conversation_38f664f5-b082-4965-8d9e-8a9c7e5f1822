# Unified Project Structure

```plaintext
dyy-trading-management/
├── .github/                          # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml                   # Test and build pipeline
│       ├── deploy-staging.yaml       # Staging deployment
│       └── deploy-production.yaml    # Production deployment
├── src/                              # Next.js application source
│   ├── app/                          # Next.js App Router
│   │   ├── (auth)/                   # Authentication routes
│   │   │   ├── login/
│   │   │   │   └── page.tsx
│   │   │   ├── register/
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx            # Auth layout
│   │   ├── (dashboard)/              # Protected dashboard routes
│   │   │   ├── shipments/
│   │   │   │   ├── page.tsx          # Shipment list with filters
│   │   │   │   ├── create/
│   │   │   │   │   └── page.tsx      # Intelligent creation form
│   │   │   │   └── [id]/
│   │   │   │       ├── page.tsx      # Shipment details
│   │   │   │       └── edit/page.tsx # Edit shipment
│   │   │   ├── master-data/
│   │   │   │   ├── companies/
│   │   │   │   │   ├── page.tsx      # Company management
│   │   │   │   │   └── [id]/page.tsx # Company details
│   │   │   │   ├── products/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── drivers/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── relationships/
│   │   │   │       └── page.tsx      # Customer-product relationships
│   │   │   ├── documents/
│   │   │   │   ├── page.tsx          # Document management
│   │   │   │   └── upload/page.tsx   # Document upload
│   │   │   ├── reports/
│   │   │   │   └── page.tsx          # Analytics dashboard
│   │   │   └── layout.tsx            # Dashboard layout with navigation
│   │   ├── (mobile)/                 # Mobile-optimized PWA routes
│   │   │   ├── driver/
│   │   │   │   ├── dashboard/
│   │   │   │   │   └── page.tsx      # Driver dashboard
│   │   │   │   ├── shipments/
│   │   │   │   │   ├── page.tsx      # Assigned shipments
│   │   │   │   │   └── [id]/
│   │   │   │   │       └── page.tsx  # Shipment tracking
│   │   │   │   └── status-update/
│   │   │   │       └── page.tsx      # Status update form
│   │   │   ├── customer/
│   │   │   │   ├── dashboard/
│   │   │   │   │   └── page.tsx      # Customer portal
│   │   │   │   └── shipments/
│   │   │   │       └── page.tsx      # Shipment tracking
│   │   │   └── layout.tsx            # Mobile layout
│   │   ├── api/                      # API routes (minimal - proxy to Supabase)
│   │   │   ├── auth/
│   │   │   │   └── callback/route.ts # Auth callback handler
│   │   │   ├── webhooks/
│   │   │   │   └── supabase/route.ts # Supabase webhooks
│   │   │   └── health/route.ts       # Health check endpoint
│   │   ├── globals.css               # Global styles with CSS variables
│   │   ├── layout.tsx                # Root layout with providers
│   │   ├── loading.tsx               # Global loading component
│   │   ├── error.tsx                 # Global error boundary
│   │   ├── not-found.tsx             # 404 page
│   │   └── page.tsx                  # Landing page
│   ├── components/                   # Reusable UI components
│   │   ├── ui/                       # ShadCN UI base components
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── select.tsx
│   │   │   ├── data-table.tsx
│   │   │   ├── form.tsx
│   │   │   ├── sheet.tsx
│   │   │   ├── toast.tsx
│   │   │   └── ...                   # Other ShadCN components
│   │   ├── forms/                    # Domain-specific form components
│   │   │   ├── shipment-form/
│   │   │   │   ├── shipment-form.tsx
│   │   │   │   ├── stakeholder-section.tsx
│   │   │   │   ├── product-section.tsx
│   │   │   │   └── logistics-section.tsx
│   │   │   ├── company-form/
│   │   │   │   ├── company-form.tsx
│   │   │   │   └── address-form.tsx
│   │   │   └── driver-form/
│   │   │       └── driver-form.tsx
│   │   ├── layout/                   # Layout components
│   │   │   ├── navigation/
│   │   │   │   ├── sidebar.tsx
│   │   │   │   ├── mobile-nav.tsx
│   │   │   │   └── breadcrumbs.tsx
│   │   │   ├── header.tsx
│   │   │   └── footer.tsx
│   │   ├── data-display/             # Data presentation components
│   │   │   ├── shipment-card.tsx
│   │   │   ├── status-badge.tsx
│   │   │   ├── timeline.tsx
│   │   │   ├── data-table-wrapper.tsx
│   │   │   └── charts/
│   │   │       ├── shipment-analytics.tsx
│   │   │       └── performance-metrics.tsx
│   │   ├── mobile/                   # Mobile-specific components
│   │   │   ├── status-update-form.tsx
│   │   │   ├── offline-indicator.tsx
│   │   │   ├── camera-capture.tsx
│   │   │   └── location-tracker.tsx
│   │   └── providers/                # Context providers
│   │       ├── theme-provider.tsx
│   │       ├── supabase-provider.tsx
│   │       └── notification-provider.tsx
│   ├── hooks/                        # Custom React hooks
│   │   ├── use-supabase.ts           # Supabase client management
│   │   ├── use-auth.ts               # Authentication state and actions
│   │   ├── use-shipments.ts          # Shipment CRUD operations
│   │   ├── use-companies.ts          # Company management
│   │   ├── use-relationships.ts      # Relationship intelligence engine
│   │   ├── use-real-time.ts          # Real-time subscriptions
│   │   ├── use-notifications.ts      # Notification management
│   │   ├── use-mobile.ts             # Mobile/PWA features
│   │   └── use-offline.ts            # Offline capabilities
│   ├── lib/                          # Utility functions and configurations
│   │   ├── supabase/
│   │   │   ├── client.ts             # Client-side Supabase client
│   │   │   ├── server.ts             # Server-side Supabase client
│   │   │   ├── auth.ts               # Auth helper functions
│   │   │   ├── middleware.ts         # Auth middleware
│   │   │   └── types.ts              # Generated TypeScript types
│   │   ├── validations/              # Zod validation schemas
│   │   │   ├── shipment.ts
│   │   │   ├── company.ts
│   │   │   └── auth.ts
│   │   ├── constants/                # Application constants
│   │   │   ├── enums.ts              # Database enums
│   │   │   ├── routes.ts             # Route definitions
│   │   │   └── theme.ts              # Theme configuration
│   │   ├── utils.ts                  # General utility functions
│   │   └── cn.ts                     # Class name utility
│   ├── stores/                       # Zustand state stores
│   │   ├── auth-store.ts             # Authentication state
│   │   ├── shipment-store.ts         # Shipment management state
│   │   ├── company-store.ts          # Company data state
│   │   ├── notification-store.ts     # Notifications state
│   │   └── ui-store.ts               # UI state (modals, sheets, etc.)
│   └── types/                        # TypeScript definitions
│       ├── database.ts               # Supabase generated types
│       ├── api.ts                    # API response types
│       ├── shipment.ts               # Extended shipment types
│       ├── company.ts                # Extended company types
│       └── global.d.ts               # Global type definitions
├── supabase/                         # Supabase configuration
│   ├── functions/                    # Edge Functions
│   │   ├── notifications/
│   │   │   ├── email-notification/
│   │   │   │   ├── index.ts
│   │   │   │   └── templates/
│   │   │   ├── sms-notification/
│   │   │   │   └── index.ts
│   │   │   └── in-app-notification/
│   │   │       └── index.ts
│   │   ├── document-processing/
│   │   │   ├── pdf-generator/
│   │   │   │   └── index.ts
│   │   │   └── file-upload-handler/
│   │   │       └── index.ts
│   │   ├── data-sync/
│   │   │   ├── relationship-engine/
│   │   │   │   └── index.ts
│   │   │   └── external-api-sync/
│   │   │       └── index.ts
│   │   ├── mobile-sync/
│   │   │   ├── offline-sync/
│   │   │   │   └── index.ts
│   │   │   └── status-update/
│   │   │       └── index.ts
│   │   └── _shared/                  # Shared utilities for functions
│   │       ├── cors.ts
│   │       ├── auth.ts
│   │       └── types.ts
│   ├── migrations/                   # Database migrations
│   │   ├── 20240101000000_initial_schema.sql
│   │   ├── 20240102000000_companies_rls.sql
│   │   ├── 20240103000000_relationship_functions.sql
│   │   ├── 20240104000000_performance_indexes.sql
│   │   ├── 20240105000000_notification_tables.sql
│   │   └── 20240106000000_audit_triggers.sql
│   ├── seed.sql                      # Development seed data
│   └── config.toml                   # Supabase configuration
├── public/                           # Static assets
│   ├── icons/                        # PWA icons
│   │   ├── icon-192x192.png
│   │   ├── icon-512x512.png
│   │   └── favicon.ico
│   ├── images/                       # Application images
│   │   └── logo.png
│   ├── manifest.json                 # PWA manifest
│   └── sw.js                         # Service worker (generated)
├── docs/                             # Documentation
│   ├── prd.md                        # Product Requirements Document
│   ├── data-model-requirements.md    # Database requirements
│   ├── architecture.md               # This document
│   ├── api-documentation.md          # API reference
│   ├── deployment-guide.md           # Deployment instructions
│   └── user-guides/                  # User documentation
│       ├── admin-guide.md
│       ├── driver-guide.md
│       └── customer-guide.md
├── tests/                            # Test files
│   ├── __mocks__/                    # Test mocks
│   ├── e2e/                          # End-to-end tests
│   │   ├── auth.spec.ts
│   │   ├── shipments.spec.ts
│   │   └── mobile-driver.spec.ts
│   ├── integration/                  # Integration tests
│   │   ├── api.test.ts
│   │   └── database.test.ts
│   └── unit/                         # Unit tests
│       ├── components/
│       ├── hooks/
│       └── utils/
├── scripts/                          # Build and utility scripts
│   ├── build.sh                      # Build script
│   ├── deploy.sh                     # Deployment script
│   ├── db-reset.sh                   # Database reset script
│   └── generate-types.sh             # Type generation script
├── .env.example                      # Environment variables template
├── .env.local                        # Local environment (gitignored)
├── .gitignore                        # Git ignore rules
├── .eslintrc.json                    # ESLint configuration
├── .prettierrc                       # Prettier configuration
├── components.json                   # ShadCN UI configuration
├── middleware.ts                     # Next.js middleware for auth
├── next.config.js                    # Next.js configuration with PWA
├── package.json                      # Dependencies and scripts
├── tailwind.config.js                # Tailwind CSS configuration
├── tsconfig.json                     # TypeScript configuration
├── playwright.config.ts              # Playwright test configuration
└── README.md                         # Project documentation
```