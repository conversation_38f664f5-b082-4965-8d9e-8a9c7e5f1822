'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Settings,
  Upload,
  Trash2,
  Download,
  Check,
  X,
  User,
  Shield,
  AlertCircle,
  Plus,
  CheckSquare,
  Loader2,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { PhotoUploadForm } from './photo-upload-form'
import { BulkPhotoDeleteDialog } from './photo-delete-dialog'
import { StatusBadge } from '@/components/data-display/status-badge'
import type { StatusPhoto, StatusPhotoGroup } from '@/hooks/use-status-photos'
import type { ShipmentStatus } from '@/lib/supabase/types'

interface PhotoManagementPanelProps {
  shipmentId: string
  photoGroups: StatusPhotoGroup[]
  onPhotosChanged?: () => void
  className?: string
}

export function PhotoManagementPanel({
  shipmentId,
  photoGroups,
  onPhotosChanged,
  className = '',
}: PhotoManagementPanelProps) {
  const { profile, isStaff } = useAuth()
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState<StatusPhotoGroup | null>(null)
  const [selectedPhotos, setSelectedPhotos] = useState<Set<string>>(new Set())
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectionMode, setSelectionMode] = useState(false)

  // Only show for staff users (admin/CS)
  if (!isStaff) {
    return null
  }

  const allPhotos = photoGroups.flatMap(group => group.photos)
  const selectedPhotoObjects = allPhotos.filter(photo => selectedPhotos.has(photo.id))

  // Toggle photo selection
  const togglePhotoSelection = (photoId: string) => {
    setSelectedPhotos(prev => {
      const newSet = new Set(prev)
      if (newSet.has(photoId)) {
        newSet.delete(photoId)
      } else {
        newSet.add(photoId)
      }
      return newSet
    })
  }

  // Select all photos
  const selectAllPhotos = () => {
    const allPhotoIds = new Set(allPhotos.map(photo => photo.id))
    setSelectedPhotos(allPhotoIds)
  }

  // Clear selection
  const clearSelection = () => {
    setSelectedPhotos(new Set())
    setSelectionMode(false)
  }

  // Handle upload success
  const handleUploadSuccess = (uploadedPhotos: any[]) => {
    setShowUploadForm(false)
    setSelectedStatus(null)
    if (onPhotosChanged) {
      onPhotosChanged()
    }
  }

  // Handle delete success
  const handleDeleteSuccess = (deletedPhotos: StatusPhoto[]) => {
    setShowDeleteDialog(false)
    clearSelection()
    if (onPhotosChanged) {
      onPhotosChanged()
    }
  }

  // Download all selected photos
  const downloadSelectedPhotos = async () => {
    for (const photo of selectedPhotoObjects) {
      if (photo.signed_url) {
        try {
          const response = await fetch(photo.signed_url)
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = `status-photo-${photo.id}.jpg`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        } catch (error) {
          console.error('Error downloading photo:', error)
        }
      }
    }
  }

  // Show upload form for specific status
  const showUploadForStatus = (group: StatusPhotoGroup) => {
    setSelectedStatus(group)
    setShowUploadForm(true)
  }

  if (showUploadForm && selectedStatus) {
    return (
      <PhotoUploadForm
        shipmentId={shipmentId}
        statusHistoryId={selectedStatus.status_history_id}
        statusName={selectedStatus.status}
        onSuccess={handleUploadSuccess}
        onCancel={() => {
          setShowUploadForm(false)
          setSelectedStatus(null)
        }}
        className={className}
      />
    )
  }

  return (
    <>
      <Card className={`bg-slate-800 border-slate-700 ${className}`}>
        <CardHeader>
          <CardTitle className="text-slate-200 flex items-center gap-2">
            <Settings className="h-5 w-5 text-orange-500" />
            Photo Management
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-400">
              <Shield className="h-3 w-3 mr-1" />
              Staff Only
            </Badge>
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Management Actions */}
          <div className="flex flex-wrap items-center gap-3">
            {/* Selection Mode Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectionMode(!selectionMode)}
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600"
            >
              <CheckSquare className="h-4 w-4 mr-2" />
              {selectionMode ? 'Exit Selection' : 'Select Photos'}
            </Button>

            {/* Selection Actions */}
            {selectionMode && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={selectAllPhotos}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600"
                >
                  <Check className="h-4 w-4 mr-2" />
                  Select All ({allPhotos.length})
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600"
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear
                </Button>
              </>
            )}

            {/* Bulk Actions */}
            {selectedPhotos.size > 0 && (
              <>
                <Badge variant="secondary" className="bg-orange-500/20 text-orange-300">
                  {selectedPhotos.size} selected
                </Badge>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadSelectedPhotos}
                  className="bg-green-600 border-green-500 text-white hover:bg-green-700"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDeleteDialog(true)}
                  className="bg-red-600 border-red-500 text-white hover:bg-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </>
            )}
          </div>

          {/* Status Groups with Management */}
          {photoGroups.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-200 mb-2">
                No Status Updates
              </h3>
              <p className="text-slate-400">
                Photos can be uploaded when there are status updates for this shipment.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {photoGroups.map((group) => (
                <StatusGroupManagement
                  key={group.status_history_id}
                  group={group}
                  selectionMode={selectionMode}
                  selectedPhotos={selectedPhotos}
                  onPhotoToggle={togglePhotoSelection}
                  onUploadClick={() => showUploadForStatus(group)}
                />
              ))}
            </div>
          )}

          {/* User Info */}
          <div className="pt-4 border-t border-slate-600">
            <div className="flex items-center space-x-2 text-xs text-slate-400">
              <User className="h-3 w-3" />
              <span>
                Managing as: {profile?.full_name || profile?.email} ({profile?.role})
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <BulkPhotoDeleteDialog
        selectedPhotos={selectedPhotoObjects}
        isOpen={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onSuccess={handleDeleteSuccess}
      />
    </>
  )
}

// Individual status group management component
interface StatusGroupManagementProps {
  group: StatusPhotoGroup
  selectionMode: boolean
  selectedPhotos: Set<string>
  onPhotoToggle: (photoId: string) => void
  onUploadClick: () => void
}

function StatusGroupManagement({
  group,
  selectionMode,
  selectedPhotos,
  onPhotoToggle,
  onUploadClick,
}: StatusGroupManagementProps) {
  const [expanded, setExpanded] = useState(false)

  return (
    <div className="border border-slate-600 rounded-lg bg-slate-700/30">
      {/* Group Header with Actions */}
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <StatusBadge status={group.status as ShipmentStatus} size="default" showIcon />
          <Badge variant="outline" className="border-orange-400 text-orange-300">
            {group.total_count} photo{group.total_count !== 1 ? 's' : ''}
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            onClick={onUploadClick}
            className="bg-orange-500 hover:bg-orange-600 text-white h-8 w-8 p-0"
          >
            <Plus className="h-4 w-4" />
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={() => setExpanded(!expanded)}
            className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 h-8 w-8 p-0"
          >
            {expanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Photos Grid with Selection */}
      {expanded && (
        <div className="p-4 border-t border-slate-600">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
            {group.photos.map((photo) => (
              <div key={photo.id} className="relative group">
                {/* Selection Checkbox */}
                {selectionMode && (
                  <div className="absolute top-2 left-2 z-10">
                    <Checkbox
                      checked={selectedPhotos.has(photo.id)}
                      onCheckedChange={() => onPhotoToggle(photo.id)}
                      className="bg-white/80 border-slate-400"
                    />
                  </div>
                )}

                {/* Photo Thumbnail */}
                {photo.signed_url ? (
                  <img
                    src={photo.signed_url}
                    alt={`Status photo from ${photo.created_at}`}
                    className={`w-full aspect-square object-cover rounded border border-slate-500 transition-all ${
                      selectionMode && selectedPhotos.has(photo.id)
                        ? 'ring-2 ring-orange-500 ring-offset-2 ring-offset-slate-800'
                        : 'hover:ring-2 hover:ring-slate-400 hover:ring-offset-2 hover:ring-offset-slate-800'
                    }`}
                    onClick={() => selectionMode && onPhotoToggle(photo.id)}
                  />
                ) : (
                  <div className="w-full aspect-square bg-slate-600 rounded border border-slate-500 flex items-center justify-center">
                    <AlertCircle className="h-6 w-6 text-slate-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}