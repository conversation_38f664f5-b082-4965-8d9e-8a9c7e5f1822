# External APIs

## SMS Service API

- **Purpose:** SMS notifications for urgent shipment updates, driver assignments, and emergency communications
- **Documentation:** https://www.twilio.com/docs/sms
- **Base URL(s):** https://api.twilio.com/2010-04-01/
- **Authentication:** API Key + Auth Token (Basic Auth)
- **Rate Limits:** 1 message per second per phone number, 100 messages per second per account

**Key Endpoints Used:**
- `POST /Accounts/{AccountSid}/Messages.json` - Send SMS messages to drivers and stakeholders

**Integration Notes:** Integrated via Supabase Edge Functions for server-side rate limiting and security.

## Line Messaging API

- **Purpose:** Primary communication channel for Thai market stakeholders
- **Documentation:** https://developers.line.biz/en/docs/messaging-api/
- **Base URL(s):** https://api.line.me/v2/bot/
- **Authentication:** Channel Access Token (Bearer Token)
- **Rate Limits:** 500 messages per minute, 10,000 messages per month (free tier)

**Key Endpoints Used:**
- `POST /message/push` - Send notifications to individual users
- `POST /message/multicast` - Send notifications to multiple users

**Integration Notes:** Essential for Thai operations where Line penetration exceeds 90%.

## WeChat Work API

- **Purpose:** Communication with Chinese market consignees and supply chain partners
- **Documentation:** https://developer.work.weixin.qq.com/document/
- **Base URL(s):** https://qyapi.weixin.qq.com/cgi-bin/
- **Authentication:** OAuth 2.0 with enterprise application credentials
- **Rate Limits:** 20,000 API calls per hour per enterprise application

**Key Endpoints Used:**
- `POST /message/send` - Send text and rich media messages
- `POST /media/upload` - Upload documents and images

**Integration Notes:** Requires enterprise WeChat Work account setup for B2B communications.
