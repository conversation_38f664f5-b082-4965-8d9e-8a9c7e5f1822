export type ShipmentStatus = 
  | 'booking_confirmed'
  | 'transport_assigned'
  | 'driver_assigned'
  | 'empty_container_picked'
  | 'arrived_at_factory'
  | 'loading_started'
  | 'departed_factory'
  | 'container_returned'
  | 'shipped'
  | 'arrived'
  | 'completed'
  | 'cancelled';

export interface StatusHistory {
  id: string;
  shipment_id: string;
  status_from?: ShipmentStatus;
  status_to: ShipmentStatus;
  notes?: string;
  location?: string;
  latitude?: number;
  longitude?: number;
  gps_coordinates?: any; // PostGIS geography type
  updated_by?: string; // Reference to profiles.user_id
  created_at: string;
}

export interface StatusImage {
  id: string;
  shipment_id?: string;
  status_history_id?: string;
  image_url: string;
  image_path: string;
  file_size?: number;
  mime_type?: string;
  metadata?: any; // JSONB for image metadata
  uploaded_by?: string;
  created_at: string;
}

export interface StatusUpdateRequest {
  status_to: ShipmentStatus;
  status_from?: ShipmentStatus;
  notes?: string;
  latitude?: number;
  longitude?: number;
  location?: string;
  photos: File[];
}

export interface GPSCoordinates {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

export interface PhotoMetadata {
  originalSize: number;
  compressedSize: number;
  dimensions: {
    width: number;
    height: number;
  };
  compressionQuality: number;
}

export interface OfflineStatusUpdate {
  id: string;
  shipment_id: string;
  status_update: Omit<StatusUpdateRequest, 'photos'>;
  photos: File[]; // Stored as blob URLs in offline storage
  timestamp: number;
  sync_status: 'pending' | 'syncing' | 'synced' | 'failed';
  retry_count: number;
}

export interface StatusTransition {
  from: ShipmentStatus;
  to: ShipmentStatus;
  requiresPhotos: boolean;
  minPhotos: number;
  maxPhotos: number;
  description: string;
}