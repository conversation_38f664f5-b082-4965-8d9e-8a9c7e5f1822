# Container & Seal Number Confirmation System - Design Document

## Overview

This document outlines the design for implementing a container and seal number confirmation system where Customer Service or Admin can confirm these values, and once confirmed, drivers cannot edit them anymore. However, Admin and Customer Service retain full editing privileges even after confirmation.

## Current System Analysis

Based on the codebase analysis, the system currently has:
- **containers** table with `container_number` and `seal_number` fields
- Driver mobile interface with `container-number-input.tsx` and `container-data-form.tsx` components
- Container validation system with format checking and uniqueness validation
- Role-based access control with Admin and Customer Service (CS) roles

## Requirements

1. Customer Service or Admin can confirm container number and seal number
2. Once confirmed, drivers cannot edit these values
3. Customer Service has full confirmation privileges
4. Admin and Customer Service can still change container number and seal number after confirmation
5. System should provide audit trail for all changes

## Detailed Design

### 1. Confirmation Dialog Placement

#### Primary Location: Container Management Pages
- **Main shipment details page** (`/shipments/[id]/page.tsx`) - Add confirmation buttons in the container section
- **Container edit form** (`container-edit-form.tsx`) - Add confirmation controls alongside existing form fields
- **Container list component** (`container-list.tsx`) - Add confirmation status indicators and quick-confirm buttons

#### Secondary Location: Mobile Interface
- **Driver container form** (`container-data-form.tsx`) - Show confirmation status (read-only for drivers)

### 2. Permission Matrix

| Role | Confirm Numbers | Edit Before Confirmation | Edit After Confirmation |
|------|----------------|-------------------------|------------------------|
| **Customer Service** | ✅ Full privileges | ✅ Yes | ✅ Yes |
| **Admin** | ✅ Full privileges | ✅ Yes | ✅ Yes |
| **Driver** | ❌ No | ✅ Yes | ❌ No |

### 3. Database Schema Changes

```sql
-- Add confirmation fields to containers table
ALTER TABLE containers ADD COLUMN 
  container_number_confirmed BOOLEAN DEFAULT false,
  container_number_confirmed_by UUID REFERENCES profiles(user_id),
  container_number_confirmed_at TIMESTAMPTZ,
  seal_number_confirmed BOOLEAN DEFAULT false,
  seal_number_confirmed_by UUID REFERENCES profiles(user_id), 
  seal_number_confirmed_at TIMESTAMPTZ,
  -- Allow tracking who can still edit after confirmation
  allow_post_confirmation_edits BOOLEAN DEFAULT false;

-- Add action_type column to status_history table for confirmation tracking
ALTER TABLE status_history ADD COLUMN 
  action_type TEXT DEFAULT 'status_change';

-- Add indexes for performance
CREATE INDEX idx_containers_confirmation_status 
ON containers(container_number_confirmed, seal_number_confirmed);

CREATE INDEX idx_containers_confirmed_by 
ON containers(container_number_confirmed_by, seal_number_confirmed_by);

CREATE INDEX idx_status_history_action_type 
ON status_history(action_type);
```

### 4. API Endpoints

#### New Endpoints
```typescript
// Confirm container and seal numbers
POST /api/containers/{id}/confirm-numbers
Body: {
  confirm_container_number: boolean,
  confirm_seal_number: boolean,
  confirmation_notes?: string
}

// Get confirmation status
GET /api/containers/{id}/confirmation-status
Response: {
  container_number_confirmed: boolean,
  container_number_confirmed_by: string,
  container_number_confirmed_at: string,
  seal_number_confirmed: boolean,
  seal_number_confirmed_by: string,
  seal_number_confirmed_at: string
}

// Update container with confirmation override
PUT /api/containers/{id}/update-confirmed
Body: {
  container_number?: string,
  seal_number?: string,
  override_reason: string
}
```

### 5. Frontend Components

#### 5.1 Confirmation Dialog Component
```typescript
// components/containers/container-confirmation-dialog.tsx
interface ContainerConfirmationDialogProps {
  container: Container
  isOpen: boolean
  onClose: () => void
  onConfirm: (data: ConfirmationData) => Promise<void>
}
```

**Features:**
- Container number review with validation status
- Seal number review with format verification  
- Confirmation timestamp and user tracking
- Override reason field (for post-confirmation edits)
- Bulk confirmation for multiple containers

#### 5.2 Visual Indicators
- 🔒 Confirmed status badges
- ✅ Green checkmarks for confirmed items
- 👤 "Confirmed by [User]" tooltips
- 📅 Confirmation timestamps
- ⚠️ Override warning indicators

#### 5.3 Edit Permission Logic

**For Drivers:**
```typescript
const canEditContainer = !container.container_number_confirmed || 
  (user.role === 'admin' || user.role === 'cs')

const canEditSeal = !container.seal_number_confirmed || 
  (user.role === 'admin' || user.role === 'cs')
```

**For CS/Admin:**
```typescript  
const canEditContainer = true // Always allowed
const showConfirmationDialog = container.container_number_confirmed && 
  previousValue !== newValue // Show reason dialog for changes
```

### 6. Component Modifications

#### 6.1 Container Edit Form Updates
- Add confirmation status display
- Add confirmation buttons for CS/Admin
- Implement conditional field locking for drivers
- Add override reason modal for post-confirmation edits

#### 6.2 Mobile Container Input Updates
- Show locked state when confirmed
- Display confirmation details
- Prevent input when confirmed and user is driver

#### 6.3 Container List Updates  
- Add confirmation status column
- Add bulk confirmation controls
- Add quick-confirm buttons

### 7. Business Logic

#### 7.1 Confirmation Workflow
1. CS/Admin reviews container and seal numbers
2. Clicks "Confirm Numbers" button
3. System validates current values
4. Records confirmation with timestamp and user
5. Sends notification to relevant stakeholders
6. Updates UI to show confirmed status

#### 7.2 Post-Confirmation Edit Workflow
1. CS/Admin attempts to edit confirmed value
2. System shows override confirmation dialog
3. User provides reason for change
4. System records the override in audit trail
5. Updates value and maintains confirmation status
6. Sends notification about the override

### 8. Audit Trail

#### 8.1 Status History Integration
All confirmation and override actions logged in `status_history` table:
```sql
INSERT INTO status_history (
  shipment_id,
  status_to,
  notes,
  updated_by,
  action_type -- New field: 'container_confirmed', 'seal_confirmed', 'confirmation_override'
)
```

#### 8.2 Notification System
- Email notifications when numbers are confirmed
- Real-time updates via Supabase subscriptions
- Mobile push notifications for drivers

### 9. Security Considerations

#### 9.1 Row Level Security (RLS) Policies
```sql
-- Only CS and Admin can confirm numbers
CREATE POLICY container_confirmation_policy ON containers
FOR UPDATE USING (
  auth.jwt() ->> 'role' IN ('admin', 'cs') OR 
  (auth.jwt() ->> 'role' = 'driver' AND NOT (
    container_number_confirmed OR seal_number_confirmed
  ))
);
```

#### 9.2 API Route Protection
- Verify user role before allowing confirmation actions
- Validate container ownership/assignment
- Log all confirmation attempts

### 10. Testing Strategy

#### 10.1 Unit Tests
- Container confirmation logic
- Permission validation
- API endpoint functionality

#### 10.2 Integration Tests
- End-to-end confirmation workflow
- Cross-role permission testing
- Mobile interface integration

#### 10.3 E2E Tests
- Driver input restriction after confirmation
- CS/Admin override functionality  
- Notification delivery

### 11. Implementation Phases

#### Phase 1: Database & API ✅ COMPLETED
- [x] Add confirmation columns to containers table
- [x] Implement API endpoints with proper authentication
- [x] Add RLS policies for role-based access
- [x] Create database migration with audit triggers
- [x] Add performance indexes

#### Phase 2: Core Components ✅ COMPLETED
- [x] Create confirmation dialog component with project theme
- [x] Update container edit form with field locking
- [x] Implement permission logic for CS/Admin
- [x] Add confirmation status indicators and badges
- [x] Create custom confirmation hooks

#### Phase 3: Mobile Integration 🔄 PARTIALLY COMPLETED
- [x] Field locking logic for drivers (via updated permissions)
- [ ] Update mobile container forms (future enhancement)
- [ ] Add confirmation status display to mobile UI
- [ ] Implement input locking for mobile interface

#### Phase 4: Notifications & Audit ✅ COMPLETED
- [x] Add comprehensive audit trail logging via database triggers
- [x] Implement real-time updates via Supabase subscriptions
- [ ] Integrate with email notification system (future enhancement)

#### Phase 5: Testing & Deployment 🔄 READY FOR TESTING
- [ ] Comprehensive testing (ready to begin)
- [ ] User acceptance testing
- [ ] Production deployment

## Implementation Summary

### ✅ **Successfully Implemented (September 2025):**

**Database Layer:**
- Migration: `20250901000001_container_seal_confirmation.sql`
- Added confirmation fields to containers table
- Automated audit trail with database triggers
- Row Level Security policies for role-based access
- Performance optimization indexes

**API Layer:**
- `POST /api/containers/{id}/confirm-numbers` - Confirmation endpoint
- `GET /api/containers/{id}/confirmation-status` - Status retrieval  
- `PUT /api/containers/{id}/update-confirmed` - CS/Admin override
- Comprehensive JWT authentication and role validation
- Detailed error handling and logging

**TypeScript Types:**
- Extended Container interface with confirmation fields
- ContainerConfirmationRequest interface
- ContainerConfirmationStatus interface
- ContainerPermissions interface

**React Components:**
- ContainerConfirmationDialog with dark blue theme compliance
- Updated ContainerEditForm with field locking
- Confirmation status badges and indicators
- useContainerConfirmation hook for API operations
- Real-time status updates via Supabase subscriptions

### 🔧 **Implementation Details:**

**Security & Permissions:**
- CS and Admin: Full confirmation and override privileges
- Drivers: Cannot edit confirmed numbers, can edit before confirmation
- All actions logged with user tracking and timestamps

**User Interface:**
- Confirmation status badges with lock icons
- Disabled fields with visual indicators
- Confirmation timestamps display
- Orange accent theme compliance (#f97316)
- Dark blue primary colors (#1e293b, #0f172a)

**Audit Trail:**
- Automatic logging via database triggers
- Action types: container_confirmed, seal_confirmed, confirmation_override
- Complete user and timestamp tracking
- Notes support for confirmation reasons

## Benefits

- **Data Integrity**: Prevents accidental changes to critical container data
- **Audit Compliance**: Full traceability of who confirmed what and when  
- **Operational Efficiency**: Clear workflow separation between data entry and approval
- **User Experience**: Visual indicators showing confirmation status
- **Flexibility**: CS/Admin retain full control while protecting from driver errors