import { describe, it, expect } from 'vitest'
import { 
  companyFormSchema, 
  forwarderAgentMetadataSchema,
  factoryInfoSchema,
  COMPANY_TYPES,
  TRANSPORT_MODES,
  FORWARDER_COVERAGE_AREAS,
  SERVICE_SPECIALIZATIONS,
  EQUIPMENT_TYPES,
  CERTIFICATIONS,
  SPECIALIZATIONS
} from '@/lib/validations/companies'

describe('Factory and Forwarder Agent Management', () => {
  describe('Forwarder Agent Metadata Validation', () => {
    it('validates required fields for forwarder agent metadata', () => {
      const invalidData = {
        services_provided: [],
        coverage_areas: [],
        equipment_types: [],
        service_specializations: [],
      }

      const result = forwarderAgentMetadataSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      
      if (!result.success) {
        expect(result.error.issues).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              path: ['services_provided'],
              message: 'At least one transportation service is required'
            }),
            expect.objectContaining({
              path: ['coverage_areas'],
              message: 'At least one coverage area is required'
            })
          ])
        )
      }
    })

    it('validates valid forwarder agent metadata', () => {
      const validData = {
        services_provided: ['sea', 'land'],
        coverage_areas: ['Southeast Asia', 'China'],
        equipment_types: ['20ft_container', '40ft_container'],
        service_specializations: ['container_shipping', 'customs_clearance'],
        operational_capacity: '100 TEU/month',
        emergency_contact: 'Emergency Ops',
        emergency_contact_phone: '+66 2 123 4567',
        office_hours: 'Mon-Fri: 08:00-18:00',
        response_time_hours: 24,
        insurance_coverage: 'USD 1,000,000',
        customs_license: 'CB-2023-001',
        years_of_experience: 15,
      }

      const result = forwarderAgentMetadataSchema.safeParse(validData)
      expect(result.success).toBe(true)
      
      if (result.success) {
        expect(result.data.services_provided).toHaveLength(2)
        expect(result.data.coverage_areas).toHaveLength(2)
        expect(result.data.response_time_hours).toBe(24)
        expect(result.data.years_of_experience).toBe(15)
      }
    })

    it('validates emergency contact phone format', () => {
      const dataWithInvalidPhone = {
        services_provided: ['sea'],
        coverage_areas: ['China'],
        emergency_contact_phone: 'invalid-phone-format'
      }

      const result = forwarderAgentMetadataSchema.safeParse(dataWithInvalidPhone)
      expect(result.success).toBe(false)
      
      if (!result.success) {
        expect(result.error.issues).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              path: ['emergency_contact_phone'],
              message: 'Emergency contact phone must contain only digits, spaces, hyphens, parentheses, and plus sign'
            })
          ])
        )
      }
    })
  })

  describe('Factory Info Validation', () => {
    it('validates required fields for factory info', () => {
      const invalidData = {
        factory_code: '',
        license_no: '',
        certifications: [],
        production_capacity_tons_per_day: 0,
        cold_storage_capacity_tons: 0,
        specializations: [],
        loading_dock_count: 1,
        container_loading_time_minutes: 120,
        advance_booking_required_hours: 24,
      }

      const result = factoryInfoSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      
      if (!result.success) {
        expect(result.error.issues).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              path: ['factory_code'],
              message: 'Factory code is required'
            }),
            expect.objectContaining({
              path: ['license_no'],
              message: 'License number is required'
            })
          ])
        )
      }
    })

    it('validates valid factory info', () => {
      const validData = {
        factory_code: 'FAC001',
        license_no: 'LIC-2023-001',
        certifications: ['HACCP', 'ISO22000', 'GMP'],
        production_capacity_tons_per_day: 500,
        cold_storage_capacity_tons: 200,
        specializations: ['durian', 'mangosteen', 'longan'],
        quality_control_manager: 'John Smith',
        quality_control_phone: '+66 2 123 4567',
        loading_dock_count: 3,
        container_loading_time_minutes: 90,
        advance_booking_required_hours: 48,
      }

      const result = factoryInfoSchema.safeParse(validData)
      expect(result.success).toBe(true)
      
      if (result.success) {
        expect(result.data.factory_code).toBe('FAC001')
        expect(result.data.certifications).toHaveLength(3)
        expect(result.data.production_capacity_tons_per_day).toBe(500)
        expect(result.data.specializations).toContain('durian')
        expect(result.data.loading_dock_count).toBe(3)
      }
    })

    it('validates numeric constraints', () => {
      const dataWithNegativeValues = {
        factory_code: 'FAC001',
        license_no: 'LIC-2023-001',
        production_capacity_tons_per_day: -100,
        cold_storage_capacity_tons: -50,
        loading_dock_count: 0,
        container_loading_time_minutes: 0,
        advance_booking_required_hours: -5,
      }

      const result = factoryInfoSchema.safeParse(dataWithNegativeValues)
      expect(result.success).toBe(false)
      
      if (!result.success) {
        expect(result.error.issues).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              path: ['production_capacity_tons_per_day'],
              message: 'Production capacity must be non-negative'
            }),
            expect.objectContaining({
              path: ['cold_storage_capacity_tons'],
              message: 'Cold storage capacity must be non-negative'
            }),
            expect.objectContaining({
              path: ['loading_dock_count'],
              message: 'Loading dock count must be at least 1'
            }),
            expect.objectContaining({
              path: ['container_loading_time_minutes'],
              message: 'Loading time must be at least 1 minute'
            }),
            expect.objectContaining({
              path: ['advance_booking_required_hours'],
              message: 'Booking hours must be non-negative'
            })
          ])
        )
      }
    })
  })

  describe('Integration Tests', () => {
    it('validates forwarder agent metadata structure', () => {
      const validMetadata = {
        services_provided: ['sea', 'land'],
        coverage_areas: ['Southeast Asia', 'China'],
        equipment_types: ['20ft_container', '40ft_container'],
        service_specializations: ['container_shipping', 'customs_clearance'],
        operational_capacity: '100 TEU/month',
        emergency_contact: 'Emergency Ops',
        emergency_contact_phone: '+66 2 123 4567',
        office_hours: 'Mon-Fri: 08:00-18:00',
        response_time_hours: 24,
        insurance_coverage: 'USD 1,000,000',
        customs_license: 'CB-2023-001',
        years_of_experience: 15,
      }

      expect(validMetadata.services_provided).toHaveLength(2)
      expect(validMetadata.coverage_areas).toHaveLength(2)
      expect(validMetadata.equipment_types).toContain('20ft_container')
      expect(validMetadata.service_specializations).toContain('container_shipping')
      expect(validMetadata.response_time_hours).toBe(24)
      expect(validMetadata.years_of_experience).toBe(15)
    })

    it('validates factory info structure', () => {
      const validFactoryInfo = {
        factory_code: 'FAC001',
        license_no: 'LIC-2023-001',
        certifications: ['HACCP', 'ISO22000', 'GMP'],
        production_capacity_tons_per_day: 500,
        cold_storage_capacity_tons: 200,
        specializations: ['durian', 'mangosteen', 'longan'],
        quality_control_manager: 'John Smith',
        quality_control_phone: '+66 2 123 4567',
        loading_dock_count: 3,
        container_loading_time_minutes: 90,
        advance_booking_required_hours: 48,
        operating_hours: {
          weekdays: '08:00-17:00',
          saturday: '08:00-12:00',
          sunday: 'closed'
        }
      }

      expect(validFactoryInfo.factory_code).toBe('FAC001')
      expect(validFactoryInfo.certifications).toHaveLength(3)
      expect(validFactoryInfo.production_capacity_tons_per_day).toBe(500)
      expect(validFactoryInfo.specializations).toContain('durian')
      expect(validFactoryInfo.loading_dock_count).toBe(3)
    })
  })
})