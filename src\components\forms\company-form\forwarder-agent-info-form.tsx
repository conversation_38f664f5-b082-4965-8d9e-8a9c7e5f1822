'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Ship,
  Truck,
  Train,
  Phone,
  Mail,
  MapPin,
  Globe,
  Package,
  Settings,
  Users,
  Building,
  AlertCircle,
  Plus,
  X,
  Clock,
} from 'lucide-react'

import {
  TRANSPORT_MODES,
  FORWARDER_COVERAGE_AREAS,
  SERVICE_SPECIALIZATIONS,
  EQUIPMENT_TYPES,
} from '@/lib/validations/companies'

interface ForwarderAgentMetadata {
  services_provided: string[]
  coverage_areas: string[]
  operational_capacity?: string
  equipment_types: string[]
  service_specializations: string[]
  emergency_contact?: string
  emergency_contact_phone?: string
  office_hours?: string
  response_time_hours?: number
  insurance_coverage?: string
  customs_license?: string
  years_of_experience?: number
  preferred_carriers?: string[]
  certifications?: string[]
}

interface ForwarderAgentInfoFormProps {
  value?: Partial<ForwarderAgentMetadata>
  onChange: (info: Partial<ForwarderAgentMetadata>) => void
  errors?: any
}

export function ForwarderAgentInfoForm({
  value = {},
  onChange,
  errors = {},
}: ForwarderAgentInfoFormProps) {
  const [newService, setNewService] = useState('')
  const [newCoverageArea, setNewCoverageArea] = useState('')
  const [newEquipmentType, setNewEquipmentType] = useState('')
  const [newSpecialization, setNewSpecialization] = useState('')
  const [newCarrier, setNewCarrier] = useState('')

  const updateField = <K extends keyof ForwarderAgentMetadata>(
    field: K,
    fieldValue: ForwarderAgentMetadata[K]
  ) => {
    onChange({ ...value, [field]: fieldValue })
  }

  // Array management helpers
  const addToArray = (
    field: keyof ForwarderAgentMetadata,
    newValue: string
  ) => {
    const current = (value[field] as string[]) || []
    if (newValue && !current.includes(newValue)) {
      updateField(field, [...current, newValue] as any)
    }
  }

  const removeFromArray = (
    field: keyof ForwarderAgentMetadata,
    valueToRemove: string
  ) => {
    const current = (value[field] as string[]) || []
    updateField(field, current.filter(item => item !== valueToRemove) as any)
  }

  // Get transport mode icon
  const getTransportModeIcon = (mode: string) => {
    switch (mode) {
      case 'sea':
        return <Ship className="h-3 w-3" />
      case 'land':
        return <Truck className="h-3 w-3" />
      case 'rail':
        return <Train className="h-3 w-3" />
      case 'air':
        return <Package className="h-3 w-3" />
      default:
        return <Settings className="h-3 w-3" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2 pb-2 border-b border-slate-600">
        <Ship className="h-5 w-5 text-indigo-500" />
        <h3 className="text-lg font-semibold text-white">
          Forwarder Agent Details
        </h3>
        <Badge
          variant="secondary"
          className="bg-indigo-500/20 text-indigo-300 border-indigo-400"
        >
          Service Information
        </Badge>
      </div>

      {/* Services Provided */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Settings className="h-4 w-4 text-blue-500" />
          <span>Transportation Services *</span>
        </Label>

        {/* Current Services */}
        {value.services_provided && value.services_provided.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {value.services_provided.map(service => (
              <Badge
                key={service}
                variant="secondary"
                className="bg-blue-500/20 text-blue-300 border-blue-400 flex items-center space-x-1"
              >
                {getTransportModeIcon(service)}
                <span>
                  {service.charAt(0).toUpperCase() + service.slice(1)}
                </span>
                <button
                  type="button"
                  onClick={() => removeFromArray('services_provided', service)}
                  className="ml-1 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Add Service */}
        <div className="flex space-x-2">
          <Select value={newService} onValueChange={setNewService}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-blue-500 focus:ring-blue-500">
              <SelectValue placeholder="Select transport mode" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {TRANSPORT_MODES.filter(
                mode => !value.services_provided?.includes(mode)
              ).map(mode => (
                <SelectItem
                  key={mode}
                  value={mode}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  <div className="flex items-center space-x-2">
                    {getTransportModeIcon(mode)}
                    <span>{mode.charAt(0).toUpperCase() + mode.slice(1)}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            onClick={() => {
              addToArray('services_provided', newService)
              setNewService('')
            }}
            disabled={!newService}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        {(!value.services_provided || value.services_provided.length === 0) && (
          <p className="text-sm text-red-400">
            At least one transportation service is required
          </p>
        )}
        <p className="text-xs text-slate-400">
          Select all transportation modes this agent provides
        </p>
      </div>

      {/* Coverage Areas */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Globe className="h-4 w-4 text-green-500" />
          <span>Coverage Areas *</span>
        </Label>

        {/* Current Coverage Areas */}
        {value.coverage_areas && value.coverage_areas.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {value.coverage_areas.map(area => (
              <Badge
                key={area}
                variant="secondary"
                className="bg-green-500/20 text-green-300 border-green-400 flex items-center space-x-1"
              >
                <MapPin className="h-3 w-3" />
                <span>{area}</span>
                <button
                  type="button"
                  onClick={() => removeFromArray('coverage_areas', area)}
                  className="ml-1 hover:text-green-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Add Coverage Area */}
        <div className="flex space-x-2">
          <Select value={newCoverageArea} onValueChange={setNewCoverageArea}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-green-500 focus:ring-green-500">
              <SelectValue placeholder="Select coverage area" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {FORWARDER_COVERAGE_AREAS.filter(
                area => !value.coverage_areas?.includes(area)
              ).map(area => (
                <SelectItem
                  key={area}
                  value={area}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  {area}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            onClick={() => {
              addToArray('coverage_areas', newCoverageArea)
              setNewCoverageArea('')
            }}
            disabled={!newCoverageArea}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        {(!value.coverage_areas || value.coverage_areas.length === 0) && (
          <p className="text-sm text-red-400">
            At least one coverage area is required
          </p>
        )}
        <p className="text-xs text-slate-400">
          Geographic regions where agent provides services
        </p>
      </div>

      {/* Equipment Types */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Package className="h-4 w-4 text-orange-500" />
          <span>Available Equipment Types</span>
        </Label>

        {/* Current Equipment Types */}
        {value.equipment_types && value.equipment_types.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {value.equipment_types.map(equipment => (
              <Badge
                key={equipment}
                variant="secondary"
                className="bg-orange-500/20 text-orange-300 border-orange-400 flex items-center space-x-1"
              >
                <Package className="h-3 w-3" />
                <span>{equipment.replace('_', ' ')}</span>
                <button
                  type="button"
                  onClick={() => removeFromArray('equipment_types', equipment)}
                  className="ml-1 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Add Equipment Type */}
        <div className="flex space-x-2">
          <Select value={newEquipmentType} onValueChange={setNewEquipmentType}>
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue placeholder="Select equipment type" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {EQUIPMENT_TYPES.filter(
                type => !value.equipment_types?.includes(type)
              ).map(type => (
                <SelectItem
                  key={type}
                  value={type}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  {type
                    .replace('_', ' ')
                    .replace(/\b\w/g, l => l.toUpperCase())}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            onClick={() => {
              addToArray('equipment_types', newEquipmentType)
              setNewEquipmentType('')
            }}
            disabled={!newEquipmentType}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-xs text-slate-400">
          Types of containers and equipment available
        </p>
      </div>

      {/* Service Specializations */}
      <div className="space-y-4">
        <Label className="text-slate-200 flex items-center space-x-2">
          <Building className="h-4 w-4 text-purple-500" />
          <span>Service Specializations</span>
        </Label>

        {/* Current Specializations */}
        {value.service_specializations &&
          value.service_specializations.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {value.service_specializations.map(spec => (
                <Badge
                  key={spec}
                  variant="secondary"
                  className="bg-purple-500/20 text-purple-300 border-purple-400 flex items-center space-x-1"
                >
                  <Building className="h-3 w-3" />
                  <span>{spec.replace('_', ' ')}</span>
                  <button
                    type="button"
                    onClick={() =>
                      removeFromArray('service_specializations', spec)
                    }
                    className="ml-1 hover:text-purple-200"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}

        {/* Add Specialization */}
        <div className="flex space-x-2">
          <Select
            value={newSpecialization}
            onValueChange={setNewSpecialization}
          >
            <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-purple-500 focus:ring-purple-500">
              <SelectValue placeholder="Select specialization" />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700">
              {SERVICE_SPECIALIZATIONS.filter(
                spec => !value.service_specializations?.includes(spec)
              ).map(spec => (
                <SelectItem
                  key={spec}
                  value={spec}
                  className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700 focus:text-white"
                >
                  {spec
                    .replace('_', ' ')
                    .replace(/\b\w/g, l => l.toUpperCase())}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            type="button"
            onClick={() => {
              addToArray('service_specializations', newSpecialization)
              setNewSpecialization('')
            }}
            disabled={!newSpecialization}
            className="bg-purple-500 hover:bg-purple-600 text-white"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-xs text-slate-400">
          Specialized services offered by this agent
        </p>
      </div>

      {/* Operational Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Operational Capacity */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Settings className="h-4 w-4 text-blue-500" />
            <span>Operational Capacity</span>
          </Label>
          <Textarea
            placeholder="e.g., 100 TEU/month, 24/7 operations, 50+ shipments monthly"
            value={value.operational_capacity || ''}
            onChange={e => updateField('operational_capacity', e.target.value)}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500 focus:ring-blue-500"
            rows={3}
          />
          <p className="text-xs text-slate-400">
            Describe handling capacity and operational scale
          </p>
        </div>

        {/* Years of Experience */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Clock className="h-4 w-4 text-yellow-500" />
            <span>Years of Experience</span>
          </Label>
          <Input
            type="number"
            placeholder="10"
            min="0"
            value={value.years_of_experience || ''}
            onChange={e =>
              updateField(
                'years_of_experience',
                parseInt(e.target.value) || undefined
              )
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-yellow-500 focus:ring-yellow-500"
          />
          <p className="text-xs text-slate-400">
            Years of experience in forwarding operations
          </p>
        </div>

        {/* Response Time */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Clock className="h-4 w-4 text-green-500" />
            <span>Response Time (hours)</span>
          </Label>
          <Input
            type="number"
            placeholder="24"
            min="0"
            value={value.response_time_hours || ''}
            onChange={e =>
              updateField(
                'response_time_hours',
                parseInt(e.target.value) || undefined
              )
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-green-500 focus:ring-green-500"
          />
          <p className="text-xs text-slate-400">
            Typical response time for quotes and inquiries
          </p>
        </div>

        {/* Office Hours */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Clock className="h-4 w-4 text-purple-500" />
            <span>Office Hours</span>
          </Label>
          <Input
            placeholder="Mon-Fri: 08:00-18:00, Sat: 09:00-12:00"
            value={value.office_hours || ''}
            onChange={e => updateField('office_hours', e.target.value)}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-purple-500"
          />
          <p className="text-xs text-slate-400">
            Standard business hours and availability
          </p>
        </div>
      </div>

      {/* Emergency Contact Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Emergency Contact Name */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Users className="h-4 w-4 text-red-500" />
            <span>Emergency Contact</span>
          </Label>
          <Input
            placeholder="24/7 emergency contact name"
            value={value.emergency_contact || ''}
            onChange={e => updateField('emergency_contact', e.target.value)}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-red-500 focus:ring-red-500"
          />
        </div>

        {/* Emergency Contact Phone */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Phone className="h-4 w-4 text-red-500" />
            <span>Emergency Phone</span>
          </Label>
          <Input
            placeholder="+66 xx xxx xxxx"
            value={value.emergency_contact_phone || ''}
            onChange={e =>
              updateField('emergency_contact_phone', e.target.value)
            }
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-red-500 focus:ring-red-500"
          />
        </div>
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Insurance Coverage */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <AlertCircle className="h-4 w-4 text-indigo-500" />
            <span>Insurance Coverage</span>
          </Label>
          <Input
            placeholder="USD 1,000,000 cargo insurance"
            value={value.insurance_coverage || ''}
            onChange={e => updateField('insurance_coverage', e.target.value)}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-indigo-500 focus:ring-indigo-500"
          />
          <p className="text-xs text-slate-400">
            Insurance coverage details and limits
          </p>
        </div>

        {/* Customs License */}
        <div className="space-y-2">
          <Label className="text-slate-200 flex items-center space-x-2">
            <Building className="h-4 w-4 text-teal-500" />
            <span>Customs License</span>
          </Label>
          <Input
            placeholder="Customs broker license number"
            value={value.customs_license || ''}
            onChange={e => updateField('customs_license', e.target.value)}
            className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-teal-500 focus:ring-teal-500"
          />
          <p className="text-xs text-slate-400">
            Customs clearance license information
          </p>
        </div>
      </div>

      {/* Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Ship className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-slate-200">Services</span>
          </div>
          <p className="text-xs text-slate-400">
            Transportation modes and service coverage
          </p>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Package className="h-4 w-4 text-orange-500" />
            <span className="text-sm font-medium text-slate-200">
              Equipment
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Available containers and specialized equipment
          </p>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
          <div className="flex items-center space-x-2 mb-2">
            <Settings className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-slate-200">
              Operations
            </span>
          </div>
          <p className="text-xs text-slate-400">
            Capacity, response time, and operational details
          </p>
        </div>
      </div>
    </div>
  )
}
