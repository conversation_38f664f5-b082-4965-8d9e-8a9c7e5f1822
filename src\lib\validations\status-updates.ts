import { z } from 'zod'
import { shipmentStatusSchema } from './shipment'

// Status update validation schema
export const statusUpdateSchema = z.object({
  shipment_id: z.string().uuid('Invalid shipment ID'),
  status_from: shipmentStatusSchema.optional(),
  status_to: shipmentStatusSchema,
  notes: z.string().optional(),
  location: z.string().optional(),
  latitude: z
    .number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90')
    .optional(),
  longitude: z
    .number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180')
    .optional(),
})

// Status update form schema for UI
export const statusUpdateFormSchema = z.object({
  status_to: shipmentStatusSchema,
  notes: z.string().optional(),
  location: z.string().optional(),
  coordinates: z
    .object({
      lat: z.number().min(-90).max(90),
      lng: z.number().min(-180).max(180),
    })
    .optional(),
})

// Create dynamic schema based on status requirements (for conditional validation)
export const createStatusUpdateFormSchema = (selectedStatus?: ShipmentStatus) => {
  const baseSchema = z.object({
    status_to: shipmentStatusSchema,
    notes: z.string().optional(),
    coordinates: z
      .object({
        lat: z.number().min(-90).max(90),
        lng: z.number().min(-180).max(180),
      })
      .optional(),
  })

  // Location is always optional - remove conditional requirement
  const locationField = z.string().optional()

  return baseSchema.extend({
    location: locationField,
  })
}

// Status history display interface
export interface StatusHistory {
  id: string
  shipment_id: string
  status_from: string | null
  status_to: string
  notes: string | null
  location: string | null
  latitude: number | null
  longitude: number | null
  updated_by: string | null
  created_at: string
  gps_coordinates?: any
  updated_by_profile?: {
    user_id: string
    full_name: string | null
    email: string | null
  }
}

// Status transition validation rules
export const STATUS_TRANSITIONS = {
  booking_confirmed: [
    'transport_assigned',
    'cancelled',
  ],
  transport_assigned: [
    'driver_assigned',
    'cancelled',
  ],
  driver_assigned: [
    'empty_container_picked',
    'cancelled',
  ],
  empty_container_picked: [
    'arrived_at_factory',
    'cancelled',
  ],
  arrived_at_factory: [
    'loading_started',
    'cancelled',
  ],
  loading_started: [
    'departed_factory',
    'cancelled',
  ],
  departed_factory: [
    'container_returned',
    'cancelled',
  ],
  container_returned: [
    'shipped',
    'cancelled',
  ],
  shipped: [
    'arrived',
    'cancelled',
  ],
  arrived: [
    'completed',
    'cancelled',
  ],
  completed: [],
  cancelled: [],
} as const

// Type exports
export type StatusUpdate = z.infer<typeof statusUpdateSchema>
export type StatusUpdateForm = z.infer<typeof statusUpdateFormSchema>
export type ShipmentStatus = z.infer<typeof shipmentStatusSchema>

// Validation helper functions
export const isValidStatusTransition = (
  currentStatus: ShipmentStatus,
  nextStatus: ShipmentStatus
): boolean => {
  const allowedTransitions = STATUS_TRANSITIONS[currentStatus] || []
  return allowedTransitions.includes(nextStatus as any)
}

export const getNextValidStatuses = (
  currentStatus: ShipmentStatus
): ShipmentStatus[] => {
  return (STATUS_TRANSITIONS[currentStatus] || []) as ShipmentStatus[]
}

// Status metadata for UI display
export const STATUS_METADATA = {
  booking_confirmed: {
    description: 'Booking has been confirmed',
    requiresLocation: false,
    isTerminal: false,
  },
  transport_assigned: {
    description: 'Transport has been assigned',
    requiresLocation: false,
    isTerminal: false,
  },
  driver_assigned: {
    description: 'Driver has been assigned',
    requiresLocation: false,
    isTerminal: false,
  },
  empty_container_picked: {
    description: 'Empty container has been picked up',
    requiresLocation: true,
    isTerminal: false,
  },
  arrived_at_factory: {
    description: 'Arrived at factory location',
    requiresLocation: true,
    isTerminal: false,
  },
  loading_started: {
    description: 'Loading process has started',
    requiresLocation: true,
    isTerminal: false,
  },
  departed_factory: {
    description: 'Departed from factory',
    requiresLocation: true,
    isTerminal: false,
  },
  container_returned: {
    description: 'Container has been returned',
    requiresLocation: true,
    isTerminal: false,
  },
  shipped: {
    description: 'Shipment has been dispatched',
    requiresLocation: false,
    isTerminal: false,
  },
  arrived: {
    description: 'Arrived at destination',
    requiresLocation: true,
    isTerminal: false,
  },
  completed: {
    description: 'Shipment completed successfully',
    requiresLocation: false,
    isTerminal: true,
  },
  cancelled: {
    description: 'Shipment has been cancelled',
    requiresLocation: false,
    isTerminal: true,
  },
} as const