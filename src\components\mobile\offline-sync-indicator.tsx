'use client'

import { useState, useEffect } from 'react'
import { 
  Wifi, 
  WifiOff, 
  <PERSON>fresh<PERSON><PERSON>, 
  <PERSON>ertCircle, 
  CheckCircle, 
  Clock,
  HardDrive 
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { OfflineSyncService } from '@/lib/utils/offline-sync'
import { OfflineStorageService } from '@/lib/utils/offline-storage'

interface SyncStatus {
  isOnline: boolean
  isSyncing: boolean
  pendingCount: number
  lastSyncTime: number | null
  errors: string[]
}

interface SyncStats {
  totalPending: number
  totalFailed: number
  storageUsage: { updates: number; photos: number; totalSizeMB: number }
  lastSyncTime: number | null
}

export function OfflineSyncIndicator() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: true,
    isSyncing: false,
    pendingCount: 0,
    lastSyncTime: null,
    errors: []
  })

  const [syncStats, setSyncStats] = useState<SyncStats | null>(null)
  const [showDetails, setShowDetails] = useState(false)
  const [isManualSyncing, setIsManualSyncing] = useState(false)

  // Update sync status
  useEffect(() => {
    const updateStatus = async () => {
      try {
        const status = await OfflineSyncService.getSyncStatus()
        setSyncStatus(status)
        
        // Update stats if details dialog is open
        if (showDetails) {
          const stats = await OfflineSyncService.getSyncStats()
          setSyncStats(stats)
        }
      } catch (error) {
        console.error('Failed to get sync status:', error)
      }
    }

    updateStatus()

    // Listen for online/offline events
    const handleOnline = () => updateStatus()
    const handleOffline = () => updateStatus()

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Listen for sync status updates
    const unsubscribe = OfflineSyncService.addSyncListener((status) => {
      setSyncStatus(status)
    })

    // Periodic updates
    const interval = setInterval(updateStatus, 30000) // Every 30 seconds

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      unsubscribe()
      clearInterval(interval)
    }
  }, [showDetails])

  const handleManualSync = async () => {
    if (!syncStatus.isOnline || syncStatus.isSyncing || isManualSyncing) return

    try {
      setIsManualSyncing(true)
      await OfflineSyncService.forceSyncWithRetry(3)
    } catch (error) {
      console.error('Manual sync failed:', error)
    } finally {
      setIsManualSyncing(false)
    }
  }

  const formatLastSyncTime = (timestamp: number | null): string => {
    if (!timestamp) return 'Never'
    
    const now = Date.now()
    const diffMs = now - timestamp
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    
    if (diffMinutes < 1) return 'Just now'
    if (diffMinutes < 60) return `${diffMinutes}m ago`
    
    const diffHours = Math.floor(diffMinutes / 60)
    if (diffHours < 24) return `${diffHours}h ago`
    
    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays}d ago`
  }

  const getStatusColor = (): string => {
    if (!syncStatus.isOnline) return 'bg-red-500'
    if (syncStatus.isSyncing || isManualSyncing) return 'bg-orange-500'
    if (syncStatus.pendingCount > 0) return 'bg-yellow-500'
    if (syncStatus.errors.length > 0) return 'bg-red-500'
    return 'bg-green-500'
  }

  const getStatusIcon = () => {
    if (!syncStatus.isOnline) return <WifiOff className="w-4 h-4" />
    if (syncStatus.isSyncing || isManualSyncing) return <RefreshCw className="w-4 h-4 animate-spin" />
    if (syncStatus.pendingCount > 0) return <Clock className="w-4 h-4" />
    if (syncStatus.errors.length > 0) return <AlertCircle className="w-4 h-4" />
    return <CheckCircle className="w-4 h-4" />
  }

  const getStatusText = (): string => {
    if (!syncStatus.isOnline) return 'Offline'
    if (syncStatus.isSyncing || isManualSyncing) return 'Syncing...'
    if (syncStatus.pendingCount > 0) return `${syncStatus.pendingCount} pending`
    if (syncStatus.errors.length > 0) return 'Sync errors'
    return 'All synced'
  }

  return (
    <>
      {/* Main Status Indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <Dialog open={showDetails} onOpenChange={setShowDetails}>
          <DialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={`${getStatusColor()} hover:opacity-90 text-white shadow-lg`}
            >
              {getStatusIcon()}
              <span className="ml-2 text-sm font-medium">
                {getStatusText()}
              </span>
            </Button>
          </DialogTrigger>

          <DialogContent className="bg-slate-900 border-slate-700 text-white max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                {syncStatus.isOnline ? (
                  <Wifi className="w-5 h-5 text-green-400" />
                ) : (
                  <WifiOff className="w-5 h-5 text-red-400" />
                )}
                <span>Sync Status</span>
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              {/* Connection Status */}
              <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-slate-300">Connection</span>
                  <Badge 
                    variant="outline"
                    className={`${
                      syncStatus.isOnline 
                        ? 'bg-green-600 text-white border-green-500' 
                        : 'bg-red-600 text-white border-red-500'
                    }`}
                  >
                    {syncStatus.isOnline ? 'Online' : 'Offline'}
                  </Badge>
                </div>

                <div className="text-xs text-slate-400">
                  {syncStatus.isOnline 
                    ? 'Status updates will sync automatically'
                    : 'Status updates will be queued locally'
                  }
                </div>
              </div>

              {/* Pending Updates */}
              {syncStatus.pendingCount > 0 && (
                <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-slate-300">Pending Updates</span>
                    <Badge variant="outline" className="bg-yellow-600 text-white border-yellow-500">
                      {syncStatus.pendingCount}
                    </Badge>
                  </div>

                  <div className="text-xs text-slate-400 mb-3">
                    Status updates waiting to be synchronized
                  </div>

                  {syncStatus.isOnline && !syncStatus.isSyncing && (
                    <Button
                      onClick={handleManualSync}
                      disabled={isManualSyncing}
                      size="sm"
                      className="w-full bg-orange-600 hover:bg-orange-700 text-white"
                    >
                      {isManualSyncing ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Syncing...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2" />
                          Sync Now
                        </>
                      )}
                    </Button>
                  )}
                </div>
              )}

              {/* Sync Progress */}
              {syncStatus.isSyncing && (
                <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <RefreshCw className="w-4 h-4 text-orange-400 animate-spin" />
                    <span className="text-sm font-medium text-slate-300">Synchronizing</span>
                  </div>
                  <Progress value={50} className="h-2" />
                  <div className="text-xs text-slate-400 mt-2">
                    Uploading status updates and photos...
                  </div>
                </div>
              )}

              {/* Errors */}
              {syncStatus.errors.length > 0 && (
                <Alert className="border-red-500/30 bg-red-500/10">
                  <AlertCircle className="h-4 w-4 text-red-400" />
                  <AlertDescription className="text-red-300">
                    <div className="text-sm font-medium mb-1">Sync Errors</div>
                    <div className="text-xs space-y-1">
                      {syncStatus.errors.slice(0, 3).map((error, index) => (
                        <div key={index}>{error}</div>
                      ))}
                      {syncStatus.errors.length > 3 && (
                        <div>...and {syncStatus.errors.length - 3} more</div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Last Sync Time */}
              <div className="text-xs text-slate-500 text-center">
                Last sync: {formatLastSyncTime(syncStatus.lastSyncTime)}
              </div>

              {/* Storage Info */}
              {syncStats && (
                <div className="bg-slate-800 rounded-lg border border-slate-700 p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <HardDrive className="w-4 h-4 text-slate-400" />
                    <span className="text-sm font-medium text-slate-300">Storage Usage</span>
                  </div>

                  <div className="space-y-2 text-xs text-slate-400">
                    <div className="flex justify-between">
                      <span>Updates:</span>
                      <span>{syncStats.storageUsage.updates}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Photos:</span>
                      <span>{syncStats.storageUsage.photos}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Size:</span>
                      <span>{syncStats.storageUsage.totalSizeMB.toFixed(1)} MB</span>
                    </div>
                  </div>

                  {syncStats.storageUsage.totalSizeMB > 50 && (
                    <div className="text-xs text-orange-400 mt-2">
                      High storage usage - consider syncing when online
                    </div>
                  )}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Offline Banner */}
      {!syncStatus.isOnline && (
        <div className="fixed top-0 left-0 right-0 z-40 bg-red-600 text-white p-2">
          <div className="flex items-center justify-center space-x-2 text-sm">
            <WifiOff className="w-4 h-4" />
            <span>You are offline. Status updates will be queued locally.</span>
          </div>
        </div>
      )}
    </>
  )
}