import type { ShipmentStatus, StatusTransition } from '@/types/status-update';

// Status Progression Logic based on business rules
export const StatusTransitions: Record<ShipmentStatus, ShipmentStatus[]> = {
  'booking_confirmed': ['transport_assigned', 'cancelled'],
  'transport_assigned': ['driver_assigned', 'cancelled'],
  'driver_assigned': ['empty_container_picked', 'cancelled'],
  'empty_container_picked': ['arrived_at_factory'],
  'arrived_at_factory': ['loading_started'],
  'loading_started': ['departed_factory'],
  'departed_factory': ['container_returned'],
  'container_returned': ['shipped'],
  'shipped': ['arrived'],
  'arrived': ['completed'],
  'completed': [], // Final state
  'cancelled': [] // Final state
};

// Photo requirements by status transition
export const StatusPhotoRequirements: Record<string, StatusTransition> = {
  'empty_container_picked': {
    from: 'driver_assigned',
    to: 'empty_container_picked',
    requiresPhotos: true,
    minPhotos: 2,
    maxPhotos: 5,
    description: 'Empty container pickup - Show container condition and seal'
  },
  'arrived_at_factory': {
    from: 'empty_container_picked',
    to: 'arrived_at_factory',
    requiresPhotos: true,
    minPhotos: 1,
    maxPhotos: 3,
    description: 'Arrived at factory - Location confirmation'
  },
  'loading_started': {
    from: 'arrived_at_factory',
    to: 'loading_started',
    requiresPhotos: true,
    minPhotos: 2,
    maxPhotos: 5,
    description: 'Loading started - Container and product photos'
  },
  'departed_factory': {
    from: 'loading_started',
    to: 'departed_factory',
    requiresPhotos: true,
    minPhotos: 2,
    maxPhotos: 5,
    description: 'Departed factory - Loaded container and seal photos'
  },
  'container_returned': {
    from: 'departed_factory',
    to: 'container_returned',
    requiresPhotos: true,
    minPhotos: 1,
    maxPhotos: 3,
    description: 'Container returned - Return location confirmation'
  },
  'shipped': {
    from: 'container_returned',
    to: 'shipped',
    requiresPhotos: false,
    minPhotos: 0,
    maxPhotos: 2,
    description: 'Shipped - Optional shipping documentation'
  },
  'arrived': {
    from: 'shipped',
    to: 'arrived',
    requiresPhotos: true,
    minPhotos: 1,
    maxPhotos: 3,
    description: 'Arrived at destination - Arrival confirmation'
  },
  'completed': {
    from: 'arrived',
    to: 'completed',
    requiresPhotos: false,
    minPhotos: 0,
    maxPhotos: 2,
    description: 'Shipment completed - Optional completion documentation'
  },
  // All other transitions require at least 1 photo
  'default': {
    from: 'booking_confirmed',
    to: 'container_assigned',
    requiresPhotos: true,
    minPhotos: 1,
    maxPhotos: 5,
    description: 'Status update documentation'
  }
};

/**
 * Get available next status options for a current status
 */
export function getNextStatusOptions(currentStatus: ShipmentStatus): ShipmentStatus[] {
  return StatusTransitions[currentStatus] || [];
}

/**
 * Validate if a status transition is allowed
 */
export function isValidStatusTransition(from: ShipmentStatus, to: ShipmentStatus): boolean {
  const availableTransitions = StatusTransitions[from] || [];
  return availableTransitions.includes(to);
}

/**
 * Get photo requirements for a specific status transition
 */
export function getPhotoRequirements(toStatus: ShipmentStatus): StatusTransition {
  return StatusPhotoRequirements[toStatus] || StatusPhotoRequirements['default'];
}

/**
 * Validate photo count for a status transition
 */
export function validatePhotosForStatus(toStatus: ShipmentStatus, photoCount: number): {
  isValid: boolean;
  message: string;
} {
  const requirements = getPhotoRequirements(toStatus);
  
  if (photoCount < requirements.minPhotos) {
    return {
      isValid: false,
      message: `Minimum ${requirements.minPhotos} photo${requirements.minPhotos > 1 ? 's' : ''} required for ${getStatusDisplayName(toStatus)}`
    };
  }
  
  if (photoCount > requirements.maxPhotos) {
    return {
      isValid: false,
      message: `Maximum ${requirements.maxPhotos} photos allowed per status update`
    };
  }
  
  return {
    isValid: true,
    message: 'Photo count is valid'
  };
}

/**
 * Get user-friendly display name for status
 */
export function getStatusDisplayName(status: ShipmentStatus): string {
  const displayNames: Record<ShipmentStatus, string> = {
    'booking_confirmed': 'Booking Confirmed',
    'transport_assigned': 'Transport Assigned',
    'driver_assigned': 'Driver Assigned',
    'empty_container_picked': 'Empty Container Picked',
    'arrived_at_factory': 'Arrived at Factory',
    'loading_started': 'Loading Started',
    'departed_factory': 'Departed Factory',
    'container_returned': 'Container Returned',
    'shipped': 'Shipped',
    'arrived': 'Arrived',
    'completed': 'Completed',
    'cancelled': 'Cancelled'
  };
  
  return displayNames[status] || status;
}

/**
 * Get status progress percentage (0-100)
 */
export function getStatusProgress(status: ShipmentStatus): number {
  const progressMap: Record<ShipmentStatus, number> = {
    'booking_confirmed': 5,
    'transport_assigned': 10,
    'driver_assigned': 15,
    'empty_container_picked': 25,
    'arrived_at_factory': 35,
    'loading_started': 50,
    'departed_factory': 65,
    'container_returned': 75,
    'shipped': 85,
    'arrived': 95,
    'completed': 100,
    'cancelled': 0
  };
  
  return progressMap[status] || 0;
}

/**
 * Check if status is a final state (no further transitions possible)
 */
export function isFinalStatus(status: ShipmentStatus): boolean {
  return status === 'completed' || status === 'cancelled';
}

/**
 * Get status color based on shipment state
 */
export function getStatusColor(status: ShipmentStatus): string {
  const colorMap: Record<ShipmentStatus, string> = {
    'booking_confirmed': 'bg-blue-500',
    'container_assigned': 'bg-blue-600',
    'pickup_scheduled': 'bg-yellow-500',
    'pickup_completed': 'bg-orange-500',
    'in_transit': 'bg-orange-600',
    'arrived_destination': 'bg-green-500',
    'delivery_scheduled': 'bg-green-600',
    'delivery_completed': 'bg-green-700',
    'documentation_complete': 'bg-green-800',
    'completed': 'bg-emerald-600',
    'cancelled': 'bg-red-500'
  };
  
  return colorMap[status] || 'bg-slate-500';
}

/**
 * Get next logical status for automatic progression
 */
export function getNextLogicalStatus(currentStatus: ShipmentStatus): ShipmentStatus | null {
  const nextOptions = getNextStatusOptions(currentStatus);
  
  // Filter out 'cancelled' as it's not part of normal progression
  const progressionOptions = nextOptions.filter(status => status !== 'cancelled');
  
  // Return the first (and usually only) progression option
  return progressionOptions.length > 0 ? progressionOptions[0] : null;
}