'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Bell,
  Building2,
  Star,
  StarOff,
  Hash,
  Info,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Mail,
  Smartphone,
  MessageSquare,
  Loader2,
  Package,
  Users,
  Link,
} from 'lucide-react'
import {
  useConsigneeOptions,
  useConsigneeNotifyPartiesRealtime,
  useConsigneeNotifyPartyCheck,
} from '@/hooks/use-consignee-notify-parties'
import { useCustomerConsignees } from '@/hooks/use-customer-consignees'
import type { ConsigneeNotifyParty } from '@/stores/consignee-notify-party-store'

interface EnhancedConsigneeIntegrationProps {
  // Customer context for filtering consignees
  selectedCustomerId?: string

  // Form values from parent shipment form
  selectedConsigneeId?: string
  selectedNotifyPartyId?: string

  // Callbacks to update parent form
  onConsigneeChange?: (consigneeId: string, consigneeData?: any) => void
  onNotifyPartyChange?: (
    notifyPartyId: string,
    relationship?: ConsigneeNotifyParty
  ) => void
  onNotificationPreferencesChange?: (preferences: any) => void
  onSpecialInstructionsChange?: (instructions: string) => void

  // Configuration options
  autoSelectDefault?: boolean
  showRelationshipDetails?: boolean
  disabled?: boolean

  // Visual customization
  compact?: boolean
  showPriorityOrder?: boolean
}

export function EnhancedConsigneeIntegration({
  selectedCustomerId,
  selectedConsigneeId,
  selectedNotifyPartyId,
  onConsigneeChange,
  onNotifyPartyChange,
  onNotificationPreferencesChange,
  onSpecialInstructionsChange,
  autoSelectDefault = true,
  showRelationshipDetails = true,
  disabled = false,
  compact = false,
  showPriorityOrder = false,
}: EnhancedConsigneeIntegrationProps) {
  const [isInitializing, setIsInitializing] = useState(false)

  // Customer-consignee relationship integration
  const {
    customerConsignees,
    loading: loadingCustomerConsignees,
    getConsigneesForCustomer,
    getDefaultConsigneeForCustomer,
  } = useCustomerConsignees(selectedCustomerId)

  // Get available consignees based on customer-consignee relationships
  const availableConsignees = useMemo(() => {
    if (!selectedCustomerId) return []

    return customerConsignees
      .filter(rel => rel.is_active) // Only active relationships
      .map(rel => rel.consignee)
      .filter(Boolean) // Remove any null consignees
  }, [selectedCustomerId, customerConsignees])

  // Get default consignee for the selected customer
  const defaultConsigneeRelationship = useMemo(() => {
    if (!selectedCustomerId) return null
    return getDefaultConsigneeForCustomer(selectedCustomerId)
  }, [selectedCustomerId, getDefaultConsigneeForCustomer])

  // Existing consignee-notify party functionality
  const { consigneeNotifyParties, loading: loadingRelationships } =
    useConsigneeNotifyPartiesRealtime(selectedConsigneeId)

  const {
    hasRelationships,
    defaultNotifyParty,
    relationshipCount,
    loading: loadingCheck,
  } = useConsigneeNotifyPartyCheck(selectedConsigneeId)

  // Find selected relationship details
  const selectedRelationship = consigneeNotifyParties.find(
    rel => rel.notify_party_id === selectedNotifyPartyId
  )

  // Get customer-consignee relationship info for display
  const customerConsigneeRelationship = useMemo(() => {
    if (!selectedCustomerId || !selectedConsigneeId) return null
    return customerConsignees.find(rel => rel.consignee_id === selectedConsigneeId)
  }, [selectedCustomerId, selectedConsigneeId, customerConsignees])

  // Auto-select default consignee when customer changes
  useEffect(() => {
    if (!selectedCustomerId || !autoSelectDefault || loadingCustomerConsignees) return

    if (defaultConsigneeRelationship && !selectedConsigneeId) {
      // Auto-select the default consignee
      const consigneeData = defaultConsigneeRelationship.consignee
      onConsigneeChange?.(defaultConsigneeRelationship.consignee_id, consigneeData)
    }
  }, [
    selectedCustomerId,
    defaultConsigneeRelationship,
    selectedConsigneeId,
    autoSelectDefault,
    loadingCustomerConsignees,
    onConsigneeChange,
  ])

  // Auto-select default notify party when consignee changes
  useEffect(() => {
    if (!selectedConsigneeId || !autoSelectDefault || isInitializing) return

    setIsInitializing(true)

    // Wait for relationships to load
    if (loadingRelationships) return

    // If there's a default notify party and no current selection, auto-select it
    if (defaultNotifyParty && !selectedNotifyPartyId) {
      onNotifyPartyChange?.(
        defaultNotifyParty.notify_party_id,
        defaultNotifyParty
      )

      // Also populate related data
      if (defaultNotifyParty.notification_preferences) {
        onNotificationPreferencesChange?.(
          defaultNotifyParty.notification_preferences
        )
      }

      if (defaultNotifyParty.special_instructions) {
        onSpecialInstructionsChange?.(defaultNotifyParty.special_instructions)
      }
    }

    setIsInitializing(false)
  }, [
    selectedConsigneeId,
    defaultNotifyParty,
    selectedNotifyPartyId,
    autoSelectDefault,
    loadingRelationships,
    onNotifyPartyChange,
    onNotificationPreferencesChange,
    onSpecialInstructionsChange,
    isInitializing,
  ])

  // Handle consignee selection
  const handleConsigneeChange = useCallback(
    (consigneeId: string) => {
      const consigneeData = availableConsignees.find(c => c.id === consigneeId)
      onConsigneeChange?.(consigneeId, consigneeData)

      // Clear notify party selection when consignee changes
      onNotifyPartyChange?.('', undefined)
    },
    [availableConsignees, onConsigneeChange, onNotifyPartyChange]
  )

  // Handle notify party selection
  const handleNotifyPartyChange = useCallback(
    (notifyPartyId: string) => {
      const relationship = consigneeNotifyParties.find(
        rel => rel.notify_party_id === notifyPartyId
      )

      onNotifyPartyChange?.(notifyPartyId, relationship)

      // Auto-populate related fields if relationship exists
      if (relationship) {
        if (relationship.notification_preferences) {
          onNotificationPreferencesChange?.(
            relationship.notification_preferences
          )
        }

        if (relationship.special_instructions) {
          onSpecialInstructionsChange?.(relationship.special_instructions)
        }
      }
    },
    [
      consigneeNotifyParties,
      onNotifyPartyChange,
      onNotificationPreferencesChange,
      onSpecialInstructionsChange,
    ]
  )

  // Get notification icons
  const getNotificationIcons = (preferences: any) => {
    if (!preferences) return []
    const icons = []
    if (preferences.email)
      icons.push(<Mail key="email" className="h-3 w-3 text-blue-400" />)
    if (preferences.sms)
      icons.push(<Smartphone key="sms" className="h-3 w-3 text-green-400" />)
    if (preferences.line)
      icons.push(
        <MessageSquare key="line" className="h-3 w-3 text-green-500" />
      )
    if (preferences.wechat)
      icons.push(
        <MessageSquare key="wechat" className="h-3 w-3 text-green-600" />
      )
    return icons
  }

  // Customer relationship status
  const customerRelationshipStatus = useMemo(() => {
    if (!selectedCustomerId) {
      return { status: 'no_customer', message: 'No customer selected' }
    }

    if (loadingCustomerConsignees) {
      return { status: 'loading', message: 'Loading customer relationships...' }
    }

    if (customerConsignees.length === 0) {
      return {
        status: 'no_relationships',
        message: 'Customer has no consignee relationships configured'
      }
    }

    const activeRelationships = customerConsignees.filter(rel => rel.is_active)
    if (activeRelationships.length === 0) {
      return {
        status: 'no_active',
        message: 'Customer has no active consignee relationships'
      }
    }

    return {
      status: 'has_relationships',
      message: `${activeRelationships.length} active consignee${activeRelationships.length !== 1 ? 's' : ''} available`
    }
  }, [selectedCustomerId, loadingCustomerConsignees, customerConsignees])

  if (compact) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Compact Consignee Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-slate-200 flex items-center gap-2">
            <Package className="h-3 w-3 text-green-500" />
            Consignee *
          </label>
          <Select
            value={selectedConsigneeId || ''}
            onValueChange={handleConsigneeChange}
            disabled={disabled || loadingCustomerConsignees || !selectedCustomerId}
          >
            <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue
                placeholder={
                  !selectedCustomerId
                    ? 'Select customer first'
                    : loadingCustomerConsignees
                    ? 'Loading consignees...'
                    : 'Select consignee'
                }
              />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
              {loadingCustomerConsignees ? (
                <div className="p-4 text-center text-slate-400">
                  <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                  Loading consignees...
                </div>
              ) : availableConsignees.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  {!selectedCustomerId
                    ? 'Select customer first'
                    : 'No consignees configured for this customer'
                  }
                </div>
              ) : (
                availableConsignees.map(consignee => {
                  const relationship = customerConsignees.find(rel => rel.consignee_id === consignee.id)
                  return (
                    <SelectItem
                      key={consignee.id}
                      value={consignee.id}
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      <div className="flex items-center space-x-2">
                        <Package className="h-3 w-3 text-green-500" />
                        <div>
                          <div className="flex items-center gap-2">
                            {consignee.name}
                            {relationship?.is_default && (
                              <Star className="h-3 w-3 text-yellow-500" />
                            )}
                          </div>
                          {consignee.contact_phone && (
                            <div className="text-xs text-slate-400">
                              Phone: {consignee.contact_phone}
                            </div>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  )
                })
              )}
            </SelectContent>
          </Select>

          {/* Compact relationship status */}
          {selectedCustomerId && (
            <div className="text-xs text-slate-400">
              {customerRelationshipStatus.status === 'loading' ? (
                <div className="flex items-center">
                  <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  {customerRelationshipStatus.message}
                </div>
              ) : customerRelationshipStatus.status === 'has_relationships' ? (
                <div className="flex items-center text-green-400">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  {customerRelationshipStatus.message}
                </div>
              ) : (
                <div className="flex items-center text-amber-400">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  {customerRelationshipStatus.message}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Compact Notify Party Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-slate-200 flex items-center gap-2">
            <Bell className="h-3 w-3 text-blue-500" />
            Notify Party *
          </label>
          <Select
            value={selectedNotifyPartyId || ''}
            onValueChange={handleNotifyPartyChange}
            disabled={disabled || !selectedConsigneeId || loadingRelationships}
          >
            <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue
                placeholder={
                  !selectedConsigneeId
                    ? 'Select consignee first'
                    : 'Select notify party'
                }
              />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
              {loadingRelationships ? (
                <div className="p-4 text-center text-slate-400">
                  <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                  Loading notify parties...
                </div>
              ) : consigneeNotifyParties.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  No notify parties configured for this consignee
                </div>
              ) : (
                consigneeNotifyParties.map(relationship => (
                  <SelectItem
                    key={relationship.notify_party_id}
                    value={relationship.notify_party_id}
                    className="text-slate-300 hover:bg-slate-700"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center space-x-2">
                        <Bell className="h-3 w-3 text-blue-500" />
                        <span>{relationship.notify_party?.name}</span>
                        {relationship.is_default && (
                          <Star className="h-3 w-3 text-yellow-500" />
                        )}
                      </div>
                      {showPriorityOrder && (
                        <Badge variant="outline" className="text-xs">
                          #{relationship.priority_order}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Customer-Consignee Relationship Status */}
      {selectedCustomerId && (
        <Alert className={`${
          customerRelationshipStatus.status === 'has_relationships'
            ? 'border-green-600 bg-green-900/20'
            : customerRelationshipStatus.status === 'no_relationships' || customerRelationshipStatus.status === 'no_active'
            ? 'border-amber-600 bg-amber-900/20'
            : 'border-blue-600 bg-blue-900/20'
        }`}>
          <Link className="h-4 w-4" />
          <AlertDescription className={`${
            customerRelationshipStatus.status === 'has_relationships'
              ? 'text-green-300'
              : customerRelationshipStatus.status === 'no_relationships' || customerRelationshipStatus.status === 'no_active'
              ? 'text-amber-300'
              : 'text-blue-300'
          }`}>
            <strong>Customer-Consignee Relationships:</strong> {customerRelationshipStatus.message}
            {(customerRelationshipStatus.status === 'no_relationships' || customerRelationshipStatus.status === 'no_active') && (
              <span className="block mt-1 text-sm">
                Please configure relationships in <strong>Master Data → Customer Relationships</strong> first.
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Consignee Selection */}
      <Card className="bg-slate-700 border-slate-600">
        <CardHeader>
          <CardTitle className="text-green-200 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-green-500" />
              Consignee Selection
              <Badge variant="outline" className="text-xs text-slate-300">
                Customer Filtered
              </Badge>
            </div>
          </CardTitle>
          <CardDescription className="text-slate-400">
            Select the consignee from customer-configured relationships
            {defaultConsigneeRelationship && (
              <span className="block mt-1 text-green-300">
                Default consignee available for auto-selection
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Select
            value={selectedConsigneeId || ''}
            onValueChange={handleConsigneeChange}
            disabled={disabled || loadingCustomerConsignees || !selectedCustomerId}
          >
            <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
              <SelectValue
                placeholder={
                  !selectedCustomerId
                    ? 'Select customer first'
                    : loadingCustomerConsignees
                    ? 'Loading customer consignees...'
                    : 'Select consignee company'
                }
              />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
              {loadingCustomerConsignees ? (
                <div className="p-4 text-center text-slate-400">
                  <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                  Loading customer consignees...
                </div>
              ) : !selectedCustomerId ? (
                <div className="p-4 text-center text-slate-400">
                  Please select a customer first
                </div>
              ) : availableConsignees.length === 0 ? (
                <div className="p-4 text-center text-slate-400">
                  No consignee relationships configured for this customer
                </div>
              ) : (
                availableConsignees.map(consignee => {
                  const relationship = customerConsignees.find(rel => rel.consignee_id === consignee.id)
                  return (
                    <SelectItem
                      key={consignee.id}
                      value={consignee.id}
                      className="text-slate-300 hover:bg-slate-700"
                    >
                      <div className="flex items-center space-x-2">
                        <Package className="h-3 w-3 text-green-500" />
                        <div>
                          <div className="flex items-center gap-2">
                            {consignee.name}
                            {relationship?.is_default && (
                              <Badge
                                variant="outline"
                                className="text-xs bg-green-500/20 text-green-300 border-green-400"
                              >
                                Default
                              </Badge>
                            )}
                          </div>
                          {consignee.contact_phone && (
                            <div className="text-xs text-slate-400">
                              Phone: {consignee.contact_phone}
                            </div>
                          )}
                          {relationship?.notes && (
                            <div className="text-xs text-slate-500">
                              {relationship.notes}
                            </div>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  )
                })
              )}
            </SelectContent>
          </Select>

          {/* Customer-Consignee relationship details */}
          {customerConsigneeRelationship && showRelationshipDetails && (
            <Card className="bg-slate-800 border-slate-600">
              <CardContent className="pt-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-400">
                      Customer-Consignee Relationship
                    </span>
                    <div className="flex items-center gap-2">
                      {customerConsigneeRelationship.is_default && (
                        <Badge className="bg-yellow-600 text-white text-xs">
                          Default
                        </Badge>
                      )}
                      <Badge
                        variant={
                          customerConsigneeRelationship.is_active
                            ? 'default'
                            : 'secondary'
                        }
                        className={
                          customerConsigneeRelationship.is_active
                            ? 'bg-green-600 text-white'
                            : 'bg-red-600 text-white'
                        }
                      >
                        {customerConsigneeRelationship.is_active
                          ? 'Active'
                          : 'Inactive'}
                      </Badge>
                    </div>
                  </div>

                  {customerConsigneeRelationship.notes && (
                    <div>
                      <span className="text-sm text-slate-400">
                        Relationship Notes
                      </span>
                      <p className="text-sm text-slate-200 mt-1 p-2 bg-slate-700 rounded border border-slate-600">
                        {customerConsigneeRelationship.notes}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Notify Party Selection */}
      {selectedConsigneeId && (
        <Card className="bg-slate-700 border-slate-600">
          <CardHeader>
            <CardTitle className="text-blue-200 flex items-center gap-2">
              <Bell className="h-5 w-5 text-blue-500" />
              Notify Party Selection
            </CardTitle>
            <CardDescription className="text-slate-400">
              Select the notify party for this shipment (intelligently
              pre-populated based on consignee relationships)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {!hasRelationships ? (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className="text-slate-300">
                  No notify party relationships configured for this consignee.
                  You may need to set up relationships in Master Data →
                  Consignee Notify Party Relationships first.
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <Select
                  value={selectedNotifyPartyId || ''}
                  onValueChange={handleNotifyPartyChange}
                  disabled={disabled || loadingRelationships}
                >
                  <SelectTrigger className="bg-slate-800 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                    <SelectValue placeholder="Select notify party" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700 max-h-64 overflow-y-auto">
                    {loadingRelationships ? (
                      <div className="p-4 text-center text-slate-400">
                        <Loader2 className="h-4 w-4 animate-spin inline mr-2" />
                        Loading notify parties...
                      </div>
                    ) : consigneeNotifyParties.length === 0 ? (
                      <div className="p-4 text-center text-slate-400">
                        No notify parties found for this consignee
                      </div>
                    ) : (
                      consigneeNotifyParties
                        .sort((a, b) => {
                          // Sort by default first, then by priority order, then by name
                          if (a.is_default && !b.is_default) return -1
                          if (!a.is_default && b.is_default) return 1

                          const priorityA = a.priority_order || 999
                          const priorityB = b.priority_order || 999
                          if (priorityA !== priorityB)
                            return priorityA - priorityB

                          // Finally sort by notify party name
                          const nameA = a.notify_party?.name || ''
                          const nameB = b.notify_party?.name || ''
                          return nameA.localeCompare(nameB)
                        })
                        .map(relationship => (
                          <SelectItem
                            key={relationship.notify_party_id}
                            value={relationship.notify_party_id}
                            className="text-slate-300 hover:bg-slate-700"
                          >
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center space-x-2">
                                <Bell className="h-3 w-3 text-blue-500" />
                                <div>
                                  <div className="flex items-center gap-2">
                                    {relationship.notify_party?.name}
                                    {relationship.is_default && (
                                      <Star className="h-3 w-3 text-yellow-500" />
                                    )}
                                  </div>
                                  {relationship.notify_party?.contact_email && (
                                    <div className="text-xs text-slate-400">
                                      {relationship.notify_party.contact_email}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-1">
                                {showPriorityOrder && (
                                  <div className="flex items-center gap-1 text-xs text-slate-400">
                                    <Hash className="h-3 w-3" />
                                    {relationship.priority_order}
                                  </div>
                                )}
                                <div className="flex items-center gap-1">
                                  {getNotificationIcons(
                                    relationship.notification_preferences
                                  )}
                                </div>
                              </div>
                            </div>
                          </SelectItem>
                        ))
                    )}
                  </SelectContent>
                </Select>

                {/* Show selected relationship details */}
                {selectedRelationship && showRelationshipDetails && (
                  <Card className="bg-slate-800 border-slate-600">
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-slate-400">
                            Consignee-Notify Party Relationship
                          </span>
                          <div className="flex items-center gap-2">
                            {selectedRelationship.is_default && (
                              <Badge className="bg-yellow-600 text-white text-xs">
                                Default
                              </Badge>
                            )}
                            <Badge
                              variant={
                                selectedRelationship.is_active
                                  ? 'default'
                                  : 'secondary'
                              }
                              className={
                                selectedRelationship.is_active
                                  ? 'bg-green-600 text-white'
                                  : 'bg-red-600 text-white'
                              }
                            >
                              {selectedRelationship.is_active
                                ? 'Active'
                                : 'Inactive'}
                            </Badge>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-slate-400">
                            Notification Channels
                          </span>
                          <div className="flex items-center space-x-1">
                            {getNotificationIcons(
                              selectedRelationship.notification_preferences
                            )}
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-sm text-slate-400">
                            Priority Order
                          </span>
                          <div className="flex items-center gap-1 text-sm text-slate-200">
                            <Hash className="h-3 w-3" />
                            {selectedRelationship.priority_order}
                          </div>
                        </div>

                        {selectedRelationship.special_instructions && (
                          <div>
                            <span className="text-sm text-slate-400">
                              Special Instructions
                            </span>
                            <p className="text-sm text-slate-200 mt-1 p-2 bg-slate-700 rounded border border-slate-600">
                              {selectedRelationship.special_instructions}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}