'use client'

import { Package, MapPin, Calendar, Truck, Factory, User, Clock, Box, Container, Plus } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useRouter } from 'next/navigation'
import { useLanguage } from '@/hooks/use-language'
import { Button } from '@/components/ui/button'

interface ShipmentCardProps {
  assignment: {
    id: string
    shipment: {
      id: string
      shipment_number: string
      status: string
      customer?: {
        name: string
      }
      factory?: {
        name: string
      }
      origin_port?: {
        name: string
      }
      destination_port?: {
        name: string
      }
      etd_date?: string
      eta_date?: string
      products?: Array<{
        product_description?: string
        quantity: number
        packaging_type: string
        unit_of_measure?: {
          name: string
        }
        product?: {
          name: string
        }
      }>
      containers?: Array<{
        id: string
        container_number?: string
        seal_number?: string
        container_type?: string
        container_size?: string
        status?: string
      }>
      status_updates?: Array<{
        status_to: string
        created_at: string
      }>
    }
    pickup_container_location?: string
    pickup_product_location?: string
    delivery_location?: string
    assignment_date?: string
    estimated_distance?: number
  }
}

const statusColors = {
  'booking_confirmed': 'bg-blue-900/20 text-blue-400 border-blue-500/30',
  'transport_assigned': 'bg-purple-900/20 text-purple-400 border-purple-500/30',
  'driver_assigned': 'bg-indigo-900/20 text-indigo-400 border-indigo-500/30',
  'empty_container_picked': 'bg-teal-900/20 text-teal-400 border-teal-500/30',
  'arrived_at_factory': 'bg-cyan-900/20 text-cyan-400 border-cyan-500/30',
  'loading_started': 'bg-yellow-900/20 text-yellow-400 border-yellow-500/30',
  'departed_factory': 'bg-amber-900/20 text-amber-400 border-amber-500/30',
  'container_returned': 'bg-lime-900/20 text-lime-400 border-lime-500/30',
  'shipped': 'bg-orange-900/20 text-orange-400 border-orange-500/30',
  'arrived': 'bg-green-900/20 text-green-400 border-green-500/30',
  'completed': 'bg-emerald-900/20 text-emerald-400 border-emerald-500/30',
  'cancelled': 'bg-red-900/20 text-red-400 border-red-500/30',
}

const statusLabels = {
  'booking_confirmed': 'Booking Confirmed',
  'transport_assigned': 'Transport Assigned',
  'driver_assigned': 'Driver Assigned',
  'empty_container_picked': 'Empty Container Picked',
  'arrived_at_factory': 'Arrived at Factory',
  'loading_started': 'Loading Started',
  'departed_factory': 'Departed Factory',
  'container_returned': 'Container Returned',
  'shipped': 'Shipped',
  'arrived': 'Arrived',
  'completed': 'Completed',
  'cancelled': 'Cancelled',
}

export function ShipmentCard({ assignment }: ShipmentCardProps) {
  const router = useRouter()
  const { t } = useLanguage()
  const { shipment } = assignment
  const statusClass = statusColors[shipment.status as keyof typeof statusColors] || statusColors.booking_confirmed
  const statusLabel = t(`status.${shipment.status}`, statusLabels[shipment.status as keyof typeof statusLabels] || shipment.status)

  const handleStatusUpdate = () => {
    router.push(`/driver/status-update?shipment_id=${shipment.id}`)
  }

  const handleContainerDataEntry = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent card click
    // Generate a new container ID for creating new container
    const containerId = `container-${shipment.id}-${Date.now()}`
    router.push(`/driver/container-data?shipmentId=${shipment.id}&containerId=${containerId}&shipmentNumber=${shipment.shipment_number}&returnUrl=${encodeURIComponent('/driver/dashboard')}`)
  }

  const handleCardClick = () => {
    handleStatusUpdate()
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return t('shipment.notSet')
    return new Date(dateString).toLocaleDateString('th-TH', {
      day: '2-digit',
      month: 'short',
      year: '2-digit'
    })
  }

  const formatTime = (dateString?: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleTimeString('th-TH', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get timestamp for specific status
  const getStatusTimestamp = (statusToFind: string) => {
    if (!shipment.status_updates) return null
    const statusUpdate = shipment.status_updates.find(update => update.status_to === statusToFind)
    return statusUpdate?.created_at
  }

  // Format datetime for status timestamps
  const formatStatusDateTime = (dateString?: string) => {
    if (!dateString) return null
    const date = new Date(dateString)
    return {
      date: date.toLocaleDateString('th-TH', {
        day: '2-digit',
        month: 'short',
        year: '2-digit'
      }),
      time: date.toLocaleTimeString('th-TH', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  // Get products summary - returns array for multiple products or string for single
  const getProductsSummary = () => {
    if (!shipment.products || shipment.products.length === 0) return null
    
    return shipment.products.map(product => {
      const productName = product.product_description || product.product?.name || 'Unknown Product'
      const quantity = product.quantity
      const packagingType = product.packaging_type || ''
      
      // Format: "Product Name - 100 Bags"
      return `${productName} - ${quantity} ${t(`packaging.${packagingType.toLowerCase().replace(' ', '_')}`, packagingType)}`
    })
  }

  // Get containers summary - returns array with container details
  const getContainersSummary = () => {
    if (!shipment.containers || shipment.containers.length === 0) return null

    return shipment.containers.map(container => ({
      id: container.id,
      display: `${container.container_number || 'No Number'} | ${container.seal_number || 'No Seal'}`,
      containerNumber: container.container_number,
      sealNumber: container.seal_number,
      type: container.container_type,
      size: container.container_size,
      containerNumberConfirmed: container.container_number_confirmed || false,
      sealNumberConfirmed: container.seal_number_confirmed || false
    }))
  }

  return (
    <div 
      className="bg-slate-800 rounded-xl border border-slate-700 overflow-hidden cursor-pointer hover:bg-slate-750 transition-colors duration-200"
      onClick={handleCardClick}
    >
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Package className="w-5 h-5 text-orange-500 flex-shrink-0" />
            <span className="font-semibold text-white text-base">
              {shipment.shipment_number}
            </span>
          </div>
          <Badge
            variant="outline"
            className={`${statusClass} border font-medium px-2 py-1 text-xs`}
          >
            {statusLabel}
          </Badge>
        </div>

        {/* Customer & Factory Info */}
        <div className="space-y-2 mb-4">
          {shipment.customer && (
            <div className="flex items-center space-x-2">
              <User className="w-4 h-4 text-slate-400 flex-shrink-0" />
              <span className="text-slate-300 text-sm">
                {shipment.customer.name}
              </span>
            </div>
          )}
          {shipment.factory && (
            <div className="flex items-center space-x-2">
              <Factory className="w-4 h-4 text-slate-400 flex-shrink-0" />
              <span className="text-slate-300 text-sm">
                {shipment.factory.name}
              </span>
            </div>
          )}
        </div>

        {/* Products Info */}
        {getProductsSummary() && (
          <div className="mb-4 space-y-2">
            {getProductsSummary()!.map((productInfo, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Box className="w-4 h-4 text-slate-400 flex-shrink-0" />
                <span className="text-slate-300 text-sm">
                  {productInfo}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Containers Info */}
        {getContainersSummary() && (
          <div className="mb-4 space-y-3">
            {getContainersSummary()!.map((containerInfo, index) => {
              const bothConfirmed = containerInfo.containerNumberConfirmed && containerInfo.sealNumberConfirmed
              const canEdit = !bothConfirmed // Drivers can edit if not both confirmed

              return (
                <div key={containerInfo.id} className="bg-slate-700/30 rounded-lg p-3 border border-slate-600/50">
                  {/* Container Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Container className="w-5 h-5 text-blue-400 flex-shrink-0" />
                      <span className="text-slate-200 font-medium text-sm">
                        {t('shipment.container')} #{index + 1}
                      </span>
                    </div>
                    {canEdit && (
                      <Button
                        onClick={(e) => {
                          e.stopPropagation() // Prevent card click
                          router.push(`/driver/container-data?shipmentId=${shipment.id}&containerId=${containerInfo.id}&shipmentNumber=${shipment.shipment_number}&returnUrl=${encodeURIComponent('/driver/dashboard')}`)
                        }}
                        size="sm"
                        variant="outline"
                        className="bg-blue-600/20 border-blue-500/30 text-blue-300 hover:bg-blue-600/30 hover:text-blue-200 px-3 py-1.5 h-8 text-xs font-medium"
                      >
                        {t('shipment.edit')}
                      </Button>
                    )}
                  </div>

                  {/* Container Details Grid */}
                  <div className="space-y-2">
                    {/* Container Number */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 flex-1 min-w-0">
                        <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0"></div>
                        <div className="flex-1 min-w-0">
                          <div className="text-xs text-slate-400 uppercase tracking-wide mb-0.5">
                            {t('shipment.containerNumber')}
                          </div>
                          <div className="text-slate-200 font-mono text-sm truncate">
                            {containerInfo.containerNumber || 'Not assigned'}
                          </div>
                        </div>
                      </div>
                      {containerInfo.containerNumberConfirmed && (
                        <Badge variant="secondary" className="bg-green-600/80 text-green-100 text-xs px-2 py-0.5 ml-2">
                          ✓ {t('common.confirmed')}
                        </Badge>
                      )}
                    </div>

                    {/* Seal Number */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 flex-1 min-w-0">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                        <div className="flex-1 min-w-0">
                          <div className="text-xs text-slate-400 uppercase tracking-wide mb-0.5">
                            {t('shipment.sealNumber')}
                          </div>
                          <div className="text-slate-200 font-mono text-sm truncate">
                            {containerInfo.sealNumber || 'Not assigned'}
                          </div>
                        </div>
                      </div>
                      {containerInfo.sealNumberConfirmed && (
                        <Badge variant="secondary" className="bg-green-600/80 text-green-100 text-xs px-2 py-0.5 ml-2">
                          ✓ {t('common.confirmed')}
                        </Badge>
                      )}
                    </div>

                    {/* Container Type & Size */}
                    {(containerInfo.type || containerInfo.size) && (
                      <div className="flex items-center space-x-4 pt-1 border-t border-slate-600/30">
                        {containerInfo.type && (
                          <div className="flex items-center space-x-1">
                            <span className="text-xs text-slate-400">{t('shipment.type')}:</span>
                            <span className="text-slate-300 text-xs">{containerInfo.type}</span>
                          </div>
                        )}
                        {containerInfo.size && (
                          <div className="flex items-center space-x-1">
                            <span className="text-xs text-slate-400">{t('shipment.size')}:</span>
                            <span className="text-slate-300 text-xs">{containerInfo.size}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        )}

        <Separator className="bg-slate-700 mb-4" />

        {/* Route Information */}
        <div className="space-y-3">
          <div className="flex items-start space-x-2">
            <MapPin className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
            <div className="flex-1 min-w-0">
              <div className="text-xs text-slate-400 uppercase tracking-wide">
                {t('shipment.pickupContainer')}
              </div>
              <div className="text-sm text-slate-300 mt-0.5">
                {assignment.pickup_container_location || 
                 shipment.origin_port?.name || 
                 t('shipment.locationTBA')}
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <MapPin className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
            <div className="flex-1 min-w-0">
              <div className="text-xs text-slate-400 uppercase tracking-wide">
                {t('shipment.pickupProduct')}
              </div>
              <div className="text-sm text-slate-300 mt-0.5">
                {assignment.pickup_product_location || 
                 shipment.factory.name || 
                 t('shipment.locationTBA')}
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <MapPin className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
            <div className="flex-1 min-w-0">
              <div className="text-xs text-slate-400 uppercase tracking-wide">
                {t('shipment.delivery')}
              </div>
              <div className="text-sm text-slate-300 mt-0.5">
                {assignment.delivery_location || 
                 shipment.destination_port?.name || 
                 t('shipment.locationTBA')}
              </div>
            </div>
          </div>
        </div>

        <Separator className="bg-slate-700 my-4" />

        {/* Dates and Distance */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center space-x-1 mb-1">
              <Calendar className="w-3 h-3 text-slate-400" />
              <span className="text-xs text-slate-400 uppercase tracking-wide">
                {t('shipment.etd')}
              </span>
            </div>
            <div className="text-sm text-slate-300">
              <div>{formatDate(shipment.etd_date)}</div>
              {shipment.etd_date && (
                <div className="text-xs text-slate-400">
                  {formatTime(shipment.etd_date)}
                </div>
              )}
            </div>
          </div>

          <div>
            <div className="flex items-center space-x-1 mb-1">
              <Calendar className="w-3 h-3 text-slate-400" />
              <span className="text-xs text-slate-400 uppercase tracking-wide">
                {t('shipment.eta')}
              </span>
            </div>
            <div className="text-sm text-slate-300">
              <div>{formatDate(shipment.eta_date)}</div>
              {shipment.eta_date && (
                <div className="text-xs text-slate-400">
                  {formatTime(shipment.eta_date)}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Status Timestamps */}
        {(() => {
          const statusTimestamps = []
          
          // Define status order and their labels
          const statusToCheck = [
            { status: 'empty_container_picked', labelKey: 'shipment.emptyContainerPickedTime' },
            { status: 'arrived_at_factory', labelKey: 'shipment.arrivedAtFactoryTime' },
            { status: 'loading_started', labelKey: 'shipment.loadingStartedTime' },
            { status: 'departed_factory', labelKey: 'shipment.departedFactoryTime' },
            { status: 'container_returned', labelKey: 'shipment.containerReturnedTime' }
          ]

          // Check current status position in workflow
          const currentStatusIndex = statusToCheck.findIndex(s => s.status === shipment.status)
          
          // Show timestamps for all completed statuses
          for (let i = 0; i <= currentStatusIndex && i < statusToCheck.length; i++) {
            const statusInfo = statusToCheck[i]
            const timestamp = getStatusTimestamp(statusInfo.status)
            
            if (timestamp) {
              const formattedDateTime = formatStatusDateTime(timestamp)
              if (formattedDateTime) {
                statusTimestamps.push(
                  <div key={statusInfo.status} className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="flex items-center space-x-1 mb-1">
                        <Clock className="w-3 h-3 text-orange-400" />
                        <span className="text-xs text-slate-400 uppercase tracking-wide">
                          {t(statusInfo.labelKey)}
                        </span>
                      </div>
                      <div className="text-sm text-slate-300">
                        <div>{formattedDateTime.date}</div>
                        <div className="text-xs text-slate-400">
                          {formattedDateTime.time}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              }
            }
          }

          return statusTimestamps.length > 0 ? (
            <>
              <Separator className="bg-slate-700 my-4" />
              <div className="space-y-3">
                {statusTimestamps}
              </div>
            </>
          ) : null
        })()}

        {/* Assignment Info */}
        {(assignment.assignment_date || assignment.estimated_distance) && (
          <>
            <Separator className="bg-slate-700 my-4" />
            <div className="grid grid-cols-2 gap-4">
              {assignment.assignment_date && (
                <div>
                  <div className="flex items-center space-x-1 mb-1">
                    <Truck className="w-3 h-3 text-slate-400" />
                    <span className="text-xs text-slate-400 uppercase tracking-wide">
                      {t('shipment.assigned')}
                    </span>
                  </div>
                  <div className="text-sm text-slate-300">
                    {formatDate(assignment.assignment_date)}
                  </div>
                </div>
              )}
              
              {assignment.estimated_distance && (
                <div>
                  <div className="flex items-center space-x-1 mb-1">
                    <MapPin className="w-3 h-3 text-slate-400" />
                    <span className="text-xs text-slate-400 uppercase tracking-wide">
                      {t('shipment.distance')}
                    </span>
                  </div>
                  <div className="text-sm text-slate-300">
                    {assignment.estimated_distance} {t('shipment.km')}
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {/* Action Footer */}
      <div className="bg-slate-700/50 px-4 py-3 border-t border-slate-700">
        <div className="space-y-3">
          {/* Container Data Entry Button - Hidden for now, drivers cannot add containers 
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Container className="w-4 h-4 text-blue-400" />
              <span className="text-sm text-slate-300">
                {!shipment.containers || shipment.containers.length === 0 
                  ? 'No Containers' 
                  : `${shipment.containers.length} Container${shipment.containers.length > 1 ? 's' : ''}`
                }
              </span>
            </div>
            <Button
              onClick={handleContainerDataEntry}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 h-8 text-xs"
            >
              <Plus className="w-3 h-3 mr-1" />
              Add Container
            </Button>
          </div>
          
          <Separator className="bg-slate-600" />
          */}
          
          {/* Status Update */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-slate-300 font-medium">
              {t('shipment.tapToUpdate')}
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-orange-400 font-semibold">
                {t('shipment.updateStatus')}
              </span>
              <span className="text-orange-400 text-lg">→</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}