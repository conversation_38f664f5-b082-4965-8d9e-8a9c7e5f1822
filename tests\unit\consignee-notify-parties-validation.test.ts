import { describe, expect, it } from 'vitest'
import {
  consigneeNotifyPartyBaseSchema,
  consigneeNotifyPartyFormSchema,
  notificationPreferencesSchema,
  relationshipIntegritySchema,
  setDefaultNotifyPartySchema,
} from '@/lib/validations/consignee-notify-parties'

describe('Consignee-Notify Party Validation', () => {
  describe('Notification Preferences Schema', () => {
    it('should validate valid notification preferences', () => {
      const validPreferences = {
        email: true,
        sms: false,
        line: true,
        wechat: false,
      }

      const result = notificationPreferencesSchema.safeParse(validPreferences)
      expect(result.success).toBe(true)
    })

    it('should allow all channels disabled (validation is at form level)', () => {
      const allDisabledPreferences = {
        email: false,
        sms: false,
        line: false,
        wechat: false,
      }

      const result = notificationPreferencesSchema.safeParse(allDisabledPreferences)
      expect(result.success).toBe(true)
    })

    it('should accept partial preferences with at least one enabled', () => {
      const partialPreferences = {
        email: true,
      }

      const result = notificationPreferencesSchema.safeParse(partialPreferences)
      expect(result.success).toBe(true)
    })

    it('should reject invalid preference values', () => {
      const invalidPreferences = {
        email: 'yes', // Should be boolean
        sms: false,
        line: true,
        wechat: false,
      }

      const result = notificationPreferencesSchema.safeParse(invalidPreferences)
      expect(result.success).toBe(false)
    })

    it('should accept all channels enabled', () => {
      const allEnabled = {
        email: true,
        sms: true,
        line: true,
        wechat: true,
      }

      const result = notificationPreferencesSchema.safeParse(allEnabled)
      expect(result.success).toBe(true)
    })
  })

  describe('Consignee-Notify Party Schema', () => {
    it('should validate a valid consignee-notify party relationship', () => {
      const validRelationship = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        is_default: false,
        is_active: true,
        notification_preferences: {
          email: true,
          sms: false,
          line: true,
          wechat: false,
        },
        priority_order: 1,
        special_instructions: 'Handle with care',
        notes: 'Primary notify party for urgent shipments',
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(validRelationship)
      if (!result.success) {
        console.log('Validation errors:', result.error.issues)
      }
      expect(result.success).toBe(true)
    })

    it('should reject relationship with invalid consignee_id', () => {
      const invalidRelationship = {
        consignee_id: 'invalid-uuid',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(invalidRelationship)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid consignee ID')
      }
    })

    it('should reject relationship with invalid notify_party_id', () => {
      const invalidRelationship = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: 'invalid-uuid',
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(invalidRelationship)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid notify party ID')
      }
    })

    it('should reject relationship with invalid priority order', () => {
      const invalidRelationship = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        priority_order: 0, // Should be at least 1
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(invalidRelationship)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Priority order must be at least 1')
      }
    })

    it('should reject relationship with priority order too high', () => {
      const invalidRelationship = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        priority_order: 1000, // Should be max 999
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(invalidRelationship)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Priority order cannot exceed 999')
      }
    })

    it('should allow optional fields to be null or undefined', () => {
      const minimalRelationship = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(minimalRelationship)
      expect(result.success).toBe(true)
    })

    it('should validate with default values applied', () => {
      const relationshipWithDefaults = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        notification_preferences: {
          sms: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(relationshipWithDefaults)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.is_default).toBe(false)
        expect(result.data.is_active).toBe(true)
        expect(result.data.priority_order).toBe(1)
      }
    })
  })

  describe('Form Schema', () => {
    it('should validate consignee-notify party form data', () => {
      const formData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        is_default: false,
        is_active: true,
        notification_preferences: {
          email: true,
          sms: false,
          line: true,
          wechat: false,
        },
        priority_order: 2,
        special_instructions: 'Contact by email first, then SMS if urgent',
        notes: 'Secondary contact for backup notifications',
      }

      const result = consigneeNotifyPartyFormSchema.safeParse(formData)
      expect(result.success).toBe(true)
    })

    it('should require consignee_id in form', () => {
      const formData = {
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyFormSchema.safeParse(formData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid input')
      }
    })

    it('should require notify_party_id in form', () => {
      const formData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyFormSchema.safeParse(formData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('Invalid input')
      }
    })

    it('should validate special instructions length', () => {
      const longInstructions = 'A'.repeat(2001) // Exceed 2000 character limit

      const formData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        special_instructions: longInstructions,
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyFormSchema.safeParse(formData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('2000 characters')
      }
    })

    it('should validate notes length', () => {
      const longNotes = 'N'.repeat(1001) // Exceed 1000 character limit

      const formData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        notes: longNotes,
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyFormSchema.safeParse(formData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('1000 characters')
      }
    })

    it('should enforce at least one notification channel in form schema', () => {
      const formData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        notification_preferences: {
          email: false,
          sms: false,
          line: false,
          wechat: false,
        },
      }

      const result = consigneeNotifyPartyFormSchema.safeParse(formData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('At least one notification channel')
      }
    })
  })

  describe('Relationship Integrity Schema', () => {
    it('should validate unique relationship constraint', () => {
      const uniqueData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        existing_relationships: [],
      }

      const result = relationshipIntegritySchema.safeParse(uniqueData)
      expect(result.success).toBe(true)
    })

    it('should detect duplicate relationships', () => {
      const duplicateData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        existing_relationships: [
          {
            id: '550e8400-e29b-41d4-a716-446655440002',
            consignee_id: '550e8400-e29b-41d4-a716-************',
            notify_party_id: '550e8400-e29b-41d4-a716-************',
          }
        ],
      }

      const result = relationshipIntegritySchema.safeParse(duplicateData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toContain('already exists')
      }
    })
  })

  describe('Set Default Notify Party Schema', () => {
    it('should validate default designation data', () => {
      const defaultData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        relationship_id: '550e8400-e29b-41d4-a716-446655440002',
      }

      const result = setDefaultNotifyPartySchema.safeParse(defaultData)
      expect(result.success).toBe(true)
    })

    it('should require consignee_id for default validation', () => {
      const invalidData = {
        relationship_id: '550e8400-e29b-41d4-a716-446655440002',
      }

      const result = setDefaultNotifyPartySchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should require relationship_id', () => {
      const invalidData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
      }

      const result = setDefaultNotifyPartySchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })
  })

  describe('Edge Cases and Complex Scenarios', () => {
    it('should validate all notification channels enabled', () => {
      const allChannelsEnabled = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        notification_preferences: {
          email: true,
          sms: true,
          line: true,
          wechat: true,
        },
        priority_order: 1,
        is_default: true,
        is_active: true,
        special_instructions: 'Use all channels for maximum reach',
        notes: 'Critical shipment contact - use all available methods',
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(allChannelsEnabled)
      expect(result.success).toBe(true)
    })

    it('should handle same consignee_id and notify_party_id (self-reference)', () => {
      const selfReference = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************', // Same as consignee
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(selfReference)
      // This should be allowed by schema but might be business rule violation
      expect(result.success).toBe(true)
    })

    it('should validate maximum priority order', () => {
      const maxPriority = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        priority_order: 999, // Maximum allowed
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(maxPriority)
      expect(result.success).toBe(true)
    })

    it('should validate minimum priority order', () => {
      const minPriority = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        priority_order: 1, // Minimum allowed
        notification_preferences: {
          email: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(minPriority)
      expect(result.success).toBe(true)
    })

    it('should handle empty special instructions and notes', () => {
      const emptyFields = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        special_instructions: '',
        notes: '',
        notification_preferences: {
          line: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(emptyFields)
      expect(result.success).toBe(true)
    })

    it('should validate maximum length special instructions', () => {
      const maxSpecialInstructions = 'S'.repeat(1000) // Maximum allowed length

      const maxLengthData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        special_instructions: maxSpecialInstructions,
        notification_preferences: {
          wechat: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(maxLengthData)
      expect(result.success).toBe(true)
    })

    it('should validate maximum length notes', () => {
      const maxNotes = 'N'.repeat(2000) // Maximum allowed length

      const maxLengthData = {
        consignee_id: '550e8400-e29b-41d4-a716-************',
        notify_party_id: '550e8400-e29b-41d4-a716-************',
        notes: maxNotes,
        notification_preferences: {
          email: true,
          sms: true,
        },
      }

      const result = consigneeNotifyPartyBaseSchema.safeParse(maxLengthData)
      expect(result.success).toBe(true)
    })
  })
})