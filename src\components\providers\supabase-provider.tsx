'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuthStore } from '@/stores/auth-store'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { UserProfile } from '@/lib/supabase/auth'

const SupabaseContext = createContext<SupabaseClient | undefined>(undefined)

export function useSupabase() {
  const context = useContext(SupabaseContext)
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider')
  }
  return context
}

interface SupabaseProviderProps {
  children: React.ReactNode
}

export function SupabaseProvider({ children }: SupabaseProviderProps) {
  const [supabase] = useState(() => createClient())
  const { setAuth, setLoading, setError, clearAuth } = useAuthStore()
  const [isInitializing, setIsInitializing] = useState(true)
  const [lastAuthEvent, setLastAuthEvent] = useState<string | null>(null)

  // Load profile using API route to bypass RLS circular dependency
  async function loadProfile(
    userId: string,
    accessToken: string,
    retryCount = 0
  ): Promise<UserProfile | null> {
    try {
      const response = await fetch('/api/profile', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        cache: 'no-cache'
      })

      if (!response.ok) {
        // Log the specific error for debugging
        const errorText = await response.text()
        console.error(`Profile API error ${response.status}:`, errorText)
        
        // If it's a 401 and we haven't retried, try once more with fresh session
        if (response.status === 401 && retryCount === 0) {
          console.log('SupabaseProvider: Retrying profile load with fresh session')
          const { data: { session } } = await supabase.auth.getSession()
          if (session?.access_token && session.access_token !== accessToken) {
            return loadProfile(userId, session.access_token, retryCount + 1)
          }
        }
        
        throw new Error(`Profile API error: ${response.status} - ${errorText}`)
      }

      const result = await response.json()
      return result.profile
    } catch (err) {
      console.error('Error loading profile via API:', err)
      // Don't throw error on profile loading failure - just return null
      // This prevents authentication loops when profile API fails
      return null
    }
  }

  useEffect(() => {
    let mounted = true

    // Get initial session
    async function getInitialSession() {
      try {
        console.log('SupabaseProvider: Getting initial session...')
        setLoading(true)
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession()

        if (error) {
          console.error('SupabaseProvider: Session error:', error)
          throw error
        }

        console.log('SupabaseProvider: Session result:', {
          hasSession: !!session,
          userId: session?.user?.id,
        })

        if (session?.user && mounted) {
          // Load user profile via API to bypass RLS circular dependency
          console.log(
            'SupabaseProvider: Loading profile for user:',
            session.user.id
          )
          const profile = await loadProfile(
            session.user.id,
            session.access_token
          )

          if (profile) {
            console.log('SupabaseProvider: Profile loaded:', {
              role: profile.role,
              email: profile.email,
            })
          } else {
            console.log('SupabaseProvider: Profile not found or error loading')
          }

          console.log('SupabaseProvider: Setting auth state...')
          setAuth(session.user, profile, session)
        } else if (mounted) {
          console.log('SupabaseProvider: No session, clearing auth...')
          clearAuth()
        }
      } catch (err) {
        console.error('SupabaseProvider: Error getting session:', err)
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Authentication error')
        }
      } finally {
        if (mounted) {
          console.log('SupabaseProvider: Finished loading')
          setLoading(false)
          setIsInitializing(false)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes with debouncing
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return

      // Skip initial sign in events during initialization to prevent loops
      if (isInitializing && event === 'INITIAL_SESSION') {
        console.log('SupabaseProvider: Skipping initial session event during initialization')
        return
      }

      // Debounce rapid auth state changes
      const eventKey = `${event}-${session?.user?.id || 'null'}`
      if (lastAuthEvent === eventKey) {
        console.log('SupabaseProvider: Skipping duplicate auth event:', event)
        return
      }
      setLastAuthEvent(eventKey)

      console.log('SupabaseProvider: Auth state changed:', event, {
        hasSession: !!session,
      })

      try {
        if (session?.user) {
          // Load user profile via API to bypass RLS circular dependency
          console.log(
            'SupabaseProvider: Loading profile for user on auth change:',
            session.user.id
          )
          const profile = await loadProfile(
            session.user.id,
            session.access_token
          )

          if (profile) {
            console.log('SupabaseProvider: Profile loaded on auth change:', {
              role: profile.role,
              email: profile.email,
            })
          } else {
            console.log(
              'SupabaseProvider: Profile not found or error loading on auth change'
            )
          }

          console.log('SupabaseProvider: Setting auth state on change...')
          setAuth(session.user, profile, session)
        } else {
          console.log('SupabaseProvider: Clearing auth on change...')
          clearAuth()
        }
      } catch (err) {
        console.error(
          'SupabaseProvider: Error in auth state change handler:',
          err
        )
        // Still set auth state even if profile loading fails
        if (session?.user) {
          console.log(
            'SupabaseProvider: Setting auth state without profile due to error...'
          )
          setAuth(session.user, null, session)
        } else {
          clearAuth()
        }
      }
    })

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [supabase, setAuth, setLoading, setError, clearAuth])

  return (
    <SupabaseContext.Provider value={supabase}>
      {children}
    </SupabaseContext.Provider>
  )
}
