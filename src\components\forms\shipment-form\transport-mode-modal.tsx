'use client'

import { useState, useCallback } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Ship,
  Truck,
  Train,
  Clock,
  MapPin,
  FileText,
  CheckCircle,
} from 'lucide-react'

export type TransportMode = 'sea' | 'land' | 'rail'

export interface TransportModeConfig {
  mode: TransportMode
  name: string
  icon: typeof Ship
  description: string
  features: string[]
  requirements: string[]
  estimatedTransitTime: string
  documentationRequired: string[]
}

const TRANSPORT_MODES: Record<TransportMode, TransportModeConfig> = {
  sea: {
    mode: 'sea',
    name: 'Sea Freight',
    icon: Ship,
    description:
      'Ocean container shipping for large cargo volumes with cost efficiency',
    features: [
      'High capacity containers',
      'Cost-effective for bulk cargo',
      'Global port network',
      'Container tracking',
    ],
    requirements: [
      'Booking confirmation mandatory',
      'Port clearance required',
      'Container seal verification',
      'Marine insurance recommended',
    ],
    estimatedTransitTime: '7-45 days depending on route',
    documentationRequired: [
      'Bill of Lading',
      'Commercial Invoice',
      'Packing List',
      'Certificate of Origin',
      'Insurance Certificate',
    ],
  },
  land: {
    mode: 'land',
    name: 'Land Transport',
    icon: Truck,
    description: 'Road and cross-border trucking for regional deliveries',
    features: [
      'Door-to-door delivery',
      'Flexible scheduling',
      'Regional coverage',
      'Real-time tracking',
    ],
    requirements: [
      'Transport permit required',
      'Route optimization needed',
      'Driver documentation',
      'Cross-border clearance',
    ],
    estimatedTransitTime: '1-7 days for regional routes',
    documentationRequired: [
      'CMR Waybill',
      'Commercial Invoice',
      'Transport Permit',
      'Driver License',
      'Vehicle Registration',
    ],
  },
  rail: {
    mode: 'rail',
    name: 'Rail Freight',
    icon: Train,
    description: 'Railway transport for efficient long-distance cargo movement',
    features: [
      'Environmental efficiency',
      'Large cargo capacity',
      'Scheduled services',
      'Weather independent',
    ],
    requirements: [
      'Railway booking required',
      'Station-to-station delivery',
      'Cargo loading assistance',
      'Rail network connectivity',
    ],
    estimatedTransitTime: '3-21 days for continental routes',
    documentationRequired: [
      'Railway Bill',
      'Commercial Invoice',
      'Loading Certificate',
      'Cargo Manifest',
      'Insurance Document',
    ],
  },
}

interface TransportModeModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onModeSelect: (mode: TransportMode, config: TransportModeConfig) => void
  selectedMode?: TransportMode
  disabled?: boolean
}

export function TransportModeModal({
  open,
  onOpenChange,
  onModeSelect,
  selectedMode,
  disabled = false,
}: TransportModeModalProps) {
  const [hoveredMode, setHoveredMode] = useState<TransportMode | null>(null)

  const handleModeSelect = useCallback(
    (mode: TransportMode) => {
      if (disabled) return

      const config = TRANSPORT_MODES[mode]
      onModeSelect(mode, config)
      onOpenChange(false)
    },
    [disabled, onModeSelect, onOpenChange]
  )

  const getModeCardClassName = (mode: TransportMode) => {
    const baseClasses =
      'cursor-pointer transition-all duration-200 hover:scale-102'
    const selectedClasses =
      selectedMode === mode
        ? 'bg-orange-600 border-orange-400 shadow-orange-500/20 shadow-lg'
        : 'bg-slate-700 border-slate-600 hover:bg-slate-600 hover:border-slate-500'
    const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : ''

    return `${baseClasses} ${selectedClasses} ${disabledClasses}`
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl bg-slate-800 border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-white flex items-center gap-2">
            <MapPin className="h-6 w-6 text-blue-500" />
            Select Transportation Mode
          </DialogTitle>
          <DialogDescription className="text-slate-400">
            Choose the transportation method for your shipment. This will
            configure the appropriate workflow fields and documentation
            requirements.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 py-4">
          {Object.values(TRANSPORT_MODES).map(config => {
            const IconComponent = config.icon
            const isSelected = selectedMode === config.mode
            const isHovered = hoveredMode === config.mode

            return (
              <Card
                key={config.mode}
                className={getModeCardClassName(config.mode)}
                onClick={() => handleModeSelect(config.mode)}
                onMouseEnter={() => setHoveredMode(config.mode)}
                onMouseLeave={() => setHoveredMode(null)}
              >
                <CardContent className="p-6 space-y-4">
                  {/* Header with Icon and Title */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`p-2 rounded-lg ${isSelected ? 'bg-orange-700' : 'bg-blue-600'}`}
                      >
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-white">
                          {config.name}
                        </h3>
                        <Badge
                          variant="outline"
                          className={`text-xs mt-1 ${isSelected ? 'border-orange-300 text-orange-200' : 'border-slate-400 text-slate-300'}`}
                        >
                          {config.mode.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                    {isSelected && (
                      <CheckCircle className="h-5 w-5 text-green-400" />
                    )}
                  </div>

                  {/* Description */}
                  <p className="text-sm text-slate-300">{config.description}</p>

                  {/* Transit Time */}
                  <div className="flex items-center space-x-2 text-sm">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span className="text-slate-300">
                      {config.estimatedTransitTime}
                    </span>
                  </div>

                  {/* Key Features */}
                  {(isHovered || isSelected) && (
                    <div className="space-y-3 animate-in slide-in-from-top-2 duration-200">
                      <div>
                        <h4 className="text-sm font-medium text-green-300 mb-2">
                          Key Features
                        </h4>
                        <ul className="space-y-1">
                          {config.features.slice(0, 3).map((feature, index) => (
                            <li
                              key={index}
                              className="text-xs text-slate-400 flex items-center"
                            >
                              <div className="w-1 h-1 bg-green-400 rounded-full mr-2" />
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-amber-300 mb-2">
                          Requirements
                        </h4>
                        <ul className="space-y-1">
                          {config.requirements
                            .slice(0, 2)
                            .map((requirement, index) => (
                              <li
                                key={index}
                                className="text-xs text-slate-400 flex items-center"
                              >
                                <div className="w-1 h-1 bg-amber-400 rounded-full mr-2" />
                                {requirement}
                              </li>
                            ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-blue-300 mb-2 flex items-center">
                          <FileText className="h-3 w-3 mr-1" />
                          Documentation
                        </h4>
                        <div className="text-xs text-slate-400">
                          {config.documentationRequired.length} documents
                          required
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Action Button */}
                  <Button
                    variant={isSelected ? 'secondary' : 'outline'}
                    size="sm"
                    className={`w-full mt-4 ${
                      isSelected
                        ? 'bg-orange-500 hover:bg-orange-600 text-white border-orange-400'
                        : 'bg-slate-800 hover:bg-slate-700 text-slate-300 border-slate-600'
                    }`}
                    disabled={disabled}
                  >
                    {isSelected ? 'Selected' : 'Select Mode'}
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Selected Mode Details */}
        {selectedMode && (
          <div className="border-t border-slate-600 pt-4">
            <div className="bg-slate-700 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">
                Selected: {TRANSPORT_MODES[selectedMode].name}
              </h4>
              <p className="text-sm text-slate-400 mb-3">
                The shipment form will be configured for {selectedMode}{' '}
                transport with the following requirements:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-green-300 font-medium">
                    Required Documents:
                  </span>
                  <ul className="mt-1 space-y-1">
                    {TRANSPORT_MODES[selectedMode].documentationRequired.map(
                      (doc, index) => (
                        <li key={index} className="text-slate-400 pl-2">
                          • {doc}
                        </li>
                      )
                    )}
                  </ul>
                </div>
                <div>
                  <span className="text-amber-300 font-medium">
                    Key Requirements:
                  </span>
                  <ul className="mt-1 space-y-1">
                    {TRANSPORT_MODES[selectedMode].requirements.map(
                      (req, index) => (
                        <li key={index} className="text-slate-400 pl-2">
                          • {req}
                        </li>
                      )
                    )}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-slate-600">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={disabled}
            className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
          >
            Cancel
          </Button>
          {selectedMode && (
            <Button
              onClick={() => handleModeSelect(selectedMode)}
              disabled={disabled}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              Continue with {TRANSPORT_MODES[selectedMode].name}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export { TRANSPORT_MODES }
