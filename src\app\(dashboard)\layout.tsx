'use client'

import { useAuth } from '@/hooks/use-auth'
import { Sidebar } from '@/components/layout/navigation/sidebar'
import { UserHeader } from '@/components/layout/navigation/user-header'
import { MobileNav } from '@/components/layout/navigation/mobile-nav'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect } from 'react'

function DashboardContent({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  // Handle authentication redirect
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      console.log(
        'DashboardLayout: User not authenticated, redirecting to login...'
      )
      const redirectUrl = `/login?redirectTo=${encodeURIComponent(pathname)}`
      router.replace(redirectUrl)
    }
  }, [loading, isAuthenticated, router, pathname])

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
          <p className="text-slate-300 mt-4">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-slate-300">Redirecting to login...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <div className="flex h-screen">
        {/* Desktop Sidebar */}
        <Sidebar className="hidden lg:flex" />

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          {/* Header */}
          <header className="bg-slate-800 border-b border-slate-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Mobile Navigation */}
                <MobileNav />
                <h1 className="text-xl font-semibold text-white">Dashboard</h1>
              </div>
              <UserHeader />
            </div>
          </header>

          {/* Page Content */}
          <div className="p-6">{children}</div>
        </main>
      </div>
    </div>
  )
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <DashboardContent>{children}</DashboardContent>
}
