-- Create a view that returns ports with coordinates as text
-- This solves the binary coordinate display issue

CREATE OR REPLACE VIEW ports_with_text_coordinates AS
SELECT 
    id,
    code,
    name,
    city,
    country,
    port_type,
    CASE 
        WHEN gps_coordinates IS NOT NULL THEN ST_AsText(gps_coordinates)
        ELSE NULL 
    END as gps_coordinates_text,
    gps_coordinates as gps_coordinates_binary,
    timezone,
    is_active,
    created_at,
    updated_at
FROM ports;

-- Grant access to the view
GRANT SELECT ON ports_with_text_coordinates TO authenticated;
GRANT SELECT ON ports_with_text_coordinates TO anon;

-- Add comment for documentation
COMMENT ON VIEW ports_with_text_coordinates IS 'View that returns ports with GPS coordinates converted to readable text format using ST_AsText()';