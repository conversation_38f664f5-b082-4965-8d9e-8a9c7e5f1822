create table public.companies (
  id uuid not null default gen_random_uuid (),
  name text not null,
  company_type public.company_type_enum not null,
  tax_id text null,
  contact_email text null,
  contact_phone text null,
  contact_fax text null,
  contact_person_first_name text null,
  contact_person_last_name text null,
  address jsonb null,
  gps_coordinates point null,
  metadata jsonb null,
  notes text null,
  is_active boolean null default true,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint companies_pkey primary key (id),
  constraint valid_metadata_structure check (
    validate_company_metadata (company_type, metadata)
  )
) TABLESPACE pg_default;

create index IF not exists idx_companies_name on public.companies using btree (name) TABLESPACE pg_default;

create index IF not exists idx_companies_active on public.companies using btree (is_active) TABLESPACE pg_default;

create index IF not exists idx_companies_type on public.companies using btree (company_type) TABLESPACE pg_default;

create index IF not exists idx_companies_gps on public.companies using gist (gps_coordinates) TABLESPACE pg_default;

create index IF not exists idx_companies_active_by_type on public.companies using btree (company_type, name) TABLESPACE pg_default
where
  (is_active = true);

create index IF not exists idx_companies_name_fts on public.companies using gin (to_tsvector('english'::regconfig, name)) TABLESPACE pg_default;

create trigger sync_companies_gps_coordinates BEFORE INSERT
or
update on companies for EACH row
execute FUNCTION sync_gps_coordinates ();

create trigger update_companies_updated_at BEFORE
update on companies for EACH row
execute FUNCTION update_updated_at_column ();


create table public.drivers (
  id uuid not null default gen_random_uuid (),
  carrier_id uuid not null,
  driver_first_name text not null,
  driver_last_name text not null,
  driver_code text null,
  phone text null,
  line_id text null,
  driver_picture_path text null,
  driver_picture_mime_type text null,
  notes text null,
  is_active boolean not null default true,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  user_id uuid null,
  constraint drivers_pkey primary key (id),
  constraint drivers_carrier_id_fkey foreign KEY (carrier_id) references companies (id) on delete CASCADE,
  constraint drivers_user_id_fkey foreign KEY (user_id) references profiles (user_id)
) TABLESPACE pg_default;

create index IF not exists idx_drivers_carrier on public.drivers using btree (carrier_id) TABLESPACE pg_default;

create index IF not exists idx_drivers_code on public.drivers using btree (driver_code) TABLESPACE pg_default;

create index IF not exists idx_drivers_phone on public.drivers using btree (phone) TABLESPACE pg_default;

create index IF not exists idx_drivers_active on public.drivers using btree (is_active) TABLESPACE pg_default;

create index IF not exists idx_drivers_carrier_active on public.drivers using btree (carrier_id, is_active) TABLESPACE pg_default
where
  (is_active = true);

create index IF not exists idx_drivers_phone_lookup on public.drivers using btree (phone) TABLESPACE pg_default;

create index IF not exists idx_drivers_line_id on public.drivers using btree (line_id) TABLESPACE pg_default
where
  (line_id is not null);

create trigger update_drivers_updated_at BEFORE
update on drivers for EACH row
execute FUNCTION update_updated_at_column ();


create table public.transportation (
  id uuid not null default gen_random_uuid (),
  shipment_id uuid null,
  carrier_id uuid null,
  driver_id uuid null,
  vehicle_head_number text null,
  vehicle_tail_number text null,
  driver_phone text null,
  assignment_date timestamp with time zone null,
  pickup_container_location text null,
  pickup_container_gps_coordinates point null,
  pickup_product_location text null,
  pickup_product_gps_coordinates point null,
  delivery_location text null,
  delivery_gps_coordinates point null,
  notes text null,
  estimated_distance numeric null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint transportation_pkey primary key (id),
  constraint transportation_carrier_id_fkey foreign KEY (carrier_id) references companies (id),
  constraint transportation_driver_id_fkey foreign KEY (driver_id) references drivers (id),
  constraint transportation_shipment_id_fkey foreign KEY (shipment_id) references shipments (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_transportation_carrier_driver on public.transportation using btree (carrier_id, driver_id) TABLESPACE pg_default;

create index IF not exists idx_transportation_shipment on public.transportation using btree (shipment_id) TABLESPACE pg_default;

create index IF not exists idx_transportation_pickup_gps on public.transportation using gist (pickup_product_gps_coordinates) TABLESPACE pg_default
where
  (pickup_product_gps_coordinates is not null);

create index IF not exists idx_transportation_delivery_gps on public.transportation using gist (delivery_gps_coordinates) TABLESPACE pg_default
where
  (delivery_gps_coordinates is not null);

create index IF not exists idx_transportation_vehicle_head on public.transportation using btree (vehicle_head_number) TABLESPACE pg_default
where
  (vehicle_head_number is not null);

create index IF not exists idx_transportation_vehicle_tail on public.transportation using btree (vehicle_tail_number) TABLESPACE pg_default
where
  (vehicle_tail_number is not null);

create trigger update_transportation_updated_at BEFORE
update on transportation for EACH row
execute FUNCTION update_updated_at_column ();


create table public.status_images (
  id uuid not null default gen_random_uuid (),
  shipment_id uuid null,
  status_history_id uuid null,
  image_url text not null,
  image_path text not null,
  file_size integer null,
  mime_type text null,
  metadata jsonb null,
  uploaded_by uuid null,
  created_at timestamp with time zone null default now(),
  constraint status_images_pkey primary key (id),
  constraint status_images_shipment_id_fkey foreign KEY (shipment_id) references shipments (id) on delete CASCADE,
  constraint status_images_status_history_id_fkey foreign KEY (status_history_id) references status_history (id),
  constraint status_images_uploaded_by_fkey foreign KEY (uploaded_by) references profiles (user_id)
) TABLESPACE pg_default;

create index IF not exists idx_status_images_status_history on public.status_images using btree (status_history_id) TABLESPACE pg_default;

create trigger sync_status_images_coordinates BEFORE INSERT
or
update on status_images for EACH row
execute FUNCTION sync_gps_coordinates ();

create trigger update_status_images_updated_at BEFORE
update on status_images for EACH row
execute FUNCTION update_updated_at_column ();


create table public.profiles (
  user_id uuid not null,
  email text not null,
  first_name text null,
  last_name text null,
  phone_number text null,
  line_id text null,
  wechat_id text null,
  role public.role_type not null,
  company_id uuid null,
  is_active boolean null default true,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  full_name text GENERATED ALWAYS as (((first_name || ' '::text) || last_name)) STORED null,
  constraint profiles_pkey primary key (user_id),
  constraint profiles_email_key unique (email),
  constraint profiles_company_id_fkey foreign KEY (company_id) references companies (id),
  constraint profiles_user_id_fkey foreign KEY (user_id) references auth.users (id),
  constraint valid_role_company_relationship check (
    (
      (
        (
          role = any (
            array[
              'admin'::role_type,
              'cs'::role_type,
              'account'::role_type
            ]
          )
        )
        and (company_id is null)
      )
      or (
        (
          role <> all (
            array[
              'admin'::role_type,
              'cs'::role_type,
              'account'::role_type
            ]
          )
        )
        and (company_id is not null)
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_profiles_role on public.profiles using btree (role) TABLESPACE pg_default;

create index IF not exists idx_profiles_company_id on public.profiles using btree (company_id) TABLESPACE pg_default;

create index IF not exists idx_profiles_email on public.profiles using btree (email) TABLESPACE pg_default;

create index IF not exists idx_profiles_active on public.profiles using btree (is_active) TABLESPACE pg_default;

create index IF not exists idx_profiles_active_by_role on public.profiles using btree (role, company_id) TABLESPACE pg_default
where
  (is_active = true);

create trigger update_profiles_updated_at BEFORE
update on profiles for EACH row
execute FUNCTION update_updated_at_column ();

create trigger validate_profile_role_company_trigger BEFORE INSERT
or
update on profiles for EACH row
execute FUNCTION validate_profile_role_company ();

