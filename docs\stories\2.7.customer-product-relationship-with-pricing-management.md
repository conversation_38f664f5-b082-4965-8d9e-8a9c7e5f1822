# Story 2.7: Customer-Product Relationship with Pricing Management

## Status
Done

## Story
**As a** CS representative,  
**I want** to manage customer-product relationships with detailed pricing and specifications,  
**so that** product information is consistent and automatically populated during shipment creation.

## Acceptance Criteria

**1:** Customer-product interface captures pricing (CIF/FOB per KG), packaging specifications, and weight details.

**2:** Product specifications include packaging type, gross/net weight per package, quality grade, and handling instructions.

**3:** Default product designation per customer supports streamlined shipment creation.

**4:** Currency selection (THB, CNY, USD, EUR) with validation and conversion capabilities.

**5:** Temperature and ventilation requirements are captured for specialized products.

## Tasks / Subtasks

- [ ] Create customer-product relationship management page and navigation integration (AC: 1, 3)
  - [ ] Add product relationships navigation item to master-data section in sidebar
  - [ ] Create `src/app/(dashboard)/master-data/products/page.tsx` with customer-product list view
  - [ ] Implement customer-product data table with filtering and search capabilities
  - [ ] Add filtering by customer company and product categories
  - [ ] Implement search functionality for customer and product names

- [ ] Develop customer-product relationship form component (AC: 1, 2, 3, 4, 5)
  - [ ] Create `src/components/forms/customer-product-form/customer-product-form.tsx` component
  - [ ] Implement customer selection dropdown (customer type only)
  - [ ] Implement product selection dropdown with existing products
  - [ ] Add pricing fields for CIF/FOB unit prices with currency selection (THB, CNY, USD, EUR)
  - [ ] Add packaging specifications: packaging type, gross/net weight per package, quality grade
  - [ ] Add logistics fields: handling instructions, temperature requirements, ventilation requirements
  - [ ] Add default product toggle with automatic previous default reset logic
  - [ ] Apply form validation with Zod schema for required fields and constraints

- [ ] Implement bulk import functionality for customer-product setup (AC: 1, 2, 4, 5)
  - [ ] Create bulk import component with CSV file upload support
  - [ ] Implement CSV validation for customer/product matching and pricing data integrity
  - [ ] Add preview functionality showing import results before commit
  - [ ] Handle error cases for invalid customer/product references and pricing validation
  - [ ] Provide import summary with success/failure counts and error details

- [ ] Create customer-product validation and state management (AC: 1, 2, 3, 4, 5)
  - [ ] Extend `src/lib/validations/` with customer-products.ts validation schema
  - [ ] Create customer-product store in `src/stores/customer-product-store.ts` for state management
  - [ ] Implement `src/hooks/use-customer-products.ts` for CRUD operations
  - [ ] Add automatic default toggle validation to ensure only one default per customer
  - [ ] Implement currency validation and conversion capabilities
  - [ ] Add pricing validation for CIF/FOB unit prices with decimal precision

- [ ] Integrate with shipment creation interfaces for intelligent pre-population (AC: 3)
  - [ ] Update shipment creation form to subscribe to customer-product relationship changes
  - [ ] Implement intelligent pre-population when customer is selected
  - [ ] Add real-time product dropdown updates when relationships change
  - [ ] Ensure default product selection works seamlessly in shipment workflows
  - [ ] Pre-populate pricing, packaging, and specifications data automatically

- [ ] Create comprehensive testing suite (All ACs)
  - [ ] Write unit tests for customer-product form validation and pricing calculations
  - [ ] Test default product toggle functionality with automatic previous default reset
  - [ ] Create integration tests for relationship CRUD operations with company type validation
  - [ ] Test bulk import functionality with CSV parsing and pricing data validation
  - [ ] Test real-time updates in shipment creation interface integration
  - [ ] Validate currency selection, conversion, and pricing precision handling
  - [ ] Test temperature and ventilation requirement validation and storage

## Dev Notes

### Previous Story Insights
From Story 2.6: Customer-Shipper Relationship Management completed with comprehensive relationship intelligence patterns and type-specific validation. The established patterns for company type filtering (customers only), default designation logic (single default per customer), and real-time integration with shipment workflows can be directly applied to customer-product relationships, ensuring consistent validation patterns and seamless shipment creation integration.

### Data Models and Database Schema Context
**Customer-Product Relationship Table Design:**
[Source: User-provided customer_products schema]
Customer-product relationships use the `customer_products` table with the following structure:
- id: UUID primary key with auto-generation (gen_random_uuid())
- customer_id: UUID reference to companies table (company_type must equal 'customer')
- product_id: UUID reference to products table with CASCADE DELETE
- customer_product_code: text - Custom product code for this customer relationship
- is_default: boolean - Default product designation per customer (only one per customer)
- is_active: boolean - Relationship status controlling visibility in workflows (default: true)
- unit_price_cif: numeric(12,4) - Cost, Insurance, Freight price per KG with 4 decimal precision
- unit_price_fob: numeric(12,4) - Free on Board price per KG with 4 decimal precision
- currency_code: currency_enum with default 'USD' (THB, CNY, USD, EUR supported)
- standard_quantity: numeric(10,2) - Standard order quantity in packages
- unit_of_measure_id: UUID reference to units_of_measure table
- gross_weight_per_package: numeric(8,4) - Gross weight per package in KG
- net_weight_per_package: numeric(8,4) - Net weight per package in KG
- quality_grade: text - Product quality classification
- packaging_type: packaging_type_enum (Bag, Plastic Basket, Carton) NOT NULL
- packaging_specifications: jsonb - Additional packaging details and requirements
- handling_instructions: text - Special handling requirements and instructions
- temperature_require: text - Temperature requirements for specialized products
- vent_require: text - Ventilation requirements for specialized products
- shelf_life_days: integer - Product shelf life in days
- notes: text - Additional relationship information and special instructions
- created_at/updated_at: timestamptz audit fields with automatic triggers

**Database Constraints and Validation:**
[Source: User-provided schema]
- Unique constraint on (customer_id, product_id) prevents duplicate relationships
- Foreign key constraints with CASCADE DELETE for data integrity
- Company type validation: `is_company_type(customer_id, 'customer')` ensures only customer companies
- Default boolean logic requires application-level enforcement for single default per customer
- Performance indexes: customer_id, product_id, default status, and active status optimized

**Relationship Intelligence Integration:**
[Source: core-workflows.md#intelligent-shipment-creation]
Customer-product relationships integrate with the Relationship Intelligence Engine for shipment pre-population:
- When customer is selected, query `customer_products` for available and default products
- Default product automatically populates in shipment creation forms with pricing and specifications
- Active relationships control product dropdown options during shipment creation
- Real-time subscriptions ensure immediate updates when relationships change
- Pricing data (CIF/FOB) automatically populates based on customer-product configuration

### API Specifications and Database Access Patterns
[Source: backend-architecture.md#data-access-layer]
**Customer-Product Data Access Pattern:**
```typescript
// Fetch customer-product relationships with complete information
const { data: relationships } = await supabase
  .from('customer_products')
  .select(`
    *,
    customer:companies!customer_id(name, company_type, contact_phone),
    product:products!product_id(name, code, category, unit_of_measure:units_of_measure(name, symbol)),
    unit_of_measure:units_of_measure(name, symbol)
  `)
  .eq('is_active', true)
  .order('customer:companies(name)')

// Create customer-product relationship with pricing validation
const { data: relationship } = await supabase
  .from('customer_products')
  .insert({
    customer_id,
    product_id,
    unit_price_cif,
    unit_price_fob,
    currency_code,
    packaging_type,
    gross_weight_per_package,
    net_weight_per_package,
    is_default,
    is_active: true,
    handling_instructions,
    temperature_require,
    vent_require,
    notes
  })
  .select()
  .single()
```

**Default Product Management Pattern:**
```typescript
// Reset previous default when setting new default
if (is_default) {
  await supabase
    .from('customer_products')
    .update({ is_default: false })
    .eq('customer_id', customer_id)
    .neq('id', relationship_id)
}

// Get default product for customer (shipment creation)
const { data: defaultProduct } = await supabase
  .from('customer_products')
  .select(`
    *,
    product:products!product_id(id, name, code),
    unit_of_measure:units_of_measure(name, symbol)
  `)
  .eq('customer_id', customer_id)
  .eq('is_default', true)
  .eq('is_active', true)
  .single()
```

### Component Architecture and Integration Patterns
[Source: frontend-architecture.md#component-architecture]
**Customer-Product Management Architecture:**
- Product relationships page at `src/app/(dashboard)/master-data/products/page.tsx`
- Customer-product form component at `src/components/forms/customer-product-form/customer-product-form.tsx`
- Bulk import component integrated within products page for initial data setup
- Leverage existing company selection patterns for customer dropdown (customer type only)
- Use existing product selection patterns with comprehensive product information display
- Use existing data table patterns with ShadCN UI components for relationship list

**ShadCN UI Component Usage:**
- Use existing DataTable components for relationship list with pagination, sorting, and filtering
- Implement Select component for customer selection with company type filtering (customer only)
- Implement Select component for product selection with product categories and search
- Use Input components for pricing fields with proper decimal precision and currency formatting
- Use Select components for currency selection (THB, CNY, USD, EUR) and packaging types
- Use Switch components for default product and active status toggles
- Leverage Badge components for relationship status and currency indicators
- Apply existing form validation patterns with react-hook-form and Zod schemas

**Real-time Integration Patterns:**
- Subscribe to customer_products table changes for live updates in relationship list
- Integrate with shipment creation forms for real-time product dropdown updates
- Use Zustand store for optimistic updates and state synchronization
- Implement real-time notifications when default product designations change
- Real-time pricing and specification updates in shipment creation interfaces

### File Locations and Project Structure Alignment
[Source: unified-project-structure.md]
**Customer-Product Relationship File Structure:**
- Main page: `src/app/(dashboard)/master-data/products/page.tsx`
- Product relationship form: `src/components/forms/customer-product-form/customer-product-form.tsx`
- Bulk import: Integrate within products page as bulk-import section
- Validation: `src/lib/validations/customer-products.ts` for relationship schema validation
- State management: `src/stores/customer-product-store.ts` for relationship operations
- Hooks: `src/hooks/use-customer-products.ts` for relationship CRUD operations
- Types: Extend existing database types for customer-product interface definitions

### Technical Constraints and Requirements
[Source: tech-stack.md#technology-stack-table]
- Maintain consistency with Next.js 14.2+ App Router and TypeScript 5.3+
- Follow existing Supabase Client API patterns with auto-generated TypeScript types
- Use PostgreSQL numeric types with proper decimal precision for pricing (12,4 precision)
- Maintain ShadCN UI components with established dark blue theme colors
- Follow existing Zustand 4.5+ state management patterns with real-time subscriptions
- Apply consistent Zod validation patterns for form validation and pricing precision
- Integrate with existing Relationship Intelligence Engine for shipment pre-population
- Support currency_enum validation (THB, CNY, USD, EUR) and formatting

### Company Type Validation and Filtering Requirements
**Customer Selection Filtering:**
- Filter companies table to show only company_type = 'customer' in customer dropdown
- Validate selected customer_id references valid customer company type using existing patterns
- Use existing company management patterns from previous stories for consistency
- Apply company type validation constraint: `is_company_type(customer_id, 'customer')`

**Product Selection and Integration:**
- Use existing products table with complete product information display
- Validate selected product_id references valid active products
- Display product code, name, category, and base unit of measure in selection
- Apply existing product management patterns from Story 2.1 for consistency

**Default Product Logic:**
- Implement application-level logic to ensure only one default product per customer
- When setting new default, automatically reset is_default = false for other relationships
- Validate default product selection in shipment creation workflows
- Handle edge cases where customer has no default product designated

### Pricing and Currency Management Requirements
**Pricing Precision and Validation:**
- Support numeric(12,4) precision for CIF and FOB pricing per KG
- Validate pricing inputs with proper decimal formatting and range validation
- Implement currency-specific formatting and display patterns
- Support currency conversion capabilities for different currency codes

**Currency Support and Validation:**
- Support currency_enum: THB, CNY, USD, EUR with USD as default
- Validate currency code selection and ensure consistency across relationships
- Implement currency-specific formatting for pricing display
- Provide currency conversion capabilities for pricing comparison

**Packaging and Specifications Management:**
- Support packaging_type_enum: Bag, Plastic Basket, Carton as NOT NULL constraint
- Validate gross/net weight per package with numeric(8,4) precision
- Support JSONB packaging_specifications for flexible additional requirements
- Implement quality grade validation and standardized quality classifications

### Bulk Import Functionality Requirements
**CSV Import Processing:**
- Support CSV format with columns: Customer Name, Product Name, CIF Price, FOB Price, Currency, Packaging Type, Gross Weight, Net Weight, Quality Grade, Temperature Requirements, Handling Instructions
- Implement fuzzy matching for customer and product names to handle minor variations
- Validate customer company types and product existence before creating relationships
- Handle pricing validation with proper decimal precision and currency validation
- Provide detailed import summary with success/failure reporting and pricing validation results

**Import Validation and Error Handling:**
- Validate CSV structure and required columns before processing
- Check for duplicate customer-product combinations in import data
- Verify customer and product existence and types with proper constraint validation
- Handle default product conflicts within import data and existing relationships
- Validate pricing data format, precision, and currency consistency
- Provide line-by-line error reporting with clear resolution guidance

### Testing

#### Testing Standards
**Test Location**: `tests/` directory with `unit/`, `integration/`, and `e2e/` subdirectories for customer-product relationship tests
**Testing Framework**: Vitest + Testing Library for unit tests, Playwright for E2E relationship management workflows
**Testing Patterns**: 
- Component testing for customer-product form with pricing validation and currency handling
- Integration testing with local Supabase instance for relationship CRUD operations with pricing data
- Mock data for isolated component tests with realistic customer, product, and pricing information
- E2E testing for complete relationship management workflows including bulk import
- Company type restriction validation testing for customers only
- Pricing precision and currency validation testing

**Specific Testing Requirements for This Story**:
- Test customer-product relationship form with customer type filtering (customer type only)
- Validate relationship creation, update, and deletion operations with proper pricing constraints
- Test default product toggle functionality with automatic previous default reset
- Verify relationship list filtering by customer company and product categories
- Test search functionality for customer and product names in relationship list
- Validate pricing input validation with decimal precision (12,4) and currency formatting
- Test currency selection (THB, CNY, USD, EUR) and validation
- Validate packaging specifications including packaging type, weights, and quality grade
- Test temperature and ventilation requirements capture and validation
- Validate bulk import functionality with CSV parsing and pricing data validation
- Test real-time integration with shipment creation interface and pre-population
- Verify relationship status management and visibility control in shipment workflows
- Test integration with existing company and product management systems
- Validate pricing calculation and display with proper currency formatting

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-23 | 1.0 | Initial story creation with comprehensive architecture context and customer_products schema integration | Scrum Master |

## Dev Agent Record

### Agent Model Used

*This section will be populated by the development agent during implementation*

### Debug Log References

*This section will be populated by the development agent during implementation*

### Completion Notes List

*This section will be populated by the development agent during implementation*

### File List

*This section will be populated by the development agent during implementation*

## QA Results

*This section will be populated by the QA agent during review*