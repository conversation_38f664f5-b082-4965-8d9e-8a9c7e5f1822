'use client'

import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type { Company } from '@/stores/company-store'
import type { CustomerConsigneeFilter } from '@/lib/validations/customer-consignees'

// Customer-consignee relationship types
export interface CustomerConsignee {
  id: string
  customer_id: string
  consignee_id: string
  is_default: boolean | null
  is_active: boolean | null
  notes: string | null
  created_at: string | null
  updated_at: string | null
  // Joined company data
  customer?: Company | null
  consignee?: Company | null
}

export interface CustomerConsigneeInsert {
  customer_id: string
  consignee_id: string
  is_default?: boolean
  is_active?: boolean
  notes?: string | null
}

export interface CustomerConsigneeUpdate {
  id?: string
  customer_id?: string
  consignee_id?: string
  is_default?: boolean
  is_active?: boolean
  notes?: string | null
}

// Bulk import data interface
export interface CustomerConsigneeBulkData {
  customer_name: string
  consignee_name: string
  is_default?: boolean
  notes?: string | null
}

export interface CustomerConsigneeBulkResult {
  success: CustomerConsigneeBulkData[]
  errors: Array<{
    row: number
    data: CustomerConsigneeBulkData
    error: string
  }>
  summary: {
    total: number
    successful: number
    failed: number
  }
}

interface CustomerConsigneeState {
  // Data state
  relationships: CustomerConsignee[]
  loading: boolean
  error: string | null

  // Filter and search state
  filter: CustomerConsigneeFilter
  searchTerm: string
  sortBy:
    | 'customer_name'
    | 'consignee_name'
    | 'is_default'
    | 'is_active'
    | 'created_at'
  sortOrder: 'asc' | 'desc'

  // Pagination state
  currentPage: number
  pageSize: number
  totalCount: number

  // UI state
  selectedRelationships: Set<string>
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  isBulkImporting: boolean

  // Actions
  fetchRelationships: () => Promise<void>
  fetchRelationshipById: (id: string) => Promise<CustomerConsignee | null>
  createRelationship: (
    relationship: CustomerConsigneeInsert
  ) => Promise<CustomerConsignee>
  updateRelationship: (
    id: string,
    updates: Partial<CustomerConsigneeUpdate>
  ) => Promise<CustomerConsignee>
  deleteRelationship: (id: string) => Promise<void>
  deleteRelationships: (ids: string[]) => Promise<void>
  bulkImportRelationships: (
    data: CustomerConsigneeBulkData[]
  ) => Promise<CustomerConsigneeBulkResult>

  // Filter and search actions
  setFilter: (filter: Partial<CustomerConsigneeFilter>) => void
  setSearchTerm: (term: string) => void
  setSorting: (
    sortBy: CustomerConsigneeState['sortBy'],
    sortOrder: CustomerConsigneeState['sortOrder']
  ) => void

  // Pagination actions
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Selection actions
  selectRelationship: (id: string) => void
  deselectRelationship: (id: string) => void
  selectAllRelationships: () => void
  clearSelection: () => void

  // Utility actions
  clearError: () => void
  reset: () => void

  // Real-time subscription management
  subscribeToRelationships: () => () => void

  // Helper methods
  getCustomerOptions: () => Promise<Company[]>
  getConsigneeOptions: () => Promise<Company[]>
  getDefaultConsigneeForCustomer: (
    customerId: string
  ) => Promise<CustomerConsignee | null>

  // Internal state for debouncing
  _searchTimeout?: NodeJS.Timeout
}

const initialState = {
  relationships: [],
  loading: false,
  error: null,
  filter: {},
  searchTerm: '',
  sortBy: 'customer_name' as const,
  sortOrder: 'asc' as const,
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  selectedRelationships: new Set<string>(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isBulkImporting: false,
  _searchTimeout: undefined,
}

export const useCustomerConsigneeStore = create<CustomerConsigneeState>(
  (set, get) => ({
    ...initialState,

    // Fetch relationships with filters, search, and pagination
    fetchRelationships: async () => {
      set({ loading: true, error: null })

      try {
        const supabase = createClient()

        // First check if the table exists by doing a simple query
        const { error: tableCheckError } = await supabase
          .from('customer_consignees')
          .select('id')
          .limit(1)

        if (tableCheckError) {
          console.warn('customer_consignees table not found or not accessible:', tableCheckError.message)
          set({
            relationships: [],
            totalCount: 0,
            loading: false,
            error: 'Customer-consignee relationships feature is not yet available. Please contact your administrator.'
          })
          return
        }

        const { filter, searchTerm, sortBy, sortOrder, currentPage, pageSize } =
          get()

        // Fetch all data matching filters (without search term first)
        let allQuery = supabase.from('customer_consignees').select(
          `
          *,
          customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
          consignee:companies!consignee_id(id, name, company_type, contact_phone, contact_email)
        `
        )

        // Apply same filters to the all query
        if (filter.customer_id) {
          allQuery = allQuery.eq('customer_id', filter.customer_id)
        }

        if (filter.consignee_id) {
          allQuery = allQuery.eq('consignee_id', filter.consignee_id)
        }

        if (filter.is_active !== undefined) {
          allQuery = allQuery.eq('is_active', filter.is_active)
        } else {
          // Default to show only active relationships
          allQuery = allQuery.eq('is_active', true)
        }

        if (filter.is_default !== undefined) {
          allQuery = allQuery.eq('is_default', filter.is_default)
        }

        const { data: allData, error: allError } = await allQuery

        if (allError) {
          throw new Error(allError.message)
        }

        let filteredData = allData || []

        // Apply search filter in JavaScript
        if (searchTerm.trim()) {
          const lowerSearchTerm = searchTerm.toLowerCase().trim()
          filteredData = filteredData.filter(relationship => {
            const customerName = relationship.customer?.name?.toLowerCase() || ''
            const consigneeName = relationship.consignee?.name?.toLowerCase() || ''
            const notes = relationship.notes?.toLowerCase() || ''

            return (
              customerName.includes(lowerSearchTerm) ||
              consigneeName.includes(lowerSearchTerm) ||
              notes.includes(lowerSearchTerm)
            )
          })
        }

        // Apply sorting
        filteredData.sort((a, b) => {
          let aValue: any
          let bValue: any

          switch (sortBy) {
            case 'customer_name':
              aValue = a.customer?.name || ''
              bValue = b.customer?.name || ''
              break
            case 'consignee_name':
              aValue = a.consignee?.name || ''
              bValue = b.consignee?.name || ''
              break
            case 'is_default':
              aValue = a.is_default ? 1 : 0
              bValue = b.is_default ? 1 : 0
              break
            case 'is_active':
              aValue = a.is_active ? 1 : 0
              bValue = b.is_active ? 1 : 0
              break
            case 'created_at':
              aValue = new Date(a.created_at || 0)
              bValue = new Date(b.created_at || 0)
              break
            default:
              aValue = a[sortBy] || ''
              bValue = b[sortBy] || ''
          }

          if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase()
            bValue = bValue.toLowerCase()
          }

          if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
          if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
          return 0
        })

        // Apply pagination to filtered results
        const totalCount = filteredData.length
        const from = (currentPage - 1) * pageSize
        const to = from + pageSize
        const paginatedData = filteredData.slice(from, to)

        set({
          relationships: paginatedData,
          totalCount,
          loading: false,
        })
      } catch (error) {
        console.error('Error fetching customer-consignee relationships:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to fetch relationships',
          loading: false,
        })
      }
    },

    // Fetch single relationship by ID
    fetchRelationshipById: async (id: string) => {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('customer_consignees')
          .select(
            `
          *,
          customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
          consignee:companies!consignee_id(id, name, company_type, contact_phone, contact_email)
        `
          )
          .eq('id', id)
          .single()

        if (error) {
          throw new Error(error.message)
        }

        return data
      } catch (error) {
        console.error('Error fetching relationship:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to fetch relationship',
        })
        return null
      }
    },

    // Create new relationship
    createRelationship: async (relationshipData: CustomerConsigneeInsert) => {
      set({ isCreating: true, error: null })

      try {
        const supabase = createClient()

        // Check for duplicate relationship - race condition prevention
        const { data: existing } = await supabase
          .from('customer_consignees')
          .select('id')
          .eq('customer_id', relationshipData.customer_id)
          .eq('consignee_id', relationshipData.consignee_id)
          .single()

        if (existing) {
          throw new Error('This customer-consignee relationship already exists')
        }

        // If setting as default, reset other defaults for this customer
        if (relationshipData.is_default) {
          await supabase
            .from('customer_consignees')
            .update({ is_default: false })
            .eq('customer_id', relationshipData.customer_id)
            .eq('is_default', true)
        }

        // Create the relationship
        const { data: relationship, error: createError } = await supabase
          .from('customer_consignees')
          .insert({
            ...relationshipData,
            is_active: relationshipData.is_active ?? true,
          })
          .select(
            `
          *,
          customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
          consignee:companies!consignee_id(id, name, company_type, contact_phone, contact_email)
        `
          )
          .single()

        if (createError) {
          throw new Error(createError.message)
        }

        // Refresh the list to show the new relationship
        await get().fetchRelationships()

        set({ isCreating: false })
        return relationship
      } catch (error) {
        console.error('Error creating relationship:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to create relationship',
          isCreating: false,
        })
        throw error
      }
    },

    // Update existing relationship
    updateRelationship: async (
      id: string,
      updates: Partial<CustomerConsigneeUpdate>
    ) => {
      set({ isUpdating: true, error: null })

      try {
        const supabase = createClient()

        // If updating to set as default, reset other defaults for this customer
        if (updates.is_default && updates.customer_id) {
          await supabase
            .from('customer_consignees')
            .update({ is_default: false })
            .eq('customer_id', updates.customer_id)
            .eq('is_default', true)
            .neq('id', id)
        }

        // Update the relationship
        const { data: relationship, error: updateError } = await supabase
          .from('customer_consignees')
          .update({
            ...updates,
            updated_at: new Date().toISOString(),
          })
          .eq('id', id)
          .select(
            `
          *,
          customer:companies!customer_id(id, name, company_type, contact_phone, contact_email),
          consignee:companies!consignee_id(id, name, company_type, contact_phone, contact_email)
        `
          )
          .single()

        if (updateError) {
          throw new Error(updateError.message)
        }

        // Update the relationship in local state
        set(state => ({
          relationships: state.relationships.map(rel =>
            rel.id === id ? { ...rel, ...relationship } : rel
          ),
          isUpdating: false,
        }))

        return relationship
      } catch (error) {
        console.error('Error updating relationship:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to update relationship',
          isUpdating: false,
        })
        throw error
      }
    },

    // Delete single relationship
    deleteRelationship: async (id: string) => {
      set({ isDeleting: true, error: null })

      try {
        const supabase = createClient()

        // Hard delete the relationship (customer-consignee relationships can be safely removed)
        const { error } = await supabase
          .from('customer_consignees')
          .delete()
          .eq('id', id)

        if (error) {
          throw new Error(error.message)
        }

        // Remove from local state
        set(state => ({
          relationships: state.relationships.filter(rel => rel.id !== id),
          selectedRelationships: new Set(
            [...state.selectedRelationships].filter(
              selectedId => selectedId !== id
            )
          ),
          isDeleting: false,
        }))
      } catch (error) {
        console.error('Error deleting relationship:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to delete relationship',
          isDeleting: false,
        })
        throw error
      }
    },

    // Delete multiple relationships
    deleteRelationships: async (ids: string[]) => {
      set({ isDeleting: true, error: null })

      try {
        const supabase = createClient()

        // Hard delete the relationships
        const { error } = await supabase
          .from('customer_consignees')
          .delete()
          .in('id', ids)

        if (error) {
          throw new Error(error.message)
        }

        // Remove from local state
        set(state => ({
          relationships: state.relationships.filter(
            rel => !ids.includes(rel.id)
          ),
          selectedRelationships: new Set(),
          isDeleting: false,
        }))
      } catch (error) {
        console.error('Error deleting relationships:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to delete relationships',
          isDeleting: false,
        })
        throw error
      }
    },

    // Bulk import relationships
    bulkImportRelationships: async (data: CustomerConsigneeBulkData[]) => {
      set({ isBulkImporting: true, error: null })

      try {
        const supabase = createClient()
        const result: CustomerConsigneeBulkResult = {
          success: [],
          errors: [],
          summary: {
            total: data.length,
            successful: 0,
            failed: 0,
          },
        }

        // Get all companies to match names
        const { data: companies } = await supabase
          .from('companies')
          .select('id, name, company_type')
          .eq('is_active', true)

        const customers =
          companies?.filter(c => c.company_type === 'customer') || []
        const consignees =
          companies?.filter(c => c.company_type === 'consignee') || []

        // Process each row
        for (let i = 0; i < data.length; i++) {
          const row = data[i]

          try {
            // Find matching companies
            const customer = customers.find(
              c =>
                c.name.toLowerCase().trim() ===
                row.customer_name.toLowerCase().trim()
            )
            const consignee = consignees.find(
              c =>
                c.name.toLowerCase().trim() ===
                row.consignee_name.toLowerCase().trim()
            )

            if (!customer) {
              throw new Error(`Customer "${row.customer_name}" not found`)
            }
            if (!consignee) {
              throw new Error(`Consignee "${row.consignee_name}" not found`)
            }

            // Check for existing relationship
            const { data: existing } = await supabase
              .from('customer_consignees')
              .select('id')
              .eq('customer_id', customer.id)
              .eq('consignee_id', consignee.id)
              .single()

            if (existing) {
              throw new Error('Relationship already exists')
            }

            // If setting as default, reset other defaults
            if (row.is_default) {
              await supabase
                .from('customer_consignees')
                .update({ is_default: false })
                .eq('customer_id', customer.id)
                .eq('is_default', true)
            }

            // Create the relationship
            await supabase.from('customer_consignees').insert({
              customer_id: customer.id,
              consignee_id: consignee.id,
              is_default: row.is_default || false,
              is_active: true,
              notes: row.notes,
            })

            result.success.push(row)
            result.summary.successful++
          } catch (error) {
            result.errors.push({
              row: i + 1,
              data: row,
              error: error instanceof Error ? error.message : 'Unknown error',
            })
            result.summary.failed++
          }
        }

        // Refresh the list
        await get().fetchRelationships()

        set({ isBulkImporting: false })
        return result
      } catch (error) {
        console.error('Error bulk importing relationships:', error)
        set({
          error:
            error instanceof Error
              ? error.message
              : 'Failed to bulk import relationships',
          isBulkImporting: false,
        })
        throw error
      }
    },

    // Filter and search actions
    setFilter: (newFilter: Partial<CustomerConsigneeFilter>) => {
      set(state => ({
        filter: { ...state.filter, ...newFilter },
        currentPage: 1, // Reset to first page when filtering
      }))
      get().fetchRelationships()
    },

    setSearchTerm: (term: string) => {
      const state = get()

      // Clear any existing timeout
      if (state._searchTimeout) {
        clearTimeout(state._searchTimeout)
      }

      set({ searchTerm: term, currentPage: 1 })

      // Debounce the search
      const timeout = setTimeout(() => {
        if (get().searchTerm === term) {
          get().fetchRelationships()
        }
      }, 300)

      set({ _searchTimeout: timeout })
    },

    setSorting: (
      sortBy: CustomerConsigneeState['sortBy'],
      sortOrder: CustomerConsigneeState['sortOrder']
    ) => {
      set({ sortBy, sortOrder })
      get().fetchRelationships()
    },

    // Pagination actions
    setPage: (page: number) => {
      set({ currentPage: page })
      get().fetchRelationships()
    },

    setPageSize: (size: number) => {
      set({ pageSize: size, currentPage: 1 })
      get().fetchRelationships()
    },

    // Selection actions
    selectRelationship: (id: string) => {
      set(state => ({
        selectedRelationships: new Set([...state.selectedRelationships, id]),
      }))
    },

    deselectRelationship: (id: string) => {
      set(state => {
        const newSelection = new Set(state.selectedRelationships)
        newSelection.delete(id)
        return { selectedRelationships: newSelection }
      })
    },

    selectAllRelationships: () => {
      set(state => ({
        selectedRelationships: new Set(state.relationships.map(rel => rel.id)),
      }))
    },

    clearSelection: () => {
      set({ selectedRelationships: new Set() })
    },

    // Utility actions
    clearError: () => {
      set({ error: null })
    },

    reset: () => {
      set(initialState)
    },

    // Real-time subscription
    subscribeToRelationships: () => {
      const supabase = createClient()

      const subscription = supabase
        .channel('customer_consignees_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'customer_consignees',
          },
          payload => {
            console.log(
              'Customer-consignee relationship change received:',
              payload
            )
            // Refresh the list when changes occur
            get().fetchRelationships()
          }
        )
        .subscribe()

      return () => {
        subscription.unsubscribe()
      }
    },

    // Helper methods
    getCustomerOptions: async () => {
      try {
        const supabase = createClient()

        const { data, error } = await supabase
          .from('companies')
          .select('id, name, company_type, contact_phone, contact_email')
          .eq('company_type', 'customer')
          .eq('is_active', true)
          .order('name')

        if (error) {
          console.error('Supabase error fetching customer options:', error)
          throw new Error(`Customer query error: ${error.message}`)
        }

        return data || []
      } catch (error) {
        console.error('Error fetching customer options:', error)
        throw error
      }
    },

    getConsigneeOptions: async () => {
      try {
        const supabase = createClient()
        console.log('Fetching consignee options...')

        const { data, error } = await supabase
          .from('companies')
          .select('id, name, company_type, contact_phone, contact_email')
          .eq('company_type', 'consignee')
          .eq('is_active', true)
          .order('name')

        if (error) {
          console.error('Supabase error fetching consignee options:', error)
          throw new Error(`Consignee query error: ${error.message}`)
        }

        console.log('Consignee options fetched:', data?.length || 0, 'records')
        return data || []
      } catch (error) {
        console.error('Error fetching consignee options:', error)
        throw error
      }
    },

    getDefaultConsigneeForCustomer: async (customerId: string) => {
      try {
        const supabase = createClient()
        const { data } = await supabase
          .from('customer_consignees')
          .select(
            `
          *,
          consignee:companies!consignee_id(id, name)
        `
          )
          .eq('customer_id', customerId)
          .eq('is_default', true)
          .eq('is_active', true)
          .single()

        return data
      } catch (error) {
        console.error('Error fetching default consignee:', error)
        return null
      }
    },
  })
)

// Selector hooks for better performance
export const useCustomerConsignees = () =>
  useCustomerConsigneeStore(state => state.relationships)
export const useCustomerConsigneesLoading = () =>
  useCustomerConsigneeStore(state => state.loading)
export const useCustomerConsigneesError = () =>
  useCustomerConsigneeStore(state => state.error)
export const useCustomerConsigneeActions = () =>
  useCustomerConsigneeStore(state => ({
    fetchRelationships: state.fetchRelationships,
    fetchRelationshipById: state.fetchRelationshipById,
    createRelationship: state.createRelationship,
    updateRelationship: state.updateRelationship,
    deleteRelationship: state.deleteRelationship,
    deleteRelationships: state.deleteRelationships,
    bulkImportRelationships: state.bulkImportRelationships,
    setFilter: state.setFilter,
    setSearchTerm: state.setSearchTerm,
    setSorting: state.setSorting,
    setPage: state.setPage,
    selectRelationship: state.selectRelationship,
    deselectRelationship: state.deselectRelationship,
    clearSelection: state.clearSelection,
    clearError: state.clearError,
    subscribeToRelationships: state.subscribeToRelationships,
    getCustomerOptions: state.getCustomerOptions,
    getConsigneeOptions: state.getConsigneeOptions,
    getDefaultConsigneeForCustomer: state.getDefaultConsigneeForCustomer,
  }))