'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { ArrowLeft, Truck, Package, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ContainerDataForm } from '@/components/mobile/container-data-form'
import { OfflineIndicator } from '@/components/mobile/offline-indicator'
import { useOfflineStatus } from '@/hooks/use-mobile'
import { useLanguage } from '@/hooks/use-language'
import type { Container } from '@/types/container'

function ContainerDataPageContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { isOnline } = useOfflineStatus()
  const { t } = useLanguage()
  
  // Get parameters from URL
  const containerId = searchParams.get('containerId')
  const shipmentId = searchParams.get('shipmentId')
  const shipmentNumber = searchParams.get('shipmentNumber')
  
  const [error, setError] = useState<string | null>(null)

  // Validate required parameters
  useEffect(() => {
    if (!containerId || !shipmentId) {
      setError(t('containerData.missingParams'))
    }
  }, [containerId, shipmentId, t])

  // Handle successful container data update
  const handleContainerDataComplete = (container: Container) => {
    console.log('Container data updated:', container)
    
    // Navigate back to shipment details or dashboard
    const returnUrl = searchParams.get('returnUrl')
    if (returnUrl) {
      router.push(returnUrl)
    } else {
      router.push('/driver/dashboard')
    }
  }

  // Handle cancel/back navigation
  const handleCancel = () => {
    const returnUrl = searchParams.get('returnUrl')
    if (returnUrl) {
      router.push(returnUrl)
    } else {
      router.back()
    }
  }

  // Show error state if parameters are missing
  if (error) {
    return (
      <div className="min-h-screen bg-slate-900 text-white p-4">
        <div className="max-w-md mx-auto">
          <Alert className="border-red-500/30 bg-red-500/10">
            <AlertTriangle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-300">
              {error}
            </AlertDescription>
          </Alert>
          
          <Button
            onClick={() => router.back()}
            className="w-full mt-4 bg-slate-700 hover:bg-slate-600 text-white"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('containerData.goBack')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <div className="bg-slate-800 border-b border-slate-700 p-4">
        <div className="flex items-center space-x-3">
          <Button
            onClick={handleCancel}
            variant="ghost"
            size="sm"
            className="text-slate-300 hover:bg-slate-700 p-2"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          
          <div className="flex-1">
            <h1 className="text-lg font-medium text-white">
              {t('containerData.title')}
            </h1>
            {shipmentNumber && (
              <p className="text-sm text-slate-400">
                {t('containerData.shipment')}: {shipmentNumber}
              </p>
            )}
          </div>
          
          <div className="flex space-x-2">
            <Package className="w-5 h-5 text-orange-400" />
            <Truck className="w-5 h-5 text-slate-400" />
          </div>
        </div>
      </div>

      {/* Offline Status */}
      {!isOnline && (
        <div className="p-4">
          <OfflineIndicator />
        </div>
      )}

      {/* Main Content */}
      <div className="p-4">
        <div className="max-w-md mx-auto">
          {containerId && shipmentId && (
            <ContainerDataForm
              containerId={containerId}
              shipmentId={shipmentId}
              onComplete={handleContainerDataComplete}
              onCancel={handleCancel}
              className="space-y-6"
            />
          )}
        </div>
      </div>

      {/* Footer Info */}
      <div className="fixed bottom-0 left-0 right-0 bg-slate-800/90 backdrop-blur border-t border-slate-700 p-4">
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-center space-x-2 text-xs text-slate-400">
            <Package className="w-4 h-4" />
            <span>{t('containerData.mobileEntry')}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

function ContainerDataLoadingFallback() {
  const { t } = useLanguage()
  return (
    <div className="min-h-screen bg-slate-900 text-white flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
        <p className="text-slate-400">{t('containerData.loadingForm')}</p>
      </div>
    </div>
  )
}

export default function ContainerDataPage() {
  return (
    <Suspense fallback={<ContainerDataLoadingFallback />}>
      <ContainerDataPageContent />
    </Suspense>
  )
}