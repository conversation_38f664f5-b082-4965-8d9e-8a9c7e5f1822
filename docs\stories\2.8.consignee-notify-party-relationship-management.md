# Story 2.8: Consignee-Notify Party Relationship Management

## Status
Done

## Story
**As a** CS representative,  
**I want** to configure consignee-notify party relationships with communication preferences,  
**so that** notification workflows are properly established for shipment coordination.

## Acceptance Criteria

**1:** Consignee-notify party relationship interface supports multiple notify parties per consignee.

**2:** Notification preferences capture communication channels (email, SMS, Line, WeChat) and priority ordering.

**3:** Default notify party selection enables automatic population during shipment creation.

**4:** Special instructions field captures consignee-specific handling or communication requirements.

**5:** Relationship validation ensures both entities have appropriate company types.

## Tasks / Subtasks

- [x] Create consignee-notify party relationship management page and navigation integration (AC: 1, 3)
  - [x] Add notify party relationships navigation item to master-data section in sidebar
  - [x] Create `src/app/(dashboard)/master-data/notify-parties/page.tsx` with consignee-notify party list view
  - [x] Implement consignee-notify party data table with filtering and search capabilities
  - [x] Add filtering by consignee company and active relationships
  - [x] Implement search functionality for consignee and notify party names

- [x] Develop consignee-notify party relationship form component (AC: 1, 2, 3, 4, 5)
  - [x] Create `src/components/forms/consignee-notify-party-form/consignee-notify-party-form.tsx` component
  - [x] Implement consignee selection dropdown (consignee type only)
  - [x] Implement notify party selection dropdown (notify_party type only)
  - [x] Add notification preferences JSONB field with communication channels (email, SMS, Line, WeChat)
  - [x] Add priority ordering field for notification sequence
  - [x] Add default notify party toggle with automatic previous default reset logic
  - [x] Add special instructions field for consignee-specific handling requirements
  - [x] Apply form validation with Zod schema for required fields and company type constraints

- [x] Implement consignee-notify party validation and state management (AC: 1, 3, 4, 5)
  - [x] Extend `src/lib/validations/` with consignee-notify-parties.ts validation schema
  - [x] Create consignee-notify party store in `src/stores/consignee-notify-party-store.ts` for state management
  - [x] Implement `src/hooks/use-consignee-notify-parties.ts` for CRUD operations
  - [x] Add automatic default toggle validation to ensure only one default per consignee
  - [x] Implement company type validation for consignee and notify_party types
  - [x] Add priority ordering validation to prevent conflicts

- [x] Integrate with shipment creation interfaces for intelligent pre-population (AC: 3)
  - [x] Update shipment creation form to subscribe to consignee-notify party relationship changes
  - [x] Implement intelligent pre-population when consignee is selected
  - [x] Add real-time notify party dropdown updates when relationships change
  - [x] Ensure default notify party selection works seamlessly in shipment workflows
  - [x] Pre-populate notification preferences and special instructions automatically

- [x] Create comprehensive testing suite (All ACs)
  - [x] Write unit tests for consignee-notify party form validation and notification preferences
  - [x] Test default notify party toggle functionality with automatic previous default reset
  - [x] Create integration tests for relationship CRUD operations with company type validation
  - [x] Test priority ordering functionality and conflict prevention
  - [x] Test real-time updates in shipment creation interface integration
  - [x] Validate notification preferences JSONB handling and communication channel storage
  - [x] Test special instructions field validation and storage

## Dev Notes

### Previous Story Insights
From Story 2.7: Customer-Product Relationship Management completed with comprehensive relationship intelligence patterns and type-specific validation. The established patterns for company type filtering (customers/products), default designation logic (single default per customer), and real-time integration with shipment workflows can be directly applied to consignee-notify party relationships, ensuring consistent validation patterns and seamless shipment creation integration.

### Data Models and Database Schema Context
**Consignee-Notify Party Relationship Table Design:**
[Source: User-provided consignee_notify_parties schema]
Consignee-notify party relationships use the `consignee_notify_parties` table with the following structure:
- id: UUID primary key with auto-generation (gen_random_uuid())
- consignee_id: UUID reference to companies table (company_type must equal 'consignee')
- notify_party_id: UUID reference to companies table (company_type must equal 'notify_party')
- is_default: boolean - Default notify party designation per consignee (only one per consignee, default: false)
- is_active: boolean - Relationship status controlling visibility in workflows (default: true)
- notification_preferences: jsonb - Communication channels and preferences (email, SMS, Line, WeChat)
- priority_order: integer - Notification priority sequence (default: 1)
- special_instructions: text - Consignee-specific handling or communication requirements
- notes: text - Additional relationship information and context
- created_at/updated_at: timestamptz audit fields with automatic triggers

**Database Constraints and Validation:**
[Source: User-provided schema]
- Unique constraint on (consignee_id, notify_party_id) prevents duplicate relationships
- Foreign key constraints with CASCADE DELETE for data integrity
- Company type validation: `is_company_type(consignee_id, 'consignee')` ensures only consignee companies
- Company type validation: `is_company_type(notify_party_id, 'notify_party')` ensures only notify party companies
- Default boolean logic requires application-level enforcement for single default per consignee
- Performance indexes: consignee_id, notify_party_id, default status (is_default + consignee_id), and active status optimized
- Combined index for active default queries: (consignee_id, is_default DESC, is_active) where is_active = true

**Relationship Intelligence Integration:**
[Source: core-workflows.md#intelligent-shipment-creation]
Consignee-notify party relationships integrate with the Relationship Intelligence Engine for shipment pre-population:
- When consignee is selected, query `consignee_notify_parties` for available and default notify parties
- Default notify party automatically populates in shipment creation forms with preferences and instructions
- Active relationships control notify party dropdown options during shipment creation
- Real-time subscriptions ensure immediate updates when relationships change
- Notification preferences (JSONB) automatically populate based on consignee-notify party configuration

### API Specifications and Database Access Patterns
[Source: backend-architecture.md#data-access-layer]
**Consignee-Notify Party Data Access Pattern:**
```typescript
// Fetch consignee-notify party relationships with complete information
const { data: relationships } = await supabase
  .from('consignee_notify_parties')
  .select(`
    *,
    consignee:companies!consignee_id(name, company_type, contact_phone),
    notify_party:companies!notify_party_id(name, company_type, contact_phone, contact_email)
  `)
  .eq('is_active', true)
  .order('priority_order')
  .order('consignee:companies(name)')

// Create consignee-notify party relationship with validation
const { data: relationship } = await supabase
  .from('consignee_notify_parties')
  .insert({
    consignee_id,
    notify_party_id,
    is_default,
    is_active: true,
    notification_preferences: {
      email: true,
      sms: false,
      line: true,
      wechat: false
    },
    priority_order,
    special_instructions,
    notes
  })
  .select()
  .single()
```

**Default Notify Party Management Pattern:**
```typescript
// Reset previous default when setting new default
if (is_default) {
  await supabase
    .from('consignee_notify_parties')
    .update({ is_default: false })
    .eq('consignee_id', consignee_id)
    .neq('id', relationship_id)
}

// Get default notify party for consignee (shipment creation)
const { data: defaultNotifyParty } = await supabase
  .from('consignee_notify_parties')
  .select(`
    *,
    notify_party:companies!notify_party_id(id, name, company_type, contact_phone, contact_email)
  `)
  .eq('consignee_id', consignee_id)
  .eq('is_default', true)
  .eq('is_active', true)
  .single()
```

### Component Architecture and Integration Patterns
[Source: frontend-architecture.md#component-architecture]
**Consignee-Notify Party Management Architecture:**
- Notify party relationships page at `src/app/(dashboard)/master-data/notify-parties/page.tsx`
- Consignee-notify party form component at `src/components/forms/consignee-notify-party-form/consignee-notify-party-form.tsx`
- Leverage existing company selection patterns for consignee dropdown (consignee type only)
- Use existing company selection patterns for notify party dropdown (notify_party type only)
- Use existing data table patterns with ShadCN UI components for relationship list

**ShadCN UI Component Usage:**
- Use existing DataTable components for relationship list with pagination, sorting, and filtering
- Implement Select component for consignee selection with company type filtering (consignee only)
- Implement Select component for notify party selection with company type filtering (notify_party only)
- Use Checkbox components for notification preferences (email, SMS, Line, WeChat)
- Use Input components for priority ordering with numeric validation
- Use Switch components for default notify party and active status toggles
- Use Textarea components for special instructions and notes fields
- Leverage Badge components for relationship status and priority indicators
- Apply existing form validation patterns with react-hook-form and Zod schemas

**Real-time Integration Patterns:**
- Subscribe to consignee_notify_parties table changes for live updates in relationship list
- Integrate with shipment creation forms for real-time notify party dropdown updates
- Use Zustand store for optimistic updates and state synchronization
- Implement real-time notifications when default notify party designations change
- Real-time notification preferences and special instructions updates in shipment creation interfaces

### File Locations and Project Structure Alignment
[Source: unified-project-structure.md]
**Consignee-Notify Party Relationship File Structure:**
- Main page: `src/app/(dashboard)/master-data/notify-parties/page.tsx`
- Notify party relationship form: `src/components/forms/consignee-notify-party-form/consignee-notify-party-form.tsx`
- Validation: `src/lib/validations/consignee-notify-parties.ts` for relationship schema validation
- State management: `src/stores/consignee-notify-party-store.ts` for relationship operations
- Hooks: `src/hooks/use-consignee-notify-parties.ts` for relationship CRUD operations
- Types: Extend existing database types for consignee-notify party interface definitions

### Technical Constraints and Requirements
[Source: tech-stack.md#technology-stack-table]
- Maintain consistency with Next.js 14.2+ App Router and TypeScript 5.3+
- Follow existing Supabase Client API patterns with auto-generated TypeScript types
- Use PostgreSQL JSONB type for notification_preferences with structured validation
- Maintain ShadCN UI components with established dark blue theme colors
- Follow existing Zustand 4.5+ state management patterns with real-time subscriptions
- Apply consistent Zod validation patterns for form validation and JSONB structure
- Integrate with existing Relationship Intelligence Engine for shipment pre-population
- Support notification preferences structure: {email, sms, line, wechat} with boolean values

### Company Type Validation and Filtering Requirements
**Consignee Selection Filtering:**
- Filter companies table to show only company_type = 'consignee' in consignee dropdown
- Validate selected consignee_id references valid consignee company type using existing patterns
- Use existing company management patterns from previous stories for consistency
- Apply company type validation constraint: `is_company_type(consignee_id, 'consignee')`

**Notify Party Selection Filtering:**
- Filter companies table to show only company_type = 'notify_party' in notify party dropdown
- Validate selected notify_party_id references valid notify party company type
- Use existing company management patterns for consistent filtering and validation
- Apply company type validation constraint: `is_company_type(notify_party_id, 'notify_party')`

**Default Notify Party Logic:**
- Implement application-level logic to ensure only one default notify party per consignee
- When setting new default, automatically reset is_default = false for other relationships
- Validate default notify party selection in shipment creation workflows
- Handle edge cases where consignee has no default notify party designated

### Notification Preferences and Priority Management Requirements
**Notification Preferences JSONB Structure:**
- Support structured JSONB: {email: boolean, sms: boolean, line: boolean, wechat: boolean}
- Validate notification preferences input with proper JSONB handling
- Implement communication channel-specific formatting and display patterns
- Provide notification preferences validation for at least one channel selected

**Priority Ordering Management:**
- Support integer priority_order field with default value 1
- Validate priority ordering input with numeric constraints (1-999)
- Implement priority-based sorting for notification sequence
- Handle priority conflicts and provide guidance for proper ordering

**Special Instructions and Communication Requirements:**
- Support text field for special_instructions with flexible content
- Validate special instructions field length and content appropriateness
- Implement special instructions display in shipment workflows
- Support notes field for additional relationship context and information

### Testing

#### Testing Standards
**Test Location**: `tests/` directory with `unit/`, `integration/`, and `e2e/` subdirectories for consignee-notify party relationship tests
**Testing Framework**: Vitest + Testing Library for unit tests, Playwright for E2E relationship management workflows
**Testing Patterns**: 
- Component testing for consignee-notify party form with notification preferences validation
- Integration testing with local Supabase instance for relationship CRUD operations with JSONB data
- Mock data for isolated component tests with realistic consignee, notify party, and preferences information
- E2E testing for complete relationship management workflows
- Company type restriction validation testing for consignee and notify_party types
- Notification preferences JSONB validation and priority ordering testing

**Specific Testing Requirements for This Story**:
- Test consignee-notify party relationship form with company type filtering (consignee and notify_party types only)
- Validate relationship creation, update, and deletion operations with proper constraints
- Test default notify party toggle functionality with automatic previous default reset
- Verify relationship list filtering by consignee company and active status
- Test search functionality for consignee and notify party names in relationship list
- Validate notification preferences JSONB handling and communication channel validation
- Test priority ordering input validation and conflict prevention
- Validate special instructions and notes field handling
- Test real-time integration with shipment creation interface and pre-population
- Verify relationship status management and visibility control in shipment workflows
- Test integration with existing company management systems
- Validate notification preferences structure and communication channel configuration

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-24 | 1.0 | Initial story creation with comprehensive architecture context and consignee_notify_parties schema integration | Scrum Master |

## Dev Agent Record

### Agent Model Used

*This section will be populated by the development agent during implementation*

### Debug Log References

*This section will be populated by the development agent during implementation*

### Completion Notes List

*This section will be populated by the development agent during implementation*

### File List

*This section will be populated by the development agent during implementation*

## QA Results

*This section will be populated by the QA agent during review*