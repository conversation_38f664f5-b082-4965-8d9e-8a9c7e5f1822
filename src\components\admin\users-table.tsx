'use client'

import { useState, useEffect, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { UserProfile } from '@/lib/supabase/auth'
import type { UserFilterState } from '@/app/(dashboard)/admin/users/page'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Edit, Eye, MoreVertical, UserX, UserCheck, Users } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { EditUserDialog } from './edit-user-dialog'
import { UserDetailDialog } from './user-detail-dialog'
import { formatDistanceToNow } from 'date-fns'

interface ExtendedUserProfile extends UserProfile {
  company_name?: string
}

interface UsersTableProps {
  filters: UserFilterState
}

export function UsersTable({ filters }: UsersTableProps) {
  const [users, setUsers] = useState<ExtendedUserProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [editingUser, setEditingUser] = useState<UserProfile | null>(null)
  const [viewingUser, setViewingUser] = useState<UserProfile | null>(null)

  const supabase = createClient()

  // Filter users based on the filters prop
  const filteredUsers = useMemo(() => {
    let filtered = users

    // Filter by search term
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      filtered = filtered.filter(
        user =>
          user.first_name?.toLowerCase().includes(searchLower) ||
          user.last_name?.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          `${user.first_name} ${user.last_name}`
            .toLowerCase()
            .includes(searchLower)
      )
    }

    // Filter by role
    if (filters.selectedRole !== 'all') {
      filtered = filtered.filter(user => user.role === filters.selectedRole)
    }

    // Filter by status
    if (filters.selectedStatus !== 'all') {
      const isActive = filters.selectedStatus === 'active'
      filtered = filtered.filter(user => user.is_active === isActive)
    }

    return filtered
  }, [users, filters])

  useEffect(() => {
    loadUsers()
  }, [])

  async function loadUsers() {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('profiles')
        .select(
          `
          *,
          companies:company_id (
            name
          )
        `
        )
        .order('created_at', { ascending: false })

      if (error) throw error

      const usersWithCompany =
        data?.map(user => ({
          ...user,
          company_name: user.companies?.name,
        })) || []

      setUsers(usersWithCompany)
    } catch (err) {
      console.error('Error loading users:', err)
      setError(err instanceof Error ? err.message : 'Failed to load users')
    } finally {
      setLoading(false)
    }
  }

  async function toggleUserStatus(userId: string, currentStatus: boolean) {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          is_active: !currentStatus,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId)

      if (error) throw error

      // Refresh the users list
      loadUsers()
    } catch (err) {
      console.error('Error updating user status:', err)
    }
  }

  function getRoleBadgeColor(role: string) {
    const colors = {
      admin: 'bg-red-500/20 text-red-300 border-red-500/30',
      cs: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      account: 'bg-green-500/20 text-green-300 border-green-500/30',
      customer: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
      carrier: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30',
      driver: 'bg-orange-500/20 text-orange-300 border-orange-500/30',
      factory: 'bg-indigo-500/20 text-indigo-300 border-indigo-500/30',
      shipper: 'bg-pink-500/20 text-pink-300 border-pink-500/30',
      consignee: 'bg-teal-500/20 text-teal-300 border-teal-500/30',
      notify_party: 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30',
      forwarder_agent: 'bg-amber-500/20 text-amber-300 border-amber-500/30',
    }
    return (
      colors[role as keyof typeof colors] ||
      'bg-slate-500/20 text-slate-300 border-slate-500/30'
    )
  }

  if (loading) {
    return (
      <div className="bg-slate-800 rounded-lg p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex space-x-4">
              <div className="h-4 bg-slate-700 rounded flex-1"></div>
              <div className="h-4 bg-slate-700 rounded w-24"></div>
              <div className="h-4 bg-slate-700 rounded w-20"></div>
              <div className="h-4 bg-slate-700 rounded w-16"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
        <p className="text-red-300">Error loading users: {error}</p>
        <Button onClick={loadUsers} className="mt-4" variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="bg-slate-800 rounded-lg overflow-hidden">
      {/* Table Header */}
      <div className="bg-slate-900 px-6 py-4 border-b border-slate-700">
        <div className="grid grid-cols-12 gap-4 text-sm font-medium text-slate-300">
          <div className="col-span-3">User</div>
          <div className="col-span-2">Role</div>
          <div className="col-span-2">Company</div>
          <div className="col-span-2">Status</div>
          <div className="col-span-2">Joined</div>
          <div className="col-span-1 text-right">Actions</div>
        </div>
      </div>

      {/* Table Body */}
      <div className="divide-y divide-slate-700">
        {filteredUsers.map(user => (
          <div
            key={user.user_id}
            className="px-6 py-4 hover:bg-slate-700/50 transition-colors"
          >
            <div className="grid grid-cols-12 gap-4 items-center">
              {/* User Info */}
              <div className="col-span-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {(user.first_name?.[0] || user.email[0]).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="text-white font-medium">
                      {user.first_name || user.last_name
                        ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
                        : user.email.split('@')[0]}
                    </p>
                    <p className="text-slate-400 text-sm">{user.email}</p>
                  </div>
                </div>
              </div>

              {/* Role */}
              <div className="col-span-2">
                <Badge className={getRoleBadgeColor(user.role)}>
                  {user.role.replace('_', ' ')}
                </Badge>
              </div>

              {/* Company */}
              <div className="col-span-2">
                <span className="text-slate-300">
                  {user.company_name || 'No company'}
                </span>
              </div>

              {/* Status */}
              <div className="col-span-2">
                <div className="flex items-center space-x-2">
                  <div
                    className={`w-2 h-2 rounded-full ${user.is_active ? 'bg-green-400' : 'bg-red-400'}`}
                  />
                  <span
                    className={
                      user.is_active ? 'text-green-300' : 'text-red-300'
                    }
                  >
                    {user.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              {/* Joined Date */}
              <div className="col-span-2">
                <span className="text-slate-400 text-sm">
                  {formatDistanceToNow(new Date(user.created_at), {
                    addSuffix: true,
                  })}
                </span>
              </div>

              {/* Actions */}
              <div className="col-span-1 text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-slate-400 hover:text-white hover:bg-orange-600 border border-slate-600 hover:border-slate-500"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className="bg-slate-800 border-slate-700"
                  >
                    <DropdownMenuItem
                      className="text-slate-300 hover:bg-slate-700"
                      onClick={() => setViewingUser(user)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-slate-300 hover:bg-slate-700"
                      onClick={() => setEditingUser(user)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit User
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-slate-300 hover:bg-slate-700"
                      onClick={() =>
                        toggleUserStatus(user.user_id, user.is_active)
                      }
                    >
                      {user.is_active ? (
                        <>
                          <UserX className="h-4 w-4 mr-2" />
                          Deactivate
                        </>
                      ) : (
                        <>
                          <UserCheck className="h-4 w-4 mr-2" />
                          Activate
                        </>
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredUsers.length === 0 && users.length > 0 && (
        <div className="px-6 py-12 text-center">
          <Users className="h-12 w-12 text-slate-600 mx-auto mb-4" />
          <p className="text-slate-400">No users match the current filters</p>
        </div>
      )}

      {users.length === 0 && !loading && (
        <div className="px-6 py-12 text-center">
          <Users className="h-12 w-12 text-slate-600 mx-auto mb-4" />
          <p className="text-slate-400">No users found</p>
        </div>
      )}

      {/* Edit User Dialog */}
      <EditUserDialog
        user={editingUser}
        open={!!editingUser}
        onOpenChange={open => !open && setEditingUser(null)}
        onUserUpdated={loadUsers}
      />

      {/* User Detail Dialog */}
      <UserDetailDialog
        user={viewingUser}
        open={!!viewingUser}
        onOpenChange={open => !open && setViewingUser(null)}
      />
    </div>
  )
}
