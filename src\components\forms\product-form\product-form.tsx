'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, Info, Package } from 'lucide-react'
import { productFormSchema, type ProductForm } from '@/lib/validations/products'
import { useUnitsData } from '@/hooks/use-units'
import {
  useProductValidation,
  useProductCategories,
} from '@/hooks/use-products'
import type { Product } from '@/lib/supabase/types'

interface ProductFormProps {
  product?: Product | null
  onSubmit: (data: ProductForm) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  className?: string
}

export function ProductForm({
  product,
  onSubmit,
  onCancel,
  isLoading = false,
  className,
}: ProductFormProps) {
  const [error, setError] = useState<string | null>(null)
  const { activeUnits, unitsByCategory } = useUnitsData()
  const { validateUniqueCode, validateHSCode } = useProductValidation()
  const { categoryOptions } = useProductCategories()

  const form = useForm<ProductForm>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      name: product?.name || '',
      code: product?.code || '',
      description: product?.description || '',
      category: product?.category || 'none',
      hs_code: product?.hs_code || '',
      unit_of_measure_id: product?.unit_of_measure_id || '',
    },
  })

  const watchedCode = form.watch('code')
  const watchedHSCode = form.watch('hs_code')
  const watchedUnitId = form.watch('unit_of_measure_id')

  // Find selected unit for display
  const selectedUnit = watchedUnitId
    ? activeUnits.find(unit => unit.id === watchedUnitId)
    : null

  // Real-time validation for unique code
  useEffect(() => {
    if (watchedCode && !validateUniqueCode(watchedCode, product?.id)) {
      form.setError('code', {
        type: 'manual',
        message: 'Product code already exists',
      })
    } else {
      form.clearErrors('code')
    }
  }, [watchedCode, validateUniqueCode, product?.id, form])

  // Real-time validation for HS code
  useEffect(() => {
    if (watchedHSCode) {
      const hsError = validateHSCode(watchedHSCode)
      if (hsError) {
        form.setError('hs_code', {
          type: 'manual',
          message: hsError,
        })
      } else {
        form.clearErrors('hs_code')
      }
    }
  }, [watchedHSCode, validateHSCode, form])

  const handleSubmit = async (data: ProductForm) => {
    try {
      setError(null)
      // Convert "none" back to null/empty for category
      const formData = {
        ...data,
        category: data.category === 'none' ? null : data.category,
      }
      await onSubmit(formData)
    } catch (error) {
      console.error('Form submission error:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to save product'
      )
    }
  }

  // Group units by category for better UX
  const unitsGroupedByCategory = Object.entries(unitsByCategory).map(
    ([category, units]) => ({
      category,
      units: units.filter(unit => unit.is_active),
    })
  )

  return (
    <Card className={`bg-slate-800 border-slate-700 ${className}`}>
      <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
        <CardTitle className="text-white flex items-center gap-2">
          <Package className="h-5 w-5 text-orange-500" />
          {product ? 'Edit Product' : 'Create Product'}
        </CardTitle>
      </CardHeader>
      <CardContent className="bg-slate-800 text-slate-100">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      Product Name *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., Premium Coffee Beans"
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      The display name for this product
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      Product Code (Optional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., COFFEE-001"
                        className="uppercase bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                        onChange={e =>
                          field.onChange(e.target.value.toUpperCase())
                        }
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Internal product identifier (letters, numbers, hyphens,
                      underscores)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-200 font-medium">
                    Description (Optional)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Detailed product description..."
                      rows={3}
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                    />
                  </FormControl>
                  <FormDescription className="text-slate-400">
                    Detailed product information (max 500 characters)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      Category (Optional)
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue
                            placeholder="Select or type category"
                            className="text-slate-400"
                          />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          <SelectItem
                            value="none"
                            className="text-white hover:bg-slate-600"
                          >
                            No Category
                          </SelectItem>
                          {categoryOptions.map(option => (
                            <SelectItem
                              key={option.value}
                              value={option.value}
                              className="text-white hover:bg-slate-600"
                            >
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Product classification for organization
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hs_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      HS Code (Optional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., 0901.21.00"
                        maxLength={20}
                        className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
                      />
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Harmonized System code for customs (6-10 digits)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Unit of Measure Selection */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="unit_of_measure_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-200 font-medium">
                      Unit of Measure *
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                          <SelectValue
                            placeholder="Select unit of measure"
                            className="text-slate-400"
                          />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          {unitsGroupedByCategory.map(({ category, units }) => (
                            <div key={category}>
                              <div className="px-2 py-1 text-sm font-medium text-orange-400 capitalize border-b border-slate-600">
                                {category}
                              </div>
                              {units.map(unit => (
                                <SelectItem
                                  key={unit.id}
                                  value={unit.id}
                                  className="text-white hover:bg-slate-600"
                                >
                                  <div className="flex items-center justify-between w-full">
                                    <span>
                                      {unit.name} ({unit.symbol})
                                    </span>
                                    <span className="text-xs text-slate-400 ml-2">
                                      {unit.code}
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </div>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription className="text-slate-400">
                      Base measurement unit for this product
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Selected Unit Display */}
              {selectedUnit && (
                <div className="flex items-center space-x-2 p-3 bg-gradient-to-r from-slate-700 to-slate-600 rounded-lg border border-slate-500">
                  <Info className="h-4 w-4 text-orange-400" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-white">
                      Selected: {selectedUnit.name} ({selectedUnit.symbol})
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge
                        variant="outline"
                        className="text-xs bg-orange-500 text-white border-orange-400"
                      >
                        {selectedUnit.category}
                      </Badge>
                      <span className="text-xs text-slate-300">
                        Code: {selectedUnit.code}
                      </span>
                      {selectedUnit.base_unit_id && (
                        <span className="text-xs text-slate-300">
                          Conversion: {selectedUnit.conversion_factor || 1}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-4 border-t border-slate-600 mt-6">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Cancel
                </Button>
              )}
              <Button
                type="submit"
                disabled={isLoading || !form.formState.isValid}
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white border-none"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {product ? 'Update Product' : 'Create Product'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
