'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { DocumentTemplateForm } from '@/components/forms/document-template-form/document-template-form'
import { useDocumentTemplates } from '@/hooks/use-document-templates'
import { useAuth } from '@/hooks/use-auth'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, CheckCircle } from 'lucide-react'
import type { DocumentTemplateInsert } from '@/types/document-template'

/**
 * New Document Template Page
 * Story 5.1: Document Template Management System
 * 
 * Page for creating new document templates with form validation
 * and admin-only access control.
 */
export default function NewTemplatePage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  
  const { createTemplate } = useDocumentTemplates({ autoRefresh: false })
  const { user } = useAuth()

  const handleSubmit = async (data: DocumentTemplateInsert) => {
    if (!user) {
      setSubmitError('User not authenticated')
      return
    }

    setIsSubmitting(true)
    setSubmitError(null)
    setSubmitSuccess(false)

    try {
      const result = await createTemplate(data, user.id)
      
      if (result.success) {
        setSubmitSuccess(true)
        // Redirect to the template list after a short delay
        setTimeout(() => {
          router.push('/documents/templates')
        }, 2000)
      } else {
        setSubmitError(result.error || 'Failed to create template')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create template'
      setSubmitError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.push('/documents/templates')
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Success message */}
      {submitSuccess && (
        <Alert className="mb-6 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertDescription className="text-green-800 dark:text-green-200">
            Template created successfully! Redirecting to templates list...
          </AlertDescription>
        </Alert>
      )}

      {/* Error message */}
      {submitError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      <DocumentTemplateForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isSubmitting}
        isEditing={false}
      />
    </div>
  )
}