import { createClient } from '@/lib/supabase/client'
import type { UserProfile, AuthError } from '@/lib/supabase/auth'
import type { UserActivationFormData } from '@/lib/validations/auth'

export interface UserManagementError {
  code: string
  message: string
  details?: any
  timestamp: Date
}

export class AdminUserService {
  private supabase = createClient()

  private mapError(error: any): UserManagementError {
    const timestamp = new Date()

    switch (error.code) {
      case 'PGRST301':
        return {
          code: 'USER_NOT_FOUND',
          message: 'User not found.',
          timestamp,
        }
      case 'PGRST204':
        return {
          code: 'NO_DATA_RETURNED',
          message: 'No data was returned from the operation.',
          timestamp,
        }
      case '23503':
        return {
          code: 'FOREIGN_KEY_VIOLATION',
          message: 'Cannot perform operation due to related data constraints.',
          timestamp,
        }
      case '23505':
        return {
          code: 'UNIQUE_VIOLATION',
          message: 'A user with this email already exists.',
          timestamp,
        }
      default:
        console.error('Unknown admin error:', error)
        return {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred. Please try again.',
          details: error,
          timestamp,
        }
    }
  }

  async activateUser(userId: string, reason?: string): Promise<UserProfile> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .update({
          is_active: true,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId)
        .select()
        .single()

      if (error) {
        throw this.mapError(error)
      }

      // Log the activation action
      await this.logUserAction(userId, 'ACTIVATED', reason)

      return data as UserProfile
    } catch (error) {
      if ((error as UserManagementError).code) {
        throw error
      }
      throw this.mapError(error)
    }
  }

  async deactivateUser(userId: string, reason?: string): Promise<UserProfile> {
    try {
      // First check if user exists and get current status
      const { data: existingUser, error: fetchError } = await this.supabase
        .from('profiles')
        .select('user_id, is_active, role')
        .eq('user_id', userId)
        .single()

      if (fetchError) {
        throw this.mapError(fetchError)
      }

      // Prevent deactivating the last admin
      if (existingUser.role === 'admin') {
        const { data: adminCount, error: countError } = await this.supabase
          .from('profiles')
          .select('user_id', { count: 'exact' })
          .eq('role', 'admin')
          .eq('is_active', true)

        if (countError) {
          throw this.mapError(countError)
        }

        if ((adminCount?.length || 0) <= 1) {
          throw {
            code: 'CANNOT_DEACTIVATE_LAST_ADMIN',
            message: 'Cannot deactivate the last active admin user.',
            timestamp: new Date(),
          } as UserManagementError
        }
      }

      const { data, error } = await this.supabase
        .from('profiles')
        .update({
          is_active: false,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId)
        .select()
        .single()

      if (error) {
        throw this.mapError(error)
      }

      // Log the deactivation action
      await this.logUserAction(userId, 'DEACTIVATED', reason)

      return data as UserProfile
    } catch (error) {
      if ((error as UserManagementError).code) {
        throw error
      }
      throw this.mapError(error)
    }
  }

  async toggleUserActivation(
    data: UserActivationFormData
  ): Promise<UserProfile> {
    if (data.isActive) {
      return this.activateUser(data.userId, data.reason)
    } else {
      return this.deactivateUser(data.userId, data.reason)
    }
  }

  async deleteUser(userId: string, reason?: string): Promise<void> {
    try {
      // First check if user exists and get current status
      const { data: existingUser, error: fetchError } = await this.supabase
        .from('profiles')
        .select('user_id, role')
        .eq('user_id', userId)
        .single()

      if (fetchError) {
        throw this.mapError(fetchError)
      }

      // Prevent deleting the last admin
      if (existingUser.role === 'admin') {
        const { data: adminCount, error: countError } = await this.supabase
          .from('profiles')
          .select('user_id', { count: 'exact' })
          .eq('role', 'admin')
          .eq('is_active', true)

        if (countError) {
          throw this.mapError(countError)
        }

        if ((adminCount?.length || 0) <= 1) {
          throw {
            code: 'CANNOT_DELETE_LAST_ADMIN',
            message: 'Cannot delete the last admin user.',
            timestamp: new Date(),
          } as UserManagementError
        }
      }

      // Log the deletion action before deleting
      await this.logUserAction(userId, 'DELETED', reason)

      // Delete from profiles table (this will cascade to auth.users via RLS/triggers)
      const { error: deleteError } = await this.supabase
        .from('profiles')
        .delete()
        .eq('user_id', userId)

      if (deleteError) {
        throw this.mapError(deleteError)
      }
    } catch (error) {
      if ((error as UserManagementError).code) {
        throw error
      }
      throw this.mapError(error)
    }
  }

  private async logUserAction(
    userId: string,
    action: string,
    reason?: string
  ): Promise<void> {
    try {
      // Get current user for logging
      const {
        data: { user },
        error: userError,
      } = await this.supabase.auth.getUser()
      if (userError || !user) {
        console.warn('Could not get current user for logging')
        return
      }

      // Log the action (you might want to create an audit_log table for this)
      console.log(`User ${action}:`, {
        targetUserId: userId,
        actionBy: user.id,
        action,
        reason,
        timestamp: new Date().toISOString(),
      })

      // If you have an audit_log table, insert the record here
      // await this.supabase.from('audit_log').insert({
      //   target_user_id: userId,
      //   action_by: user.id,
      //   action,
      //   reason,
      //   timestamp: new Date().toISOString(),
      // })
    } catch (error) {
      // Log but don't throw - audit logging shouldn't break the main operation
      console.error('Failed to log user action:', error)
    }
  }

  async getUserById(userId: string): Promise<UserProfile> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select(
          `
          *,
          companies:company_id (
            id,
            name,
            company_type
          )
        `
        )
        .eq('user_id', userId)
        .single()

      if (error) {
        throw this.mapError(error)
      }

      return data as UserProfile
    } catch (error) {
      if ((error as UserManagementError).code) {
        throw error
      }
      throw this.mapError(error)
    }
  }

  async getUsersByStatus(isActive: boolean): Promise<UserProfile[]> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select(
          `
          *,
          companies:company_id (
            id,
            name,
            company_type
          )
        `
        )
        .eq('is_active', isActive)
        .order('created_at', { ascending: false })

      if (error) {
        throw this.mapError(error)
      }

      return data as UserProfile[]
    } catch (error) {
      if ((error as UserManagementError).code) {
        throw error
      }
      throw this.mapError(error)
    }
  }

  async searchUsers(query: string): Promise<UserProfile[]> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select(
          `
          *,
          companies:company_id (
            id,
            name,
            company_type
          )
        `
        )
        .or(
          `email.ilike.%${query}%,first_name.ilike.%${query}%,last_name.ilike.%${query}%`
        )
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) {
        throw this.mapError(error)
      }

      return data as UserProfile[]
    } catch (error) {
      if ((error as UserManagementError).code) {
        throw error
      }
      throw this.mapError(error)
    }
  }
}

// Export singleton instance
export const adminUserService = new AdminUserService()
