import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/lib/supabase/types'
import type {
  DocumentGenerationRequest,
  DocumentGenerationResult,
  BulkDocumentGenerationRequest,
  BulkGenerationResult,
  ShipmentDataForGeneration,
  GeneratedDocument,
  PlaceholderProcessingResult,
  FileStorageResult,
  GenerationProgress,
  GenerationStatus
} from '@/types/document-generation'
import type { DocumentTemplate } from '@/types/document-template'
import { PDFGenerationService } from './pdf-generation-service'

/**
 * Document Generation Service
 * Story 5.2: Automated Document Generation Engine
 * 
 * Handles automated document generation from templates and shipment data
 */
export class DocumentGenerationService {
  private pdfService: PDFGenerationService

  constructor(private supabase: SupabaseClient<Database>) {
    this.pdfService = new PDFGenerationService()
  }

  /**
   * Generate a single document from template and shipment data
   */
  async generateDocument(request: DocumentGenerationRequest): Promise<DocumentGenerationResult> {
    const startTime = Date.now()
    
    try {
      // 1. Fetch shipment data with all relationships
      const shipmentData = await this.getFullShipmentData(request.shipmentId)
      if (!shipmentData) {
        throw new Error(`Shipment not found: ${request.shipmentId}`)
      }

      // 2. Fetch template
      const template = await this.getTemplate(request.templateId)
      if (!template) {
        throw new Error(`Template not found: ${request.templateId}`)
      }

      // 3. Process template placeholders
      const placeholderResult = await this.processPlaceholders(
        template,
        shipmentData,
        request.options?.additionalData
      )

      if (placeholderResult.errors.length > 0) {
        throw new Error(`Template processing errors: ${placeholderResult.errors.join(', ')}`)
      }

      // 4. Generate PDF
      const pdfBuffer = await this.generatePDF(placeholderResult.content, template)

      // 5. Store file in Supabase Storage
      const fileResult = await this.storeDocument(
        pdfBuffer,
        shipmentData,
        template,
        request.options
      )

      // 6. Create document record
      const document = await this.createDocumentRecord(
        shipmentData,
        template,
        fileResult,
        request.options
      )

      const processingTime = Date.now() - startTime

      return {
        success: true,
        document,
        templateId: request.templateId,
        processingTime
      }

    } catch (error) {
      const processingTime = Date.now() - startTime
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        templateId: request.templateId,
        processingTime
      }
    }
  }

  /**
   * Generate multiple documents in bulk
   */
  async bulkGenerateDocuments(request: BulkDocumentGenerationRequest): Promise<BulkGenerationResult> {
    const startTime = Date.now()
    const results: DocumentGenerationResult[] = []
    const { templateIds, onProgress } = request

    for (let i = 0; i < templateIds.length; i++) {
      try {
        const singleRequest: DocumentGenerationRequest = {
          shipmentId: request.shipmentId,
          templateId: templateIds[i],
          options: request.options
        }

        const result = await this.generateDocument(singleRequest)
        results.push(result)

        // Report progress
        if (onProgress) {
          const progress = ((i + 1) / templateIds.length) * 100
          onProgress(progress)
        }

      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error occurred',
          templateId: templateIds[i],
          processingTime: 0
        })
      }
    }

    const totalProcessingTime = Date.now() - startTime
    const successful = results.filter(r => r.success).length
    const failed = results.length - successful

    return {
      results,
      summary: {
        total: results.length,
        successful,
        failed,
        totalProcessingTime
      }
    }
  }

  /**
   * Get full shipment data with all relationships for template population
   * @param shipmentId - Can be either shipment UUID or shipment number
   */
  private async getFullShipmentData(shipmentId: string): Promise<ShipmentDataForGeneration | null> {
    // First, try to find by UUID (if it looks like a UUID)
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(shipmentId)
    
    const { data: shipment, error } = await this.supabase
      .from('shipments')
      .select(`
        *,
        customer:companies!shipments_customer_id_fkey(
          id,
          name,
          company_type,
          address,
          metadata,
          contact_phone,
          contact_fax,
          contact_email,
          contact_person_first_name,
          contact_person_last_name,
          tax_id
        ),
        shipper:companies!shipments_shipper_id_fkey(
          id,
          name,
          company_type,
          address,
          metadata,
          contact_phone,
          contact_fax,
          contact_email,
          contact_person_first_name,
          contact_person_last_name,
          tax_id
        ),
        consignee:companies!shipments_consignee_id_fkey(
          id,
          name,
          company_type,
          address,
          metadata,
          contact_phone,
          contact_fax,
          contact_email,
          contact_person_first_name,
          contact_person_last_name,
          tax_id
        ),
        notify_party:companies!shipments_notify_party_id_fkey(
          id,
          name,
          company_type,
          address,
          metadata,
          contact_phone,
          contact_fax,
          contact_email,
          contact_person_first_name,
          contact_person_last_name,
          tax_id
        ),
        factory:companies!shipments_factory_id_fkey(
          id,
          name,
          company_type,
          address,
          metadata,
          contact_phone,
          contact_fax,
          contact_email,
          contact_person_first_name,
          contact_person_last_name,
          tax_id
        ),
        forwarder_agent:companies!shipments_forwarder_agent_id_fkey(
          id,
          name,
          company_type,
          address,
          metadata,
          contact_phone,
          contact_fax,
          contact_email,
          contact_person_first_name,
          contact_person_last_name,
          tax_id
        ),
        origin_port:ports!shipments_origin_port_id_fkey(
          id,
          code,
          name,
          city,
          country
        ),
        destination_port:ports!shipments_destination_port_id_fkey(
          id,
          code,
          name,
          city,
          country
        ),
        shipment_products(
          id,
          product_id,
          product_description,
          quantity,
          unit_of_measure_id,
          unit_price_cif,
          unit_price_fob,
          total_value_cif,
          total_value_fob,
          gross_weight,
          net_weight,
          total_gross_weight,
          total_net_weight,
          shipping_mark,
          mfg_date,
          expire_date,
          lot_number,
          packaging_type,
          quality_grade,
          unit_of_measure:units_of_measure(
            id,
            code,
            name
          )
        ),
        containers(
          id,
          container_number,
          container_type,
          container_size,
          seal_number,
          tare_weight,
          gross_weight,
          volume,
          temperature,
          vent,
          status
        )
      `)
      .eq(isUUID ? 'id' : 'shipment_number', shipmentId)
      .single()

    if (error) {
      console.error('Error fetching shipment data:', error)
      return null
    }

    return {
      id: shipment.id, // Include the shipment UUID
      shipment_number: shipment.shipment_number,
      invoice_number: shipment.invoice_number || undefined,
      status: shipment.status || 'unknown',
      transportation_mode: shipment.transportation_mode || 'sea',
      liner: shipment.liner || undefined,
      vessel_name: shipment.vessel_name || undefined,
      voyage_number: shipment.voyage_number || undefined,
      booking_number: shipment.booking_number || undefined,
      etd_date: shipment.etd_date || undefined,
      eta_date: shipment.eta_date || undefined,
      closing_time: shipment.closing_time || undefined,
      cy_date: shipment.cy_date || undefined,
      origin_port: shipment.origin_port || undefined,
      destination_port: shipment.destination_port || undefined,
      customer: this.processCompanyData(shipment.customer),
      shipper: this.processCompanyData(shipment.shipper),
      consignee: this.processCompanyData(shipment.consignee),
      notify_party: this.processCompanyData(shipment.notify_party),
      factory: this.processCompanyData(shipment.factory),
      forwarder_agent: this.processCompanyData(shipment.forwarder_agent),
      products: shipment.shipment_products || [],
      containers: shipment.containers || [],
      total_weight: shipment.total_weight || undefined,
      total_volume: shipment.total_volume || undefined,
      total_value_cif: shipment.total_value_cif || undefined,
      total_value_fob: shipment.total_value_fob || undefined,
      total_quantity: shipment.total_quantity || undefined,
      total_gross_weight: shipment.total_gross_weight || undefined,
      total_net_weight: shipment.total_net_weight || undefined,
      currency_code: shipment.currency_code || 'USD',
      number_of_pallet: shipment.number_of_pallet || undefined,
      pallet_description: shipment.pallet_description || undefined,
      ephyto_refno: shipment.ephyto_refno || undefined,
      notes: shipment.notes || undefined,
      created_by: shipment.created_by || undefined,
      created_at: shipment.created_at || new Date().toISOString(),
      updated_at: shipment.updated_at || new Date().toISOString()
    }
  }

  /**
   * Process company data to extract and format address and metadata information
   */
  private processCompanyData(company: any): any {
    if (!company) return undefined

    // Parse the JSON address if it exists
    let addressData: any = {}
    if (company.address) {
      try {
        // If address is already parsed JSON object, use it directly
        if (typeof company.address === 'object') {
          addressData = company.address
        } else if (typeof company.address === 'string') {
          // If address is JSON string, parse it
          addressData = JSON.parse(company.address)
        }
      } catch (error) {
        console.warn('Failed to parse address JSON:', error)
        // Fallback: treat as plain string
        addressData = { full: company.address }
      }
    }

    // Parse the JSON metadata if it exists
    let metadataData: any = {}
    if (company.metadata) {
      try {
        // If metadata is already parsed JSON object, use it directly
        if (typeof company.metadata === 'object') {
          metadataData = company.metadata
        } else if (typeof company.metadata === 'string') {
          // If metadata is JSON string, parse it
          metadataData = JSON.parse(company.metadata)
        }
      } catch (error) {
        console.warn('Failed to parse metadata JSON:', error)
        // Fallback: treat as plain string
        metadataData = { raw: company.metadata }
      }
    }

    // Format individual address components
    const address = {
      // Raw JSON object for complex access
      raw: addressData,
      
      // Individual components (English as default)
      street: addressData.street?.en || addressData.street || '',
      city: addressData.city?.en || addressData.city || '',
      district: addressData.district?.en || addressData.district || '',
      province: addressData.province?.en || addressData.province || '',
      country: addressData.country?.en || addressData.country || '',
      postal_code: addressData.postal_code?.en || addressData.postal_code || '',
      
      // Formatted combined addresses
      full_en: this.formatFullAddress(addressData, 'en'),
      full_th: this.formatFullAddress(addressData, 'th'),
      full: this.formatFullAddress(addressData, 'en'), // Default to English
      
      // Line-by-line formatting for invoices
      line1: addressData.street?.en || addressData.street || '',
      line2: [
        addressData.district?.en || addressData.district,
        addressData.province?.en || addressData.province
      ].filter(Boolean).join(', '),
      line2_2: [
        addressData.city?.en || addressData.city,
        addressData.district?.en || addressData.district,
        addressData.province?.en || addressData.province,
        addressData.country?.en || addressData.country
      ].filter(Boolean).join(', '),
      line3: [
        addressData.country?.en || addressData.country,
        addressData.postal_code?.en || addressData.postal_code
      ].filter(Boolean).join(' ')
    }

    // Format metadata information
    const metadata = {
      // Raw JSON object for complex access
      raw: metadataData,
      
      // Common metadata fields
      usci: metadataData.usci || metadataData.USCI || '',
      usci_code: metadataData.usci || metadataData.USCI || metadataData.usci_code || '',
      business_license: metadataData.business_license || metadataData.license || '',
      tax_registration: metadataData.tax_registration || metadataData.tax_reg || '',
      registration_number: metadataData.registration_number || metadataData.reg_no || '',
      
      // For backward compatibility and custom fields
      ...metadataData
    }

    return {
      ...company,
      address,
      metadata,
      // Keep original for backward compatibility
      address_raw: company.address,
      metadata_raw: company.metadata
    }
  }

  /**
   * Format full address based on language
   */
  private formatFullAddress(addressData: any, language: 'en' | 'th' = 'en'): string {
    if (!addressData || typeof addressData !== 'object') return ''

    const components = [
      addressData.street?.[language] || addressData.street,
      addressData.district?.[language] || addressData.district,
      addressData.province?.[language] || addressData.province,
      addressData.country?.[language] || addressData.country,
      addressData.postal_code?.[language] || addressData.postal_code
    ].filter(Boolean)

    return components.join(', ')
  }

  /**
   * Get document template by ID
   */
  private async getTemplate(templateId: string): Promise<DocumentTemplate | null> {
    const { data: template, error } = await this.supabase
      .from('document_templates')
      .select('*')
      .eq('id', templateId)
      .eq('is_active', true)
      .single()

    if (error) {
      console.error('Error fetching template:', error)
      return null
    }

    return template
  }

  /**
   * Process template placeholders with shipment data
   */
  private async processPlaceholders(
    template: DocumentTemplate,
    shipmentData: ShipmentDataForGeneration,
    additionalData?: Record<string, any>
  ): Promise<PlaceholderProcessingResult> {
    let content = template.template_content
    const processedFields: string[] = []
    const missingFields: string[] = []
    const errors: string[] = []

    try {
      // Combine all data sources
      const allData = {
        shipment: shipmentData,
        ...additionalData
      }

      // First, process loop structures ({{#each}} blocks)
      content = this.processLoops(content, allData, processedFields, errors)
      
      // Then apply formatting (this handles both formatted and regular placeholders)
      content = this.applyFormatting(content, template, allData, processedFields, missingFields, errors)

      return {
        content,
        processedFields,
        missingFields,
        errors
      }

    } catch (error) {
      return {
        content: template.template_content,
        processedFields: [],
        missingFields: [],
        errors: [error instanceof Error ? error.message : 'Unknown processing error']
      }
    }
  }

  /**
   * Process loop structures in template content
   */
  private processLoops(content: string, data: any, processedFields: string[], errors: string[]): string {
    // Process {{#each}} loops for arrays like products
    const loopRegex = /\{\{#each\s+([^}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g
    let result = content
    let match

    while ((match = loopRegex.exec(content)) !== null) {
      const fullMatch = match[0]
      const arrayPath = match[1].trim()
      const loopContent = match[2]
      
      try {
        // Get the array data
        const arrayData = this.getNestedValue(data, arrayPath)
        
        if (Array.isArray(arrayData)) {
          let expandedContent = ''
          
          // Process each item in the array
          arrayData.forEach((item, index) => {
            let itemContent = loopContent
            
            // Replace {{@index}} with current index
            itemContent = itemContent.replace(/\{\{@index\}\}/g, String(index))
            
            // Replace {{@index1}} with current index + 1 (for human-readable numbering)
            itemContent = itemContent.replace(/\{\{@index1\}\}/g, String(index + 1))
            
            // Process all {{this.field}} patterns (including formatted ones)
            itemContent = this.processLoopItemPlaceholders(itemContent, item)
            
            expandedContent += itemContent
          })
          
          // Replace the entire loop block with expanded content
          result = result.replace(fullMatch, expandedContent)
          processedFields.push(arrayPath)
          
        } else {
          // Array not found or not an array, replace with empty content
          result = result.replace(fullMatch, '')
          errors.push(`Loop array not found or invalid: ${arrayPath}`)
        }
        
      } catch (error) {
        errors.push(`Error processing loop ${arrayPath}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        result = result.replace(fullMatch, '')
      }
    }
    
    return result
  }

  /**
   * Process placeholders within loop items ({{this.field}} patterns)
   */
  private processLoopItemPlaceholders(content: string, item: any): string {
    // Regular expression to match {{this.field}} patterns (with or without formatting)
    const thisRegex = /\{\{this\.([^}]+)\}\}/g
    let result = content
    let match

    while ((match = thisRegex.exec(content)) !== null) {
      const fullPlaceholder = match[0]
      const fieldExpression = match[1].trim()

      try {
        // Process the field expression (might include formatting)
        let replacement = this.processPlaceholder(fieldExpression, item)

        if (replacement !== null) {
          result = result.replace(fullPlaceholder, replacement)
        } else {
          result = result.replace(fullPlaceholder, '')
        }
      } catch (error) {
        console.warn(`Error processing loop placeholder ${fullPlaceholder}:`, error)
        result = result.replace(fullPlaceholder, `[ERROR: this.${fieldExpression}]`)
      }
    }

    return result
  }

  /**
   * Get nested value using dot notation path
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  }

  /**
   * Apply template-specific formatting and process all placeholders
   */
  private applyFormatting(
    content: string, 
    template: DocumentTemplate, 
    allData: any, 
    processedFields: string[], 
    missingFields: string[], 
    errors: string[]
  ): string {
    let formattedContent = content

    // Process all placeholders (both formatted and regular)
    formattedContent = this.processAllPlaceholders(formattedContent, allData, processedFields, missingFields, errors)

    return formattedContent
  }

  /**
   * Process all placeholders including formatted ones
   */
  private processAllPlaceholders(
    content: string,
    allData: any,
    processedFields: string[],
    missingFields: string[],
    errors: string[]
  ): string {
    // Regular expression to match all placeholder patterns
    const placeholderRegex = /\{\{([^}]+)\}\}/g
    let result = content
    let match

    // Process each placeholder
    while ((match = placeholderRegex.exec(content)) !== null) {
      const fullPlaceholder = match[0]
      const placeholderContent = match[1].trim()

      try {
        let replacement = this.processPlaceholder(placeholderContent, allData)

        if (replacement !== null) {
          result = result.replace(fullPlaceholder, replacement)
          processedFields.push(placeholderContent)
        } else {
          missingFields.push(placeholderContent)
          result = result.replace(fullPlaceholder, `[${placeholderContent}]`)
        }
      } catch (error) {
        errors.push(`Error processing ${placeholderContent}: ${error}`)
        result = result.replace(fullPlaceholder, `[ERROR: ${placeholderContent}]`)
      }
    }

    return result
  }

  /**
   * Process a single placeholder with or without formatting
   */
  private processPlaceholder(placeholderContent: string, allData: any): string | null {
    // Check if this is a formatted placeholder
    if (placeholderContent.includes('|')) {
      return this.processFormattedPlaceholder(placeholderContent, allData)
    } else {
      // Regular placeholder - just get the value
      const value = this.getNestedValue(allData, placeholderContent)
      return value !== undefined && value !== null ? String(value) : null
    }
  }

  /**
   * Process formatted placeholders like {{field|format:"pattern"}}
   */
  private processFormattedPlaceholder(placeholderContent: string, allData: any): string | null {
    const parts = placeholderContent.split('|')
    const fieldPath = parts[0].trim()
    const formatPart = parts[1].trim()

    // Get the raw value
    const rawValue = this.getNestedValue(allData, fieldPath)
    if (rawValue === undefined || rawValue === null) {
      return null
    }

    // Parse format directive
    if (formatPart.startsWith('number:')) {
      const format = formatPart.replace('number:', '').replace(/"/g, '')
      const num = parseFloat(rawValue)
      return isNaN(num) ? String(rawValue) : this.formatNumber(num, format)
    }

    if (formatPart.startsWith('date:')) {
      const format = formatPart.replace('date:', '').replace(/"/g, '')
      const date = new Date(rawValue)
      return isNaN(date.getTime()) ? String(rawValue) : this.formatDate(date, format)
    }

    if (formatPart.startsWith('currency:')) {
      const currencyCode = formatPart.replace('currency:', '').replace(/"/g, '')
      const num = parseFloat(rawValue)
      return isNaN(num) ? String(rawValue) : this.formatCurrency(num, currencyCode)
    }

    if (formatPart === 'upper') {
      return String(rawValue).toUpperCase()
    }

    if (formatPart === 'lower') {
      return String(rawValue).toLowerCase()
    }

    if (formatPart === 'title') {
      return String(rawValue).replace(/\w\S*/g, (txt) => 
        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
      )
    }

    // Unknown format, return raw value
    return String(rawValue)
  }


  /**
   * Format number based on pattern
   */
  private formatNumber(num: number, format: string): string {
    try {
      if (format === 'integer' || format === '0') {
        return Math.round(num).toLocaleString('en-US')
      }
      
      if (format.includes('.')) {
        const decimals = format.split('.')[1].length
        return num.toLocaleString('en-US', {
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals
        })
      }
      
      // Default: 2 decimal places with thousand separators
      return num.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    } catch (error) {
      return num.toString()
    }
  }

  /**
   * Format date based on pattern
   */
  private formatDate(date: Date, format: string): string {
    try {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      // Support common date formats
      const formats: { [key: string]: string } = {
        'DD/MM/YYYY': `${day}/${month}/${year}`,
        'MM/DD/YYYY': `${month}/${day}/${year}`,
        'YYYY-MM-DD': `${year}-${month}-${day}`,
        'DD-MM-YYYY': `${day}-${month}-${year}`,
        'DD MMM YYYY': `${day} ${this.getMonthName(date.getMonth())} ${year}`,
        'MMM DD, YYYY': `${this.getMonthName(date.getMonth())} ${day}, ${year}`,
        'YYYY/MM/DD': `${year}/${month}/${day}`
      }
      
      return formats[format] || date.toLocaleDateString('en-US')
    } catch (error) {
      return date.toLocaleDateString('en-US')
    }
  }

  /**
   * Format currency based on currency code
   */
  private formatCurrency(amount: number, currencyCode: string): string {
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode.toUpperCase(),
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    } catch (error) {
      // Fallback for unsupported currency codes
      return `${currencyCode} ${amount.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })}`
    }
  }

  /**
   * Get month name by index
   */
  private getMonthName(monthIndex: number): string {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ]
    return months[monthIndex] || 'Jan'
  }

  /**
   * Generate PDF from processed content using PDF generation service
   */
  private async generatePDF(content: string, template: DocumentTemplate): Promise<Buffer> {
    try {
      // Validate PDF generation requirements
      const validation = this.pdfService.validateRequirements(template)
      if (!validation.isValid) {
        throw new Error(`PDF generation validation failed: ${validation.errors.join(', ')}`)
      }

      // Generate PDF using the PDF service
      const pdfBuffer = await this.pdfService.generatePDF(content, template)
      return pdfBuffer
    } catch (error) {
      console.error('PDF generation failed:', error)
      throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Store document file in Supabase Storage
   */
  private async storeDocument(
    pdfBuffer: Buffer,
    shipmentData: ShipmentDataForGeneration,
    template: DocumentTemplate,
    options?: any
  ): Promise<FileStorageResult> {
    const fileName = options?.customFileName || 
      `${template.document_type}-${shipmentData.shipment_number}-v1.pdf`
    
    const filePath = `documents/${shipmentData.shipment_number}/${template.document_type}/${fileName}`

    const { data, error } = await this.supabase.storage
      .from('documents')
      .upload(filePath, pdfBuffer, {
        contentType: 'application/pdf',
        upsert: false
      })

    if (error) {
      throw new Error(`Failed to store document: ${error.message}`)
    }

    // Get public URL
    const { data: { publicUrl } } = this.supabase.storage
      .from('documents')
      .getPublicUrl(filePath)

    return {
      filePath,
      fileName,
      fileSize: pdfBuffer.length,
      fileHash: '', // Calculate hash if needed
      publicUrl
    }
  }

  /**
   * Create document record in database
   */
  private async createDocumentRecord(
    shipmentData: ShipmentDataForGeneration,
    template: DocumentTemplate,
    fileResult: FileStorageResult,
    options?: any
  ): Promise<GeneratedDocument> {
    const { data: user } = await this.supabase.auth.getUser()
    if (!user.user) {
      throw new Error('User not authenticated')
    }

    const documentData = {
      shipment_id: shipmentData.id, // Use the actual shipment UUID from shipmentData
      document_type: template.document_type,
      document_name: template.template_name,
      document_number: options?.documentNumber,
      file_path: fileResult.filePath,
      file_name: fileResult.fileName,
      file_size_bytes: fileResult.fileSize,
      file_type: 'application/pdf',
      file_hash: fileResult.fileHash,
      description: `Generated from template: ${template.template_name}`,
      version: 1,
      is_original: true,
      language: options?.language || template.language,
      is_public: false,
      access_level: 'shipment' as const,
      shared_with_customer: false,
      shared_with_carrier: false,
      is_verified: false,
      uploaded_by: user.user.id,
      upload_source: 'automated_generation'
    }

    const { data: document, error } = await this.supabase
      .from('documents')
      .insert(documentData)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create document record: ${error.message}`)
    }

    return document as GeneratedDocument
  }
}