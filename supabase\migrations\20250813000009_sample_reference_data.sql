-- Sample Reference Data
-- Story 1.2: Database Schema & RLS Foundation
-- This migration loads sample reference data for testing and application support

-- ============================================================================
-- UNITS OF MEASURE SEED DATA
-- ============================================================================

INSERT INTO units_of_measure (code, name, category, symbol, conversion_factor) VALUES
-- Weight units (base unit: KG) - KG is the standard UOM for all pricing and weight calculations
('KG', 'Kilogram', 'weight', 'kg', 1.0000),
('TON', 'Metric Ton', 'weight', 't', 1000.0000),
('LBS', 'Pounds', 'weight', 'lbs', 0.4536),

-- Count units (base unit: PCS)
('PCS', 'Pieces', 'count', 'pcs', 1.0000),
('BOX', 'Box', 'count', 'box', 1.0000),
('CTN', 'Carton', 'count', 'ctn', 1.0000),
('PALLET', 'Pallet', 'count', 'plt', 1.0000),
('BAG', 'Bag', 'count', 'bag', 1.0000),
('PLASTIC_BASKET', 'Plastic Basket', 'count', 'plsbkt', 1.0000),

-- Volume units (base unit: LTR)
('LTR', 'Liter', 'volume', 'L', 1.0000),
('CBM', 'Cubic Meter', 'volume', 'm³', 1000.0000),
('GAL', 'Gallon', 'volume', 'gal', 3.7854),

-- Length units (base unit: MTR)
('MTR', 'Meter', 'length', 'm', 1.0000),
('CM', 'Centimeter', 'length', 'cm', 0.0100),
('INCH', 'Inch', 'length', 'in', 0.0254);

-- Update base_unit_id for conversion relationships
UPDATE units_of_measure SET base_unit_id = (SELECT id FROM units_of_measure WHERE code = 'KG') WHERE category = 'weight' AND code != 'KG';
UPDATE units_of_measure SET base_unit_id = (SELECT id FROM units_of_measure WHERE code = 'PCS') WHERE category = 'count' AND code != 'PCS';
UPDATE units_of_measure SET base_unit_id = (SELECT id FROM units_of_measure WHERE code = 'LTR') WHERE category = 'volume' AND code != 'LTR';
UPDATE units_of_measure SET base_unit_id = (SELECT id FROM units_of_measure WHERE code = 'MTR') WHERE category = 'length' AND code != 'MTR';

-- ============================================================================
-- PORTS SEED DATA (Major Asian Trade Ports)
-- ============================================================================

INSERT INTO ports (code, name, city, country, country_code, port_type, latitude, longitude, timezone) VALUES
-- China Ports
('CNSHA', 'Port of Shanghai', 'Shanghai', 'China', 'CN', 'origin', 31.230416, 121.473701, 'Asia/Shanghai'),
('CNNIN', 'Port of Ningbo', 'Ningbo', 'China', 'CN', 'origin', 29.865801, 121.643204, 'Asia/Shanghai'),
('CNSZX', 'Port of Shenzhen', 'Shenzhen', 'China', 'CN', 'origin', 22.543096, 114.057865, 'Asia/Shanghai'),
('CNQIN', 'Port of Qingdao', 'Qingdao', 'China', 'CN', 'origin', 36.067108, 120.382607, 'Asia/Shanghai'),
('CNTIE', 'Port of Tianjin', 'Tianjin', 'China', 'CN', 'origin', 39.142500, 117.177497, 'Asia/Shanghai'),

-- Thailand Ports
('THBKK', 'Port of Bangkok (Klong Toei)', 'Bangkok', 'Thailand', 'TH', 'destination', 13.736717, 100.523186, 'Asia/Bangkok'),
('THLCH', 'Laem Chabang Port', 'Chonburi', 'Thailand', 'TH', 'destination', 13.081721, 100.883043, 'Asia/Bangkok'),
('THMAP', 'Map Ta Phut Port', 'Rayong', 'Thailand', 'TH', 'destination', 12.823750, 101.184444, 'Asia/Bangkok'),

-- Vietnam Ports
('VNSGN', 'Port of Ho Chi Minh City', 'Ho Chi Minh City', 'Vietnam', 'VN', 'destination', 10.762622, 106.660172, 'Asia/Ho_Chi_Minh'),
('VNHAI', 'Port of Haiphong', 'Haiphong', 'Vietnam', 'VN', 'destination', 20.844721, 106.682526, 'Asia/Ho_Chi_Minh'),

-- Singapore Port
('SGSIN', 'Port of Singapore', 'Singapore', 'Singapore', 'SG', 'transit', 1.290270, 103.851959, 'Asia/Singapore'),

-- Malaysia Ports
('MYPKG', 'Port Klang', 'Klang', 'Malaysia', 'MY', 'destination', 3.004166, 101.394444, 'Asia/Kuala_Lumpur'),
('MYTPP', 'Tanjung Pelepas Port', 'Johor', 'Malaysia', 'MY', 'destination', 1.363889, 103.548611, 'Asia/Kuala_Lumpur'),

-- Indonesia Ports
('IDJKT', 'Port of Jakarta (Tanjung Priok)', 'Jakarta', 'Indonesia', 'ID', 'destination', -6.107500, 106.880556, 'Asia/Jakarta'),
('IDSRB', 'Port of Surabaya', 'Surabaya', 'Indonesia', 'ID', 'destination', -7.200000, 112.733333, 'Asia/Jakarta'),

-- Philippines Ports
('PHMNL', 'Port of Manila', 'Manila', 'Philippines', 'PH', 'destination', 14.583333, 120.966667, 'Asia/Manila'),

-- Japan Ports
('JPTYO', 'Port of Tokyo', 'Tokyo', 'Japan', 'JP', 'destination', 35.654391, 139.775556, 'Asia/Tokyo'),
('JPYOK', 'Port of Yokohama', 'Yokohama', 'Japan', 'JP', 'destination', 35.444167, 139.636944, 'Asia/Tokyo'),

-- South Korea Ports
('KRPUS', 'Port of Busan', 'Busan', 'South Korea', 'KR', 'destination', 35.104722, 129.041944, 'Asia/Seoul');

-- ============================================================================
-- PRODUCTS SEED DATA
-- ============================================================================

-- Get the KG unit ID for references
DO $$
DECLARE
    kg_unit_id UUID;
    pcs_unit_id UUID;
    mt_unit_id UUID;
BEGIN
    SELECT id INTO kg_unit_id FROM units_of_measure WHERE symbol = 'KG';
    SELECT id INTO pcs_unit_id FROM units_of_measure WHERE symbol = 'PCS';
    SELECT id INTO mt_unit_id FROM units_of_measure WHERE symbol = 'MT';

    -- Agricultural Products
    INSERT INTO products (name, hs_code, category, description, unit_of_measure_id) VALUES
    ('Tapioca Starch', '11081400', 'Agricultural', 'Modified and unmodified tapioca starch for food and industrial use', kg_unit_id),
    ('Jasmine Rice', '10063020', 'Agricultural', 'Thai Jasmine rice, premium grade', kg_unit_id),
    ('White Sugar', '17019990', 'Agricultural', 'Refined white sugar for food industry', kg_unit_id),
    ('Dried Cassava Chips', '07149000', 'Agricultural', 'Dried cassava chips for animal feed', kg_unit_id),
    ('Coconut Oil', '15131100', 'Agricultural', 'Virgin coconut oil, food grade', kg_unit_id),
    
    -- Rubber Products
    ('Natural Rubber (STR20)', '40011000', 'Rubber', 'Standard Thai Rubber Grade 20', kg_unit_id),
    ('Latex Gloves', '40151100', 'Rubber', 'Medical grade latex examination gloves', pcs_unit_id),
    ('Rubber Sheets', '40029100', 'Rubber', 'Natural rubber sheets for manufacturing', kg_unit_id),
    
    -- Chemicals
    ('Sodium Hydroxide', '28151100', 'Chemical', 'Caustic soda flakes, industrial grade', kg_unit_id),
    ('Titanium Dioxide', '28230000', 'Chemical', 'TiO2 pigment for paint and plastics', kg_unit_id),
    ('Polyethylene Resin', '39011000', 'Chemical', 'LDPE resin granules', kg_unit_id),
    
    -- Textiles
    ('Cotton Yarn', '52051300', 'Textile', 'Combed cotton yarn for weaving', kg_unit_id),
    ('Polyester Fabric', '54076900', 'Textile', 'Woven polyester fabric', kg_unit_id),
    
    -- Electronics Components
    ('LED Chips', '85414000', 'Electronics', 'High-brightness LED semiconductor chips', pcs_unit_id),
    ('Printed Circuit Boards', '85340000', 'Electronics', 'Multi-layer PCBs for electronics', pcs_unit_id),
    
    -- Machinery Parts
    ('Steel Bearings', '84822000', 'Machinery', 'Precision ball bearings', pcs_unit_id),
    ('Hydraulic Pumps', '84135000', 'Machinery', 'Industrial hydraulic pumps', pcs_unit_id),
    
    -- Food Products
    ('Canned Pineapple', '20084000', 'Food', 'Pineapple slices in natural juice', kg_unit_id),
    ('Fish Sauce', '21039000', 'Food', 'Traditional Thai fish sauce', kg_unit_id),
    ('Instant Noodles', '19023000', 'Food', 'Instant ramen noodles', kg_unit_id);
END $$;

-- ============================================================================
-- SAMPLE COMPANIES DATA
-- ============================================================================

-- Sample Customer Companies
INSERT INTO companies (name, company_type, tax_id, contact_email, contact_phone, address) VALUES
-- Customers
('Global Trading Co., Ltd.', 'customer', 'TH12345678901', '<EMAIL>', '+66-2-123-4567', 
 '{"street": {"en": "123 Silom Road"}, "province": {"en": "Bangkok"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.736717, "lng": 100.523186}}'),
('Asia Import Export Corp.', 'customer', 'TH23456789012', '<EMAIL>', '+66-2-234-5678',
 '{"street": {"en": "456 Sukhumvit Road"}, "province": {"en": "Bangkok"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.736717, "lng": 100.523186}}'),
('Pacific Commodities Ltd.', 'customer', 'SG12345678A', '<EMAIL>', '+65-6123-4567',
 '{"street": {"en": "789 Marina Bay"}, "province": {"en": "Singapore"}, "country": {"en": "Singapore"}, "coordinates": {"lat": 1.290270, "lng": 103.851959}}'),

-- Carriers
('Swift Logistics Thailand', 'carrier', 'TH34567890123', '<EMAIL>', '+66-2-345-6789',
 '{"street": {"en": "321 Ramkhamhaeng Road"}, "province": {"en": "Bangkok"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.736717, "lng": 100.523186}}'),
('Asia Express Transport', 'carrier', 'TH45678901234', '<EMAIL>', '+66-2-456-7890',
 '{"street": {"en": "654 Lat Phrao Road"}, "province": {"en": "Bangkok"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.736717, "lng": 100.523186}}'),

-- Factories
('Thai Agricultural Processing Co.', 'factory', 'TH56789012345', '<EMAIL>', '+66-34-567-8901',
 '{"street": {"en": "987 Industrial Estate"}, "province": {"en": "Chonburi"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.081721, "lng": 100.883043}}'),
('Southeast Rubber Industries', 'factory', 'TH67890123456', '<EMAIL>', '+66-74-678-9012',
 '{"street": {"en": "147 Rubber Zone"}, "province": {"en": "Songkhla"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 7.200000, "lng": 100.600000}}'),

-- Shippers (using JSONB metadata for simple types)
('China Export Shippers Ltd.', 'shipper', 'CN123456789', '<EMAIL>', '+86-21-1234-5678',
 '{"street": {"en": "258 Huangpu Road"}, "province": {"en": "Shanghai"}, "country": {"en": "China"}, "coordinates": {"lat": 31.230416, "lng": 121.473701}}'),
('Ningbo Shipping Agency', 'shipper', 'CN234567890', '<EMAIL>', '+86-574-2345-6789',
 '{"street": {"en": "369 Port Avenue"}, "province": {"en": "Ningbo, Zhejiang"}, "country": {"en": "China"}, "coordinates": {"lat": 29.865801, "lng": 121.643204}}'),

-- Consignees (using JSONB metadata for simple types)
('Bangkok Import Consignee', 'consignee', 'TH78901234567', '<EMAIL>', '+66-2-789-0123',
 '{"street": {"en": "741 Warehouse District"}, "province": {"en": "Bangkok"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.736717, "lng": 100.523186}}'),
('Laem Chabang Receivers Ltd.', 'consignee', 'TH89012345678', '<EMAIL>', '+66-38-890-1234',
 '{"street": {"en": "852 Port Access Road"}, "province": {"en": "Chonburi"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.081721, "lng": 100.883043}}'),

-- Notify Parties (using JSONB metadata for simple types)
('Trade Notification Services', 'notify_party', 'TH90123456789', '<EMAIL>', '+66-2-901-2345',
 '{"street": {"en": "963 Business Tower"}, "province": {"en": "Bangkok"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.736717, "lng": 100.523186}}'),

-- Forwarder Agents (using JSONB metadata for simple types)
('International Freight Forwarders', 'forwarder_agent', 'TH01234567890', '<EMAIL>', '+66-2-012-3456',
 '{"street": {"en": "159 Logistics Center"}, "province": {"en": "Bangkok"}, "country": {"en": "Thailand"}, "coordinates": {"lat": 13.736717, "lng": 100.523186}}');

-- ============================================================================
-- COMPANY-SPECIFIC INFO DATA
-- ============================================================================

-- Customer Info for complex customer companies
INSERT INTO customer_info (company_id, customer_type, credit_limit_amount, credit_limit_currency, payment_terms, preferred_incoterms, annual_volume)
SELECT 
    c.id,
    'premium'::customer_type_enum,
    500000.00,
    'USD'::currency_enum,
    'Net 30',
    'CIF'::incoterms_enum,
    12000.00
FROM companies c WHERE c.name = 'Global Trading Co., Ltd.' AND c.company_type = 'customer';

INSERT INTO customer_info (company_id, customer_type, credit_limit_amount, credit_limit_currency, payment_terms, preferred_incoterms, annual_volume)
SELECT 
    c.id,
    'regular'::customer_type_enum,
    200000.00,
    'USD'::currency_enum,
    'L/C at sight',
    'FOB'::incoterms_enum,
    8000.00
FROM companies c WHERE c.name = 'Asia Import Export Corp.' AND c.company_type = 'customer';

INSERT INTO customer_info (company_id, customer_type, credit_limit_amount, credit_limit_currency, payment_terms, preferred_incoterms, annual_volume)
SELECT 
    c.id,
    'vip'::customer_type_enum,
    1000000.00,
    'USD'::currency_enum,
    'Net 15',
    'CIF'::incoterms_enum,
    25000.00
FROM companies c WHERE c.name = 'Pacific Commodities Ltd.' AND c.company_type = 'customer';

-- Carrier Info for carrier companies
INSERT INTO carrier_info (company_id, fleet_size, vehicle_types, max_capacity_tons, transport_license_number, coverage_areas, average_delivery_time_days, on_time_delivery_rate, dispatch_phone, dispatch_email)
SELECT 
    c.id,
    45,
    ARRAY['truck', 'container', 'flatbed'],
    30.0,
    'TH-TR-2023-001',
    ARRAY['Bangkok', 'Chonburi', 'Rayong', 'Eastern Thailand'],
    2,
    95.5,
    '+66-2-345-6789',
    '<EMAIL>'
FROM companies c WHERE c.name = 'Swift Logistics Thailand' AND c.company_type = 'carrier';

INSERT INTO carrier_info (company_id, fleet_size, vehicle_types, max_capacity_tons, transport_license_number, coverage_areas, average_delivery_time_days, on_time_delivery_rate, dispatch_phone, dispatch_email)
SELECT 
    c.id,
    28,
    ARRAY['truck', 'container'],
    25.0,
    'TH-TR-2023-002',
    ARRAY['Bangkok', 'Central Thailand', 'Northern Thailand'],
    3,
    92.0,
    '+66-2-456-7890',
    '<EMAIL>'
FROM companies c WHERE c.name = 'Asia Express Transport' AND c.company_type = 'carrier';

-- Factory Info for factory companies
INSERT INTO factory_info (company_id, factory_code, license_no, specializations, production_capacity_tons_per_day, cold_storage_capacity_tons, quality_control_manager, quality_control_phone)
SELECT 
    c.id,
    'TAP-001',
    'FC-TAP-2024-001',
    ARRAY['Tapioca Starch', 'Agricultural Products', 'Food Processing'],
    16, -- ~500000 monthly / 30 days = ~16667, rounded to 16 tons/day
    50,
    'Somchai Tanawat',
    '+66-34-567-8901'
FROM companies c
WHERE c.name = 'Thai Agricultural Processing Co.' AND c.company_type = 'factory';

INSERT INTO factory_info (company_id, factory_code, license_no, specializations, production_capacity_tons_per_day, cold_storage_capacity_tons, quality_control_manager, quality_control_phone)
SELECT 
    c.id,
    'SRI-001',
    'FC-SRI-2024-001',
    ARRAY['Natural Rubber', 'Latex Products', 'Rubber Manufacturing'],
    7, -- ~200000 monthly / 30 days = ~6667, rounded to 7 tons/day
    20,
    'Niran Chaidecha',
    '+66-74-678-9012'
FROM companies c
WHERE c.name = 'Southeast Rubber Industries' AND c.company_type = 'factory';

-- ============================================================================
-- SAMPLE DRIVERS DATA
-- ============================================================================

-- Drivers for Swift Logistics Thailand
INSERT INTO drivers (carrier_id, driver_code, first_name, last_name, phone, line_id, license_number, license_expiry_date)
SELECT 
    c.id,
    'SLT001',
    'Prasert',
    'Kaewmanee',
    '+66-81-123-4567',
    'prasert_driver',
    'TH-***********',
    '2025-12-31'::date
FROM companies c WHERE c.name = 'Swift Logistics Thailand' AND c.company_type = 'carrier';

INSERT INTO drivers (carrier_id, driver_code, first_name, last_name, phone, line_id, license_number, license_expiry_date)
SELECT 
    c.id,
    'SLT002',
    'Surachai',
    'Thongsuk',
    '+66-81-234-5678',
    'surachai_transport',
    'TH-***********',
    '2025-10-15'::date
FROM companies c WHERE c.name = 'Swift Logistics Thailand' AND c.company_type = 'carrier';

-- Drivers for Asia Express Transport
INSERT INTO drivers (carrier_id, driver_code, first_name, last_name, phone, line_id, license_number, license_expiry_date)
SELECT 
    c.id,
    'AET001',
    'Worawit',
    'Srisuwan',
    '+66-81-345-6789',
    'worawit_express',
    'TH-***********',
    '2025-08-20'::date
FROM companies c WHERE c.name = 'Asia Express Transport' AND c.company_type = 'carrier';

-- ============================================================================
-- SAMPLE RELATIONSHIP INTELLIGENCE DATA
-- ============================================================================

-- Customer-Shipper relationships
INSERT INTO customer_shippers (customer_id, shipper_id, is_default, frequency_score, preferred_contact_person, total_shipments)
SELECT 
    customer.id,
    shipper.id,
    true,
    25,
    'Zhang Wei - Export Manager',
    24
FROM companies customer, companies shipper
WHERE customer.name = 'Global Trading Co., Ltd.' AND customer.company_type = 'customer'
AND shipper.name = 'China Export Shippers Ltd.' AND shipper.company_type = 'shipper';

INSERT INTO customer_shippers (customer_id, shipper_id, is_default, frequency_score, preferred_contact_person, total_shipments)
SELECT 
    customer.id,
    shipper.id,
    false,
    12,
    'Li Ming - Shipping Coordinator',
    11
FROM companies customer, companies shipper
WHERE customer.name = 'Global Trading Co., Ltd.' AND customer.company_type = 'customer'
AND shipper.name = 'Ningbo Shipping Agency' AND shipper.company_type = 'shipper';

-- Consignee-Notify Party relationships
INSERT INTO consignee_notify_parties (consignee_id, notify_party_id, is_default, frequency_score, notification_priority, relationship_type)
SELECT 
    consignee.id,
    notify_party.id,
    true,
    20,
    1,
    'customs_broker'
FROM companies consignee, companies notify_party
WHERE consignee.name = 'Bangkok Import Consignee' AND consignee.company_type = 'consignee'
AND notify_party.name = 'Trade Notification Services' AND notify_party.company_type = 'notify_party';

-- Customer-Product relationships with pricing
INSERT INTO customer_products (customer_id, product_id, price_per_unit, currency, pricing_terms, price_valid_from, packaging_type, gross_weight_per_package, net_weight_per_package, pieces_per_package, is_default, frequency_score, total_orders)
SELECT 
    customer.id,
    product.id,
    0.85,
    'USD'::currency_enum,
    'CIF'::incoterms_enum,
    '2024-01-01'::date,
    'Bag'::packaging_type_enum,
    25.5,
    25.0,
    1,
    true,
    15,
    14
FROM companies customer, products product
WHERE customer.name = 'Global Trading Co., Ltd.' AND customer.company_type = 'customer'
AND product.name = 'Tapioca Starch';

INSERT INTO customer_products (customer_id, product_id, price_per_unit, currency, pricing_terms, price_valid_from, packaging_type, gross_weight_per_package, net_weight_per_package, pieces_per_package, is_default, frequency_score, total_orders)
SELECT 
    customer.id,
    product.id,
    2.15,
    'USD'::currency_enum,
    'FOB'::incoterms_enum,
    '2024-01-01'::date,
    'Bag'::packaging_type_enum,
    35.2,
    35.0,
    1,
    false,
    8,
    7
FROM companies customer, products product
WHERE customer.name = 'Asia Import Export Corp.' AND customer.company_type = 'customer'
AND product.name = 'Natural Rubber (STR20)';

-- ============================================================================
-- DOCUMENT TEMPLATES SEED DATA
-- ============================================================================

-- Basic invoice template
INSERT INTO document_templates (template_name, document_type, template_content, template_data, description, required_fields)
VALUES (
    'Standard CIF Invoice',
    'invoice_cif'::document_type_enum,
    '<html><body><h1>Commercial Invoice</h1><p>Invoice No: {{invoice_number}}</p><p>Date: {{invoice_date}}</p><table><tr><th>Product</th><th>Quantity</th><th>Unit Price</th><th>Total</th></tr>{{#products}}<tr><td>{{name}}</td><td>{{quantity}}</td><td>{{unit_price}}</td><td>{{total}}</td></tr>{{/products}}</table><p>Grand Total: {{grand_total}} {{currency}}</p></body></html>',
    '{"currency": "USD", "payment_terms": "Net 30", "incoterms": "CIF"}',
    'Standard CIF invoice template for shipments',
    ARRAY['invoice_number', 'invoice_date', 'products', 'grand_total']
);

-- Basic booking confirmation template
INSERT INTO document_templates (template_name, document_type, template_content, template_data, description, required_fields)
VALUES (
    'Booking Confirmation Standard',
    'booking_confirmation'::document_type_enum,
    '<html><body><h1>Booking Confirmation</h1><p>Booking Ref: {{booking_reference}}</p><p>Shipment No: {{shipment_number}}</p><p>From: {{origin_port}} To: {{destination_port}}</p><p>ETD: {{etd}} ETA: {{eta}}</p><p>Vessel: {{vessel_name}} Voyage: {{voyage_number}}</p></body></html>',
    '{"template_version": "1.0"}',
    'Standard booking confirmation for sea freight',
    ARRAY['booking_reference', 'shipment_number', 'origin_port', 'destination_port', 'etd', 'eta']
);

-- ============================================================================
-- FINAL DATA INTEGRITY CHECKS
-- ============================================================================

-- Verify referential integrity
DO $$
DECLARE
    check_count INTEGER;
BEGIN
    -- Check that all companies have proper relationships
    SELECT COUNT(*) INTO check_count FROM companies WHERE company_type IN ('customer', 'carrier', 'factory');
    RAISE NOTICE 'Complex companies created: %', check_count;
    
    -- Check that products reference valid units
    SELECT COUNT(*) INTO check_count FROM products p JOIN units_of_measure u ON p.unit_of_measure_id = u.id;
    RAISE NOTICE 'Products with valid units: %', check_count;
    
    -- Check that drivers reference valid carriers
    SELECT COUNT(*) INTO check_count FROM drivers d JOIN companies c ON d.carrier_id = c.id WHERE c.company_type = 'carrier';
    RAISE NOTICE 'Drivers with valid carriers: %', check_count;
    
    -- Check relationship intelligence data
    SELECT COUNT(*) INTO check_count FROM customer_shippers;
    RAISE NOTICE 'Customer-shipper relationships: %', check_count;
    
    RAISE NOTICE 'Sample reference data loaded successfully!';
END $$;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON SCHEMA public IS 'Sample reference data loaded for testing and application support - includes units, ports, products, companies, and relationships';

-- Final verification
SELECT 
    'Reference Data Summary' as section,
    (SELECT COUNT(*) FROM units_of_measure) as units_of_measure,
    (SELECT COUNT(*) FROM ports) as ports,
    (SELECT COUNT(*) FROM products) as products,
    (SELECT COUNT(*) FROM companies) as companies,
    (SELECT COUNT(*) FROM drivers) as drivers,
    (SELECT COUNT(*) FROM customer_shippers) as customer_shipper_relationships,
    (SELECT COUNT(*) FROM customer_products) as customer_product_relationships;