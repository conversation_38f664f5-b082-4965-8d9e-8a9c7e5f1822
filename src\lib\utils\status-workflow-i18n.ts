import type { ShipmentStatus } from '@/types/status-update'
import type { Locale } from '@/lib/i18n'

/**
 * Get localized status display name
 */
export function getLocalizedStatusDisplayName(status: ShipmentStatus, locale: Locale = 'en'): string {
  const displayNames: Record<Locale, Record<ShipmentStatus, string>> = {
    en: {
      'booking_confirmed': 'Booking Confirmed',
      'transport_assigned': 'Transport Assigned',
      'driver_assigned': 'Driver Assigned',
      'empty_container_picked': 'Empty Container Picked',
      'arrived_at_factory': 'Arrived at Factory',
      'loading_started': 'Loading Started',
      'departed_factory': 'Departed Factory',
      'container_returned': 'Container Returned',
      'shipped': 'Shipped',
      'arrived': 'Arrived',
      'completed': 'Completed',
      'cancelled': 'Cancelled'
    },
    th: {
      'booking_confirmed': 'ยืนยันการจอง',
      'transport_assigned': 'มอบหมายการขนส่ง',
      'driver_assigned': 'มอบหมายคนขับ',
      'empty_container_picked': 'รับตู้เปล่าแล้ว',
      'arrived_at_factory': 'ถึงโรงงานแล้ว',
      'loading_started': 'เริ่มบรรทุกสินค้า',
      'departed_factory': 'ออกจากโรงงานแล้ว',
      'container_returned': 'คืนตู้คอนเทนเนอร์แล้ว',
      'shipped': 'จัดส่งแล้ว',
      'arrived': 'ถึงจุดหมายแล้ว',
      'completed': 'เสร็จสิ้น',
      'cancelled': 'ยกเลิก'
    }
  }
  
  return displayNames[locale]?.[status] || displayNames.en[status] || status
}

/**
 * Get localized status transition description
 */
export function getLocalizedStatusTransitionDescription(status: ShipmentStatus, locale: Locale = 'en'): string {
  const descriptions: Record<Locale, Record<ShipmentStatus, string>> = {
    en: {
      'booking_confirmed': 'Initial booking confirmation received',
      'transport_assigned': 'Transportation has been assigned',
      'driver_assigned': 'Driver has been assigned to this shipment',
      'empty_container_picked': 'Empty container picked up from port/depot',
      'arrived_at_factory': 'Arrived at factory for loading',
      'loading_started': 'Started loading products into container',
      'departed_factory': 'Left factory with loaded container',
      'container_returned': 'Container returned to port/depot',
      'shipped': 'Container loaded onto vessel/transport',
      'arrived': 'Shipment arrived at destination',
      'completed': 'Shipment delivery completed',
      'cancelled': 'Shipment has been cancelled'
    },
    th: {
      'booking_confirmed': 'ได้รับการยืนยันการจองแล้ว',
      'transport_assigned': 'ได้มอบหมายการขนส่งแล้ว',
      'driver_assigned': 'ได้มอบหมายคนขับแล้ว',
      'empty_container_picked': 'รับตู้เปล่าจากท่า/คลังแล้ว',
      'arrived_at_factory': 'ถึงโรงงานเพื่อบรรทุกสินค้า',
      'loading_started': 'เริ่มบรรทุกสินค้าลงตู้คอนเทนเนอร์',
      'departed_factory': 'ออกจากโรงงานพร้อมตู้บรรทุกสินค้า',
      'container_returned': 'คืนตู้คอนเทนเนอร์ที่ท่า/คลังแล้ว',
      'shipped': 'ตู้คอนเทนเนอร์ขึ้นเรือ/ขนส่งแล้ว',
      'arrived': 'สินค้าถึงจุดหมายแล้ว',
      'completed': 'การจัดส่งเสร็จสมบูรณ์',
      'cancelled': 'ยกเลิกการจัดส่งแล้ว'
    }
  }
  
  return descriptions[locale]?.[status] || descriptions.en[status] || ''
}

/**
 * Get localized photo requirement description
 */
export function getLocalizedPhotoRequirementDescription(status: ShipmentStatus, locale: Locale = 'en'): string {
  const descriptions: Record<Locale, Record<ShipmentStatus, string>> = {
    en: {
      'booking_confirmed': 'Documentation photos',
      'transport_assigned': 'Transport assignment confirmation',
      'driver_assigned': 'Driver assignment confirmation', 
      'empty_container_picked': 'Empty container condition and seal photos',
      'arrived_at_factory': 'Factory arrival location confirmation',
      'loading_started': 'Container and product loading photos',
      'departed_factory': 'Loaded container and seal photos',
      'container_returned': 'Container return location confirmation',
      'shipped': 'Shipping documentation (optional)',
      'arrived': 'Arrival confirmation at destination',
      'completed': 'Completion documentation (optional)',
      'cancelled': 'Cancellation documentation'
    },
    th: {
      'booking_confirmed': 'รูปเอกสารการจอง',
      'transport_assigned': 'รูปยืนยันการมอบหมายขนส่ง',
      'driver_assigned': 'รูปยืนยันการมอบหมายคนขับ',
      'empty_container_picked': 'รูปสภาพตู้เปล่าและซีล',
      'arrived_at_factory': 'รูปยืนยันการมาถึงโรงงาน',
      'loading_started': 'รูปตู้คอนเทนเนอร์และการบรรทุกสินค้า',
      'departed_factory': 'รูปตู้บรรทุกสินค้าและซีล',
      'container_returned': 'รูปยืนยันการคืนตู้คอนเทนเนอร์',
      'shipped': 'รูปเอกสารการจัดส่ง (ไม่บังคับ)',
      'arrived': 'รูปยืนยันการมาถึงจุดหมาย',
      'completed': 'รูปเอกสารการเสร็จสิ้น (ไม่บังคับ)',
      'cancelled': 'รูปเอกสารการยกเลิก'
    }
  }
  
  return descriptions[locale]?.[status] || descriptions.en[status] || ''
}