'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/hooks/use-auth'
import type { 
  TransportationForm, 
  TransportationUpdate,
  TransportationQuery,
  Coordinates
} from '@/lib/validations/transportation'

// Transportation record type based on database schema
export interface Transportation {
  id: string
  shipment_id: string | null
  carrier_id: string | null
  driver_id: string | null
  vehicle_head_number: string | null
  vehicle_tail_number: string | null
  driver_phone: string | null
  assignment_date: string | null
  pickup_container_location: string | null
  pickup_container_gps_coordinates: unknown | null // PostGIS point type
  pickup_product_location: string | null
  pickup_product_gps_coordinates: unknown | null // PostGIS point type
  delivery_location: string | null
  delivery_gps_coordinates: unknown | null // PostGIS point type
  notes: string | null
  estimated_distance: number | null
  created_at: string
  // Relations
  carrier?: {
    id: string
    name: string
    company_type: string
  }
  driver?: {
    user_id: string
    driver_first_name: string
    driver_last_name: string
    phone: string | null
    line_id: string | null
    is_active: boolean
  }
  shipment?: {
    id: string
    shipment_number: string
    status: string
  }
}

// Driver type for carrier filtering
export interface Driver {
  id: string
  carrier_id: string
  driver_first_name: string
  driver_last_name: string
  driver_code: string | null
  phone: string | null
  line_id: string | null
  vehicle_head_number: string | null
  vehicle_tail_number: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

// Carrier company type
export interface Carrier {
  id: string
  name: string
  company_type: string
  contact_phone: string | null
  contact_email: string | null
  is_active: boolean
}

export function useTransportation(shipmentId?: string) {
  const [transportation, setTransportation] = useState<Transportation | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()
  const { user } = useAuth()

  // Fetch transportation assignment for a shipment
  const fetchTransportation = useCallback(async (id?: string) => {
    if (!id && !shipmentId) return

    const targetId = id || shipmentId
    if (!targetId) return

    setIsLoading(true)
    setError(null)

    try {
      const { data, error: fetchError } = await supabase
        .from('transportation')
        .select(`
          *,
          carrier:companies!carrier_id(
            id,
            name,
            company_type
          ),
          driver:drivers!driver_id(
            id,
            driver_first_name,
            driver_last_name,
            phone,
            line_id,
            is_active
          ),
          shipment:shipments!shipment_id(
            id,
            shipment_number,
            status
          )
        `)
        .eq('shipment_id', targetId)
        .maybeSingle()

      if (fetchError) {
        throw fetchError
      }

      setTransportation(data)
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to fetch transportation assignment'
      setError(message)
      console.error('Error fetching transportation:', err)
    } finally {
      setIsLoading(false)
    }
  }, [supabase, shipmentId])

  // Create transportation assignment
  const createTransportationAssignment = useCallback(async (
    data: TransportationForm & { shipment_id: string }
  ): Promise<Transportation> => {
    if (!user) {
      throw new Error('User not authenticated')
    }

    setError(null)
    
    // Convert coordinates to PostGIS point format
    const formatCoordinates = (coords?: Coordinates) => {
      if (!coords) return null
      //return `POINT(${coords.lng} ${coords.lat})`
      return `(${coords.lng},${coords.lat})`
    }

    try {
      const insertData = {
        shipment_id: data.shipment_id,
        carrier_id: data.carrier_id,
        driver_id: data.driver_id || null,
        vehicle_head_number: data.vehicle_head_number || null,
        vehicle_tail_number: data.vehicle_tail_number || null,
        driver_phone: data.driver_phone || null,
        assignment_date: data.assignment_date,
        pickup_container_location: data.pickup_container_location || null,
        pickup_container_gps_coordinates: formatCoordinates(data.pickup_container_gps_coordinates),
        pickup_product_location: data.pickup_product_location || null,
        pickup_product_gps_coordinates: formatCoordinates(data.pickup_product_gps_coordinates),
        delivery_location: data.delivery_location,
        delivery_gps_coordinates: formatCoordinates(data.delivery_gps_coordinates),
        estimated_distance: data.estimated_distance || null,
        notes: data.notes || null,
      }

      const { data: result, error: insertError } = await supabase
        .from('transportation')
        .insert([insertData])
        .select(`
          *,
          carrier:companies!carrier_id(
            id,
            name,
            company_type
          ),
          driver:drivers!driver_id(
            id,
            driver_first_name,
            driver_last_name,
            phone,
            line_id,
            is_active
          )
        `)
        .single()

      if (insertError) {
        throw insertError
      }

      // Update shipment status based on driver assignment
      if (data.shipment_id) {
        // First, update to transport_assigned
        const { error: transportStatusError } = await supabase
          .from('shipments')
          .update({ 
            status: 'transport_assigned',
            updated_at: new Date().toISOString()
          })
          .eq('id', data.shipment_id)
          .eq('status', 'booking_confirmed') // Only update if still at booking_confirmed

        if (transportStatusError) {
          console.warn('Failed to update shipment to transport_assigned status:', transportStatusError)
        }

        // If driver is assigned, immediately update to driver_assigned
        if (data.driver_id) {
          const { error: driverStatusError } = await supabase
            .from('shipments')
            .update({ 
              status: 'driver_assigned',
              updated_at: new Date().toISOString()
            })
            .eq('id', data.shipment_id)
            .eq('status', 'transport_assigned') // Only update if just set to transport_assigned

          if (driverStatusError) {
            console.warn('Failed to update shipment to driver_assigned status:', driverStatusError)
          }
        }
      }

      // Refresh local transportation state
      if (result.shipment_id === shipmentId) {
        setTransportation(result)
      }

      return result
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to create transportation assignment'
      setError(message)
      throw new Error(message)
    }
  }, [user, supabase, shipmentId])

  // Update transportation assignment
  const updateTransportationAssignment = useCallback(async (
    data: TransportationUpdate
  ): Promise<Transportation> => {
    if (!user) {
      throw new Error('User not authenticated')
    }

    if (!data.id) {
      throw new Error('Transportation assignment ID is required')
    }

    setError(null)

    // Convert coordinates to PostGIS point format
    const formatCoordinates = (coords?: Coordinates) => {
      if (!coords) return null
      //return `POINT(${coords.lng} ${coords.lat})`
      return `(${coords.lng},${coords.lat})`
    }

    try {
      const updateData = {
        ...data,
        pickup_container_gps_coordinates: data.pickup_container_gps_coordinates 
          ? formatCoordinates(data.pickup_container_gps_coordinates) 
          : undefined,
        pickup_product_gps_coordinates: data.pickup_product_gps_coordinates 
          ? formatCoordinates(data.pickup_product_gps_coordinates) 
          : undefined,
        delivery_gps_coordinates: data.delivery_gps_coordinates 
          ? formatCoordinates(data.delivery_gps_coordinates) 
          : undefined,
      }

      // Remove id from update data
      const { id, ...updateFields } = updateData

      const { data: result, error: updateError } = await supabase
        .from('transportation')
        .update(updateFields)
        .eq('id', id)
        .select(`
          *,
          carrier:companies!carrier_id(
            id,
            name,
            company_type
          ),
          driver:drivers!driver_id(
            id,
            driver_first_name,
            driver_last_name,
            phone,
            line_id,
            is_active
          )
        `)
        .single()

      if (updateError) {
        throw updateError
      }

      // Update shipment status based on driver assignment changes
      if (result.shipment_id && updateFields.driver_id !== undefined) {
        if (updateFields.driver_id) {
          // Driver was assigned - update status to driver_assigned
          const { error: driverStatusError } = await supabase
            .from('shipments')
            .update({ 
              status: 'driver_assigned',
              updated_at: new Date().toISOString()
            })
            .eq('id', result.shipment_id)
            .in('status', ['transport_assigned', 'booking_confirmed'])

          if (driverStatusError) {
            console.warn('Failed to update shipment to driver_assigned status:', driverStatusError)
          }
        } else {
          // Driver was removed - revert to transport_assigned
          const { error: transportStatusError } = await supabase
            .from('shipments')
            .update({ 
              status: 'transport_assigned',
              updated_at: new Date().toISOString()
            })
            .eq('id', result.shipment_id)
            .eq('status', 'driver_assigned')

          if (transportStatusError) {
            console.warn('Failed to update shipment to transport_assigned status:', transportStatusError)
          }
        }
      }

      // Refresh local transportation state
      if (result.shipment_id === shipmentId) {
        setTransportation(result)
      }

      return result
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to update transportation assignment'
      setError(message)
      throw new Error(message)
    }
  }, [user, supabase, shipmentId])

  // Delete transportation assignment
  const deleteTransportationAssignment = useCallback(async (id: string) => {
    if (!user) {
      throw new Error('User not authenticated')
    }

    setError(null)

    try {
      const { error: deleteError } = await supabase
        .from('transportation')
        .delete()
        .eq('id', id)

      if (deleteError) {
        throw deleteError
      }

      // Clear local state if deleted assignment was for current shipment
      if (transportation && transportation.id === id) {
        setTransportation(null)
      }

    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to delete transportation assignment'
      setError(message)
      throw new Error(message)
    }
  }, [user, supabase, transportation])

  // Load initial data
  useEffect(() => {
    if (shipmentId) {
      fetchTransportation(shipmentId)
    }
  }, [fetchTransportation, shipmentId])

  return {
    transportation,
    isLoading,
    error,
    refetchTransportation: fetchTransportation,
    createTransportationAssignment,
    updateTransportationAssignment,
    deleteTransportationAssignment,
  }
}

// Hook for fetching carrier companies
export function useCarriers() {
  const [carriers, setCarriers] = useState<Carrier[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  const fetchCarriers = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const { data, error: fetchError } = await supabase
        .from('companies')
        .select('id, name, company_type, contact_phone, contact_email, is_active')
        .eq('company_type', 'carrier')
        .eq('is_active', true)
        .order('name')

      if (fetchError) {
        throw fetchError
      }

      setCarriers(data || [])
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to fetch carrier companies'
      setError(message)
      console.error('Error fetching carriers:', err)
    } finally {
      setIsLoading(false)
    }
  }, [supabase])

  useEffect(() => {
    fetchCarriers()
  }, [fetchCarriers])

  return {
    carriers,
    isLoading,
    error,
    refetchCarriers: fetchCarriers,
  }
}

// Hook for fetching drivers by carrier
export function useDriversByCarrier(carrierId?: string) {
  const [drivers, setDrivers] = useState<Driver[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  const fetchDrivers = useCallback(async (id?: string) => {
    const targetCarrierId = id || carrierId
    if (!targetCarrierId) {
      setDrivers([])
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const { data, error: fetchError } = await supabase
        .from('drivers')
        .select('id, carrier_id, driver_first_name, driver_last_name, driver_code, phone, line_id, vehicle_head_number, vehicle_tail_number, is_active, created_at, updated_at')
        .eq('carrier_id', targetCarrierId)
        .eq('is_active', true)
        .order('driver_first_name')

      if (fetchError) {
        throw fetchError
      }

      setDrivers(data || [])
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to fetch drivers'
      setError(message)
      console.error('Error fetching drivers:', err)
    } finally {
      setIsLoading(false)
    }
  }, [supabase, carrierId])

  useEffect(() => {
    if (carrierId) {
      fetchDrivers(carrierId)
    } else {
      setDrivers([])
    }
  }, [fetchDrivers, carrierId])

  return {
    drivers,
    isLoading,
    error,
    refetchDrivers: fetchDrivers,
  }
}

// Utility hook for coordinate conversion
export function useCoordinateUtils() {
  // Parse PostGIS point coordinates from database
  const parseCoordinates = useCallback((coords: unknown): Coordinates | undefined => {
    if (!coords) return undefined
    
    try {
      const coordString = coords.toString()
      
      // ST_AsText returns format: POINT(lng lat)
      const pointMatch = coordString.match(
        /POINT\(([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\)/
      )
      
      if (pointMatch) {
        return {
          lat: parseFloat(pointMatch[2]),
          lng: parseFloat(pointMatch[1])
        }
      }
    } catch (error) {
      console.warn('Failed to parse coordinates:', error)
    }
    
    return undefined
  }, [])

  // Format coordinates for display
  const formatCoordinates = useCallback((lat: number, lng: number): string => {
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  }, [])

  return {
    parseCoordinates,
    formatCoordinates,
  }
}