'use client'

import { useState, useEffect } from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Loader2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Eye,
  AlertCircle,
  Info,
  Save,
  X,
} from 'lucide-react'
import { TemplatePreview } from './template-preview'
import type {
  DocumentTemplate,
  DocumentTemplateInsert,
  DocumentTemplateUpdate,
  DocumentType
} from '@/types/document-template'
import {
  VALID_DOCUMENT_TYPES,
  VALID_PAGE_SIZES,
  VALID_PAGE_ORIENTATIONS,
  VALID_CURRENCY_FORMATS,
  VALID_DATE_FORMATS,
  VALID_NUMBER_FORMATS,
  validateTemplateInsert,
  validateTemplateUpdate,
  validateTemplatePlaceholders
} from '@/lib/utils/template-validation'

// Form validation schema
const templateFormSchema = z.object({
  template_name: z.string().min(3, 'Template name must be at least 3 characters').max(255),
  document_type: z.enum(VALID_DOCUMENT_TYPES as [DocumentType, ...DocumentType[]]),
  version: z.string().min(1, 'Version is required').regex(/^\d+\.\d+(\.\d+)?(-[a-zA-Z0-9]+)?$/, 'Invalid version format'),
  template_content: z.string().min(1, 'Template content is required'),
  template_data: z.record(z.any()).optional(),
  template_styles: z.string().optional(),
  page_size: z.enum(VALID_PAGE_SIZES as [string, ...string[]]).default('A4'),
  page_orientation: z.enum(VALID_PAGE_ORIENTATIONS as [string, ...string[]]).default('portrait'),
  margin_top: z.number().min(0).max(200).default(20),
  margin_bottom: z.number().min(0).max(200).default(20),
  margin_left: z.number().min(0).max(200).default(20),
  margin_right: z.number().min(0).max(200).default(20),
  language: z.string().regex(/^[a-z]{2}(-[A-Z]{2})?$/, 'Invalid language format').default('en'),
  currency_format: z.enum(VALID_CURRENCY_FORMATS as [string, ...string[]]).default('USD'),
  date_format: z.enum(VALID_DATE_FORMATS as [string, ...string[]]).default('YYYY-MM-DD'),
  number_format: z.enum(VALID_NUMBER_FORMATS as [string, ...string[]]).default('en-US'),
  description: z.string().optional(),
  usage_notes: z.string().optional(),
  required_fields: z.array(z.string()).optional(),
  is_active: z.boolean().default(true),
  is_default: z.boolean().default(false),
})

type TemplateFormData = z.infer<typeof templateFormSchema>

interface DocumentTemplateFormProps {
  template?: DocumentTemplate | null
  onSubmit: (data: DocumentTemplateInsert | DocumentTemplateUpdate) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  isEditing?: boolean
  className?: string
}

/**
 * Document Template Form Component
 * Story 5.1: Document Template Management System
 * 
 * Form for creating and editing document templates with validation,
 * preview functionality, and advanced configuration options.
 */
export function DocumentTemplateForm({
  template,
  onSubmit,
  onCancel,
  isLoading = false,
  isEditing = false,
  className,
}: DocumentTemplateFormProps) {
  const [error, setError] = useState<string | null>(null)
  const [validationWarnings, setValidationWarnings] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState('content')

  const form = useForm<TemplateFormData>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      template_name: template?.template_name || '',
      document_type: template?.document_type || 'invoice_fob',
      version: template?.version || '1.0',
      template_content: template?.template_content || '',
      template_data: template?.template_data || undefined,
      template_styles: template?.template_styles || '',
      page_size: template?.page_size || 'A4',
      page_orientation: template?.page_orientation || 'portrait',
      margin_top: template?.margin_top || 20,
      margin_bottom: template?.margin_bottom || 20,
      margin_left: template?.margin_left || 20,
      margin_right: template?.margin_right || 20,
      language: template?.language || 'en',
      currency_format: template?.currency_format || 'USD',
      date_format: template?.date_format || 'YYYY-MM-DD',
      number_format: template?.number_format || 'en-US',
      description: template?.description || '',
      usage_notes: template?.usage_notes || '',
      required_fields: template?.required_fields || [],
      is_active: template?.is_active !== undefined ? template.is_active : true,
      is_default: template?.is_default || false,
    },
  })

  // Watch template content and other fields for real-time validation and preview
  const templateContent = useWatch({
    control: form.control,
    name: 'template_content',
  })

  // Watch all form fields for live preview updates
  const watchedFields = useWatch({ control: form.control })

  // Validate placeholders in template content
  useEffect(() => {
    if (templateContent) {
      const validation = validateTemplatePlaceholders(templateContent)
      if (validation.warnings) {
        setValidationWarnings(validation.warnings.map(w => w.message))
      } else {
        setValidationWarnings([])
      }
    }
  }, [templateContent])

  const handleSubmit = async (data: TemplateFormData) => {
    setError(null)
    
    try {
      // Validate the data
      const validation = isEditing 
        ? validateTemplateUpdate(data as DocumentTemplateUpdate)
        : validateTemplateInsert(data as DocumentTemplateInsert)
      
      if (!validation.isValid) {
        setError(validation.errors.map(e => e.message).join(', '))
        return
      }

      await onSubmit(data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save template'
      setError(errorMessage)
    }
  }

  const documentTypeOptions = VALID_DOCUMENT_TYPES.map(type => ({
    value: type,
    label: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }))

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
                {isEditing ? 'Edit Template' : 'Create Template'}
              </h2>
              <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                {isEditing ? 'Update template configuration and content' : 'Create a new document template with placeholders'}
              </p>
            </div>
            <div className="flex gap-2">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading} className="bg-orange-600 hover:bg-orange-700">
                {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                <Save className="h-4 w-4 mr-2" />
                {isEditing ? 'Update' : 'Create'} Template
              </Button>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Validation warnings */}
          {validationWarnings.length > 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  {validationWarnings.map((warning, index) => (
                    <div key={index}>{warning}</div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="content">
                <FileText className="h-4 w-4 mr-2" />
                Content
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
              <TabsTrigger value="formatting">
                <Palette className="h-4 w-4 mr-2" />
                Formatting
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </TabsTrigger>
            </TabsList>

            {/* Content Tab */}
            <TabsContent value="content" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Template Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="template_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Template Name*</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Invoice CIF Template" {...field} />
                          </FormControl>
                          <FormDescription>
                            Unique name to identify this template
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="document_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Document Type*</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select document type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {documentTypeOptions.map(option => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="version"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Version*</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., 1.0, 2.1-beta" {...field} />
                          </FormControl>
                          <FormDescription>
                            Semantic version number
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="is_active"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Active</FormLabel>
                            <FormDescription>
                              Template can be used for document generation
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="is_default"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Default</FormLabel>
                            <FormDescription>
                              Use as default for this document type
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Brief description of this template's purpose..."
                            className="min-h-[60px]"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Template Content</CardTitle>
                  <FormDescription>
                    Create your template using HTML/text with placeholders in the format {'{{placeholder_name}}'}.
                    Common placeholders include: {'{{shipment_number}}'}, {'{{customer_name}}'}, {'{{total_amount}}'}.
                  </FormDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="template_content"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Textarea
                            placeholder="Enter your template content with placeholders..."
                            className="min-h-[400px] font-mono text-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="usage_notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Usage Notes</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Instructions for using this template..."
                            className="min-h-[80px]"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Provide guidance on when and how to use this template
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Page Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="page_size"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Page Size</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {VALID_PAGE_SIZES.map(size => (
                                <SelectItem key={size} value={size}>{size}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="page_orientation"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Orientation</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {VALID_PAGE_ORIENTATIONS.map(orientation => (
                                <SelectItem key={orientation} value={orientation}>
                                  {orientation.charAt(0).toUpperCase() + orientation.slice(1)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-3">Margins (mm)</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <FormField
                        control={form.control}
                        name="margin_top"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Top</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                min="0" 
                                max="200"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="margin_bottom"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Bottom</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                min="0" 
                                max="200"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="margin_left"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Left</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                min="0" 
                                max="200"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="margin_right"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Right</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                min="0" 
                                max="200"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Formatting Tab */}
            <TabsContent value="formatting" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Format Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="language"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Language</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., en, en-US, th" {...field} />
                          </FormControl>
                          <FormDescription>ISO language code</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="currency_format"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Currency Format</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {VALID_CURRENCY_FORMATS.map(format => (
                                <SelectItem key={format} value={format}>{format}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="date_format"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date Format</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {VALID_DATE_FORMATS.map(format => (
                                <SelectItem key={format} value={format}>{format}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="number_format"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Number Format</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {VALID_NUMBER_FORMATS.map(format => (
                                <SelectItem key={format} value={format}>{format}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Custom Styles</CardTitle>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="template_styles"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CSS Styles</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="/* Custom CSS styles for this template */&#10;body { font-family: Arial, sans-serif; }&#10;.header { background-color: #f5f5f5; }"
                            className="min-h-[200px] font-mono text-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Add custom CSS styles for template presentation
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Preview Tab */}
            <TabsContent value="preview" className="space-y-0">
              <TemplatePreview 
                template={{
                  ...watchedFields,
                  id: template?.id,
                  created_by: template?.created_by,
                  created_at: template?.created_at,
                  updated_at: template?.updated_at,
                }}
                className="w-full"
                expanded={true}
              />
            </TabsContent>
          </Tabs>
        </form>
      </Form>
    </div>
  )
}