import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { ShipmentCard } from '../shipment-card'
import type { ShipmentWithRelations } from '@/lib/supabase/types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock data
const mockShipment: ShipmentWithRelations = {
  id: 'test-id',
  shipment_number: 'EXSEA-BKK-250101-001',
  status: 'booking_confirmed',
  transportation_mode: 'sea',
  customer_id: 'customer-1',
  customer: {
    id: 'customer-1',
    name: 'Test Customer Ltd.',
  },
  origin_port_id: 'port-1',
  origin_port: {
    id: 'port-1',
    code: 'BKK',
    name: 'Bangkok Port',
  },
  destination_port_id: 'port-2',
  destination_port: {
    id: 'port-2',
    code: 'LAX',
    name: 'Los Angeles Port',
  },
  etd_date: '2025-01-15',
  eta_date: '2025-02-01',
  containers: [],
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
}

describe('ShipmentCard', () => {
  it('renders shipment information correctly', () => {
    render(
      <ShipmentCard
        shipment={mockShipment}
        isSelected={false}
        isExpanded={false}
      />
    )

    expect(screen.getByText('EXSEA-BKK-250101-001')).toBeInTheDocument()
    expect(screen.getByText('Test Customer Ltd.')).toBeInTheDocument()
    expect(screen.getByText('BKK')).toBeInTheDocument()
    expect(screen.getByText('LAX')).toBeInTheDocument()
  })

  it('shows selection checkbox when onToggleSelection is provided', () => {
    const mockToggleSelection = vi.fn()
    
    render(
      <ShipmentCard
        shipment={mockShipment}
        isSelected={false}
        isExpanded={false}
        onToggleSelection={mockToggleSelection}
      />
    )

    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toBeInTheDocument()
    
    fireEvent.click(checkbox)
    expect(mockToggleSelection).toHaveBeenCalledTimes(1)
  })

  it('shows expand/collapse button when onToggleExpansion is provided', () => {
    const mockToggleExpansion = vi.fn()
    
    render(
      <ShipmentCard
        shipment={mockShipment}
        isSelected={false}
        isExpanded={false}
        onToggleExpansion={mockToggleExpansion}
      />
    )

    // Find the expand button using test ID
    const expandButton = screen.getByTestId('expand-toggle-button')
    
    expect(expandButton).toBeInTheDocument()
    fireEvent.click(expandButton)
    expect(mockToggleExpansion).toHaveBeenCalledTimes(1)
  })

  it('displays expanded content when isExpanded is true', () => {
    const mockToggleExpansion = vi.fn()
    
    const mockShipmentWithDetails = {
      ...mockShipment,
      invoice_number: 'INV-001',
      booking_number: 'BK-001',
      closing_time: '2025-01-10T14:30:00Z',
      containers: [
        {
          id: 'cont-1',
          container_number: 'CONT-123456',
          container_type: '20FT',
          seal_number: 'SEAL123',
          temperature: '-18°C',
          vent: 'Open',
        },
      ],
    }
    
    const { rerender } = render(
      <ShipmentCard
        shipment={mockShipmentWithDetails}
        isSelected={false}
        isExpanded={false}
        onToggleExpansion={mockToggleExpansion}
      />
    )

    // Should not show expanded content initially (it exists but is hidden)
    const invoiceText = screen.queryByText('Invoice:')
    if (invoiceText) {
      // Content should be hidden via CSS (opacity-0 and height-0)
      expect(invoiceText.closest('[aria-hidden="true"]')).toBeInTheDocument()
    }

    // Re-render with expanded=true
    rerender(
      <ShipmentCard
        shipment={mockShipmentWithDetails}
        isSelected={false}
        isExpanded={true}
        onToggleExpansion={mockToggleExpansion}
      />
    )

    // Should now show expanded content (not aria-hidden)
    const expandedInvoiceText = screen.getByText('Invoice:')
    expect(expandedInvoiceText).toBeInTheDocument()
    expect(expandedInvoiceText.closest('[aria-hidden="false"]')).toBeInTheDocument()
    
    // Check that closing time with time is displayed
    expect(screen.getByText('Closing:')).toBeInTheDocument()
    expect(screen.getByText(/Jan 10, 2025 21:30/)).toBeInTheDocument() // UTC+7 timezone conversion
    
    // Check container details are displayed
    expect(screen.getByText('Containers:')).toBeInTheDocument()
    expect(screen.getByText('CONT-123456')).toBeInTheDocument()
    expect(screen.getByText('SEAL123')).toBeInTheDocument()
    expect(screen.getByText('-18°C')).toBeInTheDocument()
    expect(screen.getByText('Open')).toBeInTheDocument()
  })

  it('applies correct classes for different variants', () => {
    const { container, rerender } = render(
      <ShipmentCard
        shipment={mockShipment}
        variant="priority"
        isSelected={false}
        isExpanded={false}
      />
    )

    // Priority variant should have orange ring
    expect(container.firstChild?.firstChild).toHaveClass('ring-orange-500/50')

    rerender(
      <ShipmentCard
        shipment={mockShipment}
        variant="standard"
        isSelected={true}
        isExpanded={false}
      />
    )

    // Selected should have blue ring
    expect(container.firstChild?.firstChild).toHaveClass('ring-blue-500')
  })

  it('handles touch interactions', () => {
    const mockToggleSelection = vi.fn()
    
    render(
      <ShipmentCard
        shipment={mockShipment}
        isSelected={false}
        isExpanded={false}
        onToggleSelection={mockToggleSelection}
      />
    )

    const card = screen.getByRole('button', { name: '' }).closest('.relative')
    
    // Test touch events
    fireEvent.touchStart(card!, {
      touches: [{ clientX: 100, clientY: 100 }],
    })
    
    fireEvent.touchMove(card!, {
      touches: [{ clientX: 50, clientY: 100 }], // Swipe left
    })
    
    fireEvent.touchEnd(card!)
    
    // Should trigger selection toggle on swipe left
    expect(mockToggleSelection).toHaveBeenCalled()
  })

  it('displays transport mode icon correctly', () => {
    const { container } = render(
      <ShipmentCard
        shipment={mockShipment}
        isSelected={false}
        isExpanded={false}
      />
    )

    // Should show ship icon for sea transport
    expect(container.querySelector('svg')).toBeInTheDocument()
  })

  it('shows recent update indicator', () => {
    const recentShipment = {
      ...mockShipment,
      updated_at: new Date().toISOString(), // Very recent update
    }

    const { container } = render(
      <ShipmentCard
        shipment={recentShipment}
        isSelected={false}
        isExpanded={false}
      />
    )

    // Should have blue border for recent updates
    expect(container.firstChild?.firstChild).toHaveClass('border-l-blue-500')
  })
})