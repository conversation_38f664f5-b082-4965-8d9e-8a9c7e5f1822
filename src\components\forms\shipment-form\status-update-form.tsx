'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MapPin, Navigation, AlertCircle, Clock, User } from 'lucide-react'
import {
  statusUpdateFormSchema,
  type StatusUpdateForm,
  type ShipmentStatus,
  getNextValidStatuses,
  STATUS_METADATA,
} from '@/lib/validations/status-updates'
import { SHIPMENT_STATUSES } from '@/lib/validations/shipment'

interface Coordinates {
  lat: number
  lng: number
}

interface StatusUpdateFormProps {
  currentStatus: ShipmentStatus
  onSubmit: (data: StatusUpdateForm & { shipment_id: string }) => Promise<void>
  shipment_id: string
  isSubmitting?: boolean
  onCancel?: () => void
}

export function StatusUpdateForm({
  currentStatus,
  onSubmit,
  shipment_id,
  isSubmitting = false,
  onCancel,
}: StatusUpdateFormProps) {
  const [coordinates, setCoordinates] = useState<Coordinates | undefined>()
  const [locationError, setLocationError] = useState<string | null>(null)
  const [isGettingLocation, setIsGettingLocation] = useState(false)

  const validNextStatuses = getNextValidStatuses(currentStatus)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty },
  } = useForm<StatusUpdateForm>({
    resolver: zodResolver(statusUpdateFormSchema),
    defaultValues: {
      status_to: validNextStatuses[0] || currentStatus,
      notes: '',
      location: '',
    },
    mode: 'onChange', // Enable validation on change to ensure isDirty works correctly
  })

  const selectedStatus = watch('status_to')
  const selectedStatusMetadata = selectedStatus ? STATUS_METADATA[selectedStatus] : null

  // Get current location using browser geolocation
  const getCurrentLocation = async () => {
    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by this browser')
      return
    }

    setIsGettingLocation(true)
    setLocationError(null)

    try {
      const position = await new Promise<GeolocationPosition>(
        (resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000,
          })
        }
      )

      const coords = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      }

      setCoordinates(coords)
    } catch (err) {
      const error = err as GeolocationPositionError
      let message = 'Failed to get location'

      switch (error.code) {
        case error.PERMISSION_DENIED:
          message = 'Location access denied by user'
          break
        case error.POSITION_UNAVAILABLE:
          message = 'Location information is unavailable'
          break
        case error.TIMEOUT:
          message = 'Location request timed out'
          break
        default:
          message = 'An unknown error occurred'
          break
      }

      setLocationError(message)
    } finally {
      setIsGettingLocation(false)
    }
  }

  const handleFormSubmit = async (data: StatusUpdateForm) => {
    const submitData = {
      ...data,
      shipment_id,
      coordinates,
    }

    try {
      await onSubmit(submitData)
    } catch (error) {
      console.error('Failed to update status:', error)
    }
  }

  // Get status badge info
  const getStatusInfo = (status: ShipmentStatus) => {
    return SHIPMENT_STATUSES.find(s => s.value === status) || {
      label: status,
      variant: 'default' as const,
      color: '#0ea5e9',
    }
  }

  const currentStatusInfo = getStatusInfo(currentStatus)

  if (validNextStatuses.length === 0) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-200 mb-2">
              No Status Updates Available
            </h3>
            <p className="text-slate-400">
              This shipment is in a terminal state and cannot be updated further.
            </p>
            <Badge
              variant={currentStatusInfo.variant}
              className="mt-4"
              style={{ backgroundColor: currentStatusInfo.color + '20', color: currentStatusInfo.color }}
            >
              {currentStatusInfo.label}
            </Badge>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-slate-200">
          <Clock className="h-5 w-5 text-orange-500" />
          <span>Update Status</span>
        </CardTitle>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-slate-400">Current:</span>
          <Badge
            variant={currentStatusInfo.variant}
            className="text-xs"
            style={{ backgroundColor: currentStatusInfo.color + '20', color: currentStatusInfo.color }}
          >
            {currentStatusInfo.label}
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Status Selection */}
          <div className="space-y-2">
            <Label className="text-slate-200">New Status *</Label>
            <Select
              value={selectedStatus}
              onValueChange={(value: ShipmentStatus) => setValue('status_to', value)}
            >
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600">
                {validNextStatuses.map(status => {
                  const statusInfo = getStatusInfo(status)
                  return (
                    <SelectItem
                      key={status}
                      value={status}
                      className="text-slate-200 focus:bg-slate-600"
                    >
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: statusInfo.color }}
                        />
                        <span>{statusInfo.label}</span>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
            {errors.status_to && (
              <p className="text-sm text-red-400">{errors.status_to.message}</p>
            )}
          </div>

          {/* Status Description */}
          {selectedStatusMetadata && (
            <Alert className="bg-slate-700/50 border-slate-600">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-slate-300">
                {selectedStatusMetadata.description}
                {selectedStatusMetadata.requiresLocation && (
                  <span className="text-orange-400 ml-2">
                    • Location information recommended
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Location Input */}
          <div className="space-y-2">
            <Label className="text-slate-200">
              Location (Optional)
            </Label>
            <Input
              {...register('location')}
              placeholder="e.g., Bangkok Port, Warehouse A, Factory Gate"
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
            />
            {errors.location && (
              <p className="text-sm text-red-400">{errors.location.message}</p>
            )}
          </div>

          {/* GPS Coordinates */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-slate-200">GPS Coordinates (Optional)</Label>
              {coordinates && (
                <Badge
                  variant="secondary"
                  className="bg-green-500/20 text-green-300 border-green-400"
                >
                  <MapPin className="h-3 w-3 mr-1" />
                  Located
                </Badge>
              )}
            </div>

            <div className="flex space-x-2">
              <Button
                type="button"
                size="sm"
                onClick={getCurrentLocation}
                disabled={isGettingLocation}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                <Navigation className="h-4 w-4 mr-2" />
                {isGettingLocation ? 'Getting Location...' : 'Use Current Location'}
              </Button>

              {coordinates && (
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={() => setCoordinates(undefined)}
                  className="bg-slate-600 border-slate-500 text-slate-200 hover:bg-slate-500"
                >
                  Clear
                </Button>
              )}
            </div>

            {coordinates && (
              <div className="bg-slate-700 rounded-lg p-3 border border-slate-600">
                <div className="flex items-center space-x-2 mb-1">
                  <MapPin className="h-3 w-3 text-orange-500" />
                  <span className="text-xs text-slate-300 font-medium">
                    Current Coordinates
                  </span>
                </div>
                <div className="text-sm text-slate-200 font-mono">
                  {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
                </div>
              </div>
            )}

            {locationError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{locationError}</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label className="text-slate-200">Notes (Optional)</Label>
            <Textarea
              {...register('notes')}
              placeholder="Add any additional notes about this status update..."
              rows={3}
              className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
            />
            {errors.notes && (
              <p className="text-sm text-red-400">{errors.notes.message}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex space-x-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Updating...
                </>
              ) : (
                <>
                  <User className="h-4 w-4 mr-2" />
                  Update Status
                </>
              )}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                className="bg-slate-600 border-slate-500 text-slate-200 hover:bg-slate-500"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}