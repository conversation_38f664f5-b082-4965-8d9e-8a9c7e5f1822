# Technical Assumptions

## Repository Structure: Monorepo

Single repository structure to manage the complete Next.js application with Supabase backend integration. This approach supports the integrated nature of the fruit export management system with shared components across different user interfaces.

## Service Architecture

**Next.js Full-Stack Application with Supabase Backend**: Monolithic frontend application built with Next.js App Router, leveraging Supabase as Backend-as-a-Service for authentication, database, storage, and real-time subscriptions. Server-side rendering for optimal performance with client-side interactivity for real-time features.

**Key Architectural Components:**
- **Frontend**: Next.js 14+ with App Router, TypeScript, Tailwind CSS for dark blue theme, ShadCN UI component library
- **Backend**: Supabase PostgreSQL with Row Level Security (RLS) policies
- **Authentication**: Supabase Auth with custom user metadata for role management
- **Real-time**: Supabase real-time subscriptions for status updates
- **Storage**: Supabase Storage for document and image management
- **Notifications**: Supabase Edge Functions for multi-channel communications
- **Mobile**: Progressive Web App (PWA) for offline driver capabilities

## Testing Requirements

**Unit + Integration Testing**: Comprehensive testing strategy including unit tests for business logic, integration tests for database operations and API endpoints, and end-to-end testing for critical user workflows. Manual testing support for mobile PWA functionality and multi-channel notifications.

**Testing Stack:**
- **Unit/Integration**: Jest, React Testing Library for frontend components
- **Database Testing**: Supabase local development environment for data layer testing
- **E2E Testing**: Playwright for cross-browser workflow validation
- **Manual Testing**: Device testing for mobile PWA and photo upload functionality

## Additional Technical Assumptions and Requests

- **Database Design**: Hybrid companies approach with base companies table and separate info tables for complex types (customers, carriers, factories) using JSONB metadata for simple types (shippers, consignees, notify parties, forwarder agents)
- **GPS Coordinates**: Hybrid storage using JSONB address field with dedicated point column for efficient geographic queries
- **Image Processing**: Automatic compression and thumbnail generation for photo uploads with 10MB size limits
- **Offline Support**: Service worker implementation for driver mobile interface with local data synchronization
- **Internationalization**: Built-in Thai/English language support with localized document generation
- **Performance**: Target sub-2 second page load times with Next.js optimization and Supabase edge caching
- **Security**: Comprehensive Row Level Security policies for multi-tenant data access control
- **Document Generation**: Server-side PDF generation using React PDF or similar library
- **Color Theme**: Exact implementation of dark blue color palette (#1e293b, #0f172a, #334155) with orange accents (#f97316)
