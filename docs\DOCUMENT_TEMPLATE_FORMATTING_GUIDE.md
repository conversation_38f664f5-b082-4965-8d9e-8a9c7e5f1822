# Document Template Formatting Guide

This guide explains how to format numbers, dates, and currencies in document templates.

## Overview

The document generation system supports various formatting options for numbers, dates, currencies, and text. You can use these formatting directives directly in your template HTML content.

## Number Formatting

### Syntax
```html
{{field_name|number:"format_pattern"}}
```

### Examples
```html
<!-- Quantity with 0 decimal places -->
{{shipment.total_quantity|number:"0"}} → 1,250

<!-- Weight with 2 decimal places -->
{{shipment.total_weight|number:"0.00"}} → 1,250.50

<!-- Volume with 3 decimal places -->
{{shipment.total_volume|number:"0.000"}} → 125.750

<!-- Integer formatting -->
{{container_count|number:"integer"}} → 5
```

**Note:** The formatting now processes both regular placeholders (like `{{field}}`) and formatted placeholders (like `{{field|format:"pattern"}}`) in a single pass, ensuring all values are properly populated.

### Supported Number Formats
- `"0"` or `"integer"` - Integer with thousand separators
- `"0.0"` - 1 decimal place with thousand separators
- `"0.00"` - 2 decimal places with thousand separators
- `"0.000"` - 3 decimal places with thousand separators

## Date Formatting

### Syntax
```html
{{date_field|date:"format_pattern"}}
```

### Examples
```html
<!-- Thai style: DD/MM/YYYY -->
{{shipment.etd_date|date:"DD/MM/YYYY"}} → 25/12/2024

<!-- US style: MM/DD/YYYY -->
{{shipment.eta_date|date:"MM/DD/YYYY"}} → 12/25/2024

<!-- ISO format: YYYY-MM-DD -->
{{shipment.closing_time|date:"YYYY-MM-DD"}} → 2024-12-25

<!-- With month name -->
{{shipment.created_at|date:"DD MMM YYYY"}} → 25 Dec 2024

<!-- US long format -->
{{shipment.updated_at|date:"MMM DD, YYYY"}} → Dec 25, 2024
```

### Supported Date Formats
- `"DD/MM/YYYY"` - 25/12/2024
- `"MM/DD/YYYY"` - 12/25/2024
- `"YYYY-MM-DD"` - 2024-12-25
- `"DD-MM-YYYY"` - 25-12-2024
- `"DD MMM YYYY"` - 25 Dec 2024
- `"MMM DD, YYYY"` - Dec 25, 2024
- `"YYYY/MM/DD"` - 2024/12/25

## Currency Formatting

### Syntax
```html
{{amount_field|currency:"currency_code"}}
```

### Examples
```html
<!-- US Dollar -->
{{shipment.total_value_cif|currency:"USD"}} → $1,250.50

<!-- Thai Baht -->
{{shipment.total_value_fob|currency:"THB"}} → ฿45,000.00

<!-- Euro -->
{{unit_price|currency:"EUR"}} → €850.25

<!-- Japanese Yen (no decimals) -->
{{total_amount|currency:"JPY"}} → ¥125,000
```

### Supported Currency Codes
- `"USD"` - US Dollar ($)
- `"THB"` - Thai Baht (฿)
- `"EUR"` - Euro (€)
- `"JPY"` - Japanese Yen (¥)
- `"GBP"` - British Pound (£)
- `"CNY"` - Chinese Yuan (¥)
- And more ISO 4217 currency codes

## Text Formatting

### Uppercase
```html
{{company.name|upper}} → COMPANY NAME LTD.
```

### Lowercase
```html
{{product.description|lower}} → fresh apples grade a
```

### Title Case
```html
{{shipper.contact_person_first_name|title}} → John Smith
```

## Loop Formatting

You can also use formatting inside loops for product lists, containers, etc.

### Loop Syntax
```html
{{#each array_field}}
  <!-- Loop content here -->
  {{this.field_name|format:"pattern"}}
  {{@index}} - Current index (0-based)
  {{@index1}} - Current index (1-based)
{{/each}}
```

### Loop Examples
```html
<!-- Product table with formatting -->
{{#each shipment.products}}
<tr>
  <td>{{@index1}}</td>
  <td>{{this.product_description|title}}</td>
  <td>{{this.quantity|number:"0"}}</td>
  <td>{{this.unit_price_cif|currency:"USD"}}</td>
  <td>{{this.total_value_cif|currency:"USD"}}</td>
  <td>{{this.gross_weight|number:"0.00"}} KG</td>
</tr>
{{/each}}

<!-- Container list with formatting -->
{{#each shipment.containers}}
<tr>
  <td>{{this.container_number|upper}}</td>
  <td>{{this.container_type}} {{this.container_size}}</td>
  <td>{{this.gross_weight|number:"0.00"}} KG</td>
  <td>{{this.volume|number:"0.000"}} CBM</td>
</tr>
{{/each}}
```

## Complex Examples

### Invoice Template Example
```html
<!DOCTYPE html>
<html>
<head>
    <title>Invoice CIF</title>
</head>
<body>
    <h1>COMMERCIAL INVOICE</h1>
    
    <div class="invoice-details">
        <p><strong>Invoice Date:</strong> {{shipment.created_at|date:"DD MMM YYYY"}}</p>
        <p><strong>Invoice Number:</strong> {{shipment.invoice_number}}</p>
    </div>
    
    <div class="company-info">
        <h3>{{shipper.name|upper}}</h3>
        <p>{{shipper.address.line1}}</p>
        <p>{{shipper.address.line2_2}}</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th>Unit Price (CIF)</th>
                <th>Total Amount</th>
            </tr>
        </thead>
        <tbody>
            {{#each shipment.products}}
            <tr>
                <td>{{this.product_description|title}}</td>
                <td>{{this.quantity|number:"0"}}</td>
                <td>{{this.unit_price_cif|currency:"USD"}}</td>
                <td>{{this.total_value_cif|currency:"USD"}}</td>
            </tr>
            {{/each}}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="3"><strong>TOTAL CIF VALUE:</strong></td>
                <td><strong>{{shipment.total_value_cif|currency:"USD"}}</strong></td>
            </tr>
        </tfoot>
    </table>
    
    <div class="shipping-info">
        <p><strong>ETD:</strong> {{shipment.etd_date|date:"DD/MM/YYYY"}}</p>
        <p><strong>ETA:</strong> {{shipment.eta_date|date:"DD/MM/YYYY"}}</p>
        <p><strong>Total Weight:</strong> {{shipment.total_weight|number:"0.00"}} KG</p>
        <p><strong>Total Volume:</strong> {{shipment.total_volume|number:"0.000"}} CBM</p>
    </div>
</body>
</html>
```

### Bill of Lading Template Example
```html
<!DOCTYPE html>
<html>
<head>
    <title>Bill of Lading</title>
</head>
<body>
    <h1>BILL OF LADING</h1>
    
    <div class="bl-header">
        <p><strong>B/L No:</strong> {{shipment.booking_number|upper}}</p>
        <p><strong>Date:</strong> {{shipment.created_at|date:"DD MMM YYYY"}}</p>
    </div>
    
    <div class="parties">
        <div class="shipper">
            <h3>SHIPPER:</h3>
            <p>{{shipper.name|upper}}</p>
            <p>{{shipper.address.full}}</p>
        </div>
        
        <div class="consignee">
            <h3>CONSIGNEE:</h3>
            <p>{{consignee.name|upper}}</p>
            <p>{{consignee.address.full}}</p>
        </div>
    </div>
    
    <div class="voyage-info">
        <p><strong>Vessel:</strong> {{shipment.vessel_name|upper}}</p>
        <p><strong>Voyage:</strong> {{shipment.voyage_number}}</p>
        <p><strong>Port of Loading:</strong> {{shipment.origin_port.name|title}}</p>
        <p><strong>Port of Discharge:</strong> {{shipment.destination_port.name|title}}</p>
    </div>
    
    <table class="cargo-details">
        <thead>
            <tr>
                <th>Container No.</th>
                <th>Description</th>
                <th>Gross Weight</th>
                <th>Measurement</th>
            </tr>
        </thead>
        <tbody>
            {{#each shipment.containers}}
            <tr>
                <td>{{this.container_number}}</td>
                <td>{{this.container_type}} {{this.container_size}}</td>
                <td>{{this.gross_weight|number:"0.00"}} KG</td>
                <td>{{this.volume|number:"0.000"}} CBM</td>
            </tr>
            {{/each}}
        </tbody>
    </table>
</body>
</html>
```

## Best Practices

1. **Always specify formatting** for numbers and dates to ensure consistency
2. **Use appropriate currency codes** that match your business requirements
3. **Test templates thoroughly** with various data scenarios
4. **Consider localization** when choosing date formats
5. **Use uppercase formatting** for official document headers and company names
6. **Apply title case** for names and descriptions to improve readability

## Template Configuration

In the document template settings, you can also set default formatting:

- **Number Format**: Default format for all numbers in the template
- **Date Format**: Default format for all dates in the template  
- **Currency Format**: Default currency code for all amounts in the template

Inline formatting (using `|` operators) will override template defaults.

## Error Handling

If a value cannot be formatted (e.g., invalid date, non-numeric value), the system will:

1. Return the original value unchanged
2. Log a warning for debugging
3. Continue processing the rest of the template

This ensures document generation doesn't fail due to formatting issues.