import { describe, expect, it, beforeEach, vi, afterEach } from 'vitest'
import { act, renderHook } from '@testing-library/react'
import { 
  useConsigneeNotifyPartyStore, 
  type ConsigneeNotifyParty, 
  type ConsigneeNotifyPartyInsert 
} from '@/stores/consignee-notify-party-store'

// Mock Supabase client
const mockSupabaseClient = {
  from: vi.fn(() => mockSupabaseClient),
  select: vi.fn(() => mockSupabaseClient),
  insert: vi.fn(() => mockSupabaseClient),
  update: vi.fn(() => mockSupabaseClient),
  delete: vi.fn(() => mockSupabaseClient),
  eq: vi.fn(() => mockSupabaseClient),
  neq: vi.fn(() => mockSupabaseClient),
  in: vi.fn(() => mockSupabaseClient),
  or: vi.fn(() => mockSupabaseClient),
  ilike: vi.fn(() => mockSupabaseClient),
  order: vi.fn(() => mockSupabaseClient),
  range: vi.fn(() => mockSupabaseClient),
  single: vi.fn(() => Promise.resolve({ data: null, error: null })),
  rpc: vi.fn(() => Promise.resolve({ data: null, error: null })),
  channel: vi.fn(() => ({
    on: vi.fn(() => ({ subscribe: vi.fn() })),
    subscribe: vi.fn(),
    unsubscribe: vi.fn(),
  })),
}

vi.mock('@/lib/supabase/client', () => ({
  createClient: () => mockSupabaseClient,
}))

// Sample test data
const mockConsignee = {
  id: '550e8400-e29b-41d4-a716-446655440000',
  name: 'Test Consignee Co.',
  company_type: 'consignee' as const,
  contact_phone: '+66-2-123-4567',
}

const mockNotifyParty = {
  id: '550e8400-e29b-41d4-a716-446655440001',
  name: 'Test Notify Party Ltd.',
  company_type: 'notify_party' as const,
  contact_phone: '+66-2-765-4321',
  contact_email: '<EMAIL>',
}

const mockRelationship: ConsigneeNotifyParty = {
  id: '550e8400-e29b-41d4-a716-446655440002',
  consignee_id: mockConsignee.id,
  notify_party_id: mockNotifyParty.id,
  is_default: false,
  is_active: true,
  notification_preferences: {
    email: true,
    sms: false,
    line: true,
    wechat: false,
  },
  priority_order: 1,
  special_instructions: 'Contact by email first',
  notes: 'Primary contact for shipment updates',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  consignee: mockConsignee,
  notify_party: mockNotifyParty,
}

const mockRelationships: ConsigneeNotifyParty[] = [
  mockRelationship,
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    consignee_id: mockConsignee.id,
    notify_party_id: '550e8400-e29b-41d4-a716-446655440004',
    is_default: true,
    is_active: true,
    notification_preferences: {
      email: true,
      sms: true,
      line: false,
      wechat: false,
    },
    priority_order: 2,
    special_instructions: 'Default notify party - use SMS for urgent matters',
    notes: 'Secondary contact with higher priority',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    consignee: mockConsignee,
    notify_party: {
      id: '550e8400-e29b-41d4-a716-446655440004',
      name: 'Default Notify Party Inc.',
      company_type: 'notify_party' as const,
      contact_phone: '+66-2-888-9999',
      contact_email: '<EMAIL>',
    },
  },
]

describe('Consignee-Notify Party Store Integration', () => {
  beforeEach(() => {
    // Reset the store state
    useConsigneeNotifyPartyStore.getState().reset()
    
    // Reset all mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('fetchRelationships', () => {
    it('should fetch relationships successfully', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockRelationships,
        count: mockRelationships.length,
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        await result.current.fetchRelationships()
      })

      expect(result.current.relationships).toEqual(mockRelationships)
      expect(result.current.totalCount).toBe(mockRelationships.length)
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle fetch error', async () => {
      const errorMessage = 'Failed to fetch relationships'
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: null,
        error: { message: errorMessage },
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        await result.current.fetchRelationships()
      })

      expect(result.current.relationships).toEqual([])
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(errorMessage)
    })

    it('should apply consignee filter', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: [mockRelationship],
        count: 1,
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        result.current.setFilter({ consignee_id: mockConsignee.id })
      })

      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('consignee_id', mockConsignee.id)
    })

    it('should apply active filter', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockRelationships,
        count: mockRelationships.length,
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        result.current.setFilter({ is_active: true })
      })

      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('is_active', true)
    })

    it('should apply search filter', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: [mockRelationship],
        count: 1,
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        result.current.setSearchTerm('Test Consignee')
      })

      // Wait for debounced search
      await new Promise(resolve => setTimeout(resolve, 350))

      expect(mockSupabaseClient.or).toHaveBeenCalledWith(
        expect.stringContaining('Test Consignee')
      )
    })

    it('should handle sorting', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockRelationships,
        count: mockRelationships.length,
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        result.current.setSorting('priority_order', 'desc')
      })

      expect(mockSupabaseClient.order).toHaveBeenCalledWith('priority_order', { ascending: false })
    })

    it('should handle pagination', async () => {
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockRelationships,
        count: mockRelationships.length,
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        result.current.setPage(2)
      })

      expect(mockSupabaseClient.range).toHaveBeenCalledWith(20, 39) // Page 2, pageSize 20
    })
  })

  describe('createRelationship', () => {
    it('should create relationship successfully', async () => {
      const newRelationshipData: ConsigneeNotifyPartyInsert = {
        consignee_id: mockConsignee.id,
        notify_party_id: mockNotifyParty.id,
        is_default: false,
        is_active: true,
        notification_preferences: {
          email: true,
          sms: true,
          line: false,
          wechat: false,
        },
        priority_order: 3,
        special_instructions: 'New relationship instructions',
        notes: 'Testing relationship creation',
      }

      const createdRelationship = {
        ...newRelationshipData,
        id: '550e8400-e29b-41d4-a716-446655440005',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        consignee: mockConsignee,
        notify_party: mockNotifyParty,
      }

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock unique check - no existing relationship found
      mockSupabaseClient.single
        .mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116', message: 'Row not found' },
        })
        // Mock insert
        .mockResolvedValueOnce({
          data: createdRelationship,
          error: null,
        })
        // Mock refresh call
        .mockResolvedValueOnce({
          data: [...mockRelationships, createdRelationship],
          count: mockRelationships.length + 1,
          error: null,
        })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      let returnedRelationship: ConsigneeNotifyParty
      await act(async () => {
        returnedRelationship = await result.current.createRelationship(newRelationshipData)
      })

      expect(returnedRelationship!).toEqual(createdRelationship)
      expect(result.current.isCreating).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle duplicate relationship error', async () => {
      const newRelationshipData: ConsigneeNotifyPartyInsert = {
        consignee_id: mockConsignee.id,
        notify_party_id: mockNotifyParty.id, // Duplicate pair
        notification_preferences: {
          email: true,
        },
      }

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock unique check - returns existing relationship
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: { id: 'existing-id' },
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        try {
          await result.current.createRelationship(newRelationshipData)
          // Should not reach here
          expect(false).toBe(true)
        } catch (error) {
          expect(error).toBeInstanceOf(Error)
          expect((error as Error).message).toContain('already exists')
        }
      })

      expect(result.current.isCreating).toBe(false)
      expect(result.current.error).toContain('already exists')
    })

    it('should handle default designation toggle', async () => {
      const newDefaultRelationshipData: ConsigneeNotifyPartyInsert = {
        consignee_id: mockConsignee.id,
        notify_party_id: '550e8400-e29b-41d4-a716-446655440006',
        is_default: true, // Setting as new default
        notification_preferences: {
          email: true,
        },
      }

      const createdRelationship = {
        ...newDefaultRelationshipData,
        id: '550e8400-e29b-41d4-a716-446655440007',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock unique check
      mockSupabaseClient.single
        .mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116', message: 'Row not found' },
        })
        // Mock reset default operation
        .mockResolvedValueOnce({
          error: null,
        })
        // Mock insert
        .mockResolvedValueOnce({
          data: createdRelationship,
          error: null,
        })
        // Mock refresh
        .mockResolvedValueOnce({
          data: [...mockRelationships, createdRelationship],
          count: mockRelationships.length + 1,
          error: null,
        })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        await result.current.createRelationship(newDefaultRelationshipData)
      })

      // Should have called to reset other defaults for the same consignee
      expect(mockSupabaseClient.update).toHaveBeenCalledWith(
        expect.objectContaining({ is_default: false })
      )
      expect(result.current.isCreating).toBe(false)
    })

    it('should validate notification preferences', async () => {
      const invalidRelationshipData: ConsigneeNotifyPartyInsert = {
        consignee_id: mockConsignee.id,
        notify_party_id: mockNotifyParty.id,
        notification_preferences: {
          email: false,
          sms: false,
          line: false,
          wechat: false, // All disabled - should fail validation
        },
      }

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        try {
          await result.current.createRelationship(invalidRelationshipData)
          expect(false).toBe(true) // Should not reach here
        } catch (error) {
          expect(error).toBeInstanceOf(Error)
          expect((error as Error).message).toContain('at least one notification channel')
        }
      })
    })
  })

  describe('updateRelationship', () => {
    it('should update relationship successfully', async () => {
      const updates = {
        special_instructions: 'Updated instructions',
        notification_preferences: {
          email: true,
          sms: true,
          line: true,
          wechat: false,
        },
        priority_order: 5,
      }

      const updatedRelationship = {
        ...mockRelationship,
        ...updates,
        updated_at: '2024-01-02T00:00:00Z',
      }

      mockSupabaseClient.single.mockResolvedValueOnce({
        data: updatedRelationship,
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      // Set initial relationships
      act(() => {
        result.current.relationships = [mockRelationship]
      })

      let returnedRelationship: ConsigneeNotifyParty
      await act(async () => {
        returnedRelationship = await result.current.updateRelationship(mockRelationship.id, updates)
      })

      expect(returnedRelationship!).toEqual(updatedRelationship)
      expect(result.current.isUpdating).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle default designation change', async () => {
      const updates = {
        is_default: true, // Changing to default
      }

      const updatedRelationship = {
        ...mockRelationship,
        ...updates,
        updated_at: '2024-01-02T00:00:00Z',
      }

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock reset default operation
      mockSupabaseClient.single
        .mockResolvedValueOnce({
          error: null,
        })
        // Mock update
        .mockResolvedValueOnce({
          data: updatedRelationship,
          error: null,
        })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        await result.current.updateRelationship(mockRelationship.id, updates)
      })

      // Should have called to reset other defaults
      expect(mockSupabaseClient.update).toHaveBeenCalledWith(
        expect.objectContaining({ is_default: false })
      )
      expect(mockSupabaseClient.neq).toHaveBeenCalledWith('id', mockRelationship.id)
    })

    it('should validate notification preferences on update', async () => {
      const invalidUpdates = {
        notification_preferences: {
          email: false,
          sms: false,
          line: false,
          wechat: false, // All disabled
        },
      }

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        try {
          await result.current.updateRelationship(mockRelationship.id, invalidUpdates)
          expect(false).toBe(true) // Should not reach here
        } catch (error) {
          expect(error).toBeInstanceOf(Error)
          expect((error as Error).message).toContain('at least one notification channel')
        }
      })
    })
  })

  describe('deleteRelationship', () => {
    it('should soft delete relationship successfully', async () => {
      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock the complete chain: from().update().eq()
      mockSupabaseClient.update.mockReturnValueOnce(mockSupabaseClient)
      mockSupabaseClient.eq.mockResolvedValueOnce({
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      // Set initial relationships
      act(() => {
        result.current.relationships = [mockRelationship]
      })

      await act(async () => {
        await result.current.deleteRelationship(mockRelationship.id)
      })

      expect(mockSupabaseClient.update).toHaveBeenCalledWith(
        expect.objectContaining({ is_active: false })
      )
      expect(result.current.relationships).toHaveLength(0)
      expect(result.current.isDeleting).toBe(false)
    })

    it('should handle delete error', async () => {
      const errorMessage = 'Failed to delete relationship'
      
      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock the complete chain with error
      mockSupabaseClient.update.mockReturnValueOnce(mockSupabaseClient)
      mockSupabaseClient.eq.mockResolvedValueOnce({
        error: { message: errorMessage },
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        try {
          await result.current.deleteRelationship(mockRelationship.id)
          // Should not reach here
          expect(false).toBe(true)
        } catch (error) {
          // Error is expected to be thrown
          expect(error).toBeInstanceOf(Error)
        }
      })

      expect(result.current.isDeleting).toBe(false)
      expect(result.current.error).toBe(errorMessage)
    })
  })

  describe('deleteRelationships (bulk)', () => {
    it('should bulk delete relationships successfully', async () => {
      const idsToDelete = ['id1', 'id2']

      // Reset mocks for this test
      vi.clearAllMocks()

      // Mock the complete chain: from().update().in()
      mockSupabaseClient.update.mockReturnValueOnce(mockSupabaseClient)
      mockSupabaseClient.in.mockResolvedValueOnce({
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      // Set initial relationships
      act(() => {
        result.current.relationships = mockRelationships
      })

      await act(async () => {
        await result.current.deleteRelationships(idsToDelete)
      })

      expect(mockSupabaseClient.in).toHaveBeenCalledWith('id', idsToDelete)
      expect(result.current.selectedRelationships.size).toBe(0)
      expect(result.current.isDeleting).toBe(false)
    })
  })

  describe('Selection Management', () => {
    it('should handle relationship selection', () => {
      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      act(() => {
        result.current.selectRelationship(mockRelationship.id)
      })

      expect(result.current.selectedRelationships.has(mockRelationship.id)).toBe(true)

      act(() => {
        result.current.deselectRelationship(mockRelationship.id)
      })

      expect(result.current.selectedRelationships.has(mockRelationship.id)).toBe(false)
    })

    it('should select all relationships', () => {
      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      // Set initial relationships
      act(() => {
        result.current.relationships = mockRelationships
        result.current.selectAllRelationships()
      })

      mockRelationships.forEach(relationship => {
        expect(result.current.selectedRelationships.has(relationship.id)).toBe(true)
      })
    })

    it('should clear selection', () => {
      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      // Select relationships first
      act(() => {
        result.current.relationships = mockRelationships
        result.current.selectAllRelationships()
        result.current.clearSelection()
      })

      expect(result.current.selectedRelationships.size).toBe(0)
    })
  })

  describe('Priority Management', () => {
    it('should reorder relationship priorities', async () => {
      const sourceId = mockRelationships[0].id
      const targetId = mockRelationships[1].id

      // Mock the reordering query
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: mockRelationships,
        error: null,
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        await result.current.reorderRelationshipPriority(sourceId, targetId)
      })

      expect(mockSupabaseClient.update).toHaveBeenCalled()
      expect(result.current.isUpdating).toBe(false)
    })

    it('should validate priority bounds', async () => {
      const invalidPriority = 1001 // Exceeds maximum

      const updates = {
        priority_order: invalidPriority,
        notification_preferences: {
          email: true,
        },
      }

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        try {
          await result.current.updateRelationship(mockRelationship.id, updates)
          expect(false).toBe(true) // Should not reach here
        } catch (error) {
          expect(error).toBeInstanceOf(Error)
          expect((error as Error).message).toContain('Priority order cannot exceed 999')
        }
      })
    })
  })

  describe('Real-time Subscription', () => {
    it('should setup subscription correctly', () => {
      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      const unsubscribe = result.current.subscribeToRelationships()

      expect(mockSupabaseClient.channel).toHaveBeenCalledWith('consignee_notify_parties_changes')
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('Company Type Validation', () => {
    it('should validate consignee company type on create', async () => {
      const invalidConsigneeData: ConsigneeNotifyPartyInsert = {
        consignee_id: 'invalid-consignee-type-id', // Should be consignee type
        notify_party_id: mockNotifyParty.id,
        notification_preferences: {
          email: true,
        },
      }

      // Mock company type validation failure
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Company type validation failed' },
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        try {
          await result.current.createRelationship(invalidConsigneeData)
          expect(false).toBe(true) // Should not reach here
        } catch (error) {
          expect(error).toBeInstanceOf(Error)
          expect((error as Error).message).toContain('consignee type')
        }
      })
    })

    it('should validate notify party company type on create', async () => {
      const invalidNotifyPartyData: ConsigneeNotifyPartyInsert = {
        consignee_id: mockConsignee.id,
        notify_party_id: 'invalid-notify-party-type-id', // Should be notify_party type
        notification_preferences: {
          email: true,
        },
      }

      // Mock company type validation failure
      mockSupabaseClient.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Company type validation failed' },
      })

      const { result } = renderHook(() => useConsigneeNotifyPartyStore())

      await act(async () => {
        try {
          await result.current.createRelationship(invalidNotifyPartyData)
          expect(false).toBe(true) // Should not reach here
        } catch (error) {
          expect(error).toBeInstanceOf(Error)
          expect((error as Error).message).toContain('notify_party type')
        }
      })
    })
  })
})