# API Specification

Based on the Supabase client API style from the Tech Stack, the system uses Supabase's auto-generated REST API with type-safe TypeScript client libraries. This approach provides real-time subscriptions, Row Level Security integration, and automatic TypeScript type generation.

## Supabase Client API

The API follows Supabase's client-side approach where the frontend directly queries the database through the Supabase client, with security enforced via Row Level Security policies rather than traditional API endpoints.

### Core API Patterns

```typescript
// Supabase Client Configuration
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Shipment Management API
export class ShipmentService {
  // Create shipment with intelligent pre-population
  async createShipment(shipmentData: Omit<Shipment, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('shipments')
      .insert([shipmentData])
      .select(`
        *,
        customer:customer_id (*),
        shipper:shipper_id (*),
        consignee:consignee_id (*),
        notify_party:notify_party_id (*),
        factory:factory_id (*),
        forwarder_agent:forwarder_agent_id (*),
        origin_port:origin_port_id (*),
        destination_port:destination_port_id (*)
      `)
      .single()
    
    if (error) throw error
    return data
  }

  // Real-time shipment status updates
  subscribeToShipmentUpdates(shipmentId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`shipment-${shipmentId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'shipments',
        filter: `id=eq.${shipmentId}`
      }, callback)
      .subscribe()
  }
}

// Driver Mobile API
export class DriverService {
  // Update shipment status with photo and GPS
  async updateShipmentStatus(statusUpdate: {
    shipment_id: string
    status_to: string
    notes?: string
    location?: string
    latitude?: number
    longitude?: number
    photos: File[]
  }) {
    // Upload photos to Supabase Storage
    const photoUploads = await Promise.all(
      statusUpdate.photos.map(async (photo, index) => {
        const fileName = `${statusUpdate.shipment_id}-${Date.now()}-${index}.jpg`
        const { data, error } = await supabase.storage
          .from('status-photos')
          .upload(fileName, photo, {
            cacheControl: '3600',
            upsert: false
          })
        
        if (error) throw error
        return {
          path: data.path,
          url: supabase.storage.from('status-photos').getPublicUrl(data.path).data.publicUrl
        }
      })
    )

    // Create status history record and update shipment
    const { data: statusHistory, error: statusError } = await supabase
      .from('status_history')
      .insert([{
        shipment_id: statusUpdate.shipment_id,
        status_to: statusUpdate.status_to,
        notes: statusUpdate.notes,
        location: statusUpdate.location,
        latitude: statusUpdate.latitude,
        longitude: statusUpdate.longitude
      }])
      .select()
      .single()

    if (statusError) throw statusError
    return statusHistory
  }
}
```
