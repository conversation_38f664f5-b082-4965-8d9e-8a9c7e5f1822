'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Ship,
  Package,
  TrendingUp,
  TrendingDown,
  BarChart3,
  FileDown,
  Calendar,
  Filter,
  Search,
  Globe,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Truck,
  Plane,
  Activity,
  Download
} from 'lucide-react'

// Mock data for demonstration
const mockShipmentStats = {
  totalShipments: 1247,
  activeShipments: 85,
  completedThisMonth: 126,
  pendingApproval: 12,
  revenue: '$2,847,650',
  avgDeliveryTime: '12.3 days'
}

const mockShipmentsByStatus = [
  { status: 'Completed', count: 745, percentage: 59.7, color: 'bg-green-500' },
  { status: 'In Transit', count: 85, percentage: 6.8, color: 'bg-blue-500' },
  { status: 'Pending', count: 267, percentage: 21.4, color: 'bg-yellow-500' },
  { status: 'Cancelled', count: 150, percentage: 12.1, color: 'bg-red-500' }
]

const mockShipmentsByMode = [
  { mode: 'Sea', count: 892, percentage: 71.5, icon: Ship },
  { mode: 'Land', count: 278, percentage: 22.3, icon: Truck },
  { mode: 'Air', count: 77, percentage: 6.2, icon: Plane }
]

const mockTopRoutes = [
  { route: 'Bangkok → Los Angeles', shipments: 145, revenue: '$486,750' },
  { route: 'Bangkok → Hamburg', shipments: 98, revenue: '$324,870' },
  { route: 'Chiang Mai → Tokyo', shipments: 76, revenue: '$287,650' },
  { route: 'Bangkok → Sydney', shipments: 65, revenue: '$245,980' },
  { route: 'Phuket → Vancouver', shipments: 54, revenue: '$198,340' }
]

const mockRecentShipments = [
  {
    id: 'EXSEA-BKK-240925-001',
    customer: 'Golden Fruits Ltd.',
    route: 'Bangkok → Los Angeles',
    status: 'In Transit',
    etd: '2024-09-25',
    eta: '2024-10-08',
    value: '$45,890'
  },
  {
    id: 'EXLAN-CMI-240924-008',
    customer: 'Fresh Export Co.',
    route: 'Chiang Mai → Tokyo',
    status: 'Customs Clearance',
    etd: '2024-09-24',
    eta: '2024-09-26',
    value: '$23,450'
  },
  {
    id: 'EXSEA-PKT-240923-015',
    customer: 'Tropical Harvest Inc.',
    route: 'Phuket → Vancouver',
    status: 'Delivered',
    etd: '2024-09-10',
    eta: '2024-09-23',
    value: '$67,230'
  },
  {
    id: 'EXAIR-BKK-240922-003',
    customer: 'Premium Fruits Asia',
    route: 'Bangkok → Dubai',
    status: 'Booking Confirmed',
    etd: '2024-09-28',
    eta: '2024-09-29',
    value: '$89,560'
  }
]

const StatCard = ({ title, value, trend, icon: Icon, color, subtitle }: {
  title: string
  value: string
  trend?: string
  icon: any
  color: string
  subtitle?: string
}) => {
  const isPositive = trend?.startsWith('+')
  const isNegative = trend?.startsWith('-')

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-slate-400 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-white mt-1">{value}</p>
            {subtitle && <p className="text-xs text-slate-500 mt-1">{subtitle}</p>}
            {trend && (
              <div className="flex items-center mt-2">
                {isPositive && <TrendingUp className="w-3 h-3 text-green-500 mr-1" />}
                {isNegative && <TrendingDown className="w-3 h-3 text-red-500 mr-1" />}
                <span className={`text-xs font-medium ${
                  isPositive ? 'text-green-500' : isNegative ? 'text-red-500' : 'text-slate-400'
                }`}>
                  {trend}
                </span>
              </div>
            )}
          </div>
          <Icon className={`w-8 h-8 ${color}`} />
        </div>
      </CardContent>
    </Card>
  )
}

const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-green-500 text-white'
      case 'in transit':
        return 'bg-blue-500 text-white'
      case 'customs clearance':
        return 'bg-yellow-500 text-black'
      case 'booking confirmed':
        return 'bg-purple-500 text-white'
      default:
        return 'bg-slate-500 text-white'
    }
  }

  return (
    <Badge className={`${getStatusColor(status)} text-xs`}>
      {status}
    </Badge>
  )
}

export default function ShipmentReportsPage() {
  const [dateRange, setDateRange] = useState('30')
  const [transportMode, setTransportMode] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Shipment Reports</h1>
          <p className="text-slate-400">Comprehensive analytics for your shipping operations</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
            <FileDown className="w-4 h-4 mr-2" />
            Export Data
          </Button>
          <Button className="bg-orange-500 hover:bg-orange-600 text-white">
            <Download className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700 mb-6">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-slate-400" />
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 3 months</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Truck className="w-4 h-4 text-slate-400" />
              <Select value={transportMode} onValueChange={setTransportMode}>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Modes</SelectItem>
                  <SelectItem value="sea">Sea Transport</SelectItem>
                  <SelectItem value="land">Land Transport</SelectItem>
                  <SelectItem value="air">Air Transport</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-slate-400" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in_transit">In Transit</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-3 text-slate-400" />
                <Input
                  placeholder="Search shipments..."
                  className="pl-10 bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <StatCard
          title="Total Shipments"
          value={mockShipmentStats.totalShipments.toLocaleString()}
          trend="+12.5%"
          icon={Ship}
          color="text-blue-500"
          subtitle="All time"
        />
        <StatCard
          title="Active Shipments"
          value={mockShipmentStats.activeShipments.toString()}
          trend="+8.3%"
          icon={Activity}
          color="text-green-500"
          subtitle="Currently in transit"
        />
        <StatCard
          title="Completed (30d)"
          value={mockShipmentStats.completedThisMonth.toString()}
          trend="+15.2%"
          icon={CheckCircle}
          color="text-green-500"
          subtitle="This month"
        />
        <StatCard
          title="Pending Approval"
          value={mockShipmentStats.pendingApproval.toString()}
          trend="-5.1%"
          icon={Clock}
          color="text-yellow-500"
          subtitle="Awaiting review"
        />
        <StatCard
          title="Total Revenue"
          value={mockShipmentStats.revenue}
          trend="+18.7%"
          icon={BarChart3}
          color="text-orange-500"
          subtitle="This month"
        />
        <StatCard
          title="Avg Delivery Time"
          value={mockShipmentStats.avgDeliveryTime}
          trend="-2.3 days"
          icon={Clock}
          color="text-purple-500"
          subtitle="Last 30 days"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Shipments by Status */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Activity className="w-5 h-5 mr-2 text-blue-500" />
              Shipments by Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockShipmentsByStatus.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${item.color}`} />
                    <span className="text-slate-300 text-sm">{item.status}</span>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-medium">{item.count}</p>
                    <p className="text-slate-400 text-xs">{item.percentage}%</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Shipments by Transport Mode */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Globe className="w-5 h-5 mr-2 text-green-500" />
              Transport Modes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockShipmentsByMode.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <item.icon className="w-4 h-4 text-orange-500" />
                    <span className="text-slate-300 text-sm">{item.mode}</span>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-medium">{item.count}</p>
                    <p className="text-slate-400 text-xs">{item.percentage}%</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Routes */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <MapPin className="w-5 h-5 mr-2 text-purple-500" />
              Top Routes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockTopRoutes.slice(0, 5).map((route, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-slate-700 last:border-b-0">
                  <div>
                    <p className="text-slate-300 text-sm font-medium">{route.route}</p>
                    <p className="text-slate-500 text-xs">{route.shipments} shipments</p>
                  </div>
                  <p className="text-orange-400 text-sm font-medium">{route.revenue}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Shipments Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Package className="w-5 h-5 mr-2 text-blue-500" />
            Recent Shipments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-slate-700">
                  <th className="text-left text-slate-400 font-medium py-3 px-4">Shipment ID</th>
                  <th className="text-left text-slate-400 font-medium py-3 px-4">Customer</th>
                  <th className="text-left text-slate-400 font-medium py-3 px-4">Route</th>
                  <th className="text-left text-slate-400 font-medium py-3 px-4">Status</th>
                  <th className="text-left text-slate-400 font-medium py-3 px-4">ETD</th>
                  <th className="text-left text-slate-400 font-medium py-3 px-4">ETA</th>
                  <th className="text-right text-slate-400 font-medium py-3 px-4">Value</th>
                </tr>
              </thead>
              <tbody>
                {mockRecentShipments.map((shipment, index) => (
                  <tr key={index} className="border-b border-slate-700 hover:bg-slate-700 transition-colors">
                    <td className="py-3 px-4">
                      <span className="text-blue-400 font-mono text-sm">{shipment.id}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-slate-300">{shipment.customer}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-slate-300 text-sm">{shipment.route}</span>
                    </td>
                    <td className="py-3 px-4">
                      <StatusBadge status={shipment.status} />
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-slate-300 text-sm">{shipment.etd}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-slate-300 text-sm">{shipment.eta}</span>
                    </td>
                    <td className="py-3 px-4 text-right">
                      <span className="text-orange-400 font-medium">{shipment.value}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="flex items-center justify-between mt-4 pt-4 border-t border-slate-700">
            <p className="text-slate-400 text-sm">Showing 4 of 1,247 shipments</p>
            <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
              View All Shipments
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}