'use client'

import { create } from 'zustand'
import { createClient } from '@/lib/supabase/client'
import type {
  PortFilter,
  GeographicSearch,
  Coordinates,
} from '@/lib/validations/ports'

// Port types (these should be moved to types.ts when database types are generated)
export interface Port {
  id: string
  code: string
  name: string
  city: string | null
  country: string
  port_type: 'origin' | 'destination' | 'transit'
  gps_coordinates: string | object | null // PostGIS geography as string or JSON object
  timezone: string | null
  is_active: boolean | null
  created_at: string | null
  updated_at: string | null
}

export interface PortInsert {
  id?: string
  code: string
  name: string
  city?: string | null
  country: string
  port_type: 'origin' | 'destination' | 'transit'
  gps_coordinates?: string | null
  timezone?: string | null
  is_active?: boolean | null
  created_at?: string | null
  updated_at?: string | null
}

export interface PortUpdate {
  id?: string
  code?: string
  name?: string
  city?: string | null
  country?: string
  port_type?: 'origin' | 'destination' | 'transit'
  gps_coordinates?: string | null
  timezone?: string | null
  is_active?: boolean | null
  created_at?: string | null
  updated_at?: string | null
}

export interface PortWithDistance extends Port {
  distance_km?: number
}

interface PortState {
  // Data state
  ports: Port[]
  loading: boolean
  error: string | null

  // Filter and search state
  filter: PortFilter
  searchTerm: string
  sortBy: 'name' | 'code' | 'country' | 'port_type' | 'created_at'
  sortOrder: 'asc' | 'desc'

  // Pagination state
  currentPage: number
  pageSize: number
  totalCount: number

  // Geographic search state
  geographicSearch: GeographicSearch | null
  nearbyPorts: PortWithDistance[]
  geographicLoading: boolean

  // UI state
  selectedPorts: Set<string>
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean

  // Actions
  fetchPorts: () => Promise<void>
  fetchPortById: (id: string) => Promise<Port | null>
  createPort: (port: PortInsert) => Promise<Port>
  updatePort: (id: string, updates: Partial<PortUpdate>) => Promise<Port>
  deletePort: (id: string) => Promise<void>
  deletePorts: (ids: string[]) => Promise<void>

  // Geographic search actions
  searchPortsWithinRadius: (
    search: GeographicSearch
  ) => Promise<PortWithDistance[]>
  getPortsByCountry: (
    country: string,
    referenceCoords?: Coordinates
  ) => Promise<PortWithDistance[]>
  calculatePortDistance: (port1Id: string, port2Id: string) => Promise<number>

  // Filter and search actions
  setFilter: (filter: Partial<PortFilter>) => void
  setSearchTerm: (term: string) => void
  setSorting: (
    sortBy: PortState['sortBy'],
    sortOrder: PortState['sortOrder']
  ) => void

  // Pagination actions
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Selection actions
  selectPort: (id: string) => void
  deselectPort: (id: string) => void
  selectAllPorts: () => void
  clearSelection: () => void

  // Utility actions
  clearError: () => void
  clearGeographicSearch: () => void
  reset: () => void

  // Real-time subscription management
  subscribeToPorts: () => () => void
}

const initialState = {
  ports: [],
  loading: false,
  error: null,
  filter: {},
  searchTerm: '',
  sortBy: 'name' as const,
  sortOrder: 'asc' as const,
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  geographicSearch: null,
  nearbyPorts: [],
  geographicLoading: false,
  selectedPorts: new Set<string>(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
}

export const usePortStore = create<PortState>((set, get) => ({
  ...initialState,

  // Fetch ports with filters, search, and pagination
  fetchPorts: async () => {
    set({ loading: true, error: null })

    try {
      const supabase = createClient()
      const { filter, searchTerm, sortBy, sortOrder, currentPage, pageSize } =
        get()

      let query = supabase
        .from('ports_with_text_coordinates')
        .select(
          'id, code, name, city, country, port_type, gps_coordinates:gps_coordinates_text, timezone, is_active, created_at, updated_at',
          { count: 'exact' }
        )

      // Apply filters
      if (filter.port_type) {
        query = query.eq('port_type', filter.port_type)
      }

      if (filter.country) {
        query = query.eq('country', filter.country)
      }

      if (filter.is_active !== undefined) {
        query = query.eq('is_active', filter.is_active)
      } else {
        // Default to show only active ports
        query = query.eq('is_active', true)
      }

      // Apply search
      if (searchTerm.trim()) {
        query = query.or(
          `name.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%,city.ilike.%${searchTerm}%,country.ilike.%${searchTerm}%`
        )
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })

      // Apply pagination
      const from = (currentPage - 1) * pageSize
      const to = from + pageSize - 1
      query = query.range(from, to)

      const { data, count, error } = await query

      if (error) {
        throw new Error(error.message)
      }

      set({
        ports: data || [],
        totalCount: count || 0,
        loading: false,
      })
    } catch (error) {
      console.error('Error fetching ports:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch ports',
        loading: false,
      })
    }
  },

  // Fetch single port by ID
  fetchPortById: async (id: string) => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('ports_with_text_coordinates')
        .select(
          'id, code, name, city, country, port_type, gps_coordinates:gps_coordinates_text, timezone, is_active, created_at, updated_at'
        )
        .eq('id', id)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      return data
    } catch (error) {
      console.error('Error fetching port:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch port',
      })
      return null
    }
  },

  // Create new port
  createPort: async (portData: PortInsert) => {
    set({ isCreating: true, error: null })

    try {
      const supabase = createClient()

      // Check for duplicate code - race condition prevention
      const { data: existing } = await supabase
        .from('ports')
        .select('id')
        .eq('code', portData.code.toUpperCase())
        .eq('is_active', true)
        .single()

      if (existing) {
        throw new Error(`Port with code "${portData.code}" already exists`)
      }

      // Convert coordinates to PostGIS geography point if provided
      let gpsCoordinates = null
      if (portData.gps_coordinates) {
        // If gps_coordinates is passed as JSON string from form, parse and convert
        try {
          const coords = JSON.parse(JSON.parse(portData.gps_coordinates))
          if (coords.lat && coords.lng) {
            // For geography columns, use raw WKT format - Supabase handles the conversion
            gpsCoordinates = `SRID=4326;POINT(${coords.lng} ${coords.lat})`
          }
        } catch {
          // If it's already a PostGIS geography string, use as is
          gpsCoordinates = portData.gps_coordinates
        }
      }

      const { data, error } = await supabase
        .from('ports')
        .insert({
          ...portData,
          code: portData.code.toUpperCase(),
          gps_coordinates: gpsCoordinates,
        })
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      // Refresh the list to show the new port
      await get().fetchPorts()

      set({ isCreating: false })
      return data
    } catch (error) {
      console.error('Error creating port:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to create port',
        isCreating: false,
      })
      throw error
    }
  },

  // Update existing port
  updatePort: async (id: string, updates: Partial<PortUpdate>) => {
    set({ isUpdating: true, error: null })

    try {
      const supabase = createClient()

      // If updating code, check for duplicates - race condition prevention
      if (updates.code) {
        const { data: existing } = await supabase
          .from('ports')
          .select('id')
          .eq('code', updates.code.toUpperCase())
          .eq('is_active', true)
          .neq('id', id)
          .single()

        if (existing) {
          throw new Error(`Port with code "${updates.code}" already exists`)
        }
      }

      // Convert coordinates to PostGIS geography point if updating
      const updateData = { ...updates }
      if (updates.gps_coordinates) {
        try {
          const coords = JSON.parse(JSON.parse(updates.gps_coordinates))
          if (coords.lat && coords.lng) {
            // For geography columns, use raw WKT format - Supabase handles the conversion
            updateData.gps_coordinates = `SRID=4326;POINT(${coords.lng} ${coords.lat})`
            console.log(
              'updateData.gps_coordinates',
              updateData.gps_coordinates
            )
          }
        } catch (ex) {
          console.log('err', ex)
          // If it's already a PostGIS geography string, use as is
          updateData.gps_coordinates = updates.gps_coordinates
        }
      }

      if (updateData.code) {
        updateData.code = updateData.code.toUpperCase()
      }

      const { error } = await supabase
        .from('ports')
        .update({ ...updateData, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }

      // Fetch the updated port with text coordinates from the view
      const { data: updatedPort, error: fetchError } = await supabase
        .from('ports_with_text_coordinates')
        .select(
          'id, code, name, city, country, port_type, gps_coordinates:gps_coordinates_text, timezone, is_active, created_at, updated_at'
        )
        .eq('id', id)
        .single()

      if (fetchError) {
        throw new Error(fetchError.message)
      }

      // Update the port in the local state with text coordinates
      set(state => ({
        ports: state.ports.map(port =>
          port.id === id ? { ...port, ...updatedPort } : port
        ),
        isUpdating: false,
      }))

      return updatedPort
    } catch (error) {
      console.error('Error updating port:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to update port',
        isUpdating: false,
      })
      throw error
    }
  },

  // Delete single port
  deletePort: async (id: string) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Check if port is being used by shipments
      // NOTE: This check would be implemented once shipment tables are available
      // For now, we'll proceed with soft delete

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('ports')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        ports: state.ports.filter(port => port.id !== id),
        selectedPorts: new Set(
          Array.from(state.selectedPorts).filter(
            selectedId => selectedId !== id
          )
        ),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting port:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to delete port',
        isDeleting: false,
      })
      throw error
    }
  },

  // Delete multiple ports
  deletePorts: async (ids: string[]) => {
    set({ isDeleting: true, error: null })

    try {
      const supabase = createClient()

      // Check if any ports are being used by shipments
      // NOTE: This check would be implemented once shipment tables are available
      // For now, we'll proceed with soft delete

      // Soft delete (set is_active to false)
      const { error } = await supabase
        .from('ports')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .in('id', ids)

      if (error) {
        throw new Error(error.message)
      }

      // Remove from local state
      set(state => ({
        ports: state.ports.filter(port => !ids.includes(port.id)),
        selectedPorts: new Set(),
        isDeleting: false,
      }))
    } catch (error) {
      console.error('Error deleting ports:', error)
      set({
        error:
          error instanceof Error ? error.message : 'Failed to delete ports',
        isDeleting: false,
      })
      throw error
    }
  },

  // Geographic search: Find ports within radius
  searchPortsWithinRadius: async (search: GeographicSearch) => {
    set({ geographicLoading: true, error: null, geographicSearch: search })

    try {
      const supabase = createClient()

      // Use the PostGIS function (when available) or fallback to basic query
      const { data, error } = await supabase.rpc('find_ports_within_radius', {
        center_lat: search.center_lat,
        center_lng: search.center_lng,
        radius_km: search.radius_km,
      })

      if (error) {
        // Fallback: basic geographic query without distance calculation
        console.warn('PostGIS function not available, using fallback query')
        const fallbackQuery = await supabase
          .from('ports_with_text_coordinates')
          .select(
            'id, code, name, city, country, port_type, gps_coordinates:gps_coordinates_text, timezone, is_active, created_at, updated_at'
          )
          .eq('is_active', true)

        if (fallbackQuery.error) {
          throw new Error(fallbackQuery.error.message)
        }

        set({
          nearbyPorts: fallbackQuery.data || [],
          geographicLoading: false,
        })
        return fallbackQuery.data || []
      }

      set({
        nearbyPorts: data || [],
        geographicLoading: false,
      })
      return data || []
    } catch (error) {
      console.error('Error searching ports within radius:', error)
      set({
        error:
          error instanceof Error
            ? error.message
            : 'Failed to search nearby ports',
        geographicLoading: false,
      })
      return []
    }
  },

  // Get ports by country with optional distance calculation
  getPortsByCountry: async (country: string, referenceCoords?: Coordinates) => {
    set({ geographicLoading: true, error: null })

    try {
      const supabase = createClient()

      if (referenceCoords) {
        // Use PostGIS function with distance calculation
        const { data, error } = await supabase.rpc(
          'get_ports_by_country_with_distance',
          {
            target_country: country,
            ref_lat: referenceCoords.lat,
            ref_lng: referenceCoords.lng,
          }
        )

        if (error) {
          throw new Error(error.message)
        }

        set({ geographicLoading: false })
        return data || []
      } else {
        // Basic query without distance
        const { data, error } = await supabase
          .from('ports_with_text_coordinates')
          .select(
            'id, code, name, city, country, port_type, gps_coordinates:gps_coordinates_text, timezone, is_active, created_at, updated_at'
          )
          .ilike('country', `%${country}%`)
          .eq('is_active', true)
          .order('name')

        if (error) {
          throw new Error(error.message)
        }

        set({ geographicLoading: false })
        return data || []
      }
    } catch (error) {
      console.error('Error fetching ports by country:', error)
      set({
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch ports by country',
        geographicLoading: false,
      })
      return []
    }
  },

  // Calculate distance between two ports
  calculatePortDistance: async (port1Id: string, port2Id: string) => {
    try {
      const supabase = createClient()

      const { data, error } = await supabase.rpc('calculate_port_distance', {
        port1_id: port1Id,
        port2_id: port2Id,
      })

      if (error) {
        throw new Error(error.message)
      }

      return data || 0
    } catch (error) {
      console.error('Error calculating port distance:', error)
      set({
        error:
          error instanceof Error
            ? error.message
            : 'Failed to calculate distance',
      })
      return 0
    }
  },

  // Filter and search actions
  setFilter: (newFilter: Partial<PortFilter>) => {
    set(state => ({
      filter: { ...state.filter, ...newFilter },
      currentPage: 1, // Reset to first page when filtering
    }))
    get().fetchPorts()
  },

  setSearchTerm: (term: string) => {
    set({ searchTerm: term, currentPage: 1 })
    // Debounce the search - in a real app, use a debounce utility
    setTimeout(() => {
      if (get().searchTerm === term) {
        get().fetchPorts()
      }
    }, 300)
  },

  setSorting: (
    sortBy: PortState['sortBy'],
    sortOrder: PortState['sortOrder']
  ) => {
    set({ sortBy, sortOrder })
    get().fetchPorts()
  },

  // Pagination actions
  setPage: (page: number) => {
    set({ currentPage: page })
    get().fetchPorts()
  },

  setPageSize: (size: number) => {
    set({ pageSize: size, currentPage: 1 })
    get().fetchPorts()
  },

  // Selection actions
  selectPort: (id: string) => {
    set(state => ({
      selectedPorts: new Set([...Array.from(state.selectedPorts), id]),
    }))
  },

  deselectPort: (id: string) => {
    set(state => {
      const newSelection = new Set(state.selectedPorts)
      newSelection.delete(id)
      return { selectedPorts: newSelection }
    })
  },

  selectAllPorts: () => {
    set(state => ({
      selectedPorts: new Set(state.ports.map(port => port.id)),
    }))
  },

  clearSelection: () => {
    set({ selectedPorts: new Set() })
  },

  // Utility actions
  clearError: () => {
    set({ error: null })
  },

  clearGeographicSearch: () => {
    set({ geographicSearch: null, nearbyPorts: [] })
  },

  reset: () => {
    set(initialState)
  },

  // Real-time subscription
  subscribeToPorts: () => {
    const supabase = createClient()

    const subscription = supabase
      .channel('ports_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ports',
        },
        payload => {
          console.log('Port change received:', payload)
          // Refresh the list when changes occur
          get().fetchPorts()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  },
}))

// Selector hooks for better performance
export const usePorts = () => usePortStore(state => state.ports)
export const usePortsLoading = () => usePortStore(state => state.loading)
export const usePortsError = () => usePortStore(state => state.error)
export const useNearbyPorts = () => usePortStore(state => state.nearbyPorts)
export const useGeographicSearch = () =>
  usePortStore(state => state.geographicSearch)
export const usePortActions = () =>
  usePortStore(state => ({
    fetchPorts: state.fetchPorts,
    fetchPortById: state.fetchPortById,
    createPort: state.createPort,
    updatePort: state.updatePort,
    deletePort: state.deletePort,
    deletePorts: state.deletePorts,
    searchPortsWithinRadius: state.searchPortsWithinRadius,
    getPortsByCountry: state.getPortsByCountry,
    calculatePortDistance: state.calculatePortDistance,
    setFilter: state.setFilter,
    setSearchTerm: state.setSearchTerm,
    setSorting: state.setSorting,
    setPage: state.setPage,
    selectPort: state.selectPort,
    deselectPort: state.deselectPort,
    clearSelection: state.clearSelection,
    clearError: state.clearError,
    clearGeographicSearch: state.clearGeographicSearch,
    subscribeToPorts: state.subscribeToPorts,
  }))
