import { z } from 'zod'

// Transportation mode enums based on database schema
export const transportModeSchema = z.enum(['sea', 'land', 'rail'])

// Shipment status enums based on database schema
export const shipmentStatusSchema = z.enum([
  'booking_confirmed',
  'transport_assigned',
  'driver_assigned',
  'empty_container_picked',
  'arrived_at_factory',
  'loading_started',
  'departed_factory',
  'container_returned',
  'shipped',
  'arrived',
  'completed',
  'cancelled',
])

// Currency enums based on database schema
export const currencySchema = z.enum(['USD', 'THB', 'EUR', 'CNY'])

// Container type and size enums (matching database enums)
export const containerTypeSchema = z.enum([
  'dry',
  'reefer',
  'open_top',
  'flat_rack',
  'tank',
])

export const containerSizeSchema = z.enum(['20ft', '40ft', '40hc', '45ft'])

export const containerStatusSchema = z.enum([
  'empty',
  'loaded',
  'in_transit',
  'delivered',
])

export const packagingTypeSchema = z.enum([
  'Bag',
  'Plastic Basket',
  'Carton',
])

// Date validation helper - ensures dates are in the future and in correct sequence
const futureDateString = z.string().refine(
  date => {
    if (!date) return false
    const inputDate = new Date(date)
    const now = new Date()
    now.setHours(0, 0, 0, 0) // Start of today
    return inputDate >= now
  },
  { message: 'Date must be today or in the future' }
)

// Base shipment schema
export const shipmentBaseSchema = z.object({
  // Auto-generated field
  shipment_number: z.string().min(1, 'Shipment number is required'),

  // Transportation configuration
  transportation_mode: transportModeSchema,

  // Stakeholder fields (mandatory)
  customer_id: z
    .string()
    .uuid('Invalid customer ID')
    .min(1, 'Customer is required'),
  factory_id: z
    .string()
    .uuid('Invalid factory ID')
    .min(1, 'Factory is required'),
  forwarder_agent_id: z
    .string()
    .uuid('Invalid forwarder agent ID')
    .min(1, 'Forwarder agent is required'),

  // Intelligent pre-population fields
  shipper_id: z.string().uuid('Invalid shipper ID').optional(),
  consignee_id: z
    .string()
    .min(1, 'Consignee is required')
    .uuid('Invalid consignee ID'),
  notify_party_id: z
    .string()
    .min(1, 'Notify party is required')
    .uuid('Invalid notify party ID'),

  // Route configuration (mandatory)
  origin_port_id: z
    .string()
    .uuid('Invalid origin port ID')
    .min(1, 'Origin port is required'),
  destination_port_id: z
    .string()
    .uuid('Invalid destination port ID')
    .min(1, 'Destination port is required'),

  // Logistics information
  liner: z.string().optional(),
  vessel_name: z
    .string()
    .min(1, 'Vessel name is required'),
  voyage_number: z.string().optional(),
  booking_number: z
    .string()
    .min(1, 'Booking number is required'),

  // Optional invoice information
  invoice_number: z.string().optional(),

  // Cargo details
  number_of_pallet: z
    .number()
    .min(0, 'Number of pallets must be non-negative')
    .optional(),
  pallet_description: z.string().optional(),
  total_weight: z
    .number()
    .min(0, 'Total weight must be non-negative')
    .optional(),
  total_volume: z
    .number()
    .min(0, 'Total volume must be non-negative')
    .optional(),
  total_value_cif: z
    .number()
    .min(0, 'Total CIF value must be non-negative')
    .optional(),
  total_value_fob: z
    .number()
    .min(0, 'Total FOB value must be non-negative')
    .optional(),

  // Phytosanitary information
  ephyto_refno: z.string().optional(),

  // Financial information
  currency_code: currencySchema.default('USD'),
  currency_code_fob: currencySchema.default('USD'),

  // Status
  status: shipmentStatusSchema.default('booking_confirmed'),

  // Additional information
  notes: z.string().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
})

// Enhanced shipment creation schema with mandatory datetime fields and validation
export const shipmentCreationSchema = shipmentBaseSchema
  .extend({
    // Mandatory datetime fields with sequence validation
    etd_date: z
      .string()
      .min(1, 'ETD date is required')
      .refine(
        date => {
          if (!date) return false
          const inputDate = new Date(date)
          const now = new Date()
          now.setHours(0, 0, 0, 0)
          return inputDate >= now
        },
        { message: 'ETD date must be today or in the future' }
      ),
    eta_date: z
      .string()
      .min(1, 'ETA date is required')
      .refine(
        date => {
          if (!date) return false
          const inputDate = new Date(date)
          const now = new Date()
          now.setHours(0, 0, 0, 0)
          return inputDate >= now
        },
        { message: 'ETA date must be today or in the future' }
      ),
    closing_time: z
      .string()
      .min(1, 'Closing time is required')
      .refine(
        date => {
          if (!date) return false
          const inputDate = new Date(date)
          const now = new Date()
          return inputDate >= now
        },
        { message: 'Closing time must be in the future' }
      ),

    // Container yard date (optional)
    cy_date: z.string().optional(),
  })
  .refine(
    data => {
      // Date sequence validation: Closing Time < ETD < ETA
      if (!data.closing_time || !data.etd_date || !data.eta_date) {
        return false
      }

      const closingTime = new Date(data.closing_time)
      const etdDate = new Date(data.etd_date)
      const etaDate = new Date(data.eta_date)

      return closingTime < etdDate && etdDate < etaDate
    },
    {
      message: 'Date sequence must be: Closing Time < ETD < ETA',
      path: ['eta_date'],
    }
  )
  .refine(
    data => {
      // Origin and destination ports cannot be the same
      return data.origin_port_id !== data.destination_port_id
    },
    {
      message: 'Origin and destination ports cannot be the same',
      path: ['destination_port_id'],
    }
  )

// Shipment update schema (all fields optional except ID)
export const shipmentUpdateSchema = shipmentCreationSchema.partial().extend({
  id: z.string().uuid('Invalid shipment ID'),
})

// Container creation schema
export const containerSchema = z.object({
  shipment_id: z.string().uuid('Invalid shipment ID'),
  container_number: z.string().min(1, 'Container number is required'),
  container_type: containerTypeSchema,
  container_size: containerSizeSchema,
  seal_number: z.string().optional(),
  tare_weight: z.number().min(0, 'Tare weight must be non-negative').optional(),
  gross_weight: z
    .number()
    .min(0, 'Gross weight must be non-negative')
    .optional(),
  volume: z.number().min(0, 'Volume must be non-negative').optional(),
  temperature: z.string().optional(),
  vent: z.string().optional(),
  status: containerStatusSchema.default('empty'),
})

// Shipment product schema
export const shipmentProductSchema = z.object({
  shipment_id: z.string().uuid('Invalid shipment ID'),
  container_id: z.string().uuid('Invalid container ID').optional(),
  product_id: z.string().uuid('Invalid product ID'),
  product_description: z.string().optional(),
  quantity: z.number().min(0.01, 'Quantity must be greater than 0'),
  unit_of_measure_id: z.string().uuid('Invalid unit of measure ID'),
  unit_price_cif: z.number().min(0, 'CIF price must be non-negative'),
  unit_price_fob: z.number().min(0, 'FOB price must be non-negative'),
  total_value_cif: z.number().min(0, 'Total CIF value must be non-negative'),
  total_value_fob: z.number().min(0, 'Total FOB value must be non-negative'),
  gross_weight: z
    .number()
    .min(0, 'Gross weight must be non-negative')
    .default(0),
  net_weight: z.number().min(0, 'Net weight must be non-negative').default(0),
  total_gross_weight: z.number().min(0, 'Total gross weight must be non-negative').default(0),
  total_net_weight: z.number().min(0, 'Total net weight must be non-negative').default(0),
  shipping_mark: z.string().default('N/M'),
  mfg_date: z.string().optional(), // Date as string for form handling
  expire_date: z.string().optional(), // Date as string for form handling
  lot_number: z.string().optional(),
  packaging_type: packagingTypeSchema,
  quality_grade: z.string().optional(),
})

// Document upload schema
export const documentUploadSchema = z.object({
  file_name: z.string().min(1, 'File name is required'),
  file_size: z
    .number()
    .min(1, 'File size must be greater than 0')
    .max(10 * 1024 * 1024, 'File size must be less than 10MB'),
  file_type: z.enum(
    [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
    ],
    {
      errorMap: () => ({ message: 'File must be PDF, DOC, DOCX, JPG, or PNG' }),
    }
  ),
  storage_path: z.string().min(1, 'Storage path is required'),
  document_type: z
    .enum([
      'booking_confirmation',
      'invoice',
      'packing_list',
      'certificate',
      'other',
    ])
    .default('booking_confirmation'),
})

// Search and filter schemas
export const shipmentFilterSchema = z.object({
  transportation_mode: transportModeSchema.optional(),
  status: shipmentStatusSchema.optional(),
  customer_id: z.string().uuid().optional(),
  factory_id: z.string().uuid().optional(),
  origin_port_id: z.string().uuid().optional(),
  destination_port_id: z.string().uuid().optional(),
  etd_date_from: z.string().optional(),
  etd_date_to: z.string().optional(),
  search: z.string().optional(),
})

// Bulk operations schemas
export const bulkDeleteShipmentsSchema = z.object({
  ids: z
    .array(z.string().uuid('Invalid shipment ID'))
    .min(1, 'At least one shipment must be selected'),
})

// Form-specific schemas for UI components
export const transportModeConfigSchema = z.object({
  mode: transportModeSchema,
  workflow_fields: z.array(z.string()),
  required_documents: z.array(z.string()),
  validation_rules: z.record(z.string(), z.any()),
})

// Relationship intelligence schemas
export const customerHistorySchema = z.object({
  customer_id: z.string().uuid(),
  frequently_used_ports: z.array(z.string().uuid()),
  preferred_shipping_routes: z.array(
    z.object({
      origin_port_id: z.string().uuid(),
      destination_port_id: z.string().uuid(),
      usage_count: z.number(),
    })
  ),
  average_shipment_volume: z.number().optional(),
  preferred_transportation_modes: z.array(transportModeSchema),
})

// Validation helpers
export const validateShipmentNumber = (
  shipmentNumber: string,
  mode: string,
  portCode: string
): boolean => {
  // Format: EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running]
  const modeCode = mode === 'sea' ? 'SEA' : mode === 'land' ? 'LND' : 'RAL'
  const pattern = new RegExp(`^EX${modeCode}-${portCode}-\\d{6}-\\d+$`)
  return pattern.test(shipmentNumber)
}

export const validateDateSequence = (
  closingTime: string,
  etd: string,
  eta: string
): boolean => {
  const closing = new Date(closingTime)
  const departure = new Date(etd)
  const arrival = new Date(eta)

  return closing < departure && departure < arrival
}

export const validatePortCombination = (
  originPortId: string,
  destinationPortId: string
): boolean => {
  return originPortId !== destinationPortId
}

// Type exports
export type ShipmentCreation = z.infer<typeof shipmentCreationSchema>
export type ShipmentUpdate = z.infer<typeof shipmentUpdateSchema>
export type ShipmentFilter = z.infer<typeof shipmentFilterSchema>
export type ShipmentBase = z.infer<typeof shipmentBaseSchema>
export type Container = z.infer<typeof containerSchema>
export type ShipmentProduct = z.infer<typeof shipmentProductSchema>
export type DocumentUpload = z.infer<typeof documentUploadSchema>
export type TransportMode = z.infer<typeof transportModeSchema>
export type ShipmentStatus = z.infer<typeof shipmentStatusSchema>
export type Currency = z.infer<typeof currencySchema>
export type ContainerType = z.infer<typeof containerTypeSchema>
export type ContainerSize = z.infer<typeof containerSizeSchema>
export type ContainerStatus = z.infer<typeof containerStatusSchema>
export type PackagingType = z.infer<typeof packagingTypeSchema>
export type TransportModeConfig = z.infer<typeof transportModeConfigSchema>
export type CustomerHistory = z.infer<typeof customerHistorySchema>

// Constants for UI
export const TRANSPORT_MODES = [
  { value: 'sea', label: 'Sea Freight', icon: 'Ship' },
  { value: 'land', label: 'Land Transport', icon: 'Truck' },
  { value: 'rail', label: 'Rail Freight', icon: 'Train' },
] as const

export const SHIPMENT_STATUSES = [
  {
    value: 'booking_confirmed',
    label: 'Booking Confirmed',
    variant: 'default' as const,
    color: '#0ea5e9',
  },
  {
    value: 'transport_assigned',
    label: 'Transport Assigned',
    variant: 'default' as const,
    color: '#3b82f6',
  },
  {
    value: 'driver_assigned',
    label: 'Driver Assigned',
    variant: 'default' as const,
    color: '#1d4ed8',
  },
  {
    value: 'empty_container_picked',
    label: 'Empty Container Picked',
    variant: 'secondary' as const,
    color: '#7c3aed',
  },
  {
    value: 'arrived_at_factory',
    label: 'Arrived at Factory',
    variant: 'secondary' as const,
    color: '#8b5cf6',
  },
  {
    value: 'loading_started',
    label: 'Loading Started',
    variant: 'secondary' as const,
    color: '#f59e0b',
  },
  {
    value: 'departed_factory',
    label: 'Departed Factory',
    variant: 'secondary' as const,
    color: '#d97706',
  },
  {
    value: 'container_returned',
    label: 'Container Returned',
    variant: 'secondary' as const,
    color: '#059669',
  },
  {
    value: 'shipped',
    label: 'Shipped',
    variant: 'secondary' as const,
    color: '#0d9488',
  },
  {
    value: 'arrived',
    label: 'Arrived',
    variant: 'secondary' as const,
    color: '#10b981',
  },
  { 
    value: 'completed', 
    label: 'Completed', 
    variant: 'success' as const,
    color: '#16a34a',
  },
  { 
    value: 'cancelled', 
    label: 'Cancelled', 
    variant: 'destructive' as const,
    color: '#dc2626',
  },
] as const

export const CURRENCIES = [
  { value: 'USD', label: 'US Dollar', symbol: '$' },
  { value: 'THB', label: 'Thai Baht', symbol: '฿' },
  { value: 'EUR', label: 'Euro', symbol: '€' },
  { value: 'CNY', label: 'Chinese Yuan', symbol: '¥' },
] as const

export const CONTAINER_TYPES = [
  { value: 'dry', label: 'Dry Container' },
  { value: 'reefer', label: 'Refrigerated Container' },
  { value: 'open_top', label: 'Open Top Container' },
  { value: 'flat_rack', label: 'Flat Rack Container' },
  { value: 'tank', label: 'Tank Container' },
] as const

export const CONTAINER_SIZES = [
  { value: '20ft', label: '20ft Container' },
  { value: '40ft', label: '40ft Container' },
  { value: '40hc', label: '40ft High Cube Container' },
  { value: '45ft', label: '45ft Container' },
] as const

export const PACKAGING_TYPES = [
  { value: 'Bag', label: 'Bag' },
  { value: 'Plastic Basket', label: 'Plastic Basket' },
  { value: 'Carton', label: 'Carton' },
] as const

export const DOCUMENT_TYPES = [
  { value: 'booking_confirmation', label: 'Booking Confirmation' },
  { value: 'invoice', label: 'Invoice' },
  { value: 'packing_list', label: 'Packing List' },
  { value: 'certificate', label: 'Certificate' },
  { value: 'other', label: 'Other' },
] as const

// File validation constants
export const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/jpeg',
  'image/png',
] as const

export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

// Shipment number generation constants
export const TRANSPORT_MODE_CODES = {
  sea: 'SEA',
  land: 'LND',
  rail: 'RAL',
} as const
