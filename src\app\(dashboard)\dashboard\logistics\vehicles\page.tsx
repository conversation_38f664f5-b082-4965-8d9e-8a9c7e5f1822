'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Filter,
  X,
  Truck,
  Car,
  Fuel,
  Calendar,
  MapPin,
  Eye,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wrench,
  User,
  Navigation2,
  Activity,
  FileText,
} from 'lucide-react'

// Mock data for vehicles
const mockVehicles = [
  {
    id: '1',
    plateNumber: 'กท-1234',
    make: 'Isuzu',
    model: 'D-Max',
    year: 2022,
    vehicleType: 'Pickup Truck',
    capacity: '1.5 tons',
    fuelType: 'Diesel',
    status: 'Available',
    currentDriver: 'Somchai Jaidee',
    currentLocation: 'Bangkok Warehouse',
    mileage: '45,230 km',
    lastMaintenance: '2024-01-15',
    nextMaintenance: '2024-02-15',
    insuranceExpiry: '2024-12-31',
    registrationExpiry: '2024-06-30',
    vin: 'ISUZU123456789012',
    color: 'White',
    fuelEfficiency: '12 km/l',
  },
  {
    id: '2',
    plateNumber: 'กข-5678',
    make: 'Mitsubishi',
    model: 'Fuso Canter',
    year: 2021,
    vehicleType: 'Small Truck',
    capacity: '3 tons',
    fuelType: 'Diesel',
    status: 'In Transit',
    currentDriver: 'Niran Suksai',
    currentLocation: 'Chonburi - Laem Chabang',
    mileage: '78,450 km',
    lastMaintenance: '2024-01-10',
    nextMaintenance: '2024-02-10',
    insuranceExpiry: '2024-08-15',
    registrationExpiry: '2024-04-20',
    vin: 'MITS789012345678',
    color: 'Blue',
    fuelEfficiency: '8 km/l',
  },
  {
    id: '3',
    plateNumber: 'กก-9012',
    make: 'Toyota',
    model: 'Hilux Revo',
    year: 2023,
    vehicleType: 'Pickup Truck',
    capacity: '1.2 tons',
    fuelType: 'Diesel',
    status: 'Maintenance',
    currentDriver: null,
    currentLocation: 'Service Center',
    mileage: '12,890 km',
    lastMaintenance: '2024-01-18',
    nextMaintenance: '2024-02-18',
    insuranceExpiry: '2025-03-15',
    registrationExpiry: '2024-11-10',
    vin: 'TOYOTA456789012345',
    color: 'Silver',
    fuelEfficiency: '13 km/l',
  },
  {
    id: '4',
    plateNumber: 'กจ-3456',
    make: 'Hino',
    model: '500 Series',
    year: 2020,
    vehicleType: 'Medium Truck',
    capacity: '8 tons',
    fuelType: 'Diesel',
    status: 'Available',
    currentDriver: 'Prasert Kaewta',
    currentLocation: 'Rayong Depot',
    mileage: '156,230 km',
    lastMaintenance: '2024-01-05',
    nextMaintenance: '2024-02-05',
    insuranceExpiry: '2024-05-20',
    registrationExpiry: '2024-09-15',
    vin: 'HINO901234567890',
    color: 'Orange',
    fuelEfficiency: '6 km/l',
  },
  {
    id: '5',
    plateNumber: 'กฉ-7890',
    make: 'Isuzu',
    model: 'NLR',
    year: 2019,
    vehicleType: 'Small Truck',
    capacity: '2.5 tons',
    fuelType: 'Diesel',
    status: 'Out of Service',
    currentDriver: null,
    currentLocation: 'Bangkok Garage',
    mileage: '203,450 km',
    lastMaintenance: '2023-12-20',
    nextMaintenance: '2024-01-20',
    insuranceExpiry: '2024-03-10',
    registrationExpiry: '2024-07-25',
    vin: 'ISUZU567890123456',
    color: 'Red',
    fuelEfficiency: '9 km/l',
  },
]

const vehicleStatuses = ['All', 'Available', 'In Transit', 'Maintenance', 'Out of Service']
const vehicleTypes = ['All', 'Pickup Truck', 'Small Truck', 'Medium Truck', 'Large Truck']

export default function VehiclesPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('All')
  const [typeFilter, setTypeFilter] = useState('All')
  const [selectedVehicles, setSelectedVehicles] = useState<string[]>([])
  const [viewingVehicle, setViewingVehicle] = useState<any>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // Filter vehicles based on search term, status, and type
  const filteredVehicles = mockVehicles.filter(vehicle => {
    const matchesSearch =
      vehicle.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (vehicle.currentDriver && vehicle.currentDriver.toLowerCase().includes(searchTerm.toLowerCase())) ||
      vehicle.currentLocation.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'All' || vehicle.status === statusFilter
    const matchesType = typeFilter === 'All' || vehicle.vehicleType === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  const toggleVehicle = (vehicleId: string) => {
    setSelectedVehicles(prev =>
      prev.includes(vehicleId)
        ? prev.filter(id => id !== vehicleId)
        : [...prev, vehicleId]
    )
  }

  const toggleAll = () => {
    setSelectedVehicles(
      selectedVehicles.length === filteredVehicles.length
        ? []
        : filteredVehicles.map(vehicle => vehicle.id)
    )
  }

  const clearSelection = () => {
    setSelectedVehicles([])
  }

  const clearFilters = () => {
    setSearchTerm('')
    setStatusFilter('All')
    setTypeFilter('All')
  }

  const getStatusBadgeProps = (status: string) => {
    switch (status) {
      case 'Available':
        return { className: 'bg-green-600 hover:bg-green-700 text-white border-green-600' }
      case 'In Transit':
        return { className: 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600' }
      case 'Maintenance':
        return { className: 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600' }
      case 'Out of Service':
        return { className: 'bg-red-600 hover:bg-red-700 text-white border-red-600' }
      default:
        return { className: 'bg-slate-600 hover:bg-slate-500 text-slate-200 border-slate-500' }
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Available':
        return <CheckCircle className="h-3 w-3 mr-1" />
      case 'In Transit':
        return <Navigation2 className="h-3 w-3 mr-1" />
      case 'Maintenance':
        return <Wrench className="h-3 w-3 mr-1" />
      case 'Out of Service':
        return <AlertTriangle className="h-3 w-3 mr-1" />
      default:
        return <Clock className="h-3 w-3 mr-1" />
    }
  }

  const getVehicleIcon = (vehicleType: string) => {
    switch (vehicleType) {
      case 'Pickup Truck':
        return <Car className="h-4 w-4" />
      default:
        return <Truck className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Fleet Management</h1>
          <p className="text-slate-400 mt-1">
            Manage vehicles, maintenance schedules, and fleet operations
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Vehicle
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700 text-white">
            <DialogHeader>
              <DialogTitle className="text-white">Register New Vehicle</DialogTitle>
              <DialogDescription className="text-slate-400">
                Add a new vehicle to your fleet with registration and technical details
              </DialogDescription>
            </DialogHeader>
            <div className="text-center py-8 text-slate-400">
              <Truck className="h-12 w-12 mx-auto mb-4" />
              <p>Vehicle registration form would be implemented here</p>
              <p className="text-sm mt-2">
                This is a mockup for presentation purposes
              </p>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filters */}
      <div className="bg-slate-800 rounded-lg p-6 space-y-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-orange-500" />
            <h3 className="text-lg font-medium text-white">Filter Vehicles</h3>
          </div>
          {(searchTerm || statusFilter !== 'All' || typeFilter !== 'All') && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-slate-400 hover:text-white"
            >
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Search Vehicles
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search by plate, make, model, driver..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-orange-500 focus:ring-orange-500"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Status
            </label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600">
                {vehicleStatuses.map(status => (
                  <SelectItem
                    key={status}
                    value={status}
                    className="text-slate-200 focus:bg-slate-600 focus:text-white"
                  >
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-slate-300 text-sm font-medium">
              Vehicle Type
            </label>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-orange-500 focus:ring-orange-500">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600">
                {vehicleTypes.map(type => (
                  <SelectItem
                    key={type}
                    value={type}
                    className="text-slate-200 focus:bg-slate-600 focus:text-white"
                  >
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {(searchTerm || statusFilter !== 'All' || typeFilter !== 'All') && (
          <div className="flex flex-wrap gap-2 pt-2">
            {searchTerm && (
              <div className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-sm flex items-center">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-2 hover:text-orange-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {statusFilter !== 'All' && (
              <div className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm flex items-center">
                Status: {statusFilter}
                <button
                  onClick={() => setStatusFilter('All')}
                  className="ml-2 hover:text-blue-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {typeFilter !== 'All' && (
              <div className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm flex items-center">
                Type: {typeFilter}
                <button
                  onClick={() => setTypeFilter('All')}
                  className="ml-2 hover:text-purple-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Fleet Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-500/20">
                <Truck className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-slate-400 text-sm">Total Fleet</p>
                <p className="text-2xl font-bold text-white">{mockVehicles.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-500/20">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-slate-400 text-sm">Available</p>
                <p className="text-2xl font-bold text-white">
                  {mockVehicles.filter(v => v.status === 'Available').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-600/20">
                <Navigation2 className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-slate-400 text-sm">In Transit</p>
                <p className="text-2xl font-bold text-white">
                  {mockVehicles.filter(v => v.status === 'In Transit').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-500/20">
                <Wrench className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-slate-400 text-sm">Maintenance</p>
                <p className="text-2xl font-bold text-white">
                  {mockVehicles.filter(v => v.status === 'Maintenance' || v.status === 'Out of Service').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      {selectedVehicles.length > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6 bg-slate-800">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">
                {selectedVehicles.length} vehicle{selectedVehicles.length !== 1 ? 's' : ''} selected
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={clearSelection}
                  className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
                >
                  Clear Selection
                </Button>
                <Button
                  variant="default"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Schedule Maintenance
                </Button>
                <Button
                  variant="destructive"
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  Deactivate Vehicles
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Vehicles Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600">
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Truck className="h-5 w-5 text-orange-500" />
              Fleet Vehicles ({filteredVehicles.length})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
            >
              Refresh Fleet
            </Button>
          </div>
        </CardHeader>
        <CardContent className="bg-slate-800 p-0">
          {filteredVehicles.length === 0 ? (
            <div className="text-center py-8">
              <Truck className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">No vehicles found</p>
              <p className="text-sm text-slate-400 mt-1">
                {searchTerm || statusFilter !== 'All' || typeFilter !== 'All'
                  ? 'Try adjusting your search criteria'
                  : 'Register your first vehicle to get started'}
              </p>
            </div>
          ) : (
            <Table className="bg-slate-800">
              <TableHeader className="bg-slate-700">
                <TableRow className="border-slate-600 hover:bg-slate-700">
                  <TableHead className="w-12 text-slate-200">
                    <Checkbox
                      checked={selectedVehicles.length === filteredVehicles.length}
                      onCheckedChange={toggleAll}
                      className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                    />
                  </TableHead>
                  <TableHead className="text-slate-200">Vehicle Details</TableHead>
                  <TableHead className="text-slate-200">Specifications</TableHead>
                  <TableHead className="text-slate-200">Current Assignment</TableHead>
                  <TableHead className="text-slate-200">Maintenance</TableHead>
                  <TableHead className="text-slate-200">Status</TableHead>
                  <TableHead className="w-32 text-slate-200">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="bg-slate-800">
                {filteredVehicles.map(vehicle => (
                  <TableRow
                    key={vehicle.id}
                    className="border-slate-600 hover:bg-slate-700 text-slate-200"
                  >
                    <TableCell>
                      <Checkbox
                        checked={selectedVehicles.includes(vehicle.id)}
                        onCheckedChange={() => toggleVehicle(vehicle.id)}
                        className="border-slate-400 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                      />
                    </TableCell>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        {getVehicleIcon(vehicle.vehicleType)}
                        <div>
                          <div className="font-semibold text-orange-400">
                            {vehicle.plateNumber}
                          </div>
                          <div className="text-sm text-slate-300">
                            {vehicle.make} {vehicle.model} ({vehicle.year})
                          </div>
                          <div className="text-xs text-slate-400">
                            {vehicle.vehicleType} • {vehicle.color}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center text-slate-300">
                          <Truck className="h-3 w-3 text-slate-400 mr-1" />
                          Capacity: {vehicle.capacity}
                        </div>
                        <div className="flex items-center text-slate-300">
                          <Fuel className="h-3 w-3 text-slate-400 mr-1" />
                          {vehicle.fuelType} • {vehicle.fuelEfficiency}
                        </div>
                        <div className="flex items-center text-slate-300">
                          <Activity className="h-3 w-3 text-slate-400 mr-1" />
                          {vehicle.mileage}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1 text-sm">
                        {vehicle.currentDriver && (
                          <div className="flex items-center text-slate-300">
                            <User className="h-3 w-3 text-slate-400 mr-1" />
                            {vehicle.currentDriver}
                          </div>
                        )}
                        <div className="flex items-center text-slate-300">
                          <MapPin className="h-3 w-3 text-slate-400 mr-1" />
                          {vehicle.currentLocation}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1 text-xs">
                        <div className="flex items-center text-slate-400">
                          <Calendar className="h-3 w-3 mr-1" />
                          Last: {vehicle.lastMaintenance}
                        </div>
                        <div className="flex items-center text-slate-400">
                          <Clock className="h-3 w-3 mr-1" />
                          Next: {vehicle.nextMaintenance}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="default"
                        {...getStatusBadgeProps(vehicle.status)}
                      >
                        {getStatusIcon(vehicle.status)}
                        {vehicle.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setViewingVehicle(vehicle)}
                          className="text-slate-400 hover:text-blue-400 hover:bg-slate-600"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-slate-400 hover:text-orange-400 hover:bg-slate-600"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-slate-400 hover:text-green-400 hover:bg-slate-600"
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* View Vehicle Details Modal */}
      {viewingVehicle && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-slate-800 rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">
                Vehicle Details
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewingVehicle(null)}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Vehicle Header */}
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                  {getVehicleIcon(viewingVehicle.vehicleType)}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white">
                    {viewingVehicle.plateNumber}
                  </h3>
                  <p className="text-slate-400 mt-1">
                    {viewingVehicle.make} {viewingVehicle.model} ({viewingVehicle.year})
                  </p>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge
                      variant="outline"
                      className="border-orange-400 text-orange-200 bg-orange-500/20"
                    >
                      <Truck className="h-3 w-3 mr-1" />
                      {viewingVehicle.vehicleType}
                    </Badge>
                    <Badge
                      variant="default"
                      {...getStatusBadgeProps(viewingVehicle.status)}
                    >
                      {getStatusIcon(viewingVehicle.status)}
                      {viewingVehicle.status}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Vehicle Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Truck className="h-4 w-4 text-orange-500" />
                    <span className="text-white font-medium">Capacity</span>
                  </div>
                  <p className="text-slate-300">{viewingVehicle.capacity}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Fuel className="h-4 w-4 text-green-500" />
                    <span className="text-white font-medium">Fuel Type</span>
                  </div>
                  <p className="text-slate-300">{viewingVehicle.fuelType}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Activity className="h-4 w-4 text-blue-500" />
                    <span className="text-white font-medium">Mileage</span>
                  </div>
                  <p className="text-slate-300">{viewingVehicle.mileage}</p>
                </div>

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Fuel className="h-4 w-4 text-purple-500" />
                    <span className="text-white font-medium">Fuel Efficiency</span>
                  </div>
                  <p className="text-slate-300">{viewingVehicle.fuelEfficiency}</p>
                </div>

                {viewingVehicle.currentDriver && (
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <User className="h-4 w-4 text-blue-500" />
                      <span className="text-white font-medium">Current Driver</span>
                    </div>
                    <p className="text-slate-300">{viewingVehicle.currentDriver}</p>
                  </div>
                )}

                <div className="bg-slate-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <MapPin className="h-4 w-4 text-red-500" />
                    <span className="text-white font-medium">Location</span>
                  </div>
                  <p className="text-slate-300">{viewingVehicle.currentLocation}</p>
                </div>
              </div>

              {/* Maintenance Information */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-white border-b border-slate-600 pb-2">
                  Maintenance & Documentation
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Calendar className="h-4 w-4 text-green-500" />
                      <span className="text-white font-medium">Last Maintenance</span>
                    </div>
                    <p className="text-slate-300">{viewingVehicle.lastMaintenance}</p>
                  </div>

                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Clock className="h-4 w-4 text-yellow-500" />
                      <span className="text-white font-medium">Next Maintenance</span>
                    </div>
                    <p className="text-slate-300">{viewingVehicle.nextMaintenance}</p>
                  </div>

                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <FileText className="h-4 w-4 text-blue-500" />
                      <span className="text-white font-medium">Insurance Expiry</span>
                    </div>
                    <p className="text-slate-300">{viewingVehicle.insuranceExpiry}</p>
                  </div>

                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <FileText className="h-4 w-4 text-purple-500" />
                      <span className="text-white font-medium">Registration Expiry</span>
                    </div>
                    <p className="text-slate-300">{viewingVehicle.registrationExpiry}</p>
                  </div>
                </div>
              </div>

              {/* Technical Details */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-white border-b border-slate-600 pb-2">
                  Technical Details
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Settings className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">VIN Number</span>
                    </div>
                    <p className="text-slate-300 font-mono text-sm">{viewingVehicle.vin}</p>
                  </div>

                  <div className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Car className="h-4 w-4 text-orange-500" />
                      <span className="text-white font-medium">Color</span>
                    </div>
                    <p className="text-slate-300">{viewingVehicle.color}</p>
                  </div>
                </div>
              </div>

              {/* Vehicle ID */}
              <div className="bg-slate-700 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Truck className="h-4 w-4 text-orange-500" />
                  <span className="text-white font-medium">Vehicle ID</span>
                </div>
                <p className="text-slate-300 font-mono text-sm">{viewingVehicle.id}</p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end mt-6 space-x-2">
              <Button
                variant="outline"
                className="bg-slate-700 border-slate-600 text-slate-200 hover:bg-slate-600 hover:text-white"
              >
                Edit Vehicle
              </Button>
              <Button
                variant="default"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Schedule Maintenance
              </Button>
              <Button
                onClick={() => setViewingVehicle(null)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}