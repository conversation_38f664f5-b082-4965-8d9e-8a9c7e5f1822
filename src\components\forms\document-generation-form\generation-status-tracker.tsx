'use client'

import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent } from '@/components/ui/card'
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Loader2, 
  FileText, 
  Upload, 
  Database 
} from 'lucide-react'
import type { GenerationProgress, GenerationStatus } from '@/types/document-generation'

interface GenerationStatusTrackerProps {
  progress: GenerationProgress | null
  className?: string
}

/**
 * Generation Status Tracker Component
 * Story 5.2: Automated Document Generation Engine - AC1
 * 
 * Displays real-time progress of document generation operations
 */
export function GenerationStatusTracker({ progress, className }: GenerationStatusTrackerProps) {
  if (!progress) {
    return null
  }

  /**
   * Get status icon based on generation status
   */
  const getStatusIcon = (status: GenerationStatus) => {
    switch (status) {
      case 'idle':
        return <Clock className="h-4 w-4 text-slate-500" />
      case 'preparing':
        return <Database className="h-4 w-4 text-blue-500 animate-pulse" />
      case 'generating':
        return <FileText className="h-4 w-4 text-orange-500" />
      case 'saving':
        return <Upload className="h-4 w-4 text-blue-500" />
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Loader2 className="h-4 w-4 text-slate-500 animate-spin" />
    }
  }

  /**
   * Get status color scheme
   */
  const getStatusColors = (status: GenerationStatus) => {
    switch (status) {
      case 'idle':
        return {
          badge: 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200',
          progress: 'bg-slate-200',
          background: 'bg-slate-50 dark:bg-slate-900'
        }
      case 'preparing':
        return {
          badge: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
          progress: 'bg-blue-200',
          background: 'bg-blue-50 dark:bg-blue-950'
        }
      case 'generating':
        return {
          badge: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
          progress: 'bg-orange-200',
          background: 'bg-orange-50 dark:bg-orange-950'
        }
      case 'saving':
        return {
          badge: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
          progress: 'bg-blue-200',
          background: 'bg-blue-50 dark:bg-blue-950'
        }
      case 'complete':
        return {
          badge: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
          progress: 'bg-green-200',
          background: 'bg-green-50 dark:bg-green-950'
        }
      case 'error':
        return {
          badge: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
          progress: 'bg-red-200',
          background: 'bg-red-50 dark:bg-red-950'
        }
      default:
        return {
          badge: 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200',
          progress: 'bg-slate-200',
          background: 'bg-slate-50 dark:bg-slate-900'
        }
    }
  }

  const colors = getStatusColors(progress.status)

  return (
    <Card className={`${className || ''} ${colors.background} border-2`}>
      <CardContent className="p-4 space-y-4">
        {/* Header with Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon(progress.status)}
            <span className="font-medium text-slate-900 dark:text-white">
              Generation Status
            </span>
          </div>
          <Badge 
            variant="secondary" 
            className={`${colors.badge} capitalize font-medium`}
          >
            {progress.status.replace('_', ' ')}
          </Badge>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-700 dark:text-slate-300">
              {progress.message}
            </span>
            <span className="text-sm font-mono font-medium text-slate-600 dark:text-slate-400">
              {Math.round(progress.progress)}%
            </span>
          </div>
          
          <Progress 
            value={progress.progress} 
            className="h-3 bg-slate-200 dark:bg-slate-700"
          />
        </div>

        {/* Current Step */}
        {progress.currentStep && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-current opacity-60" />
            <span className="text-sm text-slate-600 dark:text-slate-400">
              {progress.currentStep}
            </span>
          </div>
        )}

        {/* Error Details */}
        {progress.status === 'error' && progress.error && (
          <div className="p-3 bg-red-100 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-700 dark:text-red-300">
                <p className="font-medium mb-1">Error Details:</p>
                <p className="break-words">{progress.error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success Details */}
        {progress.status === 'complete' && (
          <div className="p-3 bg-green-100 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">
                Document generation completed successfully!
              </span>
            </div>
          </div>
        )}

        {/* Processing Animation */}
        {(progress.status === 'generating' || progress.status === 'preparing' || progress.status === 'saving') && (
          <div className="flex items-center justify-center pt-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-60" />
              <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-60" style={{ animationDelay: '0.1s' }} />
              <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-60" style={{ animationDelay: '0.2s' }} />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * Mini Status Indicator for compact spaces
 */
interface MiniStatusIndicatorProps {
  status: GenerationStatus
  progress: number
  className?: string
}

export function MiniStatusIndicator({ status, progress, className }: MiniStatusIndicatorProps) {
  const getStatusColor = (status: GenerationStatus) => {
    switch (status) {
      case 'idle': return 'text-slate-500'
      case 'preparing': return 'text-blue-500'
      case 'generating': return 'text-orange-500'
      case 'saving': return 'text-blue-500'
      case 'complete': return 'text-green-500'
      case 'error': return 'text-red-500'
      default: return 'text-slate-500'
    }
  }

  const getIcon = (status: GenerationStatus) => {
    switch (status) {
      case 'idle': return Clock
      case 'preparing': return Database
      case 'generating': return FileText
      case 'saving': return Upload
      case 'complete': return CheckCircle
      case 'error': return AlertCircle
      default: return Loader2
    }
  }

  const Icon = getIcon(status)
  const isAnimated = ['preparing', 'generating', 'saving'].includes(status)

  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      <Icon 
        className={`h-4 w-4 ${getStatusColor(status)} ${isAnimated ? 'animate-pulse' : ''}`} 
      />
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <span className="text-xs capitalize text-slate-600 dark:text-slate-400">
            {status.replace('_', ' ')}
          </span>
          <span className="text-xs font-mono text-slate-500">
            {Math.round(progress)}%
          </span>
        </div>
        <Progress value={progress} className="h-1 mt-1" />
      </div>
    </div>
  )
}