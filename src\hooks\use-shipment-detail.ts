'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { getShipmentService } from '@/lib/services/shipment-service'
import type { ShipmentWithRelations } from '@/lib/supabase/types'

interface UseShipmentDetailReturn {
  shipment: ShipmentWithRelations | null
  isLoading: boolean
  error: string | null
  refreshShipment: () => Promise<void>
}

export function useShipmentDetail(shipmentId: string): UseShipmentDetailReturn {
  const [shipment, setShipment] = useState<ShipmentWithRelations | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()
  const shipmentService = getShipmentService(supabase)

  const fetchShipment = useCallback(async () => {
    if (!shipmentId) {
      setError('Shipment ID is required')
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const result = await shipmentService.getShipmentById(shipmentId)
      
      if (!result) {
        setError('Shipment not found')
        setShipment(null)
      } else {
        setShipment(result)
      }
    } catch (err: any) {
      console.error('Error fetching shipment:', err)
      setError(err.message || 'Failed to fetch shipment details')
      setShipment(null)
    } finally {
      setIsLoading(false)
    }
  }, [shipmentId, shipmentService])

  const refreshShipment = useCallback(async () => {
    await fetchShipment()
  }, [fetchShipment])

  // Initial fetch
  useEffect(() => {
    fetchShipment()
  }, [fetchShipment])

  // Set up real-time subscription for shipment updates
  useEffect(() => {
    if (!shipmentId || !shipment) return

    const subscription = supabase
      .channel(`shipment:${shipmentId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'shipments',
          filter: `id=eq.${shipmentId}`,
        },
        (payload) => {
          // Refresh the shipment data when it's updated
          fetchShipment()
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [shipmentId, shipment, supabase, fetchShipment])

  return {
    shipment,
    isLoading,
    error,
    refreshShipment,
  }
}