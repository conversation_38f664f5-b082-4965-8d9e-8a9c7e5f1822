# Core Workflows

## Intelligent Shipment Creation Workflow

```mermaid
sequenceDiagram
    participant CS as CS Representative
    participant FE as Frontend App
    participant RIE as Relationship Intelligence Engine
    participant DB as Supabase Database
    participant SWE as Shipment Workflow Engine
    participant RTM as Real-time Manager
    
    CS->>FE: Select Transportation Mode (Sea/Land/Rail)
    FE->>FE: Configure workflow fields based on mode
    CS->>FE: Select Customer
    FE->>RIE: getCustomerDefaults(customerId)
    RIE->>DB: Query customer_shippers, customer_products
    DB-->>RIE: Return relationships with defaults
    RIE-->>FE: {defaultShipper, availableShippers, defaultProduct, availableProducts}
    FE->>FE: Pre-populate cascading selections
    
    CS->>FE: Complete mandatory fields (Factory, Forwarder Agent, ETD/ETA/Closing Time, Ports)
    CS->>FE: Submit shipment creation
    
    FE->>SWE: createShipment(shipmentData)
    SWE->>DB: Generate shipment number (EX[Mode]-[Port]-YYMMDD-[Running])
    SWE->>DB: Insert shipment record
    SWE->>DB: Auto-generate containers based on product quantities
    SWE->>RTM: Broadcast shipment creation event
    RTM->>RTM: Notify relevant stakeholders
    FE->>CS: Display shipment confirmation with auto-generated number
```

## Driver Mobile Status Update Workflow

```mermaid
sequenceDiagram
    participant DR as Driver
    participant PWA as Mobile PWA
    participant SW as Service Worker
    participant FSM as File Storage Manager
    participant DB as Supabase Database
    participant RTM as Real-time Manager
    participant MNS as Notification System
    
    DR->>PWA: Open assigned shipment
    PWA->>PWA: Check network connectivity
    
    alt Online Mode
        PWA->>DB: Fetch current shipment status
        DB-->>PWA: Return status and workflow options
    else Offline Mode
        PWA->>SW: Retrieve cached shipment data
        SW-->>PWA: Return offline shipment info
    end
    
    DR->>PWA: Select next status and capture photos
    DR->>PWA: Auto-capture GPS coordinates
    DR->>PWA: Submit status update
    
    alt Online Mode
        PWA->>FSM: Upload photos to Supabase Storage
        PWA->>DB: Create status_history record
        PWA->>DB: Update shipment status
        DB->>RTM: Trigger real-time status change event
        RTM->>MNS: Send stakeholder notifications
    else Offline Mode
        PWA->>SW: Queue status update locally
        SW->>SW: Store photos and data for sync
        PWA->>DR: Show "Queued for sync" confirmation
    end
```
