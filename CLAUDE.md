# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DYY Trading Fruit Export Management System - A Next.js 14 + Supabase application for managing fruit export operations with role-based access, shipment tracking, and multi-channel communications.

## Development Commands

### Core Development
```bash
# Development server
npm run dev

# Build for production
npm run build

# Production server
npm start

# Linting
npm run lint
```

### Testing Commands
```bash
# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# E2E tests
npm run test:e2e

# All tests (unit + integration + e2e)
npm run test:all

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage

# CI with coverage
npm run test:ci

# E2E with UI
npm run test:e2e:ui
```

### Running Specific Tests
```bash
# Single test file
npx vitest run src/lib/utils/__tests__/shipment-number-generator.test.ts

# Component tests
npx vitest run src/components/**/__tests__/**/*.test.tsx

# Specific E2E test
npx playwright test tests/e2e/shipment-creation.spec.ts

# Debug E2E test
npx playwright test --debug tests/e2e/shipment-creation.spec.ts
```

## Architecture Overview

### Tech Stack
- **Frontend**: Next.js 14 App Router, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Real-time)
- **UI Components**: Radix UI + ShadCN/UI components
- **State Management**: Zustand stores
- **Testing**: Vitest (unit/integration), Playwright (E2E)
- **Styling**: Tailwind CSS with custom dark blue theme (#1e293b, #0f172a) and orange accents (#f97316)

### Key Directories Structure
```
src/
├── app/                    # Next.js App Router pages
│   ├── (auth)/            # Authentication pages
│   └── (dashboard)/       # Main app pages
├── components/            # Reusable UI components
│   ├── ui/               # ShadCN base components
│   ├── forms/            # Form components
│   └── auth/             # Auth-specific components
├── lib/                  # Utilities and configurations
│   ├── supabase/         # Supabase client & types
│   ├── utils/            # Utility functions
│   └── validations/      # Zod schemas
├── stores/               # Zustand state management
├── hooks/                # Custom React hooks
└── types/                # TypeScript type definitions
```

### Core Features Architecture

**Authentication & Authorization**
- Role-based access control: Admin, CS, Account, Customer, Carrier, Driver, Factory, Shipper, Consignee, Notify Party, Forwarder Agent
- Supabase Auth with middleware.ts for route protection
- Row Level Security (RLS) policies in database

**Shipment Management**
- Multi-modal transport support (Sea, Land, Rail)
- Intelligent pre-population based on customer relationships
- Automatic shipment number generation: EX[Mode]-[Port]-YYMMDD-[Running]
- Required fields: Transport Mode, Factory, Forwarder Agent, ETD/ETA/Closing Time, Origin/Destination Ports

**State Management (Zustand Stores)**
- `shipment-creation-store.ts`: Multi-step shipment form state
- `company-store.ts`: Company data management
- `customer-product-store.ts`: Product relationships
- `port-store.ts`: Port information
- `auth-store.ts`: Authentication state

**Database Schema**
- Master data: Companies, Products, Ports, Units, Drivers
- Relationships: Customer-Shipper, Customer-Product, Consignee-Notify Party
- Shipments with status tracking and audit trail
- Container and product management with weight calculations

## Key Development Patterns

### Supabase Integration
- Client creation: Use `src/lib/supabase/client.ts`
- Server components: Create server client in each component
- Database types: Generated types in `src/lib/supabase/types.ts`
- Real-time subscriptions for status updates

### Form Handling
- React Hook Form with Zod validation
- Multi-step forms using Zustand store state
- Intelligent pre-population based on relationships
- File uploads with Supabase Storage integration

### Testing Strategy
- Unit tests: Focus on utilities, stores, and validation logic
- Integration tests: Cross-component interactions and API flows
- E2E tests: Complete user workflows, especially shipment creation
- Coverage targets: 80% global, 95% for critical utilities

### Component Organization
- Base UI components from ShadCN/UI in `components/ui/`
- Feature-specific components in dedicated folders
- Reusable form components in `components/forms/`
- Admin-specific components in `components/admin/`

## Important Development Notes

### Shipment Number Generation
The system uses a specific format: EX[Transportation Mode Code]-[Port Code]-YYMMDD-[Running Number]
- Transportation Mode Codes: SEA=Sea, LAN=Land, RAI=Rail
- Running number resets monthly per mode/port combination
- Collision detection with database retry mechanism

### Date Validation Requirements
ETD/ETA/Closing Time must follow logical sequence: Closing Time < ETD < ETA

### Relationship Intelligence
- Customer selection auto-loads associated shippers, products, and notify parties
- Default selections based on relationship strength and history
- Pricing intelligence from Customer-Product relationships (CIF/FOB per KG)

### Mobile PWA Support
- Progressive Web App capabilities for offline driver operations
- Mobile-first responsive design
- GPS location capture and photo uploads for status updates

## Environment Setup

Required environment variables (see .env.example):
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- Additional service configurations for notifications (Email, SMS, Line, WeChat)

## Documentation References

- Full architecture details: `docs/architecture.md`
- Product requirements: `docs/prd.md`
- Testing guide: `TESTING.md`
- Schema documentation: `shipments_schema.md`