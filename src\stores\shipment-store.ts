import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import type { ShipmentWithRelations, ShipmentStatus, TransportMode } from '@/lib/supabase/types'

// Search and filter types
export interface ShipmentFilters {
  search: string
  status: ShipmentStatus[]
  customer_id: string[]
  transportation_mode: TransportMode[]
  date_range: {
    start: string | null
    end: string | null
  }
  origin_port_id: string[]
  destination_port_id: string[]
}

// Sorting configuration
export interface SortConfig {
  field: keyof ShipmentWithRelations
  direction: 'asc' | 'desc'
}

// Pagination configuration
export interface PaginationConfig {
  page: number
  pageSize: number
  totalCount: number
  totalPages: number
}

// Saved search configuration
export interface SavedSearch {
  id: string
  name: string
  filters: ShipmentFilters
  sort: SortConfig
  created_at: string
  user_id?: string
}

// View mode types
export type ViewMode = 'table' | 'card'

// Store state
export interface ShipmentState {
  // Data
  shipments: ShipmentWithRelations[]
  selectedShipments: Set<string>
  expandedCards: string[]
  
  // Loading and error states
  isLoading: boolean
  isLoadingMore: boolean
  error: string | null
  
  // Search and filtering
  filters: ShipmentFilters
  sort: SortConfig
  pagination: PaginationConfig
  
  // Saved searches
  savedSearches: SavedSearch[]
  activeSavedSearch: string | null
  
  // UI state
  showFilters: boolean
  showAdvancedSearch: boolean
  viewMode: ViewMode
  
  // Real-time updates
  lastUpdated: string | null
  hasNewUpdates: boolean
}

// Store actions
export interface ShipmentActions {
  // Data actions
  setShipments: (shipments: ShipmentWithRelations[]) => void
  addShipments: (shipments: ShipmentWithRelations[]) => void
  updateShipment: (id: string, updates: Partial<ShipmentWithRelations>) => void
  removeShipment: (id: string) => void
  
  // Selection actions
  selectShipment: (id: string) => void
  deselectShipment: (id: string) => void
  selectAllShipments: () => void
  deselectAllShipments: () => void
  toggleShipmentSelection: (id: string) => void
  
  // Loading and error actions
  setLoading: (loading: boolean) => void
  setLoadingMore: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Filter actions
  updateFilters: (updates: Partial<ShipmentFilters>) => void
  clearFilters: () => void
  setSearchTerm: (search: string) => void
  addStatusFilter: (status: ShipmentStatus) => void
  removeStatusFilter: (status: ShipmentStatus) => void
  addCustomerFilter: (customerId: string) => void
  removeCustomerFilter: (customerId: string) => void
  setDateRange: (start: string | null, end: string | null) => void
  
  // Sorting actions
  setSort: (field: keyof ShipmentWithRelations, direction: 'asc' | 'desc') => void
  toggleSort: (field: keyof ShipmentWithRelations) => void
  clearSort: () => void
  
  // Pagination actions
  setPage: (page: number) => void
  setPageSize: (pageSize: number) => void
  setPagination: (pagination: Partial<PaginationConfig>) => void
  nextPage: () => void
  previousPage: () => void
  
  // Saved search actions
  saveSearch: (name: string) => void
  loadSavedSearch: (id: string) => void
  deleteSavedSearch: (id: string) => void
  updateSavedSearch: (id: string, updates: Partial<SavedSearch>) => void
  setActiveSavedSearch: (id: string | null) => void
  
  // UI actions
  setShowFilters: (show: boolean) => void
  toggleFilters: () => void
  setShowAdvancedSearch: (show: boolean) => void
  toggleAdvancedSearch: () => void
  setViewMode: (mode: ViewMode) => void
  toggleViewMode: () => void
  
  // Card expansion actions
  expandCard: (id: string) => void
  collapseCard: (id: string) => void
  toggleCardExpansion: (id: string) => void
  expandAllCards: () => void
  collapseAllCards: () => void
  
  // Real-time actions
  markUpdated: () => void
  markNewUpdates: (hasUpdates: boolean) => void
  
  // Utility actions
  reset: () => void
  refresh: () => void
  exportFilters: () => ShipmentFilters
  importFilters: (filters: Partial<ShipmentFilters>) => void
}

// Initial state
const initialFilters: ShipmentFilters = {
  search: '',
  status: [],
  customer_id: [],
  transportation_mode: [],
  date_range: {
    start: null,
    end: null,
  },
  origin_port_id: [],
  destination_port_id: [],
}

const initialSort: SortConfig = {
  field: 'created_at',
  direction: 'desc',
}

const initialPagination: PaginationConfig = {
  page: 1,
  pageSize: 25,
  totalCount: 0,
  totalPages: 0,
}

const initialState: ShipmentState = {
  shipments: [],
  selectedShipments: new Set(),
  expandedCards: [],
  
  isLoading: false,
  isLoadingMore: false,
  error: null,
  
  filters: initialFilters,
  sort: initialSort,
  pagination: initialPagination,
  
  savedSearches: [],
  activeSavedSearch: null,
  
  showFilters: false,
  showAdvancedSearch: false,
  viewMode: 'table', // Default to table view, will switch to card on mobile
  
  lastUpdated: null,
  hasNewUpdates: false,
}

// Create the store
export const useShipmentStore = create<ShipmentState & ShipmentActions>()(
  persist(
    immer((set, get) => ({
      ...initialState,

      // Data actions
      setShipments: (shipments) => {
        set(state => {
          state.shipments = shipments
          state.lastUpdated = new Date().toISOString()
        })
      },

      addShipments: (shipments) => {
        set(state => {
          state.shipments.push(...shipments)
          state.lastUpdated = new Date().toISOString()
        })
      },

      updateShipment: (id, updates) => {
        set(state => {
          const index = state.shipments.findIndex(s => s.id === id)
          if (index !== -1) {
            Object.assign(state.shipments[index], updates)
            state.lastUpdated = new Date().toISOString()
            state.hasNewUpdates = true
          }
        })
      },

      removeShipment: (id) => {
        set(state => {
          state.shipments = state.shipments.filter(s => s.id !== id)
          state.selectedShipments.delete(id)
          state.lastUpdated = new Date().toISOString()
        })
      },

      // Selection actions
      selectShipment: (id) => {
        set(state => {
          state.selectedShipments.add(id)
        })
      },

      deselectShipment: (id) => {
        set(state => {
          state.selectedShipments.delete(id)
        })
      },

      selectAllShipments: () => {
        set(state => {
          state.shipments.forEach(s => state.selectedShipments.add(s.id))
        })
      },

      deselectAllShipments: () => {
        set(state => {
          state.selectedShipments.clear()
        })
      },

      toggleShipmentSelection: (id) => {
        const { selectedShipments } = get()
        if (selectedShipments.has(id)) {
          get().deselectShipment(id)
        } else {
          get().selectShipment(id)
        }
      },

      // Loading and error actions
      setLoading: (loading) => {
        set(state => {
          state.isLoading = loading
        })
      },

      setLoadingMore: (loading) => {
        set(state => {
          state.isLoadingMore = loading
        })
      },

      setError: (error) => {
        set(state => {
          state.error = error
        })
      },

      // Filter actions
      updateFilters: (updates) => {
        set(state => {
          Object.assign(state.filters, updates)
          state.pagination.page = 1 // Reset to first page when filters change
          state.activeSavedSearch = null // Clear active saved search
        })
      },

      clearFilters: () => {
        set(state => {
          state.filters = { ...initialFilters }
          state.pagination.page = 1
          state.activeSavedSearch = null
        })
      },

      setSearchTerm: (search) => {
        get().updateFilters({ search })
      },

      addStatusFilter: (status) => {
        const { filters } = get()
        if (!filters.status.includes(status)) {
          get().updateFilters({
            status: [...filters.status, status],
          })
        }
      },

      removeStatusFilter: (status) => {
        const { filters } = get()
        get().updateFilters({
          status: filters.status.filter(s => s !== status),
        })
      },

      addCustomerFilter: (customerId) => {
        const { filters } = get()
        if (!filters.customer_id.includes(customerId)) {
          get().updateFilters({
            customer_id: [...filters.customer_id, customerId],
          })
        }
      },

      removeCustomerFilter: (customerId) => {
        const { filters } = get()
        get().updateFilters({
          customer_id: filters.customer_id.filter(id => id !== customerId),
        })
      },

      setDateRange: (start, end) => {
        get().updateFilters({
          date_range: { start, end },
        })
      },

      // Sorting actions
      setSort: (field, direction) => {
        set(state => {
          state.sort = { field, direction }
          state.pagination.page = 1 // Reset to first page when sort changes
        })
      },

      toggleSort: (field) => {
        const { sort } = get()
        if (sort.field === field) {
          get().setSort(field, sort.direction === 'asc' ? 'desc' : 'asc')
        } else {
          get().setSort(field, 'asc')
        }
      },

      clearSort: () => {
        set(state => {
          state.sort = { ...initialSort }
        })
      },

      // Pagination actions
      setPage: (page) => {
        set(state => {
          state.pagination.page = Math.max(1, Math.min(page, state.pagination.totalPages))
        })
      },

      setPageSize: (pageSize) => {
        set(state => {
          state.pagination.pageSize = pageSize
          state.pagination.page = 1 // Reset to first page
          state.pagination.totalPages = Math.ceil(state.pagination.totalCount / pageSize)
        })
      },

      setPagination: (pagination) => {
        set(state => {
          Object.assign(state.pagination, pagination)
        })
      },

      nextPage: () => {
        const { pagination } = get()
        if (pagination.page < pagination.totalPages) {
          get().setPage(pagination.page + 1)
        }
      },

      previousPage: () => {
        const { pagination } = get()
        if (pagination.page > 1) {
          get().setPage(pagination.page - 1)
        }
      },

      // Saved search actions
      saveSearch: (name) => {
        const { filters, sort } = get()
        const savedSearch: SavedSearch = {
          id: `search_${Date.now()}`,
          name,
          filters: { ...filters },
          sort: { ...sort },
          created_at: new Date().toISOString(),
        }

        set(state => {
          state.savedSearches.push(savedSearch)
          state.activeSavedSearch = savedSearch.id
        })
      },

      loadSavedSearch: (id) => {
        const { savedSearches } = get()
        const savedSearch = savedSearches.find(s => s.id === id)
        if (savedSearch) {
          set(state => {
            state.filters = { ...savedSearch.filters }
            state.sort = { ...savedSearch.sort }
            state.pagination.page = 1
            state.activeSavedSearch = id
          })
        }
      },

      deleteSavedSearch: (id) => {
        set(state => {
          state.savedSearches = state.savedSearches.filter(s => s.id !== id)
          if (state.activeSavedSearch === id) {
            state.activeSavedSearch = null
          }
        })
      },

      updateSavedSearch: (id, updates) => {
        set(state => {
          const index = state.savedSearches.findIndex(s => s.id === id)
          if (index !== -1) {
            Object.assign(state.savedSearches[index], updates)
          }
        })
      },

      setActiveSavedSearch: (id) => {
        set(state => {
          state.activeSavedSearch = id
        })
      },

      // UI actions
      setShowFilters: (show) => {
        set(state => {
          state.showFilters = show
        })
      },

      toggleFilters: () => {
        const { showFilters } = get()
        get().setShowFilters(!showFilters)
      },

      setShowAdvancedSearch: (show) => {
        set(state => {
          state.showAdvancedSearch = show
        })
      },

      toggleAdvancedSearch: () => {
        const { showAdvancedSearch } = get()
        get().setShowAdvancedSearch(!showAdvancedSearch)
      },

      setViewMode: (mode) => {
        set(state => {
          state.viewMode = mode
        })
      },

      toggleViewMode: () => {
        const { viewMode } = get()
        get().setViewMode(viewMode === 'table' ? 'card' : 'table')
      },

      // Card expansion actions
      expandCard: (id) => {
        set(state => {
          if (!state.expandedCards.includes(id)) {
            state.expandedCards.push(id)
          }
        })
      },

      collapseCard: (id) => {
        set(state => {
          const index = state.expandedCards.indexOf(id)
          if (index > -1) {
            state.expandedCards.splice(index, 1)
          }
        })
      },

      toggleCardExpansion: (id) => {
        const { expandedCards } = get()
        if (expandedCards.includes(id)) {
          get().collapseCard(id)
        } else {
          get().expandCard(id)
        }
      },

      expandAllCards: () => {
        set(state => {
          const allIds = state.shipments.map(s => s.id)
          state.expandedCards = [...new Set([...state.expandedCards, ...allIds])]
        })
      },

      collapseAllCards: () => {
        set(state => {
          state.expandedCards = []
        })
      },

      // Real-time actions
      markUpdated: () => {
        set(state => {
          state.lastUpdated = new Date().toISOString()
        })
      },

      markNewUpdates: (hasUpdates) => {
        set(state => {
          state.hasNewUpdates = hasUpdates
        })
      },

      // Utility actions
      reset: () => {
        set(() => ({ ...initialState }))
      },

      refresh: () => {
        get().markUpdated()
        // Note: Actual data refresh should be handled by the consuming component
      },

      exportFilters: () => {
        return get().filters
      },

      importFilters: (filters) => {
        get().updateFilters(filters)
      },
    })),
    {
      name: 'shipment-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Persist only search and UI preferences, not data
        filters: state.filters,
        sort: state.sort,
        pagination: { ...state.pagination, page: 1 }, // Reset page on reload
        savedSearches: state.savedSearches,
        showFilters: state.showFilters,
        viewMode: state.viewMode,
      }),
      version: 1,
    }
  )
)

// Helper hooks for specific parts of the store
export const useShipmentFilters = () =>
  useShipmentStore(state => state.filters)

export const useShipmentSort = () =>
  useShipmentStore(state => state.sort)

export const useShipmentPagination = () =>
  useShipmentStore(state => state.pagination)

export const useShipmentSelection = () =>
  useShipmentStore(state => ({
    selectedShipments: state.selectedShipments,
    selectShipment: state.selectShipment,
    deselectShipment: state.deselectShipment,
    toggleShipmentSelection: state.toggleShipmentSelection,
    selectAllShipments: state.selectAllShipments,
    deselectAllShipments: state.deselectAllShipments,
  }))

export const useShipmentUI = () =>
  useShipmentStore(state => ({
    showFilters: state.showFilters,
    showAdvancedSearch: state.showAdvancedSearch,
    setShowFilters: state.setShowFilters,
    toggleFilters: state.toggleFilters,
    setShowAdvancedSearch: state.setShowAdvancedSearch,
    toggleAdvancedSearch: state.toggleAdvancedSearch,
  }))

// Computed selectors
export const useShipmentStats = () => {
  return useShipmentStore(state => ({
    totalShipments: state.shipments.length,
    selectedCount: state.selectedShipments.size,
    filteredCount: state.pagination.totalCount,
    hasFilters: Object.values(state.filters).some(value => 
      Array.isArray(value) ? value.length > 0 : value !== '' && value !== null
    ),
    hasSelection: state.selectedShipments.size > 0,
    isAllSelected: state.selectedShipments.size === state.shipments.length && state.shipments.length > 0,
  }))
}