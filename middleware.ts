import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  const { pathname } = request.nextUrl

  // Define route protection rules
  const publicRoutes = ['/login', '/register', '/forgot-password', '/reset-password']
  const authRoutes = ['/login', '/register']
  const protectedRoutes = ['/dashboard', '/admin', '/profile', '/settings']
  const adminRoutes = ['/admin']
  const mobileRoutes = ['/mobile']

  // If accessing root path, redirect based on authentication status
  if (pathname === '/') {
    if (user) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    } else {
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  // If user is authenticated and trying to access auth routes, redirect to dashboard
  if (user && authRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // If user is not authenticated and trying to access protected routes, redirect to login
  if (!user && protectedRoutes.some(route => pathname.startsWith(route))) {
    const redirectUrl = new URL('/login', request.url)
    redirectUrl.searchParams.set('redirectTo', pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // If user is authenticated, check role-based access
  if (user) {
    try {
      // Get user profile to check role
      let { data: profile } = await supabase
        .from('profiles')
        .select('role, is_active')
        .eq('user_id', user.id)
        .single()

      // If profile not found, try to create it from auth metadata
      if (!profile) {
        const userData = user.user_metadata || {}
        const { error: createError } = await supabase
          .from('profiles')
          .insert({
            user_id: user.id,
            email: user.email || '',
            first_name: userData.first_name || null,
            last_name: userData.last_name || null,
            phone_number: userData.phone_number || null,
            role: userData.role || 'customer',
            company_id: userData.company_id || null,
            is_active: true,
          })

        if (createError) {
          console.error('Error creating profile in middleware:', createError)
          await supabase.auth.signOut()
          return NextResponse.redirect(new URL('/login?message=Account setup error', request.url))
        }

        // Fetch the newly created profile
        const { data: newProfile } = await supabase
          .from('profiles')
          .select('role, is_active')
          .eq('user_id', user.id)
          .single()
        
        profile = newProfile
      }

      // If profile not found or user is inactive, redirect to login
      if (!profile || !profile.is_active) {
        await supabase.auth.signOut()
        return NextResponse.redirect(new URL('/login?message=Account not found or inactive', request.url))
      }

      // Check admin routes
      if (adminRoutes.some(route => pathname.startsWith(route))) {
        if (profile.role !== 'admin') {
          return NextResponse.redirect(new URL('/unauthorized', request.url))
        }
      }

      // Check mobile routes (driver role only)
      if (mobileRoutes.some(route => pathname.startsWith(route))) {
        if (profile.role !== 'driver') {
          return NextResponse.redirect(new URL('/unauthorized', request.url))
        }
      }

      // Redirect based on role if accessing root dashboard
      if (pathname === '/dashboard') {
        switch (profile.role) {
          case 'driver':
            return NextResponse.redirect(new URL('/driver/dashboard', request.url))
          case 'admin':
            return NextResponse.redirect(new URL('/dashboard/admin', request.url))
          default:
            return NextResponse.redirect(new URL('/dashboard/overview', request.url))
        }
      }

      // Set user info in headers for use in server components
      response.headers.set('x-user-id', user.id)
      response.headers.set('x-user-role', profile.role)
      response.headers.set('x-user-email', user.email || '')
    } catch (error) {
      console.error('Middleware error:', error)
      // If there's an error fetching profile, redirect to login
      return NextResponse.redirect(new URL('/login?message=Authentication error', request.url))
    }
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
