'use client'

import { <PERSON>ertCircle, Clock, Mail, RefreshCw, Shield } from 'lucide-react'
import type { AuthError } from '@/lib/supabase/auth'

interface AuthErrorDisplayProps {
  error: AuthError | string | null
  onRetry?: () => void
  className?: string
}

export function AuthErrorDisplay({
  error,
  onRetry,
  className = '',
}: AuthErrorDisplayProps) {
  if (!error) return null

  // Handle string errors
  if (typeof error === 'string') {
    return (
      <div
        className={`bg-red-500/10 border border-red-500/20 rounded-lg p-4 ${className}`}
      >
        <div className="flex items-start gap-3">
          <AlertCircle className="h-5 w-5 text-red-400 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  const getErrorIcon = (code: string) => {
    switch (code) {
      case 'RATE_LIMITED':
        return (
          <Clock className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
        )
      case 'EMAIL_NOT_CONFIRMED':
        return <Mail className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
      case 'ACCOUNT_DEACTIVATED':
        return (
          <Shield className="h-5 w-5 text-orange-400 mt-0.5 flex-shrink-0" />
        )
      case 'NETWORK_ERROR':
        return (
          <RefreshCw className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
        )
      default:
        return (
          <AlertCircle className="h-5 w-5 text-red-400 mt-0.5 flex-shrink-0" />
        )
    }
  }

  const getErrorColors = (code: string) => {
    switch (code) {
      case 'RATE_LIMITED':
        return 'bg-yellow-500/10 border-yellow-500/20 text-yellow-300'
      case 'EMAIL_NOT_CONFIRMED':
        return 'bg-blue-500/10 border-blue-500/20 text-blue-300'
      case 'ACCOUNT_DEACTIVATED':
        return 'bg-orange-500/10 border-orange-500/20 text-orange-300'
      case 'NETWORK_ERROR':
        return 'bg-purple-500/10 border-purple-500/20 text-purple-300'
      default:
        return 'bg-red-500/10 border-red-500/20 text-red-300'
    }
  }

  const showRetryButton = (code: string) => {
    return ['NETWORK_ERROR', 'UNKNOWN_ERROR'].includes(code) && onRetry
  }

  const getHelpText = (code: string) => {
    switch (code) {
      case 'INVALID_CREDENTIALS':
        return 'Double-check your email and password. Make sure Caps Lock is off.'
      case 'EMAIL_NOT_CONFIRMED':
        return "Check your spam folder if you don't see the confirmation email."
      case 'RATE_LIMITED':
        return 'This helps protect your account from unauthorized access attempts.'
      case 'USER_NOT_FOUND':
        return 'Try signing up for a new account instead.'
      case 'EMAIL_EXISTS':
        return 'Use the "Forgot Password" link if you can\'t remember your password.'
      case 'ACCOUNT_DEACTIVATED':
        return 'Contact your administrator or support team for assistance.'
      case 'NETWORK_ERROR':
        return 'Check your internet connection and try again.'
      default:
        return 'If this problem persists, please contact support.'
    }
  }

  return (
    <div
      className={`${getErrorColors(error.code)} border rounded-lg p-4 ${className}`}
    >
      <div className="flex items-start gap-3">
        {getErrorIcon(error.code)}
        <div className="flex-1 space-y-2">
          <p className="font-medium">{error.message}</p>
          <p className="text-sm opacity-80">{getHelpText(error.code)}</p>

          {showRetryButton(error.code) && (
            <button
              onClick={onRetry}
              className="inline-flex items-center gap-2 px-3 py-1.5 text-sm bg-white/10 hover:bg-white/20 rounded-md transition-colors"
            >
              <RefreshCw className="h-3 w-3" />
              Try Again
            </button>
          )}

          {error.details && process.env.NODE_ENV === 'development' && (
            <details className="mt-2">
              <summary className="text-xs cursor-pointer opacity-60 hover:opacity-80">
                Technical Details (Dev Mode)
              </summary>
              <pre className="mt-1 text-xs opacity-60 whitespace-pre-wrap">
                {JSON.stringify(error.details, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    </div>
  )
}
