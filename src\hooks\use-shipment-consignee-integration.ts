'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  useConsigneeNotifyPartiesRealtime,
  useConsigneeNotifyPartyCheck,
  useConsigneeOptions,
} from './use-consignee-notify-parties'
import type { ConsigneeNotifyParty } from '@/stores/consignee-notify-party-store'

export interface ShipmentConsigneeData {
  consigneeId: string
  consigneeName: string
  notifyPartyId?: string
  notifyPartyName?: string
  notificationPreferences?: {
    email: boolean
    sms: boolean
    line: boolean
    wechat: boolean
  }
  specialInstructions?: string
  notes?: string
  priorityOrder?: number
  isDefaultRelationship?: boolean
}

export interface ConsigneeIntegrationOptions {
  autoSelectDefault: boolean
  enableRealTimeUpdates: boolean
  prePopulateRelatedFields: boolean
  validateRelationships: boolean
}

export interface ConsigneeIntegrationState {
  // Data
  selectedConsigneeId: string | null
  selectedNotifyPartyId: string | null
  availableNotifyParties: ConsigneeNotifyParty[]
  selectedRelationship: ConsigneeNotifyParty | null

  // Status
  isInitialized: boolean
  hasRelationships: boolean
  defaultNotifyParty: ConsigneeNotifyParty | null
  relationshipCount: number

  // Loading states
  loadingConsignees: boolean
  loadingRelationships: boolean
  loadingValidation: boolean

  // Errors
  validationError: string | null
  relationshipError: string | null
}

export interface ConsigneeIntegrationActions {
  // Selection actions
  setConsignee: (consigneeId: string) => void
  setNotifyParty: (notifyPartyId: string) => void
  clearSelection: () => void

  // Auto-population actions
  autoSelectDefaultNotifyParty: () => void
  populateRelatedFields: (
    targetFields: Record<string, any>
  ) => Record<string, any>

  // Validation actions
  validateSelection: () => Promise<boolean>
  getValidationErrors: () => string[]

  // Utility actions
  refreshRelationships: () => void
  getRelationshipSuggestions: () => ConsigneeNotifyParty[]
  exportShipmentData: () => ShipmentConsigneeData | null
}

const DEFAULT_OPTIONS: ConsigneeIntegrationOptions = {
  autoSelectDefault: true,
  enableRealTimeUpdates: true,
  prePopulateRelatedFields: true,
  validateRelationships: true,
}

export const useShipmentConsigneeIntegration = (
  options: Partial<ConsigneeIntegrationOptions> = {}
) => {
  const config = { ...DEFAULT_OPTIONS, ...options }

  // State
  const [selectedConsigneeId, setSelectedConsigneeId] = useState<string | null>(
    null
  )
  const [selectedNotifyPartyId, setSelectedNotifyPartyId] = useState<
    string | null
  >(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)
  const [relationshipError, setRelationshipError] = useState<string | null>(
    null
  )

  // Data hooks
  const { consignees, loading: loadingConsignees } = useConsigneeOptions()
  const {
    consigneeNotifyParties: availableNotifyParties,
    loading: loadingRelationships,
  } = useConsigneeNotifyPartiesRealtime(selectedConsigneeId || undefined)
  const {
    hasRelationships,
    defaultNotifyParty,
    relationshipCount,
    loading: loadingValidation,
  } = useConsigneeNotifyPartyCheck(selectedConsigneeId || undefined)

  // Computed values
  const selectedRelationship =
    availableNotifyParties.find(
      rel => rel.notify_party_id === selectedNotifyPartyId
    ) || null

  const selectedConsignee = consignees.find(c => c.id === selectedConsigneeId)

  // Actions
  const setConsignee = useCallback((consigneeId: string) => {
    setSelectedConsigneeId(consigneeId)
    setSelectedNotifyPartyId(null) // Clear notify party when consignee changes
    setValidationError(null)
    setRelationshipError(null)
  }, [])

  const setNotifyParty = useCallback((notifyPartyId: string) => {
    setSelectedNotifyPartyId(notifyPartyId)
    setValidationError(null)
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedConsigneeId(null)
    setSelectedNotifyPartyId(null)
    setValidationError(null)
    setRelationshipError(null)
    setIsInitialized(false)
  }, [])

  const autoSelectDefaultNotifyParty = useCallback(() => {
    if (
      defaultNotifyParty &&
      config.autoSelectDefault &&
      !selectedNotifyPartyId
    ) {
      setSelectedNotifyPartyId(defaultNotifyParty.notify_party_id)
      return defaultNotifyParty
    }
    return null
  }, [defaultNotifyParty, config.autoSelectDefault, selectedNotifyPartyId])

  const populateRelatedFields = useCallback(
    (targetFields: Record<string, any>) => {
      if (!selectedRelationship || !config.prePopulateRelatedFields) {
        return targetFields
      }

      const populatedFields = { ...targetFields }

      // Populate notification preferences
      if (
        selectedRelationship.notification_preferences &&
        !targetFields.notificationPreferences
      ) {
        populatedFields.notificationPreferences =
          selectedRelationship.notification_preferences
      }

      // Populate special instructions
      if (
        selectedRelationship.special_instructions &&
        !targetFields.specialInstructions
      ) {
        populatedFields.specialInstructions =
          selectedRelationship.special_instructions
      }

      // Populate notes
      if (selectedRelationship.notes && !targetFields.notes) {
        populatedFields.notes = selectedRelationship.notes
      }

      // Populate priority order
      if (selectedRelationship.priority_order && !targetFields.priorityOrder) {
        populatedFields.priorityOrder = selectedRelationship.priority_order
      }

      return populatedFields
    },
    [selectedRelationship, config.prePopulateRelatedFields]
  )

  const validateSelection = useCallback(async (): Promise<boolean> => {
    if (!config.validateRelationships) return true

    setValidationError(null)
    setRelationshipError(null)

    const errors: string[] = []

    // Validate consignee selection
    if (!selectedConsigneeId) {
      errors.push('Consignee is required')
    } else if (!selectedConsignee) {
      errors.push('Selected consignee not found')
    }

    // Validate notify party selection
    if (!selectedNotifyPartyId) {
      errors.push('Notify party is required')
    } else if (!selectedRelationship) {
      errors.push('Selected notify party relationship not found')
    }

    // Validate relationship exists and is active
    if (selectedConsigneeId && selectedNotifyPartyId && !selectedRelationship) {
      errors.push(
        'No relationship exists between selected consignee and notify party'
      )
    } else if (selectedRelationship && !selectedRelationship.is_active) {
      errors.push('Selected relationship is inactive')
    }

    // Validate notification preferences
    if (selectedRelationship?.notification_preferences) {
      const hasChannels = Object.values(
        selectedRelationship.notification_preferences
      ).some(enabled => enabled === true)
      if (!hasChannels) {
        errors.push(
          'Selected relationship has no notification channels configured'
        )
      }
    }

    if (errors.length > 0) {
      setValidationError(errors[0]) // Show first error
      setRelationshipError(errors.join('; '))
      return false
    }

    return true
  }, [
    config.validateRelationships,
    selectedConsigneeId,
    selectedConsignee,
    selectedNotifyPartyId,
    selectedRelationship,
  ])

  const getValidationErrors = useCallback((): string[] => {
    const errors: string[] = []

    if (!selectedConsigneeId) errors.push('Consignee is required')
    if (!selectedNotifyPartyId) errors.push('Notify party is required')
    if (selectedConsigneeId && !hasRelationships) {
      errors.push('No notify party relationships configured for this consignee')
    }
    if (selectedRelationship && !selectedRelationship.is_active) {
      errors.push('Selected relationship is inactive')
    }

    return errors
  }, [
    selectedConsigneeId,
    selectedNotifyPartyId,
    hasRelationships,
    selectedRelationship,
  ])

  const refreshRelationships = useCallback(() => {
    // This would trigger a refetch - the hook handles this internally
    console.log('Refreshing relationships for consignee:', selectedConsigneeId)
  }, [selectedConsigneeId])

  const getRelationshipSuggestions = useCallback((): ConsigneeNotifyParty[] => {
    if (!availableNotifyParties.length) return []

    // Sort by priority: default first, then by priority order, then by name
    return [...availableNotifyParties]
      .filter(rel => rel.is_active)
      .sort((a, b) => {
        // Default relationships first
        if (a.is_default && !b.is_default) return -1
        if (!a.is_default && b.is_default) return 1

        // Then by priority order
        const aPriority = a.priority_order || 999
        const bPriority = b.priority_order || 999
        if (aPriority !== bPriority) return aPriority - bPriority

        // Finally by name
        const aName = a.notify_party?.name || ''
        const bName = b.notify_party?.name || ''
        return aName.localeCompare(bName)
      })
  }, [availableNotifyParties])

  const exportShipmentData = useCallback((): ShipmentConsigneeData | null => {
    if (
      !selectedConsigneeId ||
      !selectedNotifyPartyId ||
      !selectedRelationship
    ) {
      return null
    }

    return {
      consigneeId: selectedConsigneeId,
      consigneeName: selectedConsignee?.name || '',
      notifyPartyId: selectedNotifyPartyId,
      notifyPartyName: selectedRelationship.notify_party?.name || '',
      notificationPreferences:
        selectedRelationship.notification_preferences || undefined,
      specialInstructions:
        selectedRelationship.special_instructions || undefined,
      notes: selectedRelationship.notes || undefined,
      priorityOrder: selectedRelationship.priority_order || undefined,
      isDefaultRelationship: selectedRelationship.is_default || false,
    }
  }, [
    selectedConsigneeId,
    selectedNotifyPartyId,
    selectedRelationship,
    selectedConsignee,
  ])

  // Auto-select default notify party when consignee changes
  useEffect(() => {
    if (!selectedConsigneeId || !config.autoSelectDefault || isInitialized)
      return

    // Wait for relationships to load
    if (loadingRelationships || loadingValidation) return

    // Auto-select default if available and no current selection
    if (defaultNotifyParty && !selectedNotifyPartyId) {
      setSelectedNotifyPartyId(defaultNotifyParty.notify_party_id)
    }

    setIsInitialized(true)
  }, [
    selectedConsigneeId,
    defaultNotifyParty,
    selectedNotifyPartyId,
    config.autoSelectDefault,
    loadingRelationships,
    loadingValidation,
    isInitialized,
  ])

  // State object
  const state: ConsigneeIntegrationState = {
    selectedConsigneeId,
    selectedNotifyPartyId,
    availableNotifyParties,
    selectedRelationship,
    isInitialized,
    hasRelationships,
    defaultNotifyParty,
    relationshipCount,
    loadingConsignees,
    loadingRelationships,
    loadingValidation,
    validationError,
    relationshipError,
  }

  // Actions object
  const actions: ConsigneeIntegrationActions = {
    setConsignee,
    setNotifyParty,
    clearSelection,
    autoSelectDefaultNotifyParty,
    populateRelatedFields,
    validateSelection,
    getValidationErrors,
    refreshRelationships,
    getRelationshipSuggestions,
    exportShipmentData,
  }

  return {
    state,
    actions,
    config,
  }
}

// Convenience hooks for specific use cases
export const useShipmentConsigneeSelector = () => {
  return useShipmentConsigneeIntegration({
    autoSelectDefault: true,
    enableRealTimeUpdates: true,
    prePopulateRelatedFields: false,
    validateRelationships: true,
  })
}

export const useShipmentFormIntegration = () => {
  return useShipmentConsigneeIntegration({
    autoSelectDefault: true,
    enableRealTimeUpdates: true,
    prePopulateRelatedFields: true,
    validateRelationships: true,
  })
}

export const useConsigneeNotifyPartyValidator = () => {
  return useShipmentConsigneeIntegration({
    autoSelectDefault: false,
    enableRealTimeUpdates: false,
    prePopulateRelatedFields: false,
    validateRelationships: true,
  })
}
