'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'

interface ServiceHealth {
  status: string
  error: string | null
  details: string
}

interface HealthCheck {
  status: string
  timestamp: string
  version?: string
  services: {
    database: ServiceHealth
    authentication: ServiceHealth
    storage: ServiceHealth
    realtime: ServiceHealth
  }
  environment?: {
    nodeEnv: string
    nextJsVersion: string
    supabaseConfigured: boolean
  }
  error?: string
}

export default function HealthPage() {
  const [health, setHealth] = useState<HealthCheck | null>(null)
  const [loading, setLoading] = useState(false)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)

  const checkHealth = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/health')
      const data = await response.json()
      setHealth(data)
      setLastChecked(new Date())
    } catch (error) {
      console.error('Health check failed:', error)
      setHealth({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Failed to fetch health status',
        services: {
          database: {
            status: 'unknown',
            error: 'Could not check',
            details: 'Network error',
          },
          authentication: {
            status: 'unknown',
            error: 'Could not check',
            details: 'Network error',
          },
          storage: {
            status: 'unknown',
            error: 'Could not check',
            details: 'Network error',
          },
          realtime: {
            status: 'unknown',
            error: 'Could not check',
            details: 'Network error',
          },
        },
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkHealth()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100'
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100'
      case 'unhealthy':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return '✅'
      case 'degraded':
        return '⚠️'
      case 'unhealthy':
        return '❌'
      default:
        return '❓'
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-foreground">
            System Health Check
          </h1>
          <Button
            onClick={checkHealth}
            disabled={loading}
            className="bg-primary hover:bg-primary/90"
          >
            {loading ? 'Checking...' : 'Refresh'}
          </Button>
        </div>

        {health && (
          <div className="space-y-6">
            {/* Overall Status */}
            <div className="bg-card rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Overall Status</h2>
                <div
                  className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(health.status)}`}
                >
                  {getStatusIcon(health.status)} {health.status.toUpperCase()}
                </div>
              </div>
              <div className="mt-4 text-sm text-muted-foreground">
                <p>Last checked: {lastChecked?.toLocaleString()}</p>
                <p>Timestamp: {new Date(health.timestamp).toLocaleString()}</p>
                {health.version && <p>Version: {health.version}</p>}
              </div>
            </div>

            {/* Services Status */}
            <div className="bg-card rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Services</h2>
              <div className="space-y-4">
                {Object.entries(health.services).map(
                  ([serviceName, service]) => (
                    <div key={serviceName} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium capitalize">
                          {serviceName}
                        </h3>
                        <div
                          className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(service.status)}`}
                        >
                          {getStatusIcon(service.status)} {service.status}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        {service.details}
                      </p>
                      {service.error && (
                        <p className="text-sm text-red-600 mt-1">
                          Error: {service.error}
                        </p>
                      )}
                    </div>
                  )
                )}
              </div>
            </div>

            {/* Environment Info */}
            {health.environment && (
              <div className="bg-card rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Environment</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="font-medium">Node Environment</p>
                    <p className="text-muted-foreground">
                      {health.environment.nodeEnv}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Next.js Version</p>
                    <p className="text-muted-foreground">
                      {health.environment.nextJsVersion}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Supabase Configured</p>
                    <p
                      className={
                        health.environment.supabaseConfigured
                          ? 'text-green-600'
                          : 'text-red-600'
                      }
                    >
                      {health.environment.supabaseConfigured
                        ? '✅ Yes'
                        : '❌ No'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {health.error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 className="font-medium text-red-800">Error</h3>
                <p className="text-red-600 mt-1">{health.error}</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
