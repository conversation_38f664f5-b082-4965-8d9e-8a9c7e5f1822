'use client'

import { useState } from 'react'
import { X, GripVertical, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { useLanguage } from '@/hooks/use-language'

interface PhotoPreviewGridProps {
  photos: File[]
  onDeletePhoto: (index: number) => void
  onReorderPhotos?: (newOrder: File[]) => void
  disabled?: boolean
  showFullscreen?: boolean
}

export function PhotoPreviewGrid({ 
  photos, 
  onDeletePhoto, 
  onReorderPhotos,
  disabled = false,
  showFullscreen = true
}: PhotoPreviewGridProps) {
  const { t } = useLanguage()
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)
  const [previewUrls, setPreviewUrls] = useState<Record<number, string>>({})

  // Generate preview URLs for files
  const getPreviewUrl = (file: File, index: number): string => {
    if (!previewUrls[index]) {
      const url = URL.createObjectURL(file)
      setPreviewUrls(prev => ({ ...prev, [index]: url }))
      return url
    }
    return previewUrls[index]
  }

  // Format file size helper
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, index: number) => {
    if (disabled || !onReorderPhotos) return
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent, index: number) => {
    if (disabled || !onReorderPhotos) return
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    setDragOverIndex(index)
  }

  const handleDragLeave = () => {
    setDragOverIndex(null)
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    if (disabled || !onReorderPhotos || draggedIndex === null) return
    
    e.preventDefault()
    
    if (draggedIndex !== dropIndex) {
      const newPhotos = [...photos]
      const draggedPhoto = newPhotos[draggedIndex]
      newPhotos.splice(draggedIndex, 1)
      newPhotos.splice(dropIndex, 0, draggedPhoto)
      onReorderPhotos(newPhotos)
    }
    
    setDraggedIndex(null)
    setDragOverIndex(null)
  }

  const handleDragEnd = () => {
    setDraggedIndex(null)
    setDragOverIndex(null)
  }

  if (photos.length === 0) {
    return (
      <div className="text-center py-8 text-slate-400">
        <div className="w-16 h-16 mx-auto mb-3 bg-slate-700 rounded-full flex items-center justify-center">
          <Eye className="w-8 h-8" />
        </div>
        <p>No photos selected</p>
        <p className="text-sm mt-1">{t('photoPreview.willAppearHere')}</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Photo Count and Info */}
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium text-slate-300">
          {t('photoPreview.selectedPhotos')} ({photos.length})
        </h3>
        {onReorderPhotos && photos.length > 1 && (
          <span className="text-xs text-slate-500">
            {t('photoPreview.dragToReorder')}
          </span>
        )}
      </div>

      {/* Photo Grid */}
      <div className="grid grid-cols-2 gap-3">
        {photos.map((photo, index) => (
          <div
            key={`${photo.name}-${index}`}
            className={`relative bg-slate-800 rounded-lg overflow-hidden border transition-all duration-200 ${
              draggedIndex === index 
                ? 'opacity-50 scale-95 border-orange-500' 
                : dragOverIndex === index 
                  ? 'border-orange-500 scale-105' 
                  : 'border-slate-700'
            }`}
            draggable={!disabled && onReorderPhotos}
            onDragStart={(e) => handleDragStart(e, index)}
            onDragOver={(e) => handleDragOver(e, index)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, index)}
            onDragEnd={handleDragEnd}
          >
            {/* Photo Image */}
            <div className="relative aspect-square">
              <img
                src={getPreviewUrl(photo, index)}
                alt={`${t('photoPreview.photo')} ${index + 1}`}
                className="w-full h-full object-cover"
                loading="lazy"
              />

              {/* Photo Index Badge */}
              <div className="absolute top-2 left-2 bg-slate-900/80 rounded-full px-2 py-1 text-xs text-white font-medium">
                {index + 1}
              </div>

              {/* Drag Handle */}
              {onReorderPhotos && photos.length > 1 && !disabled && (
                <div className="absolute top-2 right-8 bg-slate-900/80 rounded p-1 cursor-move">
                  <GripVertical className="w-3 h-3 text-slate-300" />
                </div>
              )}

              {/* Remove Button */}
              <button
                onClick={() => onDeletePhoto(index)}
                disabled={disabled}
                className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-full p-1.5 text-white transition-colors"
                aria-label="Remove photo"
              >
                <X className="w-3 h-3" />
              </button>

              {/* Fullscreen View Trigger */}
              {showFullscreen && (
                <Dialog>
                  <DialogTrigger asChild>
                    <button
                      className="absolute bottom-2 right-2 bg-slate-900/80 hover:bg-slate-800/80 rounded p-1.5 text-white transition-colors"
                      aria-label="View full size"
                    >
                      <Eye className="w-3 h-3" />
                    </button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl w-full p-0 bg-slate-900 border-slate-700">
                    <div className="relative">
                      <img
                        src={getPreviewUrl(photo, index)}
                        alt={`${t('photoPreview.photo')} ${index + 1} - ${t('photoPreview.fullSize')}`}
                        className="w-full h-auto max-h-[90vh] object-contain"
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-slate-900/90 to-transparent p-4">
                        <div className="text-white">
                          <div className="text-lg font-medium">{t('photoPreview.photo')} {index + 1}</div>
                          <div className="text-sm text-slate-300">
                            Size: {formatFileSize(photo.size)} • Type: {photo.type}
                          </div>
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            {/* Photo Info Footer */}
            <div className="p-2 bg-slate-700/50 border-t border-slate-700">
              <div className="flex justify-between items-center text-xs">
                <span className="text-slate-400 truncate flex-1 mr-2">
                  {photo.name}
                </span>
                <span className="text-slate-500 whitespace-nowrap">
                  {formatFileSize(photo.size)}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Reorder Instructions */}
      {onReorderPhotos && photos.length > 1 && !disabled && (
        <div className="text-center">
          <p className="text-xs text-slate-500">
            {t('photoPreview.dragPhotosToReorder')}
          </p>
        </div>
      )}
    </div>
  )
}